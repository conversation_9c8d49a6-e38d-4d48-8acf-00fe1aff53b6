# 25年第4周2025-01-24

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | redis大key：oms:o2o:dscloud:ordercount:500001:AY91oms:o2o:dscloud:ordercount:500001:AJG5 |  |  | 暂不处理 |
| 2 | redis大key：pay_center:service:pay_service:channel_convert_platform:WEIXIN_refundpay_center:service:pay_service:channel_convert_platform:WEIXIN_pay |  |  | 暂不处理 |
| 3 | oms:o2o:dscloud:ordercount:999999:10101oms:o2o:dscloud:ordercount:888888:0909oms:o2o:dscloud:ordercount:888888:0001无效删除 |  |  | 已处理 |
| 4 | middle-order提交订单回滚问题(处方单-订单无法取消) |  |  | 待处理 |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 | B2B加盟商城 | **交易中台**:  技术方案评审：已完成。  一心助手对接：  代码开发进度：完成40%代码开发。   **支付中台**:  技术方案评审：已完成。  一心助手对齐：已完成与一心助手对于D-ERP支付流程的对齐工作。  代码开发进度：5%。   **订单中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，等待双方对齐确认。  ERP/POS对接：提供了对接文档的初版。  代码开发进度：5%。 |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1.一心到家-生产收货地址超出配送范围修改\上线2. 一心到家-需求评审《付费会员用券逻辑调整》3.一心到家-推广订单无退款订单修数4.B2C订单请货\异常清除逻辑修改\上线5.订单一致性服务搭建6.线上问题-山西BC2请货-POS下账 | **遗留问题** **风险问题** | **需求研发**订单一致性服务搭建**技术建设** |  |
| 2 |  | **本周总工时：5d**1. 会员消费记录刷数,已完成 2. 线上kafka消息堆积问题处理 3. 会员慢病需求开发，待上线 4. 订单新模型字段讨论 5. 线下单数据修复脚本开发,30% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：4.5d**1.B2B商城，目前接口和其他平台责任已经划分进入开发阶段。 3.5 day2.支付宝商城联调（预计今天结束联调） 1 day | **遗留问题**B2B商城 出库单模块才完成20%**风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. 移动OMS刷数 2. B2B 订单新模型、接口梳理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 线上员工推广无订单问题 2. 一件代发导出问题 3. OSS资源整合 4. 客服中台技术方案编写 评审 5. 客服中台搭建 | **遗留问题**一件代发订单在服务商批量发货与自营订单存在逻辑相悖，需年后产品定夺**风险问题** | **需求研发** **技术建设** |  |
| 6 |  | **本周总工时：5d**1.交易中台-购物车开发（已完成90%） | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1. 交易中台开发 2. 日常值班问题处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. B2C订单DB重构ES问题修复：   1. 新增后续需求迭代的查询条件   2. 新增一致性检查   3. ES生成主键格式变更 2. 新增后续需求迭代的查询条件 3. 新增一致性检查 4. ES生成主键格式变更 5. 查看交易中台结算剩余内容 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 余额支付开发   1. 配置相关开发 2. 配置相关开发 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) (订单组redis cluster) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy |  |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等

1.绩效考核内容





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |
| 3 |  |  |  |
| 4 |  |  |  |