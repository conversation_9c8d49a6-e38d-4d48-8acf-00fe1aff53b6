





<!DOCTYPE html>
<html
  lang="en"
  
  data-color-mode="auto" data-light-theme="light" data-dark-theme="dark"
  data-a11y-animated-images="system" data-a11y-link-underlines="true"
  
  >




  <head>
    <meta charset="utf-8">
  <link rel="dns-prefetch" href="https://github.githubassets.com">
  <link rel="dns-prefetch" href="https://avatars.githubusercontent.com">
  <link rel="dns-prefetch" href="https://github-cloud.s3.amazonaws.com">
  <link rel="dns-prefetch" href="https://user-images.githubusercontent.com/">
  <link rel="preconnect" href="https://github.githubassets.com" crossorigin>
  <link rel="preconnect" href="https://avatars.githubusercontent.com">

  


  <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/light-d1334f2b22bf.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/light_high_contrast-f695a361c6b2.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/dark-f73a069fd33e.css" /><link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/dark_high_contrast-3a0d87f72ad4.css" /><link data-color-theme="light" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light-d1334f2b22bf.css" /><link data-color-theme="light_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_high_contrast-f695a361c6b2.css" /><link data-color-theme="light_colorblind" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_colorblind-367eb9a4565a.css" /><link data-color-theme="light_colorblind_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_colorblind_high_contrast-34780c9e589c.css" /><link data-color-theme="light_tritanopia" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_tritanopia-2ddc677c041d.css" /><link data-color-theme="light_tritanopia_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_tritanopia_high_contrast-b479ee0af6fe.css" /><link data-color-theme="dark" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark-f73a069fd33e.css" /><link data-color-theme="dark_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_high_contrast-3a0d87f72ad4.css" /><link data-color-theme="dark_colorblind" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_colorblind-b17a8392e6c4.css" /><link data-color-theme="dark_colorblind_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_colorblind_high_contrast-03758f901c24.css" /><link data-color-theme="dark_tritanopia" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_tritanopia-a1cc7dba9f73.css" /><link data-color-theme="dark_tritanopia_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_tritanopia_high_contrast-55c33b3b3010.css" /><link data-color-theme="dark_dimmed" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_dimmed-55459b36aa6d.css" /><link data-color-theme="dark_dimmed_high_contrast" crossorigin="anonymous" media="all" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_dimmed_high_contrast-b615f369440d.css" />


    <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-primitives-dc7ca6859caf.css" />
    <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-fe85dc7854c9.css" />
    <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/global-608c56995de0.css" />
    <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/github-5361678093c6.css" />
  <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/repository-b58e401b73ae.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/code-4ea853641043.css" />

  


  <script type="application/json" id="client-env">{"locale":"en","featureFlags":["alternate_user_config_repo","api_insights_show_missing_data_banner","appearance_settings","attestations_filtering","attestations_sorting","codespaces_prebuild_region_target_update","contact_requests_implicit_opt_in","contentful_lp_copilot_extensions","contentful_lp_flex_features","contentful_lp_footnotes","copilot_chat_attach_multiple_images","copilot_chat_custom_instructions","copilot_chat_repo_custom_instructions_preview","copilot_chat_vision_in_claude","copilot_chat_vision_skip_thread_create","copilot_chat_wholearea_dd","copilot_custom_copilots_feature_preview","copilot_duplicate_thread","copilot_free_to_paid_telem","copilot_ftp_settings_upgrade","copilot_ftp_upgrade_to_pro_from_models","copilot_ftp_your_copilot_settings","copilot_immersive_agent_sessions_direct_creation","copilot_immersive_structured_model_picker","copilot_new_immersive_references_ui","copilot_no_floating_button","copilot_paste_text_files","copilot_read_shared_conversation","copilot_spaces_support_forks","copilot_spark_single_user_iteration","copilot_spark_use_streaming","copilot_task_oriented_assistive_prompts","copilot_workbench_connection_reload_banner","copilot_workbench_iterate_panel","copilot_workbench_preview_analytics","copilot_workbench_refresh_on_wsod","custom_copilots_128k_window","custom_copilots_capi_mode","custom_copilots_issues_prs","direct_to_salesforce","dotcom_chat_client_side_skills","failbot_report_error_react_apps_on_page","ghost_pilot_confidence_truncation_25","ghost_pilot_confidence_truncation_40","insert_before_patch","issues_catch_non_json_graphql_response","issues_preserve_tokens_in_urls","issues_react_blur_item_picker_on_close","issues_react_bots_timeline_pagination","issues_react_create_milestone","issues_react_prohibit_title_fallback","issues_react_remove_placeholders","issues_tab_counter_updates","lifecycle_label_name_updates","link_contact_sales_swp_marketo","marketing_pages_search_explore_provider","memex_mwl_filter_field_delimiter","nonreporting_relay_graphql_status_codes","primer_primitives_experimental","primer_react_select_panel_with_modern_action_list","remove_child_patch","sample_network_conn_type","scheduled_reminders_updated_limits","site_homepage_contentful","site_msbuild_hide_integrations","site_msbuild_launch","site_msbuild_webgl_hero","spark_commit_on_default_branch","spark_sync_repository_after_iteration","swp_enterprise_contact_form","use_copilot_avatar","use_paginated_repo_picker_cost_center_form","viewscreen_sandbox","workbench_store_readonly"]}</script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/high-contrast-cookie-a58297b2ebf8.js"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/wp-runtime-8ce6273f3ef0.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js-a8c266e5f126.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52-babac9434833.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_failbot_failbot_ts-f3dd72be4f2c.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/environment-89128d48c6ff.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_primer_behaviors_dist_esm_index_mjs-c44edfed7f0d.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_selector-observer_dist_index_esm_js-cdf2757bd188.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_relative-time-element_dist_index_js-5913bc24f35d.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78-c1e2fb329866.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_text-expander-element_dist_index_js-e50fb7a5fe8c.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643-251bc3964eb6.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_markdown-toolbar-element_dist_index_js-6a8c7d9a08fe.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad-aba5025babc7.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/github-elements-86cb7fc402e2.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/element-registry-68eff60a5be0.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec-34c4b68b1dd3.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lit-html_lit-html_js-b93a87060d31.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_morphdom_dist_morphdom-esm_js-300e8e4e0414.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js-63a26702fa42.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js-595819d3686f.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f-1bcf38e06f01.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_color-convert_index_js-1a149db8dc99.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61-91618cb63471.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_updatable-content_updatable-content_ts-a5daa16ae903.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde-f953ddf42948.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_sticky-scroll-into-view_ts-e45aabc67d13.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_ajax-error_ts-app_assets_modules_github_behaviors_include-d0d0a6-a7da4270c5f4.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_behaviors_commenting_edit_ts-app_assets_modules_github_behaviors_ht-83c235-567e0f340e27.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/behaviors-c61a2dbd4863.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa-eefe25567449.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/notifications-global-40e14cc64ab7.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_catalyst_lib_inde-dbbea9-558c1f223d1d.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/code-menu-954689af15d6.js" defer="defer"></script>
  
  <script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/primer-react-a57080a0a6e8.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-core-442d3988d6da.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-lib-8705026b409a.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/octicons-react-9fd6ca6872cc.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483-b5947865157f.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6-89ab81577c38.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_dompurify_dist_purify_es_mjs-7457ebdd1a1f.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lodash-es__Stack_js-node_modules_lodash-es__Uint8Array_js-node_modules_l-4faaa6-16c4e2c524de.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_lodash-es_isEqual_js-a0841ced23fc.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_tanstack_react-virtual_dist_esm_index_js-807aab04afeb.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3-aa0d1c491a18.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806-a0e432a5dd85.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_history_history_ts-ui_packages-417c81-00e1a3522739.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_paths_index_ts-24eb15c2d826.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_ref-selector_RefSelector_tsx-d5cdb50eb045.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa-7383c64c0bfd.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2-934fa2344302.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_diffs_diff-parts_ts-d15c96aca8c4.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef-062d8d9cda55.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/app_assets_modules_github_blob-anchor_ts-ui_packages_code-nav_code-nav_ts-ui_packages_filter--8253c1-5fde020dbad1.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/react-code-view-77394c01b9d0.js" defer="defer"></script>
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.8d5e42bdd3cd6a27871d.module.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/react-code-view.ca83c136bc757c1d53e6.module.css" />

  <script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/notifications-subscriptions-menu-c9ab807bd021.js" defer="defer"></script>
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.8d5e42bdd3cd6a27871d.module.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/notifications-subscriptions-menu.07dab7f319b881c93ef5.module.css" />


  <title>styleguide/intellij-java-google-style.xml at gh-pages · google/styleguide · GitHub</title>



  <meta name="route-pattern" content="/:user_id/:repository/blob/*name(/*path)" data-turbo-transient>
  <meta name="route-controller" content="blob" data-turbo-transient>
  <meta name="route-action" content="show" data-turbo-transient>
  <meta name="fetch-nonce" content="v2:291e3198-6115-77ce-fbbf-2433c873b530">

    
  <meta name="current-catalog-service-hash" content="f3abb0cc802f3d7b95fc8762b94bdcb13bf39634c40c357301c4aa1d67a256fb">


  <meta name="request-id" content="372B:314037:8F97FA:A3E213:686B9977" data-turbo-transient="true" /><meta name="html-safe-nonce" content="dacd5c138e0fc5d315bb5b4cc0fc7256356c26f66d544847c9d9dda0768f8361" data-turbo-transient="true" /><meta name="visitor-payload" content="eyJyZWZlcnJlciI6bnVsbCwicmVxdWVzdF9pZCI6IjM3MkI6MzE0MDM3OjhGOTdGQTpBM0UyMTM6Njg2Qjk5NzciLCJ2aXNpdG9yX2lkIjoiODI2MDU5NTYzMzkxMTIwODMxMSIsInJlZ2lvbl9lZGdlIjoic291dGhlYXN0YXNpYSIsInJlZ2lvbl9yZW5kZXIiOiJpYWQifQ==" data-turbo-transient="true" /><meta name="visitor-hmac" content="f8574871c9d563856ad2e71478cc04c8b9b3631062d805443df841852ba36fb9" data-turbo-transient="true" />


    <meta name="hovercard-subject-tag" content="repository:35969061" data-turbo-transient>


  <meta name="github-keyboard-shortcuts" content="repository,source-code,file-tree,copilot" data-turbo-transient="true" />
  

  <meta name="selected-link" value="repo_source" data-turbo-transient>
  <link rel="assets" href="https://github.githubassets.com/">

    <meta name="google-site-verification" content="Apib7-x98H0j5cPqHWwSMm6dNU4GmODRoqxLiDzdx9I">

<meta name="octolytics-url" content="https://collector.github.com/github/collect" />

  <meta name="analytics-location" content="/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show" data-turbo-transient="true" />

  




    <meta name="user-login" content="">

  

    <meta name="viewport" content="width=device-width">

    

      <meta name="description" content="Style guides for Google-originated open-source projects - styleguide/intellij-java-google-style.xml at gh-pages · google/styleguide">

      <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="GitHub">

    <link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub">
    <meta property="fb:app_id" content="1401488693436528">
    <meta name="apple-itunes-app" content="app-id=1477376905, app-argument=https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml" />

      <meta name="twitter:image" content="https://opengraph.githubassets.com/b0b2584521d6df3828e310dd8cba0f2e94497e316ccd6faf94e634d9b6a33772/google/styleguide" /><meta name="twitter:site" content="@github" /><meta name="twitter:card" content="summary_large_image" /><meta name="twitter:title" content="styleguide/intellij-java-google-style.xml at gh-pages · google/styleguide" /><meta name="twitter:description" content="Style guides for Google-originated open-source projects - google/styleguide" />
  <meta property="og:image" content="https://opengraph.githubassets.com/b0b2584521d6df3828e310dd8cba0f2e94497e316ccd6faf94e634d9b6a33772/google/styleguide" /><meta property="og:image:alt" content="Style guides for Google-originated open-source projects - google/styleguide" /><meta property="og:image:width" content="1200" /><meta property="og:image:height" content="600" /><meta property="og:site_name" content="GitHub" /><meta property="og:type" content="object" /><meta property="og:title" content="styleguide/intellij-java-google-style.xml at gh-pages · google/styleguide" /><meta property="og:url" content="https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml" /><meta property="og:description" content="Style guides for Google-originated open-source projects - google/styleguide" />
  




      <meta name="hostname" content="github.com">



        <meta name="expected-hostname" content="github.com">


  <meta http-equiv="x-pjax-version" content="0c020b7714a1d95a1d105d72c4f374193eeb06e57f04da050049d3a3ee7e3c2e" data-turbo-track="reload">
  <meta http-equiv="x-pjax-csp-version" content="352e51c42d5f5727a7c545752bf34d1f83f40219e7036c6959817149a51651bc" data-turbo-track="reload">
  <meta http-equiv="x-pjax-css-version" content="96420a9d2e68ebd61a2b29383e5bbff85aa3a271ef056890a042ab19d09f23b5" data-turbo-track="reload">
  <meta http-equiv="x-pjax-js-version" content="b8ac63fef2517b94f33fa1c245d7888eb5f00c4f199f2dbba76c7642dc2bbd9e" data-turbo-track="reload">

  <meta name="turbo-cache-control" content="no-preview" data-turbo-transient="">

      <meta name="turbo-cache-control" content="no-cache" data-turbo-transient>

    <meta data-hydrostats="publish">

  <meta name="go-import" content="github.com/google/styleguide git https://github.com/google/styleguide.git">

  <meta name="octolytics-dimension-user_id" content="1342004" /><meta name="octolytics-dimension-user_login" content="google" /><meta name="octolytics-dimension-repository_id" content="35969061" /><meta name="octolytics-dimension-repository_nwo" content="google/styleguide" /><meta name="octolytics-dimension-repository_public" content="true" /><meta name="octolytics-dimension-repository_is_fork" content="false" /><meta name="octolytics-dimension-repository_network_root_id" content="35969061" /><meta name="octolytics-dimension-repository_network_root_nwo" content="google/styleguide" />



    

    <meta name="turbo-body-classes" content="logged-out env-production page-responsive">


  <meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats">

  <meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors">

  <meta name="release" content="30273de1cbda84002a1023399536c4f03f8d856b">
  <meta name="ui-target" content="full">

  <link rel="mask-icon" href="https://github.githubassets.com/assets/pinned-octocat-093da3e6fa40.svg" color="#000000">
  <link rel="alternate icon" class="js-site-favicon" type="image/png" href="https://github.githubassets.com/favicons/favicon.png">
  <link rel="icon" class="js-site-favicon" type="image/svg+xml" href="https://github.githubassets.com/favicons/favicon.svg" data-base-href="https://github.githubassets.com/favicons/favicon">

<meta name="theme-color" content="#1e2327">
<meta name="color-scheme" content="light dark" />


  <link rel="manifest" href="/manifest.json" crossOrigin="use-credentials">

  </head>

  <body class="logged-out env-production page-responsive" style="word-wrap: break-word;">
    <div data-turbo-body class="logged-out env-production page-responsive" style="word-wrap: break-word;">
      



    <div class="position-relative header-wrapper js-header-wrapper ">
      <a href="#start-of-content" data-skip-target-assigned="false" class="px-2 py-4 color-bg-accent-emphasis color-fg-on-emphasis show-on-focus js-skip-to-content">Skip to content</a>

      <span data-view-component="true" class="progress-pjax-loader Progress position-fixed width-full">
    <span style="width: 0%;" data-view-component="true" class="Progress-item progress-pjax-loader-bar left-0 top-0 color-bg-accent-emphasis"></span>
</span>      
      
      <script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_ui-commands_ui-commands_ts-b755d908e0b1.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/keyboard-shortcuts-dialog-b3dd4b1cb532.js" defer="defer"></script>
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.8d5e42bdd3cd6a27871d.module.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/keyboard-shortcuts-dialog.47de85e2c17af43cefd5.module.css" />

<react-partial
  partial-name="keyboard-shortcuts-dialog"
  data-ssr="false"
  data-attempted-ssr="false"
  data-react-profiling="false"
>
  
  <script type="application/json" data-target="react-partial.embeddedData">{"props":{"docsUrl":"https://docs.github.com/get-started/accessibility/keyboard-shortcuts"}}</script>
  <div data-target="react-partial.reactRoot"></div>
</react-partial>




      

          

              
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67-99b04cc350b5.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/sessions-6652689d63d9.js" defer="defer"></script>
<header class="HeaderMktg header-logged-out js-details-container js-header Details f4 py-3" role="banner" data-is-top="true" data-color-mode=light data-light-theme=light data-dark-theme=dark>
  <h2 class="sr-only">Navigation Menu</h2>

  <button type="button" class="HeaderMktg-backdrop d-lg-none border-0 position-fixed top-0 left-0 width-full height-full js-details-target" aria-label="Toggle navigation">
    <span class="d-none">Toggle navigation</span>
  </button>

  <div class="d-flex flex-column flex-lg-row flex-items-center px-3 px-md-4 px-lg-5 height-full position-relative z-1">
    <div class="d-flex flex-justify-between flex-items-center width-full width-lg-auto">
      <div class="flex-1">
        <button aria-label="Toggle navigation" aria-expanded="false" type="button" data-view-component="true" class="js-details-target js-nav-padding-recalculate js-header-menu-toggle Button--link Button--medium Button d-lg-none color-fg-inherit p-1">  <span class="Button-content">
    <span class="Button-label"><div class="HeaderMenu-toggle-bar rounded my-1"></div>
            <div class="HeaderMenu-toggle-bar rounded my-1"></div>
            <div class="HeaderMenu-toggle-bar rounded my-1"></div></span>
  </span>
</button>
      </div>

      <a class="mr-lg-3 color-fg-inherit flex-order-2 js-prevent-focus-on-mobile-nav"
        href="/"
        aria-label="Homepage"
        data-analytics-event="{&quot;category&quot;:&quot;Marketing nav&quot;,&quot;action&quot;:&quot;click to go to homepage&quot;,&quot;label&quot;:&quot;ref_page:Marketing;ref_cta:Logomark;ref_loc:Header&quot;}">
        <svg height="32" aria-hidden="true" viewBox="0 0 24 24" version="1.1" width="32" data-view-component="true" class="octicon octicon-mark-github">
    <path d="M12 1C5.923 1 1 5.923 1 12c0 4.867 3.149 8.979 7.521 10.436.55.096.756-.233.756-.522 0-.262-.013-1.128-.013-2.049-2.764.509-3.479-.674-3.699-1.292-.124-.317-.66-1.293-1.127-1.554-.385-.207-.936-.715-.014-.729.866-.014 1.485.797 1.691 1.128.99 1.663 2.571 1.196 3.204.907.096-.715.385-1.196.701-1.471-2.448-.275-5.005-1.224-5.005-5.432 0-1.196.426-2.186 1.128-2.956-.111-.275-.496-1.402.11-2.915 0 0 .921-.288 3.024 1.128a10.193 10.193 0 0 1 2.75-.371c.936 0 1.871.123 2.75.371 2.104-1.43 3.025-1.128 3.025-1.128.605 1.513.221 2.64.111 2.915.701.77 1.127 1.747 1.127 2.956 0 4.222-2.571 5.157-5.019 5.432.399.344.743 1.004.743 2.035 0 1.471-.014 2.654-.014 3.025 0 .289.206.632.756.522C19.851 20.979 23 16.854 23 12c0-6.077-4.922-11-11-11Z"></path>
</svg>
      </a>

      <div class="d-flex flex-1 flex-order-2 text-right d-lg-none gap-2 flex-justify-end">
          <a
            href="/login?return_to=https%3A%2F%2Fgithub.com%2Fgoogle%2Fstyleguide%2Fblob%2Fgh-pages%2Fintellij-java-google-style.xml"
            class="HeaderMenu-link HeaderMenu-button d-inline-flex f5 no-underline border color-border-default rounded-2 px-2 py-1 color-fg-inherit js-prevent-focus-on-mobile-nav"
            data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="45830fd05ddcb5dab1fd081a883123cd4ce59dba77ef03bbfd4deb80401a7c5d"
            data-analytics-event="{&quot;category&quot;:&quot;Marketing nav&quot;,&quot;action&quot;:&quot;click to Sign in&quot;,&quot;label&quot;:&quot;ref_page:Marketing;ref_cta:Sign in;ref_loc:Header&quot;}"
          >
            Sign in
          </a>
              <div class="AppHeader-appearanceSettings">
    <react-partial-anchor>
      <button data-target="react-partial-anchor.anchor" id="icon-button-4921b37f-d9ec-47c7-8a71-2d0f564532ca" aria-labelledby="tooltip-7c4bab6b-54e4-4836-8aa8-05e5b9d9f506" type="button" disabled="disabled" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium AppHeader-button HeaderMenu-link border cursor-wait">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-sliders Button-visual">
    <path d="M15 2.75a.75.75 0 0 1-.75.75h-4a.75.75 0 0 1 0-1.5h4a.75.75 0 0 1 .75.75Zm-8.5.75v1.25a.75.75 0 0 0 1.5 0v-4a.75.75 0 0 0-1.5 0V2H1.75a.75.75 0 0 0 0 1.5H6.5Zm1.25 5.25a.75.75 0 0 0 0-1.5h-6a.75.75 0 0 0 0 1.5h6ZM15 8a.75.75 0 0 1-.75.75H11.5V10a.75.75 0 1 1-1.5 0V6a.75.75 0 0 1 1.5 0v1.25h2.75A.75.75 0 0 1 15 8Zm-9 5.25v-2a.75.75 0 0 0-1.5 0v1.25H1.75a.75.75 0 0 0 0 1.5H4.5v1.25a.75.75 0 0 0 1.5 0v-2Zm9 0a.75.75 0 0 1-.75.75h-6a.75.75 0 0 1 0-1.5h6a.75.75 0 0 1 .75.75Z"></path>
</svg>
</button><tool-tip id="tooltip-7c4bab6b-54e4-4836-8aa8-05e5b9d9f506" for="icon-button-4921b37f-d9ec-47c7-8a71-2d0f564532ca" popover="manual" data-direction="s" data-type="label" data-view-component="true" class="sr-only position-absolute">Appearance settings</tool-tip>

      <template data-target="react-partial-anchor.template">
        <script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a-b50af437b812.js" defer="defer"></script>
<script crossorigin="anonymous" type="application/javascript" src="https://github.githubassets.com/assets/appearance-settings-631c3b2ed371.js" defer="defer"></script>
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.8d5e42bdd3cd6a27871d.module.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/appearance-settings.4e1ca273f504ba849f8c.module.css" />

<react-partial
  partial-name="appearance-settings"
  data-ssr="false"
  data-attempted-ssr="false"
  data-react-profiling="false"
>
  
  <script type="application/json" data-target="react-partial.embeddedData">{"props":{}}</script>
  <div data-target="react-partial.reactRoot"></div>
</react-partial>

      </template>
    </react-partial-anchor>
  </div>

      </div>
    </div>


    <div class="HeaderMenu js-header-menu height-fit position-lg-relative d-lg-flex flex-column flex-auto top-0">
      <div class="HeaderMenu-wrapper d-flex flex-column flex-self-start flex-lg-row flex-auto rounded rounded-lg-0">
          <nav class="HeaderMenu-nav" aria-label="Global">
            <ul class="d-lg-flex list-style-none">


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
      <button type="button" class="HeaderMenu-link border-0 width-full width-lg-auto px-0 px-lg-2 py-lg-2 no-wrap d-flex flex-items-center flex-justify-between js-details-target" aria-expanded="false">
        Product
        <svg opacity="0.5" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-down HeaderMenu-icon ml-1">
    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"></path>
</svg>
      </button>

      <div class="HeaderMenu-dropdown dropdown-menu rounded m-0 p-0 pt-2 pt-lg-4 position-relative position-lg-absolute left-0 left-lg-n3 pb-2 pb-lg-4 d-lg-flex flex-wrap dropdown-menu-wide">
          <div class="HeaderMenu-column pl-lg-4 px-lg-4">
              <div class="">

                <ul class="list-style-none f5" >
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_copilot&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_copilot_link_product_navbar&quot;}" href="https://github.com/features/copilot">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-copilot color-fg-subtle mr-3">
    <path d="M23.922 16.992c-.861 1.495-5.859 5.023-11.922 5.023-6.063 0-11.061-3.528-11.922-5.023A.641.641 0 0 1 0 16.736v-2.869a.841.841 0 0 1 .053-.22c.372-.935 1.347-2.292 2.605-2.656.167-.429.414-1.055.644-1.517a10.195 10.195 0 0 1-.052-1.086c0-1.331.282-2.499 1.132-3.368.397-.406.89-.717 1.474-.952 1.399-1.136 3.392-2.093 6.122-2.093 2.731 0 4.767.957 6.166 2.093.584.235 1.077.546 1.474.952.85.869 1.132 2.037 1.132 3.368 0 .368-.014.733-.052 1.086.23.462.477 1.088.644 1.517 1.258.364 2.233 1.721 2.605 2.656a.832.832 0 0 1 .053.22v2.869a.641.641 0 0 1-.078.256ZM12.172 11h-.344a4.323 4.323 0 0 1-.355.508C10.703 12.455 9.555 13 7.965 13c-1.725 0-2.989-.359-3.782-1.259a2.005 2.005 0 0 1-.085-.104L4 11.741v6.585c1.435.779 4.514 2.179 8 2.179 3.486 0 6.565-1.4 8-2.179v-6.585l-.098-.104s-.033.045-.085.104c-.793.9-2.057 1.259-3.782 1.259-1.59 0-2.738-.545-3.508-1.492a4.323 4.323 0 0 1-.355-.508h-.016.016Zm.641-2.935c.136 1.057.403 1.913.878 2.497.442.544 1.134.938 2.344.938 1.573 0 2.292-.337 2.657-.751.384-.435.558-1.15.558-2.361 0-1.14-.243-1.847-.705-2.319-.477-.488-1.319-.862-2.824-1.025-1.487-.161-2.192.138-2.533.529-.269.307-.437.808-.438 1.578v.021c0 .265.021.562.063.893Zm-1.626 0c.042-.331.063-.628.063-.894v-.02c-.001-.77-.169-1.271-.438-1.578-.341-.391-1.046-.69-2.533-.529-1.505.163-2.347.537-2.824 1.025-.462.472-.705 1.179-.705 2.319 0 1.211.175 1.926.558 2.361.365.414 1.084.751 2.657.751 1.21 0 1.902-.394 2.344-.938.475-.584.742-1.44.878-2.497Z"></path><path d="M14.5 14.25a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Zm-5 0a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            GitHub Copilot
          </div>
        Write better code with AI
      </div>

    
</a></li>

                    
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_models&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_models_link_product_navbar&quot;}" href="https://github.com/features/models">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-ai-model color-fg-subtle mr-3">
    <path d="M19.375 8.5a3.25 3.25 0 1 1-3.163 4h-3a3.252 3.252 0 0 1-4.443 2.509L7.214 17.76a3.25 3.25 0 1 1-1.342-.674l1.672-2.957A3.238 3.238 0 0 1 6.75 12c0-.907.371-1.727.97-2.316L6.117 6.846A3.253 3.253 0 0 1 1.875 3.75a3.25 3.25 0 1 1 5.526 2.32l1.603 2.836A3.25 3.25 0 0 1 13.093 11h3.119a3.252 3.252 0 0 1 3.163-2.5ZM10 10.25a1.75 1.75 0 1 0-.001 3.499A1.75 1.75 0 0 0 10 10.25ZM5.125 2a1.75 1.75 0 1 0 0 3.5 1.75 1.75 0 0 0 0-3.5Zm12.5 9.75a1.75 1.75 0 1 0 3.5 0 1.75 1.75 0 0 0-3.5 0Zm-14.25 8.5a1.75 1.75 0 1 0 3.501-.001 1.75 1.75 0 0 0-3.501.001Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            GitHub Models
              <span class="HeaderMenu-label">
                New
              </span>
          </div>
        Manage and compare prompts
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_product_navbar&quot;}" href="https://github.com/security/advanced-security">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-shield-check color-fg-subtle mr-3">
    <path d="M16.53 9.78a.75.75 0 0 0-1.06-1.06L11 13.19l-1.97-1.97a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l5-5Z"></path><path d="m12.54.637 8.25 2.675A1.75 1.75 0 0 1 22 4.976V10c0 6.19-3.771 10.704-9.401 12.83a1.704 1.704 0 0 1-1.198 0C5.77 20.705 2 16.19 2 10V4.976c0-.758.489-1.43 1.21-1.664L11.46.637a1.748 1.748 0 0 1 1.08 0Zm-.617 1.426-8.25 2.676a.249.249 0 0 0-.173.237V10c0 5.46 3.28 9.483 8.43 11.426a.199.199 0 0 0 .14 0C17.22 19.483 20.5 15.461 20.5 10V4.976a.25.25 0 0 0-.173-.237l-8.25-2.676a.253.253 0 0 0-.154 0Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            GitHub Advanced Security
          </div>
        Find and fix vulnerabilities
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;actions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;actions_link_product_navbar&quot;}" href="https://github.com/features/actions">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-workflow color-fg-subtle mr-3">
    <path d="M1 3a2 2 0 0 1 2-2h6.5a2 2 0 0 1 2 2v6.5a2 2 0 0 1-2 2H7v4.063C7 16.355 7.644 17 8.438 17H12.5v-2.5a2 2 0 0 1 2-2H21a2 2 0 0 1 2 2V21a2 2 0 0 1-2 2h-6.5a2 2 0 0 1-2-2v-2.5H8.437A2.939 2.939 0 0 1 5.5 15.562V11.5H3a2 2 0 0 1-2-2Zm2-.5a.5.5 0 0 0-.5.5v6.5a.5.5 0 0 0 .5.5h6.5a.5.5 0 0 0 .5-.5V3a.5.5 0 0 0-.5-.5ZM14.5 14a.5.5 0 0 0-.5.5V21a.5.5 0 0 0 .5.5H21a.5.5 0 0 0 .5-.5v-6.5a.5.5 0 0 0-.5-.5Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Actions
          </div>
        Automate any workflow
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;codespaces&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;codespaces_link_product_navbar&quot;}" href="https://github.com/features/codespaces">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-codespaces color-fg-subtle mr-3">
    <path d="M3.5 3.75C3.5 2.784 4.284 2 5.25 2h13.5c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 18.75 13H5.25a1.75 1.75 0 0 1-1.75-1.75Zm-2 12c0-.966.784-1.75 1.75-1.75h17.5c.966 0 1.75.784 1.75 1.75v4a1.75 1.75 0 0 1-1.75 1.75H3.25a1.75 1.75 0 0 1-1.75-1.75ZM5.25 3.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h13.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Zm-2 12a.25.25 0 0 0-.25.25v4c0 .*************.25h17.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25Z"></path><path d="M10 17.75a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Zm-4 0a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Codespaces
          </div>
        Instant dev environments
      </div>

    
</a></li>

                </ul>
              </div>
          </div>
          <div class="HeaderMenu-column pl-lg-4 px-lg-4 pb-3 pb-lg-0">
              <div class="border-bottom border-lg-bottom-0 pb-3">

                <ul class="list-style-none f5" >
                    
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;issues&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;issues_link_product_navbar&quot;}" href="https://github.com/features/issues">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-issue-opened color-fg-subtle mr-3">
    <path d="M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1ZM2.5 12a9.5 9.5 0 0 0 9.5 9.5 9.5 9.5 0 0 0 9.5-9.5A9.5 9.5 0 0 0 12 2.5 9.5 9.5 0 0 0 2.5 12Zm9.5 2a2 2 0 1 1-.001-3.999A2 2 0 0 1 12 14Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Issues
          </div>
        Plan and track work
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_review&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_review_link_product_navbar&quot;}" href="https://github.com/features/code-review">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-code-review color-fg-subtle mr-3">
    <path d="M10.3 6.74a.75.75 0 0 1-.04 1.06l-2.908 2.7 2.908 2.7a.75.75 0 1 1-1.02 1.1l-3.5-3.25a.75.75 0 0 1 0-1.1l3.5-3.25a.75.75 0 0 1 1.06.04Zm3.44 1.06a.75.75 0 1 1 1.02-1.1l3.5 3.25a.75.75 0 0 1 0 1.1l-3.5 3.25a.75.75 0 1 1-1.02-1.1l2.908-2.7-2.908-2.7Z"></path><path d="M1.5 4.25c0-.966.784-1.75 1.75-1.75h17.5c.966 0 1.75.784 1.75 1.75v12.5a1.75 1.75 0 0 1-1.75 1.75h-9.69l-3.573 3.573A1.458 1.458 0 0 1 5 21.043V18.5H3.25a1.75 1.75 0 0 1-1.75-1.75ZM3.25 4a.25.25 0 0 0-.25.25v12.5c0 .*************.25h2.5a.75.75 0 0 1 .75.75v3.19l3.72-3.72a.749.749 0 0 1 .53-.22h10a.25.25 0 0 0 .25-.25V4.25a.25.25 0 0 0-.25-.25Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Code Review
          </div>
        Manage code changes
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;discussions&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;discussions_link_product_navbar&quot;}" href="https://github.com/features/discussions">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-comment-discussion color-fg-subtle mr-3">
    <path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 14.25 14H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 15.543V14H1.75A1.75 1.75 0 0 1 0 12.25v-9.5C0 1.784.784 1 1.75 1ZM1.5 2.75v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Z"></path><path d="M22.5 8.75a.25.25 0 0 0-.25-.25h-3.5a.75.75 0 0 1 0-1.5h3.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 22.25 20H21v1.543a1.457 1.457 0 0 1-2.487 1.03L15.939 20H10.75A1.75 1.75 0 0 1 9 18.25v-1.465a.75.75 0 0 1 1.5 0v1.465c0 .*************.25h5.5a.75.75 0 0 1 .53.22l2.72 2.72v-2.19a.75.75 0 0 1 .75-.75h2a.25.25 0 0 0 .25-.25v-9.5Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Discussions
          </div>
        Collaborate outside of code
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;code_search&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;code_search_link_product_navbar&quot;}" href="https://github.com/features/code-search">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-code-square color-fg-subtle mr-3">
    <path d="M10.3 8.24a.75.75 0 0 1-.04 1.06L7.352 12l2.908 2.7a.75.75 0 1 1-1.02 1.1l-3.5-3.25a.75.75 0 0 1 0-1.1l3.5-3.25a.75.75 0 0 1 1.06.04Zm3.44 1.06a.75.75 0 1 1 1.02-1.1l3.5 3.25a.75.75 0 0 1 0 1.1l-3.5 3.25a.75.75 0 1 1-1.02-1.1l2.908-2.7-2.908-2.7Z"></path><path d="M2 3.75C2 2.784 2.784 2 3.75 2h16.5c.966 0 1.75.784 1.75 1.75v16.5A1.75 1.75 0 0 1 20.25 22H3.75A1.75 1.75 0 0 1 2 20.25Zm1.75-.25a.25.25 0 0 0-.25.25v16.5c0 .*************.25h16.5a.25.25 0 0 0 .25-.25V3.75a.25.25 0 0 0-.25-.25Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Code Search
          </div>
        Find more, search less
      </div>

    
</a></li>

                </ul>
              </div>
          </div>
          <div class="HeaderMenu-column pl-lg-4 border-lg-left pr-lg-7">
              <div class="border-bottom border-lg-bottom-0 border-bottom-0">
                    <span class="d-block h4 color-fg-default my-1" id="product-explore-heading">Explore</span>

                <ul class="list-style-none f5" aria-labelledby="product-explore-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;why_github&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;why_github_link_product_navbar&quot;}" href="https://github.com/why-github">
      Why GitHub

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;all_features&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;all_features_link_product_navbar&quot;}" href="https://github.com/features">
      All features

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;documentation&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;documentation_link_product_navbar&quot;}" href="https://docs.github.com">
      Documentation

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_skills&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_skills_link_product_navbar&quot;}" href="https://skills.github.com">
      GitHub Skills

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;blog&quot;,&quot;context&quot;:&quot;product&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;blog_link_product_navbar&quot;}" href="https://github.blog">
      Blog

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                </ul>
              </div>
          </div>

      </div>
</li>


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
      <button type="button" class="HeaderMenu-link border-0 width-full width-lg-auto px-0 px-lg-2 py-lg-2 no-wrap d-flex flex-items-center flex-justify-between js-details-target" aria-expanded="false">
        Solutions
        <svg opacity="0.5" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-down HeaderMenu-icon ml-1">
    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"></path>
</svg>
      </button>

      <div class="HeaderMenu-dropdown dropdown-menu rounded m-0 p-0 pt-2 pt-lg-4 position-relative position-lg-absolute left-0 left-lg-n3 d-lg-flex flex-wrap dropdown-menu-wide">
          <div class="HeaderMenu-column pl-lg-4 px-lg-4 pb-3 pb-lg-0">
              <div class="border-bottom border-lg-bottom-0 mb-3 pb-3">
                    <span class="d-block h4 color-fg-default my-1" id="solutions-by-company-size-heading">By company size</span>

                <ul class="list-style-none f5" aria-labelledby="solutions-by-company-size-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;enterprises&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;enterprises_link_solutions_navbar&quot;}" href="https://github.com/enterprise">
      Enterprises

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;small_and_medium_teams&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;small_and_medium_teams_link_solutions_navbar&quot;}" href="https://github.com/team">
      Small and medium teams

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;startups&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;startups_link_solutions_navbar&quot;}" href="https://github.com/enterprise/startups">
      Startups

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;nonprofits&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;nonprofits_link_solutions_navbar&quot;}" href="/solutions/industry/nonprofits">
      Nonprofits

    
</a></li>

                </ul>
              </div>
              <div class="border-bottom border-lg-bottom-0 pb-3">
                    <span class="d-block h4 color-fg-default my-1" id="solutions-by-use-case-heading">By use case</span>

                <ul class="list-style-none f5" aria-labelledby="solutions-by-use-case-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devsecops&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devsecops_link_solutions_navbar&quot;}" href="/solutions/use-case/devsecops">
      DevSecOps

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devops&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devops_link_solutions_navbar&quot;}" href="/solutions/use-case/devops">
      DevOps

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ci_cd&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ci_cd_link_solutions_navbar&quot;}" href="/solutions/use-case/ci-cd">
      CI/CD

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all_use_cases&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_use_cases_link_solutions_navbar&quot;}" href="/solutions/use-case">
      View all use cases

    
</a></li>

                </ul>
              </div>
          </div>
          <div class="HeaderMenu-column pl-lg-4 border-lg-left pr-lg-7">
              <div class="border-bottom border-lg-bottom-0 pb-3 pb-lg-0">
                    <span class="d-block h4 color-fg-default my-1" id="solutions-by-industry-heading">By industry</span>

                <ul class="list-style-none f5" aria-labelledby="solutions-by-industry-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;healthcare&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;healthcare_link_solutions_navbar&quot;}" href="/solutions/industry/healthcare">
      Healthcare

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;financial_services&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;financial_services_link_solutions_navbar&quot;}" href="/solutions/industry/financial-services">
      Financial services

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;manufacturing&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;manufacturing_link_solutions_navbar&quot;}" href="/solutions/industry/manufacturing">
      Manufacturing

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;government&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;government_link_solutions_navbar&quot;}" href="/solutions/industry/government">
      Government

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all_industries&quot;,&quot;context&quot;:&quot;solutions&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_industries_link_solutions_navbar&quot;}" href="/solutions/industry">
      View all industries

    
</a></li>

                </ul>
              </div>
          </div>

         <div class="HeaderMenu-trailing-link rounded-bottom-2 flex-shrink-0 mt-lg-4 px-lg-4 py-4 py-lg-3 f5 text-semibold">
            <a href="/solutions">
              View all solutions
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-right HeaderMenu-trailing-link-icon">
    <path d="M6.22 3.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L9.94 8 6.22 4.28a.75.75 0 0 1 0-1.06Z"></path>
</svg>
</a>         </div>
      </div>
</li>


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
      <button type="button" class="HeaderMenu-link border-0 width-full width-lg-auto px-0 px-lg-2 py-lg-2 no-wrap d-flex flex-items-center flex-justify-between js-details-target" aria-expanded="false">
        Resources
        <svg opacity="0.5" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-down HeaderMenu-icon ml-1">
    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"></path>
</svg>
      </button>

      <div class="HeaderMenu-dropdown dropdown-menu rounded m-0 p-0 pt-2 pt-lg-4 position-relative position-lg-absolute left-0 left-lg-n3 pb-2 pb-lg-4 d-lg-flex flex-wrap dropdown-menu-wide">
          <div class="HeaderMenu-column pl-lg-4 px-lg-4 pb-3 pb-lg-0">
              <div class="border-bottom border-lg-bottom-0 pb-3">
                    <span class="d-block h4 color-fg-default my-1" id="resources-topics-heading">Topics</span>

                <ul class="list-style-none f5" aria-labelledby="resources-topics-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ai&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ai_link_resources_navbar&quot;}" href="/resources/articles/ai">
      AI

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;devops&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;devops_link_resources_navbar&quot;}" href="/resources/articles/devops">
      DevOps

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;security&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;security_link_resources_navbar&quot;}" href="/resources/articles/security">
      Security

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;software_development&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;software_development_link_resources_navbar&quot;}" href="/resources/articles/software-development">
      Software Development

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;view_all&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;view_all_link_resources_navbar&quot;}" href="/resources/articles">
      View all

    
</a></li>

                </ul>
              </div>
          </div>
          <div class="HeaderMenu-column pl-lg-4 border-lg-left pr-lg-7">
              <div class="border-bottom border-lg-bottom-0 border-bottom-0">
                    <span class="d-block h4 color-fg-default my-1" id="resources-explore-heading">Explore</span>

                <ul class="list-style-none f5" aria-labelledby="resources-explore-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;learning_pathways&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;learning_pathways_link_resources_navbar&quot;}" href="https://resources.github.com/learn/pathways">
      Learning Pathways

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;events_amp_webinars&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;events_amp_webinars_link_resources_navbar&quot;}" href="https://resources.github.com">
      Events &amp; Webinars

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;ebooks_amp_whitepapers&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;ebooks_amp_whitepapers_link_resources_navbar&quot;}" href="https://github.com/resources/whitepapers">
      Ebooks &amp; Whitepapers

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;customer_stories&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;customer_stories_link_resources_navbar&quot;}" href="https://github.com/customer-stories">
      Customer Stories

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary Link--external" target="_blank" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;partners&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;partners_link_resources_navbar&quot;}" href="https://partner.github.com">
      Partners

    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-link-external HeaderMenu-external-icon color-fg-subtle">
    <path d="M3.75 2h3.5a.75.75 0 0 1 0 1.5h-3.5a.25.25 0 0 0-.25.25v8.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-3.5a.75.75 0 0 1 1.5 0v3.5A1.75 1.75 0 0 1 12.25 14h-8.5A1.75 1.75 0 0 1 2 12.25v-8.5C2 2.784 2.784 2 3.75 2Zm6.854-1h4.146a.25.25 0 0 1 .25.25v4.146a.25.25 0 0 1-.427.177L13.03 4.03 9.28 7.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.75-3.75-1.543-1.543A.25.25 0 0 1 10.604 1Z"></path>
</svg>
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;executive_insights&quot;,&quot;context&quot;:&quot;resources&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;executive_insights_link_resources_navbar&quot;}" href="https://github.com/solutions/executive-insights">
      Executive Insights

    
</a></li>

                </ul>
              </div>
          </div>

      </div>
</li>


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
      <button type="button" class="HeaderMenu-link border-0 width-full width-lg-auto px-0 px-lg-2 py-lg-2 no-wrap d-flex flex-items-center flex-justify-between js-details-target" aria-expanded="false">
        Open Source
        <svg opacity="0.5" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-down HeaderMenu-icon ml-1">
    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"></path>
</svg>
      </button>

      <div class="HeaderMenu-dropdown dropdown-menu rounded m-0 p-0 pt-2 pt-lg-4 position-relative position-lg-absolute left-0 left-lg-n3 pb-2 pb-lg-4">
          <div class="HeaderMenu-column px-lg-4">
              <div class="border-bottom mb-3 mb-lg-3 pb-3">

                <ul class="list-style-none f5" >
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_sponsors&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_sponsors_link_open_source_navbar&quot;}" href="/sponsors">
      
      <div>
          <div class="color-fg-default h4">
            GitHub Sponsors
          </div>
        Fund open source developers
      </div>

    
</a></li>

                </ul>
              </div>
              <div class="border-bottom mb-3 mb-lg-3 pb-3">

                <ul class="list-style-none f5" >
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;the_readme_project&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;the_readme_project_link_open_source_navbar&quot;}" href="https://github.com/readme">
      
      <div>
          <div class="color-fg-default h4">
            The ReadME Project
          </div>
        GitHub community articles
      </div>

    
</a></li>

                </ul>
              </div>
              <div class="border-bottom border-bottom-0">
                    <span class="d-block h4 color-fg-default my-1" id="open-source-repositories-heading">Repositories</span>

                <ul class="list-style-none f5" aria-labelledby="open-source-repositories-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;topics&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;topics_link_open_source_navbar&quot;}" href="https://github.com/topics">
      Topics

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;trending&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;trending_link_open_source_navbar&quot;}" href="https://github.com/trending">
      Trending

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;collections&quot;,&quot;context&quot;:&quot;open_source&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;collections_link_open_source_navbar&quot;}" href="https://github.com/collections">
      Collections

    
</a></li>

                </ul>
              </div>
          </div>

      </div>
</li>


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
      <button type="button" class="HeaderMenu-link border-0 width-full width-lg-auto px-0 px-lg-2 py-lg-2 no-wrap d-flex flex-items-center flex-justify-between js-details-target" aria-expanded="false">
        Enterprise
        <svg opacity="0.5" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-chevron-down HeaderMenu-icon ml-1">
    <path d="M12.78 5.22a.749.749 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.06 0L3.22 6.28a.749.749 0 1 1 1.06-1.06L8 8.939l3.72-3.719a.749.749 0 0 1 1.06 0Z"></path>
</svg>
      </button>

      <div class="HeaderMenu-dropdown dropdown-menu rounded m-0 p-0 pt-2 pt-lg-4 position-relative position-lg-absolute left-0 left-lg-n3 pb-2 pb-lg-4">
          <div class="HeaderMenu-column px-lg-4">
              <div class="border-bottom mb-3 mb-lg-3 pb-3">

                <ul class="list-style-none f5" >
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;enterprise_platform&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;enterprise_platform_link_enterprise_navbar&quot;}" href="/enterprise">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-stack color-fg-subtle mr-3">
    <path d="M11.063 1.456a1.749 1.749 0 0 1 1.874 0l8.383 5.316a1.751 1.751 0 0 1 0 2.956l-8.383 5.316a1.749 1.749 0 0 1-1.874 0L2.68 9.728a1.751 1.751 0 0 1 0-2.956Zm1.071 1.267a.25.25 0 0 0-.268 0L3.483 8.039a.25.25 0 0 0 0 .422l8.383 5.316a.25.25 0 0 0 .268 0l8.383-5.316a.25.25 0 0 0 0-.422Z"></path><path d="M1.867 12.324a.75.75 0 0 1 1.035-.232l8.964 5.685a.25.25 0 0 0 .268 0l8.964-5.685a.75.75 0 0 1 .804 1.267l-8.965 5.685a1.749 1.749 0 0 1-1.874 0l-8.965-5.685a.75.75 0 0 1-.231-1.035Z"></path><path d="M1.867 16.324a.75.75 0 0 1 1.035-.232l8.964 5.685a.25.25 0 0 0 .268 0l8.964-5.685a.75.75 0 0 1 .804 1.267l-8.965 5.685a1.749 1.749 0 0 1-1.874 0l-8.965-5.685a.75.75 0 0 1-.231-1.035Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Enterprise platform
          </div>
        AI-powered developer platform
      </div>

    
</a></li>

                </ul>
              </div>
              <div class="border-bottom border-bottom-0">
                    <span class="d-block h4 color-fg-default my-1" id="enterprise-available-add-ons-heading">Available add-ons</span>

                <ul class="list-style-none f5" aria-labelledby="enterprise-available-add-ons-heading">
                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;github_advanced_security&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;github_advanced_security_link_enterprise_navbar&quot;}" href="https://github.com/security/advanced-security">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-shield-check color-fg-subtle mr-3">
    <path d="M16.53 9.78a.75.75 0 0 0-1.06-1.06L11 13.19l-1.97-1.97a.75.75 0 0 0-1.06 1.06l2.5 2.5a.75.75 0 0 0 1.06 0l5-5Z"></path><path d="m12.54.637 8.25 2.675A1.75 1.75 0 0 1 22 4.976V10c0 6.19-3.771 10.704-9.401 12.83a1.704 1.704 0 0 1-1.198 0C5.77 20.705 2 16.19 2 10V4.976c0-.758.489-1.43 1.21-1.664L11.46.637a1.748 1.748 0 0 1 1.08 0Zm-.617 1.426-8.25 2.676a.249.249 0 0 0-.173.237V10c0 5.46 3.28 9.483 8.43 11.426a.199.199 0 0 0 .14 0C17.22 19.483 20.5 15.461 20.5 10V4.976a.25.25 0 0 0-.173-.237l-8.25-2.676a.253.253 0 0 0-.154 0Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            GitHub Advanced Security
          </div>
        Enterprise-grade security features
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description pb-lg-3" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;copilot_for_business&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;copilot_for_business_link_enterprise_navbar&quot;}" href="/features/copilot/copilot-business">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-copilot color-fg-subtle mr-3">
    <path d="M23.922 16.992c-.861 1.495-5.859 5.023-11.922 5.023-6.063 0-11.061-3.528-11.922-5.023A.641.641 0 0 1 0 16.736v-2.869a.841.841 0 0 1 .053-.22c.372-.935 1.347-2.292 2.605-2.656.167-.429.414-1.055.644-1.517a10.195 10.195 0 0 1-.052-1.086c0-1.331.282-2.499 1.132-3.368.397-.406.89-.717 1.474-.952 1.399-1.136 3.392-2.093 6.122-2.093 2.731 0 4.767.957 6.166 2.093.584.235 1.077.546 1.474.952.85.869 1.132 2.037 1.132 3.368 0 .368-.014.733-.052 1.086.23.462.477 1.088.644 1.517 1.258.364 2.233 1.721 2.605 2.656a.832.832 0 0 1 .053.22v2.869a.641.641 0 0 1-.078.256ZM12.172 11h-.344a4.323 4.323 0 0 1-.355.508C10.703 12.455 9.555 13 7.965 13c-1.725 0-2.989-.359-3.782-1.259a2.005 2.005 0 0 1-.085-.104L4 11.741v6.585c1.435.779 4.514 2.179 8 2.179 3.486 0 6.565-1.4 8-2.179v-6.585l-.098-.104s-.033.045-.085.104c-.793.9-2.057 1.259-3.782 1.259-1.59 0-2.738-.545-3.508-1.492a4.323 4.323 0 0 1-.355-.508h-.016.016Zm.641-2.935c.136 1.057.403 1.913.878 2.497.442.544 1.134.938 2.344.938 1.573 0 2.292-.337 2.657-.751.384-.435.558-1.15.558-2.361 0-1.14-.243-1.847-.705-2.319-.477-.488-1.319-.862-2.824-1.025-1.487-.161-2.192.138-2.533.529-.269.307-.437.808-.438 1.578v.021c0 .265.021.562.063.893Zm-1.626 0c.042-.331.063-.628.063-.894v-.02c-.001-.77-.169-1.271-.438-1.578-.341-.391-1.046-.69-2.533-.529-1.505.163-2.347.537-2.824 1.025-.462.472-.705 1.179-.705 2.319 0 1.211.175 1.926.558 2.361.365.414 1.084.751 2.657.751 1.21 0 1.902-.394 2.344-.938.475-.584.742-1.44.878-2.497Z"></path><path d="M14.5 14.25a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Zm-5 0a1 1 0 0 1 1 1v2a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Copilot for business
          </div>
        Enterprise-grade AI features
      </div>

    
</a></li>

                    <li>
  <a class="HeaderMenu-dropdown-link d-block no-underline position-relative py-2 Link--secondary d-flex flex-items-center Link--has-description" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;premium_support&quot;,&quot;context&quot;:&quot;enterprise&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;premium_support_link_enterprise_navbar&quot;}" href="/premium-support">
      <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-comment-discussion color-fg-subtle mr-3">
    <path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 14.25 14H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 15.543V14H1.75A1.75 1.75 0 0 1 0 12.25v-9.5C0 1.784.784 1 1.75 1ZM1.5 2.75v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Z"></path><path d="M22.5 8.75a.25.25 0 0 0-.25-.25h-3.5a.75.75 0 0 1 0-1.5h3.5c.966 0 1.75.784 1.75 1.75v9.5A1.75 1.75 0 0 1 22.25 20H21v1.543a1.457 1.457 0 0 1-2.487 1.03L15.939 20H10.75A1.75 1.75 0 0 1 9 18.25v-1.465a.75.75 0 0 1 1.5 0v1.465c0 .*************.25h5.5a.75.75 0 0 1 .53.22l2.72 2.72v-2.19a.75.75 0 0 1 .75-.75h2a.25.25 0 0 0 .25-.25v-9.5Z"></path>
</svg>
      <div>
          <div class="color-fg-default h4">
            Premium Support
          </div>
        Enterprise-grade 24/7 support
      </div>

    
</a></li>

                </ul>
              </div>
          </div>

      </div>
</li>


                <li class="HeaderMenu-item position-relative flex-wrap flex-justify-between flex-items-center d-block d-lg-flex flex-lg-nowrap flex-lg-items-center js-details-container js-header-menu-item">
    <a class="HeaderMenu-link no-underline px-0 px-lg-2 py-3 py-lg-2 d-block d-lg-inline-block" data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;pricing&quot;,&quot;context&quot;:&quot;global&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;pricing_link_global_navbar&quot;}" href="https://github.com/pricing">Pricing</a>
</li>

            </ul>
          </nav>

        <div class="d-flex flex-column flex-lg-row width-full flex-justify-end flex-lg-items-center text-center mt-3 mt-lg-0 text-lg-left ml-lg-3">
                


<qbsearch-input class="search-input" data-scope="repo:google/styleguide" data-custom-scopes-path="/search/custom_scopes" data-delete-custom-scopes-csrf="cckb5s5SZORQNameB0VnISdu_AXzjrJ93O8ES9Ntx59ngAxFnYx1bBfh5TJ_895M3bXTwHKVJyP4mwwHFgRItg" data-max-custom-scopes="10" data-header-redesign-enabled="false" data-initial-value="" data-blackbird-suggestions-path="/search/suggestions" data-jump-to-suggestions-path="/_graphql/GetSuggestedNavigationDestinations" data-current-repository="google/styleguide" data-current-org="google" data-current-owner="" data-logged-in="false" data-copilot-chat-enabled="false" data-nl-search-enabled="false" data-retain-scroll-position="true">
  <div
    class="search-input-container search-with-dialog position-relative d-flex flex-row flex-items-center mr-4 rounded"
    data-action="click:qbsearch-input#searchInputContainerClicked"
  >
      <button
        type="button"
        class="header-search-button placeholder  input-button form-control d-flex flex-1 flex-self-stretch flex-items-center no-wrap width-full py-0 pl-2 pr-0 text-left border-0 box-shadow-none"
        data-target="qbsearch-input.inputButton"
        aria-label="Search or jump to…"
        aria-haspopup="dialog"
        placeholder="Search or jump to..."
        data-hotkey=s,/
        autocapitalize="off"
        data-analytics-event="{&quot;location&quot;:&quot;navbar&quot;,&quot;action&quot;:&quot;searchbar&quot;,&quot;context&quot;:&quot;global&quot;,&quot;tag&quot;:&quot;input&quot;,&quot;label&quot;:&quot;searchbar_input_global_navbar&quot;}"
        data-action="click:qbsearch-input#handleExpand"
      >
        <div class="mr-2 color-fg-muted">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search">
    <path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"></path>
</svg>
        </div>
        <span class="flex-1" data-target="qbsearch-input.inputButtonText">Search or jump to...</span>
          <div class="d-flex" data-target="qbsearch-input.hotkeyIndicator">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" aria-hidden="true" class="mr-1"><path fill="none" stroke="#979A9C" opacity=".4" d="M3.5.5h12c1.7 0 3 1.3 3 3v13c0 1.7-1.3 3-3 3h-12c-1.7 0-3-1.3-3-3v-13c0-1.7 1.3-3 3-3z"></path><path fill="#979A9C" d="M11.8 6L8 15.1h-.9L10.8 6h1z"></path></svg>
          </div>
      </button>

    <input type="hidden" name="type" class="js-site-search-type-field">

    
<div class="Overlay--hidden " data-modal-dialog-overlay>
  <modal-dialog data-action="close:qbsearch-input#handleClose cancel:qbsearch-input#handleClose" data-target="qbsearch-input.searchSuggestionsDialog" role="dialog" id="search-suggestions-dialog" aria-modal="true" aria-labelledby="search-suggestions-dialog-header" data-view-component="true" class="Overlay Overlay--width-large Overlay--height-auto">
      <h1 id="search-suggestions-dialog-header" class="sr-only">Search code, repositories, users, issues, pull requests...</h1>
    <div class="Overlay-body Overlay-body--paddingNone">
      
          <div data-view-component="true">        <div class="search-suggestions position-fixed width-full color-shadow-large border color-fg-default color-bg-default overflow-hidden d-flex flex-column query-builder-container"
          style="border-radius: 12px;"
          data-target="qbsearch-input.queryBuilderContainer"
          hidden
        >
          <!-- '"` --><!-- </textarea></xmp> --></option></form><form id="query-builder-test-form" action="" accept-charset="UTF-8" method="get">
  <query-builder data-target="qbsearch-input.queryBuilder" id="query-builder-query-builder-test" data-filter-key=":" data-view-component="true" class="QueryBuilder search-query-builder">
    <div class="FormControl FormControl--fullWidth">
      <label id="query-builder-test-label" for="query-builder-test" class="FormControl-label sr-only">
        Search
      </label>
      <div
        class="QueryBuilder-StyledInput width-fit "
        data-target="query-builder.styledInput"
      >
          <span id="query-builder-test-leadingvisual-wrap" class="FormControl-input-leadingVisualWrap QueryBuilder-leadingVisualWrap">
            <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search FormControl-input-leadingVisual">
    <path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"></path>
</svg>
          </span>
        <div data-target="query-builder.styledInputContainer" class="QueryBuilder-StyledInputContainer">
          <div
            aria-hidden="true"
            class="QueryBuilder-StyledInputContent"
            data-target="query-builder.styledInputContent"
          ></div>
          <div class="QueryBuilder-InputWrapper">
            <div aria-hidden="true" class="QueryBuilder-Sizer" data-target="query-builder.sizer"></div>
            <input id="query-builder-test" name="query-builder-test" value="" autocomplete="off" type="text" role="combobox" spellcheck="false" aria-expanded="false" aria-describedby="validation-795d10d0-44fb-4891-9cd9-e1128d3941f8" data-target="query-builder.input" data-action="
          input:query-builder#inputChange
          blur:query-builder#inputBlur
          keydown:query-builder#inputKeydown
          focus:query-builder#inputFocus
        " data-view-component="true" class="FormControl-input QueryBuilder-Input FormControl-medium" />
          </div>
        </div>
          <span class="sr-only" id="query-builder-test-clear">Clear</span>
          <button role="button" id="query-builder-test-clear-button" aria-labelledby="query-builder-test-clear query-builder-test-label" data-target="query-builder.clearButton" data-action="
                click:query-builder#clear
                focus:query-builder#clearButtonFocus
                blur:query-builder#clearButtonBlur
              " variant="small" hidden="hidden" type="button" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium mr-1 px-2 py-0 d-flex flex-items-center rounded-1 color-fg-muted">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x-circle-fill Button-visual">
    <path d="M2.343 13.657A8 8 0 1 1 13.658 2.343 8 8 0 0 1 2.343 13.657ZM6.03 4.97a.751.751 0 0 0-1.042.018.751.751 0 0 0-.018 1.042L6.94 8 4.97 9.97a.749.749 0 0 0 .326 1.275.749.749 0 0 0 .734-.215L8 9.06l1.97 1.97a.749.749 0 0 0 1.275-.326.749.749 0 0 0-.215-.734L9.06 8l1.97-1.97a.749.749 0 0 0-.326-1.275.749.749 0 0 0-.734.215L8 6.94Z"></path>
</svg>
</button>

      </div>
      <template id="search-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search">
    <path d="M10.68 11.74a6 6 0 0 1-7.922-8.982 6 6 0 0 1 8.982 7.922l3.04 3.04a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215ZM11.5 7a4.499 4.499 0 1 0-8.997 0A4.499 4.499 0 0 0 11.5 7Z"></path>
</svg>
</template>

<template id="code-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code">
    <path d="m11.28 3.22 4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L13.94 8l-3.72-3.72a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215Zm-6.56 0a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042L2.06 8l3.72 3.72a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L.47 8.53a.75.75 0 0 1 0-1.06Z"></path>
</svg>
</template>

<template id="file-code-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-file-code">
    <path d="M4 1.75C4 .784 4.784 0 5.75 0h5.586c.464 0 .909.184 1.237.513l2.914 2.914c.329.328.513.773.513 1.237v8.586A1.75 1.75 0 0 1 14.25 15h-9a.75.75 0 0 1 0-1.5h9a.25.25 0 0 0 .25-.25V6h-2.75A1.75 1.75 0 0 1 10 4.25V1.5H5.75a.25.25 0 0 0-.25.25v2.5a.75.75 0 0 1-1.5 0Zm1.72 4.97a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734l1.47-1.47-1.47-1.47a.75.75 0 0 1 0-1.06ZM3.28 7.78 1.81 9.25l1.47 1.47a.751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018l-2-2a.75.75 0 0 1 0-1.06l2-2a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Zm8.22-6.218V4.25c0 .*************.25h2.688l-.011-.013-2.914-2.914-.013-.011Z"></path>
</svg>
</template>

<template id="history-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-history">
    <path d="m.427 1.927 1.215 1.215a8.002 8.002 0 1 1-1.6 5.685.75.75 0 1 1 1.493-.154 6.5 6.5 0 1 0 1.18-4.458l1.358 1.358A.25.25 0 0 1 3.896 6H.25A.25.25 0 0 1 0 5.75V2.104a.25.25 0 0 1 .427-.177ZM7.75 4a.75.75 0 0 1 .75.75v2.992l2.028.812a.75.75 0 0 1-.557 1.392l-2.5-1A.751.751 0 0 1 7 8.25v-3.5A.75.75 0 0 1 7.75 4Z"></path>
</svg>
</template>

<template id="repo-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo">
    <path d="M2 2.5A2.5 2.5 0 0 1 4.5 0h8.75a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h1.75v-2h-8a1 1 0 0 0-.714 1.7.75.75 0 1 1-1.072 1.05A2.495 2.495 0 0 1 2 11.5Zm10.5-1h-8a1 1 0 0 0-1 1v6.708A2.486 2.486 0 0 1 4.5 9h8ZM5 12.25a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.25a.25.25 0 0 1-.4.2l-1.45-1.087a.249.249 0 0 0-.3 0L5.4 15.7a.25.25 0 0 1-.4-.2Z"></path>
</svg>
</template>

<template id="bookmark-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-bookmark">
    <path d="M3 2.75C3 1.784 3.784 1 4.75 1h6.5c.966 0 1.75.784 1.75 1.75v11.5a.75.75 0 0 1-1.227.579L8 11.722l-3.773 3.107A.751.751 0 0 1 3 14.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.91l3.023-2.489a.75.75 0 0 1 .954 0l3.023 2.49V2.75a.25.25 0 0 0-.25-.25Z"></path>
</svg>
</template>

<template id="plus-circle-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-plus-circle">
    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Zm7.25-3.25v2.5h2.5a.75.75 0 0 1 0 1.5h-2.5v2.5a.75.75 0 0 1-1.5 0v-2.5h-2.5a.75.75 0 0 1 0-1.5h2.5v-2.5a.75.75 0 0 1 1.5 0Z"></path>
</svg>
</template>

<template id="circle-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-dot-fill">
    <path d="M8 4a4 4 0 1 1 0 8 4 4 0 0 1 0-8Z"></path>
</svg>
</template>

<template id="trash-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-trash">
    <path d="M11 1.75V3h2.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75ZM4.496 6.675l.66 6.6a.25.25 0 0 0 .249.225h5.19a.25.25 0 0 0 .249-.225l.66-6.6a.75.75 0 0 1 1.492.149l-.66 6.6A1.748 1.748 0 0 1 10.595 15h-5.19a1.75 1.75 0 0 1-1.741-1.575l-.66-6.6a.75.75 0 1 1 1.492-.15ZM6.5 1.75V3h3V1.75a.25.25 0 0 0-.25-.25h-2.5a.25.25 0 0 0-.25.25Z"></path>
</svg>
</template>

<template id="team-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-people">
    <path d="M2 5.5a3.5 3.5 0 1 1 5.898 2.549 5.508 5.508 0 0 1 3.034 4.084.75.75 0 1 1-1.482.235 4 4 0 0 0-7.9 0 .75.75 0 0 1-1.482-.236A5.507 5.507 0 0 1 3.102 8.05 3.493 3.493 0 0 1 2 5.5ZM11 4a3.001 3.001 0 0 1 2.22 5.018 5.01 5.01 0 0 1 2.56 3.012.749.749 0 0 1-.885.954.752.752 0 0 1-.549-.514 3.507 3.507 0 0 0-2.522-2.372.75.75 0 0 1-.574-.73v-.352a.75.75 0 0 1 .416-.672A1.5 1.5 0 0 0 11 5.5.75.75 0 0 1 11 4Zm-5.5-.5a2 2 0 1 0-.001 3.999A2 2 0 0 0 5.5 3.5Z"></path>
</svg>
</template>

<template id="project-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-project">
    <path d="M1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25V1.75C0 .784.784 0 1.75 0ZM1.5 1.75v12.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25ZM11.75 3a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-1.5 0v-7.5a.75.75 0 0 1 .75-.75Zm-8.25.75a.75.75 0 0 1 1.5 0v5.5a.75.75 0 0 1-1.5 0ZM8 3a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 3Z"></path>
</svg>
</template>

<template id="pencil-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-pencil">
    <path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 0 1-.927-.928l.929-3.25c.081-.286.235-.547.445-.758l8.61-8.61Zm.176 4.823L9.75 4.81l-6.286 6.287a.253.253 0 0 0-.064.108l-.558 1.953 1.953-.558a.253.253 0 0 0 .108-.064Zm1.238-3.763a.25.25 0 0 0-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 0 0 0-.354Z"></path>
</svg>
</template>

<template id="copilot-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copilot">
    <path d="M7.998 15.035c-4.562 0-7.873-2.914-7.998-3.749V9.338c.085-.628.677-1.686 1.588-2.065.013-.07.024-.143.036-.218.029-.183.06-.384.126-.612-.201-.508-.254-1.084-.254-1.656 0-.87.128-1.769.693-2.484.579-.733 1.494-1.124 2.724-1.261 1.206-.134 2.262.034 2.944.765.05.053.096.108.139.165.044-.057.094-.112.143-.165.682-.731 1.738-.899 2.944-.765 1.23.137 2.145.528 2.724 1.261.566.715.693 1.614.693 2.484 0 .572-.053 1.148-.254 1.656.066.228.098.429.126.612.012.076.024.148.037.218.924.385 1.522 1.471 1.591 2.095v1.872c0 .766-3.351 3.795-8.002 3.795Zm0-1.485c2.28 0 4.584-1.11 5.002-1.433V7.862l-.023-.116c-.49.21-1.075.291-1.727.291-1.146 0-2.059-.327-2.71-.991A3.222 3.222 0 0 1 8 6.303a3.24 3.24 0 0 1-.544.743c-.65.664-1.563.991-2.71.991-.652 0-1.236-.081-1.727-.291l-.023.116v4.255c.419.323 2.722 1.433 5.002 1.433ZM6.762 2.83c-.193-.206-.637-.413-1.682-.297-1.019.113-1.479.404-1.713.7-.247.312-.369.789-.369 1.554 0 .793.129 1.171.308 1.371.162.181.519.379 1.442.379.853 0 1.339-.235 1.638-.54.315-.322.527-.827.617-1.553.117-.935-.037-1.395-.241-1.614Zm4.155-.297c-1.044-.116-1.488.091-1.681.297-.204.219-.359.679-.242 1.614.091.726.303 1.231.618 1.553.299.305.784.54 1.638.54.922 0 1.28-.198 1.442-.379.179-.2.308-.578.308-1.371 0-.765-.123-1.242-.37-1.554-.233-.296-.693-.587-1.713-.7Z"></path><path d="M6.25 9.037a.75.75 0 0 1 .75.75v1.501a.75.75 0 0 1-1.5 0V9.787a.75.75 0 0 1 .75-.75Zm4.25.75v1.501a.75.75 0 0 1-1.5 0V9.787a.75.75 0 0 1 1.5 0Z"></path>
</svg>
</template>

<template id="copilot-error-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copilot-error">
    <path d="M16 11.24c0 .112-.072.274-.21.467L13 9.688V7.862l-.023-.116c-.49.21-1.075.291-1.727.291-.198 0-.388-.009-.571-.029L6.833 5.226a4.01 4.01 0 0 0 .17-.782c.117-.935-.037-1.395-.241-1.614-.193-.206-.637-.413-1.682-.297-.683.076-1.115.231-1.395.415l-1.257-.91c.579-.564 1.413-.877 2.485-.996 1.206-.134 2.262.034 2.944.765.05.053.096.108.139.165.044-.057.094-.112.143-.165.682-.731 1.738-.899 2.944-.765 1.23.137 2.145.528 2.724 1.261.566.715.693 1.614.693 2.484 0 .572-.053 1.148-.254 1.656.066.228.098.429.126.612.012.076.024.148.037.218.924.385 1.522 1.471 1.591 2.095Zm-5.083-8.707c-1.044-.116-1.488.091-1.681.297-.204.219-.359.679-.242 1.614.091.726.303 1.231.618 1.553.299.305.784.54 1.638.54.922 0 1.28-.198 1.442-.379.179-.2.308-.578.308-1.371 0-.765-.123-1.242-.37-1.554-.233-.296-.693-.587-1.713-.7Zm2.511 11.074c-1.393.776-3.272 1.428-5.43 1.428-4.562 0-7.873-2.914-7.998-3.749V9.338c.085-.628.677-1.686 1.588-2.065.013-.07.024-.143.036-.218.029-.183.06-.384.126-.612-.18-.455-.241-.963-.252-1.475L.31 4.107A.747.747 0 0 1 0 3.509V3.49a.748.748 0 0 1 .625-.73c.156-.026.306.047.435.139l14.667 10.578a.592.592 0 0 1 .227.264.752.752 0 0 1 .046.249v.022a.75.75 0 0 1-1.19.596Zm-1.367-.991L5.635 7.964a5.128 5.128 0 0 1-.889.073c-.652 0-1.236-.081-1.727-.291l-.023.116v4.255c.419.323 2.722 1.433 5.002 1.433 1.539 0 3.089-.505 4.063-.934Z"></path>
</svg>
</template>

<template id="workflow-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-workflow">
    <path d="M0 1.75C0 .784.784 0 1.75 0h3.5C6.216 0 7 .784 7 1.75v3.5A1.75 1.75 0 0 1 5.25 7H4v4a1 1 0 0 0 1 1h4v-1.25C9 9.784 9.784 9 10.75 9h3.5c.966 0 1.75.784 1.75 1.75v3.5A1.75 1.75 0 0 1 14.25 16h-3.5A1.75 1.75 0 0 1 9 14.25v-.75H5A2.5 2.5 0 0 1 2.5 11V7h-.75A1.75 1.75 0 0 1 0 5.25Zm1.75-.25a.25.25 0 0 0-.25.25v3.5c0 .*************.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Zm9 9a.25.25 0 0 0-.25.25v3.5c0 .*************.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25Z"></path>
</svg>
</template>

<template id="book-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-book">
    <path d="M0 1.75A.75.75 0 0 1 .75 1h4.253c1.227 0 2.317.59 3 1.501A3.743 3.743 0 0 1 11.006 1h4.245a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75h-4.507a2.25 2.25 0 0 0-1.591.659l-.622.621a.75.75 0 0 1-1.06 0l-.622-.621A2.25 2.25 0 0 0 5.258 13H.75a.75.75 0 0 1-.75-.75Zm7.251 10.324.004-5.073-.002-2.253A2.25 2.25 0 0 0 5.003 2.5H1.5v9h3.757a3.75 3.75 0 0 1 1.994.574ZM8.755 4.75l-.004 7.322a3.752 3.752 0 0 1 1.992-.572H14.5v-9h-3.495a2.25 2.25 0 0 0-2.25 2.25Z"></path>
</svg>
</template>

<template id="code-review-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code-review">
    <path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 13H8.061l-2.574 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25v-8.5C0 1.784.784 1 1.75 1ZM1.5 2.75v8.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Zm5.28 1.72a.75.75 0 0 1 0 1.06L5.31 7l1.47 1.47a.751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018l-2-2a.75.75 0 0 1 0-1.06l2-2a.75.75 0 0 1 1.06 0Zm2.44 0a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L10.69 7 9.22 5.53a.75.75 0 0 1 0-1.06Z"></path>
</svg>
</template>

<template id="codespaces-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-codespaces">
    <path d="M0 11.25c0-.966.784-1.75 1.75-1.75h12.5c.966 0 1.75.784 1.75 1.75v3A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25Zm2-9.5C2 .784 2.784 0 3.75 0h8.5C13.216 0 14 .784 14 1.75v5a1.75 1.75 0 0 1-1.75 1.75h-8.5A1.75 1.75 0 0 1 2 6.75Zm1.75-.25a.25.25 0 0 0-.25.25v5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25v-5a.25.25 0 0 0-.25-.25Zm-2 9.5a.25.25 0 0 0-.25.25v3c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-3a.25.25 0 0 0-.25-.25Z"></path><path d="M7 12.75a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Zm-4 0a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1-.75-.75Z"></path>
</svg>
</template>

<template id="comment-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-comment">
    <path d="M1 2.75C1 1.784 1.784 1 2.75 1h10.5c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 13.25 12H9.06l-2.573 2.573A1.458 1.458 0 0 1 4 13.543V12H2.75A1.75 1.75 0 0 1 1 10.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h4.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
</svg>
</template>

<template id="comment-discussion-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-comment-discussion">
    <path d="M1.75 1h8.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 10.25 10H7.061l-2.574 2.573A1.458 1.458 0 0 1 2 11.543V10h-.25A1.75 1.75 0 0 1 0 8.25v-5.5C0 1.784.784 1 1.75 1ZM1.5 2.75v5.5c0 .*************.25h1a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h3.5a.25.25 0 0 0 .25-.25v-5.5a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25Zm13 2a.25.25 0 0 0-.25-.25h-.5a.75.75 0 0 1 0-1.5h.5c.966 0 1.75.784 1.75 1.75v5.5A1.75 1.75 0 0 1 14.25 12H14v1.543a1.458 1.458 0 0 1-2.487 1.03L9.22 12.28a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215l2.22 2.22v-2.19a.75.75 0 0 1 .75-.75h1a.25.25 0 0 0 .25-.25Z"></path>
</svg>
</template>

<template id="organization-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-organization">
    <path d="M1.75 16A1.75 1.75 0 0 1 0 14.25V1.75C0 .784.784 0 1.75 0h8.5C11.216 0 12 .784 12 1.75v12.5c0 .085-.006.168-.018.25h2.268a.25.25 0 0 0 .25-.25V8.285a.25.25 0 0 0-.111-.208l-1.055-.703a.749.749 0 1 1 .832-1.248l1.055.703c.487.325.779.871.779 1.456v5.965A1.75 1.75 0 0 1 14.25 16h-3.5a.766.766 0 0 1-.197-.026c-.099.017-.2.026-.303.026h-3a.75.75 0 0 1-.75-.75V14h-1v1.25a.75.75 0 0 1-.75.75Zm-.25-1.75c0 .*************.25H4v-1.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 .75.75v1.25h2.25a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25ZM3.75 6h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5ZM3 3.75A.75.75 0 0 1 3.75 3h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 3.75Zm4 3A.75.75 0 0 1 7.75 6h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 7 6.75ZM7.75 3h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5ZM3 9.75A.75.75 0 0 1 3.75 9h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 9.75ZM7.75 9h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5Z"></path>
</svg>
</template>

<template id="rocket-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-rocket">
    <path d="M14.064 0h.186C15.216 0 16 .784 16 1.75v.186a8.752 8.752 0 0 1-2.564 6.186l-.458.459c-.314.314-.641.616-.979.904v3.207c0 .608-.315 1.172-.833 1.49l-2.774 1.707a.749.749 0 0 1-1.11-.418l-.954-3.102a1.214 1.214 0 0 1-.145-.125L3.754 9.816a1.218 1.218 0 0 1-.124-.145L.528 8.717a.749.749 0 0 1-.418-1.11l1.71-2.774A1.748 1.748 0 0 1 3.31 4h3.204c.288-.338.59-.665.904-.979l.459-.458A8.749 8.749 0 0 1 14.064 0ZM8.938 3.623h-.002l-.458.458c-.76.76-1.437 1.598-2.02 2.5l-1.5 2.317 2.143 2.143 2.317-1.5c.902-.583 1.74-1.26 2.499-2.02l.459-.458a7.25 7.25 0 0 0 2.123-5.127V1.75a.25.25 0 0 0-.25-.25h-.186a7.249 7.249 0 0 0-5.125 2.123ZM3.56 14.56c-.732.732-2.334 1.045-3.005 1.148a.234.234 0 0 1-.201-.064.234.234 0 0 1-.064-.201c.103-.671.416-2.273 1.15-3.003a1.502 1.502 0 1 1 2.12 2.12Zm6.94-3.935c-.088.06-.177.118-.266.175l-2.35 1.521.548 1.783 1.949-1.2a.25.25 0 0 0 .119-.213ZM3.678 8.116 5.2 5.766c.058-.09.117-.178.176-.266H3.309a.25.25 0 0 0-.213.119l-1.2 1.95ZM12 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
</svg>
</template>

<template id="shield-check-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-shield-check">
    <path d="m8.533.133 5.25 1.68A1.75 1.75 0 0 1 15 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.697 1.697 0 0 1-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 0 1 1.217-1.667l5.25-1.68a1.748 1.748 0 0 1 1.066 0Zm-.61 1.429.001.001-5.25 1.68a.251.251 0 0 0-.174.237V7c0 1.36.275 2.666 1.057 3.859.784 1.194 2.121 2.342 4.366 3.298a.196.196 0 0 0 .154 0c2.245-.957 3.582-2.103 4.366-3.297C13.225 9.666 13.5 8.358 13.5 7V3.48a.25.25 0 0 0-.174-.238l-5.25-1.68a.25.25 0 0 0-.153 0ZM11.28 6.28l-3.5 3.5a.75.75 0 0 1-1.06 0l-1.5-1.5a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215l.97.97 2.97-2.97a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path>
</svg>
</template>

<template id="heart-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-heart">
    <path d="m8 14.25.345.666a.75.75 0 0 1-.69 0l-.008-.004-.018-.01a7.152 7.152 0 0 1-.31-.17 22.055 22.055 0 0 1-3.434-2.414C2.045 10.731 0 8.35 0 5.5 0 2.836 2.086 1 4.25 1 5.797 1 7.153 1.802 8 3.02 8.847 1.802 10.203 1 11.75 1 13.914 1 16 2.836 16 5.5c0 2.85-2.045 5.231-3.885 6.818a22.066 22.066 0 0 1-3.744 2.584l-.018.01-.006.003h-.002ZM4.25 2.5c-1.336 0-2.75 1.164-2.75 3 0 2.15 1.58 4.144 3.365 5.682A20.58 20.58 0 0 0 8 13.393a20.58 20.58 0 0 0 3.135-2.211C12.92 9.644 14.5 7.65 14.5 5.5c0-1.836-1.414-3-2.75-3-1.373 0-2.609.986-3.029 2.456a.749.749 0 0 1-1.442 0C6.859 3.486 5.623 2.5 4.25 2.5Z"></path>
</svg>
</template>

<template id="server-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-server">
    <path d="M1.75 1h12.5c.966 0 1.75.784 1.75 1.75v4c0 .372-.116.717-.314 1 .198.283.314.628.314 1v4a1.75 1.75 0 0 1-1.75 1.75H1.75A1.75 1.75 0 0 1 0 12.75v-4c0-.358.109-.707.314-1a1.739 1.739 0 0 1-.314-1v-4C0 1.784.784 1 1.75 1ZM1.5 2.75v4c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25Zm.25 5.75a.25.25 0 0 0-.25.25v4c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-4a.25.25 0 0 0-.25-.25ZM7 4.75A.75.75 0 0 1 7.75 4h4.5a.75.75 0 0 1 0 1.5h-4.5A.75.75 0 0 1 7 4.75ZM7.75 10h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM3 4.75A.75.75 0 0 1 3.75 4h.5a.75.75 0 0 1 0 1.5h-.5A.75.75 0 0 1 3 4.75ZM3.75 10h.5a.75.75 0 0 1 0 1.5h-.5a.75.75 0 0 1 0-1.5Z"></path>
</svg>
</template>

<template id="globe-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-globe">
    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM5.78 8.75a9.64 9.64 0 0 0 1.363 4.177c.255.426.542.832.857 1.215.245-.296.551-.705.857-1.215A9.64 9.64 0 0 0 10.22 8.75Zm4.44-1.5a9.64 9.64 0 0 0-1.363-4.177c-.307-.51-.612-.919-.857-1.215a9.927 9.927 0 0 0-.857 1.215A9.64 9.64 0 0 0 5.78 7.25Zm-5.944 1.5H1.543a6.507 6.507 0 0 0 4.666 5.5c-.123-.181-.24-.365-.352-.552-.715-1.192-1.437-2.874-1.581-4.948Zm-2.733-1.5h2.733c.144-2.074.866-3.756 1.58-4.948.12-.197.237-.381.353-.552a6.507 6.507 0 0 0-4.666 5.5Zm10.181 1.5c-.144 2.074-.866 3.756-1.58 4.948-.12.197-.237.381-.353.552a6.507 6.507 0 0 0 4.666-5.5Zm2.733-1.5a6.507 6.507 0 0 0-4.666-5.5c.123.181.24.365.353.552.714 1.192 1.436 2.874 1.58 4.948Z"></path>
</svg>
</template>

<template id="issue-opened-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-issue-opened">
    <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"></path><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"></path>
</svg>
</template>

<template id="device-mobile-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-device-mobile">
    <path d="M3.75 0h8.5C13.216 0 14 .784 14 1.75v12.5A1.75 1.75 0 0 1 12.25 16h-8.5A1.75 1.75 0 0 1 2 14.25V1.75C2 .784 2.784 0 3.75 0ZM3.5 1.75v12.5c0 .*************.25h8.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25h-8.5a.25.25 0 0 0-.25.25ZM8 13a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z"></path>
</svg>
</template>

<template id="package-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-package">
    <path d="m8.878.392 5.25 3.045c.54.314.872.89.872 1.514v6.098a1.75 1.75 0 0 1-.872 1.514l-5.25 3.045a1.75 1.75 0 0 1-1.756 0l-5.25-3.045A1.75 1.75 0 0 1 1 11.049V4.951c0-.624.332-1.201.872-1.514L7.122.392a1.75 1.75 0 0 1 1.756 0ZM7.875 1.69l-4.63 2.685L8 7.133l4.755-2.758-4.63-2.685a.248.248 0 0 0-.25 0ZM2.5 5.677v5.372c0 .09.047.171.125.216l4.625 2.683V8.432Zm6.25 8.271 4.625-2.683a.25.25 0 0 0 .125-.216V5.677L8.75 8.432Z"></path>
</svg>
</template>

<template id="credit-card-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-credit-card">
    <path d="M10.75 9a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5h-1.5Z"></path><path d="M0 3.75C0 2.784.784 2 1.75 2h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 14H1.75A1.75 1.75 0 0 1 0 12.25ZM14.5 6.5h-13v5.75c0 .*************.25h12.5a.25.25 0 0 0 .25-.25Zm0-2.75a.25.25 0 0 0-.25-.25H1.75a.25.25 0 0 0-.25.25V5h13Z"></path>
</svg>
</template>

<template id="play-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-play">
    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Zm4.879-2.773 4.264 2.559a.25.25 0 0 1 0 .428l-4.264 2.559A.25.25 0 0 1 6 10.559V5.442a.25.25 0 0 1 .379-.215Z"></path>
</svg>
</template>

<template id="gift-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-gift">
    <path d="M2 2.75A2.75 2.75 0 0 1 4.75 0c.983 0 1.873.42 2.57 1.232.268.318.497.668.68 1.042.183-.375.411-.725.68-1.044C9.376.42 10.266 0 11.25 0a2.75 2.75 0 0 1 2.45 4h.55c.966 0 1.75.784 1.75 1.75v2c0 .698-.409 1.301-1 1.582v4.918A1.75 1.75 0 0 1 13.25 16H2.75A1.75 1.75 0 0 1 1 14.25V9.332C.409 9.05 0 8.448 0 7.75v-2C0 4.784.784 4 1.75 4h.55c-.192-.375-.3-.8-.3-1.25ZM7.25 9.5H2.5v4.75c0 .*************.25h4.5Zm1.5 0v5h4.5a.25.25 0 0 0 .25-.25V9.5Zm0-4V8h5.5a.25.25 0 0 0 .25-.25v-2a.25.25 0 0 0-.25-.25Zm-7 0a.25.25 0 0 0-.25.25v2c0 .*************.25h5.5V5.5h-5.5Zm3-4a1.25 1.25 0 0 0 0 2.5h2.309c-.233-.818-.542-1.401-.878-1.793-.43-.502-.915-.707-1.431-.707ZM8.941 4h2.309a1.25 1.25 0 0 0 0-2.5c-.516 0-1 .205-1.43.707-.337.392-.646.975-.879 1.793Z"></path>
</svg>
</template>

<template id="code-square-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code-square">
    <path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25Zm1.75-.25a.25.25 0 0 0-.25.25v12.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25V1.75a.25.25 0 0 0-.25-.25Zm7.47 3.97a.75.75 0 0 1 1.06 0l2 2a.75.75 0 0 1 0 1.06l-2 2a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L10.69 8 9.22 6.53a.75.75 0 0 1 0-1.06ZM6.78 6.53 5.31 8l1.47 1.47a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215l-2-2a.75.75 0 0 1 0-1.06l2-2a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path>
</svg>
</template>

<template id="device-desktop-icon">
  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-device-desktop">
    <path d="M14.25 1c.966 0 1.75.784 1.75 1.75v7.5A1.75 1.75 0 0 1 14.25 12h-3.727c.099 1.041.52 1.872 1.292 2.757A.752.752 0 0 1 11.25 16h-6.5a.75.75 0 0 1-.565-1.243c.772-.885 1.192-1.716 1.292-2.757H1.75A1.75 1.75 0 0 1 0 10.25v-7.5C0 1.784.784 1 1.75 1ZM1.75 2.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h12.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25ZM9.018 12H6.982a5.72 5.72 0 0 1-.765 2.5h3.566a5.72 5.72 0 0 1-.765-2.5Z"></path>
</svg>
</template>

        <div class="position-relative">
                <ul
                  role="listbox"
                  class="ActionListWrap QueryBuilder-ListWrap"
                  aria-label="Suggestions"
                  data-action="
                    combobox-commit:query-builder#comboboxCommit
                    mousedown:query-builder#resultsMousedown
                  "
                  data-target="query-builder.resultsList"
                  data-persist-list=false
                  id="query-builder-test-results"
                ></ul>
        </div>
      <div class="FormControl-inlineValidation" id="validation-795d10d0-44fb-4891-9cd9-e1128d3941f8" hidden="hidden">
        <span class="FormControl-inlineValidation--visual">
          <svg aria-hidden="true" height="12" viewBox="0 0 12 12" version="1.1" width="12" data-view-component="true" class="octicon octicon-alert-fill">
    <path d="M4.855.708c.5-.896 1.79-.896 2.29 0l4.675 8.351a1.312 1.312 0 0 1-1.146 1.954H1.33A1.313 1.313 0 0 1 .183 9.058ZM7 7V3H5v4Zm-1 3a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"></path>
</svg>
        </span>
        <span></span>
</div>    </div>
    <div data-target="query-builder.screenReaderFeedback" aria-live="polite" aria-atomic="true" class="sr-only"></div>
</query-builder></form>
          <div class="d-flex flex-row color-fg-muted px-3 text-small color-bg-default search-feedback-prompt">
            <a target="_blank" href="https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax" data-view-component="true" class="Link color-fg-accent text-normal ml-2">Search syntax tips</a>            <div class="d-flex flex-1"></div>
          </div>
        </div>
</div>

    </div>
</modal-dialog></div>
  </div>
  <div data-action="click:qbsearch-input#retract" class="dark-backdrop position-fixed" hidden data-target="qbsearch-input.darkBackdrop"></div>
  <div class="color-fg-default">
    
<dialog-helper>
  <dialog data-target="qbsearch-input.feedbackDialog" data-action="close:qbsearch-input#handleDialogClose cancel:qbsearch-input#handleDialogClose" id="feedback-dialog" aria-modal="true" aria-labelledby="feedback-dialog-title" aria-describedby="feedback-dialog-description" data-view-component="true" class="Overlay Overlay-whenNarrow Overlay--size-medium Overlay--motion-scaleFade Overlay--disableScroll">
    <div data-view-component="true" class="Overlay-header">
  <div class="Overlay-headerContentWrap">
    <div class="Overlay-titleWrap">
      <h1 class="Overlay-title " id="feedback-dialog-title">
        Provide feedback
      </h1>
        
    </div>
    <div class="Overlay-actionWrap">
      <button data-close-dialog-id="feedback-dialog" aria-label="Close" aria-label="Close" type="button" data-view-component="true" class="close-button Overlay-closeButton"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg></button>
    </div>
  </div>
  
</div>
      <scrollable-region data-labelled-by="feedback-dialog-title">
        <div data-view-component="true" class="Overlay-body">        <!-- '"` --><!-- </textarea></xmp> --></option></form><form id="code-search-feedback-form" data-turbo="false" action="/search/feedback" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="_lgo3aEhkA2zrvYk8DzPcNy--S6CiKyf_s0KRGWtuwrrITOc4GQfQYDX3o-LHHSh9VuZimjQgem2LBh1CM8TNg" />
          <p>We read every piece of feedback, and take your input very seriously.</p>
          <textarea name="feedback" class="form-control width-full mb-2" style="height: 120px" id="feedback"></textarea>
          <input name="include_email" id="include_email" aria-label="Include my email address so I can be contacted" class="form-control mr-2" type="checkbox">
          <label for="include_email" style="font-weight: normal">Include my email address so I can be contacted</label>
</form></div>
      </scrollable-region>
      <div data-view-component="true" class="Overlay-footer Overlay-footer--alignEnd">          <button data-close-dialog-id="feedback-dialog" type="button" data-view-component="true" class="btn">    Cancel
</button>
          <button form="code-search-feedback-form" data-action="click:qbsearch-input#submitFeedback" type="submit" data-view-component="true" class="btn-primary btn">    Submit feedback
</button>
</div>
</dialog></dialog-helper>

    <custom-scopes data-target="qbsearch-input.customScopesManager">
    
<dialog-helper>
  <dialog data-target="custom-scopes.customScopesModalDialog" data-action="close:qbsearch-input#handleDialogClose cancel:qbsearch-input#handleDialogClose" id="custom-scopes-dialog" aria-modal="true" aria-labelledby="custom-scopes-dialog-title" aria-describedby="custom-scopes-dialog-description" data-view-component="true" class="Overlay Overlay-whenNarrow Overlay--size-medium Overlay--motion-scaleFade Overlay--disableScroll">
    <div data-view-component="true" class="Overlay-header Overlay-header--divided">
  <div class="Overlay-headerContentWrap">
    <div class="Overlay-titleWrap">
      <h1 class="Overlay-title " id="custom-scopes-dialog-title">
        Saved searches
      </h1>
        <h2 id="custom-scopes-dialog-description" class="Overlay-description">Use saved searches to filter your results more quickly</h2>
    </div>
    <div class="Overlay-actionWrap">
      <button data-close-dialog-id="custom-scopes-dialog" aria-label="Close" aria-label="Close" type="button" data-view-component="true" class="close-button Overlay-closeButton"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg></button>
    </div>
  </div>
  
</div>
      <scrollable-region data-labelled-by="custom-scopes-dialog-title">
        <div data-view-component="true" class="Overlay-body">        <div data-target="custom-scopes.customScopesModalDialogFlash"></div>

        <div hidden class="create-custom-scope-form" data-target="custom-scopes.createCustomScopeForm">
        <!-- '"` --><!-- </textarea></xmp> --></option></form><form id="custom-scopes-dialog-form" data-turbo="false" action="/search/custom_scopes" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="1PvH4GFJCGkQv1ynSgmRTOwLCxfj4pz8T7fK4XlYm0oUg0Pcy3J4W_TOUGqEC1h_PVv4DPHBvdyyYI4kjakakQ" />
          <div data-target="custom-scopes.customScopesModalDialogFlash"></div>

          <input type="hidden" id="custom_scope_id" name="custom_scope_id" data-target="custom-scopes.customScopesIdField">

          <div class="form-group">
            <label for="custom_scope_name">Name</label>
            <auto-check src="/search/custom_scopes/check_name" required>
              <input
                type="text"
                name="custom_scope_name"
                id="custom_scope_name"
                data-target="custom-scopes.customScopesNameField"
                class="form-control"
                autocomplete="off"
                placeholder="github-ruby"
                required
                maxlength="50">
              <input type="hidden" value="bpDBQH5hUREIIUkmhnP9r7uOwcZUQZVLKXH8vMdp0s6FVWA1G-nTA0_i0Ch-NCjevWK9yP1FfZ6dxRtHIu57Nw" data-csrf="true" />
            </auto-check>
          </div>

          <div class="form-group">
            <label for="custom_scope_query">Query</label>
            <input
              type="text"
              name="custom_scope_query"
              id="custom_scope_query"
              data-target="custom-scopes.customScopesQueryField"
              class="form-control"
              autocomplete="off"
              placeholder="(repo:mona/a OR repo:mona/b) AND lang:python"
              required
              maxlength="500">
          </div>

          <p class="text-small color-fg-muted">
            To see all available qualifiers, see our <a class="Link--inTextBlock" href="https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax">documentation</a>.
          </p>
</form>        </div>

        <div data-target="custom-scopes.manageCustomScopesForm">
          <div data-target="custom-scopes.list"></div>
        </div>

</div>
      </scrollable-region>
      <div data-view-component="true" class="Overlay-footer Overlay-footer--alignEnd Overlay-footer--divided">          <button data-action="click:custom-scopes#customScopesCancel" type="button" data-view-component="true" class="btn">    Cancel
</button>
          <button form="custom-scopes-dialog-form" data-action="click:custom-scopes#customScopesSubmit" data-target="custom-scopes.customScopesSubmitButton" type="submit" data-view-component="true" class="btn-primary btn">    Create saved search
</button>
</div>
</dialog></dialog-helper>
    </custom-scopes>
  </div>
</qbsearch-input>


            <div class="position-relative HeaderMenu-link-wrap d-lg-inline-block">
              <a
                href="/login?return_to=https%3A%2F%2Fgithub.com%2Fgoogle%2Fstyleguide%2Fblob%2Fgh-pages%2Fintellij-java-google-style.xml"
                class="HeaderMenu-link HeaderMenu-link--sign-in HeaderMenu-button flex-shrink-0 no-underline d-none d-lg-inline-flex border border-lg-0 rounded px-2 py-1"
                style="margin-left: 12px;"
                data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="45830fd05ddcb5dab1fd081a883123cd4ce59dba77ef03bbfd4deb80401a7c5d"
                data-analytics-event="{&quot;category&quot;:&quot;Marketing nav&quot;,&quot;action&quot;:&quot;click to go to homepage&quot;,&quot;label&quot;:&quot;ref_page:Marketing;ref_cta:Sign in;ref_loc:Header&quot;}"
              >
                Sign in
              </a>
            </div>

              <a href="/signup?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo&amp;source_repo=google%2Fstyleguide"
                class="HeaderMenu-link HeaderMenu-link--sign-up HeaderMenu-button flex-shrink-0 d-flex d-lg-inline-flex no-underline border color-border-default rounded px-2 py-1"
                data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="45830fd05ddcb5dab1fd081a883123cd4ce59dba77ef03bbfd4deb80401a7c5d"
                data-analytics-event="{&quot;category&quot;:&quot;Sign up&quot;,&quot;action&quot;:&quot;click to sign up for account&quot;,&quot;label&quot;:&quot;ref_page:/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show;ref_cta:Sign up;ref_loc:header logged out&quot;}"
              >
                Sign up
              </a>

                <div class="AppHeader-appearanceSettings">
    <react-partial-anchor>
      <button data-target="react-partial-anchor.anchor" id="icon-button-39ebd673-1ecc-4e80-968b-53b281379815" aria-labelledby="tooltip-e5be334a-12d0-44a8-bb84-237d6604dc46" type="button" disabled="disabled" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium AppHeader-button HeaderMenu-link border cursor-wait">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-sliders Button-visual">
    <path d="M15 2.75a.75.75 0 0 1-.75.75h-4a.75.75 0 0 1 0-1.5h4a.75.75 0 0 1 .75.75Zm-8.5.75v1.25a.75.75 0 0 0 1.5 0v-4a.75.75 0 0 0-1.5 0V2H1.75a.75.75 0 0 0 0 1.5H6.5Zm1.25 5.25a.75.75 0 0 0 0-1.5h-6a.75.75 0 0 0 0 1.5h6ZM15 8a.75.75 0 0 1-.75.75H11.5V10a.75.75 0 1 1-1.5 0V6a.75.75 0 0 1 1.5 0v1.25h2.75A.75.75 0 0 1 15 8Zm-9 5.25v-2a.75.75 0 0 0-1.5 0v1.25H1.75a.75.75 0 0 0 0 1.5H4.5v1.25a.75.75 0 0 0 1.5 0v-2Zm9 0a.75.75 0 0 1-.75.75h-6a.75.75 0 0 1 0-1.5h6a.75.75 0 0 1 .75.75Z"></path>
</svg>
</button><tool-tip id="tooltip-e5be334a-12d0-44a8-bb84-237d6604dc46" for="icon-button-39ebd673-1ecc-4e80-968b-53b281379815" popover="manual" data-direction="s" data-type="label" data-view-component="true" class="sr-only position-absolute">Appearance settings</tool-tip>

      <template data-target="react-partial-anchor.template">
        <link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/primer-react.8d5e42bdd3cd6a27871d.module.css" />
<link crossorigin="anonymous" media="all" rel="stylesheet" href="https://github.githubassets.com/assets/appearance-settings.4e1ca273f504ba849f8c.module.css" />

<react-partial
  partial-name="appearance-settings"
  data-ssr="false"
  data-attempted-ssr="false"
  data-react-profiling="false"
>
  
  <script type="application/json" data-target="react-partial.embeddedData">{"props":{}}</script>
  <div data-target="react-partial.reactRoot"></div>
</react-partial>

      </template>
    </react-partial-anchor>
  </div>

          <button type="button" class="sr-only js-header-menu-focus-trap d-block d-lg-none">Resetting focus</button>
        </div>
      </div>
    </div>
  </div>
</header>

      <div hidden="hidden" data-view-component="true" class="js-stale-session-flash stale-session-flash flash flash-warn flash-full">
  
        <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-alert">
    <path d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
</svg>
        <span class="js-stale-session-flash-signed-in" hidden>You signed in with another tab or window. <a class="Link--inTextBlock" href="">Reload</a> to refresh your session.</span>
        <span class="js-stale-session-flash-signed-out" hidden>You signed out in another tab or window. <a class="Link--inTextBlock" href="">Reload</a> to refresh your session.</span>
        <span class="js-stale-session-flash-switched" hidden>You switched accounts on another tab or window. <a class="Link--inTextBlock" href="">Reload</a> to refresh your session.</span>

    <button id="icon-button-587a7eb9-a60a-48ae-b6e4-132b2438184d" aria-labelledby="tooltip-ff51ae3b-628b-4a73-b37f-84b8148283ee" type="button" data-view-component="true" class="Button Button--iconOnly Button--invisible Button--medium flash-close js-flash-close">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x Button-visual">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg>
</button><tool-tip id="tooltip-ff51ae3b-628b-4a73-b37f-84b8148283ee" for="icon-button-587a7eb9-a60a-48ae-b6e4-132b2438184d" popover="manual" data-direction="s" data-type="label" data-view-component="true" class="sr-only position-absolute">Dismiss alert</tool-tip>


  
</div>
    </div>

  <div id="start-of-content" class="show-on-focus"></div>








    <div id="js-flash-container" class="flash-container" data-turbo-replace>




  <template class="js-flash-template">
    
<div class="flash flash-full   {{ className }}">
  <div >
    <button autofocus class="flash-close js-flash-close" type="button" aria-label="Dismiss this message">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg>
    </button>
    <div aria-atomic="true" role="alert" class="js-flash-alert">
      
      <div>{{ message }}</div>

    </div>
  </div>
</div>
  </template>
</div>


    






  <div
    class="application-main "
    data-commit-hovercards-enabled
    data-discussion-hovercards-enabled
    data-issue-and-pr-hovercards-enabled
    data-project-hovercards-enabled
  >
        <div itemscope itemtype="http://schema.org/SoftwareSourceCode" class="">
    <main id="js-repo-pjax-container" >
      
      
    

    






  
  <div id="repository-container-header"  class="pt-3 hide-full-screen" style="background-color: var(--page-header-bgColor, var(--color-page-header-bg));" data-turbo-replace>

      <div class="d-flex flex-nowrap flex-justify-end mb-3  px-3 px-lg-5" style="gap: 1rem;">

        <div class="flex-auto min-width-0 width-fit">
            
  <div class=" d-flex flex-wrap flex-items-center wb-break-word f3 text-normal">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo color-fg-muted mr-2">
    <path d="M2 2.5A2.5 2.5 0 0 1 4.5 0h8.75a.75.75 0 0 1 .75.75v12.5a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5h1.75v-2h-8a1 1 0 0 0-.714 1.7.75.75 0 1 1-1.072 1.05A2.495 2.495 0 0 1 2 11.5Zm10.5-1h-8a1 1 0 0 0-1 1v6.708A2.486 2.486 0 0 1 4.5 9h8ZM5 12.25a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.25a.25.25 0 0 1-.4.2l-1.45-1.087a.249.249 0 0 0-.3 0L5.4 15.7a.25.25 0 0 1-.4-.2Z"></path>
</svg>
    
    <span class="author flex-self-stretch" itemprop="author">
      <a class="url fn" rel="author" data-hovercard-type="organization" data-hovercard-url="/orgs/google/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/google">
        google
</a>    </span>
    <span class="mx-1 flex-self-stretch color-fg-muted">/</span>
    <strong itemprop="name" class="mr-2 flex-self-stretch">
      <a data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" href="/google/styleguide">styleguide</a>
    </strong>

    <span></span><span class="Label Label--secondary v-align-middle mr-1">Public</span>
  </div>


        </div>

        <div id="repository-details-container" class="flex-shrink-0" data-turbo-replace style="max-width: 70%;">
            <ul class="pagehead-actions flex-shrink-0 d-none d-md-inline" style="padding: 2px 0;">
    
      

  <li>
            <a href="/login?return_to=%2Fgoogle%2Fstyleguide" rel="nofollow" id="repository-details-watch-button" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;notification subscription menu watch&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="eabd7cb41d5794122c5575122228eaefe9d2e2f01585168bc7307483c207f635" aria-label="You must be signed in to change notification settings" data-view-component="true" class="btn-sm btn">    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-bell mr-2">
    <path d="M8 16a2 2 0 0 0 1.985-1.75c.017-.137-.097-.25-.235-.25h-3.5c-.138 0-.252.113-.235.25A2 2 0 0 0 8 16ZM3 5a5 5 0 0 1 10 0v2.947c0 .**************.139l1.703 2.555A1.519 1.519 0 0 1 13.482 13H2.518a1.516 1.516 0 0 1-1.263-2.36l1.703-2.554A.255.255 0 0 0 3 7.947Zm5-3.5A3.5 3.5 0 0 0 4.5 5v2.947c0 .346-.102.683-.294.97l-1.703 2.556a.017.017 0 0 0-.003.01l.001.006c0 .***************.006l.006.004.007.001h10.964l.007-.001.006-.004.004-.006.001-.007a.017.017 0 0 0-.003-.01l-1.703-2.554a1.745 1.745 0 0 1-.294-.97V5A3.5 3.5 0 0 0 8 1.5Z"></path>
</svg>Notifications
</a>    <tool-tip id="tooltip-6308bb42-e9b1-49d7-be9f-01c2bb3928e3" for="repository-details-watch-button" popover="manual" data-direction="s" data-type="description" data-view-component="true" class="sr-only position-absolute">You must be signed in to change notification settings</tool-tip>

  </li>

  <li>
          <a icon="repo-forked" id="fork-button" href="/login?return_to=%2Fgoogle%2Fstyleguide" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;repo details fork button&quot;,&quot;repository_id&quot;:35969061,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="ec14c1459b99bfdec79bb438b0c6131c597392e0d9820552d86673966832561f" data-view-component="true" class="btn-sm btn">    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo-forked mr-2">
    <path d="M5 5.372v.878c0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75v-.878a2.25 2.25 0 1 1 1.5 0v.878a2.25 2.25 0 0 1-2.25 2.25h-1.5v2.128a2.251 2.251 0 1 1-1.5 0V8.5h-1.5A2.25 2.25 0 0 1 3.5 6.25v-.878a2.25 2.25 0 1 1 1.5 0ZM5 3.25a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Zm6.75.75a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm-3 8.75a.75.75 0 1 0-1.5 0 .75.75 0 0 0 1.5 0Z"></path>
</svg>Fork
    <span id="repo-network-counter" data-pjax-replace="true" data-turbo-replace="true" title="13,010" data-view-component="true" class="Counter">13k</span>
</a>
  </li>

  <li>
        <div data-view-component="true" class="BtnGroup d-flex">
        <a href="/login?return_to=%2Fgoogle%2Fstyleguide" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;star button&quot;,&quot;repository_id&quot;:35969061,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="436a8cb4e5b33425e501914b2425423e3deee84c4ae8dbbcbe88eb3d1a9814fc" aria-label="You must be signed in to star a repository" data-view-component="true" class="tooltipped tooltipped-sw btn-sm btn">    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-star v-align-text-bottom d-inline-block mr-2">
    <path d="M8 .25a.75.75 0 0 1 .673.418l1.882 3.815 4.21.612a.75.75 0 0 1 .416 1.279l-3.046 2.97.719 4.192a.751.751 0 0 1-1.088.791L8 12.347l-3.766 1.98a.75.75 0 0 1-1.088-.79l.72-4.194L.818 6.374a.75.75 0 0 1 .416-1.28l4.21-.611L7.327.668A.75.75 0 0 1 8 .25Zm0 2.445L6.615 5.5a.75.75 0 0 1-.564.41l-3.097.45 2.24 2.184a.75.75 0 0 1 .216.664l-.528 3.084 2.769-1.456a.75.75 0 0 1 .698 0l2.77 1.456-.53-3.084a.75.75 0 0 1 .216-.664l2.24-2.183-3.096-.45a.75.75 0 0 1-.564-.41L8 2.694Z"></path>
</svg><span data-view-component="true" class="d-inline">
          Star
</span>          <span id="repo-stars-counter-star" aria-label="38333 users starred this repository" data-singular-suffix="user starred this repository" data-plural-suffix="users starred this repository" data-turbo-replace="true" title="38,333" data-view-component="true" class="Counter js-social-count">38.3k</span>
</a></div>
  </li>

</ul>

        </div>
      </div>

        <div id="responsive-meta-container" data-turbo-replace>
</div>


          <nav data-pjax="#js-repo-pjax-container" aria-label="Repository" data-view-component="true" class="js-repo-nav js-sidenav-container-pjax js-responsive-underlinenav overflow-hidden UnderlineNav px-3 px-md-4 px-lg-5">

  <ul data-view-component="true" class="UnderlineNav-body list-style-none">
      <li data-view-component="true" class="d-inline-flex">
  <a id="code-tab" href="/google/styleguide" data-tab-item="i0code-tab" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages repo_deployments repo_attestations /google/styleguide" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-hotkey="g c" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Code&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" aria-current="page" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item selected">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code UnderlineNav-octicon d-none d-sm-inline">
    <path d="m11.28 3.22 4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L13.94 8l-3.72-3.72a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215Zm-6.56 0a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042L2.06 8l3.72 3.72a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L.47 8.53a.75.75 0 0 1 0-1.06Z"></path>
</svg>
        <span data-content="Code">Code</span>
          <span id="code-repo-tab-count" data-pjax-replace="" data-turbo-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="issues-tab" href="/google/styleguide/issues" data-tab-item="i1issues-tab" data-selected-links="repo_issues repo_labels repo_milestones /google/styleguide/issues" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-hotkey="g i" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Issues&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-issue-opened UnderlineNav-octicon d-none d-sm-inline">
    <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"></path><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"></path>
</svg>
        <span data-content="Issues">Issues</span>
          <span id="issues-repo-tab-count" data-pjax-replace="" data-turbo-replace="" title="114" data-view-component="true" class="Counter">114</span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="pull-requests-tab" href="/google/styleguide/pulls" data-tab-item="i2pull-requests-tab" data-selected-links="repo_pulls checks /google/styleguide/pulls" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-hotkey="g p" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Pull requests&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-git-pull-request UnderlineNav-octicon d-none d-sm-inline">
    <path d="M1.5 3.25a2.25 2.25 0 1 1 3 2.122v5.256a2.251 2.251 0 1 1-1.5 0V5.372A2.25 2.25 0 0 1 1.5 3.25Zm5.677-.177L9.573.677A.25.25 0 0 1 10 .854V2.5h1A2.5 2.5 0 0 1 13.5 5v5.628a2.251 2.251 0 1 1-1.5 0V5a1 1 0 0 0-1-1h-1v1.646a.25.25 0 0 1-.427.177L7.177 3.427a.25.25 0 0 1 0-.354ZM3.75 2.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm0 9.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm8.25.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Z"></path>
</svg>
        <span data-content="Pull requests">Pull requests</span>
          <span id="pull-requests-repo-tab-count" data-pjax-replace="" data-turbo-replace="" title="50" data-view-component="true" class="Counter">50</span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="actions-tab" href="/google/styleguide/actions" data-tab-item="i3actions-tab" data-selected-links="repo_actions /google/styleguide/actions" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-hotkey="g a" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Actions&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-play UnderlineNav-octicon d-none d-sm-inline">
    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Zm4.879-2.773 4.264 2.559a.25.25 0 0 1 0 .428l-4.264 2.559A.25.25 0 0 1 6 10.559V5.442a.25.25 0 0 1 .379-.215Z"></path>
</svg>
        <span data-content="Actions">Actions</span>
          <span id="actions-repo-tab-count" data-pjax-replace="" data-turbo-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="security-tab" href="/google/styleguide/security" data-tab-item="i4security-tab" data-selected-links="security overview alerts policy token_scanning code_scanning /google/styleguide/security" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-hotkey="g s" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Security&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-shield UnderlineNav-octicon d-none d-sm-inline">
    <path d="M7.467.133a1.748 1.748 0 0 1 1.066 0l5.25 1.68A1.75 1.75 0 0 1 15 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.697 1.697 0 0 1-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 0 1 1.217-1.667Zm.61 1.429a.25.25 0 0 0-.153 0l-5.25 1.68a.25.25 0 0 0-.174.238V7c0 1.358.275 2.666 1.057 3.86.784 1.194 2.121 2.34 4.366 3.297a.196.196 0 0 0 .154 0c2.245-.956 3.582-2.104 4.366-3.298C13.225 9.666 13.5 8.36 13.5 7V3.48a.251.251 0 0 0-.174-.237l-5.25-1.68ZM8.75 4.75v3a.75.75 0 0 1-1.5 0v-3a.75.75 0 0 1 1.5 0ZM9 10.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
</svg>
        <span data-content="Security">Security</span>
          <include-fragment src="/google/styleguide/security/overall-count" accept="text/fragment+html" data-nonce="v2:291e3198-6115-77ce-fbbf-2433c873b530" data-view-component="true">
  
  <div data-show-on-forbidden-error hidden>
    <div class="Box">
  <div class="blankslate-container">
    <div data-view-component="true" class="blankslate blankslate-spacious color-bg-default rounded-2">
      

      <h3 data-view-component="true" class="blankslate-heading">        Uh oh!
</h3>
      <p data-view-component="true">        <p class="color-fg-muted my-2 mb-2 ws-normal">There was an error while loading. <a class="Link--inTextBlock" data-turbo="false" href="" aria-label="Please reload this page">Please reload this page</a>.</p>
</p>

</div>  </div>
</div>  </div>
</include-fragment>

    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="insights-tab" href="/google/styleguide/pulse" data-tab-item="i5insights-tab" data-selected-links="repo_graphs repo_contributors dependency_graph dependabot_updates pulse people community /google/styleguide/pulse" data-pjax="#repo-content-pjax-container" data-turbo-frame="repo-content-turbo-frame" data-analytics-event="{&quot;category&quot;:&quot;Underline navbar&quot;,&quot;action&quot;:&quot;Click tab&quot;,&quot;label&quot;:&quot;Insights&quot;,&quot;target&quot;:&quot;UNDERLINE_NAV.TAB&quot;}" data-view-component="true" class="UnderlineNav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-graph UnderlineNav-octicon d-none d-sm-inline">
    <path d="M1.5 1.75V13.5h13.75a.75.75 0 0 1 0 1.5H.75a.75.75 0 0 1-.75-.75V1.75a.75.75 0 0 1 1.5 0Zm14.28 2.53-5.25 5.25a.75.75 0 0 1-1.06 0L7 7.06 4.28 9.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.25-3.25a.75.75 0 0 1 1.06 0L10 7.94l4.72-4.72a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path>
</svg>
        <span data-content="Insights">Insights</span>
          <span id="insights-repo-tab-count" data-pjax-replace="" data-turbo-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
</ul>
    <div style="visibility:hidden;" data-view-component="true" class="UnderlineNav-actions js-responsive-underlinenav-overflow position-absolute pr-3 pr-md-4 pr-lg-5 right-0">      <action-menu data-select-variant="none" data-view-component="true">
  <focus-group direction="vertical" mnemonics retain>
    <button id="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-button" popovertarget="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-overlay" aria-controls="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-list" aria-haspopup="true" aria-labelledby="tooltip-8d646eac-1fef-48cb-b933-d77d16c958b6" type="button" data-view-component="true" class="Button Button--iconOnly Button--secondary Button--medium UnderlineNav-item">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-kebab-horizontal Button-visual">
    <path d="M8 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3ZM1.5 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm13 0a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"></path>
</svg>
</button><tool-tip id="tooltip-8d646eac-1fef-48cb-b933-d77d16c958b6" for="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-button" popover="manual" data-direction="s" data-type="label" data-view-component="true" class="sr-only position-absolute">Additional navigation options</tool-tip>


<anchored-position data-target="action-menu.overlay" id="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-overlay" anchor="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-button" align="start" side="outside-bottom" anchor-offset="normal" popover="auto" data-view-component="true">
  <div data-view-component="true" class="Overlay Overlay--size-auto">
    
      <div data-view-component="true" class="Overlay-body Overlay-body--paddingNone">          <action-list>
  <div data-view-component="true">
    <ul aria-labelledby="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-button" id="action-menu-6a177a38-cce8-4b0e-94fe-0902d073deb3-list" role="menu" data-view-component="true" class="ActionListWrap--inset ActionListWrap">
        <li hidden="hidden" data-menu-item="i0code-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-ea117a87-4b30-426d-abd7-f3138819c52b" href="/google/styleguide" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code">
    <path d="m11.28 3.22 4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.749.749 0 0 1-1.275-.326.749.749 0 0 1 .215-.734L13.94 8l-3.72-3.72a.749.749 0 0 1 .326-1.275.749.749 0 0 1 .734.215Zm-6.56 0a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042L2.06 8l3.72 3.72a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L.47 8.53a.75.75 0 0 1 0-1.06Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Code
</span>      
</a>
  
</li>
        <li hidden="hidden" data-menu-item="i1issues-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-e49317d1-a26f-4544-86d0-f4937672a8df" href="/google/styleguide/issues" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-issue-opened">
    <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"></path><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Issues
</span>      
</a>
  
</li>
        <li hidden="hidden" data-menu-item="i2pull-requests-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-b2f2c2db-0666-4a78-8cc9-6dcf3968b3b8" href="/google/styleguide/pulls" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-git-pull-request">
    <path d="M1.5 3.25a2.25 2.25 0 1 1 3 2.122v5.256a2.251 2.251 0 1 1-1.5 0V5.372A2.25 2.25 0 0 1 1.5 3.25Zm5.677-.177L9.573.677A.25.25 0 0 1 10 .854V2.5h1A2.5 2.5 0 0 1 13.5 5v5.628a2.251 2.251 0 1 1-1.5 0V5a1 1 0 0 0-1-1h-1v1.646a.25.25 0 0 1-.427.177L7.177 3.427a.25.25 0 0 1 0-.354ZM3.75 2.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm0 9.5a.75.75 0 1 0 0 1.5.75.75 0 0 0 0-1.5Zm8.25.75a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Pull requests
</span>      
</a>
  
</li>
        <li hidden="hidden" data-menu-item="i3actions-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-9af49b54-666e-44c2-8199-9e422116bf74" href="/google/styleguide/actions" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-play">
    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Zm4.879-2.773 4.264 2.559a.25.25 0 0 1 0 .428l-4.264 2.559A.25.25 0 0 1 6 10.559V5.442a.25.25 0 0 1 .379-.215Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Actions
</span>      
</a>
  
</li>
        <li hidden="hidden" data-menu-item="i4security-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-f9c70967-b0bd-4ee3-b76d-4697069002ca" href="/google/styleguide/security" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-shield">
    <path d="M7.467.133a1.748 1.748 0 0 1 1.066 0l5.25 1.68A1.75 1.75 0 0 1 15 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.697 1.697 0 0 1-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 0 1 1.217-1.667Zm.61 1.429a.25.25 0 0 0-.153 0l-5.25 1.68a.25.25 0 0 0-.174.238V7c0 1.358.275 2.666 1.057 3.86.784 1.194 2.121 2.34 4.366 3.297a.196.196 0 0 0 .154 0c2.245-.956 3.582-2.104 4.366-3.298C13.225 9.666 13.5 8.36 13.5 7V3.48a.251.251 0 0 0-.174-.237l-5.25-1.68ZM8.75 4.75v3a.75.75 0 0 1-1.5 0v-3a.75.75 0 0 1 1.5 0ZM9 10.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Security
</span>      
</a>
  
</li>
        <li hidden="hidden" data-menu-item="i5insights-tab" data-targets="action-list.items" role="none" data-view-component="true" class="ActionListItem">
    
    
    <a tabindex="-1" id="item-f396f1c2-185e-4283-9276-5547471659d3" href="/google/styleguide/pulse" role="menuitem" data-view-component="true" class="ActionListContent ActionListContent--visual16">
        <span class="ActionListItem-visual ActionListItem-visual--leading">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-graph">
    <path d="M1.5 1.75V13.5h13.75a.75.75 0 0 1 0 1.5H.75a.75.75 0 0 1-.75-.75V1.75a.75.75 0 0 1 1.5 0Zm14.28 2.53-5.25 5.25a.75.75 0 0 1-1.06 0L7 7.06 4.28 9.78a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042l3.25-3.25a.75.75 0 0 1 1.06 0L10 7.94l4.72-4.72a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042Z"></path>
</svg>
        </span>
      
        <span data-view-component="true" class="ActionListItem-label">
          Insights
</span>      
</a>
  
</li>
</ul>    
</div></action-list>


</div>
      
</div></anchored-position>  </focus-group>
</action-menu></div>
</nav>

  </div>

  



<turbo-frame id="repo-content-turbo-frame" target="_top" data-turbo-action="advance" class="">
    <div id="repo-content-pjax-container" class="repository-content " >
    



    
      
    








<react-app
  app-name="react-code-view"
  initial-path="/google/styleguide/blob/gh-pages/intellij-java-google-style.xml"
    style="display: block; min-height: calc(100vh - 64px);"
  data-attempted-ssr="false"
  data-ssr="false"
  data-lazy="false"
  data-alternate="false"
  data-data-router-enabled="false"
  data-react-profiling="false"
>
  
  <script type="application/json" data-target="react-app.embeddedData">{"payload":{"allShortcutsEnabled":false,"fileTree":{"":{"items":[{"name":"intellij-java-google-style.xml","path":"intellij-java-google-style.xml","contentType":"file"}],"totalCount":1}},"fileTreeProcessingTime":null,"foldersToFetch":[],"incompleteFileTree":true,"repo":{"id":35969061,"defaultBranch":"gh-pages","name":"styleguide","ownerLogin":"google","currentUserCanPush":false,"isFork":false,"isEmpty":false,"createdAt":"2015-05-20T19:18:59.000Z","ownerAvatar":"https://avatars.githubusercontent.com/u/1342004?v=4","public":true,"private":false,"isOrgOwned":true},"codeLineWrapEnabled":false,"symbolsExpanded":false,"treeExpanded":true,"refInfo":{"name":"gh-pages","listCacheKey":"v0:1745617624.0","canEdit":false,"refType":"branch","currentOid":"1ab9120f856901f5cca56ca8947985628221b583"},"path":"intellij-java-google-style.xml","currentUser":null,"blob":{"rawLines":["\u003c?xml version=\"1.0\" encoding=\"UTF-8\"?\u003e","\u003ccode_scheme name=\"GoogleStyle\"\u003e","  \u003coption name=\"OTHER_INDENT_OPTIONS\"\u003e","    \u003cvalue\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"USE_TAB_CHARACTER\" value=\"false\" /\u003e","      \u003coption name=\"SMART_TABS\" value=\"false\" /\u003e","      \u003coption name=\"LABEL_INDENT_SIZE\" value=\"0\" /\u003e","      \u003coption name=\"LABEL_INDENT_ABSOLUTE\" value=\"false\" /\u003e","      \u003coption name=\"USE_RELATIVE_INDENTS\" value=\"false\" /\u003e","    \u003c/value\u003e","  \u003c/option\u003e","  \u003coption name=\"INSERT_INNER_CLASS_IMPORTS\" value=\"true\" /\u003e","  \u003coption name=\"CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND\" value=\"999\" /\u003e","  \u003coption name=\"NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND\" value=\"999\" /\u003e","  \u003coption name=\"PACKAGES_TO_USE_IMPORT_ON_DEMAND\"\u003e","    \u003cvalue /\u003e","  \u003c/option\u003e","  \u003coption name=\"IMPORT_LAYOUT_TABLE\"\u003e","    \u003cvalue\u003e","      \u003cpackage name=\"\" withSubpackages=\"true\" static=\"true\" /\u003e","      \u003cemptyLine /\u003e","      \u003cpackage name=\"\" withSubpackages=\"true\" static=\"false\" /\u003e","    \u003c/value\u003e","  \u003c/option\u003e","  \u003coption name=\"RIGHT_MARGIN\" value=\"100\" /\u003e","  \u003coption name=\"JD_ALIGN_PARAM_COMMENTS\" value=\"false\" /\u003e","  \u003coption name=\"JD_ALIGN_EXCEPTION_COMMENTS\" value=\"false\" /\u003e","  \u003coption name=\"JD_P_AT_EMPTY_LINES\" value=\"false\" /\u003e","  \u003coption name=\"JD_KEEP_EMPTY_PARAMETER\" value=\"false\" /\u003e","  \u003coption name=\"JD_KEEP_EMPTY_EXCEPTION\" value=\"false\" /\u003e","  \u003coption name=\"JD_KEEP_EMPTY_RETURN\" value=\"false\" /\u003e","  \u003coption name=\"KEEP_CONTROL_STATEMENT_IN_ONE_LINE\" value=\"false\" /\u003e","  \u003coption name=\"KEEP_BLANK_LINES_BEFORE_RBRACE\" value=\"0\" /\u003e","  \u003coption name=\"KEEP_BLANK_LINES_IN_CODE\" value=\"1\" /\u003e","  \u003coption name=\"BLANK_LINES_AFTER_CLASS_HEADER\" value=\"0\" /\u003e","  \u003coption name=\"ALIGN_MULTILINE_PARAMETERS\" value=\"false\" /\u003e","  \u003coption name=\"ALIGN_MULTILINE_FOR\" value=\"false\" /\u003e","  \u003coption name=\"CALL_PARAMETERS_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"METHOD_PARAMETERS_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"EXTENDS_LIST_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"THROWS_KEYWORD_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"METHOD_CALL_CHAIN_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"BINARY_OPERATION_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"BINARY_OPERATION_SIGN_ON_NEXT_LINE\" value=\"true\" /\u003e","  \u003coption name=\"TERNARY_OPERATION_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"TERNARY_OPERATION_SIGNS_ON_NEXT_LINE\" value=\"true\" /\u003e","  \u003coption name=\"FOR_STATEMENT_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"ARRAY_INITIALIZER_WRAP\" value=\"1\" /\u003e","  \u003coption name=\"WRAP_COMMENTS\" value=\"true\" /\u003e","  \u003coption name=\"IF_BRACE_FORCE\" value=\"3\" /\u003e","  \u003coption name=\"DOWHILE_BRACE_FORCE\" value=\"3\" /\u003e","  \u003coption name=\"WHILE_BRACE_FORCE\" value=\"3\" /\u003e","  \u003coption name=\"FOR_BRACE_FORCE\" value=\"3\" /\u003e","  \u003coption name=\"SPACE_BEFORE_ARRAY_INITIALIZER_LBRACE\" value=\"true\" /\u003e","  \u003cAndroidXmlCodeStyleSettings\u003e","    \u003coption name=\"USE_CUSTOM_SETTINGS\" value=\"true\" /\u003e","    \u003coption name=\"LAYOUT_SETTINGS\"\u003e","      \u003cvalue\u003e","        \u003coption name=\"INSERT_BLANK_LINE_BEFORE_TAG\" value=\"false\" /\u003e","      \u003c/value\u003e","    \u003c/option\u003e","  \u003c/AndroidXmlCodeStyleSettings\u003e","  \u003cJSCodeStyleSettings\u003e","    \u003coption name=\"INDENT_CHAINED_CALLS\" value=\"false\" /\u003e","  \u003c/JSCodeStyleSettings\u003e","  \u003cPython\u003e","    \u003coption name=\"USE_CONTINUATION_INDENT_FOR_ARGUMENTS\" value=\"true\" /\u003e","  \u003c/Python\u003e","  \u003cTypeScriptCodeStyleSettings\u003e","    \u003coption name=\"INDENT_CHAINED_CALLS\" value=\"false\" /\u003e","  \u003c/TypeScriptCodeStyleSettings\u003e","  \u003cXML\u003e","    \u003coption name=\"XML_ALIGN_ATTRIBUTES\" value=\"false\" /\u003e","    \u003coption name=\"XML_LEGACY_SETTINGS_IMPORTED\" value=\"true\" /\u003e","  \u003c/XML\u003e","  \u003ccodeStyleSettings language=\"CSS\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"ECMA Script Level 4\"\u003e","    \u003coption name=\"KEEP_BLANK_LINES_IN_CODE\" value=\"1\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_PARAMETERS\" value=\"false\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_FOR\" value=\"false\" /\u003e","    \u003coption name=\"CALL_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"METHOD_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"EXTENDS_LIST_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_SIGN_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_SIGNS_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"FOR_STATEMENT_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"ARRAY_INITIALIZER_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"IF_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"DOWHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"WHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"FOR_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"PARENT_SETTINGS_INSTALLED\" value=\"true\" /\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"HTML\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"JAVA\"\u003e","    \u003coption name=\"KEEP_CONTROL_STATEMENT_IN_ONE_LINE\" value=\"false\" /\u003e","    \u003coption name=\"KEEP_BLANK_LINES_IN_CODE\" value=\"1\" /\u003e","    \u003coption name=\"BLANK_LINES_AFTER_CLASS_HEADER\" value=\"1\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_PARAMETERS\" value=\"false\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_RESOURCES\" value=\"false\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_FOR\" value=\"false\" /\u003e","    \u003coption name=\"CALL_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"METHOD_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"EXTENDS_LIST_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"THROWS_KEYWORD_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"METHOD_CALL_CHAIN_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_SIGN_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_SIGNS_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"FOR_STATEMENT_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"ARRAY_INITIALIZER_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"WRAP_COMMENTS\" value=\"true\" /\u003e","    \u003coption name=\"IF_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"DOWHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"WHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"FOR_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"PARENT_SETTINGS_INSTALLED\" value=\"true\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"JSON\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"JavaScript\"\u003e","    \u003coption name=\"RIGHT_MARGIN\" value=\"80\" /\u003e","    \u003coption name=\"KEEP_BLANK_LINES_IN_CODE\" value=\"1\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_PARAMETERS\" value=\"false\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_FOR\" value=\"false\" /\u003e","    \u003coption name=\"CALL_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"METHOD_PARAMETERS_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"BINARY_OPERATION_SIGN_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"TERNARY_OPERATION_SIGNS_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"FOR_STATEMENT_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"ARRAY_INITIALIZER_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"IF_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"DOWHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"WHILE_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"FOR_BRACE_FORCE\" value=\"3\" /\u003e","    \u003coption name=\"PARENT_SETTINGS_INSTALLED\" value=\"true\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"PROTO\"\u003e","    \u003coption name=\"RIGHT_MARGIN\" value=\"80\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"protobuf\"\u003e","    \u003coption name=\"RIGHT_MARGIN\" value=\"80\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"Python\"\u003e","    \u003coption name=\"KEEP_BLANK_LINES_IN_CODE\" value=\"1\" /\u003e","    \u003coption name=\"RIGHT_MARGIN\" value=\"80\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_PARAMETERS\" value=\"false\" /\u003e","    \u003coption name=\"PARENT_SETTINGS_INSTALLED\" value=\"true\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"SASS\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"SCSS\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"TypeScript\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","  \u003ccodeStyleSettings language=\"XML\"\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"TAB_SIZE\" value=\"2\" /\u003e","    \u003c/indentOptions\u003e","    \u003carrangement\u003e","      \u003crules\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003exmlns:android\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003e^$\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003exmlns:.*\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003e^$\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:id\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003estyle\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003e^$\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003e^$\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:.*Style\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_width\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_height\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_weight\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_margin\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginTop\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginBottom\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginStart\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginEnd\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginLeft\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_marginRight\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:layout_.*\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:padding\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingTop\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingBottom\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingStart\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingEnd\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingLeft\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*:paddingRight\u003c/NAME\u003e","                \u003cXML_ATTRIBUTE /\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*\u003c/NAME\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res/android\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*\u003c/NAME\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/apk/res-auto\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*\u003c/NAME\u003e","                \u003cXML_NAMESPACE\u003ehttp://schemas.android.com/tools\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","        \u003csection\u003e","          \u003crule\u003e","            \u003cmatch\u003e","              \u003cAND\u003e","                \u003cNAME\u003e.*\u003c/NAME\u003e","                \u003cXML_NAMESPACE\u003e.*\u003c/XML_NAMESPACE\u003e","              \u003c/AND\u003e","            \u003c/match\u003e","            \u003corder\u003eBY_NAME\u003c/order\u003e","          \u003c/rule\u003e","        \u003c/section\u003e","      \u003c/rules\u003e","    \u003c/arrangement\u003e","  \u003c/codeStyleSettings\u003e","  \u003cObjective-C\u003e","    \u003coption name=\"INDENT_NAMESPACE_MEMBERS\" value=\"0\" /\u003e","    \u003coption name=\"INDENT_C_STRUCT_MEMBERS\" value=\"2\" /\u003e","    \u003coption name=\"INDENT_CLASS_MEMBERS\" value=\"2\" /\u003e","    \u003coption name=\"INDENT_VISIBILITY_KEYWORDS\" value=\"1\" /\u003e","    \u003coption name=\"INDENT_INSIDE_CODE_BLOCK\" value=\"2\" /\u003e","    \u003coption name=\"KEEP_STRUCTURES_IN_ONE_LINE\" value=\"true\" /\u003e","    \u003coption name=\"FUNCTION_PARAMETERS_WRAP\" value=\"5\" /\u003e","    \u003coption name=\"FUNCTION_CALL_ARGUMENTS_WRAP\" value=\"5\" /\u003e","    \u003coption name=\"TEMPLATE_CALL_ARGUMENTS_WRAP\" value=\"5\" /\u003e","    \u003coption name=\"TEMPLATE_CALL_ARGUMENTS_ALIGN_MULTILINE\" value=\"true\" /\u003e","    \u003coption name=\"ALIGN_INIT_LIST_IN_COLUMNS\" value=\"false\" /\u003e","    \u003coption name=\"SPACE_BEFORE_SUPERCLASS_COLON\" value=\"false\" /\u003e","  \u003c/Objective-C\u003e","  \u003cObjective-C-extensions\u003e","    \u003coption name=\"GENERATE_INSTANCE_VARIABLES_FOR_PROPERTIES\" value=\"ASK\" /\u003e","    \u003coption name=\"RELEASE_STYLE\" value=\"IVAR\" /\u003e","    \u003coption name=\"TYPE_QUALIFIERS_PLACEMENT\" value=\"BEFORE\" /\u003e","    \u003cfile\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Import\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Macro\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Typedef\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Enum\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Constant\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Global\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Struct\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"FunctionPredecl\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Function\" /\u003e","    \u003c/file\u003e","    \u003cclass\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Property\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"Synthesize\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"InitMethod\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"StaticMethod\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"InstanceMethod\" /\u003e","      \u003coption name=\"com.jetbrains.cidr.lang.util.OCDeclarationKind\" value=\"DeallocMethod\" /\u003e","    \u003c/class\u003e","    \u003cextensions\u003e","      \u003cpair source=\"cc\" header=\"h\" /\u003e","      \u003cpair source=\"c\" header=\"h\" /\u003e","    \u003c/extensions\u003e","  \u003c/Objective-C-extensions\u003e","  \u003ccodeStyleSettings language=\"ObjectiveC\"\u003e","    \u003coption name=\"RIGHT_MARGIN\" value=\"80\" /\u003e","    \u003coption name=\"KEEP_BLANK_LINES_BEFORE_RBRACE\" value=\"1\" /\u003e","    \u003coption name=\"BLANK_LINES_BEFORE_IMPORTS\" value=\"0\" /\u003e","    \u003coption name=\"BLANK_LINES_AFTER_IMPORTS\" value=\"0\" /\u003e","    \u003coption name=\"BLANK_LINES_AROUND_CLASS\" value=\"0\" /\u003e","    \u003coption name=\"BLANK_LINES_AROUND_METHOD\" value=\"0\" /\u003e","    \u003coption name=\"BLANK_LINES_AROUND_METHOD_IN_INTERFACE\" value=\"0\" /\u003e","    \u003coption name=\"ALIGN_MULTILINE_BINARY_OPERATION\" value=\"false\" /\u003e","    \u003coption name=\"BINARY_OPERATION_SIGN_ON_NEXT_LINE\" value=\"true\" /\u003e","    \u003coption name=\"FOR_STATEMENT_WRAP\" value=\"1\" /\u003e","    \u003coption name=\"ASSIGNMENT_WRAP\" value=\"1\" /\u003e","    \u003cindentOptions\u003e","      \u003coption name=\"INDENT_SIZE\" value=\"2\" /\u003e","      \u003coption name=\"CONTINUATION_INDENT_SIZE\" value=\"4\" /\u003e","    \u003c/indentOptions\u003e","  \u003c/codeStyleSettings\u003e","\u003c/code_scheme\u003e"],"stylingDirectives":[[[2,5,"pl-ent"],[5,13,"pl-e"],[14,19,"pl-s"],[14,15,"pl-pds"],[18,19,"pl-pds"],[19,28,"pl-e"],[29,36,"pl-s"],[29,30,"pl-pds"],[35,36,"pl-pds"]],[[1,12,"pl-ent"],[13,17,"pl-e"],[18,31,"pl-s"],[18,19,"pl-pds"],[30,31,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,37,"pl-s"],[15,16,"pl-pds"],[36,37,"pl-pds"]],[[5,10,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,38,"pl-s"],[19,20,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,52,"pl-s"],[45,46,"pl-pds"],[51,52,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,31,"pl-s"],[19,20,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,45,"pl-s"],[38,39,"pl-pds"],[44,45,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,38,"pl-s"],[19,20,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,42,"pl-s"],[19,20,"pl-pds"],[41,42,"pl-pds"],[43,48,"pl-e"],[49,56,"pl-s"],[49,50,"pl-pds"],[55,56,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,41,"pl-s"],[19,20,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,55,"pl-s"],[48,49,"pl-pds"],[54,55,"pl-pds"]],[[6,11,"pl-ent"]],[[4,10,"pl-ent"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,43,"pl-s"],[15,16,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,56,"pl-s"],[50,51,"pl-pds"],[55,56,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,52,"pl-s"],[15,16,"pl-pds"],[51,52,"pl-pds"],[53,58,"pl-e"],[59,64,"pl-s"],[59,60,"pl-pds"],[63,64,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,52,"pl-s"],[15,16,"pl-pds"],[51,52,"pl-pds"],[53,58,"pl-e"],[59,64,"pl-s"],[59,60,"pl-pds"],[63,64,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,49,"pl-s"],[15,16,"pl-pds"],[48,49,"pl-pds"]],[[5,10,"pl-ent"]],[[4,10,"pl-ent"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,36,"pl-s"],[15,16,"pl-pds"],[35,36,"pl-pds"]],[[5,10,"pl-ent"]],[[7,14,"pl-ent"],[15,19,"pl-e"],[20,22,"pl-s"],[20,21,"pl-pds"],[21,22,"pl-pds"],[23,38,"pl-e"],[39,45,"pl-s"],[39,40,"pl-pds"],[44,45,"pl-pds"],[46,52,"pl-e"],[53,59,"pl-s"],[53,54,"pl-pds"],[58,59,"pl-pds"]],[[7,16,"pl-ent"]],[[7,14,"pl-ent"],[15,19,"pl-e"],[20,22,"pl-s"],[20,21,"pl-pds"],[21,22,"pl-pds"],[23,38,"pl-e"],[39,45,"pl-s"],[39,40,"pl-pds"],[44,45,"pl-pds"],[46,52,"pl-e"],[53,60,"pl-s"],[53,54,"pl-pds"],[59,60,"pl-pds"]],[[6,11,"pl-ent"]],[[4,10,"pl-ent"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,29,"pl-s"],[15,16,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,41,"pl-s"],[36,37,"pl-pds"],[40,41,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,40,"pl-s"],[15,16,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,54,"pl-s"],[47,48,"pl-pds"],[53,54,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,44,"pl-s"],[15,16,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,58,"pl-s"],[51,52,"pl-pds"],[57,58,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,36,"pl-s"],[15,16,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,50,"pl-s"],[43,44,"pl-pds"],[49,50,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,40,"pl-s"],[15,16,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,54,"pl-s"],[47,48,"pl-pds"],[53,54,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,40,"pl-s"],[15,16,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,54,"pl-s"],[47,48,"pl-pds"],[53,54,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,37,"pl-s"],[15,16,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,51,"pl-s"],[44,45,"pl-pds"],[50,51,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,51,"pl-s"],[15,16,"pl-pds"],[50,51,"pl-pds"],[52,57,"pl-e"],[58,65,"pl-s"],[58,59,"pl-pds"],[64,65,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,47,"pl-s"],[15,16,"pl-pds"],[46,47,"pl-pds"],[48,53,"pl-e"],[54,57,"pl-s"],[54,55,"pl-pds"],[56,57,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,41,"pl-s"],[15,16,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,47,"pl-s"],[15,16,"pl-pds"],[46,47,"pl-pds"],[48,53,"pl-e"],[54,57,"pl-s"],[54,55,"pl-pds"],[56,57,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,43,"pl-s"],[15,16,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,57,"pl-s"],[50,51,"pl-pds"],[56,57,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,36,"pl-s"],[15,16,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,50,"pl-s"],[43,44,"pl-pds"],[49,50,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,37,"pl-s"],[15,16,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,47,"pl-s"],[44,45,"pl-pds"],[46,47,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,39,"pl-s"],[15,16,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,34,"pl-s"],[15,16,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,36,"pl-s"],[15,16,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,39,"pl-s"],[15,16,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,38,"pl-s"],[15,16,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,51,"pl-s"],[15,16,"pl-pds"],[50,51,"pl-pds"],[52,57,"pl-e"],[58,64,"pl-s"],[58,59,"pl-pds"],[63,64,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,39,"pl-s"],[15,16,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,53,"pl-s"],[15,16,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,66,"pl-s"],[60,61,"pl-pds"],[65,66,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,35,"pl-s"],[15,16,"pl-pds"],[34,35,"pl-pds"],[36,41,"pl-e"],[42,45,"pl-s"],[42,43,"pl-pds"],[44,45,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,39,"pl-s"],[15,16,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,30,"pl-s"],[15,16,"pl-pds"],[29,30,"pl-pds"],[31,36,"pl-e"],[37,43,"pl-s"],[37,38,"pl-pds"],[42,43,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,31,"pl-s"],[15,16,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,41,"pl-s"],[38,39,"pl-pds"],[40,41,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,36,"pl-s"],[15,16,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,34,"pl-s"],[15,16,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,32,"pl-s"],[15,16,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[3,9,"pl-ent"],[10,14,"pl-e"],[15,54,"pl-s"],[15,16,"pl-pds"],[53,54,"pl-pds"],[55,60,"pl-e"],[61,67,"pl-s"],[61,62,"pl-pds"],[66,67,"pl-pds"]],[[3,30,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,51,"pl-s"],[45,46,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,34,"pl-s"],[17,18,"pl-pds"],[33,34,"pl-pds"]],[[7,12,"pl-ent"]],[[9,15,"pl-ent"],[16,20,"pl-e"],[21,51,"pl-s"],[21,22,"pl-pds"],[50,51,"pl-pds"],[52,57,"pl-e"],[58,65,"pl-s"],[58,59,"pl-pds"],[64,65,"pl-pds"]],[[8,13,"pl-ent"]],[[6,12,"pl-ent"]],[[4,31,"pl-ent"]],[[3,22,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,53,"pl-s"],[46,47,"pl-pds"],[52,53,"pl-pds"]],[[4,23,"pl-ent"]],[[3,9,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,56,"pl-s"],[17,18,"pl-pds"],[55,56,"pl-pds"],[57,62,"pl-e"],[63,69,"pl-s"],[63,64,"pl-pds"],[68,69,"pl-pds"]],[[4,10,"pl-ent"]],[[3,30,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,53,"pl-s"],[46,47,"pl-pds"],[52,53,"pl-pds"]],[[4,31,"pl-ent"]],[[3,6,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,53,"pl-s"],[46,47,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,47,"pl-s"],[17,18,"pl-pds"],[46,47,"pl-pds"],[48,53,"pl-e"],[54,60,"pl-s"],[54,55,"pl-pds"],[59,60,"pl-pds"]],[[4,7,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,35,"pl-s"],[30,31,"pl-pds"],[34,35,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,51,"pl-s"],[30,31,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,59,"pl-s"],[52,53,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,52,"pl-s"],[45,46,"pl-pds"],[51,52,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,36,"pl-s"],[17,18,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,40,"pl-s"],[17,18,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,50,"pl-s"],[47,48,"pl-pds"],[49,50,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,53,"pl-s"],[17,18,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,66,"pl-s"],[60,61,"pl-pds"],[65,66,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,55,"pl-s"],[17,18,"pl-pds"],[54,55,"pl-pds"],[56,61,"pl-e"],[62,68,"pl-s"],[62,63,"pl-pds"],[67,68,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,37,"pl-s"],[17,18,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,47,"pl-s"],[44,45,"pl-pds"],[46,47,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,33,"pl-s"],[17,18,"pl-pds"],[32,33,"pl-pds"],[34,39,"pl-e"],[40,43,"pl-s"],[40,41,"pl-pds"],[42,43,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,36,"pl-s"],[17,18,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,34,"pl-s"],[17,18,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,57,"pl-s"],[51,52,"pl-pds"],[56,57,"pl-pds"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,36,"pl-s"],[30,31,"pl-pds"],[35,36,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,36,"pl-s"],[30,31,"pl-pds"],[35,36,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,53,"pl-s"],[17,18,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,67,"pl-s"],[60,61,"pl-pds"],[66,67,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,49,"pl-s"],[17,18,"pl-pds"],[48,49,"pl-pds"],[50,55,"pl-e"],[56,59,"pl-s"],[56,57,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,59,"pl-s"],[52,53,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,58,"pl-s"],[51,52,"pl-pds"],[57,58,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,52,"pl-s"],[45,46,"pl-pds"],[51,52,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,36,"pl-s"],[17,18,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,40,"pl-s"],[17,18,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,50,"pl-s"],[47,48,"pl-pds"],[49,50,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,53,"pl-s"],[17,18,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,66,"pl-s"],[60,61,"pl-pds"],[65,66,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,55,"pl-s"],[17,18,"pl-pds"],[54,55,"pl-pds"],[56,61,"pl-e"],[62,68,"pl-s"],[62,63,"pl-pds"],[67,68,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,37,"pl-s"],[17,18,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,47,"pl-s"],[44,45,"pl-pds"],[46,47,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,32,"pl-s"],[17,18,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,45,"pl-s"],[39,40,"pl-pds"],[44,45,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,33,"pl-s"],[17,18,"pl-pds"],[32,33,"pl-pds"],[34,39,"pl-e"],[40,43,"pl-s"],[40,41,"pl-pds"],[42,43,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,36,"pl-s"],[17,18,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,34,"pl-s"],[17,18,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,57,"pl-s"],[51,52,"pl-pds"],[56,57,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,36,"pl-s"],[30,31,"pl-pds"],[35,36,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,42,"pl-s"],[30,31,"pl-pds"],[41,42,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,31,"pl-s"],[17,18,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,42,"pl-s"],[38,39,"pl-pds"],[41,42,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,59,"pl-s"],[52,53,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,52,"pl-s"],[45,46,"pl-pds"],[51,52,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,40,"pl-s"],[17,18,"pl-pds"],[39,40,"pl-pds"],[41,46,"pl-e"],[47,50,"pl-s"],[47,48,"pl-pds"],[49,50,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,53,"pl-s"],[17,18,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,66,"pl-s"],[60,61,"pl-pds"],[65,66,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,55,"pl-s"],[17,18,"pl-pds"],[54,55,"pl-pds"],[56,61,"pl-e"],[62,68,"pl-s"],[62,63,"pl-pds"],[67,68,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,37,"pl-s"],[17,18,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,47,"pl-s"],[44,45,"pl-pds"],[46,47,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,41,"pl-s"],[17,18,"pl-pds"],[40,41,"pl-pds"],[42,47,"pl-e"],[48,51,"pl-s"],[48,49,"pl-pds"],[50,51,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,33,"pl-s"],[17,18,"pl-pds"],[32,33,"pl-pds"],[34,39,"pl-e"],[40,43,"pl-s"],[40,41,"pl-pds"],[42,43,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,38,"pl-s"],[17,18,"pl-pds"],[37,38,"pl-pds"],[39,44,"pl-e"],[45,48,"pl-s"],[45,46,"pl-pds"],[47,48,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,36,"pl-s"],[17,18,"pl-pds"],[35,36,"pl-pds"],[37,42,"pl-e"],[43,46,"pl-s"],[43,44,"pl-pds"],[45,46,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,34,"pl-s"],[17,18,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,57,"pl-s"],[51,52,"pl-pds"],[56,57,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,37,"pl-s"],[30,31,"pl-pds"],[36,37,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,31,"pl-s"],[17,18,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,42,"pl-s"],[38,39,"pl-pds"],[41,42,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,40,"pl-s"],[30,31,"pl-pds"],[39,40,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,31,"pl-s"],[17,18,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,42,"pl-s"],[38,39,"pl-pds"],[41,42,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,38,"pl-s"],[30,31,"pl-pds"],[37,38,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,31,"pl-s"],[17,18,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,42,"pl-s"],[38,39,"pl-pds"],[41,42,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,59,"pl-s"],[52,53,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,57,"pl-s"],[51,52,"pl-pds"],[56,57,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,36,"pl-s"],[30,31,"pl-pds"],[35,36,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,36,"pl-s"],[30,31,"pl-pds"],[35,36,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,42,"pl-s"],[30,31,"pl-pds"],[41,42,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,35,"pl-s"],[30,31,"pl-pds"],[34,35,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,29,"pl-s"],[19,20,"pl-pds"],[28,29,"pl-pds"],[30,35,"pl-e"],[36,39,"pl-s"],[36,37,"pl-pds"],[38,39,"pl-pds"]],[[6,19,"pl-ent"]],[[5,16,"pl-ent"]],[[7,12,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[37,41,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[35,48,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[32,36,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[35,48,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[29,33,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[29,33,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[35,48,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[26,30,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[35,48,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[34,38,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[39,43,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[40,44,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[40,44,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[40,44,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[43,47,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[46,50,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[45,49,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[43,47,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[44,48,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[45,49,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[36,40,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[34,38,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[37,41,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[40,44,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[39,43,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[37,41,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[38,42,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[39,43,"pl-ent"]],[[17,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[26,30,"pl-ent"]],[[17,30,"pl-ent"],[75,88,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[26,30,"pl-ent"]],[[17,30,"pl-ent"],[72,85,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[26,30,"pl-ent"]],[[17,30,"pl-ent"],[65,78,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[9,16,"pl-ent"]],[[11,15,"pl-ent"]],[[13,18,"pl-ent"]],[[15,18,"pl-ent"]],[[17,21,"pl-ent"],[26,30,"pl-ent"]],[[17,30,"pl-ent"],[35,48,"pl-ent"]],[[16,19,"pl-ent"]],[[14,19,"pl-ent"]],[[13,18,"pl-ent"],[28,33,"pl-ent"]],[[12,16,"pl-ent"]],[[10,17,"pl-ent"]],[[8,13,"pl-ent"]],[[6,17,"pl-ent"]],[[4,21,"pl-ent"]],[[3,14,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,42,"pl-s"],[17,18,"pl-pds"],[41,42,"pl-pds"],[43,48,"pl-e"],[49,52,"pl-s"],[49,50,"pl-pds"],[51,52,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,39,"pl-s"],[17,18,"pl-pds"],[38,39,"pl-pds"],[40,45,"pl-e"],[46,49,"pl-s"],[46,47,"pl-pds"],[48,49,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,46,"pl-s"],[17,18,"pl-pds"],[45,46,"pl-pds"],[47,52,"pl-e"],[53,59,"pl-s"],[53,54,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,47,"pl-s"],[17,18,"pl-pds"],[46,47,"pl-pds"],[48,53,"pl-e"],[54,57,"pl-s"],[54,55,"pl-pds"],[56,57,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,47,"pl-s"],[17,18,"pl-pds"],[46,47,"pl-pds"],[48,53,"pl-e"],[54,57,"pl-s"],[54,55,"pl-pds"],[56,57,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,58,"pl-s"],[17,18,"pl-pds"],[57,58,"pl-pds"],[59,64,"pl-e"],[65,71,"pl-s"],[65,66,"pl-pds"],[70,71,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,59,"pl-s"],[52,53,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,48,"pl-s"],[17,18,"pl-pds"],[47,48,"pl-pds"],[49,54,"pl-e"],[55,62,"pl-s"],[55,56,"pl-pds"],[61,62,"pl-pds"]],[[4,15,"pl-ent"]],[[3,25,"pl-ent"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,61,"pl-s"],[17,18,"pl-pds"],[60,61,"pl-pds"],[62,67,"pl-e"],[68,73,"pl-s"],[68,69,"pl-pds"],[72,73,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,32,"pl-s"],[17,18,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,45,"pl-s"],[39,40,"pl-pds"],[44,45,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,59,"pl-s"],[51,52,"pl-pds"],[58,59,"pl-pds"]],[[5,9,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,82,"pl-s"],[74,75,"pl-pds"],[81,82,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,81,"pl-s"],[74,75,"pl-pds"],[80,81,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,83,"pl-s"],[74,75,"pl-pds"],[82,83,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,80,"pl-s"],[74,75,"pl-pds"],[79,80,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,84,"pl-s"],[74,75,"pl-pds"],[83,84,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,82,"pl-s"],[74,75,"pl-pds"],[81,82,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,82,"pl-s"],[74,75,"pl-pds"],[81,82,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,91,"pl-s"],[74,75,"pl-pds"],[90,91,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,84,"pl-s"],[74,75,"pl-pds"],[83,84,"pl-pds"]],[[6,10,"pl-ent"]],[[5,10,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,84,"pl-s"],[74,75,"pl-pds"],[83,84,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,86,"pl-s"],[74,75,"pl-pds"],[85,86,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,86,"pl-s"],[74,75,"pl-pds"],[85,86,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,88,"pl-s"],[74,75,"pl-pds"],[87,88,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,90,"pl-s"],[74,75,"pl-pds"],[89,90,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,67,"pl-s"],[19,20,"pl-pds"],[66,67,"pl-pds"],[68,73,"pl-e"],[74,89,"pl-s"],[74,75,"pl-pds"],[88,89,"pl-pds"]],[[6,11,"pl-ent"]],[[5,15,"pl-ent"]],[[7,11,"pl-ent"],[12,18,"pl-e"],[19,23,"pl-s"],[19,20,"pl-pds"],[22,23,"pl-pds"],[24,30,"pl-e"],[31,34,"pl-s"],[31,32,"pl-pds"],[33,34,"pl-pds"]],[[7,11,"pl-ent"],[12,18,"pl-e"],[19,22,"pl-s"],[19,20,"pl-pds"],[21,22,"pl-pds"],[23,29,"pl-e"],[30,33,"pl-s"],[30,31,"pl-pds"],[32,33,"pl-pds"]],[[6,16,"pl-ent"]],[[4,26,"pl-ent"]],[[3,20,"pl-ent"],[21,29,"pl-e"],[30,42,"pl-s"],[30,31,"pl-pds"],[41,42,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,31,"pl-s"],[17,18,"pl-pds"],[30,31,"pl-pds"],[32,37,"pl-e"],[38,42,"pl-s"],[38,39,"pl-pds"],[41,42,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,49,"pl-s"],[17,18,"pl-pds"],[48,49,"pl-pds"],[50,55,"pl-e"],[56,59,"pl-s"],[56,57,"pl-pds"],[58,59,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,45,"pl-s"],[17,18,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,54,"pl-s"],[51,52,"pl-pds"],[53,54,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,43,"pl-s"],[17,18,"pl-pds"],[42,43,"pl-pds"],[44,49,"pl-e"],[50,53,"pl-s"],[50,51,"pl-pds"],[52,53,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,44,"pl-s"],[17,18,"pl-pds"],[43,44,"pl-pds"],[45,50,"pl-e"],[51,54,"pl-s"],[51,52,"pl-pds"],[53,54,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,57,"pl-s"],[17,18,"pl-pds"],[56,57,"pl-pds"],[58,63,"pl-e"],[64,67,"pl-s"],[64,65,"pl-pds"],[66,67,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,51,"pl-s"],[17,18,"pl-pds"],[50,51,"pl-pds"],[52,57,"pl-e"],[58,65,"pl-s"],[58,59,"pl-pds"],[64,65,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,53,"pl-s"],[17,18,"pl-pds"],[52,53,"pl-pds"],[54,59,"pl-e"],[60,66,"pl-s"],[60,61,"pl-pds"],[65,66,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,37,"pl-s"],[17,18,"pl-pds"],[36,37,"pl-pds"],[38,43,"pl-e"],[44,47,"pl-s"],[44,45,"pl-pds"],[46,47,"pl-pds"]],[[5,11,"pl-ent"],[12,16,"pl-e"],[17,34,"pl-s"],[17,18,"pl-pds"],[33,34,"pl-pds"],[35,40,"pl-e"],[41,44,"pl-s"],[41,42,"pl-pds"],[43,44,"pl-pds"]],[[5,18,"pl-ent"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,32,"pl-s"],[19,20,"pl-pds"],[31,32,"pl-pds"],[33,38,"pl-e"],[39,42,"pl-s"],[39,40,"pl-pds"],[41,42,"pl-pds"]],[[7,13,"pl-ent"],[14,18,"pl-e"],[19,45,"pl-s"],[19,20,"pl-pds"],[44,45,"pl-pds"],[46,51,"pl-e"],[52,55,"pl-s"],[52,53,"pl-pds"],[54,55,"pl-pds"]],[[6,19,"pl-ent"]],[[4,21,"pl-ent"]],[[2,13,"pl-ent"]]],"colorizedLines":null,"csv":null,"csvError":null,"dependabotInfo":{"showConfigurationBanner":false,"configFilePath":null,"networkDependabotPath":"/google/styleguide/network/updates","dismissConfigurationNoticePath":"/settings/dismiss-notice/dependabot_configuration_notice","configurationNoticeDismissed":null},"displayName":"intellij-java-google-style.xml","displayUrl":"https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml?raw=true","headerInfo":{"blobSize":"21.6 KB","deleteTooltip":"You must be signed in to make or propose changes","editTooltip":"You must be signed in to make or propose changes","ghDesktopPath":"https://desktop.github.com","isGitLfs":false,"onBranch":true,"shortPath":"f3a6743","siteNavLoginPath":"/login?return_to=https%3A%2F%2Fgithub.com%2Fgoogle%2Fstyleguide%2Fblob%2Fgh-pages%2Fintellij-java-google-style.xml","isCSV":false,"isRichtext":false,"toc":null,"lineInfo":{"truncatedLoc":"598","truncatedSloc":"598"},"mode":"file"},"image":false,"isCodeownersFile":null,"isPlain":false,"isValidLegacyIssueTemplate":false,"issueTemplate":null,"discussionTemplate":null,"language":"XML","languageID":399,"large":false,"planSupportInfo":{"repoIsFork":null,"repoOwnedByCurrentUser":null,"requestFullPath":"/google/styleguide/blob/gh-pages/intellij-java-google-style.xml","showFreeOrgGatedFeatureMessage":null,"showPlanSupportBanner":null,"upgradeDataAttributes":null,"upgradePath":null},"publishBannersInfo":{"dismissActionNoticePath":"/settings/dismiss-notice/publish_action_from_dockerfile","releasePath":"/google/styleguide/releases/new?marketplace=true","showPublishActionBanner":false},"rawBlobUrl":"https://github.com/google/styleguide/raw/refs/heads/gh-pages/intellij-java-google-style.xml","renderImageOrRaw":false,"richText":null,"renderedFileInfo":null,"shortPath":null,"symbolsEnabled":true,"tabSize":8,"topBannersInfo":{"overridingGlobalFundingFile":false,"globalPreferredFundingPath":null,"showInvalidCitationWarning":false,"citationHelpUrl":"https://docs.github.com/github/creating-cloning-and-archiving-repositories/creating-a-repository-on-github/about-citation-files","actionsOnboardingTip":null},"truncated":false,"viewable":true,"workflowRedirectUrl":null,"symbols":null},"copilotInfo":null,"copilotAccessAllowed":false,"modelsAccessAllowed":false,"modelsRepoIntegrationEnabled":false,"csrf_tokens":{"/google/styleguide/branches":{"post":"a1Ku___1NthFPTjuWoDeGh87b5QviN5Z6v7l8fn2tMudPJtNnrPpRbAI2bmV02aCJePGaiNjVx5k9p5PPNYlKg"},"/repos/preferences":{"post":"mFjpllsycY0DneV2uTQf03LNC-dEa1ToPTPhBHAvWjk_2EIHQBel30aVo6ORxNMlTmCP7abrn9JtxpgbogdFYA"}}},"title":"styleguide/intellij-java-google-style.xml at gh-pages · google/styleguide","appPayload":{"helpUrl":"https://docs.github.com","findFileWorkerPath":"/assets-cdn/worker/find-file-worker-263cab1760dd.js","findInFileWorkerPath":"/assets-cdn/worker/find-in-file-worker-b84e9496fc59.js","githubDevUrl":null,"enabled_features":{"code_nav_ui_events":false,"react_blob_overlay":false,"accessible_code_button":true}}}</script>
  <div data-target="react-app.reactRoot"></div>
</react-app>
</turbo-frame>



  </div>

</turbo-frame>

    </main>
  </div>

  </div>

          <footer class="footer pt-8 pb-6 f6 color-fg-muted p-responsive" role="contentinfo" >
  <h2 class='sr-only'>Footer</h2>

  


  <div class="d-flex flex-justify-center flex-items-center flex-column-reverse flex-lg-row flex-wrap flex-lg-nowrap">
    <div class="d-flex flex-items-center flex-shrink-0 mx-2">
      <a aria-label="GitHub Homepage" class="footer-octicon mr-2" href="https://github.com">
        <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github">
    <path d="M12 1C5.923 1 1 5.923 1 12c0 4.867 3.149 8.979 7.521 10.436.55.096.756-.233.756-.522 0-.262-.013-1.128-.013-2.049-2.764.509-3.479-.674-3.699-1.292-.124-.317-.66-1.293-1.127-1.554-.385-.207-.936-.715-.014-.729.866-.014 1.485.797 1.691 1.128.99 1.663 2.571 1.196 3.204.907.096-.715.385-1.196.701-1.471-2.448-.275-5.005-1.224-5.005-5.432 0-1.196.426-2.186 1.128-2.956-.111-.275-.496-1.402.11-2.915 0 0 .921-.288 3.024 1.128a10.193 10.193 0 0 1 2.75-.371c.936 0 1.871.123 2.75.371 2.104-1.43 3.025-1.128 3.025-1.128.605 1.513.221 2.64.111 2.915.701.77 1.127 1.747 1.127 2.956 0 4.222-2.571 5.157-5.019 5.432.399.344.743 1.004.743 2.035 0 1.471-.014 2.654-.014 3.025 0 .289.206.632.756.522C19.851 20.979 23 16.854 23 12c0-6.077-4.922-11-11-11Z"></path>
</svg>
</a>
      <span>
        &copy; 2025 GitHub,&nbsp;Inc.
      </span>
    </div>

    <nav aria-label="Footer">
      <h3 class="sr-only" id="sr-footer-heading">Footer navigation</h3>

      <ul class="list-style-none d-flex flex-justify-center flex-wrap mb-2 mb-lg-0" aria-labelledby="sr-footer-heading">

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to Terms&quot;,&quot;label&quot;:&quot;text:terms&quot;}" href="https://docs.github.com/site-policy/github-terms/github-terms-of-service" data-view-component="true" class="Link--secondary Link">Terms</a>
          </li>

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to privacy&quot;,&quot;label&quot;:&quot;text:privacy&quot;}" href="https://docs.github.com/site-policy/privacy-policies/github-privacy-statement" data-view-component="true" class="Link--secondary Link">Privacy</a>
          </li>

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to security&quot;,&quot;label&quot;:&quot;text:security&quot;}" href="https://github.com/security" data-view-component="true" class="Link--secondary Link">Security</a>
          </li>

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to status&quot;,&quot;label&quot;:&quot;text:status&quot;}" href="https://www.githubstatus.com/" data-view-component="true" class="Link--secondary Link">Status</a>
          </li>

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to docs&quot;,&quot;label&quot;:&quot;text:docs&quot;}" href="https://docs.github.com/" data-view-component="true" class="Link--secondary Link">Docs</a>
          </li>

          <li class="mx-2">
            <a data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to contact&quot;,&quot;label&quot;:&quot;text:contact&quot;}" href="https://support.github.com?tags=dotcom-footer" data-view-component="true" class="Link--secondary Link">Contact</a>
          </li>

          <li class="mx-2" >
  <cookie-consent-link>
    <button
      type="button"
      class="Link--secondary underline-on-hover border-0 p-0 color-bg-transparent"
      data-action="click:cookie-consent-link#showConsentManagement"
      data-analytics-event="{&quot;location&quot;:&quot;footer&quot;,&quot;action&quot;:&quot;cookies&quot;,&quot;context&quot;:&quot;subfooter&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;cookies_link_subfooter_footer&quot;}"
    >
       Manage cookies
    </button>
  </cookie-consent-link>
</li>

<li class="mx-2">
  <cookie-consent-link>
    <button
      type="button"
      class="Link--secondary underline-on-hover border-0 p-0 color-bg-transparent"
      data-action="click:cookie-consent-link#showConsentManagement"
      data-analytics-event="{&quot;location&quot;:&quot;footer&quot;,&quot;action&quot;:&quot;dont_share_info&quot;,&quot;context&quot;:&quot;subfooter&quot;,&quot;tag&quot;:&quot;link&quot;,&quot;label&quot;:&quot;dont_share_info_link_subfooter_footer&quot;}"
    >
      Do not share my personal information
    </button>
  </cookie-consent-link>
</li>

      </ul>
    </nav>
  </div>
</footer>



    <ghcc-consent id="ghcc" class="position-fixed bottom-0 left-0" style="z-index: 999999"
      data-locale="en"
      data-initial-cookie-consent-allowed=""
      data-cookie-consent-required="true"
    ></ghcc-consent>



  <div id="ajax-error-message" class="ajax-error-message flash flash-error" hidden>
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-alert">
    <path d="M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path>
</svg>
    <button type="button" class="flash-close js-ajax-error-dismiss" aria-label="Dismiss error">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg>
    </button>
    You can’t perform that action at this time.
  </div>

    <template id="site-details-dialog">
  <details class="details-reset details-overlay details-overlay-dark lh-default color-fg-default hx_rsm" open>
    <summary role="button" aria-label="Close dialog"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast hx_rsm-dialog hx_rsm-modal">
      <button class="Box-btn-octicon m-0 btn-octicon position-absolute right-0 top-0" type="button" aria-label="Close dialog" data-close-dialog>
        <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path d="M3.72 3.72a.75.75 0 0 1 1.06 0L8 6.94l3.22-3.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L9.06 8l3.22 3.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L8 9.06l-3.22 3.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L6.94 8 3.72 4.78a.75.75 0 0 1 0-1.06Z"></path>
</svg>
      </button>
      <div class="octocat-spinner my-6 js-details-dialog-spinner"></div>
    </details-dialog>
  </details>
</template>

    <div class="Popover js-hovercard-content position-absolute" style="display: none; outline: none;">
  <div class="Popover-message Popover-message--bottom-left Popover-message--large Box color-shadow-large" style="width:360px;">
  </div>
</div>

    <template id="snippet-clipboard-copy-button">
  <div class="zeroclipboard-container position-absolute right-0 top-0">
    <clipboard-copy aria-label="Copy" class="ClipboardButton btn js-clipboard-copy m-2 p-0" data-copy-feedback="Copied!" data-tooltip-direction="w">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon m-2">
    <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
</svg>
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-check js-clipboard-check-icon color-fg-success d-none m-2">
    <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"></path>
</svg>
    </clipboard-copy>
  </div>
</template>
<template id="snippet-clipboard-copy-button-unpositioned">
  <div class="zeroclipboard-container">
    <clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon">
    <path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path>
</svg>
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-check js-clipboard-check-icon color-fg-success d-none">
    <path d="M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z"></path>
</svg>
    </clipboard-copy>
  </div>
</template>




    </div>
    <div id="js-global-screen-reader-notice" class="sr-only mt-n1" aria-live="polite" aria-atomic="true" ></div>
    <div id="js-global-screen-reader-notice-assertive" class="sr-only mt-n1" aria-live="assertive" aria-atomic="true"></div>
  </body>
</html>

