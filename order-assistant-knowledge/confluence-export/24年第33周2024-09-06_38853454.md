# 24年第33周2024-09-06

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 8月13上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 8月13上线 17. 微商城：   - 预计7月初进入开发阶段   - 8月22上线 18. 预计7月初进入开发阶段 19. 8月22上线 20. 美团：   - 7月29号开始开发   - 8月16号提测   - 预计9月中旬上线 21. 7月29号开始开发 22. 8月16号提测 23. 预计9月中旬上线 24. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 25. 7月29号开始开发 26. 预计8月19号提测 | 1. 配送：   1. 消息回调-100%   2. 接口对接-100%   3. 接口中台改造-100%   4. 27号提测   5. 测试中 - 30% 2. 消息回调-100% 3. 接口对接-100% 4. 接口中台改造-100% 5. 27号提测 6. 测试中 - 30% 7. 美团：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 测试中-70% 8. 消息回调-100% 9. 接口对接-100% 10. 订单中台接口替换-100% 11. 测试中-70% |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0%    3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 50% - 订单同步服务重构, 80% - 配送信息 更新 30% - 售后审核 plantUML 20% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫售后审核 志明    todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕30% 版本升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [心云开发支持](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgwwtYh9GKRfKBvwQ7ff?scode=AOsAFQcYAAcUR2EJBBAboAOAYLADg&tab=iv14v4) |  |  |  |  |
| 7 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 8 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 9 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.对接客服系统 | 9月 | 待测试 |  |
| 2.客服系统IM对接 | 9月 | 待开始 |  |
| 3.线上单对接 | 9月 | 测试中 |  |
| 4.慢病接口支持-按门店_sku es支持搜索 | 9月 | 测试中 |  |
| 5.医保对接历史原价对接改为促销 | 9月 | 未开始 |  |
| 6.处方单履约流程V2.1 |  | O2O测试中，下周四上线B2C测试中 |  |
| 7.店铺标签 |  | 已上线 |  |
| 8.O2O正单负单下账金额优化 |  | 测试中 新需求嵌入上线时间待定 |  |
| 9.最优配送费 |  | 测试中 下周二上线 |  |
| 10.网关优化，APISIX调研 | 8月01日过会讨论方案 | 测试中 |  |
| 11.V1.2.6 小程序购买链路优化需求 | 9月13上线第一期 | 测试中 |  |
| 12.集团B2C对接优化需求 | 9月 | 开发中 |  |
| 13.抖店B2C | 9月 | 测试中 |  |
| 14.诺和订单 | 9月 | 测试中 下周四上线 |  |
| 吉客云 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 1.广播模式mq迁移 | hydee-business-order（和O2O正单负单下账金额优化一起上） |  |
| 2.XXL-JOB迁移升级 | [https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e](https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e) |  |
| 3.ELK对接上线 | 交易生产已上线 |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1. 小程序购买链路优化需求-切换门店 开发提测2.B2C库存占用线上BUG3.ELK项目全部上线 4.基于会员的RocketMQ的springboot启动器升级 | **遗留问题** | **需求研发**小程序购买链路优化需求上线**技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 慢病需求配合测试,造数压测 2. 网关、线下单接入elk上线 3. 网关SDK验签、白名单过滤器上线 4. 订单同步服务代码重构 80% | **遗留问题**  **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：** 5**d**1.B2C 作业流程优化以及历史数据清理。2.诺和订单改造3.配送迁移bug 修改。 | **遗留问题****配送接口整理至文档** **风险问题** | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：5d**1. 抖店B2C-测试中，下周二上线 2. 退款审核-plantUML绘制 3. 支持商品中台缓存门店数据 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5d**1.O2O最优配送费提测2.B2C订单效率优化(赠品链路优化)提测3.WMS推送bug处理4.拼多多、京东拆分后金额负数处理 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d** 1. 聚水潭 抖店订单堆积无法进WMS问题处理。 已完成 2. B2C 切店订单运维处理。 3. B2C 效率优化 已提测 4. h3-pay-finace、h3-pay-core xxl-job 升级。 无法升级H3项目 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 店铺标签上线 2. B2C新增下账单同步ES晚间定时任务 （待上线） 3. 抖店订单未发货部分退款后未创建正单问题处理 4. B2C京东正单取消并退款后退款单状态不是审核通过处理 5. B2C订单运维 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 网关优化   1. 内存优化   2. 组件优化   3. 网关合并 2. 内存优化 3. 组件优化 4. 网关合并 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |