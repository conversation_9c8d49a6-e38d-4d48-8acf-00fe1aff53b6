# 【20250312】 微商城购物车切换交易中台

### 一 、ydjia-merchant-customer 相关接口整理

 1. 获取购物车商品 /_get 

 请求参数： 

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| merCode | String | 商户编码，不可为空 |
| storeId | String | 门店ID |
| userId | String | 用户ID，隐藏字段 |
| clientType | String | 客户端来源，隐藏字段 |
| loginUser | LoginUserDTO | 登录会员信息，隐藏字段 |
| prescriptionDrugComplianceVersion | String | 处方药合规小程序版本，header中前端传参，版本号，隐藏字段 |
| appletAppId | String | 小程序appId，隐藏字段 |
| collectivizationFlag | Boolean | 是否要走集团化流程，隐藏字段 |
| deliveryType | Integer | 配送方式：1-门店配送，2-快递配送 |
| longitude | String | 经度 |
| latitude | String | 纬度 |
| currentStoreId | String | 当前门店ID |


 返回参数：

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| storeCommodityList | List<CartCommodityStoreDTO> | 门店商品集合 |
| chooseCommodityCount | Integer | 勾选商品数量 |
| reducePrice | BigDecimal | 预估优惠价格 |
| totalPrice | BigDecimal | 商品总价 |
| beforePrice | BigDecimal | 商品优惠前总价 |
| predThriftRespDTO | MemberVipPredThriftRespDTO | plus商品预计优惠信息 |


MemberVipPredThriftRespDTO:

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| couponThrift | BigDecimal | 优惠券节省金额 |
| plusDiscountThrift | BigDecimal | 优享折扣节省金额 |
| vipThrift | BigDecimal | 会员价优惠 |
| totalThrift | BigDecimal | 累计节省金额 |
| estimateSaveAmount | BigDecimal | 预计共节省金额（已废弃，仅为了兼容返回固定为0） |
| paidMemberFlag | Boolean | 是否为付费会员：true-是、null或false-否 |
| plusDiscountRate | BigDecimal | 付费会员折上折 |


CartCommodityStoreDTO：

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| merCode | String | 商户编码 |
| storeId | String | 门店ID |
| center | Boolean | 是否中心店：1-中心店，0-非中心店 |
| storeName | String | 门店名称 |
| isValid | Integer | 是否有效（未明确用途，需补充备注） |
| commodities | List<CartCommodityRespDTO> | 门店商品列表 |
| chooseCommodityCount | Integer | 门店-勾选商品数量 |
| reducePrice | BigDecimal | 门店-预估优惠价格 |
| totalPrice | BigDecimal | 门店-商品总价 |
| beforePrice | BigDecimal | 门店-商品优惠前总价 |
| isStoreHasPharmacist | Boolean | 门店是否配置在线审方药师 |
| hasPharmacistOnline | Boolean | 门店是否有在线药师（药事云） |
| hasPharmacist | Boolean | 门店是否有在线药师（药事云、小蜜兼容后通用字段） |
| spCode | String | 供应商编码 |
| spName | String | 供应商名称 |
| centerStoreId | String | 旗舰店ID |
| couponNotReceived | Boolean | 最优优惠券是否未领：true表示未领，结算时需要触发领券，默认值为 `Boolean.FALSE` |
| couponActivityId | Integer | 优惠券活动ID |
| couponId | Integer | 优惠券id |
| initialDeliveryPrice | BigDecimal | 门店起送价 |
| distance | String | 门店距离 |
| isCurrentStore | Integer | 是否为当前门店：0-否，1-是 |
| isDistribution | Integer | 是否支持门店配送：0-否，1-是 |
| isShipStore | Integer | 是否旗舰店：0-否，1-是 |


CartCommodityRespDTO：

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| commodityId | String | 商品ID，不可为空 |
| specId | String | 规格ID，不可为空 |
| merCode | String | 商户编码，不可为空 |
| spCode | String | 供应商编码 |
| storeId | String | 门店ID，不可为空 |
| storeName | String | 门店名称 |
| count | Integer | 数量，不可为空，须大于1 |
| limitNum | Integer | 限购数量 |
| addTime | Long | 添加时间，隐藏字段 |
| status | Integer | 是否有效 |
| choseFlag | Integer | 是否选中(1：是；0：否)，默认值为0 |
| activityId | String | 满减活动ID，用于购物车前端匹配同一个满减活动，设置活动信息 |
| displayName | String | 展示名称 |
| specName | String | 规格名称 |
| price | BigDecimal | 价格 |
| beforePrice | BigDecimal | 活动前价格 |
| erpCode | String | erpCode |
| brandName | String | 品牌，隐藏字段 |
| commodityName | String | 商品名 |
| commodityType | Integer | 商品类型（1：普通商品，2：组合商品，3：赠品） |
| isVirtual | Integer | 发货类型(0：实物发货，1：虚拟核销) |
| drugType | Integer | 药品类型(0：甲类OTC，1:处方药，2：乙类OTC，3：非处方药) |
| picUrl | String | 规格图片 |
| mainPic | String | 商品主图 |
| mPrice | BigDecimal | 标价 |
| assemblePrice | BigDecimal | 组合商品价格 |
| stock | Integer | 库存 |
| weight | Integer | 重量 |
| discount | BigDecimal | 折扣/减价 |
| freePostFee | Boolean | 是否免运费: true-是、false-否 |
| specSkuList | List<CommoditySpecSku> | 规格对应SKU键值对 |
| activityDiscountAmont | BigDecimal | 优惠分摊金额满减单规格上减免的金额 |
| pmtProductType | String | 促销商品类型N.正品G.赠品R.换购商品 |
| sourceChannelType | Integer | 渠道类型 |
| sourceChannelId | String | 渠道ID |
| needId | Integer | 是否需要用药人信息，0-不需要，1-需要 |
| firstTypeId | String | 所属一级分类ID |
| typeId | String | 三级分类id，优惠券核销需要 |
| commodityLevelActivities | List<ActivityDTO> | 单品级活动，包括特惠活动 |
| orderLevelActivities | List<ActivityDTO> | 订单级活动，包括满减、加价购等 |
| sharedStock | Integer | 共享库存 |
| sharedStockDTO | SharedStockQueryRespDTO | 商品共享仓信息：机构ID、编码、stock等 |
| distributionDay | Integer | 仓库配送时长 |
| origin | Integer | 商品来源，1-海典标准库，2-商家自定义，3-云货架 |
| windowDiagramMode | Integer | 橱窗图模式：0-营销模式，1-促销模式（无橱窗图数据） |
| windowDiagramUrl | String | 活动橱窗图链接 |
| epidemicRegistration | Integer | 疫情管控商品，0：否，1：是 |
| needHealthCode | Integer | 需要校验健康码，0：否，1：是 |
| medicalInsuranceCode | String | 商品医保编码 |
| useCoupon | Boolean | 是否使用优惠券，包括满减、折扣，不包括现金券 |
| couponPayment | BigDecimal | 传优惠券接口的商品金额 |
| supplierReceiveMoney | Integer | 是否供应商收款，1-是,0-否 |
| deliveryChannelCode | String | 投放渠道Code |
| centerStoreId | String | 旗舰店ID |
| breakType | Integer | 拆分类型：0-无 1-参与限时优惠规格 2-被拆规格 |
| goodsSalesPrice | BigDecimal | 零售价：门店价、促销价（秒杀价、新人价）、会员活动价（超级会员日价、会员日价、会员价） |
| otherSalesPrice | BigDecimal | 其他价格：plus价即优享价 |
| goodsOriginPrice | BigDecimal | 原价：门店价或参考价 |
| couponSalesPrice | BigDecimal | 券后价 |
| priceTag | Integer | 价格场景标识：1（秒杀价）、2（秒杀价、plus价）、3（新人价）、4（新人价、plus价）、5（超级会员日）、6（超级会员日、plus价）、7（会员日）、8（会员日、plus价）、9（会员价）、10（plus价）、11（plus价、券后价）、12（券后价）、13（线下促销价）、14（线下促销价、plus价）、15（价格组价格）、16（价格组价格、券后价） |
| totalPrice | BigDecimal | 总价，隐藏字段 |
| totalCouponSalesPrice | BigDecimal | 券后价合计 |
| paidMemberFlag | Boolean | 是否为付费会员：true-是、null或false-否，隐藏字段 |
| pmtFlag | Boolean | 是否参与促销活动：true-参与，隐藏字段 |
| memberDayActivityFlag | Boolean | 是否参与会员日相关活动：true-参与，隐藏字段 |
| memberPriceActivityFlag | Boolean | 是否参与会员价活动：true-参与，隐藏字段 |
| hasVipDiscount | Boolean | 商品是否有plus优惠：true-有，null或false-没有，隐藏字段 |
| discountRate | BigDecimal | plus商品折扣率，隐藏字段 |
| hasCouponPrice | Boolean | 商品是否有券后价：true-有，null或false-没有，隐藏字段 |
| couponPriceDetailDTO | ProductDiscountDetailDTO | 券后价信息，隐藏字段 |
| couponNotReceived | Boolean | 最优优惠券是否未领：true表示未领，结算时需要触发领券 |
| couponActivityId | Integer | 优惠券活动ID |
| couponId | Integer | 优惠券id |
| pmtRuleSeckillDTO | PmtRuleSeckillDTO | 打标图展示规则-限时优惠 |
| pmtRuleMoreDiscountDTO | PmtRuleMoreDiscountDTO | 打标图展示规则-多买优惠 |
| depositPayAmount | BigDecimal | 定金 |
| finalPaymentAmount | BigDecimal | 尾款 |
| isCrossBorder | Integer | 是否跨境商品：0-否，1-是 |
| costPrice | BigDecimal | 商品成本价/供货价 |
| expiring | Integer | 效期品，0否，1是 |
| isDiscountPrice | Integer | 是否为专属折扣价 0-否,1-是，默认值为0 |
| isPromoteProducts | Integer | 是否为推广商品 0-否,1-是 |
| isMembershipPrice | Integer | 是否心钻价0-否,1-是 |
| vipDiscount | BigDecimal | 心钻会员价优惠 |
| vipDiscountPlus | BigDecimal | 心钻折上折优惠 |
| isVip | Integer | 是否为VIP 0-否,1-是，隐藏字段 |
| originPrice | BigDecimal | 商品原始价格，隐藏字段 |




获取购物车流程

true获取购物车信息falseautotoptrue4013

2. 加购商品 cn.hydee.ydjia.merchantcustomer.controller.ShoppingCartController#add

请求参数 CartCommodityDTO

| 字段名 | 字段类型 | 备注 |
| --- | --- | --- |
| `serialVersionUID` | `long` | 序列化版本ID |
| `commodityId` | `String` | 商品ID，不可为空 |
| `specId` | `String` | 规格ID，不可为空 |
| `displayName` | `String` | 展示名称 |
| `storeName` | `String` | 门店名称 |
| `count` | `Integer` | 数量，不可为空 |
| `limitNum` | `Integer` | 限购数量 |
| `drugType` | `Integer` | 药品类型(0：甲类OTC，1：处方药，2：乙类OTC，3：非处方药) |
| `addTime` | `Long` | 添加时间，隐藏字段 |
| `choseFlag` | `Integer` | 是否选中(1：是；0：否)，默认值为 `StatusEnums.ENABLING.getCode()` |
| `merCode` | `String` | 商户编码 |
| `storeId` | `String` | 门店ID |
| `userId` | `String` | 用户ID，隐藏字段 |
| `status` | `Integer` | 是否有效，默认值为 `StatusEnums.ENABLING.getCode()` |
| `beforeCount` | `Integer` | 下架前购物车数量 |
| `refreshCart` | `Boolean` | 是否刷新购物车，区分购物车操作，默认值为 `false` |
| `pmtProductType` | `String` | 商品类型(N.正品G.赠品R.换购商品)，默认值为 `PmtProductType.NORMAL.getCode()` |
| `activityId` | `String` | 主动选择活动ID，从活动聚合页加购需传入 |
| `partInActivities` | `List<String>` | 参与活动集合 |
| `sourceChannelType` | `Integer` | 渠道类型 |
| `sourceChannelId` | `String` | 渠道ID |
| `clientType` | `String` | 客户端来源，隐藏字段 |
| `loginUser` | `LoginUserDTO` | 登录会员信息，隐藏字段 |
| `deliveryChannelCode` | `String` | 投放渠道Code |
| `spCode` | `String` | 供应商编码，云仓商品传入 |
| `isB2c` | `Integer` | B2C共享仓商品 |
| `breakType` | `Integer` | 拆分类型：0-无 1-参与限时优惠规格 2-被拆规格，隐藏字段 |
| `originCount` | `Integer` | 拆分规格，实际在购物车数量，隐藏字段 |
| `cartPageAdd` | `Boolean` | 购物车页面加购操作，区分购物车增加数量、切换规格，隐藏字段 |
| `prescriptionDrugComplianceVersion` | `String` | 处方药合规小程序版本，header中前端传参，版本号，隐藏字段 |
| `isCartOperate` | `Integer` | 是否购物车加购操作，0：否，1：是 |
| `appletAppId` | `String` | 小程序appId，隐藏字段 |