# 【20241120】线下单迭代【已完成】

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3504

1. 线下单成本价处理,没有则处理为NULL
2. 线下单消息模型移除枚举字段。先发sdk给下游上线，下游上线没有问题后，我这边再发布
3. mongo添加过期时间。按需添加过期索引


项目:

- order-service
- order-sync
- order-atom-service 已上线


分支: feature-offline-order-20241120

升级SDK

  2 complete 已经构建release  

<dependency>
      <groupId>com.yxt.order.open.message</groupId>
      <artifactId>order-open-message</artifactId>
      <version>1.5.1-RELEASE</version>
    </dependency>
 
<dependency>
      <groupId>com.yxt.order.open.sdk</groupId>
      <artifactId>order-open-sdk</artifactId>
      <version>1.5.1-RELEASE</version>
</dependency>

内部SDK

  4 complete 已构建release  

<dependency>
  <groupId>com.yxt.order.atom.sdk</groupId>
  <artifactId>order-atom-sdk</artifactId>
  <version>1.8.0-SNAPSHOT</version>
</dependency>