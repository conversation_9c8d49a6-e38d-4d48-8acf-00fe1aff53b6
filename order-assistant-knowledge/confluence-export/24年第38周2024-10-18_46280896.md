# 24年第38周2024-10-18

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 8月13上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 8月13上线 17. 微商城：   - 预计7月初进入开发阶段   - 8月22上线 18. 预计7月初进入开发阶段 19. 8月22上线 20. 美团：   - 7月29号开始开发   - 8月16号提测   - 预计9月中旬上线 21. 7月29号开始开发 22. 8月16号提测 23. 预计9月中旬上线 24. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 25. 7月29号开始开发 26. 预计8月19号提测 27. 快手：   1. 9月27日开始开发   2. 预计10月12日提测 28. 9月27日开始开发 29. 预计10月12日提测 | 1. 配送：   1. 消息回调-100%   2. 接口对接-100%   3. 接口中台改造-100%   4. 27号提测   5. 2024/10/15上线   6. 目前切换了达达 2. 消息回调-100% 3. 接口对接-100% 4. 接口中台改造-100% 5. 27号提测 6. 2024/10/15上线 7. 目前切换了达达 8. 美团：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 已上线   - 待替换剩余网店回调 9. 消息回调-100% 10. 接口对接-100% 11. 订单中台接口替换-100% 12. 已上线 13. 待替换剩余网店回调 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务: 测试中  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0% 3.配送信息更新:  4.申请售后/售后服务:- 售后申请流程10% | - 拣货-拣货开发中 50% - 订单同步服务重构, （测试中） - 配送信息 更新 30% - 售后审核 10% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫售后审核 志明    todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕9月27日订单/商品已上线完毕 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | [心云开发支持](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgwwtYh9GKRfKBvwQ7ff?scode=AOsAFQcYAAcUR2EJBBAboAOAYLADg&tab=iv14v4) |  |  |  |  |
| 5 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 6 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 7 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 配送.net迁移 |  | 待切换美团骑手 顺丰 |  |
| 物流中台 |  | 开发中 |  |
| 快手平台对接 |  | 测试中 |  |
| 对接客服系统二期IM |  | 已提测 |  |
| 小程序购物车二期 |  | 已上线 |  |
| 内购订单支持处方药 |  | 待评审 |  |
| 对接客服系统一期 |  | 待上线 | 黎琳 |
| 测试工具-深度路由 |  | 已上线 |  |
| B2C售后单流程优化 |  | 待上线（下周二） |  |
| 测试工具-虚拟服务MVP |  | 已上线 |  |
| 京东快递 |  | 已上线 |  |
| HANA库数据迁移到归档库 |  | 迁移中10% |  |
| 线下单促销信息 |  | 待开发 |  |
| 员工推广2期 |  | 已上线 |  |
| B2C扫描发货流程（打印调整） |  | 测试中（下周二上线） |  |
| 付费会员-虚拟订单对接 |  | 设计方案中 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| XXL-JOB迁移升级 | H3待适配 |  |
| 网关优化 | 已上线（下周变更容器规格） |  |
| 订单重构一期 | O2O订单落库流程-测试中; |  |
| B2C中转平台对接监控 | 待开始 |  |
| Q3/Q4绩效合同填写 | 已完成 |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1. 门店仓B2C按单发货 开发\提测 2. B2C扣减及释放库存逻辑调整 开发\提测\上线 3. 付费会员评审 技术方案设计 | **遗留问题** | **需求研发**1. 付费会员 技术方案设计 开发 2. 门店仓B2C按单发货 上线 **技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 线下单数据迁移,本周完成8家公司数据迁移,其余公司完成分表数据统计 2. 线上单消息推送Bug修复，已上线 3. 重构分支同步master代码 4. 科传上线配合 | **遗留问题** **风险问题** | **需求研发**- 数据迁移 - 线下单促销信息落库 **技术建设** |  |
| 3 |  | **本周总工时：** 5**d1**1.快手打印 1day2.配送上线以及切换监控 调整 1day3.B2C 作业优化 3day | **遗留问题** **风险问题**元罡前端还在对接和调整，如果周五不能处理完成 考虑周末加班 | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：4.5d**1 美团线上运行观测2 线上问题修复：1. 美团自动接单优化 2. 到店自提不需要调用拣货完成接口 3. 美团拒绝退款 操作失败的问题 4. 京东到家-商家支付远距离运费不计算到商家优惠中 3 快手测试支持与bug修复4 美团回调消息MQ消费过慢问题处理 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d**1. 客服对接二期联调 2. 员工推广链路调整开发联调提测上线 3. 客服一期bug修复 4. 付费会员评审 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5d**1.京东快递上线2.线上bug修复 首单优惠订单退款明细生成错误3.接口中通开发(京东快递已开发80%，剩物流轨迹) | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. 支付中台微商城上线自动开启支付配置 未提测，产品确认最终需求 2. 物流中台开发 开发进度 30% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. B2C退货单与退款单流程优化 待上线 2. 线上订单处理 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. **虚拟服务**   1. **添加mq消息管理**   2. **支持动态body取值**   3. **body匹配器**   4. **支持动态header取值**   5. **mockKey支持动态取值拼接**   6. **上层路由host透传**   7. **支持http://* 匹配方式**   8. **mock数据日志**   9. **oriHost匹配器** 2. **添加mq消息管理** 3. **支持动态body取值** 4. **body匹配器** 5. **支持动态header取值** 6. **mockKey支持动态取值拼接** 7. **上层路由host透传** 8. **支持http://* 匹配方式** 9. **mock数据日志** 10. **oriHost匹配器** | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |