# SDK对接指南

SDK使用方法因为订单SDK移除了feign相关依赖,在使用订单SDK是需要自己写一个接口来继承相应的API. 示例:@FeignClient(value = OrderServiceName.value)
public interface OfflineOrderQueryApiFeign extends OfflineOrderQueryApi{

}
调用方使用时,使用自己Feign实例@Resource
private OfflineOrderQueryApiFeign offlineOrderQueryApiFeign;



开发和测试版本分别为dev-SNAPSHOT和test-SNAPSHOT

## 版本管理(按时间倒序)

| 版本 | 需求 | 依赖 | 是否已发布 | 日期 |
| --- | --- | --- | --- | --- |
| 2.4.1-RELEASE | [[ORDER-5940] 会员6月21日 购买薇诺娜 线上线下准实时订单数据](https://jira.hxyxt.com/browse/ORDER-5940) | - order-atom-sdk - order-types - order-common | Green已发布 | 20250619 |
| 2.3.0-RELEASE | 异业联盟 | - order-atom-sdk | Green已发布 | 20250619 |
| 2.2.0-RELEASE |  | - order-atom-sdk - order-open-message - order-open-sdk | Green已发布 | 20250619 |
| 2.1.0-RELEASE | 用药福利二期-门店订单查询新接口 | - order-types - order-common - order-open-sdk | Green已发布 | 20250617 |
| 2.0.0-RELEASE |  | - order-types - order-common - order-atom-sdk - order-atom-open-sdk - order-open-sdk - order-open-message | Green已发布 | 20250527 |
| 1.35.0-RELEASE | [【20250326】 V2.39 用药福利支持和订单标签系统](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65481960) | - order-types - order-common - order-atom-sdk - order-atom-open-sdk - order-open-sdk - order-open-message | Green已发布 | 20250522 |
| 1.34.0-RELEASE |  | - order-types | Green已发布 | 20250521 |
| 1.33.0-RELEASE | 归档库支持,有2种方式: 1. 按照单号路由 2.指定数据库 | - order-types | Green已发布 | 20250519 |
| 1.32.0-RELEASE |  | - order-atom-sdk - order-consistent-sdk | Green已发布 | 20250508 |
| 1.31.0-RELEASE |  | - order-types - order-atom-sdk - order-open-sdk | Green已发布 | 20250427 |
| 1.30.0-RELEASE | 【20250327】线下订单查询&线下订单数据同步异常监控 | - order-types - order-permission - order-atom-sdk - order-batch-sdk - order-open-sdk - order-open-message - incubation-yxt-import-export | Green已发布 | 20250424 |


## 外部

### order-open-sdk

服务名: com.yxt.order.open.sdk.OrderServiceName.value

  <artifactId>order-open-sdk</artifactId> 
  <groupId>com.yxt.order.open.sdk</groupId>

### order-open-message

不会注册到nacos,只是pojo

  <artifactId>order-open-message</artifactId> 
  <groupId>com.yxt.order.open.message</groupId>

### order-atom-open-sdk

服务名: com.yxt.order.atom.open.sdk.OrderAtomServiceName.value

  <artifactId>order-atom-open-sdk</artifactId> 
  <groupId>com.yxt.order.atom.open.sdk</groupId>

## 内部

### order-atom-sdk

服务名: com.yxt.order.atom.open.sdk.OrderAtomServiceName.value

 <groupId>com.yxt.order.atom.sdk</groupId>
 <artifactId>order-atom-sdk</artifactId>

### order-common

 <groupId>com.yxt.order.common</groupId>
 <artifactId>order-common</artifactId>

### order-types

 <groupId>com.yxt.order.types</groupId>
 <artifactId>order-types</artifactId>

### order-domain-sdk

服务名: com.yxt.domain.order.BusinessOrderServiceName.value

 <groupId>com.yxt.domain.order</groupId>
 <artifactId>order-domain-sdk</artifactId>

### order-batch-sdk

服务名: com.yxt.order.batch.sdk.OrderBatchServiceName.value

 <groupId>com.yxt.order.batch.sdk</groupId>
 <artifactId>order-batch-sdk</artifactId>

**incubation-yxt-import-export**

订单导入导出

 <groupId>com.yxt.imex</groupId>
 <artifactId>incubation-yxt-import-export</artifactId>