# 【20240228】经营分析问题处理【已完成】

-- 开启或关闭表的动态分区
ALTER TABLE table_name SET("dynamic_partition.enable"="false");
ALTER TABLE table_name SET("dynamic_partition.enable"="true");

-- 查看表分区
SHOW PARTITIONS FROM oms_order_bill_info_dev;
SHOW PARTITIONS FROM oms_order_pay_info_dev;

-- 手动增加分区
ALTER TABLE xxxxx  ADD PARTITION p20240101 VALUES [('2024-01-01'), ('2024-01-02'));

-- 查看连接kafka的情况
SHOW ALL ROUTINE LOAD;

要开启自动分区,数据才会进来。如果要刷历史数据,要关闭自动分区。刷完之后再打开


select * from order_info where organization_code = 'C002' and service_mode = 'O2O' and order_state not in (101,102) order by created desc limit 10;
select order_no,created from order_info where order_no = 1789396844090305029 and service_mode = 'O2O' ;
select * from order_info where order_no = 1789396844090305029 and service_mode = 'O2O' ;

select * from order_detail where order_no = 1789396844090305029;

select id,commodity_average_price from erp_bill_info where order_no = 1789396844090305029 and commodity_average_price = 0;

select commodity_cost_total from doris_order_pay_info where order_no = 1789396844090305029;
select commodity_cost_total from doris_order_bill_info where order_no = 1789396844090305029;


select * from order_info where integral_flag = null limit 10;




-- topic-order-pay: topic_business_cloud_order_pay_pro
-- topic-order-bill: topic_business_cloud_order_bill_pro

select id,commodity_average_price from erp_bill_info where order_no in (1789472647893959936,1789396844090305029);
select * from doris_order_pay_info where order_no in (1789472647893959936,1789396844090305029);
select * from doris_order_bill_info where order_no in (1789472647893959936,1789396844090305029);
-- 恢复
-- 先开启动态分区
update doris_order_bill_info set commodity_cost_total = 0  where order_no in (1789472647893959936,1789396844090305029);

查询需要刷数的店铺及订单数

select count(1) c, oi.organization_code organ from order_info oi left join inner_store_dictionary isd on oi.organization_code = isd.organization_code
where oi.service_mode = 'O2O' and oi.created >= '2024-01-01 00:00:00' GROUP BY organ having c > 0 order by c desc 

项目及分支:

hydee-business-order release-flush-averagePirce

### 修改点:

1. 替换了成本价接口

2. 新增了刷数接口

刷数步骤：

  4 complete 需要商品先上线,数据先刷   10 complete 订单已上线   8 complete 因为要刷历史数据,所以需要关闭动态分区,刷完之后再打开   5 complete 先刷商品成本价 /flushCommodityAveragePrice   6 complete 再刷 cn.hydee.middle.business.order.doris.controller.OrderReportStatisticsController#pushMessage2Kafka  

---

doris_order_pay_info cancal消息,只有commodity_cost_total字段；doris_order_bill_info 有commodity_cost_total和commodity_bill_total字段

true{
    "data": [
        {
            "id": "7003768",
            "created_day": "2024-02-01",
            "created_hour": "8",
            "mer_code": "500001",
            "organization_code": "M180",
            "third_platform_code": "27",
            "order_state": "100",
            "online_store_code": "M180",
            "order_no": "1789654408950799109",
            "third_order_no": "600923552889266660",
            "organization_name": "一心堂陵水海韵广场分店",
            "online_store_name": "一心堂（陵水海韵广场店）",
            "created": "2024-02-01 08:30:44",
            "total_amount": "16.0",
            "buyer_actual_amount": "16.50",
            "merchant_actual_amount": "10.40",
            "merchant_delivery_fee": "0.0",
            "platform_delivery_fee": "4.0",
            "merchant_pack_fee": "0.0",
            "platform_pack_fee": "0.50",
            "brokerage_amount": "1.60",
            "merchant_discount_sum": "4.0",
            "platform_discount": "0.0",
            "discount_fee_dtl": "0.0",
            "r_total_food_amount": "0.0",
            "r_shop_refund": "0.0",
            "r_consumer_refund": "0.0",
            "r_merchant_refund_post_fee": "0.0",
            "r_merchant_refund_pack_fee": "0.0",
            "r_platform_refund_pack_fee": "0.0",
            "r_platform_refund_delivery_fee": "0.0",
            "r_shop_discount_refund": "0.0",
            "r_fee_refund": "0.0",
            "r_detail_discount_amount": "0.0",
            "r_platform_discount_refund": "0.0",
            "commodity_cost_total": "10.36000",
            "tag_prescription": "0",
            "tag_integral": "0",
            "tag_medical_insurance": "0",
            "tag_appoint": "0",
            "tag_new_customer": "0",
            "tag_transfer_delivery": "0",
            "tag_delivery_type": "0",
            "tag_pay_type": "0",
            "tag_change_store": "0",
            "tag_post_type": "12",
            "order_num": "1"
        }
    ],
    "database": "dscloud",
    "es": 1706772185000,
    "id": 8048557,
    "isDdl": false,
    "mysqlType": {
        "id": "bigint",
        "created_day": "date",
        "created_hour": "varchar(7)",
        "mer_code": "varchar(20)",
        "organization_code": "varchar(40)",
        "third_platform_code": "varchar(20)",
        "order_state": "tinyint",
        "online_store_code": "varchar(60)",
        "order_no": "bigint",
        "third_order_no": "varchar(100)",
        "organization_name": "varchar(128)",
        "online_store_name": "varchar(128)",
        "created": "datetime",
        "total_amount": "decimal(16,2)",
        "buyer_actual_amount": "decimal(16,2)",
        "merchant_actual_amount": "decimal(16,2)",
        "merchant_delivery_fee": "decimal(16,2)",
        "platform_delivery_fee": "decimal(16,2)",
        "merchant_pack_fee": "decimal(16,2)",
        "platform_pack_fee": "decimal(16,2)",
        "brokerage_amount": "decimal(16,2)",
        "merchant_discount_sum": "decimal(16,2)",
        "platform_discount": "decimal(16,2)",
        "discount_fee_dtl": "decimal(16,2)",
        "r_total_food_amount": "decimal(16,2)",
        "r_shop_refund": "decimal(16,2)",
        "r_consumer_refund": "decimal(16,2)",
        "r_merchant_refund_post_fee": "decimal(16,2)",
        "r_merchant_refund_pack_fee": "decimal(16,2)",
        "r_platform_refund_pack_fee": "decimal(16,2)",
        "r_platform_refund_delivery_fee": "decimal(16,2)",
        "r_shop_discount_refund": "decimal(16,2)",
        "r_fee_refund": "decimal(16,2)",
        "r_detail_discount_amount": "decimal(16,2)",
        "r_platform_discount_refund": "decimal(16,2)",
        "commodity_cost_total": "decimal(19,5)",
        "tag_prescription": "tinyint",
        "tag_integral": "varchar(1)",
        "tag_medical_insurance": "tinyint",
        "tag_appoint": "tinyint(1)",
        "tag_new_customer": "varchar(1)",
        "tag_transfer_delivery": "tinyint(1)",
        "tag_delivery_type": "tinyint",
        "tag_pay_type": "int",
        "tag_change_store": "int",
        "tag_post_type": "int",
        "order_num": "int"
    },
    "old": [
        {
            "order_state": "30"
        }
    ],
    "pkNames": [
        "id"
    ],
    "sql": "",
    "sqlType": {
        "id": -5,
        "created_day": 91,
        "created_hour": 12,
        "mer_code": 12,
        "organization_code": 12,
        "third_platform_code": 12,
        "order_state": -6,
        "online_store_code": 12,
        "order_no": -5,
        "third_order_no": 12,
        "organization_name": 12,
        "online_store_name": 12,
        "created": 93,
        "total_amount": 3,
        "buyer_actual_amount": 3,
        "merchant_actual_amount": 3,
        "merchant_delivery_fee": 3,
        "platform_delivery_fee": 3,
        "merchant_pack_fee": 3,
        "platform_pack_fee": 3,
        "brokerage_amount": 3,
        "merchant_discount_sum": 3,
        "platform_discount": 3,
        "discount_fee_dtl": 3,
        "r_total_food_amount": 3,
        "r_shop_refund": 3,
        "r_consumer_refund": 3,
        "r_merchant_refund_post_fee": 3,
        "r_merchant_refund_pack_fee": 3,
        "r_platform_refund_pack_fee": 3,
        "r_platform_refund_delivery_fee": 3,
        "r_shop_discount_refund": 3,
        "r_fee_refund": 3,
        "r_detail_discount_amount": 3,
        "r_platform_discount_refund": 3,
        "commodity_cost_total": 3,
        "tag_prescription": -6,
        "tag_integral": 12,
        "tag_medical_insurance": -6,
        "tag_appoint": -6,
        "tag_new_customer": 12,
        "tag_transfer_delivery": -6,
        "tag_delivery_type": -6,
        "tag_pay_type": 4,
        "tag_change_store": 4,
        "tag_post_type": 4,
        "order_num": 4
    },
    "table": "doris_order_pay_info",
    "ts": 1706772185809,
    "type": "UPDATE"
}