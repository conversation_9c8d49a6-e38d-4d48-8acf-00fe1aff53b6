# 24年第28周2024-08-02

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 预计8月初上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 预计8月初上线 17. 微商城：   - 预计7月初进入开发阶段 18. 预计7月初进入开发阶段 19. 美团：   - 7月29号开始开发   - 预计8月19号提测   - 预计9月中旬上线 20. 7月29号开始开发 21. 预计8月19号提测 22. 预计9月中旬上线 23. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 24. 7月29号开始开发 25. 预计8月19号提测 | 1. 京东到家：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 测试中-80% 2. 消息回调-100% 3. 接口对接-100% 4. 订单中台接口替换-100% 5. 测试中-80% 6. 微商城：   1. 消息回调 -100%   2. 接口对接-100%   3. 接口中台修改–重用之前的   4. 周四提测 7. 消息回调 -100% 8. 接口对接-100% 9. 接口中台修改–重用之前的 10. 周四提测 11. 配送：   1. 消息回调-20%   2. 接口对接-25%   3. 接口中台改造-0% 12. 消息回调-20% 13. 接口对接-25% 14. 接口中台改造-0% |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0%    3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 30% - 订单同步服务重构, 55% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫   todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕30% 版本升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 7 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 8 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 9 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 10 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.B2C退款流程优化 |  | 已上线 |  |
| 2.对接客服系统 |  | 联调阶段 |  |
| 3.线下单迁移 |  | 海典具备上线条件科传8月9日联调 |  |
| 4.线上单对接 |  |  |
| 5.美团电子围栏兼容距离限制 |  | 08/06上线 |  |
| 6.购物车小优化 |  | 测试中 |  |
| 7.购物车捆绑销售 |  | 已上线 |  |
| 8.B2C作业V2.1 |  | 开发中 |  |
| 9.O2O作业V2.1 |  | 开发中 |  |
| 10.处方单履约流程V2.1 |  | 待评审 |  |
| 11.B2C库存占用 |  | 评审完毕 |  |
| 12.店铺标签 |  | 开发中 |  |
| 13.O2O正单负单下账金额优化 |  | 开发中 |  |
| 吉客云 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 | hydee-business-order（广播方式，发送语音播报消息，一个已经修改）待验证 |  |
| XXL-JOB迁移升级 | [https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e](https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e) |  |
| Q3绩效 | [https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i) | 全部 |
| [Redis迁移](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38831735) | 梳理完pay、middle-order服务的redis key |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d 1.O2O作业组合商品金额修改 2.请货单BUG 3.修复O2O占用库存 4.B2C库存占用优化V2** | **遗留问题** | **需求研发** **技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 线下订单新增逻辑开发&配合测试 3.5d   1. 测试环境canal多次异常问题排查   2. 迁移云南生产数据&数据问题排查&迁移脚本限速（测试库多次重启） 2. 测试环境canal多次异常问题排查 3. 迁移云南生产数据&数据问题排查&迁移脚本限速（测试库多次重启） 4. 订单中台消息与下游对齐沟通&新增部分字段开发 0.5d 5. 订单同步服务重构开发,进度60% 1d | **遗留问题**  **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：** **5d** 1.配送服务对接 4 day（15%）。2.线上问题处理 1day .（天猫打印、B2C 订单） | **遗留问题** **风险问题** | **需求研发****1.调查京东B2C 和京东到家，隐私信息改造范围。****2.配送迁移****技术建设****** |  |
| 4 |  | **本周总工时：5d**1. 京东到家bug修复 2. 线上饿了么日志观测 3. 美团SDK开发+回调消息对接 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d**1. 购物车优化联调 2. 组合商品金额修改 3. 购物车捆绑销售bug修复 上线 4. 线上问题处理 5. B2C库存占用 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5d**1.店铺增加包装费下账2.订单导出增加抖音渠道，筛选条件增加配送方式3.O2O批量处理因库存不足下账失败的数据4.中通快递绑定开发者账号bug修复5.下账单的优惠分摊取值优化 | **遗留问题**1.店铺包装费下账处理2.回调物流平台失败增加重试**风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. B2C退款流程优化上线及上线后异常问题、订单下账问题处理。 2. 电子围栏优化 已提测 3. xxljob迁移 buiness-order-web 及 buiness-order-b2c-third 迁移完成 下周二上线。 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：4d**1. B2C退款优化需求上线 2. B2C退款优化–淘宝未提出问题解决（测试中） 3. 四川1000家门店Pos切换 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 智能客服接口开发 2. B2C 快递面单配置错误跟踪 3. 订单测试优化技术方案编写 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |