# 2024-10-30 checkList  打印升级和京东物流

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  | k8s-hydee-business-order |  |  |  |  |  |  |  |  |  |
| 京东无界的京东快递支持 | hydee-business-order-web[hydee-business-order-b2c-third](http://jenkins.hxyxt.com/job/k8s-hydee-business-order-web/search/?q=k8s-hydee-business-order-b2c-third)[hydee-oms-logistic](http://jenkins.hxyxt.com/job/hydee-oms-logistic/search/?q=hydee-oms-logistic) |  | ORDER-3239 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| dscloud | ``` alter table print_version_manage add  column client_type int default 0 comment '客户端打印程序类型 0:020 1:b2c' ``` | 增加打印机类型 |
| dscloud | -- 020 升级 INSERT INTO dscloud.print_version_manage (version, version_notes, force_update_flag, exe_file_for_three_five,  zip_file_for_three_five, exe_file_for_four_zero, zip_file_for_four_zero,  on_line_flag, exe_file_for_four_five_two,  zip_file_for_four_five_two,client_type) VALUES ('3.7.8', '最新版本20241107', 1,  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_normal.exe](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_normal.exe)',  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net35_O2O.zip](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net35_O2O.zip)',  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_net4.exe](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_net4.exe)',  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net40_O2O.zip](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net40_O2O.zip)', 1,  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_net452.exe](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/O2O.XinYunPrintServer_V3.7.8_net452.exe)',  '[http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net452_O2O.zip](http://sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/net452_O2O.zip)',  0); |  |
| dscloud | -- 更新B2C 下载链接``` update code_value set value_desc ='//sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/B2C.XinYunPrintServer_V3.8.1_net4.exe' where type='ZL_LINK'; ``` |  |
| dscloud | -- 京东标准模板 76x130``` INSERT INTO dscloud.logistic_stdtemplates (standard_template_name, standard_waybill_type, standard_template_url, wp_code, print_code, custom_area_url, custom_area_id, view_url, source, encrypt_flag) VALUES ('京东快递标准模板76x130', 1, 'https://template-content.jd.com/template-oss?tempCode=jdkd76x130', 'JD', '3004', 'https://template-content.jd.com/template-open?templateCode=yxtjd2024110517', 1, '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', 1, 0); ``` |  |
| dscloud | -- 京东无界下京东配置配置更新INSERT INTO dscloud.oms_custom_attributes (`key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, default_value, value_type) VALUES ( 'STORE_EXPRESS_MERCHANT_3004_JD', 'provinceCityDistrict', '发货省-市-区', '请输入发货地址', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}','', 4); INSERT INTO dscloud.oms_custom_attributes (`key`, column_code, column_name, placeholder, type, seq, require_flag, status, value_type) VALUES ('STORE_EXPRESS_MERCHANT_3004_JD', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, 1); INSERT INTO dscloud.oms_custom_attributes (`key`, column_code, column_name, placeholder, type, seq, require_flag, status, default_value, value_type) VALUES ('STORE_EXPRESS_MERCHANT_3004_JD', 'customerCode', '客户编码', '请输入客户编码', 'input', 6, 1, 1, '27K1234912', 1); update oms_custom_attributes set status = 0 where id = 260; update oms_custom_attributes set status = 0 where id = 261; |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| hydee-oms-logistic | [Apollo配置中心](https://prod-apollo-ui.hxyxt.com/config.html#/appid=hydee-oms-logistic) |  | 更新京东快递对接方案编码 |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |