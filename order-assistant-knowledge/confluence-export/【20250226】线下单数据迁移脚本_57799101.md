# 【20250226】线下单数据迁移脚本

- Green第一阶段已完成 XF_CREATETIME>='2024-07-31 00:00:00' AND XF_CREATETIME<'2025-01-16 00:00:00'
- 第二阶段待开始


| 字段 |  |  | 数据归档 |
| --- | --- | --- | --- |
| migration | 0 | 未迁移/未成功迁移的 |  |
| 1 | 迁移成功 |  |
| 2 | 校验已存在,不迁移 如果已存在还记录了单号,原因是: |  |
| 3 | 线上订单,不迁移 |  |
| 4 | 明细不存在 |  |
| 5 | 支付数据为空,不处理。  已经咨询过赵兴堂,因为不是S0开头的,数据可能由于某种原因修改过。打标记录即可与订单数据对应不上,缺少一条数据 | hana_migration_error_pay_data_empty_20250306 |


删除表

|  |  |  |
| --- | --- | --- |
| deleted_datadeleted_data_2 | 迁移重复数据,如后续需要可以从归档表查 | 正单 |
| deleted_data_3 | 退单 |
| deleted_data_4 | 迁移重复数据,如后续需要可以从归档表查 | 科传版本问题导致24年11月29号之前的订单时间和订单时间不一致.放到这个删除分表里，做溯源 |


基于此:  继续优化

原始数据迁移: 

  2 complete 合并分支到master代码   5 complete 执行线上sql   6 complete 发布  

**项目:**

order-atom-service

**分支:**

migration-step-1-archive

**表:hana_migration_error\hana_migration**

CREATE TABLE `hana_migration` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `migrate_sort` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '迁移顺序 ORDER\\REFUND',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司名',
  `target_schema` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库schema(小写,代码也重写了getter)',
  `migration_start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移的开始时间(发送MQ)',
  `migration_end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移的结束时间(发送MQ)',
  `send_mq_count` bigint DEFAULT NULL COMMENT '成功发送到迁移MQ队列的数量',
  `migration_total_count` bigint DEFAULT NULL COMMENT '迁移总数',
  `migration_total_count_success` bigint DEFAULT NULL COMMENT '成功迁移总数',
  `on_off` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '启用状态 true,false',
  `cursor_id` bigint DEFAULT '0' COMMENT '当前游标ID(归档库订单)',
  `migration_result` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移结果 true,false',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `hana_migration_error` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `hana_migration_id` bigint NOT NULL COMMENT 'hana_migration表id',
  `target_schema` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库schema(小写)',
  `condition_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '条件',
  `entry_retry_queue` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否进入重试队列 true,false',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误消息',
  `error_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '错误类型: SEND_MQ_FAILED/FROM_ARCHIVE_TO_OFFLINE_ORDER',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_hana_migration_id` (`hana_migration_id`) USING BTREE,
  KEY `idx_error_type` (`error_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; 

**xxl-job:**

发送到迁移mq失败的,重新发送: migrationHanaErrorDataHandler

迁移进度刷新 migrationHanaDataRefreshProcessHandler

**数据核对:**

ONLINE_ORDER+EXISTS+SUCCESS+count(hana_migration_error by id)

select c.count 迁移数,
        CASE
        WHEN migration = 0 THEN '未迁移成功(0)'
        WHEN migration = 1 THEN '迁移成功(1)'
        WHEN migration = 2 THEN '校验已存在,不迁移(2)'
        WHEN migration = 3 THEN '线上订单,不迁移(3)'
      WHEN migration = 4 THEN '明细不存在(4)'
      ELSE '未知状态'
    END AS 各状态迁移进度
    from (
select count(1) count ,migration from xf_transsalestotal_ynhx_data01 where  XF_CREATETIME>="2024-07-29 00:00:00" AND XF_CREATETIME<"2024-07-29 10:00:00" and XF_SELLINGAMOUNT >= 0 GROUP BY migration
) as c order by migration asc;

**容量评估:**

线下单，按照目前18张表，没个表字段都使用切都占满,一条记录大概占用20KB

具体见评估计算:

|  | DB | ES 分片副本数量（默认配置下会有1个副本，实际占用将翻倍） |
| --- | --- | --- |
| 第一阶段2024-08-01 00:00:00 到 2025-01-15 12:00:00 |  |  |
| 两年的数据占用 |  |  |
| 全部迁移完成 |  |  |


---

迁移正单任务配置

sqltruetrueINSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2020_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2020_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2020_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2020_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2021_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2021_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2021_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2021_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2022_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2022_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2022_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2022_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2023_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2023_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2023_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2023_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2024_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2024_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '四川', 'cdhx_users_2024_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '重庆', 'cqhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2020_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2020_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2020_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2020_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2021_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2021_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2021_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2021_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2022_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2022_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2022_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2022_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2023_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2023_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2023_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2023_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2024_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2024_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2024_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '广西', 'gxhx_users_2024_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '贵州', 'gzhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '河南', 'henhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '河南', 'henhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '河南康健', 'hennyhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '河南康健', 'hennyhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '海南', 'hnhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '攀枝花', 'schx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '上海', 'shhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '上海', 'shhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西广生', 'sxgshx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西广生', 'sxgshx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '山西', 'sxhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '天津', 'tjhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '天津', 'tjhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '天津乾昌', 'tjqchx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '天津乾昌', 'tjqchx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_20092013', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2021_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2022_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2023_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '云南', 'ynhx_data01_2024_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '中药科技', 'zyhx_users', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('ORDER', '中药科技', 'zyhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');



迁移退单任务配置:

sqltruetrueINSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2020_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2020_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2020_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2020_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2021_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2021_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2021_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2021_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2022_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2022_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2022_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2022_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2023_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2023_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2023_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2023_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2024_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2024_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '四川', 'cdhx_users_2024_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '重庆', 'cqhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2020_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2020_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2020_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2020_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2021_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2021_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2021_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2021_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2022_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2022_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2022_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2022_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2023_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2023_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2023_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2023_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2024_q1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2024_q2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2024_q3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '广西', 'gxhx_users_2024_q4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '贵州', 'gzhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '河南', 'henhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '河南', 'henhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '河南康健', 'hennyhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '河南康健', 'hennyhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '海南', 'hnhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2018', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2019', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '攀枝花', 'schx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '上海', 'shhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '上海', 'shhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西广生', 'sxgshx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西广生', 'sxgshx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_2020', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_2021', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_2022', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_2023', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_2024', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '山西', 'sxhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '天津', 'tjhx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '天津', 'tjhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '天津乾昌', 'tjqchx_data01', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '天津乾昌', 'tjqchx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_20092013', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2021_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2022_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_11', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_12', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2023_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_1', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_10', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_2', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_3', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_4', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_5', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_6', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_7', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_8', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '云南', 'ynhx_data01_2024_9', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '中药科技', 'zyhx_users', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');
INSERT INTO `dscloud_offline`.`hana_migration`(`migrate_sort`, `company_name`, `target_schema`, `migration_start_time`, `migration_end_time`, `send_mq_count`, `migration_total_count`, `migration_total_count_success`, `on_off`, `cursor_id`, `migration_result`, `note`, `updated_time`) VALUES ('REFUND', '中药科技', 'zyhx_users_20241007_2025011512', '2024-07-31 00:00:00', '2025-01-16 00:00:00', 0, 0, 0, 'false', 0, '', '', '2025-02-27 14:13:39');