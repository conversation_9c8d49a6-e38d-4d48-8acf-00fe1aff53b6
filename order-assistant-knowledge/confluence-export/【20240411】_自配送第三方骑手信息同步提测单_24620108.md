# 【20240411】 自配送第三方骑手信息同步提测单

| **【自配送第三方骑手信息同步】提测申请** |  |
| **提测需求** | [【20240411】 自配送第三方骑手信息同步 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24620104) |  |
| **相关人员** | **PM** |  |  |
| **UI** | 　 |  |
| **RD** |  |  |
| **FE** |  |  |
| **QA** |  |  |
| **提测Git分支** | **前端服务** |  |  |
| **后端服务** | ds-service-mtbusiness-order the3platform | ```  ``` |
| **开发详情** | **配置变更** | ``` nacos 配置 RouteApiConfig ```{  "ModuleId": "2005",  "MethodName": "hems.rider.h5url.get",  "ApiPath": "api/FengNiaoJPRider/GetLocationH5Url"  },  {  "ModuleId": "2001",  "MethodName": "hems.rider.h5url.get",  "ApiPath": "api/FengNiaoRider/GetLocationH5Url"  },  {  "ModuleId": "2004",  "MethodName": "hems.rider.h5url.get",  "ApiPath": "api/ShunFengRider/GetLocationH5Url"  },  {  "ModuleId": "2002",  "MethodName": "hems.rider.h5url.get",  "ApiPath": "api/MeiTuanRider/GetLocationH5Url"  },  {  "ModuleId": "2003",  "MethodName": "hems.rider.h5url.get",  "ApiPath": "api/DadaRider/GetLocationH5Url"  } |  |
| **SQL变更** | insert into hydee_aurora_basic.t_sys_routes_api(apiid,moduleid,methodname,apipath,remark,isenable,createdtime,modifiedtime)  values   ('0a5aed35215440aa875a8534c2ef1711','2005','hems.rider.h5url.get','api/FengNiaoJPRider/GetLocationH5Url','【蜂鸟即配】获取骑手位置H5页面',1,'2024-04-09 00:00:00','2024-04-09 00:00:00'),  ('0a5aed35215440aa875a8534c2ef1712','2001','hems.rider.h5url.get','api/FengNiaoRider/GetLocationH5Url','【蜂鸟配送】获取骑手位置H5页面',1,'2024-04-09 00:00:00','2024-04-09 00:00:00'),  ('0a5aed35215440aa875a8534c2ef1713','2004','hems.rider.h5url.get','api/ShunFengRider/GetLocationH5Url','【顺丰同城】获取骑手位置H5页面',1,'2024-04-09 00:00:00','2024-04-09 00:00:00'),  ('0a5aed35215440aa875a8534c2ef1714','2002','hems.rider.h5url.get','api/MeiTuanRide/GetLocationH5Url','【美团骑手】获取骑手位置H5页面',1,'2024-04-09 00:00:00','2024-04-09 00:00:00'),  ('0a5aed35215440aa875a8534c2ef1715','2003','hems.rider.h5url.get','api/DadaRider/GetLocationH5Url','【达达配送】获取骑手位置H5页面',1,'2024-04-09 00:00:00','2024-04-09 00:00:00'); |  |
| **缓存变更** | 无 |  |
| **第三方依赖** | 无 |  |
| **是否需要压测** | 无需 |  |
| **其他** | 无 |  |
| **自测&评估** | **冒烟情况** | 自测完毕 |  |
| **风险评估** |  |  |
| **测试建议** | 无 |  |
| **上线信息** | **计划上线时间** |  |  |
| **上线checklist** |  |  |
| **备注** |  |  |