# 【20240410】V1.1.7 内购商城二期-限购、总部仓自提

# 一、背景

## 1.1 业务背景

内购商城二期，实现对内购商品的周期限购，对应订单需要做限购校验处理，内购商品提供总部仓自提方式

## 1.2 痛点分析

周期限购计算方式、总部仓自提运费计算

## 1.3 系统现状

未实现内购商品的周期限购和总部仓自提

# 二、需求分析

## 2.1 业务流程

[内购商城二期-限购、总部仓自提](https://modao.cc/app/8VeblZNOsaxkzkpuperrij#screen=slupa4aquzh0p96)

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

true总部仓自提falseautotoptrue3391

# 五、详细设计

## 5.1 详细模块设计

**5.1.1 周期限购计算方式**

****

**根据天、周、月实现周期限购范围**

**天：**

**周：**

**月：**

## 5.3 接口设计

### 5.3.1 前端交互接口

#### 1.订单初始化：1.0/order/orderConfirmInit

#### 详细实现：对于云仓/b2c订单需要获取总部仓自提相关信息，新增总部仓自提标识和总部仓自提地址

#### 2.订单确认：1.0/order/orderConfirm

**详细实现：**对于总部仓自提产生的0元单不需要处理，过滤掉

**3.订单提交：**1.0/order/addOrder

**详细实现：**周期限购校验，根据商品规格id获取指定商品的限购规则，根据规则获取查询出指定商城(普通商城、兑换商城、内购商城)对应的订单数量，用于做限购校验

**5.统计用户购买记录：**1.0/order-info/countNumberByTime

 **详细实现：**新增参数限购订单类型limitOrderType，1内购订单 2兑换订单 3非内购和兑换订单，针对不同商城，周期限购互不影响

## 5.4 涉及数据库

## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年04月11日**

**研发时间：2024年04月11日-2024年04月17日（含研发自测）；联调时间：2024年04月18日-2023年04月23日；测试时间：2024年03月14日-2024年04月23日；上线时间：2024年04月28日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 内购商城订单相关 | 订单初始化 | ydjia-merchant-customer |  |  |  |  |  |
| 订单确认 |  |  |  |
| 订单提交 |  |  |  |
| 订单信息相关 | 统计用户购买记录 | hydee-middle-order |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等