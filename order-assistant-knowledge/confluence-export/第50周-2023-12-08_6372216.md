# 第50周-2023-12-08

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | **长期项目** .net流程梳理 |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：40小时**本周总工时：40小时1.汇付插件对接-开发提测 2.汇付SDK升级-开发提测 3.订单分析订单热力图BUG 4.微商城订单流水优化 5.拣货复核明细缺失定位以及修改 6.抖店020对接，切店自动化评审 | **㊀计划工作****㊁实际完成**1. 汇付插件提测下周上线 2. 商城订单流水优化提测下周上线 3. .抖店020评审完毕待出设计方案 **㊂遗留问题**1. 拣货复核明细缺失定位以及修改今天下班前解决 **㊃风险问题****** | **需求研发相关**1. 抖店020订单对接设计方案 2. 抖店020订单对接代码开发 3. 日常订单BUG修改 |  |
| 2 | 郭志明 | **本周总工时：40h**1. 饿了么退款退货流程8h 2. b2c库存接口替换6h 3. 订单运营数据导出3h 4. 线上测试数据清理6h 5. 平台运费优惠拆封6h 6. 微商城订单库存释放问题分析2h 7. 切店自动化9h | **㊀计划工作**1. 饿了么退款退货流程 2. b2c库存接口替换 3. 订单运营数据导出 4. 线上测试数据清理 5. 平台运费优惠拆封 6. 微商城订单库存释放问题分析 7. 切店自动化需求评审、设计 **㊁实际完成**1. 饿了么退款退货流程 100% 2. b2c库存接口替换 100% 3. 订单运营数据导出 100% 4. 线上测试数据清理 100% 5. 平台运费优惠拆封 100% 6. 微商城订单库存释放问题分析 100% 7. 切店自动化设计90% **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）**1. 在解决bug的时候通常会对业务流程进行跟踪分析，建议将一些复杂的业务流程能写到CF中 **** | **㊀需求研发相关**1. 切店自动化开发、自测、提测 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 3 |  | **本周总工时：40小时**1. 保山医保通道对接 15h 2. 商户号1641710730 微信支付资金冻结问题修复 6h 3. 退款审核报错问题修复 6h 4. 汇付插件sdk升级 3h 5. 订单无法推送到门店问题解决 3h 6. B2C订单推送B2C管理平台金额错误修复 5h 7. B2C异常订单导出 2h | **㊀计划工作**1. 保山医保通道对接 2. 商户号1641710730 微信支付资金冻结问题修复 3. 退款审核报错问题修复 4. 汇付插件sdk升级 5. 订单无法推送到门店问题解决 6. B2C订单推送B2C管理平台金额错误修复 7. B2C异常订单导出 8. 【生产环境】下单明明有库存，返回库存不足异常 **㊁实际完成**1. 保山医保通道对接， 总体进展90% 2. 商户号1641710730 微信支付资金冻结问题修复， 总体进度90% 3. 退款审核报错问题修复 总体进度100% 4. 汇付插件sdk升级 总体进度 100% 5. 订单无法推送到门店问题解决 总体进度100% 6. B2C订单推送B2C管理平台金额错误修复 总体进度 100% 7. B2C异常订单导出 总体进度 100%\ 8. 【生产环境】下单明明有库存，返回库存不足异常 总体进度 0% **㊂遗留问题**1. 保山医保支付时防重复校验代码出现两条支付流水，导致查询支付状态失败 2. 保山医保授权码到期自动审核拒绝 3. 商户号1641710730 微信支付资金冻结 等待批量刷解冻接口 **㊃风险问题** **** | **㊀需求研发相关**1. pos对接 2. 订单优化处理 3. 保山医保整体流程测试 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 4 |  | **本周总工时：40小时**1. 刷新图片和物流信息,8h 2. 拉取雨诺退款单下账状态 6h 3. 刷新店铺配置2h 4. 处方单二次审方问题3h 5. 清除线上云仓测试订单5h 6. 处方图片上传OSS超时8h 7. 刷新京东到家店铺超过10分钟改为自配送设置 3h 8. 切点自动化接口对接 5h **** | **㊀计划工作**1. 刷新云仓迁移订单的图片和物流信息 2. 拉取雨诺退款单下账状态 3. 刷新店铺配置 4. 清除线上云仓测试订单 5. 刷新京东到家店铺超过10分钟改为自配送 6. 处方图片上传OSS超时处理 7. 切店自动化接口打通 **㊁实际完成**1. 刷新云仓迁移订单的图片和物流信息，总体进展100%; 2. 拉取雨诺退款单下账状态,100% 3. 刷新店铺配置,100% 4. 清除线上云仓测试订单 100% 5. 处方图片上传OSS超时处理 80% 6. 切店自动化接口打通,50% **㊂遗留问题**1. 处方图片上传OSS上线 2. 京东到家开发者账号还没有 **** | **㊀需求研发相关**1. 切店自动化的开发 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 5 |  | **本周总工时：40小时****1.打印程序测试发布 2day****2.运费拆分 熟悉java 代码 2day****3.其他协助 1day** | **计划工作**1. 完成打印程序升级测试 2. 心云平台订单优惠运费拆分 3. 打印程序热更新 **㊁实际完成**1. 完成打印程序升级测试 100%; 2. 心云平台订单优惠运费拆分 100% 3. 打印程序热更新 100% **** | **** | .NET 数据中台 和 java 业务代码业务关联 没有梳理，查询问题 耗时耗力。需要整理形成文档上。[.NET 工程维护 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6370373) |
| 6 | 崔建波 | **本周总工时：40小时**1. 库存扣减使用新的接口,16h 2. 雨诺状态回调失败，8h 3. pod重启导致服务不可用，和王庭一起查看排查问题，最终修改shutdown的执行逻辑，在shutdown之前unregister nacos。8h 4. 订单营销分析做订单迁移，写设计文档。8h | **计划工作**1. 库存扣减占用 2. 雨诺状态回调失败 3. pod重启问题 **㊁实际完成**1. 库存扣减占用 100%; 2. 雨诺状态回调失败 100% 3. pod重启问题 50% **** | 1. 订单营销分析做订单迁移 2. pod重启问题，重写shutdown逻辑。在shutdown之前unregister nacos |  |
| 7 | 李洋 | **本周总工时：40小时**1.修改二次退款退款单状态为已取消问题 0.5天2.订单数据修复：修复无拣货信息生产数据 1天3.协助世达分析微信订单退款成功心云订单状态却没有更新 0.5天4.订单数据修复：订单商品被用户直接拿走，店员还没有具体操作 0.5天5.订单数据修复：老系统的订单显示待配送，点配送报1038错误 0.5天6.排查C530门店修改配送方式返回了两条美团数据 1天 **** | **㊀计划工作****㊁实际完成**1.修改二次退款退款单状态为已取消问题 2.订单数据修复：修复无拣货信息生产数据 3.协助世达分析微信订单退款成功心云订单状态却没有更新 4.订单数据修复：订单商品被用户直接拿走，店员还没有具体操作 5.订单数据修复：老系统的订单显示待配送，点配送报1038错误 6.排查C530门店修改配送方式返回了两条美团数据 | **㊀需求研发相关**1.pos对接**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 8 | 徐国华 | **** | **** | **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
| 经营分析历史数据迁移 | 已评审，编写设计方案 | 评审设计方案，并上线 | 20号前上线 |
| 切店自动化 | 已评审设计方案 | 评审设计方案，并提测 | 20号前上线 |
| 海典POS对接 | 已评审PRD | 评审设计方案，进入开发 | 月底前上线 |
| 医保 | 联调90% | 提测 |  |
| 抖店对接 | 已评审PRD | 评审设计方案，进入开发 | 月底前提测 |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |
| 3 |  |  |  |
| 4 |  |  |  |
| 5 |  |  |  |
| 6 |  |  |  |
| 7 |  |  |  |
| 8 |  |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | CR问题描述 | 改造方案 |
| --- | --- | --- |
| 1 |  |  |
| 2 |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 系统环境 | 缺陷等级 | 缺陷描述 | 负责人 | 问题分析 | 处理结果 |
| --- | --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | 切流范围确认 |  |  |  |
| 2 | 保山医保授权码到期自动审核拒绝 |  |  |  |
| 3 | 聚石塔、云顶、多多云 需要跟进真实B2C海典转发流程 |  |  |  |