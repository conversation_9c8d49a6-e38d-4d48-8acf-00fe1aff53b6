# hana-科传数据迁移字段映射

固定的Schema:

1. HX_CRM.CV_STOREINFOR 查订单归属组织信息
2. "HX_CRM"."TB_CRM_MATSTER_N" 查会员信息
3. XF_TRANSSALESTENDER 查支付信息


主表和明细信息Schema不一样,不需要固定，按地区有以下Schema:

YNHX_DATA01(云南）,
GXHX_USERS（广西）,
GZHX_USERS(贵州），
SCHX_USERS(攀枝花),
SXHX_USERS(山西），
CQHX_USERS(重庆），
CDHX_USERS(四川）,
SHHX_DATA01(上海),
TJHX_DATA01(天津），
HNHX_DATA01(海南），
HENHX_DATA01(河南),
SXGSHX_DATA01(山西广生),
TJQCHX_DATA01(天津乾昌),
HENNYHX_DATA01(河南康健),
ZYHX_USERS(中药科技)

| **字段名** | **字段解释** |
| baseOrderInfo订单基础信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据 → (订单中台处理)** | **备注(※表示自己确认的)** | | orderNo | OfflineOrderNo | 内部订单号 | 无 → 内部生成 |  | | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 无 → 科传 |  | | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 不同的schema下发现XF_DOCNO对应多个店铺,所以确认一个单的条件: XF_DOCNO+XF_STORECODE | | dayNum |  | 每日号 | XF_TXSERIAL？无 |  | | orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 |  | | created |  | 创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | | payTime |  | 支付时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | | billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | | completeTime |  | 完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | | actualPayAmount |  | 实付金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  | | actualCollectAmount |  | 实收金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  | | orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | 无 |  | | serialNo |  | 优惠券核销流水号 | 无 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据 → (订单中台处理)** | **备注(※表示自己确认的)** | orderNo | OfflineOrderNo | 内部订单号 | 无 → 内部生成 |  | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 无 → 科传 |  | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 不同的schema下发现XF_DOCNO对应多个店铺,所以确认一个单的条件: XF_DOCNO+XF_STORECODE | dayNum |  | 每日号 | XF_TXSERIAL？无 |  | orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 |  | created |  | 创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | payTime |  | 支付时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | completeTime |  | 完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | actualPayAmount |  | 实付金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  | actualCollectAmount |  | 实收金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  | orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | 无 |  | serialNo |  | 优惠券核销流水号 | 无 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据 → (订单中台处理)** | **备注(※表示自己确认的)** |
| orderNo | OfflineOrderNo | 内部订单号 | 无 → 内部生成 |  |
| thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 无 → 科传 |  |
| thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 不同的schema下发现XF_DOCNO对应多个店铺,所以确认一个单的条件: XF_DOCNO+XF_STORECODE |
| dayNum |  | 每日号 | XF_TXSERIAL？无 |  |
| orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 |  |
| created |  | 创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  |
| payTime |  | 支付时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  |
| billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  |
| completeTime |  | 完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  |
| actualPayAmount |  | 实付金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  |
| actualCollectAmount |  | 实收金额 | XF_TRANSSALESTOTALXF_SELLINGAMOUNTXF_NETAMOUNT 【20250314】 |  |
| orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | 无 |  |
| serialNo |  | 优惠券核销流水号 | 无 |  |
| baseOrganizationInfo订单归属组织信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | **** | | storeCode |  | 门店编码 | HX_CRM.CV_STOREINFORPLANT | HX_CRM.CV_STOREINFOR PLANT 关联 XF_TRANSSALESTOTAL.XF_STORECODE |  | | storeName |  | 门店名称 | HX_CRM.CV_STOREINFORPLANT_TEXT |  |  | | companyCode |  | 分公司Code | HX_CRM.CV_STOREINFORCOMP_CODE |  |  | | companyName |  | 分公司名称 | HX_CRM.CV_STOREINFORZC_GSJC |  |  | | storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营JOIN - 加盟 | HX_CRM.CV_STOREINFOR_BIC_ZC_INPRO ZOJM-独立法人加盟店Z003-分支机构加盟店Z001-直营门店 |  |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | **** | storeCode |  | 门店编码 | HX_CRM.CV_STOREINFORPLANT | HX_CRM.CV_STOREINFOR PLANT 关联 XF_TRANSSALESTOTAL.XF_STORECODE |  | storeName |  | 门店名称 | HX_CRM.CV_STOREINFORPLANT_TEXT |  |  | companyCode |  | 分公司Code | HX_CRM.CV_STOREINFORCOMP_CODE |  |  | companyName |  | 分公司名称 | HX_CRM.CV_STOREINFORZC_GSJC |  |  | storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营JOIN - 加盟 | HX_CRM.CV_STOREINFOR_BIC_ZC_INPRO ZOJM-独立法人加盟店Z003-分支机构加盟店Z001-直营门店 |  |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | **** |
| storeCode |  | 门店编码 | HX_CRM.CV_STOREINFORPLANT | HX_CRM.CV_STOREINFOR PLANT 关联 XF_TRANSSALESTOTAL.XF_STORECODE |  |
| storeName |  | 门店名称 | HX_CRM.CV_STOREINFORPLANT_TEXT |  |  |
| companyCode |  | 分公司Code | HX_CRM.CV_STOREINFORCOMP_CODE |  |  |
| companyName |  | 分公司名称 | HX_CRM.CV_STOREINFORZC_GSJC |  |  |
| storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营JOIN - 加盟 | HX_CRM.CV_STOREINFOR_BIC_ZC_INPRO ZOJM-独立法人加盟店Z003-分支机构加盟店Z001-直营门店 |  |  |
| baseCashierDeskInfo订单归属收银台信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | XF_TRANSSALESTOTALXF_TILLID | 关联订单表字段XF_TRANSSALESTOTAL同一个表 | | cashier |  | 收银员编码 | XF_TRANSSALESTOTALXF_CASHIER |  | | picker |  | 拣货员编码 | XF_TRANSSALESTOTALXF_SALESMAN | 销售员 | | shiftId | ShiftId | 班次 | 无 |  | | shiftDate |  | 班次日期 | 无 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | XF_TRANSSALESTOTALXF_TILLID | 关联订单表字段XF_TRANSSALESTOTAL同一个表 | cashier |  | 收银员编码 | XF_TRANSSALESTOTALXF_CASHIER |  | picker |  | 拣货员编码 | XF_TRANSSALESTOTALXF_SALESMAN | 销售员 | shiftId | ShiftId | 班次 | 无 |  | shiftDate |  | 班次日期 | 无 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | XF_TRANSSALESTOTALXF_TILLID | 关联订单表字段XF_TRANSSALESTOTAL同一个表 |
| cashier |  | 收银员编码 | XF_TRANSSALESTOTALXF_CASHIER |  |
| picker |  | 拣货员编码 | XF_TRANSSALESTOTALXF_SALESMAN | 销售员 |
| shiftId | ShiftId | 班次 | 无 |  |
| shiftDate |  | 班次日期 | 无 |  |
| baseUserInfo订单归属会员信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |  | | userId | OfflineUserId | 会员id | "HX_CRM"."TB_CRM_MATSTER_N"会员ID(会员编号) | 字段为中文之前是取XF_VIPCODE在XF_TRANSSALESITEM有XF_VIPCODE字段备注: XF_VIPCODE为空则是非会员单XF_TRANSSALESTOTAL.XF_CLIENTCODE 会员卡号备注: XF_CLIENTCODE 为空则是非会员单 | 关联方式XF_TRANSSALESTOTAL.XF_CLIENTCODE =会员卡ID(实物卡号)   2 incomplete 需要将这个字段转为会员的用户ID MemberInfoApi#batchGetUserIdByCrmIds | | userName |  | 会员名称 | "HX_CRM"."TB_CRM_MATSTER_N"姓名 | 字段为中文 |  | | userCardNo | OfflineUserCardNo | 会员卡号 | "HX_CRM"."TB_CRM_MATSTER_N"会员卡ID(实物卡号) | 字段为中文 |  | | userMobile | OfflineUserMobile | 会员手机号 | "HX_CRM"."TB_CRM_MATSTER_N"移动电话 | 字段为中文 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |  | userId | OfflineUserId | 会员id | "HX_CRM"."TB_CRM_MATSTER_N"会员ID(会员编号) | 字段为中文之前是取XF_VIPCODE在XF_TRANSSALESITEM有XF_VIPCODE字段备注: XF_VIPCODE为空则是非会员单XF_TRANSSALESTOTAL.XF_CLIENTCODE 会员卡号备注: XF_CLIENTCODE 为空则是非会员单 | 关联方式XF_TRANSSALESTOTAL.XF_CLIENTCODE =会员卡ID(实物卡号)   2 incomplete 需要将这个字段转为会员的用户ID MemberInfoApi#batchGetUserIdByCrmIds | userName |  | 会员名称 | "HX_CRM"."TB_CRM_MATSTER_N"姓名 | 字段为中文 |  | userCardNo | OfflineUserCardNo | 会员卡号 | "HX_CRM"."TB_CRM_MATSTER_N"会员卡ID(实物卡号) | 字段为中文 |  | userMobile | OfflineUserMobile | 会员手机号 | "HX_CRM"."TB_CRM_MATSTER_N"移动电话 | 字段为中文 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |  |
| userId | OfflineUserId | 会员id | "HX_CRM"."TB_CRM_MATSTER_N"会员ID(会员编号) | 字段为中文之前是取XF_VIPCODE在XF_TRANSSALESITEM有XF_VIPCODE字段备注: XF_VIPCODE为空则是非会员单XF_TRANSSALESTOTAL.XF_CLIENTCODE 会员卡号备注: XF_CLIENTCODE 为空则是非会员单 | 关联方式XF_TRANSSALESTOTAL.XF_CLIENTCODE =会员卡ID(实物卡号)   2 incomplete 需要将这个字段转为会员的用户ID MemberInfoApi#batchGetUserIdByCrmIds |
| userName |  | 会员名称 | "HX_CRM"."TB_CRM_MATSTER_N"姓名 | 字段为中文 |  |
| userCardNo | OfflineUserCardNo | 会员卡号 | "HX_CRM"."TB_CRM_MATSTER_N"会员卡ID(实物卡号) | 字段为中文 |  |
| userMobile | OfflineUserMobile | 会员手机号 | "HX_CRM"."TB_CRM_MATSTER_N"移动电话 | 字段为中文 |  |
| basePrescriptionInfo订单归属处方信息 | 无->不处理| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 | 无 |  | | prescriptionNo | OfflinePrescriptionNo | 处方单号 | 无 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 | 无 |  | prescriptionNo | OfflinePrescriptionNo | 处方单号 | 无 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 | 无 |  |
| prescriptionNo | OfflinePrescriptionNo | 处方单号 | 无 |  |
| payInfoList订单多支付信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币MEDICAL_HEALTH_INSURANCE - 医保CASH - 现金WECHAT_PAY - 微信支付ZHI_FU_BAO_PAY - 支付宝BANK_CARD - 银行卡TOP_UP_CARDS - 充值卡 | XF_TRANSSALESTENDERXF_TENDERCODE | 正单 XF_TRANSSALESTENDER.XF_DOCNO= XF_TRANSSALESTOTALXF_DOCNO关联条件XF_DOCNO and XF_STORECODE 支付编码对应的名称 从HX_SJCJ.TENDTYT表中获取 | | payAmount |  | 支付金额 | XF_BASEAMOUNT 实付金额 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币MEDICAL_HEALTH_INSURANCE - 医保CASH - 现金WECHAT_PAY - 微信支付ZHI_FU_BAO_PAY - 支付宝BANK_CARD - 银行卡TOP_UP_CARDS - 充值卡 | XF_TRANSSALESTENDERXF_TENDERCODE | 正单 XF_TRANSSALESTENDER.XF_DOCNO= XF_TRANSSALESTOTALXF_DOCNO关联条件XF_DOCNO and XF_STORECODE 支付编码对应的名称 从HX_SJCJ.TENDTYT表中获取 | payAmount |  | 支付金额 | XF_BASEAMOUNT 实付金额 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币MEDICAL_HEALTH_INSURANCE - 医保CASH - 现金WECHAT_PAY - 微信支付ZHI_FU_BAO_PAY - 支付宝BANK_CARD - 银行卡TOP_UP_CARDS - 充值卡 | XF_TRANSSALESTENDERXF_TENDERCODE | 正单 XF_TRANSSALESTENDER.XF_DOCNO= XF_TRANSSALESTOTALXF_DOCNO关联条件XF_DOCNO and XF_STORECODE 支付编码对应的名称 从HX_SJCJ.TENDTYT表中获取 |
| payAmount |  | 支付金额 | XF_BASEAMOUNT 实付金额 |  |
| orderDetailList订单明细 | | **字段名** | **字段解释** | | baseOrderDetailInfo明细基础信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | | rowNo |  | 商品行号 | XF_TXSERIAL |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | | commodityCostPrice |  | 商品成本价 | 无->0 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | rowNo |  | 商品行号 | XF_TXSERIAL |  | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | commodityCostPrice |  | 商品成本价 | 无->0 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | | rowNo |  | 商品行号 | XF_TXSERIAL |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | | commodityCostPrice |  | 商品成本价 | 无->0 |  | | pickInfoList明细拣货信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | rowNo |  | 商品行号 |  |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | rowNo |  | 商品行号 |  |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | rowNo |  | 商品行号 |  |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | **字段名** | **字段解释** | baseOrderDetailInfo明细基础信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | | rowNo |  | 商品行号 | XF_TXSERIAL |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | | commodityCostPrice |  | 商品成本价 | 无->0 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | rowNo |  | 商品行号 | XF_TXSERIAL |  | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | commodityCostPrice |  | 商品成本价 | 无->0 |  | pickInfoList明细拣货信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | rowNo |  | 商品行号 |  |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | rowNo |  | 商品行号 |  |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  |
| **字段名** | **字段解释** |
| baseOrderDetailInfo明细基础信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | | rowNo |  | 商品行号 | XF_TXSERIAL |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | | commodityCostPrice |  | 商品成本价 | 无->0 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  | rowNo |  | 商品行号 | XF_TXSERIAL |  | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE | erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty | price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 | totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 | discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 | billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 | billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 | commodityCostPrice |  | 商品成本价 | 无->0 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| orderNo | OfflineOrderNo | 内部订单号 | 无 → 订单中台处理 |  |
| rowNo |  | 商品行号 | XF_TXSERIAL |  |
| platformSkuId | PlatformSkuId | 商品三方平台编码 | 无 |  |
| erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联订单主信息XF_TRANSSALESTOTALXF_DOCNO 关联条件XF_DOCNO and XF_STORECODE |
| erpName |  | 商品名称 | HANA_D.MAKTMAKTX  关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  |
| commodityCount |  | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |  |
| status | OfflineDetailStatus | 明细状态 NORMAL-正常 | → 默认正常 |  |
| giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  |
| originalPrice |  | 商品原单价 | new: XF_ORGUPRICE 未乘以数量 | ----可以反算（totalAmount+discountAmount）/qty |
| price |  | 商品售价（实际） | 无 → 自己算 | 可以反算（totalAmount）/qty XF_AMTSOLD/XF_QTYSOLD 实价 |
| totalAmount |  | 商品总额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 总金额是已经减去折扣金额的 |
| discountShare |  | 优惠分摊 | 无→无,可以自己算 XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 乘以数量的，**总分摊**discountAmount== XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT |
| discountAmount |  | 折扣金额 | XF_MARKDOWNAMT+XF_DISCOUNTAMT+XF_PROMOTIONAMT | 同上，总的 |
| billPrice |  | 下账单价（实际） | XF_AMTSOLD/XF_QTYSOLD 实价 | XF_AMTSOLD/XF_QTYSOLD 实价 |
| billAmount |  | 下账金额 | XF_TRANSSALESITEMXF_AMTSOLD | **已经乘以数量的** 下账金额是已经减去折扣金额的 |
| commodityCostPrice |  | 商品成本价 | 无->0 |  |
| pickInfoList明细拣货信息 | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | rowNo |  | 商品行号 |  |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | rowNo |  | 商品行号 |  |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  | makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  | count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| rowNo |  | 商品行号 |  |  |
| erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU |  |
| makeNo | OfflineMarkNo | 商品批号 | XF_TRANSSALESITEMXF_ITEMLOTNUM |  |
| count |  | 数量 | XF_TRANSSALESITEMXF_QTYSOLD |  |
| orderPromotionList线下单促销表 | | 字段名 | Java类型 | 字段注释 | 同步hana数据 | | --- | --- | --- | --- | | id | Long | 主键 |  | | orderNo | String | 内部订单号,自己生成 |  | | erpCode | String | 商品编码 | XF_TRANSSALESITEM XF_PLU | | commodityCount | BigDecimal | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD | | thirdOrderNo | String | 第三方平台订单号 | XF_TRANSSALESITEMXF_DOCNO | | promotionNo | String | 促销编码 | XF_TRANSSALESITEMXF_PROMID1~XF_PROMID5 | | subPromotionNo | String | 子促销编码 |  | | promotionType | String | 促销类型 |  | | promotionAmount | BigDecimal | 促销金额 | XF_TRANSSALESITEMXF_PROMAMT1~XF_PROMAMT5 | | createdBy | String | 创建人 |  | | updatedBy | String | 更新人 |  | | createdTime | Date | 创建时间 |  | | updatedTime | Date | 更新时间 |  | | version | Long | 数据版本，每次update+1 |  | | extendJson | String | 拓展字段 |  | | type | String | ORDER,DETAIL |  | | 字段名 | Java类型 | 字段注释 | 同步hana数据 | id | Long | 主键 |  | orderNo | String | 内部订单号,自己生成 |  | erpCode | String | 商品编码 | XF_TRANSSALESITEM XF_PLU | commodityCount | BigDecimal | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD | thirdOrderNo | String | 第三方平台订单号 | XF_TRANSSALESITEMXF_DOCNO | promotionNo | String | 促销编码 | XF_TRANSSALESITEMXF_PROMID1~XF_PROMID5 | subPromotionNo | String | 子促销编码 |  | promotionType | String | 促销类型 |  | promotionAmount | BigDecimal | 促销金额 | XF_TRANSSALESITEMXF_PROMAMT1~XF_PROMAMT5 | createdBy | String | 创建人 |  | updatedBy | String | 更新人 |  | createdTime | Date | 创建时间 |  | updatedTime | Date | 更新时间 |  | version | Long | 数据版本，每次update+1 |  | extendJson | String | 拓展字段 |  | type | String | ORDER,DETAIL |  |
| 字段名 | Java类型 | 字段注释 | 同步hana数据 |
| id | Long | 主键 |  |
| orderNo | String | 内部订单号,自己生成 |  |
| erpCode | String | 商品编码 | XF_TRANSSALESITEM XF_PLU |
| commodityCount | BigDecimal | 商品数量 | XF_TRANSSALESITEMXF_QTYSOLD |
| thirdOrderNo | String | 第三方平台订单号 | XF_TRANSSALESITEMXF_DOCNO |
| promotionNo | String | 促销编码 | XF_TRANSSALESITEMXF_PROMID1~XF_PROMID5 |
| subPromotionNo | String | 子促销编码 |  |
| promotionType | String | 促销类型 |  |
| promotionAmount | BigDecimal | 促销金额 | XF_TRANSSALESITEMXF_PROMAMT1~XF_PROMAMT5 |
| createdBy | String | 创建人 |  |
| updatedBy | String | 更新人 |  |
| createdTime | Date | 创建时间 |  |
| updatedTime | Date | 更新时间 |  |
| version | Long | 数据版本，每次update+1 |  |
| extendJson | String | 拓展字段 |  |
| type | String | ORDER,DETAIL |  |
| orderCouponList线下单coupon表 | | 字段名 | Java类型 | 字段注释 | 同步hana数据 | | --- | --- | --- | --- | | id | Long | 主键 | 无 | | orderNo | String | 内部订单号,自己生成 | | erpCode | String | 商品编码 | | commodityCount | BigDecimal | 商品数量 | | thirdOrderNo | String | 第三方平台订单号 | | couponNo | String | 优惠券编码 | | openCode | String | 优惠券导出编码 | | couponName | String | 优惠券名称 | | couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 | | couponDenomination | String | 优惠券面值(String) | | usedCouponAmount | BigDecimal | 使用优惠券金额 | | createdBy | String | 创建人 | | updatedBy | String | 更新人 | | createdTime | Date | 创建时间 | | updatedTime | Date | 更新时间 | | version | Long | 数据版本，每次update+1 | | extendJson | String | 拓展字段 | | type | String | ORDER,DETAIL | | 字段名 | Java类型 | 字段注释 | 同步hana数据 | id | Long | 主键 | 无 | orderNo | String | 内部订单号,自己生成 | erpCode | String | 商品编码 | commodityCount | BigDecimal | 商品数量 | thirdOrderNo | String | 第三方平台订单号 | couponNo | String | 优惠券编码 | openCode | String | 优惠券导出编码 | couponName | String | 优惠券名称 | couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 | couponDenomination | String | 优惠券面值(String) | usedCouponAmount | BigDecimal | 使用优惠券金额 | createdBy | String | 创建人 | updatedBy | String | 更新人 | createdTime | Date | 创建时间 | updatedTime | Date | 更新时间 | version | Long | 数据版本，每次update+1 | extendJson | String | 拓展字段 | type | String | ORDER,DETAIL |
| 字段名 | Java类型 | 字段注释 | 同步hana数据 |
| id | Long | 主键 | 无 |
| orderNo | String | 内部订单号,自己生成 |
| erpCode | String | 商品编码 |
| commodityCount | BigDecimal | 商品数量 |
| thirdOrderNo | String | 第三方平台订单号 |
| couponNo | String | 优惠券编码 |
| openCode | String | 优惠券导出编码 |
| couponName | String | 优惠券名称 |
| couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 |
| couponDenomination | String | 优惠券面值(String) |
| usedCouponAmount | BigDecimal | 使用优惠券金额 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |
| createdTime | Date | 创建时间 |
| updatedTime | Date | 更新时间 |
| version | Long | 数据版本，每次update+1 |
| extendJson | String | 拓展字段 |
| type | String | ORDER,DETAIL |
| **退单** |
| baseRefundInfo退单基础信息 | 退单看数据并没有新增一条退款记录而是在正单的维度添加一个XF_VOIDDOCNO字段值，然后将原单金额改为负数| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | orderNo | OfflineOrderNo | 内部正单订单号 | → 内部查找正单号 |  | | refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  | | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | → 科传 |  | | thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 | XF_TRANSSALESTOTALXF_VOIDDOCNO 关联条件XF_VOIDDOCNO and XF_STORECODE | XF_VOIDDOCNO 不一定有 退货类型不同，不一定有 XF_VOIDDOCNO 如果有值，对应的就是正单的XF_DOCNO | | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 一定有值，但是是另一个值，新的一个单，和正单的对应不上 | | refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | XF_VOIDDOCNO 字段1. V开头的是全单退 2. S->开头的要看实际情况，通过计算可以获得 | 判断不出来是部分退还是全额退。没有这个场景，做不到 | | afterSaleType | AfterSaleType | 售后单类型AFTER_SALE_AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 AFTER_SALE_AMOUNT_GOODS 退货退款 | 可以默认退货退款?→只有退货退款默认**AFTER_SALE_AMOUNT_GOODS** |  | | refundState | RefundState | 退单状态 REFUNDED 已退款 | → 默认已退款 |  | | reason |  | 退款原因 | 无->无 |  | | created |  | 退单创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | | applyTime |  | 退单申请时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | | completeTime |  | 退单完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME | XF_TXDATE+ XF_TXTIME | | billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | | totalAmount |  | 退款商品金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 | | shopRefund |  | 商家退款总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 | | consumerRefund |  | 退买家总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的负数 | | serialNo |  | 优惠券核销流水号 | 无 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | orderNo | OfflineOrderNo | 内部正单订单号 | → 内部查找正单号 |  | refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | → 科传 |  | thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 | XF_TRANSSALESTOTALXF_VOIDDOCNO 关联条件XF_VOIDDOCNO and XF_STORECODE | XF_VOIDDOCNO 不一定有 退货类型不同，不一定有 XF_VOIDDOCNO 如果有值，对应的就是正单的XF_DOCNO | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 一定有值，但是是另一个值，新的一个单，和正单的对应不上 | refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | XF_VOIDDOCNO 字段1. V开头的是全单退 2. S->开头的要看实际情况，通过计算可以获得 | 判断不出来是部分退还是全额退。没有这个场景，做不到 | afterSaleType | AfterSaleType | 售后单类型AFTER_SALE_AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 AFTER_SALE_AMOUNT_GOODS 退货退款 | 可以默认退货退款?→只有退货退款默认**AFTER_SALE_AMOUNT_GOODS** |  | refundState | RefundState | 退单状态 REFUNDED 已退款 | → 默认已退款 |  | reason |  | 退款原因 | 无->无 |  | created |  | 退单创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | applyTime |  | 退单申请时间 | XF_TRANSSALESTOTALXF_CREATETIME |  | completeTime |  | 退单完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME | XF_TXDATE+ XF_TXTIME | billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  | totalAmount |  | 退款商品金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 | shopRefund |  | 商家退款总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 | consumerRefund |  | 退买家总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的负数 | serialNo |  | 优惠券核销流水号 | 无 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| orderNo | OfflineOrderNo | 内部正单订单号 | → 内部查找正单号 |  |
| refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  |
| thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | → 科传 |  |
| thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 | XF_TRANSSALESTOTALXF_VOIDDOCNO 关联条件XF_VOIDDOCNO and XF_STORECODE | XF_VOIDDOCNO 不一定有 退货类型不同，不一定有 XF_VOIDDOCNO 如果有值，对应的就是正单的XF_DOCNO |
| thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | XF_TRANSSALESTOTALXF_DOCNO | 一定有值，但是是另一个值，新的一个单，和正单的对应不上 |
| refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | XF_VOIDDOCNO 字段1. V开头的是全单退 2. S->开头的要看实际情况，通过计算可以获得 | 判断不出来是部分退还是全额退。没有这个场景，做不到 |
| afterSaleType | AfterSaleType | 售后单类型AFTER_SALE_AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 AFTER_SALE_AMOUNT_GOODS 退货退款 | 可以默认退货退款?→只有退货退款默认**AFTER_SALE_AMOUNT_GOODS** |  |
| refundState | RefundState | 退单状态 REFUNDED 已退款 | → 默认已退款 |  |
| reason |  | 退款原因 | 无->无 |  |
| created |  | 退单创单时间 | XF_TRANSSALESTOTALXF_CREATETIME |  |
| applyTime |  | 退单申请时间 | XF_TRANSSALESTOTALXF_CREATETIME |  |
| completeTime |  | 退单完成时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME | XF_TXDATE+ XF_TXTIME |
| billTime |  | 下账时间 | XF_TRANSSALESTOTALXF_TXDATE+ XF_TXTIME |  |
| totalAmount |  | 退款商品金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 |
| shopRefund |  | 商家退款总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的 |
| consumerRefund |  | 退买家总金额 | XF_TRANSSALESTOTALXF_NETAMOUNT | 取明细表的XF_AMTSOLD累加 XF_AMTSOLD： 已经乘以数量的负数 |
| serialNo |  | 优惠券核销流水号 | 无 |  |
| baseRefundOrganizationInfo退款订单归属组织信息 | 复用baseOrganizationInfo |
| baseRefundUserInfo退单归属会员信息 | 复用baseUserInfo |
| baseRefundCashierDeskInfo退单归属收银台信息 | 复用baseCashierDeskInfo |
| 退单明细refundDetailList | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | | refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  | | rowNo |  | 商品行号 | 无->无 |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无->无 |  | | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联条件XF_VOIDDOCNO and XF_STORECODE | | erpName |  | 商品名称 | HANA_D.MAKTMAKTX 关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | | refundCount |  | 退款数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | | refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 | → 默认明细状态正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | | originalPrice |  | 商品原单价 | 同正单计算逻辑 | 负数同正单计算逻辑 | | price |  | 商品售价 | 同正单计算逻辑 | 负数同正单计算逻辑 | | commodityCostPrice |  | 商品成本价 | → 默认是0? → 处理为0 |  | | totalAmount |  | 商品总额 | 负数同正单计算逻辑 |  | | discountShare |  | 优惠分摊 | 负数同正单计算逻辑 |  | | discountAmount |  | 折扣金额 | 负数同正单计算逻辑 |  | | billPrice |  | 下账单价 | 负数同正单计算逻辑 |  | | billAmount |  | 下账金额 | 负数同正单计算逻辑 |  | | **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** | refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  | rowNo |  | 商品行号 | 无->无 |  | platformSkuId | PlatformSkuId | 商品三方平台编码 | 无->无 |  | erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联条件XF_VOIDDOCNO and XF_STORECODE | erpName |  | 商品名称 | HANA_D.MAKTMAKTX 关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  | refundCount |  | 退款数量 | XF_TRANSSALESITEMXF_QTYSOLD |  | refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 | → 默认明细状态正常 |  | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  | originalPrice |  | 商品原单价 | 同正单计算逻辑 | 负数同正单计算逻辑 | price |  | 商品售价 | 同正单计算逻辑 | 负数同正单计算逻辑 | commodityCostPrice |  | 商品成本价 | → 默认是0? → 处理为0 |  | totalAmount |  | 商品总额 | 负数同正单计算逻辑 |  | discountShare |  | 优惠分摊 | 负数同正单计算逻辑 |  | discountAmount |  | 折扣金额 | 负数同正单计算逻辑 |  | billPrice |  | 下账单价 | 负数同正单计算逻辑 |  | billAmount |  | 下账金额 | 负数同正单计算逻辑 |  |
| **字段名** | **类型** | **字段解释** | **同步hana数据** | **备注** |
| refundNo | OfflineRefundNo | 内部退款单号 | → 内部生成 |  |
| rowNo |  | 商品行号 | 无->无 |  |
| platformSkuId | PlatformSkuId | 商品三方平台编码 | 无->无 |  |
| erpCode | OfflineErpCode | 商品编码 | XF_TRANSSALESITEMXF_PLU | 关联条件XF_VOIDDOCNO and XF_STORECODE |
| erpName |  | 商品名称 | HANA_D.MAKTMAKTX 关联字段 HANA_D.MAKT 表MATNR字段就是erp编码关联方式 XF_TRANSSALESITEM.XF_PLU= HANA_D.MAKT.MATNR |  |
| refundCount |  | 退款数量 | XF_TRANSSALESITEMXF_QTYSOLD |  |
| refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 | → 默认明细状态正常 |  |
| giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品NOT_GIFT - 非赠品 | → 默认非赠品 |  |
| originalPrice |  | 商品原单价 | 同正单计算逻辑 | 负数同正单计算逻辑 |
| price |  | 商品售价 | 同正单计算逻辑 | 负数同正单计算逻辑 |
| commodityCostPrice |  | 商品成本价 | → 默认是0? → 处理为0 |  |
| totalAmount |  | 商品总额 | 负数同正单计算逻辑 |  |
| discountShare |  | 优惠分摊 | 负数同正单计算逻辑 |  |
| discountAmount |  | 折扣金额 | 负数同正单计算逻辑 |  |
| billPrice |  | 下账单价 | 负数同正单计算逻辑 |  |
| billAmount |  | 下账金额 | 负数同正单计算逻辑 |  |


验证SQL

-- 迁移数据验证
YNHX_DATA01
GXHX_USERS
GZHX_USERS
SCHX_USERS
SXHX_USERS
CQHX_USERS
CDHX_USERS
SHHX_DATA01
TJHX_DATA01
HNHX_DATA01
HENHX_DATA01
SXGSHX_DATA01
TJQCHX_DATA01
HENNYHX_DATA01
ZYHX_USERS



-------------------验证数据正确性----------------------------

XF_TRANSSALESTOTAL
XF_TRANSSALESITEM
XF_TRANSSALESTENDER


-- 查主单信息
select 
XF_DOCNO as "单号",
XF_CREATETIME as "创单时间",
XF_NETAMOUNT as "实收金额", --172.4
-- 关联订单归属组织字段
XF_STORECODE as "门店编码",
-- 订单收银台信息
XF_TILLID as "pos收银台编码",
XF_CASHIER as "收银员编码",
XF_SALESMAN as "拣货员编码",
-- 会员卡号
XF_CLIENTCODE as "会员卡号" --530104104479
from CDHX_USERS.XF_TRANSSALESTOTAL 
where 
XF_DOCNO = 'S010000239' and XF_STORECODE = 'H687' --一个docNo对应很多门店,所以加上XF_STORECODE



-- 查订单归属组织信息 H687
select 
PLANT as "门店编码",
PLANT_TEXT as "门店名称",
COMP_CODE as "分公司Code",
ZC_GSJC as "分公司名称",
_BIC_ZC_INPRO as "门店直营加盟类型"
from HX_CRM.CV_STOREINFOR where PLANT = 'H687'


-- 查询订单归属会员信息
select 
"会员ID(会员编号)",
"姓名",
"会员卡ID(实物卡号)",
"移动电话"
from "HX_CRM"."TB_CRM_MATSTER_N" where "会员卡ID(实物卡号)" = '530104104479'



-- 查询支付信息 172.4
select 
XF_TENDERCODE,
XF_BASEAMOUNT
from CDHX_USERS.XF_TRANSSALESTENDER where XF_DOCNO = 'S010000239' and XF_STORECODE = 'H687'

-- 查询明细

select
XF_PLU as "商品编码",
XF_QTYSOLD as "商品数量",
XF_AMTSOLD as "下账金额",
XF_AMTSOLD as "商品总额",
XF_HXALLDISCLESS1 as "折扣金额",
XF_ITEMLOTNUM as "商品批号",
(XF_AMTSOLD/XF_QTYSOLD) as "**下账单价**",
(XF_AMTSOLD/XF_QTYSOLD) as "**商品实际售价**",
((XF_AMTSOLD+XF_HXALLDISCLESS1)/XF_QTYSOLD) as "**商品原单价**",
(XF_HXALLDISCLESS1/XF_QTYSOLD) as "**折扣单价(未乘以数量)**"
from 
CDHX_USERS.XF_TRANSSALESITEM where XF_DOCNO = 'S010000239' and XF_STORECODE = 'H687'


------------验证退货单号--
-- 73.9
select * from CDHX_USERS.XF_TRANSSALESTOTAL where XF_VOIDDOCNO='S010030097' and  XF_STORECODE = 'H976'
-- 对应的正单
select * from CDHX_USERS.XF_TRANSSALESTOTAL where XF_DOCNO='V010000066' and  XF_STORECODE = 'H976'


select
XF_PLU as "商品编码",
XF_QTYSOLD as "商品数量",
XF_AMTSOLD as "下账金额",
XF_AMTSOLD as "商品总额",
XF_HXALLDISCLESS1 as "折扣金额",
XF_ITEMLOTNUM as "商品批号",
(XF_AMTSOLD/XF_QTYSOLD) as "**下账单价**",
(XF_AMTSOLD/XF_QTYSOLD) as "**商品实际售价**",
((XF_AMTSOLD+XF_HXALLDISCLESS1)/XF_QTYSOLD) as "**商品原单价**",
(XF_HXALLDISCLESS1/XF_QTYSOLD) as "**折扣单价(未乘以数量)**"
from 
CDHX_USERS.XF_TRANSSALESITEM where XF_VOIDDOCNO='S010030097' and  XF_STORECODE = 'H976'


select
XF_PLU as "商品编码",
XF_QTYSOLD as "商品数量",
XF_AMTSOLD as "下账金额",
XF_AMTSOLD as "商品总额",
XF_HXALLDISCLESS1 as "折扣金额",
XF_ITEMLOTNUM as "商品批号",
(XF_AMTSOLD/XF_QTYSOLD) as "**下账单价**",
(XF_AMTSOLD/XF_QTYSOLD) as "**商品实际售价**",
((XF_AMTSOLD+XF_HXALLDISCLESS1)/XF_QTYSOLD) as "**商品原单价**",
(XF_HXALLDISCLESS1/XF_QTYSOLD) as "**折扣单价(未乘以数量)**"
from 
CDHX_USERS.XF_TRANSSALESITEM where XF_DOCNO='V010000066' and  XF_STORECODE = 'H976'






迁移主单逻辑