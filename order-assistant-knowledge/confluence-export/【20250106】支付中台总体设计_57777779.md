# 【20250106】支付中台总体设计

## 一、支付中台总体设计

支付中台边界定义：负责资金流向控制。不负责具体的资金存储和虚拟资产管理。

例如：通过支付中台充值，支付中台负责充的钱的去向，并在充值成功后通知相关系统，不负责具体的虚拟资产充值。

### 1.1 产品图

### 1.2 业务架构图

true支付业务架构图falseautotoptrue9042

### 1.3 核心业务分层交互流程图

true支付层级falseautotoptrue9113

### 二、业务细节

### 2.1 支付业务流程

收银台：收银台处于上游业务和支付引擎之间，上游业务和收银台进行交互，不关心具体支付逻辑，根据各个产品特性对外暴露功能接口；它负责谁把钱支付给谁的数据准备，封装后交由支付引擎完成后续操作。

支付引擎：只负责进行具体支付，不关心产品对外暴露情况，根据具体的配置参数路由到具体的支付渠道执行支付流程

支付记录：支付过程中产生的数据，清算系统、分账系统、分润系统只对接支付记录进行具体钱的分配

支付配置：管理接入应用、商户、商户支付配置等。

true支付业务图falseautotoptrue8412

### 2.2 支付\退款时序图

#### 2.2.1 正常支付流程

正常支付流程INLINE```
@startuml participant APP participant 交易中台 participant 支付中台 participant 余额系统 participant ERP系统 group 创建支付单 交易中台 -> 支付中台: 创建支付单 支付中台 -> 支付中台: 创建支付单 支付中台 -->> 交易中台: 返回支付单号 note right: 状态：待支付 end group 支付 APP -> 支付中台: 查询收银台数据 支付中台 -->> APP: APP -> 支付中台: 支付 支付中台 -> 支付中台:没有余额支付单信息 支付中台 -> 余额系统: 支付 余额系统 -> 余额系统: 创建余额支付单 余额系统 -->> 支付中台: 余额支付单信息 支付中台 -->> APP: 余额支付单信息     group sync 真实支付     APP -> 余额系统: 支付     余额系统 -> ERP系统: 扣减余额     ERP系统 -> ERP系统: 扣减余额     ERP系统 -> 余额系统:支付结果     余额系统 -->> APP:支付结果     end     group async 支付结果通知     余额系统 -> 支付中台:支付结果     支付中台 -> 支付中台:更新支付状态,记录支付账单     支付中台 -->> 交易中台:支付结果     end end @enduml
```

#### 2.2.2 支付中状态扭转

支付中状态INLINE```
@startuml participant APP participant 交易中台 participant 支付中台 participant 余额系统 participant ERP系统 group sync 真实支付 note left: 拉起支付页面 APP -> 余额系统: 支付 余额系统 -> ERP系统: 扣减余额 ERP系统 -> ERP系统: 扣减余额超时 ERP系统 -> 余额系统: 未知异常 end group async 支付结果通知 余额系统 -> 支付中台:支付中 支付中台 -> 支付中台:状态更新为支付中 支付中台 -->> 交易中台:支付中状态 end @enduml
```

#### 2.2.3 支付失败流程

支付失败流程INLINE```
@startuml participant APP participant 交易中台 participant 支付中台 participant 余额系统 participant ERP系统 group sync 真实支付 note left: 拉起支付页面 APP -> 余额系统: 支付 余额系统 -> ERP系统: 扣减余额 ERP系统 -> ERP系统: 明确异常 end group async 支付结果通知 余额系统 -> 支付中台:支付失败 支付中台 -> 支付中台:状态更新为待支付 支付中台 -->> 交易中台:待支付状态 end @enduml
```

#### 2.2.4 一个支付单支持多次支付执行流程

true重复支付falseautotoptrue5012

#### 2.2.5 普通退款流程

退款流程INLINE```
@startuml participant POS participant 交易中台 participant 支付中台 participant 余额系统 participant ERP系统 POS -> 交易中台:发起退款 交易中台 -> 支付中台:发起退款 支付中台 -> 支付中台:创建退款单,保存 支付中台 -> 余额系统:发起退款 余额系统 -> 余额系统:创建余额退款单 余额系统 -->> 支付中台:创建成功 支付中台 -->> 交易中台:退款中 group async 成功退款 余额系统 -> ERP系统:退款 ERP系统 -->> 余额系统: 退款成功 余额系统 -->> 支付中台: 退款成功 支付中台 -> 支付中台: 更新退款单状态,保存退款账单 支付中台 -->> 交易中台: 退款成功 note right: 失败重试5次 end group async 退款失败 余额系统 -> ERP系统:退款 ERP系统 -->> 余额系统: 退款失败 余额系统 -->> 支付中台: 退款失败 支付中台 -> 支付中台: 更新退款单状态,保存退款账单 支付中台 -> 支付中台: 告警 支付中台 -->> 交易中台: 退款失败 note right: 失败重试5次 end @enduml
```

### 2.3 状态机

#### 2.3.1 支付状态机

领域方法 → 状态机事件 → 领域事件(本地事件 、MQ,) 

true支付状态机falseautotoptrue93210

#### 2.3.2 退款状态机

true退款状态机falseautotoptrue5113

#### 2.3.3 分账状态

#### 2.3.4 分润状态

#### 2.3.5 结算状态

### 2.4 安全设计

#### 2.4.1 接口安全

暂无

### 2.5 数据一致性设计

#### 2.5.1 分布式锁

业务方主动发起的 : 如果同时有相同的单子在操作,直接失败,用户看见了可以再次操作

有系统发起的 : 等待锁释放后再操作,保证操作的不丢失

true支付分布式锁falseautotoptrue6212

#### 2.5.2 cas+version方式

| update table set columns = #{value} where version = #{version} |
| --- |


| if(!update()){ throw new PayBizNotHandleException("操作慢一点");} |
| --- |


#### 2.5.3 定时补偿

通过xxl-job 定时查询状态为非终态的订单,通过查询接口获取最新的状态

##### 2.5.3.1 支付单状态补偿

true支付单状态补偿falseautotoptrue7784

##### 2.5.3.2 退款单状态补偿

true退款补偿流程falseautotoptrue1211

#### 2.5.4 对账单对账

暂无

### 2.6 数据库设计

#### 2.6.1 容量评估

线下单： 3000000*52 ≈ 2亿 ，如果按每张表500万算 最少需要40张表 2的整数倍为56张。

线上单：微商城(8000) * 52 ≈ 50万 ，如果按每张表500万算，一张表即可。

#### 2.6.2 数据库设计

1. 支持每日百万级别订单量
2. 同一个支付单所关联的数据，同一个商户的所有数据不会跨库操作
3. 随着时间推移，按时间维度对数据进行归档，减少后期扩容表所带来的迁移问题。


true数据库设计falseautotoptrue12217

### 2.7 表概要设计

#### 2.7.1 配置表

获取配置策略

微商城- 微信支付-直营店: 使用子公司配置

微商城-微信支付-加盟店: 门店配置

保山医保: 门店配置

true配置数据表设计falseautotoptrue115317

#### 2.7.2 支付、退款表设计

当前表设计支持后期多维度的复杂支付单场景. 

支付单设计: 核心主表 + 业务子表设计 

退款单设计: 退款表通过父id实现父子单设计

支付场景维度: 店(支付商户)、支付渠道、支付方式、订单

退款场景维度: 店(支付商户)、支付渠道、订单

true支付表设计falseautotoptrue8219

### 2.8 流量激增处理方案

#### 2.8.1 核心业务非核心业务解耦实现非核心业务降级

Sentinel服务降级是基于接口实现的，针对性降级设计可控粒度更细。

- 将核心业务和非核心业务解耦、核心和非核心之间通过异步通知触发，就目前规划的应用场景通知可丢失。
- 非核心业务在流量激增时通过降级拦截器拦截需要执行的任务。


true降级策略falseautotoptrue4567

#### 2.8.2 支付渠道降级

当某个支付渠道频繁失败时，对齐进行降级处理

true渠道降级falseautotoptrue5711

#### 2.8.3 服务扩容

支付中台是一个无状态服务，支持横向扩展。借助外部流量监pod扩容能力，在流量激增时，对支付中台进行动态扩容。

### 2.9 支付集成测试框架

统一执行流程,关键节点预埋接口,实现各种场景快速构建各种测试用例.

true测试用例falseautotoptrue6313

### 2.10 异常设计

异常作用: 终端流程 、统一输出日志、统一返回结果

需要干预的异常: 执行过程中的未知异常、明确需要干预的异常

不需要干预的异常: 参数校验错误、支付单不存在、支付成功的单子不能继续支付

自定义不需要干预的异常,并重写GlobalExceptionHandler 忽略 参数校验等异常信息的error输出

### 2.11 操作日志记录组件

记录支付过程中入和出所有接口参数、返回值和异常.

通过数量和时间两个维度完成数据批量存储

true操作日志组件falseautotoptrue5414

### 2.12 风控系统

暂无