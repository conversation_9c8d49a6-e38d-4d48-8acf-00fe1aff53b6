# 2025-04-15 天猫B2C checklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 拼多多对接 | 1.third-platform-order-tm2.third-platform-callback-tm3.third-platform-logistics4.logistics-center5.third-platform-other6.hydee-third-inside (hydee-oms-logistic)7.hydee-business-order-web8.hydee-business-order-b2c-third9.hydee-api-gateway10.third-platform-gateway11.cloud-ui | 1.sdk打包2.需要新appllo配置 | 4998 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备 注 |
| --- | --- | --- |
| dscloud.code_value | ``` INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ( 'REFUND_PROCESSING_PLATFORM', '3002', '天猫', '2025-03-15 17:46:55'); ``` |  |
| dscloud | ``` update ds_online_client set access_type=2 where platform_code='3002'; ``` |  |
| order_config_center.express_merchant | INSERT INTO order_config_center.express_merchant (express_account_name, plat_code, status, link_type, config_info, created_by, updated_by, created_time, updated_time, version) VALUES ( '菜鸟云栈', 'CNYZ', 'OPEN', '3002', '{}', null, null, '2025-05-13 17:13:38', '2025-05-13 17:13:38', 1); |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| third-platform-callback-tm | application.yml | callback-message:  topicList:  - businessType: order  platformCodeList: ["3002"]  topic: TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-TM_CALLBACK  - businessType: system  platformCodeList: ["3002"]  topic: TP_SYSTEM_THIRD-PLATFORM-OTHER-CALLBACK-SYSTEM_CALLBACK  - businessType: logistics  platformCodeList: ["3002"]  topic: TP_LOGISTICS_THIRD-PLATFORM-LOGISTICS-CALLBACK-OTHER_CALLBACK | 1.订单消息回调2.授权回调3.物流回调 |
| third-platform-order-tm | application.yml | message:  topic: TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-TM_CALLBACK # tag区分，多个使用 || 分割，如：9002||11  tag: TAG_3002 notify-message:  #接口中台b2c订单消息  b2c-order-message-topic: TP_B2C_ORDER_THIRD-PLATFORM_ORDER  #接口中台b2c退款单  b2c-refund-order-message-topic: TP_B2C_REFUND_ORDER_THIRD-PLATFORM_ORDERcallback:third-platform-logistics delayLevel: 9 ## 控制消息延时推送 默认5分钟 pushMessage: false ## 是否推送拼多多消息 |  |
| hydee-api-gateway | application.yml | ## 天猫回调  - id: yxt-third-platform-callback-tm  uri: [lb://third-platform-callback](lb://third-platform-callback-pdd)-tm  predicates:  - Path=/third-platform/callback/3002/**  filters:  - StripPrefix=1 |  |
| third-platform-gateway | application.yml | ## 天猫  - id: third-platform-order-tm  uri: [lb://third-platform-order-](lb://third-platform-order-pdd)tm  predicates:  - Path=/third-platform/order/3002/**  - id: third-platform-goods-ptm  uri: [lb://third-platform-goods](lb://third-platform-goods)  predicates:  - Path=/third-platform/goods/3002/** |  |
| third-platform-other | application.yml | system:  topic: TP_SYSTEM_THIRD-PLATFORM-OTHER-CALLBACK-SYSTEM_CALLBACK  # tag区分，多个使用 || 分割，如：TAG_9002||TAG_11  tag: TAG_3008||TAG_11||TAG_3003||TAG_3002 |  |
| hydee-business-order-web | application.yml | message-notify:  #接口中台b2c订单消息  b2c-order-message-topic: TP_B2C_ORDER_THIRD-PLATFORM_ORDER  #接口中台b2c退款单消息  b2c-refund-order-message-topic: TP_B2C_REFUND_ORDER_THIRD-PLATFORM_ORDER  group:  b2c-order-message-topic: TP_B2C_ORDER_THIRD-PLATFORM_ORDER_MESSAGE  b2c-refund-order-message-topic: TP_B2C_REFUND_ORDER_THIRD-PLATFORM_ORDER_MESSAGEmigrated-config-list: - platform-code: "3002"  migrated-store-code-list: []#上线号更新为线上的网店编码 #是否走新物流中台配置oms:  b2c:  openStoreExpressMerId: 132,94,105,99,107,100,114,98,38,112,88,177,176,175,180#物流配置  logistic:  token: E2otsWWNMbs0hmNo9WiD  lists:  - platform-code: 3002  api-type: feign  url: [http://tb.b2c.oms.pt.ydjia.cn](http://tb.b2c.oms.pt.ydjia.cn) |  |
| hydee-business-order | application.yml | - platform-code: "3002"  migrated-store-code-list: [] #门店相关配置 天猫、拼多多、京东、药房网、平安健康、饿百、微商城 serverurl:  url3002: [https://eco.taobao.com/router/rest](https://eco.taobao.com/router/rest) |  |
| third-platform-logistics | application.yml | topic: TP_LOGISTICS_THIRD-PLATFORM-LOGISTICS-CALLBACK-OTHER_CALLBACK  tag: TAG_JD||TAG_JTSD||TAG_YZXB||TAG_YTO||TAG_ZTO||TAG_3003||TAG_3002 |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
| RocketMQ | TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-TM_CALLBACK | 天猫订单回调消息 |
| RocketMQ | TP_B2C_ORDER_THIRD-PLATFORM_ORDER | B2C订单消息 |
| RocketMQ | TP_B2C_REFUND_ORDER_THIRD-PLATFORM_ORDER | B2C退款单消息 |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


#### 2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
| ``` yxt-third-platform-sdk-common项目SDK版本更新 ``` | ``` 1.2.6-RELEASE -> 1.2.7-RELEASE ``` |  |
| ``` yxt-third-platform-sdk-logistics项目SDK版本更新 ``` | ``` 1.5.2-RELEASE -> 1.5.3-RELEASE ``` |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
| 授权回调（通过商家后台账号密码登录） | 测试：[https://oauth.taobao.com/authorize?response_type=code&client_id=35039658](https://oauth.taobao.com/authorize?response_type=code&client_id=27953965&redirect_uri=http://www.baidu.com&state=1212&view=web)[&redirect_uri=https://test-api.hxyxt.com/zeus/third-platform/callback/3002/500001/b595a374696645ae9ea134c65dd51ad2/auth&](https://fuwu.pinduoduo.com/service-market/auth?response_type=code&client_id=b9ff94a84c7b450b940c7a0042f45584&redirect_uri=https://test-api.hxyxt.com/zeus/third-platform/callback/3003/500001/2faadef5b3bd46869fddae37a7119f39/auth&state=42)[&state=1212&view=web](https://oauth.taobao.com/authorize?response_type=code&client_id=27953965&redirect_uri=http://www.baidu.com&state=1212&view=web)线上：[https://oauth.taobao.com/authorize?response_type=code&client_id=11111111](https://oauth.taobao.com/authorize?response_type=code&client_id=27953965&redirect_uri=http://www.baidu.com&state=1212&view=web)[&redirect_uri=https://api.hxyxt.com/zeus/third-platform/callback/3002/500001/{}/auth&](https://fuwu.pinduoduo.com/service-market/auth?response_type=code&client_id=b9ff94a84c7b450b940c7a0042f45584&redirect_uri=https://test-api.hxyxt.com/zeus/third-platform/callback/3003/500001/2faadef5b3bd46869fddae37a7119f39/auth&state=42)[&state=1212&view=web](https://oauth.taobao.com/authorize?response_type=code&client_id=27953965&redirect_uri=http://www.baidu.com&state=1212&view=web) |  |
| 拉取电子面单网点、电子面单模板、自定义区域模板 重配店铺物流配置 同步到物流中台 |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |