# 【20231208】经营分析数据同步

## 一、 背景

### 1.1 业务背景

 从雨诺迁移过来的O2O订单，没有同步到经营分析数据库，导致领导不能通过经营分析功能查看门店的销售情况。

### 1.2 痛点分析

### 1.3 系统现状

## 二、 需求分析

### 2.1 业务流程

### 2.2 需求功能点

1. 经营分析数据同步


 将雨诺OMS的O2O订单同步到Doris数据库

 2.经营分析数据的要求

平台销售统计按照订单下单时间统计O2O订单的相关金额，且通过溯源到订单下单时间方式扣除了订单的退款金额。
统计数据范围：只统计O2O订单数据，即通过O2O模块完成订单履约的数据；
统计时间：按照订单的下单时间统计
统计口径：统计有效订单的相关金额指标，有效订单为排除已取消、已关闭订单状态的其他所有订单；
退款金额扣减方式：通过溯源到订单下单时间方式扣除了订单的退款金额；

举例：
下单时间2022-6-30：订单1001-商品A销售金额100元；此时统计2022-6-30的商品金额为100元。
退款申请时间2022-7-1：订单1001-商品A退款了30元；此时统计2022-6-30的商品金额为70元。（100-30=70）

 3.B2C和云仓订单不导入经营分析

## 三、 目标

### 3.1 本期目标

#### 3.1.1 业务目标

#### 3.1.2 技术目标

### 3.2 中长期目标

愿景、长远规划


## 四、整体设计

### 4.1统一语言定义

业务、技术名词解释等

### 4.2 设计流程

**true未命名绘图falseautotoptrue7288**

## 1、平台销售统计

### 支付金额计算逻辑（经营分析直接使用Doris表的字段数据）

| 经营数据中文名字 | 订单总金额 | 订单笔单价 | 买家实付金额 | 买家实付笔单价 | 商家实收金额 | 商家实收笔单价 | 有效订单数 | 商品总金额 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 对应的doris表字段 | orderTotalAmount | orderUnitPrice | buyerActualAmount | buyerActualAmountUnitPrice | merchantActualAmount | merchantActualAmountUnitPrice | availableCount | commodityTotalAmount |
| 计算逻辑 | total_amount  + merchant_delivery_fee  + platform_delivery_fee  + merchant_pack_fee  + platform_pack_fee  - r_total_food_amount  - r_merchant_refund_post_fee  - r_merchant_refund_pack_fee  - r_platform_refund_pack_fee  - r_platform_refund_delivery_fee | orderTotalAmount / availableCount | buyer_actual_amount  - r_consumer_refund | buyerActualAmount/availableCount | merchant_actual_amount - r_shop_refund | merchantActualAmount/availableCount | order_num（常量1） | total_amount  - r_total_food_amount |
| 计算逻辑 | 商品总金额 +商家配送费 +平台配送费 +商家打包费 +平台打包费 -退单商品总金额 -退单商家配送费 -退单商家打包费 -退单平台打包费 -退单平台配送费```  ``````  ``` | 商品总金额/有效订单数 | 客户实付 - 客户退款 | 买家实付金额 / 有效订单数 | 商家实收 - 商家退款 | 商家实收金额 / 有效订单数 | 有效订单总数量（已成功创建的订单，不包含已取消，已关闭的订单） | 商品总金额- 退款商品总金额 |
| 心云平台对经营 数据的文字定义 | 商品总金额 +商家配送 +平台配送费 +商家包装费 +平台包装费 | 订单总金额 / 有效订单数 | 有效订单的买家实付总金额 | 买家实付金额 / 有效订单数 | 商品总金额 - 商家优惠金额  + 商家配送费  + 商家包装费 - 交易佣金  - 商品明细优惠金额 | 商家实收金额 / 有效订单数 | 商家接单后，未被取消或关闭的订单数 | 订单商品明细的商品金额汇总 |


### 支付金额数据来源（经营分析Doris表字段数据来源）

| 经营分析字段 | 来源于表 | 来源于字段 | 字段中文名 | 雨诺订单迁移提供的值 | 心云平台提供的值 |
| --- | --- | --- | --- | --- | --- |
| total_amount | order_pay_info | total_amount | 商品总金额 | Total_fee（商品金额） | total_fee（商品金额） |
| merchant_actual_amount | order_pay_info | merchant_actual_amount | 商家实收 | 0（正确值：totalFee -merchantDiscountSum +merchantDeliveryFee +merchantPackFee -brokerageAmount -detailDiscountCollect） | totalFee -merchantDiscountSum +merchantDeliveryFee +merchantPackFee -brokerageAmount -detailDiscountCollect商品总金额 - 商家优惠金额 + 商家配送费 + 商家包装费 - 佣金 - 商品明细折扣汇总 |
| merchant_delivery_fee | order_pay_info | merchant_delivery_fee | 商家配送费 | Post_fee（邮费) - plattranscoupon（平台承担运费优惠） | deliveryFee(配送费) |
| platform_delivery_fee | order_pay_info | platform_delivery_fee | 平台配送费 | 0 | 0 |
| merchant_pack_fee | order_pay_info | merchant_pack_fee | 商家打包费 | packagefee（打包费） | package_fee（打包费） |
| platform_pack_fee | order_pay_info | platform_pack_fee | 平台打包费 | 0 | 0 |
| brokerage_amount | order_pay_info | brokerage_amount | 交易佣金 | servicefee（服务费） |  |
| merchant_discount_sum | order_pay_info | merchant_discount_sum | 商家优惠 | couponsamount（优惠金额(商家承担的货品优惠金额)） |  |
| platform_discount | order_pay_info | platform_discount | 平台优惠 | couponamountOrigin -couponsamount +plattranscoupon 原始优惠-商家承担+平台承担运费优惠 |  |
| discount_fee_dtl | order_pay_info | discount_Fee_Dtl | 优惠金额(商家承担的货品优惠金额) | couponsamount |  |
| r_total_food_amount | order_refund_statistics | total_food_amount | 退款商品总金额 | 0（正确值：从RefundOrder同步） | refundOrder.totalFoodAmount（退款商品总金额） |
| r_shop_refund | order_refund_statistics | shop_refund | 商家退款总金额 | 0（正确值：从RefundOrder同步） | refundOrder.shopRefund(商家退款总金额) |
| r_merchant_refund_post_fee | order_refund_statistics | merchant_refund_post_fee | 退商家配送费 | 0（正确值：从RefundOrder同步） | refundOrder.merchantRefundPostFee( 退商家配送费) |
| r_merchant_refund_pack_fee | order_refund_statistics | merchant_refund_pack_fee | 退商家包装费 | 0（正确值：从RefundOrder同步） | refundOrder.merchantRefundPackFee( 退商家包装费) |
| r_platform_refund_pack_fee | order_refund_statistics | platform_refund_pack_fee | 退平台配送费 | 0（正确值：从RefundOrder同步） | refundOrder.platformRefundDeliveryFee(退平台配送费) |
| r_platform_refund_delivery_fee | order_refund_statistics | platform_refund_delivery_fee | 退平台包装费 | 0（正确值：从RefundOrder同步） | refundOrder.platformRefundPackFee(退平台包装费) |


## 2、零售下账统计

### 下账金额计算逻辑（经营分析直接使用Doris表的字段数据）

| 经营数据中文名字 | 下账总金额 | 已下账订单数 | 下账笔单价 | 下账商品金额 | 下账商品成本 | 商品毛利额 | 商品毛利率 | 综合营收 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 对应的doris表字段 | bBillTotalAmount | orderCount | billUnitPrice | commodityBillTotal | commodityCostTotal | grossProfitAmount | grossProfitPercent | grossProfitAmountComprehensive |
| 计算逻辑 | b_bill_total_amount  - rb_refund_merchant_total | order_num（常量1） | billTotalAmount/orderCount | commodity_bill_total(erp_bill_info.bill_commodity_amount - erp_refund_info.refund_goods_total) | commodity_cost_total | commodityBillTotal - CommodityCostTotal | grossProfitAmount/ CommodityBillTotal | commodityBillTotal  +bill_amount_comprehensive - commodityCostTotal |
| 计算逻辑 | 下账总金额 -下账商家退款总金额 | 订单总数量（下账状态为100的订单，下账成功） | 下账总金额 / 已下账订单数 | 下账商品总金额  - 下账退款商品总金额 | 下账商品总成本 | 下账商品金额 - 下账商品成本 | 商品毛利额 / 下账商品金额 | 下账商品金额 +参与计算综合毛利的下账金额 -下账商品成本 |
| 心云平台对经营 数据的文字定义 | 下账商品金额 +商家配送费 +平台配送费 +商家包装费 +平台包装费 +平台收取佣金 +商家优惠金额 +平台优惠金额 +商品明细优惠金额 | 已下账至ERP且未被取消或关闭的订单数 | 下账总金额 / 已下账订单数 | 下账给ERP的商品总金额，等于订单商品明细的下账金额汇总 | 如果未获取到ERP零售下账返回的成本，则取商品中台的加权平均成本；反之，则取ERP零售下账返回的成本 | 下账商品金额  - 商品成本（ERP零售下账返回的成本） | 商品毛利额 / 下账商品金额 | 下账商品金额 + 门店下账配置中设置为需要计算毛利的下账金额 |


### 下账金额数据来源（经营分析Doris表字段数据来源）

| 经营分析字段 | 来源于表 | 来源于字段 | 字段中文名 | 雨诺订单迁移提供的值 | 心云平台提供的值 |
| --- | --- | --- | --- | --- | --- |
| b_bill_total_amount | erp_bill_info | bill_total_amount | 下账总金额 | accountAmount（下账金额）雨诺已经计算好的商品总额 +配送费 -商家承担运费优惠 -商家承担的货品优惠 | 商品明细的下账金额汇总 +商家配送 +平台配送费 +商家包装费 +平台包装费 +商家优惠金额 +平台优惠金额 +商品明细优惠金额 +平台收取佣金 |
| commodity_cost_total | erp_bill_info | commodity_average_price | 商品加权成本 | 0 | 0 |
| bill_amount_comprehensive | erp_bill_info | bill_amount_comprehensive | 参与计算综合毛利的下账金额 | 0 | 0 |
| rb_refund_merchant_total | erp_refund_info | refund_merchant_total | 下账商家退款总金额 | consumerRefund （退买家总金额） | shopRefund 商家退款总金额 |
| commodity_bill_total | erp_bill_info erp_refund_info | erp_bill_info.bill_commodity_amount - erp_refund_info.refund_goods_total | 下账商品总金额 - 下账退款商品总金额 | settlement_amount - consumerRefund  计算金额 - 退买家总金额 | 商品明细的下账金额汇总 - 退款商品总金额 |


merchantDeliveryFee 

| 商品成本 | 预估毛利额 | 预估毛利率 | 商家配送费 | 平台配送费 | 商家包装费 | 平台包装费 | 交易佣金 | 商家优惠金额 | 平台优惠金额 | 商家明细优惠金额 | 无效订单数 | 无效订单率 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| commodityCostTotal | estimatedGrossProfitAmount | estimatedGrossProfitPercent | merchantDeliveryFee | platformDeliveryFee | merchantPackFee | platformPackFee | brokerageAmount | merchantDiscountSum | platformDiscount | discountFeeDtl | unAvailableCount | unAblePercent |
| commodity_cost_total | merchantActualAmount-commodityCostTotal | estimatedGrossProfitAmount/ merchantActualAmount | merchant_delivery_fee-r_merchant_refund_post_fee | platform_delivery_fee-r_platform_refund_delivery_fee | merchant_pack_fee-r_merchant_refund_pack_fee | platform_pack_fee-r_platform_refund_pack_fee | brokerage_amount-r_fee_refund | merchant_discount_sum-r_shop_discount_refund | platform_discount-r_platform_discount_refund | discount_fee_dtl-r_detail_discount_amount | 订单状态是已完成 | 订单状态是已完成 |
|  | 商家实收金额（包括商家收取的配送费和包装费）- 商品成本（如果未获取到ERP零售下账返回的成本，则取商品中台的加权平均成本） |  |  |  |  |  |  |  |  |  |  |  |


### 已切店，未切店，确定经营数据来源？

已切店微商城订单： 

 历史订单：全部走推送逻辑，add操作。

 新订单：每次订单状态更新，走推送逻辑。

 如果是新增订单，就add

 如果是取消订单，就update

未切店微商城订单：目前代码逻辑是没有推送到Doris

 添加逻辑：

 创单：推送到雨诺OMS成功后，推送到Doris

 退单：雨诺OMS同意退款后，心云接收到退款状态，推送到Doris

## 五、 详细设计

### 1、 模块详细设计

本次需求基于原有流程上进行改造

| 项目 | 分支 | 合并请求 |
| --- | --- | --- |
| hydee-business-order | feature-operate-analyse |  |


### 2、 存储数据库设计

涉及到的数据库表

 源数据：
 

| 表中文名 | 表名字 |
| --- | --- |
| 订单数据 | order_info |
| 支付数据 | order_pay_info |
| 下账数据 | erp_bill_info |
| 订单退款预处理表 | order_refund_statistics |
| 退单下账 | erp_refund_info |
| 退款单 | refund_order |


 目标数据：

| 表中文名 | 表名字 |
| --- | --- |
| 零售下账统计 | doris_order_bill_info |
| 平台销售统计 | doris_order_pay_info |


### 3、 接口设计

设计到的程序接口

接口要求：

 1、按照门店来同步，单个门店O2O订单历史数据最大约1万，每次同步完一个门店就打印日志“xxx门店，O2O总订单xx条，成功xx条，失败xx条”。

 2、接口支持按照门店重复导入，代码会自动判断订单是否已经同步过

 3、接口触发方式

 已经切店的场景： 通过手动通过postman发起request请求，触发同步任务

 未切店的场景： 在创单和退单节点去同步经营数据

 未来做切流工作场景：在切流同步订单数据完成后，触发经营数据接口

//正向单
orderDorisService.pushMessage2Kafka(订单编号List);

//逆向单
orderDorisService.pushMessage2KafkaForCancelBill(订单编号List);

订单编号List数据来源：

1. 正向单的数据来源： select order_no from order_info where migration_order_no is not null
2. 逆向单的数据来源： select order_no from order_info where migration_order_no is not null and order_state = 102


### 4、查询经营数据

**1.平台销售统计**

sqlDJangoselect orderTotalAmount  订单总金额, ROUND(orderTotalAmount/availableCount,2) 订单笔单价,buyerActualAmount 买家实付金额,round(buyerActualAmount/availableCount,2) 买家实付笔单价,
       merchantActualAmount 商家实收金额,round(merchantActualAmount/availableCount,2)商家实收笔单价,availableCount 有效订单数,commodityTotalAmount 商品总金额
from (select sum(case when order_state > 100 then 0 else order_num end)                       as availableCount,
             sum(case
                     when order_state > 100 then 0
                     else total_amount + merchant_delivery_fee + platform_delivery_fee + merchant_pack_fee +
                          platform_pack_fee
                         - r_total_food_amount - r_merchant_refund_post_fee - r_merchant_refund_pack_fee -
                          r_platform_refund_pack_fee -
                          r_platform_refund_delivery_fee end)                                    orderTotalAmount,
             sum(case
                     when order_state > 100 then 0
                     else buyer_actual_amount - r_consumer_refund end)                        as buyerActualAmount,
             sum(case
                     when order_state > 100 then 0
                     else merchant_actual_amount - r_shop_refund end)                         as merchantActualAmount,
             sum(case
                     when order_state > 100 then 0
                     else total_amount - r_total_food_amount end)                             as commodityTotalAmount

      from doris_order_pay_info
      where third_order_no in ('4094160135150630640','1100818553448321564','1100818562794647095','1100818531634175639')) as cc;




查询结果：

#### 

#### 2.零售下账统计

sqlDJangoselect billTotalAmount 下账总金额,orderCount 已下账订单数,round(billTotalAmount/orderCount,2) 下账笔单价, commodityBillTotal 下账商品金额, commodityCostTotal 下账商品成本,
       (commodityBillTotal - commodityCostTotal) 商品毛利额, round((commodityBillTotal - commodityCostTotal)/commodityBillTotal,2) 商品毛利率,
       (commodityBillTotal+billAmountComprehensive-commodityCostTotal) 综合营收
from (
    select sum(order_num)                                                 as orderCount,
       sum(b_bill_total_amount - rb_refund_merchant_total)            as billTotalAmount,
       sum(commodity_bill_total)                                      as commodityBillTotal,
       sum(commodity_cost_total)                                      as commodityCostTotal,
       sum(bill_amount_comprehensive)                                 as billAmountComprehensive
from doris_order_bill_info
 where third_order_no in ('1100830613577185708','1100830562399788161','1100830512537666444')
) as cc   

#### 查询结果：

#### 4、 安全设计

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

项目工时、分工等

| 模块 | 功能点 | 开发 | 工时 | 开始时间 | 结束时间 | 完成度 |
| --- | --- | --- | --- | --- | --- | --- |
| 订单数据迁移 | 出设计文档 | 崔建波 | 8 |  |  |  |
| 开发程序同步接口 | 崔建波 | 2 |  |  |  |
| 测试自调 | 崔建波 | 2 |  |  |  |


## 九、 上线方案