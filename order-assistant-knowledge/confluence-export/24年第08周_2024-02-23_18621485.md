# 24年第08周 2024-02-23

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5day**1. [V 1.5.1 版本（日常迭代）](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18617985)店铺管理模块功能开发 2. 拼多多B2C支持 3. ds_online_store_delivery重复数据清除sql编写 | **㊀计划工作**1. 店铺同步信息优化 2. 店铺自动化配置优化 3. 拼多多B2C上线 **㊁实际完成**1. 店铺同步信息优化 - 测试中 2. 店铺自动化配置优化 - 测试中 3. 拼多多B2C - 待上线 4. ds_online_store_delivery重复数据清除sql编写 - 待评审 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）**1. **SQL注意事项** 2. **SpringBoot中的多线程开发** | **㊀需求研发相关**1. pos 地址维护/运费下账 2. V1.5.1 上线 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 | @王世达 | **本周总工时：5 day**1. 海典H2联调测试 2. 医保支付获取省/市医保标识 | **㊀计划工作**1. 海典H2联调测试 2. 医保支付获取省/市医保标识 **㊁实际完成**1. 海典H2联调测试 目前O2O无下账相关bug B2C组合商品待处理 原流程Bug部分转需求。 2. 医保支付获取省/市医保标识 支付通道到小前台链路使用mock数据已自测。 **㊂遗留问题******1.海典H2 库存异常返回格式待于海典同步沟通 2. 医保支付获取省/市医保标识 小前台到oms中台链路待处理。 科传增加医保标识待处理**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 3 | @杨国枫 | **本周总工时：5 day**1. 内购商城订单部分 2. O2O订单占用释放库存优化（70%） | **㊀计划工作**1. 内购商城订单部分 2. O2O订单占用释放库存优化（70%） **㊁实际完成**1. 内购商城订单部分 2. O2O订单占用释放库存优化（70%） **㊂遗留问题**1. 内购商城-新活动库存占用 2. O2O订单占用释放库存优化 **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 4 |  | **本周总工时：5day**1. 内购商城联调 2. 线上问题支持 3. 下账时机修改技术方案编写 | **㊀计划工作**1. 内购商城联调 2. 线上问题支持 3. 下账时机修改技术方案编写 **㊁实际完成****㊂遗留问题**1. 内购商城订单流程下发 2. 下账时机修改及生成运费单 **㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 内购商城配合测试 2. 下账时机修改及运费单的生成 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 5 | @杨俊峰 | **本周总工时：7day**1. 产线问题协助处理 2.5day 2. .NET CORE 服务容器化 1.5day 3. 小修改 1.5 day 4. B2C拼多多面单打印 1.5day | **㊀计划工作****㊁实际完成****㊂遗留问题****1.容器话部署后中文乱码。目前还没有找到问题。****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 6 | @李洋 | **本周总工时：5 day**1. 海典H2联调测试 | **㊀计划工作**1. 海典H2联调测试 2. 订单拣货添加批号校验 **㊁实际完成**1.海典H2联调测试**㊂遗留问题**1. 订单拣货添加批号校验 **㊃风险问题**1. H2非下账bug：饿百多次退款产生多个退款单问题 2. H2非下账bug：美团赠品单价为0 **㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 订单拣货添加批号校验 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 7 | @杨润康 | **本周总工时：5 day**1. 年前头脑风暴V1.0落文档 2. 文档 3. 网关监控数据处理(网关升级) 4. 线上问题排查、下账失败提示需求 5. 年前的经营数据已刷完 6.  7. DDD项目新建(进行中) | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. 优雅发布订单全部推上线 2. DDD项目搭建(需包含分库分表) |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 切换值班

| 时间 | 值班人员 |  |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |