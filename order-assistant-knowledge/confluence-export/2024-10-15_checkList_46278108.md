# 2024-10-15 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 京东快递 | hydee-oms-logistic |  |  |  |  |  |  |  | 2024-10-15 |  |
| 首单优惠订单生成退款明细优化 | ``` hydee-business-order-web hydee-business-order-b2c-third ``` |  |  |  |  |  |  |  | 2024-10-15 |  |
| 修复线上单消息推送Bug | hydee-business-order |  |  |  |  |  |  |  | 2024-10-15 |  |
| 适配order_info逻辑删除字段,同步删除es索引数据 | order-atom-service |  |  |  |  |  |  |  | 2024-10-15 |  |
| 京东到家-商家承担费用中排除商家支付远距离运费 | third-platform-order-jddj |  |  |  |  |  |  |  |  |  |
| 1 美团自动接单优化 2 到店自提不需要调用拣货完成接口 3 美团拒绝退款 操作失败的问题 | third-platform-order-mt |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
|  | ``` -- 快递商配置显示京东快递 update dict_express_merchant set express_merchant_used = 1 where id = 74; -- 京东快递商动态表单配置 INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES ( null, null, null, null, 'EXPRESS_MERCHANT:JD', 'accessToken', 'AccessToken', '请输入AccessToken', 'input', 3, 1, 1, null, null, 'a8b24fe44cfc4e5bbc1be1c9f4833b1b', 1, null); INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES ( null, null, null, null, 'EXPRESS_MERCHANT:JD', 'appKey', 'AppKey', '请输入AppKey', 'input', 1, 1, 1, null, null, '603FE0D5D4A60E1144D6EAE5009A505A', 1, null); INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES ( null, null, null, null, 'EXPRESS_MERCHANT:JD', 'appSecret', 'AppSecret', '请输入AppSecret', 'input', 2, 1, 1, null, null, 'd61d49e3e8d34c83af63dede9ae54602', 1, null); INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES ( null, null, null, null, 'EXPRESS_MERCHANT:JD', 'lopdn', 'LOP-DN', '请输入LOP-DN', 'input', 4, 1, 1, null, null, 'ECAP', 1, null); -- 京东快递动态表单配置 `````` update oms_custom_attributes set column_name = '客户编码' ,placeholder = '请输入客户编码' where id = 148; ``` |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| hydee-oms-logistic |  | jd:  baseurl: [https://api.jdl.com](https://api.jdl.com)  preCheck: /ecap/v1/orders/precheck  create: /ecap/v1/orders/create  cancel: /ecap/v1/orders/cancel | 京东快递接口地址 |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | Tag | MQ名称 | 备注 |
| --- | --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |