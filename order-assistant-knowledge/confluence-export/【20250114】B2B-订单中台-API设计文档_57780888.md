# 【20250114】B2B-订单中台-API设计文档

# 订单中台 → 一心助手\心云

## 枚举统计

| 枚举 | 枚举说明 | 枚举值 | 备注 |
| --- | --- | --- | --- |
| OrderSearchConditionEnum | 订单查询条件类型 | STORE_CODE("线上门店编码,支持多个"), ORG_CODE("实际发货机构编码,支持多个"), SOURCE_ORG_CODE("下单机构编码,支持多个"), ORDER_START_CREATED("开始时间(订单创建时间)"), ORDER_END_CREATED("结束时间(订单创建时间)"), ORDER_NO("订单号,支持多个"), THIRD_ORDER_NO("三方订单号,支持多个"), ERP_INFO("商品编码/商品名称"), ORG_REL("实际发货机构编码/下单机构编码,支持多个"), PLATFORM("平台编码,支持多个"), ORDER_FLAG("订单标记"), ORDER_FLAG_AND("订单标记(多个，and查询)"), ORDER_FLAG_OR("订单标记(多个，or查询)"), ORDER_STATUS("订单状态,具体状态值见OrderMainStatus,支持多个"), ORDER_PAYMENT_STATUS("订单支付状态,具体状态值见OrderPaymentStatus,支持多个"), ORDER_DELIVERY_STATUS("订单发货状态,具体状态值见OrderDeliveryStatus,支持多个"), AFTER_SALE_STATUS("订单售后状态,具体状态值见AfterSaleStatus,支持多个"), BUSINESS_TYPE("业务类型,O2O/B2C/JOIN_B2B"), USER_ID("会员id,支持多个"),LAUNCHED_ORG_CODE("下单发起方机构编码") |  |
| OrderMainStatus | 订单状态 | CREATED("CREATED","创建订单"), ACCEPTED("ACCEPTED","已接单"), PICKED("PICKED","已拣货"), SHIPPED("SHIPPED","已发货"), RECEIVED("RECEIVED","已送达"), DONE("DONE","已完成"), CANCELED("CANCELED","已取消"), CLOSED("CLOSED","已关闭"), |  |
| OrderPaymentStatus | 订单支付状态 | UN_PAY("UNPAY", "未支付"), PART_PAY("PART_PAY", "部分支付"), PAID("PAID", "已支付"), PAY_FAIL("PAY_FAIL", "支付失败"); |  |
| PlatformCodeEnum | 平台编码 | HAIDIAN-海典KE_CHUAN-科传 JD_DAOJIA-京东到家MEITUAN-美团E_BAI-饿百YD_JIA-微商城DOUDIAN-抖店 ZHIFUBAO-支付宝小程序 KUAI_SHOU-快手ASSIST_J_B2B - 一心助手加盟B2B |  |
| OrderFlagEnum | 订单标记 | EXCEPTION("该订单为异常订单"), PRESCRIPTION("该订单为处方订单"), ROUTE("订单路由"), REFUNDING("订单退款中"), INTEGRAL("积分订单"), MEDICAL("医保订单") |  |
| BusinessType | 业务类型 | O2O("O2O"), B2C("B2C"), JOIN_B2B("JOIN_B2B"), |  |
| OrderQueryScaleEnum | 订单查询范围 | MAIN( "主信息"), DETAIL( "明细信息"), PAY( "支付信息"), USER("订单会员信息"), BILL( "下账信息"), BILL_DETAIL( "下账明细"), LOGISTICS("快递信息"), RECEIVE_INFO("收货信息"), DELIVERY_ORDER("发货单信息"), DELIVERY_DETAIL("发货单明细（包含拣货信息）") |  |
| OrderDeliveryStatus | 发货状态 | WAIT_VERIFY("WAIT_VERIFY","待审核"), WAIT_SHIP("WAIT","待发货"), PART_SHIPPED("PART_SHIPPED","部分发货"), SHIPPED("SHIPPED","已发货"), COMPLETE("RECEIVED","已签收"), | 暂定 |
| DeliveryTypeEnum | 配送方式 | ``` PLAT("1", "平台配送"), PLAT_THIRD("2", "平台合作方配送"), SELLER_SELF("3", "门店配送"), BUYER_SELF("4", "到店自提"), EXPRESS("5", "快递"); ``` |  |
| AfterSaleOrderSearchConditionEnum | 退单查询条件 | STORE_CODE("线上门店编码，可多个"), ORG_CODE("实际发货机构编码，可多个"), SOURCE_ORG_CODE("下单机构编码，可多个"), ORG_REL("实际发货机构编码/下单机构编码，可多个"), START_DATE("开始时间(售后单创建时间)"), END_DATE("结束时间(售后单创建时间)"), AFTER_SALE_NO("售后单号，可多个"), THIRD_AFTER_SALE_NO("三方售后单号，可多个"), ERP_INFO("商品编码/商品名称"), PLATFORM("平台编码，可多个"), AFTER_SALE_FLAG("售后单标记"), AFTER_SALE_FLAG_AND("售后单标记(支持传多个，使用and查询)"), AFTER_SALE_FLAG_OR("售后单标记(支持传多个，使用or查询)"), ORDER_NO("订单号，可多个"), AFTER_SALE_STATE("售后单状态，可多个"), RETURN_STATE("退货状态，可多个"), REFUND_STATE("退款状态，可多个"), BUSINESS_TYPE("业务类型,O2O/B2C/JOIN_B2B"), AFTER_SALE_SCOPE("售后范围 PART-部分 ALL-全部"), AFTER_SALE_TYPE("售后单类型: 取消订单、退款、退货、换货、维修"), USER_ID("会员id,支持多个"),LAUNCHED_ORG_CODE("下单发起方机构编码") |  |
| AfterSaleFlagEnum | 售后单标记 | ``` NO_ORDER("无正单"); ``` |  |
| AfterSaleStatus | 售后单状态 | WAIT_VERIFY("WAIT_VERIFY","待审核"), PASSED("PASSED","审核通过,处理中"), COMPLETE("COMPLETE","售后完成"), REJECTED("REJECTED","售后拒绝"), CANCELED("CANCELED","售后取消"), |  |
| AfterSaleQueryScaleEnum | 售后单查询范围 | ```   MAIN("售后主信息"),   DETAIL("售后单明细"),   ERP("售后单下账信息"),   PAY( "售后单金额信息"),   CHECK_INFO("售后单审核信息"),   RETURN("退货单"),   REFUND("退款单") ``` |  |
| ReturnStatus | 退货状态 | WAIT_RETURN("WAIT_RETURN","待退货"), RETURN_SHIPPED("RETURN_SHIPPED","退货运输中"), COMPLETED("COMPLETED","退货验货通过"), REJECTED("REJECTED","退货拒绝"), CANCELED("CANCELED","取消"); |  |
| RefundStatus | 退款状态 | REFUNDING("REFUNDING","退款中"), COMPLETED("COMPLETED","已退款"), REFUND_FAIL("REFUND_FAIL","退款失败"), CANCELED("CANCELED","取消"); |  |


## 正单

### 正单数量统计

1. URL:/1.0/order/count/statistic
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| searchConditionList |  | 筛选条件 | List<Object> | 否 |  |
|  | searchType | 查询条件类型，见 OrderSearchConditionEnum | String |  |  |
|  | searchData | 条件值 | String |  |  |
| statisticType |  | 聚合条件，可用值： **ORDER_STATUS**：根据订单状态统计，订单状态见 OrderStatusEnum | string | 是 |  |
| **请求示例** |
| RDarktrue{     "searchConditionList":[         {             "searchType":"PLATFORM",             "searchData":"ASSIST_J_B2B"         }     ],     "statisticType":"ORDER_STATUS" } |
3. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| searchConditionList |  | 筛选条件 | List<Object> | 否 |  |
|  | searchType | 查询条件类型，见 OrderSearchConditionEnum | String |  |  |
|  | searchData | 条件值 | String |  |  |
| statisticType |  | 聚合条件，可用值： **ORDER_STATUS**：根据订单状态统计，订单状态见 OrderStatusEnum | string | 是 |  |
| **请求示例** |
| RDarktrue{     "searchConditionList":[         {             "searchType":"PLATFORM",             "searchData":"ASSIST_J_B2B"         }     ],     "statisticType":"ORDER_STATUS" } |
4. 响应体：
  1. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| countList |  | 统计结果 | List<Object> |  |
|  | statisticKey | 聚合键 | String |  |
|  | statisticValue | 聚合值 | String |  |
| **响应示例** |
| RDarktrue{     "code": "10000",     "msg": "操作成功",     "subCode": null,     "subMessage": null,     "traceId": null,     "data": {         "countList": [             {                 "statisticKey": "77",                 "statisticValue": "3352"             },             {                 "statisticKey": "101",                 "statisticValue": "489"             }         ]     },     "timestamp": 1736913289688 } |
5. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| countList |  | 统计结果 | List<Object> |  |
|  | statisticKey | 聚合键 | String |  |
|  | statisticValue | 聚合值 | String |  |
| **响应示例** |
| RDarktrue{     "code": "10000",     "msg": "操作成功",     "subCode": null,     "subMessage": null,     "traceId": null,     "data": {         "countList": [             {                 "statisticKey": "77",                 "statisticValue": "3352"             },             {                 "statisticKey": "101",                 "statisticValue": "489"             }         ]     },     "timestamp": 1736913289688 } |
6. 备注：


### 正单分页查询

1. URL:/1.0/order/page/query
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| pageSize |  | 每页显示条数，不传默认20 | Long | 否 |  |
| currentPage |  | 当前页，从第1页开始，不传默认为1 | Long | 否 | 如果searchAfter不为空，则以searchAfter为准进行分页，currentPage无效 |
| searchAfter |  | 翻页参数为空查询第一页，后续从上一页响应体中获取 | String |  |
| queryScaleList |  | 查询订单信息范围 可用值：详见OrderQueryScaleEnum | List<String> | 是 |  |
| searchConditionList |  |  |  | 是 |  |
|  | searchType | 查询条件类型，详见 OrderSearchConditionEnum |  |  |  |
|  | searchData | 条件值，查询多个值可用英文逗号(,)拼接，如：A003,H812 |  |  |  |
| **请求示例** |
| {     "searchConditionList":[         {             "searchType":"PLATFORM",             "searchData":"ASSIST_J_B2B"         },         {             "searchType":"ORG_REL",             "searchData":"H571"         },         {             "searchType":"ORDER_FLAG",             "searchData":"REFUNDING"         }     ],     "queryScaleList":["MAIN","DETAIL","PAY","DELIVERY_ORDER","LOGISTICS"] } |
3. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| pageSize |  | 每页显示条数，不传默认20 | Long | 否 |  |
| currentPage |  | 当前页，从第1页开始，不传默认为1 | Long | 否 | 如果searchAfter不为空，则以searchAfter为准进行分页，currentPage无效 |
| searchAfter |  | 翻页参数为空查询第一页，后续从上一页响应体中获取 | String |  |
| queryScaleList |  | 查询订单信息范围 可用值：详见OrderQueryScaleEnum | List<String> | 是 |  |
| searchConditionList |  |  |  | 是 |  |
|  | searchType | 查询条件类型，详见 OrderSearchConditionEnum |  |  |  |
|  | searchData | 条件值，查询多个值可用英文逗号(,)拼接，如：A003,H812 |  |  |  |
| **请求示例** |
| {     "searchConditionList":[         {             "searchType":"PLATFORM",             "searchData":"ASSIST_J_B2B"         },         {             "searchType":"ORG_REL",             "searchData":"H571"         },         {             "searchType":"ORDER_FLAG",             "searchData":"REFUNDING"         }     ],     "queryScaleList":["MAIN","DETAIL","PAY","DELIVERY_ORDER","LOGISTICS"] } |
4. 响应体： ’
  1. | **响应参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 备注 |
| currentPage | 当前页 | List<Object> |  |
| pageSize | 每页显示条数，不传默认20 | Long |  |
| searchAfter | 翻页参数,JSON数组 | String |  |
| totalCount | 总条数 | Long |  |
| data | 数据（与订单详情返回结果一致） | Object |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": ,     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
5. | **响应参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 备注 |
| currentPage | 当前页 | List<Object> |  |
| pageSize | 每页显示条数，不传默认20 | Long |  |
| searchAfter | 翻页参数,JSON数组 | String |  |
| totalCount | 总条数 | Long |  |
| data | 数据（与订单详情返回结果一致） | Object |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": ,     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
6. 备注：


### 订单详情

1. URL:/1.0/order/info
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 是否必填 | 备注 |
| orderNo | 订单号 | String | 是 |  |
| queryScaleList | 查询订单信息范围 可用值：详见OrderQueryScaleEnum | List<String> | 是 |  |
| **请求示例** |
| {     "orderNo": "123458788",     "queryScaleList":["MAIN","DETAIL","PAY","DELIVERY_ORDER","LOGISTICS"] } |
3. | **请求参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 是否必填 | 备注 |
| orderNo | 订单号 | String | 是 |  |
| queryScaleList | 查询订单信息范围 可用值：详见OrderQueryScaleEnum | List<String> | 是 |  |
| **请求示例** |
| {     "orderNo": "123458788",     "queryScaleList":["MAIN","DETAIL","PAY","DELIVERY_ORDER","LOGISTICS"] } |
4. 响应体：
  1. | **响应参数** |
| --- |
| 参数名称 |  |  | 参数描述 | 数据类型 | 备注 |
| orderInfo |  |  | 订单主信息 | List<Object> |  |
|  | orderNo |  | 内部订单号 | String |  |
|  | parentOrderNo |  | 内部父订单号 | String |  |
|  | parentThirdOrderNo |  | 三方平台父订单号 | String |  |
|  | thirdOrderNo |  | 三方平台订单号 | String |  |
|  | orderMainStatus |  | 订单状态，详见 OrderMainStatus | Integer |  |
|  | orderPaymentStatus |  | 订单支付状态，详见 OrderPaymentStatus |  |  |
|  | orderDeliveryStatus |  | 订单发货状态，详见 OrderDeliveryStatus |  |  |
|  | afterSaleStatus |  | 订单售后状态，详见 AfterSaleStatus |  |  |
|  | thirdPlatformCode |  | 平台编码，详见 PlatformCodeEnum | String |  |
|  | dayNum |  | 每日号 | String |  |
|  | buyerRemark |  | 买家备注 | String |  |
|  | merCode |  | 商户编码 | String |  |
|  | clientCode |  | 网店编码 | String |  |
|  | onlineStoreCode |  | 实际下单线上门店code | String |  |
|  | onlineStoreName |  | 实际下单线上门店名称 | String |  |
|  | organizationCode |  | 实际发货线下门店编码 | String |  |
|  | organizationName |  | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode |  | 下单发起方机构编码 | String |  |
|  | launchOrganizationName |  | 下单发起方机构名称 |  |  |
|  | companyCode |  | 子公司编码 | String |  |
|  | companyName |  | 子公司名称 | String |  |
|  | orgParentPath |  | 机构路径 | String |  |
|  | userId |  | 会员id | String |  |
|  | businessType |  | 服务模式，详见 BusinessType | String |  |
|  | orderCreated |  | 订单创建时间，秒级时间戳 | String |  |
|  | payTime |  | 支付时间，秒级时间戳 | String |  |
|  | createTime |  | 系统创建时间，秒级时间戳 | String |  |
|  | payTimoutCancelTime |  | 支付自动取消时间，秒级时间戳 | String |  |
|  | orderFlags |  | 订单标记 | List<String> |  |
| orderDetailList |  |  | 订单明细列表 | List<OrderDetail> |  |
|  | orderDetailNo |  | 订单明细编号 | String |  |
|  | orderNo |  | 订单号 | String |  |
|  | thirdOrderDetailNo |  | 三方订单明细编号 | String |  |
|  | rowNo |  | 商品行号 | String |  |
|  | platformSkuId |  | 商品三方平台编码 | String |  |
|  | erpCode |  | 商品erp编码 | String |  |
|  | erpName |  | 商品名称 | String |  |
|  | mainPic |  | 商品主图 | String |  |
|  | commoditySpec |  | 商品规格 | String |  |
|  | commodityCount |  | 商品数量 | String |  |
|  | giftType |  | 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品 | String |  |
|  | isMedicareItem |  | 是否为医保商品 | Boolean |  |
|  | status |  | 明细状态，NORMAL-正常 exchange-换货 exchanged-换货后 | String |  |
|  | originalPrice |  | 商品原单价 | BigDecimal |  |
|  | price |  | 商品售价 | BigDecimal |  |
|  | payment |  | 实付单价（线上单目前缺失） | BigDecimal |  |
|  | totalAmount |  | 售买总价，price * commodityCount | BigDecimal |  |
|  | discountAmount |  | 明细总优惠 | BigDecimal |  |
|  | actualAmount |  | 实付金额(totalAmount - discountAmount) | BigDecimal |  |
|  | discountShareAmount |  | 优惠分摊 | BigDecimal |  |
|  | swapOrderDetailList |  | 换货后的明细列表 | List<OrderDetail> |  |
| orderPayInfo |  |  | 订单支付详情 | Object |  |
|  | orderNo |  | 内部订单号 | String |  |
|  | buyerActualAmount |  | 客户实付 | BigDecimal |  |
|  | merchantActualAmount |  | 商家实收 | BigDecimal |  |
|  | brokerageAmount |  | 交易佣金 | BigDecimal |  |
|  | totalAmount |  | 商品总金额 | BigDecimal |  |
|  | deliveryAmount |  | 配送费 | BigDecimal |  |
|  | packAmount |  | 包装费 | BigDecimal |  |
|  | merchantDeliveryAmount |  | 商家配送费 | BigDecimal |  |
|  | merchantOrderDiscountAmount |  | 商家订单级-优惠汇总 | BigDecimal |  |
|  | merchantDeliveryDiscountAmount |  | 商家配送费优惠 | BigDecimal |  |
|  | merchantCommodityDiscountAmount |  | 商家商品总优惠 | BigDecimal |  |
|  | platformDeliveryAmount |  | 平台配送费 | BigDecimal |  |
|  | platformOrderDiscountAmount |  | 平台订单级-优惠汇总 | BigDecimal |  |
|  | platformDeliveryDiscountAmount |  | 平台配送费优惠 | BigDecimal |  |
|  | platformCommodityDiscountAmount |  | 平台商品优惠金额 | BigDecimal |  |
|  | remainBrokerageAmount |  | 剩余交易佣金(实时) | BigDecimal |  |
|  | medicareAmount |  | 医保金额 | BigDecimal |  |
|  | paymentList |  | 支付方式列表 | List<Object> |  |
|  |  | thirdPayNo | 支付唯一号 | String |  |
|  |  | payType | 支付方式 | String |  |
|  |  | payName | 支付方式名称 | String |  |
|  |  | payAmount | 支付金额 | BigDecimal |  |
| receiverInfo |  |  | 收货人信息 | Object |  |
|  | receiverName |  | 收货人名 | String |  |
|  | receiverTelephone |  | 收货人电话 | String |  |
|  | receiverMobile |  | 收货人手机号码 | String |  |
|  | province |  | 收货人省份 | String |  |
|  | city |  | 收货人城市 | String |  |
|  | district |  | 收货人所在城市区域 | String |  |
|  | town |  | 收货人所在城镇 | String |  |
|  | address |  | 收货人详细地址 | String |  |
|  | zipCode |  | 邮编 | String |  |
|  | fullAddress |  | 完整详细地址 | String |  |
|  | privacyPhone |  | 脱敏手机号 | String |  |
|  | receiverNamePrivacy |  | 收货人隐私姓名 | String |  |
|  | receiverPhonePrivacy |  | 收货人隐私手机号 | String |  |
|  | receiverAddressPrivacy |  | 收货人隐私地址 | String |  |
| logisticsList |  |  | 物流信息 | List<Object> |  |
|  | logisticsTrackingNo |  | 物流单号 | String |  |
|  | logisticsCompany |  | 物流公司 | String |  |
|  | logisticsName |  | 物流名称 | String |  |
|  | state |  | 单据状态，详见OrderDeliveryStatus | Integer |  |
|  | actualDeliveryFee |  | 实际运费 | BigDecimal |  |
|  | deliveryFeeTotal |  | 总运费 | BigDecimal |  |
|  | deliveryTip |  | 配送小费 | BigDecimal |  |
|  | courier |  | 配送员 | String |  |
|  | courierPhone |  | 配送员电话 | String |  |
|  | callTime |  | 呼叫时间，秒级时间戳 | String |  |
|  | acceptTime |  | 接单时间，秒级时间戳 | String |  |
|  | pickTime |  | 取货时间，秒级时间戳 | String |  |
|  | cancelTime |  | 取消时间，秒级时间戳 | String |  |
|  | cancelFrom |  | 取消来源 | String |  |
|  | cancelReason |  | 取消原因 | String |  |
|  | cancelDetail |  | 取消描述 | String |  |
|  | exceptionReason |  | 异常原因 | String |  |
|  | logisticsType |  | 配送方式，详见 DeliveryTypeEnum | Integer |  |
|  | deliveryPlatName |  | 第三方配送有配送平台 | String |  |
|  | deliveryClientCode |  | 第三方配送有配送网店 | String |  |
|  | deliveryStoreCode |  | 第三方配送有配送门店 | String |  |
| deliveryOrderInfoList |  |  | 发货单信息 | List<Object> |  |
|  | deliveryOrderNo |  | 发货单号 | String |  |
|  | thirdDeliveryOrderNo |  | 三方发货单号 | String |  |
|  | deliveryDate |  | 发货日期，秒级时间戳 | String |  |
|  | senderPlanOrganizationCode |  | 计划发货方编码 | String |  |
|  | senderPlanOrganizationName |  | 计划发货方名称 | String |  |
|  | senderRealOrganizationCode |  | 实际发货方编码 | String |  |
|  | senderRealOrganizationName |  | 实际发货方名称 | String |  |
|  | deliveryTransfer |  | 转单状态 | String |  |
|  | deliveryStatus |  | 发货单状态,详见 OrderDeliveryStatus | String |  |
| deliveryOrderDetailList |  |  |  |  |  |
|  | deliveryOrderDetailNo |  | 发货单明细编号 | String |  |
|  | deliveryOrderNo |  | 发货单号 | String |  |
|  | orderDetailNo |  | 内部明细编号 | String |  |
|  | logisticsTrackingNo |  | 物流单号 | String |  |
|  | erpCode |  | 商品erp编码 | String |  |
|  | erpName |  | 商品名称 | String |  |
|  | commodityCount |  | 商品数量 | String |  |
|  | status |  | 状态 | String |  |
|  | deliveryOrderDetailPickList |  | 拣货信息 | List<Object> |  |
|  |  | erpCode | 商品erp编码 | String |  |
|  |  | count | 拣货数量 | String |  |
|  |  | makeNo | 商品批号 | String |  |
| orderAccountInfo |  |  |  |  |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": [       {         "deliveryOrderInfo": {           "deliveryDate": "",           "deliveryOrderNo": "",           "deliveryState": "",           "deliveryTransfer": "",           "senderPlanOrganizationCode": "",           "senderPlanOrganizationName": "",           "senderRealOrganizationCode": "",           "senderRealOrganizationName": "",           "thirdDeliveryOrderNo": "",          "deliveryOrderDetailList": [           {             "commodityCount": "",             "deliveryOrderDetailNo": "",             "deliveryOrderDetailPickList": [               {                 "count": "",                 "deliveryOrderDetailNo": "",                 "deliveryOrderDetailPickNo": "",                 "erpCode": "",                 "makeNo": ""               }             ],             "deliveryOrderNo": "",             "erpCode": "",             "erpName": "",             "logisticsTrackingNo": "",             "orderDetailNo": "",             "status": ""           }         ]},         "logisticsList": [           {             "acceptTime": "",             "actualDeliveryFee": 0,             "callTime": "",             "cancelDetail": "",             "cancelFrom": "",             "cancelReason": "",             "cancelTime": "",             "changeFlag": 0,             "courier": "",             "courierPhone": "",             "delayState": 0,             "deliveryClientCode": "",             "deliveryFeeTotal": 0,             "deliveryPlatName": "",             "deliveryStoreCode": "",             "deliveryTip": 0,             "exceptionReason": "",             "logisticsCompany": "",             "logisticsName": "",             "logisticsTrackingNo": "",             "logisticsType": "",             "orderNo": "",             "pickTime": "",             "preCallFlag": 0,             "state": 0           }         ],         "orderAccountDetailList": [           {             "accountOrderDetailNo": "",             "accountOrderNo": "",             "billAmount": 0,             "billPrice": 0,             "commodityCostPrice": 0,             "commodityCount": 0,             "discountAmount": 0,             "discountShare": 0,             "erpCode": "",             "erpName": "",             "giftType": "",             "originalPrice": 0,             "price": 0,             "totalAmount": 0           }         ],         "orderAccountInfo": {           "accountOrderNo": "",           "accountOrderState": "",           "billCommodityAmount": 0,           "billTime": "",           "buyerActualAmount": 0,           "clientConfId": "",           "commodityTotalAmount": 0,           "costCenterCode": "",           "medicareAmount": 0,           "merchantActualAmount": 0,           "merchantDeliveryFee": 0,           "merchantDiscount": 0,           "merchantPackFee": 0,           "orderNo": "",           "platBrokerageAmount": 0,           "platformDeliveryFee": 0,           "platformDiscount": 0,           "platformPackFee": 0,           "posCode": ""         },         "orderDetailList": [           {             "actualAmount": 0,             "commodityCount": "",             "commoditySpec": "",             "discountAmount": 0,             "discountShareAmount": 0,             "erpCode": "",             "erpName": "",             "giftType": "",             "isMedicareItem": true,             "mainPic": "",             "orderDetailNo": "",             "orderNo": "",             "originalPrice": 0,             "platformSkuId": "",             "price": 0,             "rowNo": "",             "status": 0,             "swapOrderDetailList": [               {                 "actualAmount": 0,                 "commodityCount": "",                 "commoditySpec": "",                 "discountAmount": 0,                 "discountShareAmount": 0,                 "erpCode": "",                 "erpName": "",                 "giftType": "",                 "isMedicareItem": true,                 "mainPic": "",                 "orderDetailNo": "",                 "orderNo": "",                 "originalPrice": 0,                 "platformSkuId": "",                 "price": 0,                 "rowNo": "",                 "status": 0,                 "swapOrderDetailList": [                   {}                 ],                 "thirdOrderDetailNo": "",                 "totalAmount": 0               }             ],             "thirdOrderDetailNo": "",             "totalAmount": 0           }         ],         "orderInfo": {           "buyerRemark": "",           "clientCode": "",           "companyCode": "",           "companyName": "",           "createTime": "",           "dayNum": "",           "merCode": "",           "onlineStoreCode": "",           "onlineStoreName": "",           "orderCreated": "",           "orderFlags": [],           "orderNo": "",           "orderState": "",           "orgParentPath": "",           "organizationCode": "",           "organizationName": "",           "parentOrderNo": "",           "parentThirdOrderNo": "",           "payTime": "",           "serviceMode": "",           "sourceOrganizationCode": "",           "sourceOrganizationName": "",           "thirdOrderNo": "",           "thirdPlatformCode": "",           "userId": ""         },         "orderPayInfo": {           "brokerageAmount": 0,           "buyerActualAmount": 0,           "deliveryAmount": 0,           "medicareAmount": 0,           "merchantActualAmount": 0,           "merchantCommodityDiscountAmount": 0,           "merchantDeliveryAmount": 0,           "merchantDeliveryDiscountAmount": 0,           "merchantOrderDiscountAmount": 0,           "orderNo": "",           "packAmount": 0,           "paymentList": [             {               "payAmount": 0,               "payName": "",               "payType": ""             }           ],           "platformCommodityDiscountAmount": 0,           "platformDeliveryAmount": 0,           "platformDeliveryDiscountAmount": 0,           "platformOrderDiscountAmount": 0,           "remainBrokerageAmount": 0,           "totalAmount": 0         },         "orderUserInfo": {           "orderNo": "",           "userCardNo": "",           "userId": "",           "userMobile": "",           "userName": ""         },         "receiverInfo": {           "address": "",           "city": "",           "district": "",           "fullAddress": "",           "orderNo": "",           "privacyPhone": "",           "province": "",           "receiverAddressPrivacy": "",           "receiverCode": "",           "receiverMobile": "",           "receiverName": "",           "receiverNamePrivacy": "",           "receiverPhonePrivacy": "",           "receiverTelephone": "",           "town": "",           "zipCode": ""         }       }     ],     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
5. | **响应参数** |
| --- |
| 参数名称 |  |  | 参数描述 | 数据类型 | 备注 |
| orderInfo |  |  | 订单主信息 | List<Object> |  |
|  | orderNo |  | 内部订单号 | String |  |
|  | parentOrderNo |  | 内部父订单号 | String |  |
|  | parentThirdOrderNo |  | 三方平台父订单号 | String |  |
|  | thirdOrderNo |  | 三方平台订单号 | String |  |
|  | orderMainStatus |  | 订单状态，详见 OrderMainStatus | Integer |  |
|  | orderPaymentStatus |  | 订单支付状态，详见 OrderPaymentStatus |  |  |
|  | orderDeliveryStatus |  | 订单发货状态，详见 OrderDeliveryStatus |  |  |
|  | afterSaleStatus |  | 订单售后状态，详见 AfterSaleStatus |  |  |
|  | thirdPlatformCode |  | 平台编码，详见 PlatformCodeEnum | String |  |
|  | dayNum |  | 每日号 | String |  |
|  | buyerRemark |  | 买家备注 | String |  |
|  | merCode |  | 商户编码 | String |  |
|  | clientCode |  | 网店编码 | String |  |
|  | onlineStoreCode |  | 实际下单线上门店code | String |  |
|  | onlineStoreName |  | 实际下单线上门店名称 | String |  |
|  | organizationCode |  | 实际发货线下门店编码 | String |  |
|  | organizationName |  | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode |  | 下单发起方机构编码 | String |  |
|  | launchOrganizationName |  | 下单发起方机构名称 |  |  |
|  | companyCode |  | 子公司编码 | String |  |
|  | companyName |  | 子公司名称 | String |  |
|  | orgParentPath |  | 机构路径 | String |  |
|  | userId |  | 会员id | String |  |
|  | businessType |  | 服务模式，详见 BusinessType | String |  |
|  | orderCreated |  | 订单创建时间，秒级时间戳 | String |  |
|  | payTime |  | 支付时间，秒级时间戳 | String |  |
|  | createTime |  | 系统创建时间，秒级时间戳 | String |  |
|  | payTimoutCancelTime |  | 支付自动取消时间，秒级时间戳 | String |  |
|  | orderFlags |  | 订单标记 | List<String> |  |
| orderDetailList |  |  | 订单明细列表 | List<OrderDetail> |  |
|  | orderDetailNo |  | 订单明细编号 | String |  |
|  | orderNo |  | 订单号 | String |  |
|  | thirdOrderDetailNo |  | 三方订单明细编号 | String |  |
|  | rowNo |  | 商品行号 | String |  |
|  | platformSkuId |  | 商品三方平台编码 | String |  |
|  | erpCode |  | 商品erp编码 | String |  |
|  | erpName |  | 商品名称 | String |  |
|  | mainPic |  | 商品主图 | String |  |
|  | commoditySpec |  | 商品规格 | String |  |
|  | commodityCount |  | 商品数量 | String |  |
|  | giftType |  | 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品 | String |  |
|  | isMedicareItem |  | 是否为医保商品 | Boolean |  |
|  | status |  | 明细状态，NORMAL-正常 exchange-换货 exchanged-换货后 | String |  |
|  | originalPrice |  | 商品原单价 | BigDecimal |  |
|  | price |  | 商品售价 | BigDecimal |  |
|  | payment |  | 实付单价（线上单目前缺失） | BigDecimal |  |
|  | totalAmount |  | 售买总价，price * commodityCount | BigDecimal |  |
|  | discountAmount |  | 明细总优惠 | BigDecimal |  |
|  | actualAmount |  | 实付金额(totalAmount - discountAmount) | BigDecimal |  |
|  | discountShareAmount |  | 优惠分摊 | BigDecimal |  |
|  | swapOrderDetailList |  | 换货后的明细列表 | List<OrderDetail> |  |
| orderPayInfo |  |  | 订单支付详情 | Object |  |
|  | orderNo |  | 内部订单号 | String |  |
|  | buyerActualAmount |  | 客户实付 | BigDecimal |  |
|  | merchantActualAmount |  | 商家实收 | BigDecimal |  |
|  | brokerageAmount |  | 交易佣金 | BigDecimal |  |
|  | totalAmount |  | 商品总金额 | BigDecimal |  |
|  | deliveryAmount |  | 配送费 | BigDecimal |  |
|  | packAmount |  | 包装费 | BigDecimal |  |
|  | merchantDeliveryAmount |  | 商家配送费 | BigDecimal |  |
|  | merchantOrderDiscountAmount |  | 商家订单级-优惠汇总 | BigDecimal |  |
|  | merchantDeliveryDiscountAmount |  | 商家配送费优惠 | BigDecimal |  |
|  | merchantCommodityDiscountAmount |  | 商家商品总优惠 | BigDecimal |  |
|  | platformDeliveryAmount |  | 平台配送费 | BigDecimal |  |
|  | platformOrderDiscountAmount |  | 平台订单级-优惠汇总 | BigDecimal |  |
|  | platformDeliveryDiscountAmount |  | 平台配送费优惠 | BigDecimal |  |
|  | platformCommodityDiscountAmount |  | 平台商品优惠金额 | BigDecimal |  |
|  | remainBrokerageAmount |  | 剩余交易佣金(实时) | BigDecimal |  |
|  | medicareAmount |  | 医保金额 | BigDecimal |  |
|  | paymentList |  | 支付方式列表 | List<Object> |  |
|  |  | thirdPayNo | 支付唯一号 | String |  |
|  |  | payType | 支付方式 | String |  |
|  |  | payName | 支付方式名称 | String |  |
|  |  | payAmount | 支付金额 | BigDecimal |  |
| receiverInfo |  |  | 收货人信息 | Object |  |
|  | receiverName |  | 收货人名 | String |  |
|  | receiverTelephone |  | 收货人电话 | String |  |
|  | receiverMobile |  | 收货人手机号码 | String |  |
|  | province |  | 收货人省份 | String |  |
|  | city |  | 收货人城市 | String |  |
|  | district |  | 收货人所在城市区域 | String |  |
|  | town |  | 收货人所在城镇 | String |  |
|  | address |  | 收货人详细地址 | String |  |
|  | zipCode |  | 邮编 | String |  |
|  | fullAddress |  | 完整详细地址 | String |  |
|  | privacyPhone |  | 脱敏手机号 | String |  |
|  | receiverNamePrivacy |  | 收货人隐私姓名 | String |  |
|  | receiverPhonePrivacy |  | 收货人隐私手机号 | String |  |
|  | receiverAddressPrivacy |  | 收货人隐私地址 | String |  |
| logisticsList |  |  | 物流信息 | List<Object> |  |
|  | logisticsTrackingNo |  | 物流单号 | String |  |
|  | logisticsCompany |  | 物流公司 | String |  |
|  | logisticsName |  | 物流名称 | String |  |
|  | state |  | 单据状态，详见OrderDeliveryStatus | Integer |  |
|  | actualDeliveryFee |  | 实际运费 | BigDecimal |  |
|  | deliveryFeeTotal |  | 总运费 | BigDecimal |  |
|  | deliveryTip |  | 配送小费 | BigDecimal |  |
|  | courier |  | 配送员 | String |  |
|  | courierPhone |  | 配送员电话 | String |  |
|  | callTime |  | 呼叫时间，秒级时间戳 | String |  |
|  | acceptTime |  | 接单时间，秒级时间戳 | String |  |
|  | pickTime |  | 取货时间，秒级时间戳 | String |  |
|  | cancelTime |  | 取消时间，秒级时间戳 | String |  |
|  | cancelFrom |  | 取消来源 | String |  |
|  | cancelReason |  | 取消原因 | String |  |
|  | cancelDetail |  | 取消描述 | String |  |
|  | exceptionReason |  | 异常原因 | String |  |
|  | logisticsType |  | 配送方式，详见 DeliveryTypeEnum | Integer |  |
|  | deliveryPlatName |  | 第三方配送有配送平台 | String |  |
|  | deliveryClientCode |  | 第三方配送有配送网店 | String |  |
|  | deliveryStoreCode |  | 第三方配送有配送门店 | String |  |
| deliveryOrderInfoList |  |  | 发货单信息 | List<Object> |  |
|  | deliveryOrderNo |  | 发货单号 | String |  |
|  | thirdDeliveryOrderNo |  | 三方发货单号 | String |  |
|  | deliveryDate |  | 发货日期，秒级时间戳 | String |  |
|  | senderPlanOrganizationCode |  | 计划发货方编码 | String |  |
|  | senderPlanOrganizationName |  | 计划发货方名称 | String |  |
|  | senderRealOrganizationCode |  | 实际发货方编码 | String |  |
|  | senderRealOrganizationName |  | 实际发货方名称 | String |  |
|  | deliveryTransfer |  | 转单状态 | String |  |
|  | deliveryStatus |  | 发货单状态,详见 OrderDeliveryStatus | String |  |
| deliveryOrderDetailList |  |  |  |  |  |
|  | deliveryOrderDetailNo |  | 发货单明细编号 | String |  |
|  | deliveryOrderNo |  | 发货单号 | String |  |
|  | orderDetailNo |  | 内部明细编号 | String |  |
|  | logisticsTrackingNo |  | 物流单号 | String |  |
|  | erpCode |  | 商品erp编码 | String |  |
|  | erpName |  | 商品名称 | String |  |
|  | commodityCount |  | 商品数量 | String |  |
|  | status |  | 状态 | String |  |
|  | deliveryOrderDetailPickList |  | 拣货信息 | List<Object> |  |
|  |  | erpCode | 商品erp编码 | String |  |
|  |  | count | 拣货数量 | String |  |
|  |  | makeNo | 商品批号 | String |  |
| orderAccountInfo |  |  |  |  |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": [       {         "deliveryOrderInfo": {           "deliveryDate": "",           "deliveryOrderNo": "",           "deliveryState": "",           "deliveryTransfer": "",           "senderPlanOrganizationCode": "",           "senderPlanOrganizationName": "",           "senderRealOrganizationCode": "",           "senderRealOrganizationName": "",           "thirdDeliveryOrderNo": "",          "deliveryOrderDetailList": [           {             "commodityCount": "",             "deliveryOrderDetailNo": "",             "deliveryOrderDetailPickList": [               {                 "count": "",                 "deliveryOrderDetailNo": "",                 "deliveryOrderDetailPickNo": "",                 "erpCode": "",                 "makeNo": ""               }             ],             "deliveryOrderNo": "",             "erpCode": "",             "erpName": "",             "logisticsTrackingNo": "",             "orderDetailNo": "",             "status": ""           }         ]},         "logisticsList": [           {             "acceptTime": "",             "actualDeliveryFee": 0,             "callTime": "",             "cancelDetail": "",             "cancelFrom": "",             "cancelReason": "",             "cancelTime": "",             "changeFlag": 0,             "courier": "",             "courierPhone": "",             "delayState": 0,             "deliveryClientCode": "",             "deliveryFeeTotal": 0,             "deliveryPlatName": "",             "deliveryStoreCode": "",             "deliveryTip": 0,             "exceptionReason": "",             "logisticsCompany": "",             "logisticsName": "",             "logisticsTrackingNo": "",             "logisticsType": "",             "orderNo": "",             "pickTime": "",             "preCallFlag": 0,             "state": 0           }         ],         "orderAccountDetailList": [           {             "accountOrderDetailNo": "",             "accountOrderNo": "",             "billAmount": 0,             "billPrice": 0,             "commodityCostPrice": 0,             "commodityCount": 0,             "discountAmount": 0,             "discountShare": 0,             "erpCode": "",             "erpName": "",             "giftType": "",             "originalPrice": 0,             "price": 0,             "totalAmount": 0           }         ],         "orderAccountInfo": {           "accountOrderNo": "",           "accountOrderState": "",           "billCommodityAmount": 0,           "billTime": "",           "buyerActualAmount": 0,           "clientConfId": "",           "commodityTotalAmount": 0,           "costCenterCode": "",           "medicareAmount": 0,           "merchantActualAmount": 0,           "merchantDeliveryFee": 0,           "merchantDiscount": 0,           "merchantPackFee": 0,           "orderNo": "",           "platBrokerageAmount": 0,           "platformDeliveryFee": 0,           "platformDiscount": 0,           "platformPackFee": 0,           "posCode": ""         },         "orderDetailList": [           {             "actualAmount": 0,             "commodityCount": "",             "commoditySpec": "",             "discountAmount": 0,             "discountShareAmount": 0,             "erpCode": "",             "erpName": "",             "giftType": "",             "isMedicareItem": true,             "mainPic": "",             "orderDetailNo": "",             "orderNo": "",             "originalPrice": 0,             "platformSkuId": "",             "price": 0,             "rowNo": "",             "status": 0,             "swapOrderDetailList": [               {                 "actualAmount": 0,                 "commodityCount": "",                 "commoditySpec": "",                 "discountAmount": 0,                 "discountShareAmount": 0,                 "erpCode": "",                 "erpName": "",                 "giftType": "",                 "isMedicareItem": true,                 "mainPic": "",                 "orderDetailNo": "",                 "orderNo": "",                 "originalPrice": 0,                 "platformSkuId": "",                 "price": 0,                 "rowNo": "",                 "status": 0,                 "swapOrderDetailList": [                   {}                 ],                 "thirdOrderDetailNo": "",                 "totalAmount": 0               }             ],             "thirdOrderDetailNo": "",             "totalAmount": 0           }         ],         "orderInfo": {           "buyerRemark": "",           "clientCode": "",           "companyCode": "",           "companyName": "",           "createTime": "",           "dayNum": "",           "merCode": "",           "onlineStoreCode": "",           "onlineStoreName": "",           "orderCreated": "",           "orderFlags": [],           "orderNo": "",           "orderState": "",           "orgParentPath": "",           "organizationCode": "",           "organizationName": "",           "parentOrderNo": "",           "parentThirdOrderNo": "",           "payTime": "",           "serviceMode": "",           "sourceOrganizationCode": "",           "sourceOrganizationName": "",           "thirdOrderNo": "",           "thirdPlatformCode": "",           "userId": ""         },         "orderPayInfo": {           "brokerageAmount": 0,           "buyerActualAmount": 0,           "deliveryAmount": 0,           "medicareAmount": 0,           "merchantActualAmount": 0,           "merchantCommodityDiscountAmount": 0,           "merchantDeliveryAmount": 0,           "merchantDeliveryDiscountAmount": 0,           "merchantOrderDiscountAmount": 0,           "orderNo": "",           "packAmount": 0,           "paymentList": [             {               "payAmount": 0,               "payName": "",               "payType": ""             }           ],           "platformCommodityDiscountAmount": 0,           "platformDeliveryAmount": 0,           "platformDeliveryDiscountAmount": 0,           "platformOrderDiscountAmount": 0,           "remainBrokerageAmount": 0,           "totalAmount": 0         },         "orderUserInfo": {           "orderNo": "",           "userCardNo": "",           "userId": "",           "userMobile": "",           "userName": ""         },         "receiverInfo": {           "address": "",           "city": "",           "district": "",           "fullAddress": "",           "orderNo": "",           "privacyPhone": "",           "province": "",           "receiverAddressPrivacy": "",           "receiverCode": "",           "receiverMobile": "",           "receiverName": "",           "receiverNamePrivacy": "",           "receiverPhonePrivacy": "",           "receiverTelephone": "",           "town": "",           "zipCode": ""         }       }     ],     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
6. 备注：


## 售后单

### 售后单数量查询

1. URL:/1.0/after-sale/count/statistic
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| searchConditionList |  | 筛选条件 | List<Object> | 否 |  |
|  | searchType | 查询条件类型，见 AfterSaleOrderSearchConditionEnum | String |  |  |
|  | searchData | 条件值 | String |  |  |
| statisticType |  | 聚合条件，可用值： **REFUND_STATUS**：根据退单状态统计，见 AfterSaleStatus | string | 是 |  |
| **请求示例** |
| RDarktrue{     "searchConditionList":[         {             "searchType":"SERVICE_MODE",             "searchData":"JOIN_B2B"         }     ],     "statisticType":"AFTER_SALE_STATUS" } |
3. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| searchConditionList |  | 筛选条件 | List<Object> | 否 |  |
|  | searchType | 查询条件类型，见 AfterSaleOrderSearchConditionEnum | String |  |  |
|  | searchData | 条件值 | String |  |  |
| statisticType |  | 聚合条件，可用值： **REFUND_STATUS**：根据退单状态统计，见 AfterSaleStatus | string | 是 |  |
| **请求示例** |
| RDarktrue{     "searchConditionList":[         {             "searchType":"SERVICE_MODE",             "searchData":"JOIN_B2B"         }     ],     "statisticType":"AFTER_SALE_STATUS" } |
4. 响应体：
  1. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| countList |  | 统计结果 | List<Object> |  |
|  | statisticKey | 聚合键 | String |  |
|  | statisticValue | 聚合值 | String |  |
| **响应示例** |
| RDarktrue{     "code": "10000",     "msg": "操作成功",     "subCode": null,     "subMessage": null,     "traceId": null,     "data": {         "countList": [             {                 "statisticKey": "10",                 "statisticValue": "3352"             },             {                 "statisticKey": "102",                 "statisticValue": "489"             }         ]     },     "timestamp": 1736913289688 } |
5. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| countList |  | 统计结果 | List<Object> |  |
|  | statisticKey | 聚合键 | String |  |
|  | statisticValue | 聚合值 | String |  |
| **响应示例** |
| RDarktrue{     "code": "10000",     "msg": "操作成功",     "subCode": null,     "subMessage": null,     "traceId": null,     "data": {         "countList": [             {                 "statisticKey": "10",                 "statisticValue": "3352"             },             {                 "statisticKey": "102",                 "statisticValue": "489"             }         ]     },     "timestamp": 1736913289688 } |
6. 备注：


### 售后单分页查询

1. URL:/1.0/after-sale/page/query
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| pageSize |  | 每页显示条数，不传默认20 | Long | 否 |  |
| currentPage |  | 当前页，从第1页开始，不传默认为1 | Long | 否 | 如果searchAfter不为空，则以searchAfter为准进行分页，currentPage无效 |
| searchAfter |  | 翻页参数为空查询第一页，后续从上一页响应体中获取 | String |  |
| queryScaleList |  | 查询订单信息范围 可用值：详见AfterSaleQueryScaleEnum | List<String> | 是 |  |
| searchConditionList |  |  |  | 是 |  |
|  | searchType | 查询条件类型，详见 AfterSaleOrderSearchConditionEnum |  |  |  |
|  | searchData | 条件值，查询多个值可用英文逗号(,)拼接，如：A003,H812 |  |  |  |
| **请求示例** |
| {     "searchConditionList":[         {             "searchType":"USER_ID",             "searchData":"12545565"         }, 			{             "searchType":"SERVICE_MODE",             "searchData":"JOIN_B2B"             }            ],     "queryScaleList":["MAIN","DETAIL","PAY"] } |
3. | **请求参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 是否必填 | 备注 |
| pageSize |  | 每页显示条数，不传默认20 | Long | 否 |  |
| currentPage |  | 当前页，从第1页开始，不传默认为1 | Long | 否 | 如果searchAfter不为空，则以searchAfter为准进行分页，currentPage无效 |
| searchAfter |  | 翻页参数为空查询第一页，后续从上一页响应体中获取 | String |  |
| queryScaleList |  | 查询订单信息范围 可用值：详见AfterSaleQueryScaleEnum | List<String> | 是 |  |
| searchConditionList |  |  |  | 是 |  |
|  | searchType | 查询条件类型，详见 AfterSaleOrderSearchConditionEnum |  |  |  |
|  | searchData | 条件值，查询多个值可用英文逗号(,)拼接，如：A003,H812 |  |  |  |
| **请求示例** |
| {     "searchConditionList":[         {             "searchType":"USER_ID",             "searchData":"12545565"         }, 			{             "searchType":"SERVICE_MODE",             "searchData":"JOIN_B2B"             }            ],     "queryScaleList":["MAIN","DETAIL","PAY"] } |
4. 响应体：
  1. | **响应参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 备注 |
| currentPage | 当前页 | List<Object> |  |
| pageSize | 每页显示条数，不传默认20 | Long |  |
| searchAfter | 翻页参数,JSON数组 | String |  |
| totalCount | 总条数 | Long |  |
| data | 数据（与**售后单详情**返回结果一致） | Object |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": ,     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
5. | **响应参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 备注 |
| currentPage | 当前页 | List<Object> |  |
| pageSize | 每页显示条数，不传默认20 | Long |  |
| searchAfter | 翻页参数,JSON数组 | String |  |
| totalCount | 总条数 | Long |  |
| data | 数据（与**售后单详情**返回结果一致） | Object |  |
| **响应示例** |
| RDarktrue{   "code": "",   "data": {     "currentPage": 0,     "data": ,     "pageSize": 0,     "searchAfter": "",     "totalCount": 0,     "totalPage": 0   },   "msg": "",   "subCode": "",   "subMessage": "",   "timestamp": 0,   "traceId": "" } |
6. 备注：


### 售后单详情

1. URL:/1.0/after-sale/page/query
2. 请求体：
  1. | **请求参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 是否必填 | 备注 |
| afterSaleNo | 售后单号 | string | 是 |  |
| queryScaleList | 查询订单信息范围 可用值：详见AfterSaleQueryScaleEnum | List<String> | 是 |  |
| **请求示例** |
| {     "afterSaleNo": "123458788",     "queryScaleList":["MAIN","DETAIL"] } |
3. | **请求参数** |
| --- |
| 参数名称 | 参数描述 | 数据类型 | 是否必填 | 备注 |
| afterSaleNo | 售后单号 | string | 是 |  |
| queryScaleList | 查询订单信息范围 可用值：详见AfterSaleQueryScaleEnum | List<String> | 是 |  |
| **请求示例** |
| {     "afterSaleNo": "123458788",     "queryScaleList":["MAIN","DETAIL"] } |
4. 响应体：
  1. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| afterSaleOrderInfo |  |  |  |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | thirdAfterSaleNo | 三方售后单号 | String |  |
|  | orderNo | 内部订单号 | String |  |
|  | thirdOrderNo | 三方平台订单号 | String |  |
|  | afterSaleStatus | 退单状态，详见 AfterSaleStatus | Integer |  |
|  | thirdPlatformCode | 平台编码，详见 PlatformCodeEnum | String |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
|  | businessType | 业务类型，详见 BusinessType | String |  |
|  | reason | 售后原因 | String |  |
|  | desc | 退款描述 | String |  |
|  | applyTime | 退款申请时间,秒级时间戳 | String |  |
|  | afterSaleScope | 退款类型,PART-部分，ALL-全部 | String |  |
|  | afterSaleType | 售后单类型：CANCEL-取消订单REFUND-退款RETURN-退货EXCHANGE-换货 | String |  |
|  | completeTime | 售后完成时间,秒级时间戳 | String |  |
|  | afterSalePictureList | 售后凭证图片 | List<String> |  |
|  | afterSaleFlags | 售后单标记 | List<String> |  |
|  | logisticsTrackingNoList | 原物流单号 | List<String> | JOIN_B2B订单专有 |
| afterSaleOrderDetailList |  |  |  |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | afterSaleOrderDetailNo | 售后单明细号 | String |  |
|  | orderDetailNo | 订单明细编号 | String |  |
|  | thirdOrderDetailNo | 三方订单明细编号 | String |  |
|  | platformSkuId | 商品三方平台编码 | String |  |
|  | erpCode | 商品erp编码 | String |  |
|  | erpName | 商品名称 | String |  |
|  | mainPic | 商品主图 | String |  |
|  | commodityCount | 退款数量 | String |  |
|  | originCommodityCount | 原始订单明细数量 | String |  |
|  | originalPrice | 商品原单价 | BigDecimal |  |
|  | price | 商品售价 | BigDecimal |  |
|  | totalAmount | 售买总价 | BigDecimal |  |
|  | discountAmount | 明细总优惠 | BigDecimal |  |
|  | discountShareAmount | 优惠分摊 | BigDecimal |  |
|  | giftType | 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品 | String |  |
| afterSaleOrderPayInfo |  |  |  |  |
|  | afterSaleNo | 内部售后单号 | String |  |
|  | totalItemRefundAmount | 退款商品总金额 | BigDecimal |  |
|  | consumerRefundAmount | 退买家总金额 | BigDecimal |  |
|  | merchantRefundAmount | 商家退款总金额 | BigDecimal |  |
|  | brokerageRefundAmount | 返还佣金 | BigDecimal |  |
|  | platformDiscountRefundAmount | 退平台优惠 | BigDecimal |  |
|  | merchantDiscountRefundAmount | 退还商家优惠 | BigDecimal |  |
|  | platformDeliveryRefundAmount | 退平台配送费 | BigDecimal |  |
|  | merchantDeliveryRefundAmount | 退商家配送费 | BigDecimal |  |
|  | platformPackRefundAmount | 退平台包装费 | BigDecimal |  |
|  | merchantPackRefundAmount | 退商家包装费 | BigDecimal |  |
|  | merchantDetailDiscountRefundAmount | 退商家商品明细优惠 | BigDecimal |  |
|  | platformDetailDiscountRefundAmount | 退平台商品明细优惠 | BigDecimal |  |
|  | paymentList | 支付方式列表 | List<Object> |  |
| afterSaleOrderAuditInfo |  |  | Object |  |
|  | afterSaleOrderNo | 售后单号 | String |  |
|  | checkTime | 审核时间 | String |  |
|  | checkerId | 审核人id | String |  |
|  | checkerName | 审核人名 | String |  |
|  | checkType | 审核类型 | String |  |
|  | checkResult | 审核结果 | String |  |
|  | checkMark | 审核备注 | String |  |
| returnOrderInfo |  |  | Object |  |
|  | returnNo | 退货单号 | String |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | orderNo | 心云订单号 | String |  |
|  | thirdPlatformCode | 平台编码 | String |  |
|  | thirdAfterOrderNo | 平台售后单号 | String |  |
|  | thirdOrderNo | 三方平台订单号 | String |  |
|  | returnState | 退货单状态 | String |  |
|  | receiver | 收货员id | String |  |
|  | receivingTime | 收货时间,秒级时间戳 | String |  |
|  | remarks | 备注 | String |  |
|  | userId | 会员id | String |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
| refundOrderInfo |  |  |  |  |
|  | refundNo | 内部退款单号,自己生成 |  |  |
|  | parentRefundNo | 内部父退款单号 |  |  |
|  | afterSaleNo | 售后单号 |  |  |
|  | orderNo | 退单对应的内部订单号(自己生成) |  |  |
|  | thirdPlatformCode | 平台编码 |  |  |
|  | thirdRefundNo | 平台退款单号 |  |  |
|  | parentThirdRefundNo | 平台退款单号(主) |  |  |
|  | thirdOrderNo | 第三方平台订单号 |  |  |
|  | userId | 会员id |  |  |
|  | afterSaleScope | 售后范围 PART-部分 ALL-全部 |  |  |
|  | afterSaleType | 售后单类型: 取消订单、退款、退货、换货、维修 |  |  |
|  | refundState | 退款状态 REFUNDED 已退款 |  |  |
|  | completeTime | 退款完成时间，秒级时间戳 |  |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
| **响应示例z** |  |
| RDarktrue{ 	"code": "", 	"data": { 		"currentPage": 0, 		"data": [ 			{ 				"afterSaleOrderAuditInfo": [ 					{ 						"afterSaleOrderNo": "", 						"checkMark": "", 						"checkResult": "", 						"checkTime": "", 						"checkType": "", 						"checkerId": "", 						"checkerName": "" 					} 				], 				"afterSaleOrderDetailList": [ 					{ 						"afterSaleNo": "", 						"afterSaleOrderDetailNo": "", 						"commodityCount": "", 						"discountAmount": 0, 						"discountShareAmount": 0, 						"erpCode": "", 						"erpName": "", 						"giftType": "", 						"mainPic": "", 						"orderDetailNo": "", 						"originalPrice": 0, 						"platformSkuId": "", 						"price": 0, 						"thirdOrderDetailNo": "", 						"totalAmount": 0 					} 				], 				"afterSaleOrderInfo": { 					"afterSaleFlags": [], 					"afterSaleNo": "", 					"afterSalePictureList": [], 					"afterSaleScope": "", 					"afterSaleState": "", 					"afterSaleType": "", 					"applyTime": "", 					"buyerRemark": "", 					"companyCode": "", 					"companyName": "", 					"completeTime": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"reason": "", 					"serviceMode": "", 					"sourceOrganizationCode": "", 					"sourceOrganizationName": "", 					"thirdAfterSaleNo": "", 					"thirdDeliveryOrderNoList": [], 					"thirdOrderNo": "", 					"thirdPlatformCode": "" 				}, 				"afterSaleOrderPayInfo": { 					"afterSaleNo": "", 					"brokerageRefundAmount": 0, 					"consumerRefundAmount": 0, 					"merchantDeliveryRefundAmount": 0, 					"merchantDetailDiscountRefundAmount": 0, 					"merchantDiscountRefundAmount": 0, 					"merchantPackRefundAmount": 0, 					"merchantRefundAmount": 0, 					"paymentList": [ 						{ 							"payAmount": 0, 							"payName": "", 							"payType": "" 						} 					], 					"platformDeliveryRefundAmount": 0, 					"platformDetailDiscountRefundAmount": 0, 					"platformDiscountRefundAmount": 0, 					"platformPackRefundAmount": 0, 					"totalItemRefundAmount": 0 				}, 				"refundOrderInfo": { 					"afterSaleNo": "", 					"afterSaleType": "", 					"companyCode": "", 					"companyName": "", 					"completeTime": "", 					"merCode": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"parentRefundNo": "", 					"parentThirdRefundNo": "", 					"refundNo": "", 					"refundState": "", 					"refundType": "", 					"thirdOrderNo": "", 					"thirdPlatformCode": "", 					"thirdRefundNo": "", 					"userId": "" 				}, 				"returnOrderInfo": { 					"afterSaleNo": "", 					"companyCode": "", 					"companyName": "", 					"merCode": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"receiver": "", 					"receivingTime": "", 					"remarks": "", 					"returnNo": "", 					"returnState": "", 					"thirdAfterOrderNo": "", 					"thirdOrderNo": "", 					"thirdPlatformCode": "", 					"userId": "" 				} 			} 		], 		"pageSize": 0, 		"searchAfter": "", 		"totalCount": 0, 		"totalPage": 0 	}, 	"msg": "", 	"subCode": "", 	"subMessage": "", 	"timestamp": 0, 	"traceId": "" } |  |
5. | **响应参数** |
| --- |
| 参数名称 |  | 参数描述 | 数据类型 | 备注 |
| afterSaleOrderInfo |  |  |  |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | thirdAfterSaleNo | 三方售后单号 | String |  |
|  | orderNo | 内部订单号 | String |  |
|  | thirdOrderNo | 三方平台订单号 | String |  |
|  | afterSaleStatus | 退单状态，详见 AfterSaleStatus | Integer |  |
|  | thirdPlatformCode | 平台编码，详见 PlatformCodeEnum | String |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
|  | businessType | 业务类型，详见 BusinessType | String |  |
|  | reason | 售后原因 | String |  |
|  | desc | 退款描述 | String |  |
|  | applyTime | 退款申请时间,秒级时间戳 | String |  |
|  | afterSaleScope | 退款类型,PART-部分，ALL-全部 | String |  |
|  | afterSaleType | 售后单类型：CANCEL-取消订单REFUND-退款RETURN-退货EXCHANGE-换货 | String |  |
|  | completeTime | 售后完成时间,秒级时间戳 | String |  |
|  | afterSalePictureList | 售后凭证图片 | List<String> |  |
|  | afterSaleFlags | 售后单标记 | List<String> |  |
|  | logisticsTrackingNoList | 原物流单号 | List<String> | JOIN_B2B订单专有 |
| afterSaleOrderDetailList |  |  |  |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | afterSaleOrderDetailNo | 售后单明细号 | String |  |
|  | orderDetailNo | 订单明细编号 | String |  |
|  | thirdOrderDetailNo | 三方订单明细编号 | String |  |
|  | platformSkuId | 商品三方平台编码 | String |  |
|  | erpCode | 商品erp编码 | String |  |
|  | erpName | 商品名称 | String |  |
|  | mainPic | 商品主图 | String |  |
|  | commodityCount | 退款数量 | String |  |
|  | originCommodityCount | 原始订单明细数量 | String |  |
|  | originalPrice | 商品原单价 | BigDecimal |  |
|  | price | 商品售价 | BigDecimal |  |
|  | totalAmount | 售买总价 | BigDecimal |  |
|  | discountAmount | 明细总优惠 | BigDecimal |  |
|  | discountShareAmount | 优惠分摊 | BigDecimal |  |
|  | giftType | 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品 | String |  |
| afterSaleOrderPayInfo |  |  |  |  |
|  | afterSaleNo | 内部售后单号 | String |  |
|  | totalItemRefundAmount | 退款商品总金额 | BigDecimal |  |
|  | consumerRefundAmount | 退买家总金额 | BigDecimal |  |
|  | merchantRefundAmount | 商家退款总金额 | BigDecimal |  |
|  | brokerageRefundAmount | 返还佣金 | BigDecimal |  |
|  | platformDiscountRefundAmount | 退平台优惠 | BigDecimal |  |
|  | merchantDiscountRefundAmount | 退还商家优惠 | BigDecimal |  |
|  | platformDeliveryRefundAmount | 退平台配送费 | BigDecimal |  |
|  | merchantDeliveryRefundAmount | 退商家配送费 | BigDecimal |  |
|  | platformPackRefundAmount | 退平台包装费 | BigDecimal |  |
|  | merchantPackRefundAmount | 退商家包装费 | BigDecimal |  |
|  | merchantDetailDiscountRefundAmount | 退商家商品明细优惠 | BigDecimal |  |
|  | platformDetailDiscountRefundAmount | 退平台商品明细优惠 | BigDecimal |  |
|  | paymentList | 支付方式列表 | List<Object> |  |
| afterSaleOrderAuditInfo |  |  | Object |  |
|  | afterSaleOrderNo | 售后单号 | String |  |
|  | checkTime | 审核时间 | String |  |
|  | checkerId | 审核人id | String |  |
|  | checkerName | 审核人名 | String |  |
|  | checkType | 审核类型 | String |  |
|  | checkResult | 审核结果 | String |  |
|  | checkMark | 审核备注 | String |  |
| returnOrderInfo |  |  | Object |  |
|  | returnNo | 退货单号 | String |  |
|  | afterSaleNo | 售后单号 | String |  |
|  | orderNo | 心云订单号 | String |  |
|  | thirdPlatformCode | 平台编码 | String |  |
|  | thirdAfterOrderNo | 平台售后单号 | String |  |
|  | thirdOrderNo | 三方平台订单号 | String |  |
|  | returnState | 退货单状态 | String |  |
|  | receiver | 收货员id | String |  |
|  | receivingTime | 收货时间,秒级时间戳 | String |  |
|  | remarks | 备注 | String |  |
|  | userId | 会员id | String |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
| refundOrderInfo |  |  |  |  |
|  | refundNo | 内部退款单号,自己生成 |  |  |
|  | parentRefundNo | 内部父退款单号 |  |  |
|  | afterSaleNo | 售后单号 |  |  |
|  | orderNo | 退单对应的内部订单号(自己生成) |  |  |
|  | thirdPlatformCode | 平台编码 |  |  |
|  | thirdRefundNo | 平台退款单号 |  |  |
|  | parentThirdRefundNo | 平台退款单号(主) |  |  |
|  | thirdOrderNo | 第三方平台订单号 |  |  |
|  | userId | 会员id |  |  |
|  | afterSaleScope | 售后范围 PART-部分 ALL-全部 |  |  |
|  | afterSaleType | 售后单类型: 取消订单、退款、退货、换货、维修 |  |  |
|  | refundState | 退款状态 REFUNDED 已退款 |  |  |
|  | completeTime | 退款完成时间，秒级时间戳 |  |  |
|  | merCode | 商户编码 | String |  |
|  | organizationCode | 实际发货线下门店编码 | String |  |
|  | organizationName | 实际发货线下门店名称 | String |  |
|  | launchOrganizationCode | 下单发起方机构编码 | String |  |
|  | launchOrganizationName | 下单发起方机构名称 | String |  |
|  | companyCode | 子公司编码 | String |  |
|  | companyName | 子公司名称 | String |  |
|  | orgParentPath | 机构路径 | String |  |
| **响应示例z** |  |
| RDarktrue{ 	"code": "", 	"data": { 		"currentPage": 0, 		"data": [ 			{ 				"afterSaleOrderAuditInfo": [ 					{ 						"afterSaleOrderNo": "", 						"checkMark": "", 						"checkResult": "", 						"checkTime": "", 						"checkType": "", 						"checkerId": "", 						"checkerName": "" 					} 				], 				"afterSaleOrderDetailList": [ 					{ 						"afterSaleNo": "", 						"afterSaleOrderDetailNo": "", 						"commodityCount": "", 						"discountAmount": 0, 						"discountShareAmount": 0, 						"erpCode": "", 						"erpName": "", 						"giftType": "", 						"mainPic": "", 						"orderDetailNo": "", 						"originalPrice": 0, 						"platformSkuId": "", 						"price": 0, 						"thirdOrderDetailNo": "", 						"totalAmount": 0 					} 				], 				"afterSaleOrderInfo": { 					"afterSaleFlags": [], 					"afterSaleNo": "", 					"afterSalePictureList": [], 					"afterSaleScope": "", 					"afterSaleState": "", 					"afterSaleType": "", 					"applyTime": "", 					"buyerRemark": "", 					"companyCode": "", 					"companyName": "", 					"completeTime": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"reason": "", 					"serviceMode": "", 					"sourceOrganizationCode": "", 					"sourceOrganizationName": "", 					"thirdAfterSaleNo": "", 					"thirdDeliveryOrderNoList": [], 					"thirdOrderNo": "", 					"thirdPlatformCode": "" 				}, 				"afterSaleOrderPayInfo": { 					"afterSaleNo": "", 					"brokerageRefundAmount": 0, 					"consumerRefundAmount": 0, 					"merchantDeliveryRefundAmount": 0, 					"merchantDetailDiscountRefundAmount": 0, 					"merchantDiscountRefundAmount": 0, 					"merchantPackRefundAmount": 0, 					"merchantRefundAmount": 0, 					"paymentList": [ 						{ 							"payAmount": 0, 							"payName": "", 							"payType": "" 						} 					], 					"platformDeliveryRefundAmount": 0, 					"platformDetailDiscountRefundAmount": 0, 					"platformDiscountRefundAmount": 0, 					"platformPackRefundAmount": 0, 					"totalItemRefundAmount": 0 				}, 				"refundOrderInfo": { 					"afterSaleNo": "", 					"afterSaleType": "", 					"companyCode": "", 					"companyName": "", 					"completeTime": "", 					"merCode": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"parentRefundNo": "", 					"parentThirdRefundNo": "", 					"refundNo": "", 					"refundState": "", 					"refundType": "", 					"thirdOrderNo": "", 					"thirdPlatformCode": "", 					"thirdRefundNo": "", 					"userId": "" 				}, 				"returnOrderInfo": { 					"afterSaleNo": "", 					"companyCode": "", 					"companyName": "", 					"merCode": "", 					"onlineStoreCode": "", 					"onlineStoreName": "", 					"orderNo": "", 					"orgParentPath": "", 					"organizationCode": "", 					"organizationName": "", 					"receiver": "", 					"receivingTime": "", 					"remarks": "", 					"returnNo": "", 					"returnState": "", 					"thirdAfterOrderNo": "", 					"thirdOrderNo": "", 					"thirdPlatformCode": "", 					"userId": "" 				} 			} 		], 		"pageSize": 0, 		"searchAfter": "", 		"totalCount": 0, 		"totalPage": 0 	}, 	"msg": "", 	"subCode": "", 	"subMessage": "", 	"timestamp": 0, 	"traceId": "" } |  |
6. 备注：


# 交易中台 ↔ 订单中台

## 订单创建

## 订单取消

## 订单状态变更

# 订单中台 → DERP/POS

## 出库单回调 （主要用于加盟商采购商品后WMS 发货，通过ERP 透传出库单给OMS）

URL: third-platform/1.0/outbound-order/save

调用方：D-ERP

请求参数

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| orderNO | Long | 是 | 平台订单号 | 18776648958736745 |
| storeCode | String | 是 | 门店编码 | GM092 |
| warehouse_cdoe | String | 是 | 仓库编码 | D190 |
| deliverOrders | List<Object> | 是 | 出库订单信息 |  |
| deliverOrder |
| outboundOrder | Object | 是 | 出库单对应的物流信息 |  |
| goods | List<Object> | 是 | 出库单对应商品 |  |
| stockPicker | Object | 是 | 拣货人员信息 |  |
| logisticsOrder |
| outboundOrderNo | String | 是 | 出库单号 | 123JDH324324 |
| waybillCode | String | 是 | 物流单号 | YT009478534 |
| carrierCode | String | 是 | 物流公司编码 | YTO |
| goods |
| erpCode | String | 是 | 商品编码 | 125290 |
| name | String | 是 | 商品名称 | 999感冒灵颗粒 |
| manufacturer | String | 是 | 生产厂商 | 三九制药厂 |
| specs | String | 是 | 规格 | 10mg*6 |
| qty | Int | 是 | 数量 | 2 |
| batchNo | String | 是 | 批次号 | 568743 |
| expiration | String | 是 | 有效期 | 2027-01-01 |
| traceabilityCode | String | 否 | 追溯码 | 234355657 |
| stockPicker |
| pickerUserId | String | 是 | 拣货人user id | 40239424242 |
| pickerUserName | String | 是 | 拣货人名字 | 张三 |
| checkerUserId | String | 否 | 复核人员id |  |
| checkerUserName | String | 否 | 复核人员名称 |  |


示例：

RDarktrue{
    "orderNO": "18776648958736745",
    "deliverOrders": [
        {
            "outboundOrder": {
                "outboundOrderNo": "123JDH324324",
                "waybillCode": "YT009478534",
                "carrierCode": "YTO"
            },
            "goods": [
                {
                    "erpCode": "137655",
                    "name": "999感冒灵颗粒",
                    "manufacturer": "三九制药厂",
                    "specs": "10mg*6",
                    "qty": 2,
                    "batchNo": "349999",
                    "expiration": "2027-01-01",
                    "traceabilityCode": "234355657"
                }
            ],
            "stockPicker": {
                "pickerUserId": "40239424242",
                "pickerUserName": "张三",
                "checkerUserId": null,
                "checkerUserName": null
            }
        }
    ]
}

返回值

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 0为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为0时，此值必定返回 | 商品信息异常，请确认 |


## 售后退货退款单回调 （退货退款流程中 DERP系统回调退货单状态）

URL: /third-platform/1.0/return-order/save

调用方: POS 、WMS

请求参数

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| afterSaleNo | String | 是 | 平台订单号 | 18776648958736745 |
| orderNo | String | 否 | 1.门店退货必填 |  |
| storeCode | String | 是 | 门店编码 | GM092 |
| refundType | Int | 是 | 1.加盟店退货 2.调拨单退货 | 1 |
| status | Int | 是 | -2：审核失败-1：待审核0：待收货1：已收货 | 0 |
| totalAmount | BigDecimal | 是 | 退款总额 | 20.56 |
| userId | String | 是 | 申请人id | 40239424242 |
| userName | String | 是 | 申请人姓名 | 张三 |
| goods | List<Object> | 是 | 退货商品信息 |  |
| goods |
| erpCode | String | 是 | 商品编码 | 125290 |
| name | String | 是 | 商品名称 | 999感冒灵颗粒 |
| manufacturer | String | 是 | 生产厂商 | 三九制药厂 |
| specs | String | 是 | 规格 | 10mg*6 |
| qty | Int | 是 | 数量 | 2 |
| batchNo | String | 是 | 批次号 | 568743 |
| expiration | String | 是 | 有效期 | 2027-01-01 |
| traceabilityCode | String | 否 | 追溯码 | 234355657 |
| price | ``` BigDecimal ``` | 是 | 退货商品单价 | 2.56 |


示例：

true{
    "orderNO": "18776648958736745",
    "afterSaleNo": "18776648958736745",
    "refundType": "1",
    "status": 0,
    "totalAmount": 23.44,
    "goods": [
        {
            "erpCode": "137655",
            "name": "999感冒灵颗粒",
            "manufacturer": "三九制药厂",
            "specs": "10mg*6",
            "qty": 2,
            "batchNo": "349999",
            "expiration": "2027-01-01",
            "traceabilityCode": "234355657",
            "price": 12.3
        }
    ],
    "userId": "40239424242",
    "userName": "张三"
}




返回值：

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 0为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为0时，此值必定返回 | 商品信息异常，请确认 |


## 生成销售单 （主要用于加盟商向集团内部其他门店调货使用）

URL: /third-platform/1.0/sale-order/save

调用方：D-ERP

请求参数

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| thirdOrderNo | String | 是 | 平台订单号（通行凭证） |  |
| orderNo | Long | 是 | 系统订单号 | 18776648958736745 |
| storeCode | String | 是 | 门店编码 | GM092 |
| createTime | DateTime | 是 | 订单创建时间 | 2025-01-10 12:20:22 |
| payTime | DateTime | 是 | 付款时间 | 2025-01-10 12:30:22 |
| status | Int | 是 | 1: 待支付30：待配送40：配送中100：已完成 102：取消 | 5 |
| totalAmount | BigDecimal | 是 | 订单总金额（实付 | 20.56 |
| userId | String | 是 | 申请人id | 40239424242 |
| userName | String | 是 | 申请人姓名 | 张三 |
| goods | List<Object> | 是 | 商品信息 |  |
| goods |
| erpCode | String | 是 | 商品编码 | 125290 |
| name | String | 是 | 商品名称 | 999感冒灵颗粒 |
| manufacturer | String | 是 | 生产厂商 | 三九制药厂 |
| specs | String | 是 | 规格 | 10mg*6 |
| qty | Int | 是 | 数量 | 2 |
| batchNo | String | 是 | 批次号 | 568743 |
| expiration | String | 是 | 有效期 | 2027-01-01 |
| traceabilityCode | String | 否 | 追溯码 | 234355657 |
| price | ``` BigDecimal ``` | 是 | 退货商品单价 | 2.56 |


示例：

true{
    "thirdOrderNo": "18776648958736745",
    "orderNo": "18776648958736745",
    "createTime": "2025-01-01 12:22:15",
    "payTime": "2025-01-01 12:22:19",
    "status": 10,
    "totalAmount": 36.56,
    "userId": "40239424242",
    "userName": "张三",
    "goods": [
        {
            "erpCode": "137655",
            "name": "999感冒灵颗粒",
            "manufacturer": "三九制药厂",
            "specs": "10mg*6",
            "qty": 2,
            "batchNo": "349999",
            "expiration": "2027-01-01",
            "traceabilityCode": "234355657",
            "price": 12.3
        }
    ]
}

返回值：

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 10000为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为10000时，此值必定返回 | 商品信息异常，请确认 |


## 订单状态回调 （主要用于D-ERP通知心云订单发货完成）

URL：/third-platform/1.0/sale-order/status/update

调用方：D-ERP

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| orderNo | Long | 是 | 系统订单号 | 18776648958736745 |
| status | Int | 是 | 40：配送中100：已完成 102：取消 | 40 |
| updateTime | DateTime | 是 | 更新时间 | 2025-01-15 14:22:12 |
| remark | ``` String ``` | 否 | 备注信息 |  |
| outboundOrders | List<String> | 否 | 推送完成状态时必填 | ["343","4435234325"] |


示例：

RDarktrue{
    "orderNo": "18776648958736745",
    "updateTime": "2025-01-01 12:22:15",
    "status": 40,
    "outboundOrders":["343","4435234325"]
 }

返回值：

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 0为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为0时，此值必定返回 | 商品信息异常，请确认 |


## 推送销售订单至POS 系统(正单下发后，心云OMS 透传给POS 系统)

URL: /third-platform/1.0/sale-order/save

调用方：OMS

请求参数

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| orderNo | Long | 是 | 系统订单号 | 18776648958736745 |
| storeCode | String | 是 | 门店编码 | GM020 |
| createTime | DateTime | 是 | 订单创建时间 | 2025-01-10 12:20:22 |
| payTime | DateTime | 是 | 付款时间 | 2025-01-10 12:30:22 |
| status | Int | 是 | 1: 待支付30：待配送40：配送中100：已完成 102：取消 | 5 |
| totalAmount | BigDecimal | 是 | 订单总金额（实付 | 20.56 |
| userId | String | 是 | 申请人id | 40239424242 |
| userName | String | 是 | 申请人姓名 | 张三 |
| goods | List<Object> | 是 | 商品信息 |  |
| goods |
| erpCode | String | 是 | 商品编码 | 125290 |
| name | String | 是 | 商品名称 | 999感冒灵颗粒 |
| manufacturer | String | 是 | 生产厂商 | 三九制药厂 |
| specs | String | 是 | 规格 | 10mg*6 |
| qty | Int | 是 | 数量 | 2 |
| batchNo | String | 是 | 批次号 | 568743 |
| expiration | String | 是 | 有效期 | 2027-01-01 |
| traceabilityCode | String | 否 | 追溯码 | 234355657 |
| price | ``` BigDecimal ``` | 是 | 退货商品单价 | 2.56 |


请求参数：

true{
    "orderNo": "18776648958736745",
    "storeCode":"GM910"
    "createTime": "2025-01-01 12:22:15",
    "payTime": "2025-01-01 12:22:19",
    "status": 10,
    "totalAmount": 36.56,
    "userId": "40239424242",
    "userName": "张三",
    "goods": [
        {
            "erpCode": "137655",
            "name": "999感冒灵颗粒",
            "manufacturer": "三九制药厂",
            "specs": "10mg*6",
            "qty": 2,
            "batchNo": "349999",
            "expiration": "2027-01-01",
            "traceabilityCode": "234355657",
            "price": 12.3
        }
    ]
}

返回值：

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 0为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为0时，此值必定返回 | 商品信息异常，请确认 |


## 出库单物流信息回调（DERP接收到WMS 系统出库单信息后透传给OMS 系统）

URL: third-platform/1.0/outbound-order/logistics/save

调用方：D-ERP

请求参数

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| orderNO | Long | 是 | 平台订单号 | 18776648958736745 |
| storeCode | String | 是 | 门店编码 | GM092 |
| warehouseCode | String | 是 | 仓库编码 | D190 |
| outboundOrderNo | String | 是 | 出库单号 | 123JDH324324 |
| deliveryType | String | 是 | 出库物流类型 0:第三方物流1:集团物流配送 |  |
| waybillCode | String | 否 | 物流单号 deliveryType为0 必传 | YT009478534 |
| carrierCode | String | 否 | 物流公司编码deliveryType 为0 必传 | YTO |
| deliveryName | String | 否 | 配送人员姓名 deliveryType 为1 必传 | 张三 |
| deliveryPhone | String | 否 | 配送人员电话deliveryType 为1 必传 | 13355556666 |
| licensePlate | String | 否 | 汽车牌照deliveryType 为1 必传 | 川A 297FG |


请求参数：

true{
    "orderNO": "18776648958736745",
    "storeCode": "GM910",
    "warehouseCode":"DS101"
    "outboundOrderNo": "20259897893",
    "deliveryType": "1",
    "waybillCode": NULL,
    "carrierCode": NULL,
    "deliveryPhone": "40239424242",
    "deliveryName": "张三",
    "licensePlate": "川A 297FG"
}

返回值：

| 字段名称 | 类型 | 是否必填 | 备注 | 示例 |
| --- | --- | --- | --- | --- |
| code | String | 是 | 0为成功 其余为失败 | 10000 |
| msg | String | 否 | 描述信息，如果code 不为0时，此值必定返回 | 商品信息异常，请确认 |