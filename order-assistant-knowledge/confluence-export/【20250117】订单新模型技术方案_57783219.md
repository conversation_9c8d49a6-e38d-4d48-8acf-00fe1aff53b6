# 【20250117】订单新模型技术方案

# 为什么做？

## 日益增长的多样化需求与现有订单模型不满足的矛盾

目前已经存在的模型：

1. 微商城-middle-order
2. B2C-OMS_order
3. O2O:order_info
4. 线下单：offline_order


- 越来越多的需求需要同时查询线上线下订单，如会员消费记录、移动OMS等，而且我们目前线上单和线下单是两套模型，而且线下单有分库分表，线上单没有
- 新的B2B商城业务，与现有的O2O、B2C模型不完全匹配，兼容难度过大


## 日益增长的数据量压力与现有业务处理能力不匹配的矛盾

### 订单查询问题

- 现在的线上订单表未做分库分表，目前一个月的O2O订单量大约在300w左右，B2C的订单量大约在40w左右，现在订单表已有3kw数据，慢sql频发


### 门店订单作业问题

- 目前订单作业列表和订单查询列表都使用同一个ES索引查询，一旦出现大数据量修改，出现canal消息堆积，会导致门店接收不到新订单，进而影响订单作业


## 用户对系统【易操作】的要求与现有系统部分功能模块不满足的矛盾

- **流程自动化程度低**：订单中台缺乏有效的自动化工具和流程，大部分订单处理工作依赖人工操作，容易出现人为错误和效率低下等问题。例如，订单的审核、分拣、发货等环节都需要人工逐一处理，不仅效率低，还容易出现漏审、**错发**等情况。
- **缺乏订单监控（正在做）：**偶现漏单问题，门店仍然需要依靠三方的后台提醒


## 开发人员对系统整体质量的高要求与现有系统技术债务累计的矛盾

- **技术债务问题：**
  - 现有系统时不时会出现循环依赖、数据库死锁等问题
  - 业务代码充满了大量的if-else、冗余代码、功能强复用，偶尔导致改一处bug，引发N个bug
- 现有系统时不时会出现循环依赖、数据库死锁等问题
- 业务代码充满了大量的if-else、冗余代码、功能强复用，偶尔导致改一处bug，引发N个bug
- **系统边界模糊：**
  - B2C门店仓的下账逻辑在O2O服务中
  - 现有的B2C订单，是单独一套服务（business-order-web、business-order-b2c-third）、单独有自己的数据表（oms_XXX），但它又和O2O的表有深度的耦合，如order_info表中
  - 对于传统B2C平台如拼多多的订单，是在order-b2c-third服务中通过接口拉取，而像微商城的O2O转B2C的订单，又是先进O2O服务（business-order）进行业务处理，然后通过MQ分发到B2C服务中处理
- B2C门店仓的下账逻辑在O2O服务中
- 现有的B2C订单，是单独一套服务（business-order-web、business-order-b2c-third）、单独有自己的数据表（oms_XXX），但它又和O2O的表有深度的耦合，如order_info表中
- 对于传统B2C平台如拼多多的订单，是在order-b2c-third服务中通过接口拉取，而像微商城的O2O转B2C的订单，又是先进O2O服务（business-order）进行业务处理，然后通过MQ分发到B2C服务中处理
- **数据表设计：**
  - 表设计不够合理，字段冗余、无用字段、字段长度等问题
  - 边界不够清晰，如下账金额、退款处理时的数据修改问题
- 表设计不够合理，字段冗余、无用字段、字段长度等问题
- 边界不够清晰，如下账金额、退款处理时的数据修改问题


# 做什么？

## 订单新模型

### 订单流程中的各种单据以及单据流转

true新模型 生命周期falseautotoptrue66611

### E-R图

### 表设计

250

## 业务优化

### 新模型系统泳道图

true新模型 泳道图 falseautotoptrue33511

### 正单业务梳理

true正单 falseautotoptrue11981

### 逆单业务梳理

true逆单流程falseautotoptrue26181

# 怎么做？

## 里程碑

| 阶段 | 季度 | 内容 | 功能模块 |  |
| --- | --- | --- | --- | --- |
| 1 | Q1 | 新模型-支撑线下单/B2B订单 | 订单中台:依据新模型-实现B2B订单流转 交易中台:从0-1搭建 支付中台:支持B2B余额支付 作业中台: 依据老模型实现订单分页及订单详情查询 (满足移动OMS一期) | 订单: 郭志明 杨俊峰 交易: 王世达 李洋 蒋一良 支付: 焦钰斌 作业中台: 郭志明 |
| 2 | Q2 | 新模型-支撑全域订单查询 | 订单中台: 迁移O2O/B2C/虚拟订单到新模型 (**o2o/b2c的切换迁移方案，要注意双写**) 交易中台: 适配微商城 微商城小前台: 剔除小前台订单模块. 直连中台能力 支付中台:从0-1搭建支付架构 作业中台:依据新模型实现ES列表查询及详情 | 订单: 杨润康 杨俊峰 交易: 王世达 微商城小前台: 蒋一良 支付中台: 焦钰斌 李洋 作业中台: 郭志明 |
| 3 | Q3 | 新模型-迁移作业能力 | 作业中台:支持拣货/发货能力 物流中台:支持外卖呼叫 售后模块:支持售后审核/退货审核/退款审核 下账模块:支持正逆单下账单 |  |
| 4 | Q4 | 新模型-线上切换流量及交互优化 |  |  |


## 里程碑架构图

### Q1内容

trueQ1规划falseautotoptrue17511

### Q2内容

trueQ2规划falseautotoptrue17511

### Q3内容

trueQ3falseautotoptrue17511

### Q4内容

trueQ4规划falseautotoptrue17611

## 新旧模型切换方案

### 方案选择

1. 数据做双向同步
2. 一心助手灰度发布: 切店店铺支持拣货/发货功能.其他店铺不支持操作
3. 心云支持所有门店操作拣货/发货
4. 后续其他能力依赖老模型实现,后续步骤统一在老模型作业


优点: 需求灵活度高,可交付节点适应小步快跑节奏.

缺点: 双向同步数据,技术更具挑战性,需要考虑双边并发场景解决.

### 方案落地示意图

#### 整体过程

true落地过程falseautotoptrue19932

#### 拣货关注点

true拣货注意点falseautotoptrue19511