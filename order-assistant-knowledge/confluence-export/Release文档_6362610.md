# Release文档

# 1 DB

| 库 | 表 | 修改类型 | 执行sql | 运行时机 | 其他说明 |
| --- | --- | --- | --- | --- | --- |
| base_info | sys_merchant | 添加字段 | sqlRDarkALTER TABLE `base_info`.`sys_merchant` ADD COLUMN `corporate_identity_pics` VARCHAR(256) CHARACTER SET 'utf8' NULL DEFAULT NULL COMMENT '法人身份证照片' AFTER `cloud_warehouse_goods_audit`; | Blue服务部署前 |  |
| dscloud | dict_order_export_column | 刷数据，屏蔽导出用户分账相关字段 | update dscloud.dict_order_export_column set default_choose = 0 where service_type = 22 and column_code in ('userBillAmount','userActualBillAmount','userType','userId','userName','shareUserStCode','shareUserStName') | Blue服务部署前 |  |
| h3_pay_core | assignment_engine_assignment | 新增表 | CREATE TABLE `h3_pay_core`.`assignment_engine_assignment` (   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',   `business_type` int NOT NULL COMMENT '业务类型',   `business_unique_id` varchar(200)  NOT NULL COMMENT '业务数据唯一id',   `assignment_status` tinyint NOT NULL DEFAULT 2 COMMENT '任务状态  1:ENQUEUE, 2:FAIELD, 3:SUCCEED',   `sub_assignment_status` int  COMMENT '子任务状态',   `pause_status_bit` bigint  COMMENT '二进制暂停状态',   `next_execute_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下次执行时间',   `retry_times` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',   `total_execute_times` int(11) NOT NULL DEFAULT '0' COMMENT '执行次数',   `enable_status` int(11) NOT NULL DEFAULT 1 COMMENT '是否启用',   `failure_code` int(11) NULL COMMENT '错误码，用户自己定义  字段废弃',   `extend_search_param` json COMMENT '扩展查询json字段， 需要使用字段查询建议创建虚拟列',   `req_content` json NOT NULL COMMENT '请求参数json',   `resp_content` longtext COLLATE utf8mb4_unicode_ci COMMENT '返回内容',   `assignment_flag` bigint NOT NULL DEFAULT '-1' COMMENT '任务标识（2进制扩展）',   `sharding_num` BIGINT DEFAULT NULL COMMENT '分片键' ,   `remark` varchar(500)  DEFAULT NULL COMMENT '备注',   `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modified` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `group_id`       bigint(20)   NULL DEFAULT '-1' COMMENT '集团ID',   `group_name`      varchar(200)   NULL DEFAULT ''  COMMENT '集团名称',   `tenant_id` bigint(20) NOT NULL DEFAULT '-1',   `yn` tinyint NOT NULL DEFAULT '1',   `prescription_status` INT DEFAULT 0 NOT NULL COMMENT '处方单状态：0 非处方单, 1 处方单待审核 ,2处方单审核通过, 3处方单审核不通过',   PRIMARY KEY (`id`),   UNIQUE KEY `uniq_business_type_business_unique_id` (`business_type`,`business_unique_id`) USING BTREE,   KEY `idx_fetch_assignment` (`business_type`, `assignment_status`, `next_execute_time`, `pause_status_bit`) USING BTREE,   KEY `idx_assignment_status` (`assignment_status`) USING BTREE ) ENGINE=InnoDB   COMMENT='Assignment-Engine 任务表'; |  |  |
| srm_bill | srm_bill_goods_info | 增加字段 | alter table srm_bill.srm_bill_goods_info add real_price decimal(10, 2) null comment '服务商实际售价' |  |  |


# 2 微服务

| 服务名 |  |  |
| --- | --- | --- |
| ydjia-merchant-manager |  |  |
| ydjia-merchant-promote |  |  |
| hydee-middle-merchandise |  |  |
| hydee-sp-platform |  |  |
| hydee-middle-baseinfo |  |  |
| h3-pay-finance |  |  |
| hydee-xxl-job |  |  |
| h3-pay-core |  |  |
| ydjia-srm-bills |  |  |


# 3 XXL-JOB

| 任务描述 | Job Handler | 操作类型 | 执行器名称 | Corn | 其他 |
| --- | --- | --- | --- | --- | --- |
| 价格组导出任务 | promoteExportJobHandler | 要确认对应环境是否有该JOB | 海典中台调度中心 | 0 */1 * * * ? |  |
| 云货架自动分账 | srmBillsAutoBillHandler | 要确认对应环境是否有该JOB | 海典中台调度中心 | 0 0/1 * * * ? |  |
| 云货架分账补偿 | srmBillsCompensateHandler | 要确认对应环境是否有该JOB | 海典中台调度中心 | 0 0 0/1 * * ? |  |
| 汇付账户余额下午检查 | payCoreJobHandler | 要确认对应环境是否有该JOB | 海典中台调度中心 | 0 0 17 * * ? |  |
| 汇付账户余额上午检查 | payCoreJobHandler | 要确认对应环境是否有该JOB | 海典中台调度中心 | 0 30 8 * * ? |  |


# 4 Apollo

| 服务名 | 配置项 | 补充说明 |  |  |
| --- | --- | --- | --- | --- |
| ydjia-srm-bills | pay:  # 分账url  billUrl: [https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/divide/v1.1](https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/divide/v1.1)  # 查询分账状态url  billQueryUrl: [https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/divideQuery/v1.1](https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/divideQuery/v1.1)  # 分账回调 url  notifyUrl: [https://dev-merchants.hxyxt.com/api-gateway/srm-bills/1.0/bills/settleNotify](https://dev-merchants.hxyxt.com/api-gateway/srm-bills/1.0/bills/settleNotify)  # 添加分账人 URL  addAccountReceiverUrl: [https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/addDivideAccount/v1.1](https://dev-hcloud.hxyxt.cn/gateway/open-pay-core/secondPhase/addDivideAccount/v1.1)  #查询门店支付商户号  queryStPayCode: [https://dev-hcloud.hxyxt.cn/gateway/open-pay-finance/operation/queryPlatformPayInfo/v1.1](https://dev-hcloud.hxyxt.cn/gateway/open-pay-finance/operation/queryPlatformPayInfo/v1.1) | 修改这几个地址的host |  |  |
| hydee-api-gateway | - id: ydjia-srm-bills  uri: [lb://ydjia-srm-bills](lb://ydjia-srm-bills)  predicates:  - Path=/srm-bills/**  filters:  - StripPrefix=1 | 添加 srm-bills 路由 |  |  |
| h3-pay-core | payment.huifuId ：6666000142142724payment.acct.warning.money ： 1000 |  |  |  |
| h3-pay-finance | huifu.payment.serviceMchId = 6666000141608533huifu.payment.servicePublicKey = MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDOSJ4bU6ivmGa3cN72unBm0f8RYKm8jImhy691o4tf4Njqw8JbVt0mZmjUy6A/EMsPLnlWoXcaZ50AUUpOL7ecKZsdX6cwMSpbTpHTEK8whV+Wsb1r90ZSMtxpJKyG/Cgn77o/aS1zJ/wueDAHraPjP+nYb303hgowFRxUbLv0KQIDAQABhuifu.payment.huiFuPublicKey = MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoVGc9TDppfxlLUgazYfcZkLUDyqlGcuVT5Jq/SinSqdhVH4/qGmjw/ybPmlEYJ/MP710bt9qKns4kMVshHT5ZwgmRByOc+Fr4zvW1ilIGLtFC4qfjmqqJWn7pWbEIWwHdOK6l0CDpZafdHIwf5SW5SNJyig7241xTxjKqLl8xq+5AAufnMINpLJ9IypLbVRMxxaoyKNY/A93TjgD0IBYAo/N13h741eDSGVZLaXFrashmR+/LO/TOFDETD92c3GR004ZnrOposDm1XOMIP1SUDTIWBfv7z4ivCiC9Mt0ZdIaODtPzP1R5iNR0wWzldppNq8xqJU4hpkCE9hYKPf3+QIDAQABhuifu.payment.servicePrivateKey = MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAM5InhtTqK+YZrdw3va6cGbR/xFgqbyMiaHLr3Wji1/g2OrDwltW3SZmaNTLoD8Qyw8ueVahdxpnnQBRSk4vt5wpmx1fpzAxKltOkdMQrzCFX5axvWv3RlIy3GkkrIb8KCfvuj9pLXMn/C54MAeto+M/6dhvfTeGCjAVHFRsu/QpAgMBAAECgYBulmOchscCDBPU9/+wCddEaAP0DvwwBq+qO4X+7QRFDEd95CCH0xOQCIreuaNY6+nQY7vNA5bUYwO6LdpF/DLT4IOBp5RtIxS3i9LN2SpQk9L+xMorUTFiL2HhyNtI6mCJHrNSyArx+qRntBVm2BlhZVyq8ZVS0WnwAWU+gu38AQJBAPJmCbDn4Nx53pXSwU8ghBfrkpfHvrQm03ZjnekzjDGcdJ3G7cpBnHRxFi6zQbyGEpaCc1Nuyxg5pgIjzHxxbOkCQQDZ282FdkfM30ab538ju6HnrZ4uU1mPkRRv0YxUsLUmfUbu8JU6gJF70HG3dck9K4+WwBFuTZuym3C8pau+IMVBAkB95+RWwEq7Go40MjF4oUIbjqApOHZk6tnh36JxM/Y4+rRUE7UF3oiervK/tRqLCvTyurWee6kAX0lMST340Y2pAkEAruFCmV/ZQxv4Ei5CYOy9irxTvdwqiJRtU1Vi0JkI0Vy/FA8Lu2p5Xmp03lAuumu9V8XWODUURsuAM+7JblXgAQJBALQRRtenaxuDmf/saipdeIr3Ah3Q+Unfmyw9iMo1YND8fIxAc+h+RaWt4UGQs1IDaawSBoRU5acsUyJfm6igVUQ=huifu.payment.productId = PAYUNhuifu.payment.publicSecretKey = 604d1debca112ba58f0287640fbc3015huifu.payment.publicAppId = wx2b4af4bac659845e |  |  |  |
| srm-bill | bill: #配置一心到家平台 platform: payCode: 74975488 merCode: "010" merName: 一心到家平台 platformMerName: 一心到家平台 payMerCode: 74975488 |  |  |  |


# 5 其他

需要到ali oss上替换导入商品模板：云仓商品价格方案模板.xlsx