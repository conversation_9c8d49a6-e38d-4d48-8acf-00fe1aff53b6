# 4.1.1 项目介绍

# 一、业务介绍

## 1.1 业务简介

### 1.1.1 产品定位

### 1.1.2 业务全景与用户角色

### 1.1.3 业务术语与缩写解释

| **名称** | **名次释义** |
| OA | 集团的一个老办公系统 |
| 科传POS | 集团老的POS系统，科传POS收银系统，目前门店正在使用的系统 |
| 海典POS | 未来将要使用的POS系统，从海典购买的，目前正在做系统切换的工作,预计25年全部完成切换 |
| 订单中台 | PC端系统,是心云系统内部的订单模块,后续都简称为订单中台 |
| OMS | 新零售后台,指心云OMS |
| DERP系统 | 从金蝶购买的系统，用于主数据、财务等的管理[https://yxtuat.yxtmart.cn/ierp/login.html](https://yxtuat.yxtmart.cn/ierp/login.html)（测试环境） |


## 1.2 业务现状

### 1.2.1 里程碑及规划

**里程碑**

| 项目规划 | 里程碑 | 说明 |
| --- | --- | --- |
| 心云OMS1.0版本 | 1、23年11月22日 上线1.0版本,并切换贵州分公司.2、23年12月20日 切换山西、天津分公司.3、24年01月09日 切换海南、重庆、广西、河南、四川分公司.4、24年01月22日 切换云南、上海分公司,全国全部切流心云OMS完毕 |  |
| 心云OMS1.0+版本 | 1、24年03月04日 上线海典POS接入,并切换四川攀枝花和重庆分公司. |  |


**规划**

### 1.2.2 核心业务流程

### 1.2.3 业务指标

### 1.2.4 团队成员

### 1.2.4 团队成员

| 岗位 | 成员 |
| --- | --- |
| PM | 伍凡凡、徐凯、雷达 |
| 前端 | 吴敏 |
| 后端 | 徐国华、杨润康、蒋一良、王世达、李洋、杨国枫、杨花、郭志明、杨俊峰、焦钰斌 |
| QA | 杨飞、杨明杰、龙敏 |
| 数据 | 傅纯元 |
| 运营 |  |


# 二、架构梳理

## 2.1 逻辑视图

### 2.1.1 分层架构

#### 1、一心堂项目整体分层架构图

true服务架构falseautotoptrue8721

### 2.1.2 交易生产项目微服务依赖关系图 true未命名绘图falseautotoptrue9611

### 2.1.3 领域划分

## 2.2 开发视图

### true未命名绘图111falseautotoptrue11251

## 2.3 运行视图

## 2.4 部署视图

## 2.5 数据视图

### 2.5.1 模型

### 2.5.2 数据流

## 2.7 核心问题

# 3.质量保障

# 4.技术沉淀

# 5.技术规划

# 6.业务规划及思考

# 7.参考资料

###