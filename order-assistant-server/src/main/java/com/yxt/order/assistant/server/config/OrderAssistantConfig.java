package com.yxt.order.assistant.server.config;


import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "order.assistant")
public class OrderAssistantConfig {

  private Dify dify;
  private CfBlackListConfig cfBlackListConfig;


  /**
   * Dify配置
   */
  @Data
  public static class Dify {
    private String baseUrl;
    private String apiKey;
  }


  @Slf4j
  @Data
  public static class CfBlackListConfig {

    private List<String> pageIdList;
    private List<String> titleList;
    private List<String> ignoreLike; // 模糊匹配,忽略


    public boolean isBlackList(String pageId, String title) {
      boolean black = pageIdList.contains(pageId) || titleList.contains(title);
      if (black) {
        log.info("pageId:{} title:{} 命中黑名单,不收纳进知识库", pageId, title);
      }
      return black;
    }

    public boolean isIgnore(String title) {
      for (String ignore : ignoreLike) {
        if (title.toLowerCase().contains(ignore.toLowerCase())) {
          log.info("title(ignore):{} 命中忽略规则,不收纳进知识库", title);
          return true;
        }
      }
      return false;
    }



  }


}
