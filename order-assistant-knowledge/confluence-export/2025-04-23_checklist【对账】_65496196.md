# 2025-04-23 checklist【对账】

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 订单对账 | order-reconciliation | 先上 |  |  |  |  |  |  |  |  |
|  | hydee-business-order |  |  |  |  |  |  |  |  |  |
|  | third-platform-other |  |  |  |  |  |  |  |  |  |
|  | hydee-middle-order |  | feature/ORDER-5997 |  |  |  |  |  |  |  |
|  | h3-pay-finance |  | feature/ORDER-5997 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| dscloud | ``` CREATE TABLE `code_value` (                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',                               `code_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '码值组',                               `code_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '码值类型',                               `code_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '码值value',                               `third_platform_code` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台编码',                               `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                               PRIMARY KEY (`id`),                               KEY `idx_type_code` (`code_group`,`code_type`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='码值表'; CREATE TABLE `export_task` (                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',                                `request_json` longtext NOT NULL COMMENT '请求json内容',                                `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1.待执行 2.执行中 3.执行完成 4.执行失败 5.取消)',                                `export_type` varchar(100) NOT NULL DEFAULT '' COMMENT '导出类型',                                `export_count` int NOT NULL COMMENT '导出数量',                                `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',                                `is_delete` tinyint DEFAULT '0' COMMENT '是否删除(0.未删除 1.删除)',                                `message` varchar(200) DEFAULT '' COMMENT '导出信息',                                `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',                                `created_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',                                `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',                                `updated_name` varchar(50) DEFAULT NULL COMMENT '更新人姓名',                                `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                                `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',                                `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',                                PRIMARY KEY (`id`) USING BTREE,                                KEY `Idx_status` (`status`),                                KEY `Idx_eType_userId` (`export_type`,`created_by`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导出任务'; CREATE TABLE `reconciliation_config` (                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',                                          `business_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务编号',                                          `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台编码',                                          `field_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字段类型',                                          `field_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字段名称',                                          `calculate_rule` json DEFAULT NULL COMMENT '计算规则',                                          `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',                                          `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',                                          `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                                          `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',                                          `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',                                          `extend` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段',                                          PRIMARY KEY (`id`),                                          UNIQUE KEY `idx_config_id` (`business_no`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对账配置表'; CREATE TABLE `platform_settle_account` (                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',                                            `third_settle_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台结算单号',                                            `third_order_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台订单号',                                            `third_refund_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台退款单号',                                            `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '三方平台编码',                                            `settle_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '核算类型  ORDER:销售单 REFUND:退款单',                                            `company_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',                                            `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司名称',                                            `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构编码',                                            `organization_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构名称',                                            `platform_start_time` datetime DEFAULT NULL COMMENT '平台下单时间',                                            `settle_date` datetime DEFAULT NULL COMMENT '结算日期',                                            `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',                                            `merchant_freight_subside` decimal(16,6) DEFAULT NULL COMMENT '商家承担运费补贴',                                            `brokerage_amount` decimal(16,6) DEFAULT NULL COMMENT '交易佣金',                                            `platform_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '平台配送费',                                            `qisuda_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '企速达配送费',                                            `timely_insurance` decimal(16,6) DEFAULT NULL COMMENT '准时宝险',                                            `reward` decimal(16,6) DEFAULT NULL COMMENT '奖励',                                            `customer_compensate_fee` decimal(16,6) DEFAULT NULL COMMENT '客服赔付费用',                                            `due_amount` decimal(16,6) DEFAULT NULL COMMENT '回款金额',                                            `subside` decimal(16,6) DEFAULT NULL COMMENT '补贴',                                            `merchant_tip` decimal(16,6) DEFAULT NULL COMMENT '商家承担小费',                                            `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',                                            `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',                                            `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                                            `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',                                            `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',                                            PRIMARY KEY (`id`),                                            UNIQUE KEY `idx_third_settle_no` (`third_settle_no`),                                            KEY `idx_third_platform_code` (`third_platform_code`),                                            KEY `idx_organization_code` (`organization_code`),                                            KEY `idx_third_order_no` (`third_order_no`),                                            KEY `idx_third_refund_no` (`third_refund_no`),                                            KEY `idx_platform_start_time` (`platform_start_time`),                                            KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台结算核算表'; CREATE TABLE `order_reconciliation` (                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',                                         `third_order_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台订单号',                                         `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台编码',                                         `company_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',                                         `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司名称',                                         `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构编码',                                         `organization_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构名称',                                         `platform_start_time` datetime DEFAULT NULL COMMENT '平台下单时间',                                         `settle_date` datetime DEFAULT NULL COMMENT '结算日期',                                         `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',                                         `account_amount` decimal(16,6) DEFAULT NULL COMMENT '下账金额',                                         `differ_amount` decimal(16,6) DEFAULT NULL COMMENT '差异金额',                                         `close_account_flag` tinyint DEFAULT NULL COMMENT '关账标记   0：未关账   1：已关账',                                         `exception_flag` tinyint DEFAULT NULL COMMENT '异常标记   0：非异常单  1：异常单',                                         `exception_status` tinyint DEFAULT NULL COMMENT '异常状态     0：未处理   1：已处理',                                         `exception_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常类型OMS_NOT_ACCOUNT:心云未下账 CALCULATE_ERROR:计算公式异常',                                         `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',                                         `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',                                         `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                                         `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',                                         `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',                                         PRIMARY KEY (`id`),                                         UNIQUE KEY `idx_third_order_no` (`third_order_no`),                                         KEY `idx_third_platform_code` (`third_platform_code`),                                         KEY `idx_organization_code` (`organization_code`),                                         KEY `idx_platform_start_time` (`platform_start_time`),                                         KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单对账表'; CREATE TABLE `store_reconciliation` (                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',                                         `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台编码',                                         `company_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',                                         `company_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司名称',                                         `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构编码',                                         `organization_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机构名称',                                         `settle_date` datetime DEFAULT NULL COMMENT '结算日期',                                         `order_count` bigint DEFAULT NULL COMMENT '总订单数',                                         `effect_order_count` bigint DEFAULT NULL COMMENT '有效订单数',                                         `differ_order_count` bigint DEFAULT NULL COMMENT '差异订单数',                                         `exception_order_count` bigint DEFAULT NULL COMMENT '异常订单数  差异订单数初始值快照',                                         `exception_order_count_handled` bigint DEFAULT NULL COMMENT '已处理异常订单数',                                         `exception_not_account_count` bigint DEFAULT NULL COMMENT '未下账异常订单数',                                         `exception_calculate_error_count` bigint DEFAULT NULL COMMENT '计算公式异常订单数',                                         `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',                                         `account_amount` decimal(16,6) DEFAULT NULL COMMENT '下账金额',                                         `differ_amount` decimal(16,6) DEFAULT NULL COMMENT '差异金额',                                         `merchant_freight_subside` decimal(16,6) DEFAULT NULL COMMENT '商家承担运费补贴',                                         `brokerage_amount` decimal(16,6) DEFAULT NULL COMMENT '交易佣金',                                         `platform_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '平台配送费',                                         `qisuda_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '企速达配送费',                                         `timely_insurance` decimal(16,6) DEFAULT NULL COMMENT '准时宝险',                                         `reward` decimal(16,6) DEFAULT NULL COMMENT '奖励',                                         `customer_compensate_fee` decimal(16,6) DEFAULT NULL COMMENT '客服赔付费用',                                         `due_amount` decimal(16,6) DEFAULT NULL COMMENT '回款金额',                                         `subside` decimal(16,6) DEFAULT NULL COMMENT '补贴',                                         `merchant_tip` decimal(16,6) DEFAULT NULL COMMENT '商家承担小费',                                         `close_account_flag` tinyint DEFAULT NULL COMMENT '关账标记   0：未关账   1：已关账',                                         `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',                                         `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',                                         `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',                                         `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',                                         `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',                                         PRIMARY KEY (`id`),                                         UNIQUE KEY `idx_settle_plat_organization` (`settle_date`,`third_platform_code`,`organization_code`),                                         KEY `idx_third_platform_code` (`third_platform_code`),                                         KEY `idx_organization_code` (`organization_code`),                                         KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺对账表'; ``` | 表创建 |
| dscloud | ``` INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(1, 'ACCOUNT_CONFIG', 'settleAmount', '商家应收款', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(2, 'ACCOUNT_CONFIG', 'totalFoodAmount', '商品总价（不含商品包装盒费）', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(3, 'ACCOUNT_CONFIG', 'boxAmount', '商品包装盒费总价', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(4, 'ACCOUNT_CONFIG', 'activityPoiAmount', '商家活动总支出金额（含赠品成本）', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(5, 'ACCOUNT_CONFIG', 'activityMeituanAmount', '美团活动补贴总金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(6, 'ACCOUNT_CONFIG', 'activityAgentAmount', '代理商活动承担金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(7, 'ACCOUNT_CONFIG', 'platformChargeFee', '佣金', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(8, 'ACCOUNT_CONFIG', 'performanceServiceFee', '订单配送服务费金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(9, 'ACCOUNT_CONFIG', 'userPayShippingAmount', '用户支付配送费', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(10, 'ACCOUNT_CONFIG', 'userOnlinePayAmount', '用户在线支付金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(11, 'ACCOUNT_CONFIG', 'rate', '佣金的费率', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(12, 'ACCOUNT_CONFIG', 'bottom', '保底金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(13, 'ACCOUNT_CONFIG', 'settleMilli', '结算金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(14, 'ACCOUNT_CONFIG', 'wmDonationAmount', '公益捐赠金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(15, 'ACCOUNT_CONFIG', 'wmDoggyBagAmount', '打包袋金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(16, 'ACCOUNT_CONFIG', 'dealTip', '配送小费', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(17, 'ACCOUNT_CONFIG', 'productPreferences', '商家活动支出分摊到商品上的优惠总金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(18, 'ACCOUNT_CONFIG', 'notProductPreferences', '商家活动支出的未分摊到商品上的总金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(19, 'ACCOUNT_CONFIG', 'medicalInsuranceFee', '医保报销费用', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(20, 'ACCOUNT_CONFIG', 'medicalInsuranceCash', '医保自费金额', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(21, 'ACCOUNT_CONFIG', 'agreementCommissionRebateAmount', '无门槛订单的返利', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(22, 'ACCOUNT_CONFIG', 'skuActSubsidy', '商家活动支出-商品分摊', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(23, 'ACCOUNT_CONFIG', 'shippingActSubsidy', '商家活动支出-配送费分摊', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(24, 'ACCOUNT_CONFIG', 'bagActSubsidy', '商家活动支出-打包袋分摊', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(25, 'ACCOUNT_CONFIG', 'businessPlatformChargeFee', '企业版佣金', '27', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(26, 'ACCOUNT_CONFIG', 'adjust_fee', '调账金额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(27, 'ACCOUNT_CONFIG', 'agent_rate', '代理商补贴', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(28, 'ACCOUNT_CONFIG', 'cold_box_fee', '冷链加工费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(29, 'ACCOUNT_CONFIG', 'commission', '实收佣金', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(30, 'ACCOUNT_CONFIG', 'guarantee_fee', '保底抽佣金额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(31, 'ACCOUNT_CONFIG', 'package_fee', '包装费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(32, 'ACCOUNT_CONFIG', 'platform_rate', '平台补贴', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(33, 'ACCOUNT_CONFIG', 'product_fee', '商品总金额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(34, 'ACCOUNT_CONFIG', 'send_fee', '配送费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(35, 'ACCOUNT_CONFIG', 'shop_rate', '商户补贴', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(36, 'ACCOUNT_CONFIG', 'user_fee', '用户实付金额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(37, 'ACCOUNT_CONFIG', 'zhongbao_call_fee', '众包呼单费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(38, 'ACCOUNT_CONFIG', 'actual_base_logistics_amount', '履约服务费（基础物流费）', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(39, 'ACCOUNT_CONFIG', 'baseLogisticsAmount', '基础物流费总额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(40, 'ACCOUNT_CONFIG', 'merchantCouponAmount', '商户补贴-商家券', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(41, 'ACCOUNT_CONFIG', 'merchantDeliveryCouponAmount', '商户补贴-配送费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(42, 'ACCOUNT_CONFIG', 'merchantDeliverySubsidyAmount', '商户补贴-配送费活动', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(43, 'ACCOUNT_CONFIG', 'merchantCashGiftAmount', '商户补贴-礼金', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(44, 'ACCOUNT_CONFIG', 'merchantSubsidyAmount', '商户补贴-活动', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(45, 'ACCOUNT_CONFIG', 'merchantItemCouponAmount', '商户补贴-单品券', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(46, 'ACCOUNT_CONFIG', 'originDeliverFee', '配送费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(47, 'ACCOUNT_CONFIG', 'deliveryTipAmount', '呼单小费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(48, 'ACCOUNT_CONFIG', 'elemeCouponAmount', '饿了么平台补贴-商家券', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(49, 'ACCOUNT_CONFIG', 'elemeDeliveryCouponAmount', '饿了么平台补贴-配送费券', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(50, 'ACCOUNT_CONFIG', 'elemeDeliverySubsidyAmount', '饿了么平台补贴-配送费活动', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(51, 'ACCOUNT_CONFIG', 'elemeCashGiftAmount', '饿了么平台补贴-礼金', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(52, 'ACCOUNT_CONFIG', 'elemeSubsidyAmount', '饿了么平台补贴-活动', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(53, 'ACCOUNT_CONFIG', 'elemeRedPacket', '饿了么平台补贴-红包', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(54, 'ACCOUNT_CONFIG', 'merchantRedPacket', '商户补贴-红包', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(55, 'ACCOUNT_CONFIG', 'addition_service_price', '增值服务费总额', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(56, 'ACCOUNT_CONFIG', 'medical_card_pay', '医保卡支付', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(57, 'ACCOUNT_CONFIG', 'pay_channel_fee', '支付服务费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(58, 'ACCOUNT_CONFIG', 'shipping_cost', '应收运费', '24', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(59, 'ACCOUNT_CONFIG', 'goodsBill', '用户支付货款', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(60, 'ACCOUNT_CONFIG', 'dueAmount', '应结金额', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(61, 'ACCOUNT_CONFIG', 'freightBill', '运费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(62, 'ACCOUNT_CONFIG', 'packagingFeeAmount', '商家收包装费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(63, 'ACCOUNT_CONFIG', 'platformSubsidySuspend', '平台补贴暂扣', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(64, 'ACCOUNT_CONFIG', 'packageBill', '餐盒费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(65, 'ACCOUNT_CONFIG', 'commission', '总佣金(货款佣金+运费佣金+餐盒费佣金)', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(66, 'ACCOUNT_CONFIG', 'storeSubsidy', '商家承担补贴(货款+运费)', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(67, 'ACCOUNT_CONFIG', 'subsidy', '总补贴(平台+商家承担补贴)', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(68, 'ACCOUNT_CONFIG', 'marketBill', '平台承担补贴(市场费)', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(69, 'ACCOUNT_CONFIG', 'marketingServiceFee', '营销服务费用', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(70, 'ACCOUNT_CONFIG', 'originalAmount', '订单原价', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(71, 'ACCOUNT_CONFIG', 'paymentSubsidies', '商家承担货款补贴', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(72, 'ACCOUNT_CONFIG', 'storeFreightSubsidy', '商家承担运费补贴', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(73, 'ACCOUNT_CONFIG', 'storeFreightAmount', '商家自送配送费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(74, 'ACCOUNT_CONFIG', 'pickupServiceAmount', '商家承担小费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(75, 'ACCOUNT_CONFIG', 'performanceServiceFee', '基础服务费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(76, 'ACCOUNT_CONFIG', 'invoiceFee', '开票金额', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(77, 'ACCOUNT_CONFIG', 'platformFreightSubsidy', '平台运费补贴', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(78, 'ACCOUNT_CONFIG', 'fundPay', '医保统筹金额', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(79, 'ACCOUNT_CONFIG', 'psnAcctPay', '医保个账金额', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(80, 'ACCOUNT_CONFIG', 'ownPayAmt', '医保自付金额', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(81, 'ACCOUNT_CONFIG', 'supplierServiceAmount', '商家服务费', '11', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(82, 'ACCOUNT_CONFIG', 'orderAmount', '订单金额', '43', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(83, 'ACCOUNT_CONFIG', 'refundAmount', '退款金额', '43', '2025-03-12 13:42:16.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(84, 'ACCOUNT_EXCEPTION', 'OMS_NOT_ACCOUNT', '心云未下账', NULL, '2025-03-12 13:44:34.000'); INSERT INTO code_value (id, code_group, code_type, code_value, third_platform_code, sys_create_time) VALUES(85, 'ACCOUNT_EXCEPTION', 'CALCULATE_ERROR', '计算公式异常', NULL, '2025-03-12 13:44:34.000'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(14, '1783052553662110001', '11', 'settleAmount', '结算金额', '{"settleAmount": {"addRuleItem": ["originalAmount", "paymentSubsidies", "storeFreightAmount"], "deductRuleItem": ["marketBill"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-11 14:22:11.000', 2, 'JDDJ'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(16, '1783052553662110003', '11', 'brokerageAmount', '佣金', '{"brokerageAmount": {"addRuleItem": ["invoiceFee"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:51:46.000', 1, 'JDDJ'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(22, '1783052553662110009', '11', 'dueAmount', '回款金额', '{"dueAmount": {"addRuleItem": ["dueAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:51:46.000', 1, 'JDDJ'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(23, '1783052553662110010', '11', 'subside', '补贴', '{"subside": {"addRuleItem": ["marketBill"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:51:46.000', 1, 'JDDJ'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(24, '1783052553662110011', '11', 'merchantTip', '商家承担小费', '{"merchantTip": {"addRuleItem": ["pickupServiceAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:51:46.000', 1, 'JDDJ'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(25, '1783052553662240001', '24', 'settleAmount', '结算金额', '{"otherCompany": {"addRuleItem": ["product_fee", "merchantSubsidyAmount", "merchantCashGiftAmount", "merchantItemCouponAmount", "merchantRedPacket"], "deductRuleItem": ["merchantCouponAmount"]}, "tianJingCompany": {"addRuleItem": ["product_fee", "shipping_cost", "merchantSubsidyAmount", "merchantCashGiftAmount", "merchantItemCouponAmount", "merchantRedPacket", "merchantDeliverySubsidyAmount"], "deductRuleItem": ["merchantCouponAmount"]}, "shangHaiCompanyAndMerchantDelivery": {"addRuleItem": ["product_fee", "shipping_cost", "merchantSubsidyAmount", "merchantCashGiftAmount", "merchantRedPacket", "merchantDeliverySubsidyAmount"], "deductRuleItem": ["medical_card_pay", "merchantCouponAmount"]}, "shangHaiCompanyAndNoMerchantDelivery": {"addRuleItem": ["product_fee", "merchantSubsidyAmount", "merchantCashGiftAmount", "merchantItemCouponAmount", "merchantRedPacket"], "deductRuleItem": ["medical_card_pay", "merchantCouponAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-10 11:24:28.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(27, '1783052553662240003', '24', 'brokerageAmount', '佣金', '{"brokerageAmount": {"addRuleItem": ["commission", "actual_base_logistics_amount", "pay_channel_fee"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 12:01:26.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(28, '1783052553662240004', '24', 'platformDeliveryFee', '平台运费', '{"platformDeliveryFee": {"addRuleItem": ["send_fee", "merchantDeliverySubsidyAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 12:01:26.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(36, '1783052553662270001', '27', 'settleAmount', '结算金额', '{"merchantDelivery": {"addRuleItem": ["totalFoodAmount", "userPayShippingAmount"], "deductRuleItem": ["skuActSubsidy", "shippingActSubsidy"]}, "platformDelivery": {"addRuleItem": ["totalFoodAmount"], "deductRuleItem": ["skuActSubsidy"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-09 10:29:54.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(37, '1783052553662270002', '27', 'merchantFreightSubside', '商家承担运费补贴', '{"deliveryFee": {"deductRuleItem": ["settleAmount"]}, "takeoutMerchantDelivery": {"deductRuleItem": ["shippingActSubsidy"]}, "takeoutPlatformDelivery": {"deductRuleItem": ["userPayShippingAmount", "shippingActSubsidy"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:14:45.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(38, '1783052553662270003', '27', 'brokerageAmount', '佣金', '{"brokerageAmount": {"deductRuleItem": ["platformChargeFee"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:14:54.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(39, '1783052553662270004', '27', 'platformDeliveryFee', '平台运费', '{"platformDeliveryFee": {"deductRuleItem": ["performanceServiceFee"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:15:54.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(40, '1783052553662270005', '27', 'qisudaDeliveryFee', '企速达运费', '{"qikeDeliveryReturnFee": {"deductRuleItem": ["performanceServiceFee"]}, "noQikeDeliveryReturnFee": {"deductRuleItem": ["settleAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:17:32.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(41, '1783052553662270006', '27', 'timelyInsurance', '准时宝险', '{"insuranceFee": {"deductRuleItem": ["settleAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:18:40.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(42, '1783052553662270007', '27', 'reward', '奖励', '{"reward": {"addRuleItem": ["settleAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:19:39.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(43, '1783052553662270008', '27', 'customerCompensateFee', '客服赔付费用', '{"customerCompensateFee": {"deductRuleItem": ["settleAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-28 10:20:51.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(47, '1783052553662430001', '43', 'settleAmount', '结算金额', '{"settleAmount": {"addRuleItem": ["orderAmount"], "deductRuleItem": ["refundOrderAmount"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 15:36:12.000', 1, NULL); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(58, '1783052553662110012', '11', 'settleAmount', '结算金额', '{"settleAmount": {"addRuleItem": ["originalAmount", "paymentSubsidies", "storeFreightAmount"], "deductRuleItem": ["marketBill"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-11 10:56:16.000', 2, 'JDXSD'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(59, '1783052553662110013', '11', 'brokerageAmount', '佣金', '{"brokerageAmount": {"addRuleItem": ["invoiceFee"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:54:15.000', 1, 'JDXSD'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(60, '1783052553662110014', '11', 'dueAmount', '回款金额', '{"dueAmount": {"addRuleItem": ["dueAmount"], "deductRuleItem": ["marketBill"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:54:15.000', 1, 'JDXSD'); INSERT INTO reconciliation_config (id, business_no, third_platform_code, field_type, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version, extend) VALUES(61, '1783052553662110015', '11', 'subside', '补贴', '{"subside": {"addRuleItem": ["marketBill"]}}', 'system', 'system', '2025-03-12 13:42:16.000', '2025-04-07 13:54:15.000', 1, 'JDXSD'); ``` | 配置初始化 |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| business-gateway | application.yml | # 订单对账服务  - id: order-reconciliation  uri: [lb://order-reconciliation](lb://order-reconciliation)  predicates:  - Path=/order-reconciliation/**  filters:  - StripPrefix=1 | 网关新增对账服务路由 |
| order-reconciliation | application.yml |  | 新服务新增apollo配置 |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 关账 | order-reconciliation | closeReconciliationJobHandler | 0 0 8 * * ? |  |  |  |
| 订单对账 | order-reconciliation | reconciliationJobHandler | 0 0 0 * * ? |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 complete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |