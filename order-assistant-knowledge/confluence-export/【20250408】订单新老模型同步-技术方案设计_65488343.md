# 【20250408】订单新老模型同步-技术方案设计

# 表结构设计

## 新增表

| 表名 | 表结构 | 建表DDL | 描述 |
| --- | --- | --- | --- |
| order_sync_mapping | | 字段 | 类型 | 注释 | | --- | --- | --- | | id | bigint | 主键id，自增 | | origin_business_no | varchar(64) | 同步前业务单号 | | target_business_no | varchar(64) | 同步后业务单号 | | third_business_no | varchar(64) | 三方业务单号 | | third_platform_code | varchar(16) | 三方平台编码 | | org_code | varchar(64) | 机构编码 | | business_type | varchar(16) | O2O，B2C | | sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 | | remark | text | 备注信息 | | last_modify_time | datetime | 最新的更新时间，回环问题 | | created_time | datetime | 创建时间 | | updated_time | datetime | 更新时间 | | 字段 | 类型 | 注释 | id | bigint | 主键id，自增 | origin_business_no | varchar(64) | 同步前业务单号 | target_business_no | varchar(64) | 同步后业务单号 | third_business_no | varchar(64) | 三方业务单号 | third_platform_code | varchar(16) | 三方平台编码 | org_code | varchar(64) | 机构编码 | business_type | varchar(16) | O2O，B2C | sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 | remark | text | 备注信息 | last_modify_time | datetime | 最新的更新时间，回环问题 | created_time | datetime | 创建时间 | updated_time | datetime | 更新时间 | CREATE TABLE `order_sync_mapping` (   `id` bigint NOT NULL AUTO_INCREMENT,   `origin_business_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步前业务单号',   `target_business_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步后业务单号',   `third_business_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方业务单号',   `third_platform_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台编码',   `org_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构编码',   `business_type` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'O2O，B2C',   `sync_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ORDER_TO_NEW：正单老模型同步到新模型\r\nORDER_TO_OLD：正单新模型同步到老模型\r\nAFTER_SALE_TO_NEW：售后单老模型同步到新模型\r\nAFTER_SALE_TO_OLD：售后单新模型同步到老模型',   `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',   `last_modify_time` datetime DEFAULT NULL COMMENT '最新的更新时间，回环问题',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   PRIMARY KEY (`id`),   KEY `origin_biz_no` (`origin_business_no`),   KEY `target_biz_no` (`target_business_no`),   KEY `third_related` (`third_platform_code`,`third_business_no`),   KEY `org_related` (`business_type`,`org_code`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单同步映射表'; |  |
| 字段 | 类型 | 注释 |
| id | bigint | 主键id，自增 |
| origin_business_no | varchar(64) | 同步前业务单号 |
| target_business_no | varchar(64) | 同步后业务单号 |
| third_business_no | varchar(64) | 三方业务单号 |
| third_platform_code | varchar(16) | 三方平台编码 |
| org_code | varchar(64) | 机构编码 |
| business_type | varchar(16) | O2O，B2C |
| sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 |
| remark | text | 备注信息 |
| last_modify_time | datetime | 最新的更新时间，回环问题 |
| created_time | datetime | 创建时间 |
| updated_time | datetime | 更新时间 |
| order_sync_init_error_log | | 字段 | 类型 | 注释 | | --- | --- | --- | | id | bigint | 主键id，自增 | | business_no | varchar(64) | 同步前业务单号 | | sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 | | remark | text | 备注信息 | | last_modify_time | datetime | 最新的更新时间，回环问题 | | created_time | datetime | 创建时间 | | updated_time | datetime | 更新时间 | | 字段 | 类型 | 注释 | id | bigint | 主键id，自增 | business_no | varchar(64) | 同步前业务单号 | sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 | remark | text | 备注信息 | last_modify_time | datetime | 最新的更新时间，回环问题 | created_time | datetime | 创建时间 | updated_time | datetime | 更新时间 | CREATE TABLE `order_sync_init_error_log` (   `id` bigint NOT NULL AUTO_INCREMENT,   `business_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '同步前业务单号',   `sync_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ORDER_TO_NEW：正单老模型同步到新模型\r\nORDER_TO_OLD：正单新模型同步到老模型\r\nAFTER_SALE_TO_NEW：售后单老模型同步到新模型\r\nAFTER_SALE_TO_OLD：售后单新模型同步到老模型',   `last_modify_time` datetime DEFAULT NULL COMMENT '最新的更新时间',   `remark` text COLLATE utf8mb4_general_ci COMMENT '备注信息',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   PRIMARY KEY (`id`),   KEY `origin_biz_no` (`business_no`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单同步日志记录表'; |  |
| 字段 | 类型 | 注释 |
| id | bigint | 主键id，自增 |
| business_no | varchar(64) | 同步前业务单号 |
| sync_type | varchar(16) | ORDER_TO_NEW：正单老模型同步到新模型 ORDER_TO_OLD：正单新模型同步到老模型 AFTER_SALE_TO_NEW：售后单老模型同步到新模型 AFTER_SALE_TO_OLD：售后单新模型同步到老模型 |
| remark | text | 备注信息 |
| last_modify_time | datetime | 最新的更新时间，回环问题 |
| created_time | datetime | 创建时间 |
| updated_time | datetime | 更新时间 |


## 修改表

| 表名 | 修改表DDL | 描述 |
| --- | --- | --- |
| account_order | ALTER TABLE `dscloud`.`account_order` MODIFY COLUMN `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`, ALGORITHM = INPLACE, LOCK = NONE; | 设置update_time为【根据当前时间戳更新】 |
| account_refund | ALTER TABLE `dscloud`.`account_refund` MODIFY COLUMN `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`, ALGORITHM = INPLACE, LOCK = NONE; | 设置update_time为【根据当前时间戳更新】 |
| account_refund_detail | ALTER TABLE `dscloud`.`account_refund_detail` MODIFY COLUMN `update_time` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `create_time`, ALGORITHM = INPLACE, LOCK = NONE; | 设置update_time为【根据当前时间戳更新】 |
| offline_order |  | 修改order_tag_segment类型 |


# 详细设计

## 同步过程分解

### 【老模型】→ 【新模型】

1. 在还是依赖【老模型】进行订单履约的时候，就只涉及【老模型】→ 【新模型】增量+历史数据单向同步，不开启 【新模型】向【老模型】的同步，此时所有的订单信息以【老模型】为准
  1. 数据新增/更新，通过canal进行同步
  2. 历史数据同步则需要job方式进行异步处理
  3. true同步第一阶段-单向同步falseautotoptrue89213
2. 数据新增/更新，通过canal进行同步
3. 历史数据同步则需要job方式进行异步处理
4. true同步第一阶段-单向同步falseautotoptrue89213


### 【老模型】↔【新模型】

1. 在开始替换【老模型】接口时（应该先替换创单部分），开启 【新模型】向【老模型】的同步，此时就会有【双向同步】，即部分订单操作还是通过【老模型】，部分订单操作会在【新模型】，此时都会触发同步
  1. true订单同步-双向同步falseautotoptrue11416
2. true订单同步-双向同步falseautotoptrue11416


### 【新模型】→【老模型】

1. 订单操作全部替换到新模型之后，关闭【老模型】→ 【新模型】的同步，此时所有的订单信息以【新模型】为准
  1. true订单同步第三阶段-单向同步falseautotoptrue7021
2. true订单同步第三阶段-单向同步falseautotoptrue7021


## 同步过程中的问题

### 回环问题

1. 问题描述：以【老模型】→ 【新模型】为例，canal A监听【老模型】，当数据发生了变更，会往【新模型】进行同步；此时还有一个canal B在监听【新模型】，所以此时又会去往【老模型】进行同步，导致成为一个【环】，如下图：
  1. true回环问题falseautotoptrue7066
2. true回环问题falseautotoptrue7066
3. 如何解决？
  1. 构建【新模型】时，对于像 update_time 这种设置了【根据当前时间戳】的时间字段，需要原样 copy 【老模型】的值。但这种方式还是会触发 C 和 D 的步骤去操作一遍【老模型】数据表，但如果此时构建的【老模型】数据和原来一模一样的话，那此时【老模型】就不会有实际数据变更，那么就不会有 binlog 产生，所以 新一轮的 【A1】步骤就不会发生，此时会断掉回环
    1. true回环问题1falseautotoptrue7063
  2. true回环问题1falseautotoptrue7063
  3. 使用update_time实现乐观锁，在每次发生同步的时候，将 update_time 进行保存，每次进行同步时，先判断一次时间，如果当前数据变更的 update_time <= 上次的 update_time ，记录 remark 并退出。
    1. true回环问题图2falseautotoptrue10753
  4. true回环问题图2falseautotoptrue10753
  1. true回环问题1falseautotoptrue7063
  1. true回环问题图2falseautotoptrue10753
4. 构建【新模型】时，对于像 update_time 这种设置了【根据当前时间戳】的时间字段，需要原样 copy 【老模型】的值。但这种方式还是会触发 C 和 D 的步骤去操作一遍【老模型】数据表，但如果此时构建的【老模型】数据和原来一模一样的话，那此时【老模型】就不会有实际数据变更，那么就不会有 binlog 产生，所以 新一轮的 【A1】步骤就不会发生，此时会断掉回环
  1. true回环问题1falseautotoptrue7063
5. true回环问题1falseautotoptrue7063
6. 使用update_time实现乐观锁，在每次发生同步的时候，将 update_time 进行保存，每次进行同步时，先判断一次时间，如果当前数据变更的 update_time <= 上次的 update_time ，记录 remark 并退出。
  1. true回环问题图2falseautotoptrue10753
7. true回环问题图2falseautotoptrue10753


## 模型同步图解

### 历史数据同步

1. 什么样的数据算历史数据？
  1. 已完结的订单、退单
2. 已完结的订单、退单
3. 截至 2025-04-14，数据统计：
  1. 全部订单： 1385951
  2. B2C（'WSC1111','WSC0000','ALQ1'，'AJG5'）
    1. 全部订单：614262
    2. 未支付已取消订单：208966
    3. 已完结订单（已完成、已退款）：404106
    4. 未支付已完结订单（貌似是雨诺迁移订单）：84285
    5. 分月统计如下：
      1. 
    6. 
    1. 
  3. 全部订单：614262
  4. 未支付已取消订单：208966
  5. 已完结订单（已完成、已退款）：404106
  6. 未支付已完结订单（貌似是雨诺迁移订单）：84285
  7. 分月统计如下：
    1. 
  8. 
  9. O2O
    1. 全部订单：771689
    2. 未支付已取消订单：29032
    3. 已完结订单（已完成、已退款）：675077
    4. 分月统计数据如下：
      1. 
    5. 
    1. 
  10. 全部订单：771689
  11. 未支付已取消订单：29032
  12. 已完结订单（已完成、已退款）：675077
  13. 分月统计数据如下：
    1. 
  14. 
  1. 全部订单：614262
  2. 未支付已取消订单：208966
  3. 已完结订单（已完成、已退款）：404106
  4. 未支付已完结订单（貌似是雨诺迁移订单）：84285
  5. 分月统计如下：
    1. 
  6. 
  1. 
  1. 全部订单：771689
  2. 未支付已取消订单：29032
  3. 已完结订单（已完成、已退款）：675077
  4. 分月统计数据如下：
    1. 
  5. 
  1. 
4. 全部订单： 1385951
5. B2C（'WSC1111','WSC0000','ALQ1'，'AJG5'）
  1. 全部订单：614262
  2. 未支付已取消订单：208966
  3. 已完结订单（已完成、已退款）：404106
  4. 未支付已完结订单（貌似是雨诺迁移订单）：84285
  5. 分月统计如下：
    1. 
  6. 
  1. 
6. 全部订单：614262
7. 未支付已取消订单：208966
8. 已完结订单（已完成、已退款）：404106
9. 未支付已完结订单（貌似是雨诺迁移订单）：84285
10. 分月统计如下：
  1. 
11. 
12. O2O
  1. 全部订单：771689
  2. 未支付已取消订单：29032
  3. 已完结订单（已完成、已退款）：675077
  4. 分月统计数据如下：
    1. 
  5. 
  1. 
13. 全部订单：771689
14. 未支付已取消订单：29032
15. 已完结订单（已完成、已退款）：675077
16. 分月统计数据如下：
  1. 
17. 
18. 使用xxl-job通过任务触发同步
  1. true历史数据同步falseautotoptrue8215
19. true历史数据同步falseautotoptrue8215


### 监听老模型新增/更新数据并同步到新模型（以正向单为例）

true正单增量-更新数据同步falseautotoptrue165112

# canal监听配置

| 实例名 | 监听表以及过滤字段 | 备注 |
| --- | --- | --- |
| order_old_order_model | | 类型 | 表 | 过滤字段 | 备注 | | --- | --- | --- | --- | | B2C | account_order | order_no\update_time | 销售单下账表(update_time未设置"根据当前时间戳更新") | | account_order_detail | order_no\updated_time | 销售单下账明细表 | | oms_order_info | order_no\modify_time | B2C订单表 | | 公共 | order_info | order_no\modify_time | 订单主表 | | order_delivery_address | order_no\modify_time | 订单收货地址表 | | order_delivery_record | order_no\modify_time | 订单配送记录 | | order_detail | order_no\modify_time | 订单明细 | | order_pay_info | order_no\modify_time | 支付信息表 | | erp_bill_info | order_no\modify_time | ERP下账金额信息表 | | O2O | medical_trace_code | order_no\modify_time | 追溯码 | | 类型 | 表 | 过滤字段 | 备注 | B2C | account_order | order_no\update_time | 销售单下账表(update_time未设置"根据当前时间戳更新") | account_order_detail | order_no\updated_time | 销售单下账明细表 | oms_order_info | order_no\modify_time | B2C订单表 | 公共 | order_info | order_no\modify_time | 订单主表 | order_delivery_address | order_no\modify_time | 订单收货地址表 | order_delivery_record | order_no\modify_time | 订单配送记录 | order_detail | order_no\modify_time | 订单明细 | order_pay_info | order_no\modify_time | 支付信息表 | erp_bill_info | order_no\modify_time | ERP下账金额信息表 | O2O | medical_trace_code | order_no\modify_time | 追溯码 | 订单老模型正单数据表变更监听 |
| 类型 | 表 | 过滤字段 | 备注 |
| B2C | account_order | order_no\update_time | 销售单下账表(update_time未设置"根据当前时间戳更新") |
| account_order_detail | order_no\updated_time | 销售单下账明细表 |
| oms_order_info | order_no\modify_time | B2C订单表 |
| 公共 | order_info | order_no\modify_time | 订单主表 |
| order_delivery_address | order_no\modify_time | 订单收货地址表 |
| order_delivery_record | order_no\modify_time | 订单配送记录 |
| order_detail | order_no\modify_time | 订单明细 |
| order_pay_info | order_no\modify_time | 支付信息表 |
| erp_bill_info | order_no\modify_time | ERP下账金额信息表 |
| O2O | medical_trace_code | order_no\modify_time | 追溯码 |
| order_old_refund_model | | 类型 | 表 | 过滤字段 | 备注 | | --- | --- | --- | --- | | B2C | after_sale_order | refund_no\update_time | 售后单表 | | account_refund | refund_no\update_time | 退款单下账表 | | account_refund_detail | refund_no\update_time | 退款单下账单明细表 | | return_goods_order | after_sale_no\return_goods_no\modify_time | 退货单表 | | 公共 | erp_refund_info | refund_no\modify_time | ERP退款下账金额信息表 | | refund_detail | refund_no\modify_time | 退款明细表 | | refund_order | refund_no\modify_time | 退款单 | | 类型 | 表 | 过滤字段 | 备注 | B2C | after_sale_order | refund_no\update_time | 售后单表 | account_refund | refund_no\update_time | 退款单下账表 | account_refund_detail | refund_no\update_time | 退款单下账单明细表 | return_goods_order | after_sale_no\return_goods_no\modify_time | 退货单表 | 公共 | erp_refund_info | refund_no\modify_time | ERP退款下账金额信息表 | refund_detail | refund_no\modify_time | 退款明细表 | refund_order | refund_no\modify_time | 退款单 | 订单老模型逆单数据表变更监听 |
| 类型 | 表 | 过滤字段 | 备注 |
| B2C | after_sale_order | refund_no\update_time | 售后单表 |
| account_refund | refund_no\update_time | 退款单下账表 |
| account_refund_detail | refund_no\update_time | 退款单下账单明细表 |
| return_goods_order | after_sale_no\return_goods_no\modify_time | 退货单表 |
| 公共 | erp_refund_info | refund_no\modify_time | ERP退款下账金额信息表 |
| refund_detail | refund_no\modify_time | 退款明细表 |
| refund_order | refund_no\modify_time | 退款单 |
| order_new_order_model | canal对于分表数据如何对过滤字段进行设置还需研究| 表 | 过滤字段 | 备注 | | --- | --- | --- | | offline_order_* | order_no\sys_update_time | 正单主表 | |  |  |  | | 表 | 过滤字段 | 备注 | offline_order_* | order_no\sys_update_time | 正单主表 |  |  |  | 订单新模型正单数据表变更监听 |
| 表 | 过滤字段 | 备注 |
| offline_order_* | order_no\sys_update_time | 正单主表 |
|  |  |  |
| order_new_afterSale_model | | 表 | 过滤字段 | 备注 | | --- | --- | --- | | order_after_sale.after_sale_order_* | after_sale_no\sys_update_time | 售后单主表 | | 表 | 过滤字段 | 备注 | order_after_sale.after_sale_order_* | after_sale_no\sys_update_time | 售后单主表 | 订单新模型售后单数据表变更监听 |
| 表 | 过滤字段 | 备注 |
| order_after_sale.after_sale_order_* | after_sale_no\sys_update_time | 售后单主表 |