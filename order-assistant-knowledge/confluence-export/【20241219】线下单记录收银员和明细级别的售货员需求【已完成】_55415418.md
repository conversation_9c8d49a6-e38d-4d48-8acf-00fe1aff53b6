# 【20241219】线下单记录收银员和明细级别的售货员需求【已完成】

order-service

order-sync

order-atom-service

分支: feature-saler-info-20241219

版本前缀: saler-

SDK:

  10 complete order-open-message release   11 complete order-open-sdk release   12 complete order-atom-sdk release  

    <dependency>
      <groupId>com.yxt.order.open.message</groupId>
      <artifactId>order-open-message</artifactId>
      <version>saler-SNAPSHOT</version>
    </dependency>

	<dependency>
      <groupId>com.yxt.order.open.sdk</groupId>
      <artifactId>order-open-sdk</artifactId>
      <version>saler-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>saler-SNAPSHOT</version>
    </dependency>

DB:

  4 complete dev   5 complete test   6 complete prod 需要提交脚本  

ALTER TABLE `offline_order_detail_${seq}` ADD COLUMN `saler_id` varchar(20) NULL COMMENT '售货员Id' ,ADD COLUMN `saler_name` varchar(20) NULL COMMENT '售货员Name' ;
ALTER TABLE `offline_refund_order_detail_${seq}` ADD COLUMN `saler_id` varchar(20) NULL COMMENT '售货员Id',ADD COLUMN `saler_name` varchar(20) NULL COMMENT '售货员Name';