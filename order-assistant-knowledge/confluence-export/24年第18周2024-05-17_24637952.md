# 24年第18周2024-05-17

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25号讨论完O2O订单域核心服务入参 | 创单-润康拣货、换货-国华 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审4月26日进入开发阶段5月17日抖店开发完成，待部署测试 |  | 下一月的目标。 |
| 3 | 优雅发布升级 |  | 4月12日完成待办列表,推动全部门升级 | 已完成 | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  | 已完成版本升级, 周一已经部署到开发、测试环境 | 下周三待上线 | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档 | 暂停 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | 支付中台重构 |  |  | 暂停 |  |
| 8 | Rocketmq |  |  | 已经梳理出规范,待评审 |  |
| 9 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 10 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |


### 二、本周工作情况

**1.总览**

本周项目总览

1.订单路由 上线 5.14/去除智能配送功能 @王世达 

2.对接极兔/邮政 提测 5.15中午 测试(@杨飞 @龙敏 ) 争取5.18周六代码上线.5.20业务使用 @焦钰斌 

3.订单运维 上线 5.14/去除配送平台配送单号变更为平台单号 @杨国枫 

4.美团隐私/京东下账调整 提测 5.13 . 5.21上线 @王世达 

5.B2C员工绩效返利 进入开发 5.14 @杨国枫 

6.吉客云 暂缓

7.支付宝 暂缓 

8.1 科传下账 进度正常 @王世达 

8.2 京东打点回传 提测 5.13 @杨俊峰 

8.3 B2C下账失败 进度正常 @李洋 

**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1.新增吉客运订单2.物流回滚，分摊金额全量上线3.-微商城员工B2C订单统计技术方案编写/评审4.推广记录新增流程 | **计划工作**1.新增吉客运订单2.物流回滚，分摊金额全量上线3.-微商城员工B2C订单统计技术方案编写/评审4.推广记录新增流程**㊁实际完成**1.新增吉客运订单2.物流回滚，分摊金额全量上线3.-微商城员工B2C订单统计技术方案编写/评审4.推广记录新增流程 **㊂遗留问题**1.微商城B2C推广业绩统计**㊃风险问题** **㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关**微商城B2C推广业绩统计**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 2 | 杨润康 | **本周总工时：5d**1. api网关调整 1d   1. 使用自定义的健康检查接口 已上线   2. k8s健康检查时间从150s调整到30s 已上线   3. IO_WOKER_COUNT、IO_SELECT_COUNT参数验证 进行中 2. 使用自定义的健康检查接口 已上线 3. k8s健康检查时间从150s调整到30s 已上线 4. IO_WOKER_COUNT、IO_SELECT_COUNT参数验证 进行中 5. businesses网关   1. 部分错误日志调整，已上线   2. 已完成版本升级,开发、测试环境已经部署，预计下周三上线 6. 部分错误日志调整，已上线 7. 已完成版本升级,开发、测试环境已经部署，预计下周三上线 8. 网关(2个)拦截/actuator/shutdown接口 9. businesses-order-web开发测试环境一直重启问题和运维一起排查,健康检查时间过短导致 10. 线下单   1. 海典优惠分摊金额取值调整   2. 科传线下单同步接口开发(70%)   3. 联调(造数) 11. 海典优惠分摊金额取值调整 12. 科传线下单同步接口开发(70%) 13. 联调(造数) | **㊀计划工作****下周三上线businesses-gateway大版本升级****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 3 | 杨俊峰 | **本周总工时：5day**1. 商品信息变更通知加强 0.5day 2. 京东到家骑手信息上传 1day 3. 饿了么门店获取接口更新 0.5 day 4. 打印改造升级 2.5 day (目前发货单只处理了 58宽度的小票模板，A4模板没有处理) 5. 线上问题支援 0.5 day | **㊀计划工作****㊁实际完成****㊂遗留问题** **㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 4 |  | **本周总工时：5**1. 拣货复核批次校验问题 2. .net重构：   1. 抖店部分已完成开发   2. 5-16下午跟运维提了服务部署申请   3. 原定今天提测，但运维同学还未部署，延缓到下周二 3. 抖店部分已完成开发 4. 5-16下午跟运维提了服务部署申请 5. 原定今天提测，但运维同学还未部署，延缓到下周二 6. 其他问题支持 | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 |  | **本周总工时：5pd**1. 吉客云回调更新发货信息 2. B2C商品推广技术方案评审，接口文档编写，B端推广记录的查询与导出 3. 线上告警云仓商品发货报错 | **㊀计划工作****㊁实际完成****㊂遗留问题**B2C商品推广**㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：5pd**1.智能选择配送（需要出需求重做）2.店铺设置物流公司接口联调3.快递公司logo生成4.sql脚本5.极兔、邮政上线配置6.提供商品中台接口，获取旗舰店仓库信息7.面单收件人信息加密 | **㊀计划工作**1.店铺设置物流公司接口联调2.脚本和文件url生成3.极兔、邮政线上配置**㊁实际完成**1.店铺设置物流公司接口联调2.脚本和文件url生成3.极兔、邮政线上配置**㊂遗留问题**1.智能选择配送需要重构2.店铺设置物流公司接口联调**㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 7 |  | **本周总工时：** 1. 订单路由 已上线 2. 科传下账订单号修改+支付方式 测试中 财务验收阶段 3. 美团隐私信息处理 测试中 4. 拼多多/菜鸟/京东 面单打印联调 京东无界打印配置完成 5. 云仓退款处理(退款单生成) | **㊀计划工作****㊁实际完成****㊂遗留问题****1. a.****美团隐私处理B2C平台订单与系统订单收货地址存储位置不同。****b. B2C解密需要解析省市区（用处排查）****2. 微商城退款问题，需要提需求到产品，增加主动退款功能****3. 路由查单，需要后期和产品沟通，单独开发页签进行查看，剥离当前业务流程****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 8 |  | **本周总工时：**1. 订单路由Bug修复与上线 2. B2C/O2O下账失败修改批次（测试中） 3. 重庆与攀枝花海典H2下账仅退款订单数据核对 | **㊀计划工作**1. 路由上线 2. B2C/O2O下账失败修改批次上线 **㊁实际完成**1. 路由上线 **㊂遗留问题**1. B2C/O2O下账失败修改批次上线 2. 支付宝流程接入 **㊃风险问题** **㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 9 |  | **本周总工时：5pd** | **㊀计划工作****㊁实际完成****㊂遗留问题** | **㊀需求研发相关**1. 邮政对接/极兔对接/默认快递技术方案编写 |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |