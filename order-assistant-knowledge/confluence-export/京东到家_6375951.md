# 京东到家

# 1.开放平台地址:https://opendj.jd.com/index.html

# 2.接口系统级参数

| **字段** | **类型** | **是否必须** | **描述** |
| token | String | 是 | 采用OAuth授权方式为必填参数 |
| app_key | String | 是 | 应用的app_key |
| sign | String | 是 | 签名 |
| timestamp | String | 是 | 时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2011-06-16 13:23:30 |
| format | String | 是 | 暂时只支持json |
| v | String | 是 | API协议版本，可选值:1.0 |


# 3.订单类API

## 3.1 订单列表查询接口

**获取“订单明细”唯一标准接口，查询时间限制每小时分段查询，避免订单分页查询页数过多,导致查询效率低问题； 注:单次请求入参要求pageNo*pageSize<10000，否则查询异常，返回结果为空。 目前（小时达、天选）订单skuIds(记录参加活动的sku数组)，不做记录。您需要调取：【新版订单金额拆分接口】进行获取sku的商家承担金额以及平台承担金额。 此接口仅支持查询最近一年的订单数据（未完成订单不受时间限制）**

### 3.1.1 接口地址:https://openapi.jddj.com/djapi/order/es/query

### 3.1.2 入参

| **字段** | **类型** | **是否必须** | **示例值** | **描述** |
| pageNo | Long | 否 | 2 | 当前页数,默认：1 |
| pageSize | Integer | 否 | 30 | 每页条数,默认：20，最大值100 |
| orderId | Long | 否 | 100001036354906 | 订单号（如果传了订单号，其他条件不生效） |
| buyerFullName | String | 否 | 张三 | 客户名 |
| buyerFullName_like | String | 否 | 张 | 客户名（模糊查询） |
| buyerMobile | String | 否 | 15600000000 | 手机号 |
| orderPayType | Integer | 否 | 4 | 订单支付类型（1：货到付款，4：在线支付） |
| buyerPin | String | 否 | JD_2aeh208df8789 | 买家账号 |
| orderStartTime_begin | Date | 否 | 2016-05-05 00:00:00 | 订单开始时间(开始) |
| orderStartTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单开始时间(结束) |
| orderPurchaseTime_begin | Date | 否 | 2016-05-05 00:00:00 | 购买成交时间-支付(开始) |
| orderPurchaseTime_end | Date | 否 | 2016-05-08 23:00:00 | 购买成交时间-支付(结束) |
| deliveryConfirmTime_begin | Date | 否 | 2016-05-08 00:00:00 | 妥投时间(开始) |
| deliveryConfirmTime_end | Date | 否 | 2016-05-08 23:00:00 | 妥投时间(结束) |
| orderCloseTime_begin | Date | 否 | 2016-05-08 00:00:00 | 订单关闭时间(开始) |
| orderCloseTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单关闭时间(结束) |
| orderCancelTime_begin | Date | 否 | 2016-05-08 00:00:00 | 订单取消时间(开始) |
| orderCancelTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单取消时间(结束) |
| orderStatus | Integer | 否 | 32000 | 订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成） |
| +orderStatus_list | Set | 否 |  | 订单状态复选条件 |
| +buyerCity_list | Set | 否 |  | 城市复选条件 |
| deliveryBillNo | String | 否 | 1000001 | 承运单号，通常情况下和订单号一致 |
| businessType_list | Integer[] | 否 | 1,2 | 业务类型（1:京东到家商超,2:京东到家美食,3:京东到家精品有约,4:京东到家开放仓,5:哥伦布店内订单,6:货柜项目订单,7:智能货柜项目订单,8:轻松购订单,9:自助收银订单,10:超级会员码），当多个业务类型时，是以逗号分隔的数值串。 |
| orderType | Integer | 否 | 10000 | 订单类型 10000:从门店出的订单 |
| orderTakeSelfCode | String | 否 | eRowg | 订单自提码，当该字段有值时，要求到家配送门店编码必填。 |
| deliveryStationNo | String | 否 | 1000001 | 到家门店编码 |
| deliveryStationNoIsv | String | 否 | 2000001 | 商家门店编码 |
| srcOrderId | String | 否 | *********** | 订单来源系统(比如京东订单号) |
| returnedFields | String[] | 否 | ["orderId","discount","product"] | 设置返回结果(data.result.resultList中)的字段，字段间用英文逗号隔开，不传返回全部字段。 |


### 3.1.3 出参

| **字段** | **类型** | **示例值** | **描述** |
| code | String | 0 | 0表示成功，其他均为失败。 详见:[到家平台状态码说明](https://opendj.jd.com/staticnew/widgets/doc/portalCodes.html) |
| msg | String | 操作成功 | 结果描述 |
| -data | String |  | 返回对象，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。 |
| code | String | 0 | 状态码，0为成功，非0均为失败，其中（1:参数错误，-4为订单中心底层ES中间件服务抖动异常，请重试） |
| msg | String | 操作成功 | 描述信息 |
| -result | String |  | 数据 |
| pageNo | Integer | 1 | 当前页数,默认：1 |
| pageSize | Integer | 20 | 每页条数,默认：20，超过100也只返回100条 |
| maxPageSize | Integer | 100 | 每页最大条数 |
| totalCount | Integer | 100 | 数据总条数 |
| -resultList | List<OrderInfoDTO> |  | 包含需要查询订单的List列表 |
| orderId | Long | 100001036354906 | 订单号 |
| srcInnerType | Integer | 0 | 订单来源类型(0:原订单，10:退款单，20:补货单，30:直赔商品 ，40:退货 ，50:上门店换新) |
| +orderCustomerServiceRemark | String |  | 订单客服备注，如果是小时达订单, parentId标识小时达订单父单Id ,如果parentId=0或者为空表示没有父单 |
| orderType | Integer | 10000 | 订单类型（10000：从门店出的订单） |
| orderStatus | Integer | 20010 | 订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成） |
| orderStatusTime | Date | 2016-07-09 18:34:07 | 订单状态最新更改时间 |
| orderVenderRemark | String | 订单商家备注 | 订单商家备注 |
| orderStartTime | Date | 2016-07-09 15:40:30 | 下单时间 |
| orderPurchaseTime | Date | 2016-07-09 15:40:50 | 订单成交时间(在线支付类型订单的付款完成时间) |
| orderAgingType | Integer | 12 | 订单时效类型(0:无时效;2:自定义时间;1:次日达;27:七小时达;24:六小时达;21:五小时达;18:四小时达;15:三小时达;12:两小时达;9:一小时达;6:半小时达;10：一个半小时达；13：两个半小时达；16：三个半小时达；42：十二小时达；78：二十四小时达；150：四十八小时达；151：七十二小时达；251：7天；300：30天；351：90天；500：365天；) |
| orderPreStartDeliveryTime | Date | 2016-07-09 17:41:00 | 预计送达开始时间（最快开始配送时间） |
| orderPreEndDeliveryTime | Date | 2016-07-09 17:41:00 | 预计送达结束时间（最晚配送完成时间） |
| pickDeadline | Date | 2016-07-09 17:41:00 | 商家最晚拣货完成时间 |
| orderCancelTime | Date | 2016-07-09 18:34:07 | 订单取消时间 |
| orderCancelRemark | String | 其它 | 订单取消备注 |
| orgCode | String | 71948 | 商家编码 |
| buyerPin | String | JD_2aeh208df8789 | 买家账号 |
| buyerFullName | String | 王小明 | 收货人名称 |
| buyerFullAddress | String | 上海市徐汇区乐山路19号广元西路乐山路，乐山路19号 | 收货人地址 |
| buyerTelephone | String | 18816912316 | 收货人电话 |
| buyerMobile | String | 18816912316 | 收货人手机号 |
| lastFourDigitsOfBuyerMobile | String | 8367 | 收货人真实手机号后四位 |
| deliveryStationNo | String | 11000478 | 到家配送门店编码 |
| deliveryStationNoIsv | String | 10100478 | 商家门店编码 |
| deliveryStationName | String | SH031永和大王-广元店 | 配送门店名称 |
| deliveryCarrierNo | String | 9966 | 承运商编号(9966:达达专送;2938:商家自送;3587:同城快递;9999:到店自提) |
| deliveryCarrierName | String | 众包配送 | 承运商名称 |
| deliveryBillNo | String | 1000001 | 承运单号，通常情况下和订单号一致 |
| deliveryPackageWeight | Double | 0.10000000149011612 | 包裹重量（单位：kg） |
| deliveryConfirmTime | Date | 2016-07-09 18:34:07 | 妥投时间 |
| orderPayType | Integer | 4 | 订单支付类型(1：货到付款，4:在线支付;) |
| payChannel | Integer | 8001 | 订单支付渠道，8001：微信支付；8002：微信免密代扣；8003：微信找人代付；9000：京东支付（无法判断具体京东支付子类型）；9002：京东银行卡支付；9004：京东收银台-京东白条；9012：京东余额支付；9014：京东白条；9022：京东小金库支付；4001： apple pay ；4002：云闪付【小时达、天选业务此字段无数据】 |
| orderTotalMoney | Long | 2500 | 订单商品销售价总金额，等于sum（京东到家销售价skuJdPrice*商品下单数量skuCount） |
| orderDiscountMoney | Long | 0 | 订单级别优惠商品金额：(不含单品促销类优惠金额及运费相关优惠金额)，等于OrderDiscountlist表中，除优惠类型7，8，12，15，16，18，外的优惠金额discountPrice累加和 |
| orderFreightMoney | Long | 300 | 用户支付的实际订单运费：订单应收运费（orderReceivableFreight）-运费优惠（OrderDiscountlist表中，优惠类型7，8，12，15，16，18，的优惠金额。运费优惠大于应收运费时，实际支付为0 |
| orderBaseFreightMoney | Long | 300 | 订单基础运费，应收运费为零此字段为空，自提订单无此字段数据 |
| localDeliveryMoney | Integer | 500 | 达达同城送运费(单位：分) |
| merchantPaymentDistanceFreightMoney | Long | 100 | 商家支付远距离运费(单位：分)。达达配送默认只能服务半径2公里内的用户，商家可与到家运营沟通开通远距离服务，超过2公里后每1公里加收2元运费。费用承担方为商家 |
| orderReceivableFreight | Long | 300 | 订单应收运费：用户应该支付的订单运费，即未优惠前应付运费(不计满免运费，运费优惠券，VIP免基础运费等优惠)，包含用户小费。订单对应门店配送方式为商家自送，则订单应收运费为设置的门店自送运费；订单对应门店配送方式为达达配送，则订单应收运费为用户支付给达达的配送费（平台规则统一设置，如基础运费、重量阶梯运费、距离阶梯运费、夜间或天气等因素的附加运费） |
| orderDistanceStepFreight | Long | 120 | 距离阶梯运费 |
| platformPointsDeductionMoney | Long | 100 | 用户积分抵扣金额 |
| orderBuyerPayableMoney | Long | 2800 | 用户应付金额（单位为分）=商品销售价总金额orderTotalMoney -订单优惠总金额 orderDiscountMoney+实际订单运费orderFreightMoney +包装金额packagingMoney -用户积分抵扣金额platformPointsDeductionMoney |
| packagingMoney | Long | 300 | 包装金额 |
| tips | Long | 200 | 商家给配送员加的小费，原订单配送方式为达达配送，在运单状态为待抢单且超时5分钟后，商家可以转成自己配送，转自送后，如果订单商家有小费，则商家小费清零。到家系统会下发订单转自送消息，商家需订阅转自送消息，再次调用订单列表查询接口获取订单信息 |
| adjustIsExists | Boolean | false | 订单商品缺货时，可以调用订单调整接口调整订单，调整之后需再次调用订单查询接口查询订单详情。该字段表示是否存在订单调整(false:否;true:是) |
| adjustId | Long | 0 | 调整单编号 |
| isGroupon | Boolean | false | 是否拼团订单(false:否;true:是) |
| buyerCoordType | Integer | 2 | 收货人地址定位类型（buyerCoordType值为空或为1时，定位类型为gps，如为其他值时，定位类型为非gps类型。) |
| buyerLng | Double | 121.4332 | 收货人地址腾讯坐标纬度 |
| buyerLat | Double | 31.19627 | 收货人地址腾讯坐标经度 |
| buyerCity | String | 1 | 收货人市ID |
| buyerCityName | String | 北京市 | 收货人市名称 |
| buyerCountry | String | 72 | 收货人县(区)ID |
| buyerCountryName | String | 朝阳区 | 收货人县(区)名称 |
| orderBuyerRemark | String | 所购商品如遇缺货，您需要：其它商品继续配送（缺货商品退款） | 订单买家备注 |
| businessTag | String | dj_new_cashier;one_dingshida;picking_up; | 业务标识，用英文分号分隔（订单打标写入此字段，如one_dingshida 定时达，dj_aging_nextday 隔夜达，dj_aging_immediately 立即达，picking_up 拣货完成，pay_off 支付完成,lengcang 冷藏,lengdong 冷冻,changwen 常温,yisui 易碎,yeti 液体,yiyao 医药,dj_prescription_order 处方药订单,printed 已打印,first_order 平台首单,user_urge_order 用户催单,dj_timeout_pay 享受超时赔付,dj_giftcard_consume 订单使用礼品卡,dj_vender_vip 商家会员,easy_first_order 轻松购首单用户,dj_vender_sam 山姆会员店,dj_money_ice 结算页购买冰袋,dj_purchase_vip 结算页购买vip,dj_prepare_sale_order 拼团预售订单,dj_order_store_shared_win_first （门店共赢自提单）或 dj_shared_win_new（新门店共赢自提单）；dj_order_no_pickcode（无需验证提货码）；dj_weight_refund_difference（称重退差））；vender_first_order（商家新用户/门店新客）；OC00006（小时达药急送）; OC00019 (医保订单)”； |
| equipmentId | String | e6008a0091df6ef9bb4ab499804dc487 | 设备id |
| buyerPoi | String | 东大街东里社区 | 收货人POI信息 |
| ordererName | String |  | 订购人姓名(此字段针对鲜花业务) |
| ordererMobile | String |  | 订购人电话(此字段针对鲜花业务) |
| orderNum | Long | 1 | 当天门店订单序号 |
| userTip | Long | 10 | 用户小费（用户给配送员加小费） |
| middleNumBindingTime | Date | 2016-07-09 17:41:00 | 收货人电话中间号有效期 |
| deliverInputTime | Date | 2016-07-09 18:41:00 | 订单抛入达达抢单池时间 |
| businessType | Integer | 1 | 订单业务类型(1：京东到家商超，2：京东到家美食，4：京东到家开放仓，5：哥伦布店内订单，6：货柜订单，8：轻松购订单，9：是自助收银，10：超级会员码) |
| venderVipCardId | String | A100001 | 商家会员VIP卡号 |
| orderInvoiceOpenMark | Integer | 2 | 订单开发票标识（1.开发票；2.不开发票） |
| specialServiceTag | String | [https://img30.360buyimg.com/myorderpdj/s960x1280_jfs/t2345/1.jpg](https://img30.360buyimg.com/myorderpdj/s960x1280_jfs/t2345/1.jpg) | 处方订单处方单图片地址，当处方单未开具时，不会返回该字段信息 |
| srcOrderType | String | 0 | 订单来源系统，枚举如下：(0, 京东到家),(1, 到家app),(2, 到家h5),(3, 京东搜索订单),(5, 微信小程序),(6, 线下购物),(7, 好收成),(8, 自助收银),(9, 自营店pos机),(10, 售后),(11, 自营店自助收银),(12, 商家小程序),(13, 超级会员-自助收银-到家app),(14, 自营店轻松购),(15, 超级会员-自助收银-小程序),(16, 超级会员-POS-到家app),(17, 超级会员-POS-小程序),(18, 百度小程序),(19, QQ小程序),(20,京东融合【小时达】),(21,头条小程序），(22,医药小程序)，(23,京东天选);（24,支付宝小程序） |
| srcOrderId | String |  | 订单来源系统(比如京东订单号) |
| +orderInvoice | OrderInvoice |  | 发票具体信息 |
| +product | List<OrderProductDTO> |  | 包含需要查询订单的商品List列表 |
| +discount | List<OrderDiscountDTO> |  | 包含需要查询订单的优惠List列表 |
| +prescriptionDTO | PrescriptionDTO |  | 处方药详情信息（包含订单预售类字段） |
| +orderJdSendpay | String |  | 自提服务费信息-JSON格式 |
| +orderGiftCardDetailDTO | OrderGiftCardDetailDTO |  | 礼品卡相关信息 |
| detail | String |  | 业务处理详情 |


# 门店类API

## 根据门店号查询门店运费配置信息

### 接口地址:https://openapi.jddj.com/djapi/freight/queryStoreFreightConfigNew

### 入参

| **字段** | **类型** | **是否必须** | **示例值** | **描述** |
| stationNo | String | 是 |  | 门店编号 |


### 出参

| **字段** | **类型** | **示例值** | **描述** |
| code | String | 0 | 0表示成功，其他均为失败。 详见:[到家平台状态码说明](https://opendj.jd.com/staticnew/widgets/doc/portalCodes.html) |
| msg | String | 操作成功 | 结果描述 |
| -data | String |  | 返回值，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。 |
| code | String |  | 成功失败标识 0:成功 其它：失败 |
| msg | String |  | 成功失败描述 |
| detail | String |  | 详细信息 |
| -result | StoreFreightConfigDTO |  | 返回结果 |
| stationNo | String |  | 门店编号 |
| carrierNo | Integer |  | 承运商编号 |
| startMoney | Long |  | 起送价(分) |
| createTime | Date |  | 创建时间 |
| updateTime | Date |  | 更新时间 |
| createPin | String |  | 创建用户 |
| updatePin | String |  | 更新用户 |
| selfDeliveryFreightMoney | Long |  | 商家自送门店费用 (只有商家自送门店才会有值，众包配送为null) |
| startBeginTime | Date |  | 起送价开始时间 |
| startEndTime | Date |  | 起送价结束时间 |
| freeMoneyList | List<Long> |  | 免运门槛金额列表 |
| openDistanceFreight | Boolean |  | 是否有距离运费 |
| distanceFreightThreshold | Integer |  | 收取距离运费阈值（距离超出该值开始收取远距离运费），单位：米 |
| distanceUnit | Integer |  | 距离运费距离递进阶梯，单位：米 |
| distanceFreight | Long |  | 距离运费每阶梯运费增加金额 |
| fullFreeMoney | Long |  | 商家自送门店免运门槛金额 单位分 |
| isFullFree | Boolean |  | 是否满免 |
| -freeFreightInfoList | List<FreeFreightInfo> |  | 免运规则列表 |
| fullFreeMoney | Long |  | 满减运费阈值 单位：分 |
| freeMoney | Long |  | 减Y元基础运费 |
| freeType | Integer |  | 免运费类型 免运费类型 0：全免 1：免基础 2：免部分基础运费 -1：不免 |
| outActivityId | String |  | 外部活动ID |


## 根据到家门店编码查询门店基本信息接口

### 接口地址：京东到家 | 开放平台 (jd.com)

### 接口限频：300次/分钟

### 入参：

| **字段** | **类型** | **是否必须** | **示例值** | **描述** |
| StoreNo | String | 是 |  | 到家门店编码 |


### 出参

| **字段** | **类型** | **示例值** | **描述** |
| code | String | 0 | 0表示成功，其他均为失败。 详见:[到家平台状态码说明](https://opendj.jd.com/staticnew/widgets/doc/portalCodes.html) |
| msg | String | 操作成功 | 结果描述 |
| -data | String |  | 返回对象，该属性值为字符串型JSON数据，请先按照字符串取值，再解析转换成JSON对象。 |
| code | String | 0 | -4，未知错误 -3,系统错误 -2 警告 -1 失败 0,成功 1,参数错误 |
| msg | String | 操作成功 | 描述信息 |
| -result | StoreInfo |  | 数据 |
| id | Long | 1 | 主键ID |
| venderId | String | 71598 | 商家编码 |
| venderName | String | 京东到家 | 商家名称 |
| stationName | String | 测试门店 | 门店名称 |
| stationNo | String | md001 | 到家门店编码 |
| outSystemId | String | md001 | 商家门店编码 |
| lat | Double | 38.843041980552606 | 经度 |
| lng | Double | 115.4581091015625 | 纬度 |
| coordinateAddress | String | 大兴亦庄 | 地址 |
| coordinate | String | 115.4581091015625 | 坐标 |
| province | Integer | 1 | 省份 |
| provinceName | String | 北京 | 省份名称 |
| city | Integer | 1 | 城市 |
| cityName | String | 北京 | 城市名称 |
| county | Integer | 2 | 镇/区编码 |
| countyName | String | 大兴 | 镇名称 |
| phone | String | 010-100001 | 门店电话 |
| mobile | String | 13688888888 | 门店手机 |
| stationAddress | String | 北京市大兴区亦庄亦成财富中心 | 地址 |
| createPin | String | jddj | 创建人 |
| createTime | Long | 1572594152000 | 创建时间 |
| yn | Byte | 0 | 门店状态,0启用,1禁用 |
| closeStatus | Integer | 0 | 营业状态,0正常营业,1休息中 |
| preWarehouse | Integer | 1 | 是否前置仓,1是,2不是 |
| updatePin | String | jddj | 更新操作人 |
| selfPickSupport | Integer | 1 | 是否支持自提,1支持,2不支持 |
| wareType | Integer | 1 | 是否是仓库类型门店,1是,2否 |
| stationDeliveryStatus | Integer | 1 | 是否已设置配送服务,1是,2否 |
| supportOfflinePurchase | Integer | 1 | 是否支持线下购,0不支持,1支持 |
| standByPhone | String | 1311111111 | 备联电话，多个电话逗号隔开（只有备联电话才能联系客户，其他电话无法打通客户电话。如为座机，请连续输入区号和电话号，400电话不识别） |
| timeAmType | Integer | 1 | 是否支持上午配送,1支持,2不支持 |
| timePmType | Integer | 1 | 是否支持下午配送,1是,2否 |
| serviceTimeStart1 | Integer | 11 | 营业开始时间1，传值规则：每30分钟加1，即00:00为0，00:30为1，以此类推，23:30为47，23:59为48 |
| serviceTimeEnd1 | Integer | 20 | 营业结束时间1，传值规则：每30分钟加1，即00:00为0，00:30为1，以此类推，23:30为47，23:59为48 |
| serviceTimeStart2 | Integer | 11 | 营业开始时间2，传值规则：每30分钟加1，即00:00为0，00:30为1，以此类推，23:30为47，23:59为48 |
| serviceTimeEnd2 | Integer | 20 | 营业结束时间2，传值规则：每30分钟加1，即00:00为0，00:30为1，以此类推，23:30为47，23:59为48 |
| isMembership | Integer | 1 | 1 会员店，0 非会员店 |
| innerNoStatus | Integer | 1 | 中间号状态 1 开启，0关闭 |
| isAutoOrder | Integer | 1 | 是否自动接单，0:是1:否 |
| carrierNo | Integer | 2938 | 9966 众包,2938 自送 |
| isNoPaper | Integer | 1 | 是否无纸化 1是，2否 |
| orderAging | Integer | 1 | 订单时效，单位：分钟 |
| orderNoticeType | Integer | 1 | 订单通知类型 1：下单即通知（订单通知商家的时间为：无论用户选择未来任何时段，订单都会立即下发给商家。）2：服务开始前n小时通知（订单通知商家的时间为：用户选择送达最晚时间-订单时效-n。） |
| supportInvoice | Integer | 1 | 是否支持发票，0：不支持，1：支持 |
| regularFlag | Integer | 1 | 送达类型 1：立即送达；2：定时达 3：立即送达+定时达 |
| yjsStoreType | Integer | 1 | 药急送门店类型 1或空: 药急送小时店 2:药急送中心店 |