# 3.订单助手开发

### 项目

[order-assistant](https://yxtgit.hxyxt.com/order/order-assistant)

order-atom-service

### 分支

feature-order-assistant

feature-order-assistant-xframe2 基于xframe2的,目前不可用。base: feature-order-assistant



### SDK版本

版本前缀: order-assistant-

	<dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>order-assistant-SNAPSHOT</version>
    </dependency>

### MCP工具开发

配置方案:

在Nacos上配置: [nacos mcp 开发配置](http://10.4.3.210:8848/nacos/#/configurationManagement?dataId=&group=&appName=&namespace=7417dbee-48a8-48ff-aa2e-6e410cc817d1&namespaceShowName=yxt-mcp-manager-dev&serviceNameParam=&groupNameParam=&pageSize=&pageNo=)

| URL | 说明 | mcp_resource.json MCP资源 |
| --- | --- | --- |
| /mcp/query/order-type | 通过订单号获取订单类型 | {         "$schema": "https://json-schema.org/draft/2020-12/schema",         "type": "object",         "properties": {             "businessOrderNo": {                 "type": "string",                 "description": "业务订单号,此参数需要要求用户提供，如果用户未提供需要引导用户提供"             }         },         "required": [             "businessOrderNo"         ],         "additionalProperties": false,         "yxtToolName": "order-assistant_queryOrderType",         "yxtToolDescription": "根据业务单号获取订单类型。订单类型有O2O、B2C和OFFLINE，针对必填参数需要强制要求用户提供，如果用户未提供业务单号则不能调用此工具，需要提示用户补充订单号,例如1164413186808350255",         "yxtToolEnable": true,         "yxtToolProvider": {             "protocol": "server",             "protocolMeta": {                 "serverName": "order-assistant",                 "serverUrl": "/mcp/query/order-type"             },             "alarmTagIdList": [                 11             ]         }     } |


应用和工具配置

| 维度 | 配置 mcp_auth.json |
| --- | --- |
| 应用维度 | {  "targetType": "APPID",  "targetList": [  "order-assistant-dify"  ],  "authToolList": [  "order-assistant_queryOrderType"  ]  } |
| 场景维度 |  |
| 角色维度 |  |
| 用户维度 |  |


access/agent sse 配置

mcp_access.json

    {
      "appId":"order-assistant-dify",
      "authMode":"SSE_USERID"
    }

sse

{
  "yxt_mcp_server": {
    "transport": "sse",
    "url": "http://10.4.1.182:8089/sse?app_id=order-assistant-dify&yxt_operator_context_operator_user_id=123"
  }
}

前置工作

truedffalseautotoptrue13811

调用链路

trueffalseautotoptrue7211

### 知识库维护

[https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-knowledge](https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-knowledge)

### prompt维护

[https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-prompt](https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-prompt)

### ETL工具维护

Demo

true知识库falseautotoptrue5211

### 开发规划

| 任务 |  |
| --- | --- |
| 知识库 | AI 能力基础,回答是否完善，意图识别是否准确,依赖于提示词 |
| 提示词 | 让AI更好的完成任务 |
| MCP开发 | 门槛: 支持基础订单操作（查询订单状态、修改订单信息、处理简单售后等）目标: 覆盖订单全生命周期的主要问题和边缘场景挑战: 覆盖几乎所有订单相关场景，包括复杂的异常情况 |


20250703和产品沟通结果: 优先做知识库和提示词