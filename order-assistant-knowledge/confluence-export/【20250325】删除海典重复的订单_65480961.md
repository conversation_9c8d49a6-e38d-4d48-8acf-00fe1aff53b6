# 【20250325】删除海典重复的订单

### 背景

线下单库:


hana库:


现在有一个重复问题，海典那边过来的三方单号是数字，迁移订单的三方单号字段对应的是hana库中的XF_DOCNO字段，和海典的对不齐。然后看了hana数据发现XF_TXSERIAL对应的值和海典实时同步过来三方单号是对上的。

### 处理方式

1.找到海典的三方单号和hana的XF_TXSERIAL相同的订单；
2.再排查对应的字段是否一一对应，对应的则删除hana迁移的那部分订单；
3.若不对应，找到几个对应的订单，查询一下case，找海典和hana，看看数据的准确性；然后再处理这部分数据；

### TODO

  4 complete deleted_data_6 添加到线上  

### 生产数据核验

20250326抽样核对

Green符合

20250327抽样核对

Green符合

删除的重复数据有: