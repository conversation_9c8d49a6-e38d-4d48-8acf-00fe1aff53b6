# 【20241126】各平台订单账单拉取（可直接json存储），协助大数据完成门店的成本分析和盈利分析

一心数科数字化产研中心-Scrumissuekey,summary,issuetype,created,updated,duedate,assignee,reporter,priority,status,resolutionkey,summary,type,created,updated,due,assignee,reporter,priority,status,resolutionb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3623

# 拉取前一天账单数据流程图

true拉取昨日对账单falseautotoptrue20915

# 指定日期对账单数据拉取

true获取指定日期的对账单falseautotoptrue11913

# job

| 任务描述 | 执行器 | JobHandler | Cron | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 获取昨日对账单 | 【订单】异步服务 | yesterdayAccountBillPullHandler |  | {  "merCode": "500001",  "platformCode": "27",  "subDays": "2" } |  |
| 处理数据库中获取美团历史对账单的任务 | accountBillPullJobProcessHandler |  | {  "merCode": "500001",  "platformCode": "27",  "processSize": 10 } |  |
| 清理对账单表中的历史数据 | accountBillCleanHandler |  | {  "merCode": "500001",  "platformCode": ["11","24","27"]  "cleanDays": 90 } |  |


# API

## order-sync

### 直接通过接口拉取账单

1. url：POST /account-bill/pull
2. 请求参数：
  1. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| onlineClientCode | 网店编码 | String | 否 | 当门店编码为空、网店编码不为空时生效，会拉取该网店下的所有门店账单 |
| onlineStoreCodeList | 门店编码列表 | List<String> | 否 | 当门店编码不为空时，只会拉取这些门店的对账单，会忽略网店编码 |
| pullStartDate | 开始账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 否 | 为空时默认查昨天 |
| pullEndDate | 结束账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 否 | 为空时默认查昨天 |
3. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| onlineClientCode | 网店编码 | String | 否 | 当门店编码为空、网店编码不为空时生效，会拉取该网店下的所有门店账单 |
| onlineStoreCodeList | 门店编码列表 | List<String> | 否 | 当门店编码不为空时，只会拉取这些门店的对账单，会忽略网店编码 |
| pullStartDate | 开始账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 否 | 为空时默认查昨天 |
| pullEndDate | 结束账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 否 | 为空时默认查昨天 |


### 通过job拉取指定日期账单

1. url：POST account-bill/pull/with-job
2. 请求参数：
  1. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| onlineClientCode | 网店编码 | String | 否 | 为空时会拉取该网店下的所有门店账单 |
| pullStartDate | 开始账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 是 | 会按照天+网店的维度进行拆分比如：pullStartDate: 2024-12-01 10:00:00endStartDate: 2024-12-02 20:00:00查询到的所有网店为：clientA，clientB，那么存到表里的数据为：| client_code | pull_start_time | pull_end_time | | --- | --- | --- | | clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | | clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | | clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | | clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | | client_code | pull_start_time | pull_end_time | clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| client_code | pull_start_time | pull_end_time |
| clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 |
| clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 |
| clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| pullEndDate | 结束账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 是 |
3. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| onlineClientCode | 网店编码 | String | 否 | 为空时会拉取该网店下的所有门店账单 |
| pullStartDate | 开始账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 是 | 会按照天+网店的维度进行拆分比如：pullStartDate: 2024-12-01 10:00:00endStartDate: 2024-12-02 20:00:00查询到的所有网店为：clientA，clientB，那么存到表里的数据为：| client_code | pull_start_time | pull_end_time | | --- | --- | --- | | clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | | clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | | clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | | clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | | client_code | pull_start_time | pull_end_time | clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 | clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 | clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| client_code | pull_start_time | pull_end_time |
| clientA | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 |
| clientA | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| clientB | 2024-12-01 10:00:00 | 2024-12-01 23:59:59 |
| clientB | 2024-12-02 00:00:00 | 2024-12-02 20:00:00 |
| pullEndDate | 结束账单日期，格式：yyyy-MM-dd HH:mm:ss | String | 是 |


### 执行job

1. url：POST account-bill/pull/with-job/process
2. 请求体：
  1. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| jobStatus | 对账单拉取job状态1 待执行 2 执行中 3 执行成功 4 执行失败 | Integer | 是 |  |
| processSize | 处理数量 | Integer | 否，默认10 |  |
3. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCode | 平台编码 | String | 是 |  |
| jobStatus | 对账单拉取job状态1 待执行 2 执行中 3 执行成功 4 执行失败 | Integer | 是 |  |
| processSize | 处理数量 | Integer | 否，默认10 |  |


### 历史数据清除

1. url: POST account-bill/clean
2. 请求参数
  1. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCodeList | 平台编码列表 | List<String> | 是 |  |
| onlineStoreCodeList | 门店编码列表 | List<String> | 否 |  |
| cleanDays | 清洗多久前的数据 | Integer | 是 |  |
3. | 字段名 | 字段描述 | 字段类型 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| merCode | 商户编码 | String | 是 |  |
| platformCodeList | 平台编码列表 | List<String> | 是 |  |
| onlineStoreCodeList | 门店编码列表 | List<String> | 否 |  |
| cleanDays | 清洗多久前的数据 | Integer | 是 |  |


## third-platform-other

### 异步获取门店对账单

### 同步获取门店对账单

### 分页获取门店对账单