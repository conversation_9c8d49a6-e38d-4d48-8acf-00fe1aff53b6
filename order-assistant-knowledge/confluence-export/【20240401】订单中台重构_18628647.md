# 【20240401】订单中台重构

## 一、 背景

### 1.1 业务背景

****

### 1.2 痛点分析

### 1.3 系统现状

#### 1.3.1 现在的.net拉单服务

true.net拉单falseautotoptrue13514

#### 1.3.2 订单现有MVC应用架构

truemvcfalseautotoptrue15713

## 二、 目标

### 2.1 本期目标

心云系统页面的操作使用DDD重构

一期范围:

1. 订单配置
2. 订单查询
3. 订单DDL


订单一期重构过程中要处理的内容:

  53 incomplete 新系统需提供一个接口,返回给前端标识已经重构好,可以直接调用新的接口,走配置中心   54 incomplete 接头或者方法重构完添加特定注解标注,利于团队协作 @HasRewrite   55 incomplete traceId   56 incomplete 网关调整   58 incomplete 状态枚举  

待办:

  44 complete 重构的价值   45 complete 迭代周期按照周维度   46 complete 应用级别的架构图，体现出变化 参考:[1.项目介绍 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6358267)  

#### 2.1.1 业务目标

能够给业务带来的价值，目标应该是可量化衡量的，符合SMART的。
衡量ROI

| 指标 | 业务价值 | 具体衡量指标 |  |
| --- | --- | --- | --- |
| 代码复杂度和可维护性 | **减少Bug修复时间**，降低维护成本 | Cyclomatic Complexity | 期望<= 5 .最多<=10 |
| 代码行数 | 单文件不超过500行 |
| 注释量 | 注释行占总代码行比例在15%-20% |
| Bug修复时间 | **紧急（P1）**：致命类BUG，需要立即修复，修复时长<4小时。**高（P2）**：严重类BUG，当天修复。**中（P3）**：非主流程类的重要问题，产品发布前修复。**低（P4）**：针对轻微类型BUG，可选修复。 |
| 测试覆盖率和质量 | **减少潜在Bug**，提高系统可靠性 | 单元测试覆盖率 | >= 60-70% |
| 测试用例通过率 | >= 90% |
| Bug数量 | 生产bug<=1生产严重bug =0 |
| 系统稳定性评估 | 99.9% 较高可用 |
| 模块化和组件化 | **缩短**开发周期，**加速**产品交付 | 模块化程度评估 |  |
| 组件化程度评估 |  |
| 开发周期时长 |  |
| 产品交付周期 |  |
| 业务表达和理解 | **减少开发偏差和错误**，提高用户满意度 | 业务需求变更频率 |  |
| 用户满意度调查结果 |  |
| 开发偏差量 |  |
| Bug数量 |  |
| 迭代和交付周期 | 提高市场竞争力，满足客户需求 | 迭代周期时长 |  |
| 交付版本发布评率 |  |
| 市场竞争力评估 |  |
| 客户需求满足度评估 |  |
| 可扩展性和定制性、可观察性 | 满足不同客户需求，降低定制化开发成本 | 定制化需求实现时间 |  |
| 系统扩展性评估 |  |
| 定制化开发成本评估 |  |


  63 incomplete 技术层面的业务监控   64 incomplete 业务分级(降级)  

#### 2.1.2 技术目标

 一般是技术指标（SLA）、沉淀基础服务、节约开发成本等

| 指标 |  | 当前 | 目标 | 备注 |
| --- | --- | --- | --- | --- |
| 性能指标 | 请求QPS |  |  | 接口维度,相关负责人填写 |
| 响应时间 |  |  | 接口维度,相关负责人填写 |
| 资源使用率 | CPU占用率 |  |  |  |
| 内存使用率 |  |  |  |
| 磁盘I/O |  |  |  |
| 网络I/O |  |  |  |
| Redis占用 |  |  |  |
| 错误率 | 超时率 |  |  |  |
| 错误率 | 404占比、500占比 |  |  |
| 代码 | [圈复杂度](https://www.cnblogs.com/Jcloud/p/16550668.html) | <=5, 最多不超过10 |  | 相关负责人把控 |
| 代码规范 |  |  | 相关负责人把控 |
| 千行问题 |  |  | 相关负责人把控 |
| 架构指标 | 可测试性 |  |  |  |
| 可部署性 |  |  |  |
| 模块化 |  |  |  |
| 扩展性 |  |  |  |


### 2.2 中长期目标

1. 收敛订单应用, 移除.net技术栈, 拥抱Java
2. 一期目标:订单查询,配置,DDL； 二期目标: 订单下账相关、Job
3. 构建出边界清晰的订单领域模型,发挥领域模型的价值:
  1. 让业务可以被更准确的定义和理解
  2. 产品和开发使用通用语言交流沟通
  3. 敏捷、迭代式和持续建模
  4. 在业务领域创建解决方案
4. 让业务可以被更准确的定义和理解
5. 产品和开发使用通用语言交流沟通
6. 敏捷、迭代式和持续建模
7. 在业务领域创建解决方案


## 四、整体设计

### 4.1统一语言定义

|  |  |
| --- | --- |
| 聚合 | 需要将领域中高度内聚的概念放到一起组成一个整体。理解: 聚合就是由业务和逻辑紧密关联的实体和值对象组合而成的，聚合是数据修改和持久化的基本单元，每一个聚合对应一个仓储，实现数据的持久化。 |
| 聚合根 | 一组相关对象的集合，作为一个整体被外界访问。聚合根的 ID 全局唯一。领域对象。理解: 把聚合比作组织，那聚合根就是这个组织的负责人。聚合根也称为根实体，它不仅是实体，还是聚合的管理者 |
| 实体 | 有生命周期，有状态，通过 ID 进行唯一标识 |
| 值对象 | 属性，配合描述实体的状态 |
| 领域 | 领域就是用来确定范围的，范围即边界。DDD 的领域就是这个边界内要解决的业务问题域。 |
| 子领域 | 领域可以进一步划分为子领域。每个子域对应一个更小的问题域或更小的业务范围 |
| 核心域、通用域、支撑域 | 子域可以根据自身重要性和功能属性划分为三类子域,分别是：核心域、通用域和支撑域 核心域: 决定产品和公司核心竞争力的子域,是业务成功的主要因素和公司的核心竞争力 通用域: 没有太多个性化的诉求，同时被多个子域使用的通用功能子域 支撑域: 不包含决定产品和公司核心竞争力的功能，也不包含通用功能的子域 |
| 领域服务 | 跨聚合根的业务处理使用领域服务来处理。即,领域服务负责对聚合根进行调度和封装，同时可以对外提供各种形式的服务，对于不能直接通过聚合根完成的业务操作就需要通过领域服务. 例如: 聚合根本身无法完全处理这个逻辑，例如支付这个步骤，订单聚合不可能支付，所以在订单聚合上架一层领域服务，在领域服务中实现支付逻辑，然后应用服务调用领域服务 |
| 界限上下文 | 限界就是领域的边界，而上下文则是语义环境。简单理解就是限界上下文可以理解为语义环境。 例如: 商品在不同的阶段有不同的术语，在销售阶段是商品，而在运输阶段则变成了货物。同样的一个东西，由于业务领域的不同，赋予了这些术语不同的涵义和职责边界，这个边界就可能会成为未来微服务设计的边界 |


**总结:**

通过聚合根来引用实体，挂载值对象，对外屏蔽内部的实体逻辑

### 4.2 用例图

trueUse Casefalseautotoptrue17002

### 4.3 ER图

### 4.4 架构图

架构五视图：逻辑架构、开发架构、物理架构、运行架构、数据架构，跟进需要进行选择.后期主要是不断迭代演进的架构推演，改动的或新增的模块特殊颜色标识。

**逻辑架构: 功能需求**

开发架构: 开发周期质量属性

运行架构: 运行期质量属性

物理架构: 安装和部署需求

数据架构: 数据需求

#### 替换.net技术栈,一期

> 最终替换完成之后,再结合订单新的应用架构,数据交互流程,点击

true未命名绘图falseautotoptrue12016

#### 订单DDD架构:

truedddfalseautotoptrue194010

### 域服务初步划分

**true域服务初步划分falseautotoptrue11023**

### 订单部署架构

**true部署架构falseautotoptrue9912**

### 替换.net技术栈之后的交互方式

**true替换.net技术栈falseautotoptrue8513**

### 4.5 核心技术问题

主要是解决订单历史的技术债务问题

- 订单分布式锁
- 乐观锁机制: 在原子服务对data_version做统一处理
- traceId: 链路追踪
- 分库分表: sharding-jdbc,目前已集成到原子服务
- 内存缓存、redis、mq等组件的使用规范


## 五、 详细设计

### 1、 模块详细设计

重构保持和现有逻辑、流程一致。在重构前**由对应的研发人员**先绘制现有接口的流程图,确认无误后再进行重构工作

### 2、 存储数据库设计

订单中台重构不涉及数据库存储结构的变动

### 3、 接口设计

新增接口: 

  48 incomplete 新系统的接口, 提供给前端做url的切换  

重构接口的URL保持不变

### 4、 安全设计

幂等设计: 核心订单系统保持原有的幂等设计。非核心订单系统在保持原有业务幂等的条件下,需要增加消息的幂等处理

接口防刷: 降级处理, 统一的hystrix降级配置 + 订单自定义的限流注解

### 5、 监控报警

- 止损和回滚方案: 通过配置可以切换回老系统
- 监控:


-   - 错误日志监控
  - prometheus监控，接口、错误率、资源等
- 错误日志监控
- prometheus监控，接口、错误率、资源等
- 降级:


-   - Hystrix
  - 订单接口自有限流处理
- Hystrix
- 订单接口自有限流处理


## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

基础服务:

order-sync 承接订单非核心的业务,提升主线业务的处理能力及稳定性

order-atom-service 原子服务，主从、分库分表、SQL优化、业务下沉

order-job 订单批处理应用,将现有的订单批处理从订单主应用中剥离

质量保证:

- 单元测试覆盖率, 对纯POJO的module,测试覆盖率 60-70%
- 核心接口业务逻辑流程图补齐(由对应的研发人员负责)


## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

### 时间评估:

后端工作量粗略估算(**不含联调、测试、穿插需求、公共假期等**):

按1人天: 238.5d , → 10.96个月(按每月21.75天计算)

按3人天: 77.9d → 3.65个月(按每月21.75天计算)

联调时间: 需要前端排期

测试时间: 需要测试排期

**周维度推进:**

重构进度推进以**周维度**推进,完成每周目标,当完成一个页面的所有功能就提测。jira按照页面维度建任务、提测、缺陷管理等,子任务细分到接口维度

**重构和提测的粒度:**

以页面为最小粒度,即每次提测都是一个完整的功能点, 测试需要关注页面维度的接口是否都切换到新的订单服务(F12)

  65 incomplete 数据归档   66 incomplete 脱敏字段(日志层面)  

**b2c和o2o有部分url重复,只是网关的path有区别,/dscloud/和/b2c/**

| 一级菜单 | 二级菜单 | 三级菜单 | 功能点 | 预估工时 | 负责人 | 周 | url |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 设置 | 门店管理 | 网店管理 | 网店列表 | 0.5d |  |  | /1.0/ds/baseinfo/searchOnlineClient |  |
| 网店导出记录 | 0.5d |  |  | /1.0/ds/export/list | 通用接口,传不同的exportType |
| 埋点数据上传 | 接口无功能 |  |  | /1.0/ds/eventTracking/uploadEvent | 无效接口 |
| 添加网店 | 2d |  |  | /1.0/ds/baseinfo/createClient |  |
| 编辑网店 | 1d |  |  | /1.0/ds/baseinfo/updateClient |  |
| 手动导入店铺 | 2d |  |  | /1.0/ds/store/sync/by_excel |  |
| 同步门店 | 2d |  |  | /1.0/ds/baseinfo/synStore |  |
| 删除门店前调用 | 0.5d |  |  | /1.0/ds/order/getOrderByClientCode/{} |  |
| 删除门店 | 2d |  |  | /1.0/ds/baseinfo/deleteClientById/{} |  |
| 店铺管理 | 店铺管理列表 | 1.5d |  |  | /1.0/ds/baseinfo/queryDsOnlineStore |  |
| /1.0/ds/baseinfo/queryDsOnlineStorePage |  |
| 删除店铺前调用 | 0.5d |  |  | /1.0/ds/order/getOrderByOnlineStore/{} |  |
| 删除店铺 | 0.5d |  |  | /1.0/ds/baseinfo/deleteOnlineStoreById/{} |  |
| 店铺停业 | 1d |  |  | /1.0/ds/baseinfo/setOnlineStoreOpenStatus |  |
| 操作记录 | 0.5d |  |  | /1.0/ds/updateLog/page |  |
| 下账设置 | 0.5d |  |  | /1.0/ds/store/bill/config/client/getPage |  |
| 下账详情 | 1.5d |  |  | /1.0/ds/store/bill/config/{} |  |
| /1.0/ds/baseinfo/queryByCode |  |
| 创建下账配置 | 1.5d |  |  | /1.0/ds/store/bill/config/getLastClientConf |  |
| /1.0/ds/baseinfo/queryByCode 复用接口 0d | 和下账详情的调用接口一致 |
| /1.0/ds/store/bill/config/_create | 创建下账配置 |
| 提示音设置 | 2d |  |  | /1.0/ds/baseinfo/toSoundConf | 如果没有配置,含初始化 |
| /1.0/ds/baseinfo/updateSoundConf |  |
| 自配送设置页面 | 0 |  |  | /1.0/ds/baseinfo/queryByCode 0d |  |
| 0.5d |  |  | /1.0/ds/baseinfo/toDeliveryConf |  |
| /1.0/ds/order/changeSelfDelivery/getConfigListByStoreId |  |
| 0.5d |  |  | /1.0/ds/baseinfo/setStatus | 具体配送中心的启用、禁用 |
| 0.5d |  |  | /1.0/ds/baseinfo/searchDeliveryStore | 绑定配送门店列表 |
| /1.0/ds/baseinfo/saveDeliveryConf | 双击选中配送门店绑定 |
| 1.5d |  |  | /1.0/ds/order/changeSelfDelivery/updateConfigDelivery | 平台配送转自配送配置-配送方式 |
| /1.0/ds/order/changeSelfDelivery/updateConfigDelayMinutes | 平台配送转自配送配置-启用 |
| /1.0/ds/order/changeSelfDelivery/updateConfigState |
| 订单处理设置 | 3d |  |  | /1.0/ds/templateInfo/selectByType 0.2d |  |
| /ds/config/queryCompareWhiteList 0.2d |  |
| /1.0/ds/baseinfo/toOrderConf 1.5d | 含初始化步骤 |
| /1.0/ds/baseinfo/updateOrderConf 1d |  |
| /1.0/ds/baseinfo/getAllDeliveryType 0.1d | 点击循环呼叫骑手 |
| 店铺配置 | 2.5d |  |  | /1.0/ds/baseinfo/queryByCode 0d |  |
| /1.0/ds/baseinfo/storeConf 2d | 含初始化步骤 |
| /1.0/ds/baseinfo/updateStoreConf 0.5d |  |
| 查看任务进度 | 0.5d |  |  | /1.0/ds/batch/import/page |  |
| 查看导出记录 | 0d |  |  | /1.0/ds/export/list | 复用接口 |
| 批量修改店铺配置 | 5.5d |  |  | /1.0/ds/baseinfo/getStoreTreeByOrg/500001 1d |  |
| /b2c/1.0/dict/store/classify 0.2d | 店铺字典分类 hydee-business-order-web项目中的接口 |
| /1.0/ds/store/batch/config/selfDeliveryType/all 0.1d |  |
| /1.0/ds/platform/pay/relation/systemConfig 0.2d |  |
| /1.0/ds/templateInfo/selectByType 0d | 复用接口 |
| /1.0/ds/config/queryCompareWhiteList 0d | 复用接口 |
| /1.0/ds/store/batch/config/store/batch/setting 1d | 店铺配置-确认 |
| /1.0/ds/store/batch/config/storeOrder/batch/setting 1d | 订单处理设置-确认 |
| /1.0/ds/store/batch/config/storeSound/batch/setting 0.5d | 提示音设置-确认 |
| /1.0/ds/store/batch/config/storeBill/batch/setting 1.5d | 下账设置- 确认 |
| 批量修改营业状态 | 1d |  |  | /1.0/ds/store/batch/config/storeStatus/batch/setting | 营业状态设置-确认 |
| /1.0/ds/store/batch/config/storeTime/batch/setting | 营业时间设置-确认 |
| 批量修改所属机构 | 2d |  |  | /1.0/ds/batch/template/{type} | 下载模板 |
| /1.0/ds/batch/upload/{type} | 上传 |
| 批量设置配送门店 | /1.0/ds/batch/template/{type} | 下载模板 |
| /1.0/ds/batch/upload/{type} | 上传 |
| 授权解绑 | 列表 | 0.5d |  |  | /1.0/ds/store/auto/auth/record |  |
| 上传excel | 0.5d |  |  | /1.0/ds/store/auto/auth/by_excel |  |
| 第三方配送 | 列表 | 2d |  |  | /1.0/ds/baseinfo/searchDeliveryClient 0.2d | 查询用户可查看的门店 |
| /1.0/ds/baseinfo/newMy 0.3d |  |
| /b2c/1.0/ds/order/log/merCode/containsTM 0.1d | 商户是否存在天猫店铺 |
| /1.0/ds/order/log/judge/aliHealth 0.1d |  |
| /1.0/ds/baseinfo/getPlatformByMerCode/500001 0.2d |  |
| /1.0/ds/baseinfo/getOnlineStoreInfoByStCode/A002 1d | 含初始化 |
| 配送账号管理 | 3d |  |  | /1.0/ds/baseinfo/queryDeliveryAccount 0.2d |  |
| /1.0/ds/baseinfo/updateDeliveryClient 0.3d | 配送账号授权 |
| /1.0/ds/batch/import/page 0d 复用接口 | 查看同步记录 |
| /1.0/ds/batch/template/AsyncStoreDelivery 1d | 导入同步配送门店模板 |
| /1.0/ds/batch/upload/AsyncStoreDelivery 0.5d | 上传配送门店 |
| /1.0/ds/delivery/store/updateDeliveryStore 1d 含创建 | 手动更新配送门店 |
| 新增配送账号 | 0.5d |  |  | /1.0/ds/baseinfo/saveDeliveryClient |  |
| POS类型管理 | 列表 | 0.3d |  |  | /ds/organization/pos/config/list |  |
| 修改 | 0.5d |  |  | /ds/organization/pos/config |  |
| O2O订单设置(一级菜单【订单】中也包含) | 快捷回复模板 | 列表 | 1d |  |  | /1.0/ds/templateInfo/page/queryTemplate |  |
| 创建 | /1.0/ds/templateInfo/addTemplateInfo |  |
| 编辑 | /1.0/ds/templateInfo/updateTemplateInfo |  |
| 删除 | /1.0/ds/templateInfo/deleteTemplateInfo |  |
| 小票模板设置 | 列表 | 0.5d |  |  | /1.0/ds/templateInfo/page/queryTemplatePrint |  |
| 详情 | /1.0/ds/templateInfo/queryDetail |  |
| 新增小票模板页 | /1.0/ds/templateInfo/getDefaultPrintProperty |  |
| 新增 | 0d 复用 |  |  | /1.0/ds/templateInfo/addTemplateInfo 0d | 复用接口 |
| 编辑 | /1.0/ds/templateInfo/updateTemplateInfo 0d | 复用接口 |
| 删除 | /1.0/ds/templateInfo/deleteTemplateInfo 0d | 复用接口 |
| B2C订单设置(一级菜单【订单】中也包含) | b2c基础设置 | 基础设置 | 0.5d |  |  | 获取该商户下的处方单配置信息 /1.0/order/setting/getPrescription/1.0/order/setting/updatePrescription |  |
| 异常单设置 | 1.5d |  |  | 列表: /1.0/exAutoRestoreConfig/list 启用禁用: /1.0/exAutoRestoreConfig/modify/status获取异常类型: /1.0/order/getOrderExType 新增:/1.0/exAutoRestoreConfig删除:/1.0/exAutoRestoreConfig/modify/status status传-1 |  |
| 下账设置 | 0.2d |  |  | 更新 /1.0/order/setting/updateAutoBill |  |
| 快递不达记录 | 列表 | 1.5d |  |  | /1.0/express/limit GET |  |
| 查询商户添加的快递 | /1.0/dict/express/merchant |  |
| 新增 | /dict/administration/area/merchant/1.0/express/limit POST |  |
| 启用/禁用 | /1.0/express/limit/enable |  |
| 删除/批量删除 | /1.0/express/limit/batch/{} DELETE |  |
| 快递例外策略 | 列表 | 0.5d |  |  | /1.0/express/exceptive GET |  |
| 新增 | 1d |  |  | /1.0/ds/baseinfo/queryDsOnlineStore b2c服务中的 复用接口 0d/1.0/dict/express/merchant 复用接口 0d /1.0/dict/administration/area/merchant 复用接口 0d /1.0/express/exceptive POST新增 |  |
| 启用禁用 | /1.0/express/exceptive/enable |  |
| 详情 | /1.0/express/exceptive/430 |  |
| 编辑 | /1.0/express/exceptive PUT |  |
| 删除/批量删除 | /1.0/express/exceptive/batch/{} DELETE |  |
| 快递默认策略 | 列表 | 0.2d |  |  | /1.0/express/default |  |
| 新增 | 0.5d |  |  | /1.0/dict/express/merchant 复用接口 /1.0/dict/administration/area/merchant 复用接口 /1.0/express/default POST |  |
| 启用/禁用 | 0.5d |  |  | /1.0/express/default/enable |  |
| 更新 | /1.0/express/default PUT |  |
| 批量启用/禁用 | /1.0/express/default/enable/batch |  |
| 删除/批量删除 | /1.0/express/default/batch/{} |  |
| 赠品规则设置 | 列表 | 1.5d |  |  | /1.0/baseInfo/Setting/giftRuleSetList |  |
| 新增 | /1.0/ds/baseinfo/queryDsOnlineStore b2c 复用接口 0d/1.0/baseInfo/Setting/setGiftRule |  |
| 编辑 | /1.0/baseInfo/Setting/getGiftRule 详情 /1.0/baseInfo/Setting/editGiftRule 编辑 |  |
| 查看日志 | /1.0/baseInfo/Setting/getGiftRuleLog |  |
| 启用/禁用 | /1.0/baseInfo/Setting/isValid/giftRuleSet |  |
| 智能审单策略 | 列表 | 1d |  |  | /1.0/baseInfo/Setting/getAuditStrategyList |  |
| 新增/编辑 | /1.0/ds/baseinfo/queryDsOnlineStore b2c 复用接口 0d/1.0/baseInfo/Setting/addOrEdit/AuditStrategy |  |
| 禁用/启用 | /1.0/baseInfo/Setting/isValid/auditStrategy |  |
| 删除/批量删除 | /1.0/baseInfo/Setting/deleteAuditStrategy |  |
| 区域仓库优先级 | 列表 | 1.5d |  |  | /1.0/warehouse/area/config/queryAreaConfig |  |
| 新增 | /1.0/warehouse/area/config/queryArea/-1/1.0/warehouse/area/config/saveAreaConfig |  |
| 编辑 | /1.0/warehouse/area/config/queryArea/68 复用接口 /1.0/warehouse/area/config/updateAreaConfig |  |
| 启用/禁用 | /1.0/warehouse/area/config/enableAreaConfig |  |
| 删除 | /1.0/warehouse/area/config/deleteAreaConfig/68 |  |
| 列表 | 1d |  |  | /1.0/warehouse/ship/config/page |  |
| 新建 | /1.0/dict/administration/area/merchant 复用接口 /1.0/ds/baseinfo/queryDsOnlineStore 复用接口 /1.0/ds/baseinfo/getMerSupplier 获取商户对应的供应商 /1.0/warehouse/ship/config |  |
| 启用/禁用 | /1.0/warehouse/ship/config/enable/102 |  |
| 编辑 | /1.0/dict/administration/area/merchant 复用接口 /1.0/ds/baseinfo/queryDsOnlineStore 复用接口 /1.0/warehouse/ship/config PUT |  |
| 删除 | /1.0/warehouse/ship/config/102 DELETE |  |
| 拆单策略 | 启用按钮 | 0.5d |  |  | /1.0/split/strategy/enable/{} |  |
| /1.0/split/strategy 策略 GET |  |
| 例外条件 | /1.0/dict/administration/area/merchant 复用接口 /1.0/ds/baseinfo/queryDsOnlineStore 复用接口 /1.0/split/strategy/excepted |  |
| 合单策略 | 保存 | 0.5d |  |  | /1.0/mergeOrderStrategy/save |  |
| 详情 | /1.0/mergeOrderStrategy/select |  |
| 空单策略 | 列表 | 2d  空单待确认 |  |  | /ds/baseinfo/platform 获取平台接口数据  /1.0/emptyOrder/list |  |
| 新增或编辑 | /1.0/emptyOrder/saveOrUpdate |  |
| 查看空单详情 | /1.0/emptyOrder/{} |  |
| 启用、禁用、删除 | /1.0/emptyOrder/status |  |
| 日志 | /1.0/emptyOrder/log/{} |  |
| 商品设置-列表 | /1.0/emptyOrder/goods/list |  |
| 删除空单商品信息 | /1.0/emptyOrder/goods/{} DELETE |  |
| 保存或修改商品信息 | /1.0/emptyOrder/goods/saveOrUpdate |  |
|  | 空单标记接口? /emptyOrder/tag? |  |
| 模板管理 | 列表 | 1.5d |  |  | /1.0/templateManage/selectPage |  |
| 模板配置 | /1.0/customtempConfig/getListByType/{}/{} |  |
| 编辑/保存 | /1.0/customtempConfig/saveConfig |  |
| 禁用/启用 | /1.0/templateManage/updateStatus |  |
| 删除 | /1.0/templateManage/deleteTemplates/231 |  |
| 详情 | /1.0/templateManage/templatesInfo |  |
| 新增 | 类别选择**发货单**时调用这个接口/1.0/sendOrder/getTemplateList |  |
| 电子面单 | 列表 | 0.2d |  |  | /1.0/logisticConfigInfo/list |  |
| 新增 | 1.5d |  |  | /1.0/logisticConfigInfo/onlineStore 涉及到权限代码 1d /1.0/logistic/getExpressList /1.0/logisticConfigInfo/tempByPlatExpress /1.0/logisticCustomAttributes/getAttrs /1.0/logisticConfigInfo/saveConfig 保存 |  |
| 编辑 | 0.5d |  |  | /1.0/logisticConfigInfo/tempByPlatExpress 复用接口 /1.0/logisticConfigInfo/onlineStore 复用接口 /1.0/logisticConfigInfo/branchByStoreExpress /1.0/logisticConfigInfo/branchAdd/332 /1.0/logistic/getExpressList 复用接口/1.0/logisticCustomAttributes/getAttrs 复用接口/1.0/logisticConfigInfo/updateConfig 编辑 |  |
| 启用/禁用 | 0.5d |  |  | /1.0/logisticConfigInfo/updateConfigStatus |  |
| 详情 | 都是前面复用接口 |  |
| 删除 | /1.0/logisticConfigInfo/deleteConfig |  |
| 店铺分类 | 列表 | 0.5d |  |  | /1.0/dict/store/classify GET |  |
| 新增 | /1.0/dict/store/classify POST |  |
| 编辑 | /dict/store/classify PUT |  |
| 删除 | /1.0/dict/store/classify/54 DELETE |  |
| 行政区域 | 列表 | 0.5d |  |  | /1.0/dict/administration/area/merchant/level这个方法在前面有多处使用 cn.hydee.business.order.common.service.area.impl.AreaBusinessServiceImpl#queryOrUpdateAreaCache |  |
| 订单 | O2O订单管理 | 订单处理 | 列表 | 2d |  |  | /1.0/ds/order/page/normal |  |
|  | tab统计 | 1d |  |  | /1.0/ds/order/state/count/500001/A190 |  |
|  | 列表-待审方 | 2d |  |  | 9 incomplete  列表上可能有按钮 |  |
|  | 列表-待接单 | 2d |  |  | 10 incomplete  列表上可能有按钮 |  |
|  | 列表-待拣货 | 9d |  |  | 打印拣货单 /1.0/ds/print/web 1d点击修改门店,弹窗 /1.0/ds/baseinfo/queryOrganizationForModify 0.2d修改门店 /1.0/ds/order/update/orderStore 涉及到库存 1d配送跟踪 /1.0/ds/delivery/log/search/{} 0.2d拣货复核:/1.0/ds/order/detail/getCachePickConfirmInfo 0.2d/1.0/ds/order/detail/all/{} 订单详情接口 复用接口 0d/1.0/ds/baseinfo/queryDeliveryPlatform 0.5d/1.0/ds/order/detail/batch/stock 2d 涉及到库存相关/1.0/ds/baseinfo/queryEmpByCondition 0.2d/ds/order/upOrderBatchNo 录入批号 1d/1.0/duty-cash 点击完成拣货 0.5d/1.0/ds/order/mark/exception 提交异常 1d |  |
|  | 列表-待配送 | 6d |  |  | 重新打印拣货单 /1.0/ds/print/web 复用接口 0d重新打印配送小票 /1.0/ds/print/web 复用接口 只是templateType不同 0d到店提货弹窗 /1.0/ds/baseinfo/queryEmpByCondition 复用接口 0d/2.0/ds/order/delivery/delivery/out 到店提货确认 涉及到零售下账 2d/1.0/ds/delivery/log/search/{} 配送跟踪 复用接口 0d取消且修改配送方式:/1.0/ds/baseinfo/queryDeliveryPlatform 复用接口 0d/1.0/ds/order/delivery/record 0.2d/1.0/ds/order/delivery/check/appoint/callRider 检查1d /1.0/ds/order/delivery/merchant 商家配送 3d 逻辑比较复杂 |  |
|  | 列表-配送中 | 3d |  |  | 确认妥投 /2.0/ds/order/delivery/delivery/success 2d/2.0/ds/order/sync 点完确认妥投后会调用订单数据同步...(不应该由前端调用) 1d重新打印拣货单和配送小票/1.0/ds/print/web 复用接口 |  |
|  | 列表-预售单 | 2d |  |  | 列表: /1.0/ds/order/page/obOrder 1d确认到货 /1.0/ds/order/makeSure/obOrderGoods 1d |  |
|  | 列表-取消中 | 3.5d |  |  | 列表: /1.0/ds/order/page/canceling 0.5d同意:/1.0/ds/order/merchant/cancel/agree 2d拒绝:/1.0/ds/order/merchant/cancel/refuse 1d重新打印小票:/1.0/ds/print/web 复用接口 |  |
|  | 列表-异常 | 7d |  |  | 列表: /1.0/ds/order/page/exception 0.5d 异常详情:/1.0/ds/order/detail/all/{}会调用详情 0.5d/1.0/ds/order/detail/batch/batchNo 复用接口 0d修改门店: 同待拣货的修改门店换货:/1.0/ds/order/detail/merchant/modify/detail 1.5d强审:弹窗 /1.0/ds/order/merchant/ex/force-audit 2d强行通过: /1.0/ds/order/merchant/ex/force-audit-status 1d取消:/1.0/ds/order/detail/all/{}会调用详情 复用接口 0d/1.0/ds/store/bill/config/getByOrderNo/{} 0.5d/1.0/ds/order/merchant/cancel/order 取消订单 和这个接口有复用代码(/1.0/ds/order/merchant/cancel/agree) 1d |  |
|  | 列表-催单 | 1.5d |  |  | 列表:/1.0/ds/order/page/urge 0.5d催单回复:/2.0/ds/order/merchant/urge/reply 1d |  |
|  | 列表-退款中 | 5.5d |  |  | 列表: /1.0/ds/refund/ing/page 0.5d 点击退款单号: /1.0/ds/refund/detail/{} 退款单详情 1d/1.0/ds/order/page/orderLog/{}/{} 退款单日志 0.5d 点击退款审核后的弹窗:/1.0/ds/refund/detail/{} 退款单详情 复用接口 0d/1.0/ds/refund/refund/reason 退款原因 0.5d/1.0/ds/store/bill/config/getByOrderNo/{} 复用接口 0d/1.0/ds/templateInfo/selectByType 复用接口 0d 选择退款商品:/1.0/ds/order/detail/all/{} 复用接口 0d 点击提交:/1.0/ds/refund/audit 涉及仅退款、退货退款、拒绝退款 3d |  |
|  | 列表-销售单下账 | 1d |  |  | 列表: /1.0/ds/order/page/orderLedgerListPage 1d详情/1.0/ds/order/detail/all/{} 复用接口/1.0/ds/order/detail/batch/batchNo 复用接口/1.0/ds/order/page/orderLog/{}/{} 日志 复用接口 |  |
|  | 列表-退款单下账 | 0.5d |  |  | 列表: /1.0/ds/refund/RefundLedgerList 0.5d 无其他操作 |  |
|  | 订单查询 | 订单列表 | 10d |  |  | 列表查询: /1.0/ds/order/page/search 2d 批量打印小票:/1.0/ds/order/searchOrderPrint 打印配送单、拣货小票 1d重打小票:/1.0/ds/print/web 打印配送单、拣货小票 复用接口0d 导出配置:/1.0/ds/export/config/list 列表 0.5d/1.0/ds/export/config/save 保存导出配置 0.5d锁库存:/2.0/ds/order/lock/inventory 3d 构建请求里包含save、update等方法 点击退款:/1.0/ds/store/bill/config/getByOrderNo 复用接口 0d/1.0/ds/order/detail/all/{} 复用接口 0d/1.0/duty-cash 复用接口 0d/1.0/ds/order/merchant/check/cancel/order 3d |  |
|  | 订单详情 | 0d |  |  | /1.0/ds/order/page/orderLog/{}/{} 日志 复用接口0d/1.0/ds/order/detail/all/{}详情 复用接口 0d/1.0/ds/order/detail/batch/batchNo 复用接口0d |  |
|  | 订单详情-修改商家备注 | 0.2d |  |  | /1.0/ds/order/changeSellerRemark/sellerRemark |  |
|  | 订单详情-替换商品 | 3d |  |  | /1.0/ds/order/detail/merchant/modify/erpDetail 涉及库存、拣货信息、成本价等 3d |  |
|  | 订单详情-查看下账配置 | 0d |  |  | /1.0/ds/store/bill/config/{} 查看下账详情 前面有复用接口 0d /1.0/ds/baseinfo/queryByCode 0d |  |
|  | 修改订单-订单信息修改:- 修改订单状态 - 退款下账单状态修改 - 退款单状态修改 - 退款下账单状态修改 - 修改订单货位调整单号 - 解锁订单已锁库存 - 清除订单异常/退款中/取消中标识 - 录入美团退款单金额 | 3d |  |  | /1.0/ds/order/operate/change |  |
|  | 修改订单-订单信息修改: - 修改门店订单 | 1.5d |  |  | 修改门店订单会调用一下接口:/1.0/ds/order/detail/simple/{} 0.5d/1.0/ds/baseinfo/getPlatformClientAuthorityStore/{}/{} 1d  /1.0/ds/order/operate/change 复用接口 0d |  |
|  | 修改订单-订单补推 | 2d |  |  | 选择平台门店:/1.0/ds/baseinfo/queryDsOnlineStorePage 复用接口 0d补推接口: /1.0/ds/order/operate/orderRepairByThirdOrderNos 2d 都是重新调用接口 |  |
|  | 修改订单-下单失败修改 | 3d |  |  | 查询: /ds/inner/devOps/correctionTool/qryOrderInfo 0.5d 如果能查到,会继续调用:/1.0/ds/baseinfo/queryEmpByCondition 复用接口 0d 点击【确认】会调用:/1.0/ds/order/checkBatchNos 0.5d变更: /ds/inner/devOps/correctionTool/updateOrderInfo 2d |  |
|  | 导出 | 0.5d |  |  | 查看导出记录 /1.0/ds/export/list 复用接口 0d导出:/1.0/ds/export/orderInfo 0.5d |  |
|  | 退款单查询 | 列表 | 0.5d |  |  | /1.0/ds/refund/page 0.5d |  |
|  | 退款单详情 | 0d |  |  | /1.0/ds/refund/detail/{} 复用接口 0d |  |
|  | 无门店异常单 | 列表 | 0.5d |  |  | /1.0/ds/order/page/noStoreException 复用通用的es逻辑 0.5d |  |
|  | 匹配门店 | 1d |  |  | /1.0/ds/order/matching/stores 代码中querySysStoreInfo复用,含update 1d |  |
|  | 取消订单 | 0d |  |  | /1.0/ds/order/detail/all/{} 复用接口 0d/1.0/ds/store/bill/config/getByOrderNo/{} 复用接口 0d/1.0/ds/order/merchant/cancel/order 复用接口 0d |  |
|  | 下账列表 | 销售下账单-列表 | 1.5d |  |  | 总数(4个tab): /1.0/ds/order/page/orderLedgerCount 0.5d列表数据(4个tab): /1.0/ds/order/page/orderLedgerListPage 1d详情: 复用订单详情接口 0d |  |
|  | 销售下账单-下账失败-修补批号 | 1d |  |  | 点击修改批号:/ds/inner/devOps/correctionTool/qryOrderInfo 复用接口 0d /1.0/ds/order/checkBatchNos 复用接口 0d更新接口 /ds/inner/devOps/correctionTool/updateOrderPickInfo 1d |  |
|  | 销售下账单-导出 | 0.5d |  |  | /1.0/ds/export/list 复用接口 0d/1.0/ds/export/ledgerExport 0.5d |  |
|  | 退款下账单-列表 | 0.2d |  |  | 总数:/1.0/ds/refund/RefundLedgerCount 0.2d列表:/1.0/ds/refund/RefundLedgerList 复用接口  订单详细和退款单详情是复用接口 |  |
|  | 退款下账单-导出 | 0.5d |  |  | /1.0/ds/export/refundLedgerExport 0.5d |  |
|  | 配送单列表 | 列表 | 2d |  |  | 进入列表会调用:/1.0/code/SERVICE_CODE_MT 0.5d列表:/1.0/ds/order/delivery/page/search 1.5d |  |
|  | 导出 | 0.5d |  |  | /1.0/ds/export/deliveryExport 0.5d |  |
|  | 取消配送 |  |  |  | 18 incomplete 造数据或者看接口 /merchant/cancel? |  |
|  | 订单评价列表 | 列表 | 1.5d |  |  | /1.0/ds/commentInfo/page/queryComment 0.5d |  |
|  | 详情 | /1.0/ds/commentInfo/queryCommentDetail 0.2d |  |
|  | 回复评价 | /1.0/ds/commentInfo/replyComment 0.8d 含调用三方接口 |  |
|  | 平台对账 | 列表 | 1.5d |  |  | /1.0/ds/account/check/page/querySummary 0.5d导出所有门店: /1.0/ds/export/accountCheckStoreSumExport 0.5d导出所有订单: /1.0/ds/export/accountCheckAllStoreOrderExport 0.5d |  |
|  | 详情 | 0.7d |  |  | /1.0/ds/account/check/page/queryCheck 0.5d导出: /1.0/ds/export/accountCheckOrderExport 0.2d |  |
|  | 账单详情 | 0.2d |  |  | /1.0/ds/account/check/queryCheckDetail 0.2d |  |
|  | 查看导出记录 | 0d |  |  | /1.0/ds/export/list 复用接口 0d |  |
|  | ERP对账 | 列表 | 1.5d |  |  | /1.0/ds/baseinfo/queryDsOnlineStorePage 复用接口 0d 列表: /1.0/ds/erp/account/check/page/querySummary 0.5d 导出所有门店: /1.0/ds/export/erpCheckExport/store 0.5d  导出所有订单: /1.0/ds/export/erpCheckExport/order/mer 0.5d |  |
|  | 详情 | 0.5d |  |  | /1.0/ds/erp/account/check/page/order/search 0.5d |  |
|  | 批量核对 | 2d |  |  | 批量核对:1d查看核对数据: /1.0/ds/batch/import/page ImportConstant.ERP_CHECK_HANDLE 1d |  |
|  | 核对 | 0.3d |  |  | 20 incomplete /1.0/ds/erp/account/check/handleErpCheckOrder(从代码获取) 复用批量和对的代码 0.3d |  |
|  | 导出订单数据 | 0.5d |  |  | /1.0/ds/export/erpCheckExport/order/store 0.5d |  |
|  | 商城订单 | 订单列表 | ydjia-merchant-manager → hydee-middle-order 做了一层转发 |  |
|  | 拼团订单 |  |
|  | B2C订单管理 | B2C订单处理 | 订单处理各个tab共用接口 | 6.3d |  |  | /1.0/orderListShowConfig/queryByType 订单列表显示配置 0.2d /1.0/ds/baseinfo/platform 获取平台数据接口 0.3d 涉及权限处理 /1.0/userCustomConfig/getByUserId/{} 获取用户自定义配置 0.3d /1.0/order/page/normal 列表接口 3d 涉及权限处理、详情、异常、es等信息处理  点击平台订单,详情: /1.0/order/platform/detail/{} 1d /1.0/order/getOrderLog 订单日志 0.2d  点击系统订单,详情: /1.0/order/detail/{} 1d 修改备注: /1.0/omsOrder/batchSaveRemarkInfo 0.3d 查看下账配置,复用hydee-business-order中的接口: /**dscloud**/1.0/ds/baseinfo/queryByCode 复用接口 0d /**dscloud**/1.0/ds/store/bill/config/{} 复用接口 0d |  |
|  |  | 待审方 | 1d |  |  | /dscloud/1.0/ds/baseinfo/getOnlineStoreInfoByStCode/A002 复用接口(这里调用了hydee-business-order服务的接口) 复用接口 0d确认审方:/1.0/omsOrder/confirmPrescriptions(从代码获得) 0.5d审方不通过: /1.0/omsOrder/prescriptionsFail(从代码获得) 0.5d |  |
|  |  | 待审核 | 25d |  |  | /1.0/order/state/count/500001/-99 获取线下门店订单各状态数量 0.5d /1.0/versionRemind/get 获取版本依赖提醒信息 0.2d /1.0/order/erp/audit/count 获取用户erp审核中数量 0.5d 涉及es 审核: /1.0/omsOrder/confirmCheckOrder 2d改订单-改仓库: /1.0/omsOrder/batchSaveWarehouseId 1d 涉及库存扣减改订单-改快递: /1.0/omsOrder/batchSaveExpressInfo 0.3d改订单-改收货信息: /1.0/omsOrder/batchChangeAddressDetail 1d 改订单-改系统备注: /1.0/omsOrder/batchSaveRemarkInfo 复用接口 0d 改订单-拆分订单: /1.0/split/getOrderDetail 获取明细 0.5d /1.0/split/splitOrder (拆分订单,从代码中获取) 3d 有多个处理器需要处理 改订单-手工合单: /1.0/mergeOrderStrategy/manualMerge 3d 改订单-合并还原: /1.0/mergeOrderStrategy/restore (从代码中获得) 2d改订单-导入空单:/1.0/emptyOrder/tag/import/template 模板下载 0.2d/1.0/emptyOrder/tag/import 导入接口 上传oss,入batch_task表,然后发送系统事件, 触发是这里触发cn.hydee.batch.event.BatchTaskEventListener,具体处理代码为:cn.hydee.business.order.b2c.service.emptyorder.processor.EmptyOrderImportProcessor ,cn.hydee.business.order.b2c.service.emptyorder.dto.EmptyOrderImportDTO 线上没有数据,功能是还在使用?是否需要迁移? 3d  改订单-导入空单-查看导入记录: /1.0/emptyOrder/tag/import/page 0.3d  改订单-标记空单: /1.0/emptyOrder/tag/batch 1d改订单-取消空单: /1.0/emptyOrder/tag/batchCancel 1d改商品-添加商品: /1.0/ds/baseinfo/batchSaveOrderGift 涉及CommodityAutoCreateEvent事件逻辑 1d( 复用/1.0/omsOrder/batchGetWareInfo接口)改商品-替换商品: /1.0/omsOrder/batchChangeOrderDetail 涉及库存变更 3d 取消订单:/1.0/order/unDeliveryOrUnTake/batchCancelOrder 调用取消代码,异常tab-取消订单代码一致, 1d(不处理取消代码,取消代码在下面处理) 转异常单: /1.0/order/getOrderExType 获取异常类型,复用接口 0.2d /1.0/order/submitException 0.3d |  |
|  |  | 待发货 | 3.5d+1d(红色未找到接口,预留时间) |  |  | 获取面单模板信息: /1.0/templateManage/dict 0.2d  改订单-改收货信息和系统备注,复用待审核的接口  导入快递单号: 下账模板 /1.0/batchShip/import/template 0.2d 上传: /1.0/batchShip/import 1d 获取电子面单: /1.0/scanShip/expressSheet/generate/batch 2d 逻辑较多  29 incomplete 扫描发货    30 incomplete 批量发货  取消订单: 复用待审核的接口 0d  31 incomplete 面单打印  发货单打印:  /dist/mapByType 调用字典接口 0.1d  32 incomplete 面单和发货单一起打印 |  |
|  |  | 已发货 | 0.2d |  |  | 改订单-改系统备注,复用待审核的接口 更新快递单号: /1.0/scanShip/updateExpress 0.2d 获取电子面单: 复用待发货的获取电子面单 |  |
|  |  | 退款处理 | 0.9d |  |  | /1.0/ds/baseinfo/refunding/platform 获取退款中平台数据接口 0.2d /1.0/refundProcessing/list 退款处理中订单列表查询 0.5d /1.0/dist/web/getMap 前端定制化字典集合 0.2d |  |
|  |  | 异常 | 7d |  |  | /1.0/order/getOrderExType 获取异常类型,复用接口 0d 列表: /1.0/order/page/exception 列表(这是b2c的) 升级到es 1d 【改订单】【改商品】中所有的操作复用待审核中的【改订单】【改商品】取消订单:/1.0/order/cancelOrder 涉及库存 3d 转正常单: /1.0/order/restoreOrder 2d 清除异常: /1.0/order/cleanEx 1d |  |
|  | B2C订单查询 | 列表 | 9.9d |  |  | /1.0/dist/web/getMap 复用接口 0d /1.0/dict/administration/area/merchant 复用接口 0d /1.0/order/list 列表 1d 数据组织逻辑复杂 手工订单:前置: /1.0/dict/administration/area/merchant 复用 、/1.0/dict/express/merchant 复用、/1.0/ds/baseinfo/platform 复用/1.0/order/detail/forSupply/ 手工创建补发单用平台订单详情查询 0.5d/1.0/omsOrder/batchGetWareInfo 在创建手工单的弹窗点击新增商品调用 3d 含CommodityAutoCreateEvent事件逻辑/1.0/order/manual 手工建单 2d 修改订单:销售单下账状态修改、修改订单状态: /1.0/order/status/modify 0.3d 导入订单,导入:/1.0/order/import/template 模板下载 0.1d /1.0/order/import 导入 2d(含处理逻辑)  导入订单,查看导入记录: /1.0/order/import/page 0.5d 订单、订单明细导出配置:/1.0/export/config GET 查看 保存 POST 0.5d |  |
|  | B2C平台订单 | 列表 | 0.5d |  |  | /1.0/plat/order/list 0.5d |  |
|  | 详情 | 0d |  |  | /1.0/order/platform/detail/{} 复用 0d |  |
|  | 手工补单 | 0.3d |  |  | /1.0/plat/order/repair 0.3d |  |
|  | B2C平台退款单 | 列表 | 4d |  |  | 前置接口:/1.0/ds/baseinfo/queryDsWarehouse 0.2d /1.0/dict/administration/area/merchant 复用 0d 列表: /1.0/refund/plat/page 0.3d  详情: /1.0/refund/plat/{} 0.5d  /1.0/order/platform/detail/ 复用接口 0d 申请售后: /1.0/order/detail/forAfterSale/{} 1d/1.0/refund/manualCreate/afterSaleOrder 创建售后单 2d |  |
|  | B2C售后单 | 列表 | 0.5d |  |  | /1.0/refund/afterSale 列表 0.5d 日志 /1.0/order/getOrderLog 复用接口 0d |  |
|  | 详情 | 0.6d |  |  | 售后单详情/1.0/refund/getAfterSale/Detail/{} 0.5d 修改备注 /1.0/refund/afterSaleOrder/remarks 0.1d下账配置是复用的，hydee-business-order接口 |  |
|  | 关闭售后单 | 0.2d |  |  | /1.0/refund/close/afterSale 纯DB操作 |  |
|  | 快速退货 | 2d |  |  | /1.0/refund/afterSaleOrder/quickRefund   里面的详情复用/1.0/order/detail/forAfterSale/这个接口; 售后复用的是/refund/manualCreate/afterSaleOrder接口;/refund/returnGoods/audit 这个接口在页面上没有找到,但是在这个接口中有调用,**需要处理**,/detail/forAfterSale/{}详情接口复用 ； 2d |  |
|  | 查看售后单导出记录 | 1.3d |  |  | /1.0/export/config GET POST 复用 0d /1.0/export/after_sale/action 售后单导出 1d /1.0/exportRecord/search 0.3d |  |
|  | 新建售后单 | 0d |  |  | /1.0/refund/manualCreate/afterSaleOrder 复用 0d |  |
|  | B2C退货单 | 列表 | 0.7d |  |  | /1.0/refund/returnGoods 0.3d |  |
|  | 详情 | /1.0/refund/getReturnGoods/Detail 0.2d |  |
|  | 修改备注 | /1.0/refund/returnGoodsOrder/remarks 0.2d |  |
|  | B2C下账列表 | 销售下账单 | 3.5d |  |  | 列表: /1.0/billList/page/orderLedgerList 0.3d /1.0/billList/page/orderLedgerCount 0.2d /1.0/order/platform/detail/{}、/1.0/order/detail/{} 、/1.0/order/getOrderLog复用接口 待下账: /1.0/billList/sale/bill 下账 2d(下账逻辑的代码被注释掉了,需要关注下) 只有更新操作  查看导出记录: /1.0/exportRecord/search 复用 0d导出: /1.0/export/order/bill/action 1d/1.0/export/config GET POST 复用 0d |  |
|  |  |  | 退款下账单 | 2.5d |  |  | 列表: /1.0/billList/page/afterSale/ledgerList 0.3d /1.0/billList/page/afterSale/ledgerCount 0.2d 查看导出记录: /1.0/exportRecord/search 复用 0d 导出:/1.0/export/after_sale/bill/action 0.5d /1.0/export/config GET POST 复用 0d  /1.0/order/platform/detail/、/1.0/order/detail/、/1.0/order/getOrderLog 复用  /1.0/refund/getAfterSale/Detail、/1.0/ds/baseinfo/platform 复用 下账:/1.0/refund/bill 核心逻辑主要是构建请求,然后调用接口 1.5d |  |
|  | 订单分析 | 经营分析 | 列表 | 8d |  |  | **平台销售统计**今日实时 /1.0/ds/report/statistics/reportColumn/query 获取字段配置 0.5d/1.0/ds/report/statistics/orderPay/realTime 1d历史/1.0/ds/report/statistics/orderPay/historyTime 0.5d /1.0/ds/report/statistics/orderPay/historyOrg 0.5d  点击机构展开: /1.0/ds/report/statistics/orderPay/historyStore 0.5d 机构导出: /ds/export/reportStatisticsPayOrgExport 插入记录 + 导出 0.5d线上门店导出: /1.0/ds/export/reportStatisticsPayStoreExport 0.5d 机构和线上门店导出记录: /1.0/ds/export/list 复用 0d  **零售下账统计**今日实时/1.0/ds/order/list/PCA 查询店员可以选择的省市区 0.5d /1.0/ds/report/statistics/reportColumn/query 复用 0d/1.0/ds/report/statistics/orderBill/realTime 1d 历史数据分析: /1.0/ds/report/statistics/orderBill/historyTime 0.5d /1.0/ds/report/statistics/orderBill/historyOrg 0.5d 点击机构展开: /1.0/ds/report/statistics/orderBill/historyStore 0.5d 机构导出: /1.0/ds/export/reportStatisticsBillOrgExport 0.5d线上门店导出: /1.0/ds/export/reportStatisticsBillStoreExport 0.5d 机构和线上门店导出记录: /1.0/ds/export/list 复用 |  |
|  | 订单热力图 | 列表 | 2d |  |  | /1.0/ds/order/cluster/queryOrderByCluster 1d /1.0/ds/order/cluster/queryStoreByCity 0.5d(复用第一个接口中的queryLineOffStoreListByArea方法) /1.0/ds/order/cluster/queryAllOnlineStoreAndOrderCount 0.5d(复用第一个接口中的queryLineOffStoreListByArea方法) |  |
|  | 进行中订单看板 | 列表 | 2.5d |  |  | /1.0/ds/order/orderInfo/orgSnapshot redis中获取,处内存操作 0.5d展开机构: /1.0/ds/order/orderInfo/storeSnapshot 1d  机构导出: /1.0/ds/export/snapshot/organization 0.5d门店导出: /1.0/ds/export/snapshot/store 0.5d 机构和线上门店导出记录: /1.0/ds/export/list 复用 |  |
|  | 服务分析报表 | 列表 | 1.5d |  |  | /1.0/ds/service/mer/qryReport 0.5d 展开 /1.0/ds/service/store/qryReport 0.5d导出机构: /1.0/ds/export/organization/service/report 0.2d 导出门店: /1.0/ds/export/store/service/report 0.3d  机构和线上门店导出记录: /1.0/ds/export/list 复用0d |  |
|  | 超卖订单分析 | 列表 | 0.6d |  |  | /2.0/ds/order/orderOverSold/reasonInfo 获取超卖原因枚举0.1d/2.0/ds/order/orderOverSold/query 0.5d |  |
|  | 待发货订单统计 |  | 0.5d + 0.5d（红色未找到接口预留） |  |  | b2c的接口: /b2c/1.0/ds/baseinfo/queryDsWarehouse 复用 0d /b2c/1.0/ds/baseinfo/queryDsOnlineStore 复用 0d /b2c/1.0/order/unDelivery/goods 0.5d/b2c/1.0/exportRecord/search 查看导出记录  34 incomplete 导出接口 |  |


## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等

### 兼容方案

订单提供一个接口列表,列表数据是订单使用DDD应用架构重写好的url,前端在每次请求前判断请求的url是否列表中,如果在列表中则路由到新的DDD应用

true新老接口请求方案falseautotoptrue5211

迁移后流量监控:

grafana上监控老应用有无流量: [地址](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/EG1KNOVIk/xin-ling-shou-wei-fu-wu-jie-kou-jian-kong?var-namespace=prod&var-application=dscloud-hydee-business-order-service&var-instance=All&var-uri=All)