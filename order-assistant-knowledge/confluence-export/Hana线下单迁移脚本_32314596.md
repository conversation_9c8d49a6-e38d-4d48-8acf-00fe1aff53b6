# Hana线下单迁移脚本

### 数据量统计

|  | 表 | 数据量 |
| --- | --- | --- |
| 迁移订单归属信息 | HX_CRM.CV_STOREINFOR | 13435 0.002GB |
| 迁移会员信息 | HX_CRM.TB_CRM_MATSTER_N | 8421 5241-- 这个不用迁移了,调用会员接口 |
| 迁移商品信息 | HANA_D.MAKT | 31 3926 0.03GB |
| 迁移支付描述 | HX_SJCJ.TENDTYT | 644 0.0002GB |
| 迁移公司员工表 | HANA_APP.HRMRESOURCE | 16 5082 0.24GB |
| 迁移返利表 | YNHX_DATA01.XF_DBM_COUPON —— 只有云南公司(YNHX_DATA01)使用 | 158 1274 0.08GB |
| 迁移赠品活动表 | YNHX_DATA01.XF_ZT_GIFTGIVING_MEMOMAP —— 只有云南公司(YNHX_DATA01)使用 | 9 0505 0.002GB |


### 各公司订单数据量统计

| 表 | YNHX_DATA01(云南） | GXHX_USERS（广西） | GZHX_USERS(贵州） | SCHX_USERS(攀枝花) | SXHX_USERS(山西） | CQHX_USERS(重庆） | CDHX_USERS(四川） | SHHX_DATA01(上海) | TJHX_DATA01(天津） |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 订单表 XF_TRANSSALESTOTAL | 412,133,428 **4亿 40.59GB** | 80,072,308 5.5GB | 43,785,029 2.31GB | 31,939,591 2.07GB | 42,362,343 2.09 GB | 40,815,600 2.54GB | 84,381,048 5.64GB | 3,879,485 0.27GB | 3,462,236 0.2GB |
| 订单明细表 XF_TRANSSALESITEM | 849,107,199 **8亿 88.89GB** | 154,305,785 **1.5亿 8.12GB** | 76,759,271 4.29GB | 61,494,505 3.93GB | 85,766,737 4.45GB | 81,701,964 5.24GB | 167,809,781 **1.6亿 11.31GB** | 6,796,466 0.5GB | 8,026,655 0.41GB |
| 订单支付表 XF_TRANSSALESTENDER | 430,926,020 **4亿 45.02GB** | 86,172,751 5.58GB | 46,008,559 2.64GB | 32,787,884 2.15GB | 45,085,445 1.90GB | 42,801,779 2.79GB | 91,189,147 4.52GB | 4,445,482 0.25GB | 4,342,410 0.22GB |


| 表 | HNHX_DATA01(海南） | HENHX_DATA01(河南) | SXGSHX_DATA01(山西广生) | TJQCHX_DATA01(天津乾昌) | HENNYHX_DATA01(河南康健) | ZYHX_USERS(中药科技) |
| --- | --- | --- | --- | --- | --- | --- |
| 订单表 XF_TRANSSALESTOTAL | 47,152,323 2.57GB | 4,384,125 0.22GB | 3,636,761 0.19GB | 1,342,897 0.12GB | 1,827,119 0.08GB | 27,277 0.001GB |
| 订单明细表 XF_TRANSSALESITEM | 90,972,758 5.13GB | 7,555,044 0.43GB | 7,072,112 0.38GB | 3,195,590 0.29GB | 3,732,908 0.18GB | 77,213 0.003GB |
| 订单支付表 XF_TRANSSALESTENDER | 50,944,032 3.12GB | 4,608,432 0.28GB | 3,921,050 0.17GB | 1,521,474 0.13GB | 1,963,017 0.13GB | 27,299 0.001GB |


汇总(数据截止20241008):

总数据量: 3252320339, 32.5亿

订单数据hana占用磁盘大小: 267.09GB

非订单数据hana占用磁盘大小: 0.14GB

全量迁移至归档库mysql容量需要乘以7左右,即1870.61GB左右(不含新增的索引)

### 迁移记录

### 数据量统计

|  | 表 | 数据量 |  |
| --- | --- | --- | --- |
| 迁移订单归属信息 | HX_CRM.CV_STOREINFOR | 13435 0.002GB | 219 complete 前置数据迁移_迁移20241007前的数据_yangrunkang |
| 迁移商品信息 | HANA_D.MAKT | 31 3926 0.03GB |
| 迁移支付描述 | HX_SJCJ.TENDTYT | 644 0.0002GB |
| 迁移公司员工表 | HANA_APP.HRMRESOURCE | 16 5082 0.24GB |
| 迁移返利表 | YNHX_DATA01.XF_DBM_COUPON 只有云南公司(YNHX_DATA01)使用 | 158 1274 0.08GB |
| 迁移赠品活动表 | YNHX_DATA01.XF_ZT_GIFTGIVING_MEMOMAP  只有云南公司(YNHX_DATA01)使用 | 9 0505 0.002GB |


| 公司 | 任务名 |
| --- | --- |
| ZYHX_USERS(中药科技) | 220 complete ZYHX_USERS_中药科技_迁移20241007前的数据_yangrunkang |
| HENNYHX_DATA01(河南康健) | 221 complete HENNYHX_DATA01_河南康健_迁移20241007前的数据_yangrunkang |
| TJQCHX_DATA01(天津乾昌) | 222 complete TJQCHX_DATA01天津乾昌_迁移20241007前的数据_yangrunkang |
| SXGSHX_DATA01(山西广生) | 223 complete SXGSHX_DATA01_山西广生_迁移20241007前的数据_yangrunkang |
| HENHX_DATA01(河南) | 224 complete HENHX_DATA01_河南_迁移20241007前的数据_yangrunkang |
| TJHX_DATA01(天津） | 225 complete TJHX_DATA01_天津_迁移20241007前的数据_yangrunkang |
| SHHX_DATA01(上海) | 226 complete SHHX_DATA01_上海_迁移20241007前的数据_yangrunkang |
| SCHX_USERS(攀枝花) 8.15GB | 227 complete SCHX_USERS_攀枝花_迁移20241007前的数据_yangrunkang |
| SXHX_USERS(山西） 8.44GB | 228 complete SXHX_USERS_山西_迁移20241007前的数据_yangrunkang |
| GZHX_USERS(贵州） 9.24GB | 229 complete GZHX_USERS_贵州_迁移20241007前的数据_yangrunkang |
| CQHX_USERS(重庆） 10.57GB | 230 complete CQHX_USERS_重庆_迁移20241007前的数据_yangrunkang |
| HNHX_DATA01(海南）10.82GB | 231 complete HNHX_DATA01_海南_迁移20241007前的数据_yangrunkang |
| 264 complete HNHX_DATA01_海南_迁移20241007前的数据_yangrunkang_数据错误_重跑 |
| GXHX_USERS（广西）19.2GB | 232 complete GXHX_USERS_广西_迁移20241007前的数据_yangrunkang |
| CDHX_USERS(四川） 21.47GB | 233 complete CDHX_USERS_四川_迁移20241007前的数据_yangrunkang |
| YNHX_DATA01(云南）174.5GB | 234 complete YNHX_DATA01_云南_20092013_迁移_yangrunkang_v2 |
| 269 complete YNHX_DATA01_云南_2021_迁移_yangrunkang |
| 270 complete YNHX_DATA01_云南_2022_迁移_yangrunkang |
| 271 complete YNHX_DATA01_云南_2023_迁移_yangrunkang |
| 272 complete YNHX_DATA01_云南_迁移20240101到20241007前的数据_yangrunkang |


20250213更新

故，历史归档数据截止至2025年1月15号12点。

### 迁移记录

- 迁移增量数据时,需要命名指定时间范围,>,<=,避免迁移重复数据
- 如果需要重新全量同步，在任务界面添加truncate


| hana表 | mysql表 | 时间范围 | 是否迁移至归档库 | 记录(源数据量，目标库数据量) | 第二次迁移(迁移日期20250225,包含20250115) | 是否迁移至线下单表 |
| --- | --- | --- | --- | --- | --- | --- |
| 迁移订单归属信息 HX_CRM.CV_STOREINFOR | CV_STOREINFOR | 2024108 全量 | 21 complete | 20241009：13,435,13435 | 385 complete     386 complete     387 complete |  |
| 迁移商品信息 HANA_D.MAKT | MAKT | 2024108 全量 | 22 complete | 20241009: 313,946,313946 |  |
| 迁移支付描述 HX_SJCJ.TENDTYT | TENDTYT | 2024108 全量 | 23 complete | 20241009: 644，644 |  |
| 迁移公司员工表 HANA_APP.HRMRESOURCE | HRMRESOURCE | CREATEDATE <= '20241007' | 24 complete | 20241009: CREATEDATE <= '20241007',165,082,165082 | 388 incomplete 1   389 incomplete 23   390 incomplete 34 |  |
| 迁移返利表 XF_DBM_COUPON —— 只有云南公司使用 | XF_DBM_COUPON_YNHX_DATA01 | XF_TXDATE <= '2024-10-07 00:00:00' | 25 complete | 20241009:XF_TXDATE <= '2024-10-07 00:00:00',1,580,889,1580889 |  |
| 迁移赠品活动表 XF_ZT_GIFTGIVING_MEMOMAP —— 只有云南公司使用 | XF_ZT_GIFTGIVING_MEMOMAP_YNHX_DATA01 | XF_TXDATE <= '2024-10-07 00:00:00' 这个表的最新数据到20240624 | 26 complete | 20241014:XF_TXDATE <= '2024-10-07 00:00:00',90,505,90505 |  |


基础数据校验SQL

#hana


select count(1) from HX_CRM.CV_STOREINFOR  union all
select count(1) from HANA_D.MAKT  union all
select count(1) from HX_SJCJ.TENDTYT  union all
select count(1) from HANA_APP.HRMRESOURCE where CREATEDATE  <= '2025-01-16 00:00:00' union all
select count(1) from YNHX_DATA01.XF_DBM_COUPON where XF_TXDATE <= '2025-01-16 00:00:00' union all
select count(1) from YNHX_DATA01.XF_ZT_GIFTGIVING_MEMOMAP where XF_TXDATE <= '2025-01-16 00:00:00';




#mysql
select count(1) from CV_STOREINFOR union all
select count(1) from MAKT union all
select count(1) from TENDTYT union all
select count(1) from HRMRESOURCE where CREATEDATE  <= '2025-01-16 00:00:00' union all
select count(1) from XF_DBM_COUPON_YNHX_DATA01 where XF_TXDATE <= '2025-01-16 00:00:00' union all
select count(1) from XF_ZT_GIFTGIVING_MEMOMAP_YNHX_DATA01 where XF_TXDATE <= '2025-01-16 00:00:00';




| 公司 | 条件及是否迁移完成 | 订单表XF_TRANSSALESTOTAL | 订单明细表XF_TRANSSALESITEM | 订单支付表XF_TRANSSALESTENDER | 备注 |
| --- | --- | --- | --- | --- | --- |
| ZYHX_USERS(中药科技) | 29 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 27,277,27277 | 77,213，77213  138 complete 重新迁移 | 27,299，27299 |  |
| 338 complete batchworks配置,已配置但是无任何作用,只是标识   308 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' | 增量数据无需迁移(但是表还是建了的，因为是统一脚本) |  |
| HENNYHX_DATA01(河南康健) | 30 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 1,822,007,1822007 | 3,721,660,3721660  139 complete 重新迁移 | 1,957,453,1957453 |  |
| 339 complete batchworks配置   340 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| TJQCHX_DATA01(天津乾昌) | 33 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 1,339,689,1339689 | 3,187,479,3187479  140 complete 重新迁移 | 1,517,701,1517701 |  |
| 341 complete batchworks配置   342 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| SXGSHX_DATA01(山西广生) | 34 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 3,632,708,3632708 | 7,064,375,7064375  141 complete 重新迁移 | 3,916,669,3916669 |  |
| 343 complete batchworks配置   344 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| HENHX_DATA01(河南) | 36 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 4,379,387,4379387 | 7,546,394,7546394  142 complete 重新迁移 | 4,603,625,4603625 |  |
| 345 complete batchworks配置   346 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| TJHX_DATA01(天津） | 38 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 3,457,597,3457597 | 8,016,163,8016163  143 complete 重新迁移 | 4,337,670,4337670 | hana表item表字段多了2个字段,XF_YIBAOLESS,XF_PAYMENTLESS,因为此，修改了item表生产模板,已经迁移的表都没有改字段，后续的迁移有则迁移改字段,无则不迁移 |
| 347 complete batchworks配置   348 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| SHHX_DATA01(上海) | 40 complete XF_CREATETIME <= '2024-10-07 00:00:00' | 3,866,848,3866848 | 6,772,823,6772816  144 complete 重新迁移  差7条数据通知海典处理后,数据可以对齐了6,772,816/6,772,823,6772816 | 4,431,536,4431536 | XF_CLIENTCODE,XF_VIPCODE长度调整到24,模板也调整了 |
| 349 complete batchworks配置   350 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| CQHX_USERS(重庆）分表策略按**年**分表   105 incomplete 已配置 | XF_CREATETIME >= '2018-01-01 00:00:00' AND XF_CREATETIME < '2019-01-01 00:00:00' | 239 complete 迁移任务创建   240 complete 数据量核对  订单数据 4,351,986/4351986 4,630,786/4630786 4,982,334/4982334 5,940,138/5940138 7,016,517/7016517 6,936,867/6936867 6,916,331/6916331  明细数据 8,344,363/8344363 9,262,651/9262651 10,325,395/10325395 12,035,320/12035320 14,849,740/14849740 13,524,585/13524585 13,284,668/13284668  支付数据 4,530,741/4530741 4,902,234/4902234 5,324,244/5324244 6,267,604/6267604 7,382,154/7382154 7,302,182/7302182 7,051,267/7051267 | 添加了是否迁移字段  63 complete 上面的表可以通过sql来添加,不用重新迁移 |
| XF_CREATETIME >= '2019-01-01 00:00:00' AND XF_CREATETIME < '2020-01-01 00:00:00' |
| XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |
| 351 complete batchworks配置   352 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| SXHX_USERS(山西） 分表策略按**年**分表   106 complete 已配置 | XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' | 241 complete 迁移任务创建   242 complete 数据量核对  订单数据 7,349,531/7349531 8,471,747/8471747 8,390,600/8390600 9,168,335/9168335 8,939,164/8939164  明细数据 13,727,888/13727888 16,275,854/16275854 17,900,317/17900317 19,594,189/19594189 18,183,213/18183213  支付数据 7,854,068/7854068 9,116,263/9116263 8,949,695/8949695 9,736,215/9736215 9,384,279/9384279 |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |  |
| 353 complete batchworks配置   354 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| SCHX_USERS(攀枝花)分表策略按**年**分表    107 complete 已配置 | XF_CREATETIME >= '2018-01-01 00:00:00' AND XF_CREATETIME < '2019-01-01 00:00:00' | 182 complete done 3,542,825/ 3542825 | 183 complete done 7,215,608/ 7215608 | 184 complete done 3,646,506/ 3646506 |  |
| XF_CREATETIME >= '2019-01-01 00:00:00' AND XF_CREATETIME < '2020-01-01 00:00:00' | 185 complete done 3,735,371/ 3735371 | 186 complete done 7,230,600/ 7230600 | 187 complete done 3,832,683/ 3832683 |  |
| XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' | 188 complete done 3,870,348/ 3870348 | 189 complete done 7,581,446/ 7581446 | 190 complete done 4,013,404/ 4013404 |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' | 191 complete done 3,871,211/ 3871211 | 192 complete done 7,786,531 / 7786531 | 193 complete done 3,968,312/ 3968312 |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' | 194 complete done 4,161,567 / 4161567 | 195 complete done 8,103,152/ 8103152 | 196 complete done 4,283,615/ 4283615 |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' | 197 complete done 3,951,141/ 3951141 | 198 complete done 7,230,155 / 7230155 | 199 complete done 4,048,895 / 4048895 |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' | 200 complete done 8,692,738/ 8692738 | 201 complete done 16,135,049/ 16135049 | 202 complete done 8,877,243/ 8877243 |  |
| 355 complete batchworks配置   356 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| GZHX_USERS(贵州）分表策略按**年**分表   108 complete 已配置 | XF_CREATETIME >= '2018-01-01 00:00:00' AND XF_CREATETIME < '2019-01-01 00:00:00' | 245 complete 迁移任务创建   246 complete 数据量核对  订单数据 3,394,232/3394232 3,996,917/3996917 4,942,804/4942804 6,584,474/6584474 7,869,339/7869339 8,661,536/8661536 8,289,281/8289281 明细数据 6,402,092/6402092 7,072,262/7072262 8,734,751/8734751 11,246,788/11246788 13,896,482/13896482 15,022,480/15022480 14,302,860/14302860 支付数据 3,624,144/3624144 4,298,380/4298380 5,294,760/5294760 6,924,011/6924011 8,235,368/8235368 9,111,342/9111342 8,473,243/8473243 |  |
| XF_CREATETIME >= '2019-01-01 00:00:00' AND XF_CREATETIME < '2020-01-01 00:00:00' |  |
| XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |  |
| 357 complete batchworks配置   358 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| 公司 | 条件及是否迁移完成 | 订单表XF_TRANSSALESTOTAL | 订单明细表XF_TRANSSALESITEM | 订单支付表XF_TRANSSALESTENDER | 备注 |
| GXHX_USERS（广西）分表策略按**季度**分表   109 complete 已配置 | XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2020-04-01 00:00:00' | 255 complete 迁移任务创建   256 complete 数据量核对   订单数据 3,177,294/3177294 3,478,361/3478361 3,421,887/3421887 3,689,402/3689402 3,516,901/3516901 3,823,893/3823893 4,388,371/4388371 4,703,643/4703643 4,148,448/4148448 4,430,302/4430302 4,171,339/4171339 5,502,522/5502522 3,652,703/3652703 4,260,499/4260499 4,295,423/4295423 4,880,690/4880690 4,840,695/4840695 4,649,477/4649477 4,623,569/4623569 343,344/343344 明细数据 6,285,969/6285969 6,608,032/6608032 6,612,342/6612342 7,026,909/7026909 6,731,807/6731807 6,869,236/6869236 7,761,876/7761876 8,259,604/8259604 7,463,046/7463046 8,033,052/8033052 7,650442/7650442 10,781,481/10781481 6,906,394/6906394 8,318400/8318400 8,523,914/8523914 9,895,316/9895316 10,321,037/10321037 9,919499/9919499 9,467464/9467464 717,038/717038 支付数据 3,478,917/3478917 3,807814/3807814 3,753,066/3753066 4,011,562/4011562 3,792,569/3792569 4,120,366/4120366 4,706,809/4706809 5,093,776/5093776 4,506,714/4506714 4,754,439/4754439 4479,932/4479932 5,882,939/5882939 3,976,558/3976558 4,687,519/4687519 4,709,463/4709463 5,312,022/5312022 5,063,899/5063899 4,826,993/4826993 4,778,633/4778633 353,120/353120 |  |
| XF_CREATETIME >= '2020-04-01 00:00:00' AND XF_CREATETIME < '2020-07-01 00:00:00' |  |
| XF_CREATETIME >= '2020-07-01 00:00:00' AND XF_CREATETIME < '2020-10-01 00:00:00' |  |
| XF_CREATETIME >= '2020-10-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2021-04-01 00:00:00' |  |
| XF_CREATETIME >= '2021-04-01 00:00:00' AND XF_CREATETIME < '2021-07-01 00:00:00' |  |
| XF_CREATETIME >= '2021-07-01 00:00:00' AND XF_CREATETIME < '2021-10-01 00:00:00' |  |
| XF_CREATETIME >= '2021-10-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2022-04-01 00:00:00' |  |
| XF_CREATETIME >= '2022-04-01 00:00:00' AND XF_CREATETIME < '2022-07-01 00:00:00' |  |
| XF_CREATETIME >= '2022-07-01 00:00:00' AND XF_CREATETIME < '2022-10-01 00:00:00' |  |
| XF_CREATETIME >= '2022-10-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2023-04-01 00:00:00' |  |
| XF_CREATETIME >= '2023-04-01 00:00:00' AND XF_CREATETIME < '2023-07-01 00:00:00' |  |
| XF_CREATETIME >= '2023-07-01 00:00:00' AND XF_CREATETIME < '2023-10-01 00:00:00' |  |
| XF_CREATETIME >= '2023-10-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME < '2024-04-01 00:00:00' |  |
| XF_CREATETIME >= '2024-04-01 00:00:00' AND XF_CREATETIME < '2024-07-01 00:00:00' |  |
| XF_CREATETIME >= '2024-07-01 00:00:00' AND XF_CREATETIME < '2024-10-01 00:00:00' |  |
| XF_CREATETIME >= '2024-10-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |  |
| 359 complete batchworks配置   360 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| 公司 | 条件及是否迁移完成 | 订单表XF_TRANSSALESTOTAL | 订单明细表XF_TRANSSALESITEM | 订单支付表XF_TRANSSALESTENDER | 备注 |
| YNHX_DATA01(云南）  分表策略结合按**年**和**季度**分表   110 complete 已配置 | 2009-2013 按一个表处理,表名20092013:XF_CREATETIME >= '2009-01-01 00:00:00' AND XF_CREATETIME < '2014-01-01 00:00:00' | 275 complete 迁移任务创建   276 complete 数据量核对    订单数据102/102 9.006,960/9006960 7,237593/7237593 8,631,008/8631008 8.200.207/8200207 8,519,961/8519961 8183094/8183094 8,851,395/8851395 8,935,984/8935984 8,871,159/8871159 9,348,754/9348754 8,771,886/8771886 9,931,283/9931283 9,226,852/9226852 7660781/7660781 8,789,968/8789968 8,320.662/8320662 8,347,809/8347809 8,396,546/8396546 9,380,591/9380591 9,086,765/9086765 8,627,035/8627035 9,097,103/9097103 8957516/8957516 15,073,934/15073934 9,530,223/9530223 7,455,890/7455890 9,497,112/9497112 8,760,184/8760184 8,940,347/8940347 8,841,586/8841586 8,831,441/8831441 8,778,217/8778217 9,059,566/9059566 9,423,386/9423386 9,379,469/9379469 10,153,443/10153443 10,036,006/10036006 9.495826/9495826 10,011,138/10011138 8,943,055/8943055 8,862,257/8862257 8.798053/8798053 10,111,327/10111327 9833443/9833443 9,416,111/9416111 2,092,308/2092308 明细数据434/434 21486.950/21486950 18.110.565/18110565 19.484,289/19484289 18,002482/18002482 17.583,286/17583286 16,796,376/16796376 18,480858/18480858 17.995.675/17995675 18.164.053/18164053 19.138,632/19138632 18638.909/18638909 21,453,074/21453074 20.759.103/20759103 16,304,586/16304586 18,518781/18518781 17,774,395/17774395 17253,322/17253322 17085267/17085267 19.067344/19067344 18,226,245/18226245 18491,132/18491132 18858.549/18858549 19.073,330/19073330 35,333,223/35333223 18944553/18944553 15.450.072/15450072 18,777164/18777164 17861,118/17861118 18009,991/18009991 17.560.797/17560797 17,517469/17517469 17.609.419/17609419 18408,969/18408969 18,318,950/18318950 18888,306/18888306 20.777955/20777955 19.911,596/19911596 19.526.025/19526025 19793,861/19793861 17,825,669/17825669 17.805.168/17805168 16.318.475/16318475 18,059.247/18059247 17900.784/17900784 17,150.651/17150651 3,838,317/3838317支付数据 102/102 9,489,799/9489799 7,641,687/7641687 9.152,359/9152359 8,630.846/8630846 8,862,473/8862473 8469.253/8469253 9.181,372/9181372 9,267,460/9267460 9,201,787/9201787 9,721,838/9721838 9,143,429/9143429 10,340.038/10340038 9,633,813/9633813 7,990427/7990427 9,189,919/9189919 8,694019/8694019 8713,613/8713613 8791069/8791069 9.854.444/9854444 9,587899/9587899 9.086,003/9086003 9,602,643/9602643 9,457,151/9457151 15,680,292/15680292 10.004,543/10004543 7894,323/7894323 10.039.818/10039818 9,285,034/9285034 9.480,322/9480322 9,376,881/9376881 9,375,576/9375576 9,298n784/9298784 9,533,192/9533192 9,983,965/9983965 9,951255/9951255 10,770.045/10770045 10,609,227/10609227 9,769,373/9769373 10,304,656/10304656 9,210.803/9210803 9,108,277/9108277 8,988,691/8988691 10,330,118/10330118 10.042,723/10042723 9,614,976/9614976 2,133,284/2133284 |  |
| 2013-2020中间没有数据,不处理2021-2024按月分表 |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2021-02-01 00:00:00' |  |
| XF_CREATETIME >= '2021-02-01 00:00:00' AND XF_CREATETIME < '2021-03-01 00:00:00' |  |
| XF_CREATETIME >= '2021-03-01 00:00:00' AND XF_CREATETIME < '2021-04-01 00:00:00' |  |
| XF_CREATETIME >= '2021-04-01 00:00:00' AND XF_CREATETIME < '2021-05-01 00:00:00' |  |
| XF_CREATETIME >= '2021-05-01 00:00:00' AND XF_CREATETIME < '2021-06-01 00:00:00' |  |
| XF_CREATETIME >= '2021-06-01 00:00:00' AND XF_CREATETIME < '2021-07-01 00:00:00' |  |
| XF_CREATETIME >= '2021-07-01 00:00:00' AND XF_CREATETIME < '2021-08-01 00:00:00' |  |
| XF_CREATETIME >= '2021-08-01 00:00:00' AND XF_CREATETIME < '2021-09-01 00:00:00' |  |
| XF_CREATETIME >= '2021-09-01 00:00:00' AND XF_CREATETIME < '2021-10-01 00:00:00' |  |
| XF_CREATETIME >= '2021-10-01 00:00:00' AND XF_CREATETIME < '2021-11-01 00:00:00' |  |
| XF_CREATETIME >= '2021-11-01 00:00:00' AND XF_CREATETIME < '2021-12-01 00:00:00' |  |
| XF_CREATETIME >= '2021-12-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2022-02-01 00:00:00' |  |
| XF_CREATETIME >= '2022-02-01 00:00:00' AND XF_CREATETIME < '2022-03-01 00:00:00' |  |
| XF_CREATETIME >= '2022-03-01 00:00:00' AND XF_CREATETIME < '2022-04-01 00:00:00' |  |
| XF_CREATETIME >= '2022-04-01 00:00:00' AND XF_CREATETIME < '2022-05-01 00:00:00' |  |
| XF_CREATETIME >= '2022-05-01 00:00:00' AND XF_CREATETIME < '2022-06-01 00:00:00' |  |
| XF_CREATETIME >= '2022-06-01 00:00:00' AND XF_CREATETIME < '2022-07-01 00:00:00' |  |
| XF_CREATETIME >= '2022-07-01 00:00:00' AND XF_CREATETIME < '2022-08-01 00:00:00' |  |
| XF_CREATETIME >= '2022-08-01 00:00:00' AND XF_CREATETIME < '2022-09-01 00:00:00' |  |
| XF_CREATETIME >= '2022-09-01 00:00:00' AND XF_CREATETIME < '2022-10-01 00:00:00' |  |
| XF_CREATETIME >= '2022-10-01 00:00:00' AND XF_CREATETIME < '2022-11-01 00:00:00' |  |
| XF_CREATETIME >= '2022-11-01 00:00:00' AND XF_CREATETIME < '2022-12-01 00:00:00' |  |
| XF_CREATETIME >= '2022-12-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2023-02-01 00:00:00' |  |
| XF_CREATETIME >= '2023-02-01 00:00:00' AND XF_CREATETIME < '2023-03-01 00:00:00' |  |
| XF_CREATETIME >= '2023-03-01 00:00:00' AND XF_CREATETIME < '2023-04-01 00:00:00' |  |
| XF_CREATETIME >= '2023-04-01 00:00:00' AND XF_CREATETIME < '2023-05-01 00:00:00' |  |
| XF_CREATETIME >= '2023-05-01 00:00:00' AND XF_CREATETIME < '2023-06-01 00:00:00' |  |
| XF_CREATETIME >= '2023-06-01 00:00:00' AND XF_CREATETIME < '2023-07-01 00:00:00' |  |
| XF_CREATETIME >= '2023-07-01 00:00:00' AND XF_CREATETIME < '2023-08-01 00:00:00' |  |
| XF_CREATETIME >= '2023-08-01 00:00:00' AND XF_CREATETIME < '2023-09-01 00:00:00' |  |
| XF_CREATETIME >= '2023-09-01 00:00:00' AND XF_CREATETIME < '2023-10-01 00:00:00' |  |
| XF_CREATETIME >= '2023-10-01 00:00:00' AND XF_CREATETIME < '2023-11-01 00:00:00' |  |
| XF_CREATETIME >= '2023-11-01 00:00:00' AND XF_CREATETIME < '2023-12-01 00:00:00' |  |
| XF_CREATETIME >= '2023-12-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME < '2024-02-01 00:00:00' |  |
| XF_CREATETIME >= '2024-02-01 00:00:00' AND XF_CREATETIME < '2024-03-01 00:00:00' |  |
| XF_CREATETIME >= '2024-03-01 00:00:00' AND XF_CREATETIME < '2024-04-01 00:00:00' |  |
| XF_CREATETIME >= '2024-04-01 00:00:00' AND XF_CREATETIME < '2024-05-01 00:00:00' |  |
| XF_CREATETIME >= '2024-05-01 00:00:00' AND XF_CREATETIME < '2024-06-01 00:00:00' |  |
| XF_CREATETIME >= '2024-06-01 00:00:00' AND XF_CREATETIME < '2024-07-01 00:00:00' |  |
| XF_CREATETIME >= '2024-07-01 00:00:00' AND XF_CREATETIME < '2024-08-01 00:00:00' |  |
| XF_CREATETIME >= '2024-08-01 00:00:00' AND XF_CREATETIME < '2024-09-01 00:00:00' |  |
| XF_CREATETIME >= '2024-09-01 00:00:00' AND XF_CREATETIME < '2024-10-01 00:00:00' |  |
| XF_CREATETIME >= '2024-10-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |  |
| 361 complete batchworks配置   362 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' | **考虑一下是否要按月来。结论: 不用,因为使用了游标,不影响查询** |  |
| 公司 | 条件及是否迁移完成 | 订单表XF_TRANSSALESTOTAL | 订单明细表XF_TRANSSALESITEM | 订单支付表XF_TRANSSALESTENDER | 备注 |
| HNHX_DATA01(海南）分表策略按**年**分表   113 complete 已配置 | XF_CREATETIME >= '2019-01-01 00:00:00' AND XF_CREATETIME < '2020-01-01 00:00:00' | 259 complete 迁移任务创建   260 complete 数据量核对  订单数据 5,995,892/5995892 6131,946/6131946 7,217,700/7217700 8,278,250/8278250 9.836,186/9836186 9,643,328/9643328 明细数据 10,687,458/10687458 11,664,624/11664624 12,702,582/12702582 15,983,914/15983914 20,197,007/20197007 19,640,046/19640046 支付数据 6,488,373/6488373 6,777,567/6777567 7,878,911/7878911 9,150,499/9150499 10,681,587/10681587 9,916,835/9916835 |  |
| XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' |  |
| 363 complete batchworks配置   364 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |
| 公司 | 条件及是否迁移完成 | 订单表XF_TRANSSALESTOTAL | 订单明细表XF_TRANSSALESITEM | 订单支付表XF_TRANSSALESTENDER | 备注 |
| CDHX_USERS(四川）分表策略按**季度**分表   114 complete 已配置 | XF_CREATETIME >= '2020-01-01 00:00:00' AND XF_CREATETIME < '2020-04-01 00:00:00' | 261 complete 迁移任务创建   262 complete 数据量核对   订单数据 3,632,948/3632948 3,739,858/3739858 3,602,338/3602338 3,891,590/3891590 3,802,116/3802116 4,171,337/4171337 4,346,875/4346875 4,496,018/4496018 4,366,329/4366329 4,265,416/4265416 4,365445/4365445 5,814,835/5814835 4,584,053/4584053 4,847,630/4847630 5,003,616/5003616 5,800,130/5800130 6,378,825/6378825 5,991849/5991849 1,279,840/1279840 明细数据 7,468,104/7468104 7,512,047/7512047 7,556,584/7556584 8,125,058/8125058 8,045,754/8045754 8,529.470/8529470 8,726,924/8726924 9,014788/9014788 8,624,543/8624543 8,576,083/8576083 8,685,804/8685804 12,132,201/12132201 8931,890/8931890 9,392,298/9392298 9,558,847/9558847 11,096,130/11096130 12,160,903/12160903 11,263,085/11263085 2,409,268/2409268 支付数据 3,935,637/3935637 4,078,738/4078738 3,977,428/3977428 4,334,375/4334375 4,223,712/4223712 4,600,800/4600800 4,728,490/4728490 4,875,024/4875024 4,714,083/4714083 4,617,238/4617238 4,713,374/4713374 6,244,516/6244516 4,957,595/4957595 5,267,416/5267416 5,407,825/5407825 6,242,248/6242248 6,687,789/6687789 6,252,090/6252090 1,330,769/1330769 |  |
| XF_CREATETIME >= '2020-04-01 00:00:00' AND XF_CREATETIME < '2020-07-01 00:00:00' |  |
| XF_CREATETIME >= '2020-07-01 00:00:00' AND XF_CREATETIME < '2020-10-01 00:00:00' |  |
| XF_CREATETIME >= '2020-10-01 00:00:00' AND XF_CREATETIME < '2021-01-01 00:00:00' |  |
| XF_CREATETIME >= '2021-01-01 00:00:00' AND XF_CREATETIME < '2021-04-01 00:00:00' |  |
| XF_CREATETIME >= '2021-04-01 00:00:00' AND XF_CREATETIME < '2021-07-01 00:00:00' |  |
| XF_CREATETIME >= '2021-07-01 00:00:00' AND XF_CREATETIME < '2021-10-01 00:00:00' |  |
| XF_CREATETIME >= '2021-10-01 00:00:00' AND XF_CREATETIME < '2022-01-01 00:00:00' |  |
| XF_CREATETIME >= '2022-01-01 00:00:00' AND XF_CREATETIME < '2022-04-01 00:00:00' |  |
| XF_CREATETIME >= '2022-04-01 00:00:00' AND XF_CREATETIME < '2022-07-01 00:00:00' |  |
| XF_CREATETIME >= '2022-07-01 00:00:00' AND XF_CREATETIME < '2022-10-01 00:00:00' |  |
| XF_CREATETIME >= '2022-10-01 00:00:00' AND XF_CREATETIME < '2023-01-01 00:00:00' |  |
| XF_CREATETIME >= '2023-01-01 00:00:00' AND XF_CREATETIME < '2023-04-01 00:00:00' |  |
| XF_CREATETIME >= '2023-04-01 00:00:00' AND XF_CREATETIME < '2023-07-01 00:00:00' |  |
| XF_CREATETIME >= '2023-07-01 00:00:00' AND XF_CREATETIME < '2023-10-01 00:00:00' |  |
| XF_CREATETIME >= '2023-10-01 00:00:00' AND XF_CREATETIME < '2024-01-01 00:00:00' |  |
| XF_CREATETIME >= '2024-01-01 00:00:00' AND XF_CREATETIME < '2024-04-01 00:00:00' |  |
| XF_CREATETIME >= '2024-04-01 00:00:00' AND XF_CREATETIME < '2024-07-01 00:00:00' |  |
| XF_CREATETIME >= '2024-07-01 00:00:00' AND XF_CREATETIME < '2024-10-01 00:00:00' |  |
| Q4单没有的原因: 四川公司POS已经切换为海典了，和之前攀枝花海典是共用一个库，所以hana里数据都是处理在SCHX_USERS里 |  |  |  |  |
| 365 complete batchworks配置,因为没有数据，就没有配置。所以所有schema任务加起来一共42个,而不是45个。   366 complete XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' |  |  |


  6 complete 迁移订单归属信息 HX_CRM.CV_STOREINFOR   7 complete 迁移会员信息HX_CRM.TB_CRM_MATSTER_N 不用迁移   8 complete 迁移商品信息 HANA_D.MAKT   9 complete 迁移支付描述 HX_SJCJ.TENDTYT   10 complete 迁移公司员工表 HANA_APP.HRMRESOURCE   13 complete 迁移返利表 XF_DBM_COUPON —— 只有云南公司使用   14 complete 迁移赠品活动表 XF_ZT_GIFTGIVING_MEMOMAP —— 只有云南公司使用  

### 基于datax实现

datax: [https://github.com/alibaba/DataX](https://github.com/alibaba/DataX)

datax下载地址: [https://datax-opensource.oss-cn-hangzhou.aliyuncs.com/202308/datax.tar.gz](https://datax-opensource.oss-cn-hangzhou.aliyuncs.com/202308/datax.tar.gz)

ngdbc包: 

截止20240606,官方开源版本不支持sap hana数据源,需要手动将sap hana的数据源添加到datax的资源库中,并使用rdbmsreader来抽取数据操作:将ngdbc-2.4.70.jar复制到/datax/datax/plugin/reader/rdbmsreader/libs/目录

### 1. 迁移订单归属信息 HX_CRM.CV_STOREINFOR

MySQL建表

trueCREATE TABLE CV_STOREINFOR (
	id INT AUTO_INCREMENT PRIMARY KEY,
    PLANT VARCHAR(80),
    PLANT_TEXT VARCHAR(255),
    COMP_CODE VARCHAR(50),
    ZC_GSJC VARCHAR(100),
    SALES_DIST VARCHAR(52),
    SALES_DIST_TEXT VARCHAR(255),
    ZC_BZIRK VARCHAR(52),
    ZC_BZIRK_TEXT VARCHAR(255),
    COMP_DQ VARCHAR(5),
    COMP_DQ_NM VARCHAR(60),
    _BIC_ZC_INPRO VARCHAR(4),
    JMTYPE VARCHAR(21),
    SPGR1 VARCHAR(2),
    MDS INT NOT NULL,
    CURRENCY VARCHAR(3),
    UNIT VARCHAR(2),
    EROED VARCHAR(8),
    COL4 VARCHAR(1),
    COL3 VARCHAR(6),
    COL5 VARCHAR(60),
    STATS INT,
    INPRO VARCHAR(4),
    PITIME DATETIME(3), -- LONGDATE converted to DATETIME
    ZTYPE VARCHAR(4),
    COL1 VARCHAR(20),
    COL2 VARCHAR(20),
    COL6 VARCHAR(60),
    ACHVM VARCHAR(1) NOT NULL,
    AREA VARCHAR(4),
    ZC_MLINE_TEXT VARCHAR(20),
    ZC_VERFL VARCHAR(6),
    RT_LCLDAT VARCHAR(8),
    RT_LOPDAT VARCHAR(8),
    ZNEWDT VARCHAR(8),
    ZC_MEDIC VARCHAR(1),
    RELOCATIONSIGN VARCHAR(1),
    ZC_POSIT1 VARCHAR(2),
    TZC_POSIT1 VARCHAR(20),
    ZPLANT3 VARCHAR(4),
    REGION VARCHAR(3),
    REGION_TEXT VARCHAR(20),
    COUNTY_CDE VARCHAR(3),
    COUNTY_TEXT VARCHAR(20),KEY `idx_plant` (`PLANT`) )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select PLANT,PLANT_TEXT,COMP_CODE,ZC_GSJC,SALES_DIST,SALES_DIST_TEXT,ZC_BZIRK,ZC_BZIRK_TEXT,COMP_DQ,COMP_DQ_NM,_BIC_ZC_INPRO,JMTYPE,SPGR1,MDS,CURRENCY,UNIT,EROED,COL4,COL3,COL5,STATS,INPRO,PITIME,ZTYPE,COL1,COL2,COL6,ACHVM,AREA,ZC_MLINE_TEXT,ZC_VERFL,RT_LCLDAT,RT_LOPDAT,ZNEWDT,ZC_MEDIC,RELOCATIONSIGN,ZC_POSIT1,TZC_POSIT1,ZPLANT3,REGION,REGION_TEXT,COUNTY_CDE,COUNTY_TEXT from HX_CRM.CV_STOREINFOR"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":1000,
            "splitPk": "PLANT"
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "PLANT",
              "PLANT_TEXT",
              "COMP_CODE",
              "ZC_GSJC",
              "SALES_DIST",
              "SALES_DIST_TEXT",
              "ZC_BZIRK",
              "ZC_BZIRK_TEXT",
              "COMP_DQ",
              "COMP_DQ_NM",
              "_BIC_ZC_INPRO",
              "JMTYPE",
              "SPGR1",
              "MDS",
              "CURRENCY",
              "UNIT",
              "EROED",
              "COL4",
              "COL3",
              "COL5",
              "STATS",
              "INPRO",
              "PITIME",
              "ZTYPE",
              "COL1",
              "COL2",
              "COL6",
              "ACHVM",
              "AREA",
              "ZC_MLINE_TEXT",
              "ZC_VERFL",
              "RT_LCLDAT",
              "RT_LOPDAT",
              "ZNEWDT",
              "ZC_MEDIC",
              "RELOCATIONSIGN",
              "ZC_POSIT1",
              "TZC_POSIT1",
              "ZPLANT3",
              "REGION",
              "REGION_TEXT",
              "COUNTY_CDE",
              "COUNTY_TEXT"
            ],
            "connection": [
              {
                "table": [
                  "CV_STOREINFOR"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}



### 2.迁移会员信息HX_CRM.TB_CRM_MATSTER_N

true CREATE TABLE `tb_crm_matster_n` (
  `id` int NOT NULL AUTO_INCREMENT,
  `GUID` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员卡ID(实物卡号)` varchar(25) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员卡号状态` varchar(5) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员卡类型` varchar(5) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员ID(会员编号)` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员资格ID` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `会员资格状态` varchar(5) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `CRM会员卡ID(虚拟卡号)` varchar(16) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `姓名` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `性别` text COLLATE utf8mb4_general_ci,
  `出生日期` varchar(8) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `身份证号` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `固定电话` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `移动电话` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `电子邮件地址` varchar(241) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `居住地址` text COLLATE utf8mb4_general_ci,
  `会员等级` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `等级值余额` decimal(15,2) DEFAULT NULL,
  `积分余额` decimal(15,2) DEFAULT NULL,
  `上月过期积分` decimal(18,2) DEFAULT NULL,
  `本月即将过期积分` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `累计交易次数` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `累计消费金额` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `归属门店` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `发行店铺号` varchar(14) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `发行日期` varchar(8) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `职业` varchar(70) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `医保类型` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `中控台创建用户号` text COLLATE utf8mb4_general_ci,
  `中控台创建用户姓名` text COLLATE utf8mb4_general_ci,
  `微信账号` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `负责员工工号` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `负责员工姓名` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ABC分类` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `支付宝绑定店铺` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `微信绑定店铺` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `支付宝绑定日期` double DEFAULT NULL,
  `微信绑定日期` double DEFAULT NULL,
  `健康档案病症首次建档门店` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `健康档案病症首次建档日期` text COLLATE utf8mb4_general_ci,
  `首次建档日期-母婴` double DEFAULT NULL,
  `首次建档门店-母婴` varchar(30) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `末次消费日期` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `累计进店次数` bigint DEFAULT NULL,
  `REG_CHANNEL` varchar(24) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `REG_CHANNEL_NAME` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `NEW_ACTIVE_VIP_YEAR` varchar(4) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `慢卡类型` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `慢卡病种` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`),   KEY `idx_member_card_no` (`会员卡ID(实物卡号)`) USING BTREE,KEY `idx_date` (`发行日期`)  USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select \"GUID\",\"会员卡ID(实物卡号)\",\"会员卡号状态\",\"会员卡类型\",\"会员ID(会员编号)\",\"会员资格ID\",\"会员资格状态\",\"CRM会员卡ID(虚拟卡号)\",\"姓名\",\"性别\",\"出生日期\",\"身份证号\",\"固定电话\",\"移动电话\",\"电子邮件地址\",\"居住地址\",\"会员等级\",\"等级值余额\",\"积分余额\",\"上月过期积分\",\"本月即将过期积分\",\"累计交易次数\",\"累计消费金额\",\"归属门店\",\"发行店铺号\",\"发行日期\",\"职业\",\"医保类型\",\"中控台创建用户号\",\"中控台创建用户姓名\",\"微信账号\",\"负责员工工号\",\"负责员工姓名\",\"ABC分类\",\"支付宝绑定店铺\",\"微信绑定店铺\",\"支付宝绑定日期\",\"微信绑定日期 \",\"健康档案病症首次建档门店\",\"健康档案病症首次建档日期\",\"首次建档日期-母婴\",\"首次建档门店-母婴\",\"末次消费日期\",\"累计进店次数\",\"REG_CHANNEL\",\"REG_CHANNEL_NAME\",\"NEW_ACTIVE_VIP_YEAR\",\"慢卡类型\",\"慢卡病种\" from HX_CRM.TB_CRM_MATSTER_N"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
             "`GUID`",
              "`会员卡ID(实物卡号)`",
              "`会员卡号状态`",
              "`会员卡类型`",
              "`会员ID(会员编号)`",
              "`会员资格ID`",
              "`会员资格状态`",
              "`CRM会员卡ID(虚拟卡号)`",
              "`姓名`",
              "`性别`",
              "`出生日期`",
              "`身份证号`",
              "`固定电话`",
              "`移动电话`",
              "`电子邮件地址`",
              "`居住地址`",
              "`会员等级`",
              "`等级值余额`",
              "`积分余额`",
              "`上月过期积分`",
              "`本月即将过期积分`",
              "`累计交易次数`",
              "`累计消费金额`",
              "`归属门店`",
              "`发行店铺号`",
              "`发行日期`",
              "`职业`",
              "`医保类型`",
              "`中控台创建用户号`",
              "`中控台创建用户姓名`",
              "`微信账号`",
              "`负责员工工号`",
              "`负责员工姓名`",
              "`ABC分类`",
              "`支付宝绑定店铺`",
              "`微信绑定店铺`",
              "`支付宝绑定日期`",
              "`微信绑定日期`",
              "`健康档案病症首次建档门店`",
              "`健康档案病症首次建档日期`",
              "`首次建档日期-母婴`",
              "`首次建档门店-母婴`",
              "`末次消费日期`",
              "`累计进店次数`",
              "`REG_CHANNEL`",
              "`REG_CHANNEL_NAME`",
              "`NEW_ACTIVE_VIP_YEAR`",
              "`慢卡类型`",
              "`慢卡病种`"
            ],
            "connection": [
              {
                "table": [
                  "tb_crm_matster_n"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}
   

### 3.迁移区域订单信息

#### 3.1迁移区域订单表 区域.XF_TRANSSALESTOTAL

建表SQL

trueCREATE TABLE XF_TRANSSALESTOTAL (
    id INT AUTO_INCREMENT PRIMARY KEY,
    XF_STORECODE VARCHAR(6),
    XF_TILLID VARCHAR(3),
    XF_TXDATE DATE,
    XF_TXSERIAL DOUBLE,
    XF_TXTIME VARCHAR(6),
    XF_TXBATCH DOUBLE,
    XF_DOCNO VARCHAR(10),
    XF_VOIDDOCNO VARCHAR(10),
    XF_TXTYPE DOUBLE,
    XF_TXHOUR DOUBLE,
    XF_CASHIER VARCHAR(10),
    XF_SALESMAN VARCHAR(10),
    XF_CLIENTCODE VARCHAR(12),
    XF_PURCHASESTAFFCODE VARCHAR(10),
    XF_PURCHASEDEPENDENT DOUBLE,
    XF_DEMOGRAPHICCODE VARCHAR(4),
    XF_DEMOGRAPHICDATA VARCHAR(4),
    XF_NETQTY DECIMAL(16, 4),
    XF_ORIGINALAMOUNT DECIMAL(16, 4),
    XF_SELLINGAMOUNT DECIMAL(16, 4),
    XF_DISCOUNTAPPROVE VARCHAR(10),
    XF_DISCOUNTAMOUNT DECIMAL(16, 4),
    XF_TTLTAXAMOUNT1 DECIMAL(16, 4),
    XF_TTLTAXAMOUNT2 DECIMAL(16, 4),
    XF_NETAMOUNT DECIMAL(16, 4),
    XF_PAIDAMOUNT DECIMAL(16, 4),
    XF_CHANGEAMOUNT DECIMAL(16, 4),
    XF_DEFAULTTENDER VARCHAR(2),
    XF_NUMOFITEM DOUBLE,
    XF_NUMOFTENDER DOUBLE,
    XF_PRICEINCLUDETAX VARCHAR(2),
    XF_SHOPTAXGROUP VARCHAR(40),
    XF_EXTENDPARAM VARCHAR(128),
    XF_DESTLOCATIONLIST VARCHAR(250),
    XF_POSTDATE VARCHAR(21),
    XF_CREATETIME VARCHAR(21),
    XF_SALESMODE VARCHAR(2),
    CRM_EXECUTED VARCHAR(1),
    CRM_EXECUTED1 VARCHAR(1),
    XF_REFFROM VARCHAR(20),
    XF_REFDOCNO VARCHAR(50),
    UNIQUE (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
  KEY `idx_order` (`XF_DOCNO`,`XF_STORECODE`),
  KEY `idx_create_time` (`XF_CREATETIME`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIER,XF_SALESMAN,XF_CLIENTCODE,XF_PURCHASESTAFFCODE,XF_PURCHASEDEPENDENT,XF_DEMOGRAPHICCODE,XF_DEMOGRAPHICDATA,XF_NETQTY,XF_ORIGINALAMOUNT,XF_SELLINGAMOUNT,XF_DISCOUNTAPPROVE,XF_DISCOUNTAMOUNT,XF_TTLTAXAMOUNT1,XF_TTLTAXAMOUNT2,XF_NETAMOUNT,XF_PAIDAMOUNT,XF_CHANGEAMOUNT,XF_DEFAULTTENDER,XF_NUMOFITEM,XF_NUMOFTENDER,XF_PRICEINCLUDETAX,XF_SHOPTAXGROUP,XF_EXTENDPARAM,XF_DESTLOCATIONLIST,XF_POSTDATE,XF_CREATETIME,XF_SALESMODE,CRM_EXECUTED,CRM_EXECUTED1,XF_REFFROM,XF_REFDOCNO from CDHX_USERS.XF_TRANSSALESTOTAL"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIER",
              "XF_SALESMAN",
              "XF_CLIENTCODE",
              "XF_PURCHASESTAFFCODE",
              "XF_PURCHASEDEPENDENT",
              "XF_DEMOGRAPHICCODE",
              "XF_DEMOGRAPHICDATA",
              "XF_NETQTY",
              "XF_ORIGINALAMOUNT",
              "XF_SELLINGAMOUNT",
              "XF_DISCOUNTAPPROVE",
              "XF_DISCOUNTAMOUNT",
              "XF_TTLTAXAMOUNT1",
              "XF_TTLTAXAMOUNT2",
              "XF_NETAMOUNT",
              "XF_PAIDAMOUNT",
              "XF_CHANGEAMOUNT",
              "XF_DEFAULTTENDER",
              "XF_NUMOFITEM",
              "XF_NUMOFTENDER",
              "XF_PRICEINCLUDETAX",
              "XF_SHOPTAXGROUP",
              "XF_EXTENDPARAM",
              "XF_DESTLOCATIONLIST",
              "XF_POSTDATE",
              "XF_CREATETIME",
              "XF_SALESMODE",
              "CRM_EXECUTED",
              "CRM_EXECUTED1",
              "XF_REFFROM",
              "XF_REFDOCNO"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalestotal"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}

#### 3.2迁移区域订单明细表 区域.XF_TRANSSALESITEM

建表SQL

trueCREATE TABLE XF_TRANSSALESITEM (
    id INT AUTO_INCREMENT PRIMARY KEY,
    XF_STORECODE VARCHAR(6),
    XF_TILLID VARCHAR(3),
    XF_TXDATE DATE,
    XF_TXSERIAL DOUBLE,
    XF_TXTIME VARCHAR(6),
    XF_TXBATCH DOUBLE,
    XF_DOCNO VARCHAR(10),
    XF_VOIDDOCNO VARCHAR(10),
    XF_TXTYPE DOUBLE,
    XF_TXHOUR DOUBLE,
    XF_CASHIER VARCHAR(10),
    XF_SALESMAN VARCHAR(10),
    XF_VIPCODE VARCHAR(12),
    XF_DEMOGRAPCODE VARCHAR(15),
    XF_DEMOGRAPDATA VARCHAR(15),
    XF_PLU VARCHAR(30),
    XF_STYLE VARCHAR(30),
    XF_COLOR VARCHAR(6),
    XF_SIZE VARCHAR(14),
    XF_ITEMLOTNUM VARCHAR(30),
    XF_QTYSOLD DECIMAL(16, 4),
    XF_AMTSOLD DECIMAL(16, 4),
    XF_COSTSOLD DECIMAL(18, 6),
    XF_MARKDOWNAMT DECIMAL(16, 4),
    XF_DISCOUNTAMT DECIMAL(16, 4),
    XF_PROMOTIONAMT DECIMAL(16, 4),
    XF_TAXAMOUNT1 DECIMAL(16, 4),
    XF_TAXAMOUNT2 DECIMAL(16, 4),
    XF_TAXRATE1 DECIMAL(16, 4),
    XF_TAXRATE2 DECIMAL(16, 4),
    XF_EXSTK2SALES DECIMAL(12, 4),
    XF_ORGUPRICE DECIMAL(16, 4),
    XF_ISDEPOSIT VARCHAR(1),
    XF_ISWHOLESALE VARCHAR(1),
    XF_ISPRICEALTERNATE VARCHAR(1),
    XF_ISPRICEOVERRIDE VARCHAR(1),
    XF_ISNEWITEM VARCHAR(1),
    XF_PRICEAPPROVE VARCHAR(10),
    XF_COUPONNUMBER VARCHAR(12),
    XF_DISCOUNTAPPROVE VARCHAR(10),
    XF_ITEMDISCOUNTAMT DECIMAL(16, 4),
    XF_TTLDISCOUNTLESS DECIMAL(16, 4),
    XF_PROMID1 VARCHAR(6),
    XF_PROMAMT1 DECIMAL(16, 4),
    XF_PROMQTY1 DECIMAL(16, 4),
    XF_PROMID2 VARCHAR(6),
    XF_PROMAMT2 DECIMAL(16, 4),
    XF_PROMQTY2 DECIMAL(16, 4),
    XF_PROMID3 VARCHAR(6),
    XF_PROMAMT3 DECIMAL(16, 4),
    XF_PROMQTY3 DECIMAL(16, 4),
    XF_PROMID4 VARCHAR(6),
    XF_PROMAMT4 DECIMAL(16, 4),
    XF_PROMQTY4 DECIMAL(16, 4),
    XF_PROMID5 VARCHAR(6),
    XF_PROMAMT5 DECIMAL(16, 4),
    XF_PROMQTY5 DECIMAL(16, 4),
    XF_SALESITEMREMARK VARCHAR(80),
    XF_EXTENDPARAM VARCHAR(250),
    XF_DESTLOCATIONLIST VARCHAR(250),
    XF_PRICECENTER VARCHAR(6),
    XF_COSTCENTER VARCHAR(6),
    XF_POSTDATE VARCHAR(21),
    XF_CREATETIME VARCHAR(21),
    XF_ISPOSTING VARCHAR(1),
    CRM_EXECUTED VARCHAR(1),
    CRM_EXECUTED1 VARCHAR(1),
    XF_HXALLDISCLESS1 DECIMAL(16, 4),
    UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
KEY `idx_docNo_item` (`XF_STORECODE`,`XF_DOCNO`,`XF_TXDATE`,`XF_TILLID`),
                                    KEY `idx_create_time` (`XF_CREATETIME`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIER,XF_SALESMAN,XF_VIPCODE,XF_DEMOGRAPCODE,XF_DEMOGRAPDATA,XF_PLU,XF_STYLE,XF_COLOR,XF_SIZE,XF_ITEMLOTNUM,XF_QTYSOLD,XF_AMTSOLD,XF_COSTSOLD,XF_MARKDOWNAMT,XF_DISCOUNTAMT,XF_PROMOTIONAMT,XF_TAXAMOUNT1,XF_TAXAMOUNT2,XF_TAXRATE1,XF_TAXRATE2,XF_EXSTK2SALES,XF_ORGUPRICE,XF_ISDEPOSIT,XF_ISWHOLESALE,XF_ISPRICEALTERNATE,XF_ISPRICEOVERRIDE,XF_ISNEWITEM,XF_PRICEAPPROVE,XF_COUPONNUMBER,XF_DISCOUNTAPPROVE,XF_ITEMDISCOUNTAMT,XF_TTLDISCOUNTLESS,XF_PROMID1,XF_PROMAMT1,XF_PROMQTY1,XF_PROMID2,XF_PROMAMT2,XF_PROMQTY2,XF_PROMID3,XF_PROMAMT3,XF_PROMQTY3,XF_PROMID4,XF_PROMAMT4,XF_PROMQTY4,XF_PROMID5,XF_PROMAMT5,XF_PROMQTY5,XF_SALESITEMREMARK,XF_EXTENDPARAM,XF_DESTLOCATIONLIST,XF_PRICECENTER,XF_COSTCENTER,XF_POSTDATE,XF_CREATETIME,XF_ISPOSTING,CRM_EXECUTED,CRM_EXECUTED1,XF_HXALLDISCLESS1 from CDHX_USERS.XF_TRANSSALESITEM"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIER",
              "XF_SALESMAN",
              "XF_VIPCODE",
              "XF_DEMOGRAPCODE",
              "XF_DEMOGRAPDATA",
              "XF_PLU",
              "XF_STYLE",
              "XF_COLOR",
              "XF_SIZE",
              "XF_ITEMLOTNUM",
              "XF_QTYSOLD",
              "XF_AMTSOLD",
              "XF_COSTSOLD",
              "XF_MARKDOWNAMT",
              "XF_DISCOUNTAMT",
              "XF_PROMOTIONAMT",
              "XF_TAXAMOUNT1",
              "XF_TAXAMOUNT2",
              "XF_TAXRATE1",
              "XF_TAXRATE2",
              "XF_EXSTK2SALES",
              "XF_ORGUPRICE",
              "XF_ISDEPOSIT",
              "XF_ISWHOLESALE",
              "XF_ISPRICEALTERNATE",
              "XF_ISPRICEOVERRIDE",
              "XF_ISNEWITEM",
              "XF_PRICEAPPROVE",
              "XF_COUPONNUMBER",
              "XF_DISCOUNTAPPROVE",
              "XF_ITEMDISCOUNTAMT",
              "XF_TTLDISCOUNTLESS",
              "XF_PROMID1",
              "XF_PROMAMT1",
              "XF_PROMQTY1",
              "XF_PROMID2",
              "XF_PROMAMT2",
              "XF_PROMQTY2",
              "XF_PROMID3",
              "XF_PROMAMT3",
              "XF_PROMQTY3",
              "XF_PROMID4",
              "XF_PROMAMT4",
              "XF_PROMQTY4",
              "XF_PROMID5",
              "XF_PROMAMT5",
              "XF_PROMQTY5",
              "XF_SALESITEMREMARK",
              "XF_EXTENDPARAM",
              "XF_DESTLOCATIONLIST",
              "XF_PRICECENTER",
              "XF_COSTCENTER",
              "XF_POSTDATE",
              "XF_CREATETIME",
              "XF_ISPOSTING",
              "CRM_EXECUTED",
              "CRM_EXECUTED1",
              "XF_HXALLDISCLESS1"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalesitem"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}
  

#### 3.3迁移区域订单支付表 区域.XF_TRANSSALESTENDER

建表SQL

trueCREATE TABLE XF_TRANSSALESTENDER (
    id INT AUTO_INCREMENT PRIMARY KEY,
    XF_STORECODE VARCHAR(6),
    XF_TILLID VARCHAR(3),
    XF_TXDATE DATE,
    XF_TXSERIAL DOUBLE,
    XF_POSTDATE VARCHAR(21),
    XF_TXTIME VARCHAR(6),
    XF_TXBATCH DOUBLE,
    XF_DOCNO VARCHAR(10),
    XF_VOIDDOCNO VARCHAR(10),
    XF_TXTYPE DOUBLE,
    XF_TXHOUR DOUBLE,
    XF_CASHIERCODE VARCHAR(10),
    XF_TENDERCODE VARCHAR(2),
    XF_SPECIFICEDTYPE DOUBLE,
    XF_PAYAMOUNT DECIMAL(16, 4),
    XF_BASEAMOUNT DECIMAL(16, 4),
    XF_EXTENDPARAM VARCHAR(250),
    XF_CREATETIME VARCHAR(21),
    XF_DESTLOCATIONLIST VARCHAR(250),
    XF_EXCESSMONEY DECIMAL(16, 4),
    CRM_EXECUTED VARCHAR(1),
    CRM_EXECUTED1 VARCHAR(1),
    UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
  KEY `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`),
  KEY `idx_tendercode` (`XF_TENDERCODE`),
                                    KEY `idx_create_time` (`XF_CREATETIME`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_POSTDATE,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIERCODE,XF_TENDERCODE,XF_SPECIFICEDTYPE,XF_PAYAMOUNT,XF_BASEAMOUNT,XF_EXTENDPARAM,XF_CREATETIME,XF_DESTLOCATIONLIST,XF_EXCESSMONEY,CRM_EXECUTED,CRM_EXECUTED1 from CDHX_USERS.XF_TRANSSALESTENDER"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
             "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_POSTDATE",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIERCODE",
              "XF_TENDERCODE",
              "XF_SPECIFICEDTYPE",
              "XF_PAYAMOUNT",
              "XF_BASEAMOUNT",
              "XF_EXTENDPARAM",
              "XF_CREATETIME",
              "XF_DESTLOCATIONLIST",
              "XF_EXCESSMONEY",
              "CRM_EXECUTED",
              "CRM_EXECUTED1"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalestender"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}
  

### 4. 迁移商品信息 HANA_D.MAKT

建表语句

trueCREATE TABLE MAKT (
		id INT AUTO_INCREMENT PRIMARY KEY,
    `MANDT` VARCHAR(3) NOT NULL COMMENT '客户端',
`MATNR` VARCHAR(18) NOT NULL COMMENT '商品编码',
`SPRAS` VARCHAR(1) NOT NULL COMMENT '语言代码',
`MAKTX` VARCHAR(100),
`MAKTG` VARCHAR(40) COMMENT '有关匹配码的大写的商品描述',
`REGBD` VARCHAR(60) COMMENT '注册商标',
`CURRN` VARCHAR(60) COMMENT '通用名',
`ANAME` VARCHAR(100) COMMENT '商品全称',
`CONFD` VARCHAR(80) COMMENT '批准文号',
`EXECS` VARCHAR(60) COMMENT '执行标准',
`ZBCAT` VARCHAR(1) COMMENT '国家基本用药目录',
`ZCOMC` VARCHAR(1) COMMENT '全局商品角色',
`ZFOEM` VARCHAR(1) COMMENT '全局商品经营属性',
`ZGLOA` VARCHAR(1) COMMENT '全局总代',
`ZPRES` VARCHAR(2) COMMENT '特殊赠品标识',
`ZMEB` VARCHAR(2) COMMENT '电商商品标识',
`ZOTO` VARCHAR(2) COMMENT '门店代售品种',
`ZBRGEW` INT COMMENT '电商毛重(G)',
`ZPROCESS` VARCHAR(2) COMMENT '需加工',
`ZCYCLE` INT COMMENT '用药周期（天）',
`ZSUPCODE` VARCHAR(1) COMMENT '电子监管码标识',
`ZCURING` VARCHAR(1) COMMENT '养护属性',
`ZINDUSTRY` VARCHAR(2) COMMENT '工业板块产品',
`ZMANAG` VARCHAR(10),
`ZMILK` VARCHAR(10),
`ZTTCURING` VARCHAR(1) COMMENT 'TT商品养护标识',
`ZUNIT` VARCHAR(60) COMMENT '单位（M-learning专用）',
`ZBEHV` VARCHAR(60) COMMENT '包装（M-learning专用）',
`ZGROE` VARCHAR(60) COMMENT '规格（M-learning专用）',
`ZPLI` VARCHAR(50),
`ZJZKP` VARCHAR(1) COMMENT '禁止开票',
`SSBM` VARCHAR(19) COMMENT '税收编码',
`ZUNIT_GTAX` VARCHAR(4) COMMENT '单位(金税发票）',
`TAXRATE` VARCHAR(10) COMMENT '税率',
`YHZCBS` VARCHAR(1) COMMENT '优惠政策标识',
`ZZSTSGLCODE` VARCHAR(1) COMMENT '增值税特殊管理代码',
`LSLBS` VARCHAR(1) COMMENT '零税率标识',
`YHZCMC` VARCHAR(1000),
`ZBASIC_CODE` VARCHAR(14) COMMENT '药品本位码',
`ZLIST_HOLDING` VARCHAR(10) COMMENT '上市许可持有人',
`ZPFKL_DL` INT COMMENT '1g颗粒相当于饮片量(g)',
`ZPFKL_ZDL` INT COMMENT '1瓶(袋)相当于饮片总量(g)',
`ZJDA_SAISO` VARCHAR(2) COMMENT '季节属性',
`ZJDA_AGE` VARCHAR(2) COMMENT '目标客户年龄段',
`ZJDA_SEX` VARCHAR(2) COMMENT '目标客户性别',
`ZJDA_PLGTP` VARCHAR(2) COMMENT '价格带',
`ZJDA_PROFIT` VARCHAR(2) COMMENT '毛利带',
`ZJDA_HAVE` VARCHAR(2) COMMENT '自有品牌',
`ZJDA_UNAME` VARCHAR(12) COMMENT '执行人',
`ZJDA_DATE` VARCHAR(8) COMMENT '执行日期',
`ZJDA_TIME` VARCHAR(14) COMMENT '执行时间',
`ZMIN_UNIT` VARCHAR(10) COMMENT '最小包装单位',
`ZMIN_COUNT` INT COMMENT '最小销售数量',
UNIQUE KEY (MANDT,MATNR,SPRAS),
  KEY `idx_matnr` (`MATNR`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select MANDT,MATNR,SPRAS,MAKTX,MAKTG,REGBD,CURRN,ANAME,CONFD,EXECS,ZBCAT,ZCOMC,ZFOEM,ZGLOA,ZPRES,ZMEB,ZOTO,ZBRGEW,ZPROCESS,ZCYCLE,ZSUPCODE,ZCURING,ZINDUSTRY,ZMANAG,ZMILK,ZTTCURING,ZUNIT,ZBEHV,ZGROE,ZPLI,ZJZKP,SSBM,ZUNIT_GTAX,TAXRATE,YHZCBS,ZZSTSGLCODE,LSLBS,YHZCMC,ZBASIC_CODE,ZLIST_HOLDING,ZPFKL_DL,ZPFKL_ZDL,ZJDA_SAISO,ZJDA_AGE,ZJDA_SEX,ZJDA_PLGTP,ZJDA_PROFIT,ZJDA_HAVE,ZJDA_UNAME,ZJDA_DATE,ZJDA_TIME,ZMIN_UNIT,ZMIN_COUNT from HANA_D.MAKT"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "MANDT",
              "MATNR",
              "SPRAS",
              "MAKTX",
              "MAKTG",
              "REGBD",
              "CURRN",
              "ANAME",
              "CONFD",
              "EXECS",
              "ZBCAT",
              "ZCOMC",
              "ZFOEM",
              "ZGLOA",
              "ZPRES",
              "ZMEB",
              "ZOTO",
              "ZBRGEW",
              "ZPROCESS",
              "ZCYCLE",
              "ZSUPCODE",
              "ZCURING",
              "ZINDUSTRY",
              "ZMANAG",
              "ZMILK",
              "ZTTCURING",
              "ZUNIT",
              "ZBEHV",
              "ZGROE",
              "ZPLI",
              "ZJZKP",
              "SSBM",
              "ZUNIT_GTAX",
              "TAXRATE",
              "YHZCBS",
              "ZZSTSGLCODE",
              "LSLBS",
              "YHZCMC",
              "ZBASIC_CODE",
              "ZLIST_HOLDING",
              "ZPFKL_DL",
              "ZPFKL_ZDL",
              "ZJDA_SAISO",
              "ZJDA_AGE",
              "ZJDA_SEX",
              "ZJDA_PLGTP",
              "ZJDA_PROFIT",
              "ZJDA_HAVE",
              "ZJDA_UNAME",
              "ZJDA_DATE",
              "ZJDA_TIME",
              "ZMIN_UNIT",
              "ZMIN_COUNT"
            ],
            "connection": [
              {
                "table": [
                  "makt"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}
  

### 5. 迁移支付描述 HX_SJCJ.TENDTYT

建表语句

true
CREATE TABLE TENDTYT (
		id INT AUTO_INCREMENT PRIMARY KEY,
	  MANDT VARCHAR(9),
	 PROFILETYPE VARCHAR(12),
	 TENDERTYPECODE VARCHAR(12),
	 SPRAS VARCHAR(3),
	 DESCRIPTION VARCHAR(150),
	 KEY `idx_c_s` (`TENDERTYPECODE`,`SPRAS`)
 ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
 
 

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select MANDT,PROFILETYPE,TENDERTYPECODE,SPRAS,DESCRIPTION from HX_SJCJ.TENDTYT"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "MANDT",
              "PROFILETYPE",
              "TENDERTYPECODE",
              "SPRAS",
              "DESCRIPTION"
            ],
            "connection": [
              {
                "table": [
                  "tendtyt"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}



### 6. 迁移公司员工表 HANA_APP.HRMRESOURCE

建表语句

trueCREATE TABLE HRMRESOURCE (
	self_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自定义的自增id(yxt_order)',
  ID DECIMAL(28, 0) NOT NULL,
  LOGINID VARCHAR(60),
  PASSWORD VARCHAR(100),
  LASTNAME VARCHAR(60),
  SEX VARCHAR(1),
  BIRTHDAY VARCHAR(10),
  NATIONALITY DECIMAL(28, 0),
  SYSTEMLANGUAGE DECIMAL(28, 0),
  MARITALSTATUS VARCHAR(1),
  TELEPHONE VARCHAR(60),
  MOBILE VARCHAR(60),
  MOBILECALL VARCHAR(60),
  EMAIL VARCHAR(60),
  LOCATIONID DECIMAL(28, 0),
  WORKROOM VARCHAR(1000),
  HOMEADDRESS VARCHAR(100),
  RESOURCETYPE VARCHAR(1),
  STARTDATE VARCHAR(10),
  ENDDATE VARCHAR(10),
  JOBTITLE DECIMAL(28, 0),
  JOBACTIVITYDESC VARCHAR(200),
  JOBLEVEL DECIMAL(28, 0),
  SECLEVEL DECIMAL(28, 0),
  DEPARTMENTID DECIMAL(28, 0),
  SUBCOMPANYID1 DECIMAL(28, 0),
  COSTCENTERID DECIMAL(28, 0),
  MANAGERID DECIMAL(28, 0),
  ASSISTANTID DECIMAL(28, 0),
  BANKID1 DECIMAL(28, 0),
  ACCOUNTID1 VARCHAR(100),
  RESOURCEIMAGEID DECIMAL(28, 0),
  CREATERID DECIMAL(28, 0),
  CREATEDATE VARCHAR(10),
  LASTMODID DECIMAL(28, 0),
  LASTMODDATE VARCHAR(10),
  LASTLOGINDATE VARCHAR(10),
  DATEFIELD1 VARCHAR(10),
  DATEFIELD2 VARCHAR(10),
  DATEFIELD3 VARCHAR(10),
  DATEFIELD4 VARCHAR(10),
  DATEFIELD5 VARCHAR(10),
  NUMBERFIELD1 DOUBLE,
  NUMBERFIELD2 DOUBLE,
  NUMBERFIELD3 DOUBLE,
  NUMBERFIELD4 DOUBLE,
  NUMBERFIELD5 DOUBLE,
  TEXTFIELD1 VARCHAR(100),
  TEXTFIELD2 VARCHAR(100),
  TEXTFIELD3 VARCHAR(100),
  TEXTFIELD4 VARCHAR(100),
  TEXTFIELD5 VARCHAR(100),
  TINYINTFIELD1 DECIMAL(28, 0),
  TINYINTFIELD2 DECIMAL(28, 0),
  TINYINTFIELD3 DECIMAL(28, 0),
  TINYINTFIELD4 DECIMAL(28, 0),
  TINYINTFIELD5 DECIMAL(28, 0),
  CERTIFICATENUM VARCHAR(60),
  NATIVEPLACE VARCHAR(200),
  EDUCATIONLEVEL DECIMAL(28, 0),
  BEMEMBERDATE VARCHAR(10),
  BEPARTYDATE VARCHAR(10),
  WORKCODE VARCHAR(60),
  REGRESIDENTPLACE VARCHAR(200),
  HEALTHINFO VARCHAR(1),
  RESIDENTPLACE VARCHAR(200),
  POLICY VARCHAR(30),
  DEGREE VARCHAR(30),
  HEIGHT VARCHAR(10),
  USEKIND DECIMAL(28, 0),
  JOBCALL DECIMAL(28, 0),
  ACCUMFUNDACCOUNT VARCHAR(30),
  BIRTHPLACE VARCHAR(60),
  FOLK VARCHAR(30),
  RESIDENTPHONE VARCHAR(60),
  RESIDENTPOSTCODE VARCHAR(60),
  EXTPHONE VARCHAR(50),
  MANAGERSTR VARCHAR(500),
  STATUS DECIMAL(28, 0),
  FAX VARCHAR(120),
  ISLABOUUNION VARCHAR(1),
  WEIGHT DECIMAL(28, 0),
  TEMPRESIDENTNUMBER VARCHAR(60),
  PROBATIONENDDATE VARCHAR(10),
  COUNTRYID DECIMAL(28, 0),
  PASSWDCHGDATE VARCHAR(10),
  NEEDUSB DECIMAL(28, 0),
  SERIAL VARCHAR(32),
  ACCOUNT VARCHAR(60),
  LLOGINID VARCHAR(60),
  NEEDDYNAPASS DECIMAL(28, 0),
  DSPORDER DOUBLE,
  PASSWORDSTATE DECIMAL(28, 0),
  ACCOUNTTYPE DECIMAL(28, 0),
  BELONGTO DECIMAL(28, 0),
  DACTYLOGRAM text,
  ASSISTANTDACTYLOGRAM text,
  PASSWORDLOCK DECIMAL(28, 0),
  SUMPASSWORDWRONG DECIMAL(28, 0),
  OLDPASSWORD1 VARCHAR(100),
  OLDPASSWORD2 VARCHAR(100),
  MSGSTYLE VARCHAR(20),
  MESSAGERURL VARCHAR(100),
  PINYINLASTNAME VARCHAR(50),
  TOKENKEY VARCHAR(100),
  USERUSBTYPE VARCHAR(10),
  BEGDA VARCHAR(100),
  ENDDA VARCHAR(100),
  AEDTM VARCHAR(100),
  BUKRS VARCHAR(100),
  KOSTL VARCHAR(100),
  ORGEH VARCHAR(100),
  WERKS VARCHAR(100),
  NAME1 VARCHAR(100),
  PERSG VARCHAR(100),
  PTEXT VARCHAR(100),
  PERSK VARCHAR(100),
  PTEXT1 VARCHAR(100),
  BTRTL VARCHAR(100),
  BTRTL1 VARCHAR(100),
  ABKRS VARCHAR(100),
  ABKTX VARCHAR(100),
  INSTI VARCHAR(100),
  NMF01 VARCHAR(100),
  NMF02 VARCHAR(100),
  PLANS VARCHAR(8),
  STEXT VARCHAR(80),
  ISCORRECTED VARCHAR(1),
  ISINSERT VARCHAR(1),
  ADSJGS VARCHAR(500),
  ADGS VARCHAR(500),
  ADBM VARCHAR(500),
  ZZHRPOSTE VARCHAR(60),
  LTEXT VARCHAR(200),
  HXOA_INSERTTIME DATETIME,
  HXOA_UPDATETIME DATETIME,
  UNIQUE KEY (ID),
  KEY `idx_login_id` (`LOGINID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "HANA_CD",
            "password": "Pas$w0rd@01",
            "connection": [
              {
                "querySql": [
                  "select ID,LOGINID,PASSWORD,LASTNAME,SEX,BIRTHDAY,NATIONALITY,SYSTEMLANGUAGE,MARITALSTATUS,TELEPHONE,MOBILE,MOBILECALL,EMAIL,LOCATIONID,WORKROOM,HOMEADDRESS,RESOURCETYPE,STARTDATE,ENDDATE,JOBTITLE,JOBACTIVITYDESC,JOBLEVEL,SECLEVEL,DEPARTMENTID,SUBCOMPANYID1,COSTCENTERID,MANAGERID,ASSISTANTID,BANKID1,ACCOUNTID1,RESOURCEIMAGEID,CREATERID,CREATEDATE,LASTMODID,LASTMODDATE,LASTLOGINDATE,DATEFIELD1,DATEFIELD2,DATEFIELD3,DATEFIELD4,DATEFIELD5,NUMBERFIELD1,NUMBERFIELD2,NUMBERFIELD3,NUMBERFIELD4,NUMBERFIELD5,TEXTFIELD1,TEXTFIELD2,TEXTFIELD3,TEXTFIELD4,TEXTFIELD5,TINYINTFIELD1,TINYINTFIELD2,TINYINTFIELD3,TINYINTFIELD4,TINYINTFIELD5,CERTIFICATENUM,NATIVEPLACE,EDUCATIONLEVEL,BEMEMBERDATE,BEPARTYDATE,WORKCODE,REGRESIDENTPLACE,HEALTHINFO,RESIDENTPLACE,POLICY,DEGREE,HEIGHT,USEKIND,JOBCALL,ACCUMFUNDACCOUNT,BIRTHPLACE,FOLK,RESIDENTPHONE,RESIDENTPOSTCODE,EXTPHONE,MANAGERSTR,STATUS,FAX,ISLABOUUNION,WEIGHT,TEMPRESIDENTNUMBER,PROBATIONENDDATE,COUNTRYID,PASSWDCHGDATE,NEEDUSB,SERIAL,ACCOUNT,LLOGINID,NEEDDYNAPASS,DSPORDER,PASSWORDSTATE,ACCOUNTTYPE,BELONGTO,DACTYLOGRAM,ASSISTANTDACTYLOGRAM,PASSWORDLOCK,SUMPASSWORDWRONG,OLDPASSWORD1,OLDPASSWORD2,MSGSTYLE,MESSAGERURL,PINYINLASTNAME,TOKENKEY,USERUSBTYPE,BEGDA,ENDDA,AEDTM,BUKRS,KOSTL,ORGEH,WERKS,NAME1,PERSG,PTEXT,PERSK,PTEXT1,BTRTL,BTRTL1,ABKRS,ABKTX,INSTI,NMF01,NMF02,PLANS,STEXT,ISCORRECTED,ISINSERT,ADSJGS,ADGS,ADBM,ZZHRPOSTE,LTEXT,HXOA_INSERTTIME,HXOA_UPDATETIME from HANA_APP.HRMRESOURCE"
                ],
                "jdbcUrl": [
                  "**************************"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "agent",
            "password": "FgRdxNn8ADFC",
            "column": [
              "ID",
              "LOGINID",
              "PASSWORD",
              "LASTNAME",
              "SEX",
              "BIRTHDAY",
              "NATIONALITY",
              "SYSTEMLANGUAGE",
              "MARITALSTATUS",
              "TELEPHONE",
              "MOBILE",
              "MOBILECALL",
              "EMAIL",
              "LOCATIONID",
              "WORKROOM",
              "HOMEADDRESS",
              "RESOURCETYPE",
              "STARTDATE",
              "ENDDATE",
              "JOBTITLE",
              "JOBACTIVITYDESC",
              "JOBLEVEL",
              "SECLEVEL",
              "DEPARTMENTID",
              "SUBCOMPANYID1",
              "COSTCENTERID",
              "MANAGERID",
              "ASSISTANTID",
              "BANKID1",
              "ACCOUNTID1",
              "RESOURCEIMAGEID",
              "CREATERID",
              "CREATEDATE",
              "LASTMODID",
              "LASTMODDATE",
              "LASTLOGINDATE",
              "DATEFIELD1",
              "DATEFIELD2",
              "DATEFIELD3",
              "DATEFIELD4",
              "DATEFIELD5",
              "NUMBERFIELD1",
              "NUMBERFIELD2",
              "NUMBERFIELD3",
              "NUMBERFIELD4",
              "NUMBERFIELD5",
              "TEXTFIELD1",
              "TEXTFIELD2",
              "TEXTFIELD3",
              "TEXTFIELD4",
              "TEXTFIELD5",
              "TINYINTFIELD1",
              "TINYINTFIELD2",
              "TINYINTFIELD3",
              "TINYINTFIELD4",
              "TINYINTFIELD5",
              "CERTIFICATENUM",
              "NATIVEPLACE",
              "EDUCATIONLEVEL",
              "BEMEMBERDATE",
              "BEPARTYDATE",
              "WORKCODE",
              "REGRESIDENTPLACE",
              "HEALTHINFO",
              "RESIDENTPLACE",
              "POLICY",
              "DEGREE",
              "HEIGHT",
              "USEKIND",
              "JOBCALL",
              "ACCUMFUNDACCOUNT",
              "BIRTHPLACE",
              "FOLK",
              "RESIDENTPHONE",
              "RESIDENTPOSTCODE",
              "EXTPHONE",
              "MANAGERSTR",
              "STATUS",
              "FAX",
              "ISLABOUUNION",
              "WEIGHT",
              "TEMPRESIDENTNUMBER",
              "PROBATIONENDDATE",
              "COUNTRYID",
              "PASSWDCHGDATE",
              "NEEDUSB",
              "SERIAL",
              "ACCOUNT",
              "LLOGINID",
              "NEEDDYNAPASS",
              "DSPORDER",
              "PASSWORDSTATE",
              "ACCOUNTTYPE",
              "BELONGTO",
              "DACTYLOGRAM",
              "ASSISTANTDACTYLOGRAM",
              "PASSWORDLOCK",
              "SUMPASSWORDWRONG",
              "OLDPASSWORD1",
              "OLDPASSWORD2",
              "MSGSTYLE",
              "MESSAGERURL",
              "PINYINLASTNAME",
              "TOKENKEY",
              "USERUSBTYPE",
              "BEGDA",
              "ENDDA",
              "AEDTM",
              "BUKRS",
              "KOSTL",
              "ORGEH",
              "WERKS",
              "NAME1",
              "PERSG",
              "PTEXT",
              "PERSK",
              "PTEXT1",
              "BTRTL",
              "BTRTL1",
              "ABKRS",
              "ABKTX",
              "INSTI",
              "NMF01",
              "NMF02",
              "PLANS",
              "STEXT",
              "ISCORRECTED",
              "ISINSERT",
              "ADSJGS",
              "ADGS",
              "ADBM",
              "ZZHRPOSTE",
              "LTEXT",
              "HXOA_INSERTTIME",
              "HXOA_UPDATETIME"
            ],
            "connection": [
              {
                "table": [
                  "hrmresource"
                ],
                "jdbcUrl": "*****************************************************************************************************************************************************"
              }
            ]
          }
        }
      }
    ]
  }
}



### 7. 迁移返利对应表xf_dbm_coupon——只有云南公司使用

> 不需要添加时间,半年内新功能

建表语句

true-- 只有云南公司启用了 YNHX_DATA01
CREATE TABLE XF_DBM_COUPON_${schema} (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `XF_STORECODE` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_TILLID` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_TXDATE` VARCHAR(21) NOT NULL DEFAULT '',
                                 `XF_TXTIME` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_DOCNO` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_DBMID` VARCHAR(90) NOT NULL DEFAULT '',
                                 `XF_DESCI` VARCHAR(300) NOT NULL DEFAULT '',
                                 `XF_REBATEAMT` DECIMAL(16,4) NOT NULL DEFAULT 0,
                                 `XF_EXPIRETIME` VARCHAR(20) NOT NULL DEFAULT '',
                                 `XF_STATUS` VARCHAR(3) NOT NULL DEFAULT '',
                                 `XF_USEDDATE` VARCHAR(21) NOT NULL DEFAULT '',
                                 `XF_USEDTIME` VARCHAR(10) NOT NULL DEFAULT '',
                                 `XF_USEDDOCNO` VARCHAR(10) NOT NULL DEFAULT '',
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY (`XF_STORECODE`, `XF_TILLID`, `XF_TXDATE`, `XF_TXTIME`, `XF_DOCNO`),
                                 KEY `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`) USING BTREE,
                                 KEY `idx_usedDocNo_storeCode` (`XF_USEDDOCNO`,`XF_STORECODE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXTIME,XF_DOCNO,XF_DBMID,XF_DESCI,XF_REBATEAMT ,XF_EXPIRETIME ,XF_STATUS,XF_USEDDATE,XF_USEDTIME,XF_USEDDOCNO from ${schema}.xf_dbm_coupon"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXTIME",
              "XF_DOCNO",
              "XF_DBMID",
              "XF_DESCI",
              "XF_REBATEAMT",
              "XF_EXPIRETIME",
              "XF_STATUS",
              "XF_USEDDATE",
              "XF_USEDTIME",
              "XF_USEDDOCNO"
            ],
            "connection": [
              {
                "table": [
                  "xf_dbm_coupon_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ]
          }
        }
      }
    ]
  }
}

### 8.迁移赠品活动表XF_ZT_GIFTGIVING_MEMOMAP——只有云南公司使用

> 不需要添加时间,半年内新功能

建表语句

true-- 只有云南公司启用了 YNHX_DATA01
CREATE TABLE XF_ZT_GIFTGIVING_MEMOMAP_${schema} (
                                            `id` int NOT NULL AUTO_INCREMENT,
                                            `XF_STORECODE` VARCHAR(10),
                                            `XF_TILLID` VARCHAR(3),
                                            `XF_TXDATE` DATE,
                                            `XF_TXTIME` VARCHAR(6),
                                            `XF_DOCNO` VARCHAR(10),
                                            `XF_GEDOCNO` VARCHAR(10),
                                            `XF_GIFTEVENTID` VARCHAR(30),
                                            `XF_GIFTEVENTAMT` DECIMAL(12,4),
                                            `XF_TTLGIFTAMT` DECIMAL(12,4),
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY (`XF_STORECODE`, `XF_TILLID`, `XF_TXDATE`, `XF_TXTIME`, `XF_DOCNO`),
                                            KEY `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`) USING BTREE,
                                            KEY `idx_geDocNo_storeCode` (`XF_GEDOCNO`,`XF_STORECODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

迁移脚本

true{
  "job": {
    "setting": {
      "speed": {
        "channel": 3
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXTIME,XF_DOCNO,XF_GEDOCNO,XF_GIFTEVENTID,XF_GIFTEVENTAMT,XF_TTLGIFTAMT from ${schema}.XF_ZT_GIFTGIVING_MEMOMAP"
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize":10000
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXTIME",
              "XF_DOCNO",
              "XF_GEDOCNO",
              "XF_GIFTEVENTID",
              "XF_GIFTEVENTAMT",
              "XF_TTLGIFTAMT"
            ],
            "connection": [
              {
                "table": [
                  "xf_zt_giftgiving_memomap_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ]
          }
        }
      }
    ]
  }
}

### 执行脚本

进入到datax的bin目录:

true# 测试使用
python3 datax.py json配置文件

# 生产使用,日志文件要确保目录存在,日志文件需要根据任务的不同来修改命名
nohup python python3 datax.py json配置文件 > /migration/datax/logfile.log 2>&1 &