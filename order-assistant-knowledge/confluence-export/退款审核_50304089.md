# 退款审核

## 部分退款字段更新梳理

|  |  |  |  | 需要的参数 | 更新的表以及字段 |
| --- | --- | --- | --- | --- | --- |
| setPartRefundCommissionEBai() |  |  |  |  |  |
| setEBAIPartRefundCommission() 设置饿百佣金以及佣金计算标识 |  |  | OrderInfo、RefundOrder、OrderPayInfo、otheRefundOrderList | - OrderPayInfo.remainBrokerageAmount - // 新佣金 = 订单上的剩余佣金 - 饿百获取的剩余佣金 + 原退款单原佣金 - RefundOrder.setFeeRefund(omsRemainCommission.subtract(eBaiRemainCommission).add(refundOrder.getFeeRefund())); RefundOrder.setShopRefund(refundOrder.getShopRefund().subtract(ro.getFeeRefund())); - (RefundOrder::getState, RefundStateEnum.SUCCESS.getCode()).eq(RefundOrder::getType, RefundTypeEnum.PART.getCode()).eq(RefundOrder::getEbaiCommissionFlag, DsConstants.INTEGER_ZERO).set(ebai_commission_flag = 1) |
| refreshRefundSinkAmount部分退款的刷新金额 |  |  |  |  |
|  | calculateRefundAmountWithConfig根据下账配置计算退款下账金额 |  | RefundOrder、ErpRefundInfo、StoreBillConfig | 根据下账配置，依据refundOrder计算ErpRefundInfo相关金额字段： BrokerageAmount、PlatformDiscount、MerchantDiscount、DiscountAmount、RefundPostFee、PlatformRefundDeliveryFee、PlatformRefundPackFee、PackageFee、ApportionAmount、RefundGoodsTotal、refundMerchantTotal |
| 微商城：calcMicroShopPartRefundDetails |  | StoreBillConfig、RefundDetail、ErpRefundInfo、RefundOrder | refundDetail.setActualNetAmount、refundDetail.setBillPrice、refundDetail.setRefundDiscountAmount |
| 其他：calculateDetailSinkAmountV2 |  | ErpRefundInfo、OrderPayInfo、StoreBillConfig、RefundDetail | refundDetail.setActualNetAmount、refundDetail.setShareAmount、refundDetail.setBillPrice |
| 部分退款：changePartRefundState |  |  |  | RefundDetail、OrderDetail | BigDecimal discountAmount = orderDetail.getDiscountAmount().subtract(refundDetail.getCouponAmount()==null?BigDecimal.ZERO:refundDetail.getCouponAmount())  .subtract(refundDetail.getActivityDiscountAmont()==null?BigDecimal.ZERO:refundDetail.getActivityDiscountAmont()); //actualAmount = 商品金额-商家明细优惠金额-调整金额 BigDecimal foodAmount = new BigDecimal(orderDetail.getCommodityCount().intValue() - alreadyRefundCount).multiply(orderDetail.getPrice()); BigDecimal actualAmount = foodAmount.subtract(discountAmount).subtract(orderDetail.getAdjustAmount());orderDetail.setRefundCount、orderDetail.setStatus、orderDetail.setActualAmount、orderDetail.setDiscountAmount |
| 全额退款agreeRefund |  |  |  |  | ``` //原单已下账的情况：将其他的部分退款单全部取消 `````` RefundOrder refundOrderUpdate = new RefundOrder(); refundOrderUpdate.setErpState(RefundErpStateEnum.CANCELED.getCode()); refundOrderUpdate.setState(RefundStateEnum.CANCEL.getCode()); UpdateWrapper<RefundOrder> updateWrapper = new UpdateWrapper<>(); updateWrapper.lambda()         .eq(RefundOrder::getOrderNo, orderInfo.getOrderNo())         .eq(RefundOrder::getType, RefundTypeEnum.PART.getCode())         .eq(RefundOrder::getErpState, RefundErpStateEnum.WAIT_REFUND.getCode()); ``` |
| ``` 补充创建明细 ```JD_DAOJIA：createRemainingRefundDetailsForJd（不更新退单信息）其他：createRemainingRefundDetailsAndUpdateRefundOrder |  |  |  | ```  ``` |
|  | handleRemainingRefundAmount |  | ``` RefundOrder、 `````` OrderInfo、 `````` OrderPayInfo、 `````` otherRefundList、 ``` | true    protected void handleRemainingRefundAmount(RefundOrderContext context) {         RefundOrder refundOrder = context.getRefundOrder();         OrderInfo orderInfo = context.getOrderInfo();         OrderPayInfo orderPayInfo = orderPayInfoMapper.selectByOrderNo(orderInfo.getOrderNo());         context.setOrderPayInfo(orderPayInfo);          // 退买家总金额         BigDecimal consumerRefund = orderPayInfo.getBuyerActualAmount();         // 退款商品总金额         BigDecimal totalFoodAmount = orderPayInfo.getTotalAmount();         // 商家退款总金额         BigDecimal shopRefund = orderPayInfo.getMerchantActualAmount();         // 退佣金         BigDecimal feeRefund = orderPayInfo.getBrokerageAmount();         // 退平台优惠         BigDecimal platformDiscountRefund = orderPayInfo.getPlatformDiscount();         // 退商家优惠         BigDecimal shopDiscountRefund = orderPayInfo.getMerchantDiscountSum();         // 退平台配送费         BigDecimal platformRefundDeliveryFee = orderPayInfo.getPlatformDeliveryFee();         // 退商家配送费         BigDecimal merchantRefundPostFee = orderPayInfo.getMerchantDeliveryFee();         // 退平台打包费         BigDecimal platformRefundPackFee = orderPayInfo.getPlatformPackFee();         // 退商家打包费         BigDecimal merchantRefundPackFee = orderPayInfo.getMerchantPackFee();         // 退商品明细优惠         BigDecimal detailDiscountAmount = orderPayInfo.getDetailDiscountCollect();          //构建已退款         List<RefundOrder> hasRefundOrderList = getHasRefundList(orderInfo);          for (RefundOrder hasRefund : hasRefundOrderList) {             // 退买家总金额             consumerRefund = consumerRefund.subtract(hasRefund.getConsumerRefund());             // 退款商品总金额             totalFoodAmount = totalFoodAmount.subtract(hasRefund.getTotalFoodAmount());             // 商家退款总金额             shopRefund = shopRefund.subtract(hasRefund.getShopRefund());             // 退佣金             if (hasRefund.getFeeRefund() != null) {                 feeRefund = feeRefund.subtract(hasRefund.getFeeRefund());             }             // 退平台优惠             if (hasRefund.getPlatformDiscountRefund() != null) {                 platformDiscountRefund = platformDiscountRefund.subtract(hasRefund.getPlatformDiscountRefund());             }             // 退商家优惠             if (hasRefund.getShopDiscountRefund() != null) {                 shopDiscountRefund = shopDiscountRefund.subtract(hasRefund.getShopDiscountRefund());             }             // 退平台配送费             platformRefundDeliveryFee = platformRefundDeliveryFee.subtract(hasRefund.getPlatformRefundDeliveryFee());             // 退商家配送费             merchantRefundPostFee = merchantRefundPostFee.subtract(hasRefund.getMerchantRefundPostFee());             // 退平台打包费             platformRefundPackFee = platformRefundPackFee.subtract(hasRefund.getPlatformRefundPackFee());             // 退商家打包费             merchantRefundPackFee = merchantRefundPackFee.subtract(hasRefund.getMerchantRefundPackFee());             // 退商品明细优惠             detailDiscountAmount = detailDiscountAmount.subtract(hasRefund.getDetailDiscountAmount());         }          // 退款单金额         refundOrder.setTotalAmount(consumerRefund);         // 退邮费         refundOrder.setPostageAmount(                 platformRefundDeliveryFee.compareTo(BigDecimal.ZERO) == 0 ? merchantRefundPostFee : platformRefundDeliveryFee         );         // 全部退款无关字段         refundOrder.setUserPostage(BigDecimal.ZERO);         refundOrder.setPlatformPostage(BigDecimal.ZERO);         // 退买家总金额         refundOrder.setConsumerRefund(consumerRefund);         // 退款商品总金额         refundOrder.setTotalFoodAmount(totalFoodAmount);         // 商家退款总金额         refundOrder.setShopRefund(shopRefund);         // 退佣金         refundOrder.setFeeRefund(feeRefund);         // 退平台优惠         refundOrder.setPlatformDiscountRefund(platformDiscountRefund);         // 退商家优惠         refundOrder.setShopDiscountRefund(shopDiscountRefund);         // 退平台配送费         refundOrder.setPlatformRefundDeliveryFee(platformRefundDeliveryFee);         // 退商家配送费         refundOrder.setMerchantRefundPostFee(merchantRefundPostFee);         // 退平台打包费         refundOrder.setPlatformRefundPackFee(platformRefundPackFee);         // 退商家打包费         refundOrder.setMerchantRefundPackFee(merchantRefundPackFee);         // 退商品明细优惠         refundOrder.setDetailDiscountAmount(detailDiscountAmount);     }  //获取订单未下账退款单未重算  && 订单已下账退款单已下账数据     private List<RefundOrder> getHasRefundList(OrderInfo orderInfo){         // 计算退款单主表金额         int refundBillErpState = ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState()) ?                 RefundErpStateEnum.HAS_REFUND.getCode() : RefundErpStateEnum.CANCELED.getCode();         LambdaQueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<RefundOrder>().lambda()                 .eq(RefundOrder::getOrderNo, orderInfo.getOrderNo())                 .eq(RefundOrder::getType, RefundTypeEnum.PART.getCode())                 .eq(RefundOrder::getState, RefundStateEnum.SUCCESS.getCode())                 .eq(RefundOrder::getErpState, refundBillErpState);         if(RefundErpStateEnum.CANCELED.getCode() == refundBillErpState){             queryWrapper.eq(RefundOrder::getReCalculateOriginOrderFlag, DsConstants.INTEGER_ZERO);         }         return refundOrderMapper.selectList(queryWrapper);     } |
| createRemainingRefundDetails |  | RefundOrder、RefundDetail、OrderDetail、 | ``` refundDetail全部信息 ``` |
|  | calcRefundDetailAmount | RefundOrder、OrderInfo、RefundDetail、otherRefundList，otherRefundDetailList |
| calculateRemainingRefundSinkAmount |  |  |  |
|  | ``` calculateRemainingRefundAmount ``` |  |  |
| ``` calculateDetailSinkAmountV2 ``` |  |  |
| ``` erp零售流水相关下账 `````` autoExecuteV2 ``` |  |  |  |  |  |
|  | 如果是美团且是部分退款autoSinkRefund = autoAssignMtRefundAmountFromRecordByRefund |  |  |  |  |
|  |  | 更新退款表金额 重算下账金额、退款明细金额updateRefundAmountWithRecord |  | 美团退款单明细、RefundOrder、StoreBillConfig、RefundDetail、OrderDetail、ErpRefundInfo | if (charge.getPlatformChargeFee() == null  && charge.getActivityMeituanAmount() == null  && charge.getActivityPoiAmount() == null) {  return; }**RefundOrder**：if (charge.getTotalFoodAmount() != null && charge.getTotalFoodAmount().movePointRight(2).intValue() != 0) {  refundOrder.setTotalFoodAmount(charge.getTotalFoodAmount().abs()); } if (charge.getSettleAmount() != null && charge.getSettleAmount().movePointRight(2).intValue() != 0) {  refundOrder.setShopRefund(charge.getSettleAmount().abs()); } if (charge.getPlatformChargeFee() != null) {  refundOrder.setFeeRefund(charge.getPlatformChargeFee().abs()); } else {  refundOrder.setFeeRefund(BigDecimal.ZERO); } if (charge.getActivityMeituanAmount() != null) {  refundOrder.setPlatformDiscountRefund(charge.getActivityMeituanAmount().abs()); } else {  refundOrder.setPlatformDiscountRefund(BigDecimal.ZERO); } if (charge.getActivityPoiAmount() != null) {  refundOrder.setShopDiscountRefund(charge.getActivityPoiAmount().abs()); } else {  refundOrder.setShopDiscountRefund(BigDecimal.ZERO); }  // 退配送费 BigDecimal boxAmount = charge.getBoxAmount().abs(); if (boxAmount != null && boxAmount.movePointRight(2).intValue() != 0) {  StoreBillConfig storeBillConfig = storeBillConfigService.getBillConfigById(orderInfo.getClientConfId()); if (storeBillConfig.getPackageFeeInventory()) {  if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getPackageFeeFetch())) {  refundOrder.setPlatformRefundPackFee(boxAmount); }  if (DsConstants.INTEGER_TWO.equals(storeBillConfig.getPackageFeeFetch())) {  refundOrder.setMerchantRefundPackFee(boxAmount); }  } }  **RefundDetail**：if(DsConstants.INTEGER_ONE.equals(allDomain.getOrderPayInfo().getApportionType())  && !DsConstants.INTEGER_ONE.equals(allDomain.getComplexModifyFlag())){  MtDetailsDiscountMatcher.convertMtRefundDetailDiscount(allDomain.getOrderDetailList(),refundDetailList,record); } Map<String,OrderDetail> swapMap = orderDetailList.stream()  .filter(item -> OrderDetailStatusEnum.REPLACE.getCode().equals(item.getStatus()))  .collect(Collectors.toMap(item -> String.format("%s_%s",item.getErpCode(),item.getBarCode()), item -> item, (a, b) -> a)); Map<String,OrderDetail> normalMap = orderDetailList.stream()  .filter(item -> OrderDetailStatusEnum.REPLACE.getCode().compareTo(item.getStatus()) > 0)  .collect(Collectors.toMap(item -> String.format("%s_%s",item.getErpCode(),item.getBarCode()),item -> item, (a,b) -> a));for(RefundDetail refundDetail : refundDetailList){  if(StringUtils.isNotBlank(refundDetail.getDetailDiscount())){{  continue; }}  String discountKey = String.format("%s_%s",refundDetail.getErpCode(),refundDetail.getBarCode()); OrderDetail orderDetail = normalMap.get(discountKey); if(orderDetail != null){  OrderDetail swapDetail = swapMap.get(String.format("%s_%s",orderDetail.getErpCode(),orderDetail.getBarCode())); if(swapDetail != null){  discountKey = String.format("%s_%s",swapDetail.getErpCode(),swapDetail.getBarCode()); }  }  DetailsDiscount discount = discountMap.get(discountKey); if(discount == null){  continue; }  refundDetail.setDetailDiscount(JSON.toJSONString(discount)); } |
|  |  | refreshRefundSinkAmount() |  |  |
|  | !ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState()) || autoSinkRefund |  |  |  |  |
|  |  | ErpStateEnum.HAS_SALESinkToErpRefund.operation | ```  ``` | ``` OrderInfo、 `````` RefundOrder、 `````` orderDetail、 `````` OrderPickInfo ``` | ``` RefundOrder.setBillType ``` |
|  |  |  | ``` partRefundCommissionCheckEBAI ``` |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |
|  |  |  |  |  |  |