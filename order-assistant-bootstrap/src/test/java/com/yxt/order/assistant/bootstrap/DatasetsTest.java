package com.yxt.order.assistant;


import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.DatasetResponse;
import java.io.IOException;
import javax.annotation.Resource;
import org.junit.Test;

public class DatasetsTest extends BaseTest{

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  private String dataSetId = "4f215f8e-406d-43bc-ad83-5b5047c72009";

  @Test
  public void testDataSet() throws DifyApiException, IOException {
    DatasetResponse dataset = difyDatasetsClient.getDataset(dataSetId);
    System.out.println();
  }
}
