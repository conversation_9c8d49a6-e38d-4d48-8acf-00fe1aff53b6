# 24年第29周2024-08-09

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 预计8月13上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 预计8月13上线 17. 微商城：   - 预计7月初进入开发阶段 18. 预计7月初进入开发阶段 19. 美团：   - 7月29号开始开发   - 预计8月19号提测   - 预计9月中旬上线 20. 7月29号开始开发 21. 预计8月19号提测 22. 预计9月中旬上线 23. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 24. 7月29号开始开发 25. 预计8月19号提测 | 1. 京东到家：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 测试中-95% 2. 消息回调-100% 3. 接口对接-100% 4. 订单中台接口替换-100% 5. 测试中-95% 6. 微商城：   1. 消息回调 -100%   2. 接口对接-100%   3. 接口中台修改–重用之前的   4. 周四提测 7. 消息回调 -100% 8. 接口对接-100% 9. 接口中台修改–重用之前的 10. 周四提测 11. 配送：   1. 消息回调-25%   2. 接口对接-40%   3. 接口中台改造-100% 12. 消息回调-25% 13. 接口对接-40% 14. 接口中台改造-100% 15. 美团：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-30%   - 预计8-16号提测 16. 消息回调-100% 17. 接口对接-100% 18. 订单中台接口替换-30% 19. 预计8-16号提测 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0%    3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 50% - 订单同步服务重构, 55% - 配送信息更新开发中 30% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫   todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕30% 版本升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 7 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 8 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 9 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.对接客服系统 |  | 需求变更，等具体的需求明确后进行 |  |
| 2.线下单对接-海典 |  | 已上预发 |  |
| 3.线下单对接-科传 |  | 待联调 |  |
| 4.线上单对接 |  | 测试中 |  |
| 5.慢病接口支持-按门店_sku es支持搜索 |  | 开发30% |  |
| 6.美团电子围栏兼容距离限制 |  | 已上线-.net存在问题.功能上线但未启用. |  |
| 7.医保对接历史原价对接改为促销 |  | 8月19号之前进入开发 |  |
| 8.B2C中转平台迁移部署 |  | 9月9号之前完成上线 已经dev部署 |  |
| 9.B2C作业V2.1 |  | 测试中 龙敏 下周四上线 |  |
| 10.O2O作业V2.1 |  | 测试中 下周二上线 |  |
| 11.处方单履约流程V2.1 |  | 下周三开始 |  |
| 12.店铺标签 |  | 开发40% 下周二提测 |  |
| 13.O2O正单负单下账金额优化 |  | 测试中 |  |
| 14.B2C库存占用 |  | 待技术评审 |  |
| 15.购物车小优化 |  | 8月19号计划上线 |  |
| 吉客云 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 | hydee-business-order（广播方式，发送语音播报消息，一个已经修改）待验证 |  |
| XXL-JOB迁移升级 | [https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e](https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e) |  |
| Q3绩效 | [https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i) | 拉齐评级标准 |
| [Redis迁移](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38831735) | 已完成迁移 |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** | **遗留问题** | **需求研发** **技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 线下单:   1. 线下单新增接口开发,已完成     1. 添加内部父单号关联，添加联合订单接口开发,已完成     2. 根据整单查退单接口开发,已完成     3. 统一替换会员接口调整开发,已完成   2. 添加内部父单号关联，添加联合订单接口开发,已完成   3. 根据整单查退单接口开发,已完成   4. 统一替换会员接口调整开发,已完成   5. 迁移数据问题排查(hana生产库无明细订单、冻结会员信息查不到等问题)   6. 海典线下单上预发,已完成   1. 添加内部父单号关联，添加联合订单接口开发,已完成   2. 根据整单查退单接口开发,已完成   3. 统一替换会员接口调整开发,已完成 2. 线下单新增接口开发,已完成   1. 添加内部父单号关联，添加联合订单接口开发,已完成   2. 根据整单查退单接口开发,已完成   3. 统一替换会员接口调整开发,已完成 3. 添加内部父单号关联，添加联合订单接口开发,已完成 4. 根据整单查退单接口开发,已完成 5. 统一替换会员接口调整开发,已完成 6. 迁移数据问题排查(hana生产库无明细订单、冻结会员信息查不到等问题) 7. 海典线下单上预发,已完成 8. 配合慢病二期,提供店铺查询接口,开发中30% 9. 其他: 配合大数据、营销,沟通订单相关字段,对齐口径 | **遗留问题**  **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：** **5d**1.迁移配送服务。目前美团骑手迁移完毕 3day2.配送范围调整，上线后 达达骑手404 目前没有环境复现，上线失败。 1 day3.其他线上问题处理 1day 。京东无界 隐私改造，创建面单时需要传递OAID 这个时从拉单信息里面获取的，需要调整 b2c-third ，获取oaid 、在创建面单 传过去。和海典具体确认 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：5d**.net迁移-美团模块：1. 消息回调-100% 2. 接口对接-90% 3. 订单中台接口替换-30% | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d**1. 购物车优化 2. O2O作业下账问题 3. B2C库存占用技术方案 4. 线上问题处理 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：4d**1.下账单明细拆分2.新下账单批量处理3.订单导出bug修复 | **遗留问题**1.毛利预警更新2.V2.1需求bug处理**风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. B2C 6~7 月历史单，根据状态导入下账单 待下账/下账失败 已完成 2. 电子围栏范围限制优化 已上线 3. 电子围栏动态监听同步 4. B2C订单管理系统环境部署 dev环境已部署 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. B2C未发货取消订单创建售后单问题 2. B2C售后单未更新成已完成问题 3. B2C退款单下账单无赠品问题 4. 店铺标签开发 40% | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 智能客服调整接口编写 2. 开发测试优化工具技术方案编写 3. 动态路由开发 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |