# 【20250307】log4j2替换logback

## 1、对接方案

[Logback 更换为 Log4j2 最佳实践 - 后端研发部 - 一心数科数字化产研中心-wiki](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50318691)

## 2、切换相关服务

| 服务 | 服务描述 | 进度 | 发版时间 | 备注 |
| --- | --- | --- | --- | --- |
| order-service | 新订单中台 | pro |  |  |
| order-sync-service | 新订单中台异步服务 | pro |  |  |
| order-atom-service | 新订单中台原子服务 | pro |  |  |
| third-platform-callback-elm | 接口中台饿了么回调服务 | pro |  |  |
| third-platform-callback-jddj | 接口中台京东到家回调服务 | pro |  |  |
| third-platform-callback-mt | 接口中台美团回调服务 | pro |  |  |
| third-platform-callback-other | 接口中台其他回调服务 | pro |  |  |
| third-platform-callback-rider | 接口中台骑手回调服务 | pro |  |  |
| third-platform-logistics | 接口中台物流服务 | pro |  |  |
| third-platform-order-elm | 接口中台饿了么订单服务 | pro |  |  |
| third-platform-order-jddj | 接口中台京东到家订单服务 | pro |  |  |
| third-platform-order-mt | 接口中台美团订单服务 | pro |  |  |
| third-platform-order-other | 接口中台订单其他服务 | pro |  |  |
| third-platform-other | 接口中台其他服务 | pro |  |  |
| third-platform-rider | 接口中台骑手服务 | pro |  |  |
| third-platform-gateway | 接口中台网关服务 |  |  | 版本跨度太大，暂不处理 |
| logistics-center | 物流中台 | pro |  |  |
| message-icsim-server | 智能客服中心服务 |  |  | 重构中，暂不处理 |
| hydee-xxl-job | 海典统一调度中心服务 |  |  | [1.1.0.pa](http://1.1.0.pa)版本，暂不处理 |
| middle-id | 海典全局ID服务 |  |  |  |
| businesses-gateway | 海典业务网关服务 |  |  | [1.1.0.pa](http://1.1.0.pa)版本，暂不处理 |
| middle-datasync-message | 订单同步 | pro |  |  |
| hydee-api-gateway | 海典OpenApi网关服务 |  |  | [1.1.0.pa](http://1.1.0.pa)，暂不处理 |
| hydee-middle-sdp | 海典商城分销中台服务 | pro |  |  |
| hydee-middle-order | 海典订单服务 | pro |  |  |
| hydee-business-order | 海典OMS服务 | pro |  |  |
| hydee-business-order-web | 海典OMS-B2C服务 |  |  | [1.1.1.pa](http://1.1.1.pa)版本，暂不处理 |
| hydee-business-order-b2c-third | 三方b2c订单 |  |  | [1.1.1.pa](http://1.1.1.pa)版本，暂不处理 |
| the3platform-message | 3方平台（抖音、京东惠采、会订货）消息 |  |  | 已废弃，替换为接口中台 |
| the3platform-adapter | 3方平台（抖音、京东惠采、会订货） |  |  | 已废弃，替换为接口中台 |
| hydee-middle-payment | 海典支付服务 | pro |  |  |
| h3-pay-finance | 支付服务（支付配置） |  |  | h3支付项目重构中，暂不处理 |
| h3-pay-core | 支付服务（支付逻辑） |  |  | h3支付项目重构中，暂不处理 |


## 3、切换步骤

1. apollo服务治理：apollo上新增配置，确保application.yml,bizconfig.properties,thirdconfig.properties三个配置文件都有并发布
2. 发布服务


## 4、相关问题

问题一：项目在本地运行没问题，在test环境无法启动：没有规范apollo规范

原因：项目父包进行过升级，新增了“apollo配置文件治理”

解决方案：[项目配置文件治理 - 后端研发部 - 一心数科数字化产研中心-wiki](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=37781975)

问题二：pre环境打包发布服务报错：禁用引入shapshot依赖包

原因：项目父包进行过升级，新增了“禁用引入shapshot依赖包”

解决方案：[snapshot版本依赖治理 - 后端研发部 - 一心数科数字化产研中心-wiki](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38842783)