# 【20240425】【O2O/B2C】拣货复核时，相同ERPCode的商品合并拣货

# 一、背景

## 1.1 业务背景

## 1.2 痛点分析

## 1.3 系统现状

# 二、需求分析

## 2.1 业务流程

[[ORDER-1014] 【O2O/B2C】拣货复核时，相同ERPCode的商品合并拣货 - 一心数科数字化产研中心-Scrum (hxyxt.com)](https://jira.hxyxt.com/browse/ORDER-1014)

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

### 4.2.1 门店信息变更MQ监听

### 4.2.2 POS配置流程

#### 4.2.2.1 门店配置

#### 4.2.2.2 组织机构配置

# 五、详细设计

## 5.1 详细模块设计

增加开关：open-pick-merge，当为true时走合并逻辑，为false时走老逻辑

## 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |


## 5.3 接口设计

### 5.3.1 前端交互接口（更新）

#### 1 API-获取复核信息,返回未拣货与已拣货的数据

1. url: dscloud/1.0/ds/order/detail/getCachePickConfirmInfo
2. 请求类型：POST
3. 变化点：
  1. 响应体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | ``` 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId ``` |
| ``` openPickMerge ``` | Boolean | ``` 是否开启拣货信息合并 ``` |
  2. 示例：
    - {
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "orderNo": null,
        "openPickMerge": true,
        "pickedInfo": [],
        "notPickedInfo": [
            {
                "detailId": 25175512,
                "detailIdList": null,
                "barCode": "6937951901114",
                "erpCode": "170654",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 1,
                "goodVaildityDate": null
            },
            {
                "detailId": 25175509,
                "detailIdList": [
                    25175509,
                    25175510
                ],
                "barCode": "6941548900015",
                "erpCode": "114973",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 2,
                "goodVaildityDate": null
            }
        ]
    },
    "timestamp": 1714027297390
}
  3. {
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "orderNo": null,
        "openPickMerge": true,
        "pickedInfo": [],
        "notPickedInfo": [
            {
                "detailId": 25175512,
                "detailIdList": null,
                "barCode": "6937951901114",
                "erpCode": "170654",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 1,
                "goodVaildityDate": null
            },
            {
                "detailId": 25175509,
                "detailIdList": [
                    25175509,
                    25175510
                ],
                "barCode": "6941548900015",
                "erpCode": "114973",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 2,
                "goodVaildityDate": null
            }
        ]
    },
    "timestamp": 1714027297390
}
  - {
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "orderNo": null,
        "openPickMerge": true,
        "pickedInfo": [],
        "notPickedInfo": [
            {
                "detailId": 25175512,
                "detailIdList": null,
                "barCode": "6937951901114",
                "erpCode": "170654",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 1,
                "goodVaildityDate": null
            },
            {
                "detailId": 25175509,
                "detailIdList": [
                    25175509,
                    25175510
                ],
                "barCode": "6941548900015",
                "erpCode": "114973",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 2,
                "goodVaildityDate": null
            }
        ]
    },
    "timestamp": 1714027297390
}
4. 响应体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | ``` 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId ``` |
| ``` openPickMerge ``` | Boolean | ``` 是否开启拣货信息合并 ``` |
5. 示例：
  - {
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "orderNo": null,
        "openPickMerge": true,
        "pickedInfo": [],
        "notPickedInfo": [
            {
                "detailId": 25175512,
                "detailIdList": null,
                "barCode": "6937951901114",
                "erpCode": "170654",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 1,
                "goodVaildityDate": null
            },
            {
                "detailId": 25175509,
                "detailIdList": [
                    25175509,
                    25175510
                ],
                "barCode": "6941548900015",
                "erpCode": "114973",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 2,
                "goodVaildityDate": null
            }
        ]
    },
    "timestamp": 1714027297390
}
6. {
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "orderNo": null,
        "openPickMerge": true,
        "pickedInfo": [],
        "notPickedInfo": [
            {
                "detailId": 25175512,
                "detailIdList": null,
                "barCode": "6937951901114",
                "erpCode": "170654",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 1,
                "goodVaildityDate": null
            },
            {
                "detailId": 25175509,
                "detailIdList": [
                    25175509,
                    25175510
                ],
                "barCode": "6941548900015",
                "erpCode": "114973",
                "pickedNum": 0,
                "refundCount": 0,
                "detailNum": 2,
                "goodVaildityDate": null
            }
        ]
    },
    "timestamp": 1714027297390
}


#### 2 API-更新订单拣货批次号

1. url: dscloud/1.0/ds/order/upOrderBatchNo
2. 请求类型：POST
3. 变化点：
  1. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
  2. 示例：
    - {
    "orderNo": "1795191948774953990",
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
  3. {
    "orderNo": "1795191948774953990",
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
  - {
    "orderNo": "1795191948774953990",
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
4. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
5. 示例：
  - {
    "orderNo": "1795191948774953990",
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
6. {
    "orderNo": "1795191948774953990",
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}


#### 3 API-确认拣货

1. url: dscloud/2.0/ds/order/pick/confirm
2. 请求方式：POST
3. 变化点：
  1. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
  2. 示例：
    - {
    "orderNo": "1795191948774953990",
    "deliveryPlatform": "到店自提",
    "orderState": 20,
    "pickOperatorId": "4088620517526182920",
    "pickOperatorName": "童应枣"
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
  3. {
    "orderNo": "1795191948774953990",
    "deliveryPlatform": "到店自提",
    "orderState": 20,
    "pickOperatorId": "4088620517526182920",
    "pickOperatorName": "童应枣"
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
  - {
    "orderNo": "1795191948774953990",
    "deliveryPlatform": "到店自提",
    "orderState": 20,
    "pickOperatorId": "4088620517526182920",
    "pickOperatorName": "童应枣"
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
4. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
5. 示例：
  - {
    "orderNo": "1795191948774953990",
    "deliveryPlatform": "到店自提",
    "orderState": 20,
    "pickOperatorId": "4088620517526182920",
    "pickOperatorName": "童应枣"
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}
6. {
    "orderNo": "1795191948774953990",
    "deliveryPlatform": "到店自提",
    "orderState": 20,
    "pickOperatorId": "4088620517526182920",
    "pickOperatorName": "童应枣"
    "pickDetailList":
    [
        {
            "commodityBatchNo": "220906",
            "count": 2,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221106",
            "count": 3,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221256",
            "count": 4,
            "erpCode": "104054",
            "orderDetailIdList": [4013492,4013493],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "220945",
            "count": 2,
            "erpCode": "104055",
            "orderDetailIdList": [4013494,4013495],
            "purchasePrice": ""
        },
        {
            "commodityBatchNo": "221658",
            "count": 1,
            "erpCode": "104056",
            "orderDetailId": 4013496,
            "purchasePrice": ""
        }
    ]
}


#### 4 API-缓存复核信息、校验是否通过复核（这两个接口暂时没发现页面交互调用）

1. url：/cachePickConfirmInfo、/checkPickConfirm
2. 请求方式：POST
3. 变更点：
  1. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
  2. 示例：
    - {
    "barCode": "",
    "erpCode": "",
    "orderNo": "1797286452519202561",
    "userId": "1695970231258714114",
    "detailIdList":
    [
        25175509,
        25175510
    ],
    "num": 2
}
  3. {
    "barCode": "",
    "erpCode": "",
    "orderNo": "1797286452519202561",
    "userId": "1695970231258714114",
    "detailIdList":
    [
        25175509,
        25175510
    ],
    "num": 2
}
  - {
    "barCode": "",
    "erpCode": "",
    "orderNo": "1797286452519202561",
    "userId": "1695970231258714114",
    "detailIdList":
    [
        25175509,
        25175510
    ],
    "num": 2
}
4. 请求体新增字段| 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` detailIdList ``` | List<Long> | 明细id列表,当拣货明细合并时，该列表放合并的orderDetailId |
5. 示例：
  - {
    "barCode": "",
    "erpCode": "",
    "orderNo": "1797286452519202561",
    "userId": "1695970231258714114",
    "detailIdList":
    [
        25175509,
        25175510
    ],
    "num": 2
}
6. {
    "barCode": "",
    "erpCode": "",
    "orderNo": "1797286452519202561",
    "userId": "1695970231258714114",
    "detailIdList":
    [
        25175509,
        25175510
    ],
    "num": 2
}


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 开发时间 | 负责人 | 进度 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 会员组MQ消息监听 | 监听组织机构变动消息，并新增/更新我们的业务表 | business-order |  |  | 暂定，依赖会员组 |  |  |
| 门店配置 | 详情回显，新增字段：POS类型 | ydjia-merchant-platformbusiness-order |  |  |  |  |
| 编辑保存，新增字段：POS类型 |  |  |  |
| 组织机构-POS机设置 | 表结构修改重新刷inner表数据？ | business-order |  |  |  |  |
|  | 列表查询显示 |  |  |  |  |  |
|  | POS回显/编辑保存 |  |  |  | - |  |
| 缓存（暂定） | 增加列表缓存 | business-order |  |  | 时间足够可写 |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等