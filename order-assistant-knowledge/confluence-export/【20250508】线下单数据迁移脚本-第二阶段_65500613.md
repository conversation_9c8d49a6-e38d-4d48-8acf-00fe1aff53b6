# 【20250508】线下单数据迁移脚本-第二阶段

### Green已完成

### 一、遗留问题

历史迁移:

历史数据未完成的纠正:

**每一次变更需要考虑是否要处理ES**

### 二、第二阶段

第二阶段迁移时间范围

XF_CREATETIME>='2024-04-10 00:00:00' AND XF_CREATETIME<'2024-07-31 00:00:00'

第二阶段相关表

|  |  |  |
| --- | --- | --- |
| hana_migration_stage_2_remigrate | 第二阶段迁移任务表 | 表结构,移除了几个统计字段,方便维护CREATE TABLE `hana_migration_stage_2` (   `id` bigint NOT NULL AUTO_INCREMENT,   `migrate_sort` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '迁移顺序 ORDER\\REFUND',   `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司名',   `target_schema` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库schema(小写,代码也重写了getter)',   `migration_start_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移的开始时间(发送MQ)',   `migration_end_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移的结束时间(发送MQ)',   `on_off` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '启用状态 true,false',   `migration_result` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移结果 true,false',   `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `config_json` varchar(2048) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置json',   PRIMARY KEY (`id`) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |
| hana_migration_error_stage_2_remigrate | 第二阶段异常明细表 | 第二阶段CREATE TABLE `hana_migration_error_stage_2` (   `id` bigint NOT NULL AUTO_INCREMENT,   `hana_migration_id` bigint NOT NULL COMMENT 'hana_migration表id',   `target_schema` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据库schema(小写)',   `condition_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '条件',   `entry_retry_queue` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否进入重试队列 true,false',   `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误消息',   `error_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '错误类型: SEND_MQ_FAILED/FROM_ARCHIVE_TO_OFFLINE_ORDER',   `created_time` datetime DEFAULT NULL COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   PRIMARY KEY (`id`),   KEY `idx_created_time` (`created_time`) USING BTREE,   KEY `idx_hana_migration_id` (`hana_migration_id`) USING BTREE,   KEY `idx_error_type` (`error_type`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |
| order_data_relation_hana_stage_2_remigrate | 第二阶段归档表与线下订单表 | 主要方便后面刷ES,不用去全表扫线下单所有的分表，直接读取这个表就好了。提速CREATE TABLE `order_data_relation_hana_stage_2` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',   `type` varchar(255) DEFAULT NULL COMMENT 'Type: OFFLINE_ORDER\\OFFLINE_REFUND_ORDER',   `business_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '系统业务单号',   `target_schema` varchar(255) DEFAULT NULL COMMENT 'Target schema',   `hana_id` varchar(255) DEFAULT NULL COMMENT 'Hana archive database ID',   PRIMARY KEY (`id`),   KEY `idx_type_business_no` (`type`,`business_no`) COMMENT 'Index on type and business_no',   KEY `idx_target_schema_hana_id` (`target_schema`,`hana_id`,`business_no`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Mapping between order data and Hana data'; |


### 三、特别注意

高斯DB，归档库中的表在迁移时需要排除

1. 表名中含有 txbatch 字符的,需要排除,这个是因为在第一阶段迁移时 XF_TXSERIAL\XF_TXBATCH 字段值在mysql体现为科学计数法,进而重新迁移的表,后来在程序中解决了,故可以不再处理,忽略即可


### 四、脚本

|  |  |  |
| --- | --- | --- |
|  |  |  |