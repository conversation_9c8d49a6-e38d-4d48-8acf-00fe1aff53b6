# 【20250325】支付中台-余额支付

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 余额支付 | ``` yxt-payment ``` |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| middle_payment | ``` create table middle_payment.common_extend (     id          bigint unsigned auto_increment comment '主键ID'         not null primary key,     unique_no   bigint unsigned                     null comment '唯一单号',     type        varchar(32)                         not null comment '类型',     extra_info  text                                null comment '扩展信息',     create_time timestamp default CURRENT_TIMESTAMP not null comment '创建时间',     update_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间' )     comment '公共扩展信息表' collate = utf8mb4_unicode_ci; create index idx_unique_no_type     on middle_payment.common_extend (unique_no desc, type asc); create table middle_payment.pay_message_log (     id              bigint unsigned auto_increment comment '主键ID'         not null  primary key,     exception_msg   text                                null comment '异常信息',     request_body    json                                null comment '方法入参',     response_body   json                                null comment '方法出参',     out_order_no    varchar(64)                         null comment '支付单号',     app_id          varchar(32)                         null comment '应用ID',     merchant_code   varchar(32)                         null comment '商户号',     out_refund_no   varchar(64)                         null comment '退款单号',     pay_order_no    bigint unsigned                     null comment '支付单号',     refund_order_no bigint unsigned                     null comment '退款单号',     method_name     varchar(50)                         null comment '方法名称',     create_time     timestamp                           not null comment '创建时间',     update_time     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间' )     comment '支付信息日志表' charset = utf8mb4; create index idx_out_order_no     on middle_payment.pay_message_log (out_order_no desc); create index idx_out_refund_no     on middle_payment.pay_message_log (out_refund_no desc); create index idx_pay_order_no     on middle_payment.pay_message_log (pay_order_no desc); create index idx_refund_order_no     on middle_payment.pay_message_log (refund_order_no desc); create table middle_payment.pay_order (     id                     bigint unsigned auto_increment comment '主键ID'         not null    primary key,     pay_order_no           bigint unsigned                     not null comment '支付单号',     app_id                 varchar(32)                         not null comment '应用ID',     merchant_code          varchar(32)                         not null comment '商户号(门店唯一编码)',     out_order_no           varchar(32)                         not null comment '外部订单号',     amount                 decimal(16, 6)                      not null comment '订单金额(元)',     currency               varchar(16)                         not null comment '货币类型',     pay_channel_type_code  varchar(32)                         null comment '支付通道类型',     out_business_type_code varchar(32)                         null comment '业务支付类型编码',     out_business_type_name varchar(64)                         null comment '业务支付类型名称',     notify_url             varchar(255)                        null comment '支付结果回调通知地址',     expire_time            timestamp                           null comment '订单过期时间',     status                 varchar(32)                         not null comment '订单状态',     close_reason           varchar(255)                        null comment '订单关闭原因',     close_time             timestamp                           null comment '订单关闭时间',     end_time               timestamp                           null comment '支付完成时间，成功时间或失败时间',     pay_order_type         varchar(32)                         null comment '支付订单类型',     third_pay_params       varchar(128)                        null comment '三方支付信息,app支付时是返回给手机端做跳转支付的',     call_third_no          varchar(64)                         null comment '请求三方的单号,一般情况在等于payOrderNo',     refunded_balance       decimal(16, 6)                      null comment '退款金额',     locked_balance         decimal(16, 6)                      null comment '退款预占金额',     version                int       default 0                 not null comment '版本号',     create_time            timestamp default CURRENT_TIMESTAMP not null comment '创建时间',     update_time            timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',     constraint idx_app_id_merchant_code_out_order_no         unique (app_id, merchant_code, out_order_no),     constraint uk_pay_order_no         unique (pay_order_no desc) )     comment '支付订单表' collate = utf8mb4_unicode_ci; create index idx_create_time     on middle_payment.pay_order (create_time desc); create index idx_expire_time     on middle_payment.pay_order (expire_time desc); create table middle_payment.pay_receipt (     id                     bigint unsigned auto_increment comment '主键ID'         not null    primary key,     pay_receipt_no         bigint unsigned                     not null comment '支付账单号',     app_id                 varchar(32)                         not null comment '应用ID',     merchant_code          varchar(32)                         not null comment '商户编码',     out_order_no           varchar(32)                         not null comment '商户订单号',     pay_order_no           bigint unsigned                     not null comment '支付单号',     call_third_no          varchar(64)                         not null comment '请求三方的单号,一般情况在等于payOrderNo',     third_pay_order_no     varchar(64)                         null comment '三方支付单号',     pay_channel_type_code  varchar(32)                         null comment '支付通道类型',     payment_amount         decimal(16, 6)                      not null comment '支付总金额(元)',     currency               varchar(16)                         not null comment '货币类型',     third_merchant_code    varchar(64)                         null comment '渠道商户号',     out_business_type_code varchar(32)                         null comment '业务退款类型编码',     out_business_type_name varchar(64)                         null comment '业务退款类型名称',     payer                  text                                null comment '支付者信息',     reason                 text                                null comment '原因',     version                int       default 0                 not null comment '版本号',     create_time            timestamp default CURRENT_TIMESTAMP not null comment '创建时间',     update_time            timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',     constraint uk_pay_receipt_no         unique (pay_receipt_no desc) )     comment '支付回执表' collate = utf8mb4_unicode_ci; create index idx_app_id_merchant_code_out_order_no     on middle_payment.pay_receipt (app_id, merchant_code, out_order_no); create index idx_pay_order_no     on middle_payment.pay_receipt (pay_order_no desc); create table middle_payment.refund_order (     id                     bigint unsigned auto_increment comment '主键ID'         not null   primary key,     app_id                 varchar(32)                         not null comment '应用ID',     merchant_code          varchar(32)                         not null comment '商户号',     refund_order_no        bigint unsigned                     not null comment '退款单号',     pay_order_no           bigint unsigned                     null comment '支付单号',     out_order_no           varchar(32)                         null comment '商户订单号',     pay_channel_type_code  varchar(32)                         null comment '支付通道类型',     end_time               timestamp                           null comment '退款成功、失败时间',     refund_status          varchar(32)                         not null comment '退款状态',     refund_order_type      varchar(32)                         null comment '退款类型',     out_business_type_code varchar(32)                         null comment '业务支付类型编码',     out_business_type_name varchar(64)                         null comment '业务支付类型名称',     notify_url             varchar(255)                        null comment '回调通知URL',     out_refund_no          varchar(64)                         not null comment '商户退款订单号',     after_sale_no          varchar(32)                         null comment '售后单',     amount                 decimal(16, 6)                      not null comment '退款金额(元)',     total                  decimal(16, 6)                      null comment '订单总金额(元)',     currency               varchar(16)                         not null comment '货币类型',     reason                 varchar(255)                        null comment '原因',     parent_refund_order_no bigint unsigned                     not null comment '父退款单号',     version                int       default 0                 not null comment '版本号',     create_time            timestamp default CURRENT_TIMESTAMP not null comment '创建时间',     update_time            timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',     constraint uk_refund_order_no         unique (refund_order_no desc) )     comment '退款订单表' collate = utf8mb4_unicode_ci; create index idx_after_sale_no     on middle_payment.refund_order (after_sale_no desc); create index idx_app_id_merchant_code_out_order_no     on middle_payment.refund_order (app_id, merchant_code, out_order_no); create index idx_app_id_merchant_code_out_refund_no     on middle_payment.refund_order (app_id, merchant_code, out_refund_no); create index idx_create_time     on middle_payment.refund_order (create_time desc); create index idx_pay_order_no     on middle_payment.refund_order (pay_order_no desc); create table middle_payment.refund_receipt (     id                     bigint unsigned auto_increment comment '主键ID'         not null    primary key,     app_id                 varchar(32)                         not null comment '应用ID',     merchant_code          varchar(32)                         not null comment '商户号',     pay_order_no           bigint unsigned                     not null comment '支付单号',     refund_order_no        bigint unsigned                     null comment '支付中台退款单号',     pay_channel_type_code  varchar(32)                         null comment '支付通道类型',     out_refund_no          varchar(64)                         not null comment '商户退款订单号',     refund_receipt_no      bigint unsigned                     not null comment '退款账单号',     amount                 decimal(16, 6)                      not null comment '退款金额(元)',     currency               varchar(16)                         not null comment '货币类型',     refund_order_type      varchar(32)                         null comment '退款方式',     out_business_type_code varchar(32)                         null comment '业务退款类型编码',     out_business_type_name varchar(64)                         null comment '业务退款类型名称',     third_refund_no        varchar(64)                         null comment '三方退款单号',     third_merchant_code    varchar(64)                         null comment '渠道商户号',     version                int       default 0                 not null comment '版本号',     create_time            timestamp default CURRENT_TIMESTAMP not null comment '创建时间',     update_time            timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',     constraint uk_refund_receipt_no         unique (refund_receipt_no desc) )     comment '退款回执表' collate = utf8mb4_unicode_ci; create index idx_app_id_merchant_code_out_refund_no     on middle_payment.refund_receipt (app_id, merchant_code, out_refund_no); create index idx_pay_order_no     on middle_payment.refund_receipt (pay_order_no desc); create index idx_refund_order_no     on middle_payment.refund_receipt (refund_order_no desc); ``` |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| yxt-payment | server:  port: 8080 spring:  lifecycle:  timeout-per-shutdown-phase: 20s  application:  name: yxt-payment  redis:  password: ${myEncrypt.des(65d5b0df64714707076972bc2bc1a443216acc162845f052769669c42c20a90e)}  lettuce:  pool:  # 连接池中“空闲”连接的最大数量。使用负值表示空闲连接数量无限制。默认8  max-idle: 8  # 连接池中应维持的最小空闲连接数量。仅当此值和驱逐线程运行间隔均为正数时，该设置才生效。默认0  min-idle: 2  # 连接池在任意时刻能够分配的最大连接数量。使用负值表示无限制。默认8  max-active: 200  # 当连接池耗尽时，连接分配应阻塞的最大时间，之后会抛出异常。使用负值表示无限期阻塞.默认-1  max-wait: 10s  # 空闲对象驱逐线程运行的间隔时间。当为正数时，空闲对象驱逐线程会启动，否则不执行空闲对象驱逐操作。默认空  timeBetweenEvictionRuns: 10m  timeout: 5000  cluster: # 此处新增  nodes: [redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com](http://redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com).:6379  max-redirects: 3  main:  allow-bean-definition-overriding: true  cloud:  sentinel:  enabled: false  discovery:  client:  composite-indicator:  enabled: false # 屏蔽服务注册发现健康检查  nacos:  discovery:  register-enabled: true  server-addr: [http://sk-prod-nacos.nacos.cse.com:8848](http://sk-prod-nacos.nacos.cse.com:8848);  namespace: d04f590f-6926-4e84-becd-a62f369686a2  metadata:  department:  datasource:  dynamic:  primary: master  strict: false  datasource:  master:  url: jdbc:[mysql://10.100.5.198:3306/middle_payment?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&connectTimeout=2000&socketTimeout=60000](mysql://10.100.5.198:3306/middle_payment?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&connectTimeout=2000&socketTimeout=60000)  username: middle_payment_agent  password: UzJFamMUdu7fcYf  driver-class-name: com.mysql.cj.jdbc.Driver  type: com.alibaba.druid.pool.DruidDataSource  druid:  # 最大活跃连接数  max-active: 50  # 初始化时创建的连接数  initial-size: 10  # 最小空闲连接数  min-idle: 10  # 获取连接的最大等待时间（毫秒）  max-wait: 10000  # 检测连接池中空闲连接的周期（毫秒）  time-between-eviction-runs-millis: 60000  # 空闲连接的最小存活时间（毫秒）  min-evictable-idle-time-millis: 300000  # 用于验证连接是否有效的 SQL 查询  validation-query: SELECT 1 FROM DUAL  slave:  url: jdbc:[mysql://10.100.5.151:3306/middle_payment?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&connectTimeout=2000&socketTimeout=60000](mysql://10.100.5.151:3306/middle_payment?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&connectTimeout=2000&socketTimeout=60000)  username: middle_payment_agent  password: UzJFamMUdu7fcYf  driver-class-name: com.mysql.cj.jdbc.Driver  type: com.alibaba.druid.pool.DruidDataSource  druid:  # 最大活跃连接数  max-active: 50  # 初始化时创建的连接数  initial-size: 10  # 最小空闲连接数  min-idle: 10  # 获取连接的最大等待时间（毫秒）  max-wait: 10000  # 检测连接池中空闲连接的周期（毫秒）  time-between-eviction-runs-millis: 60000  # 空闲连接的最小存活时间（毫秒）  min-evictable-idle-time-millis: 300000  # 用于验证连接是否有效的 SQL 查询  validation-query: SELECT 1 FROM DUALfeign:  hystrix:  enabled: false  okhttp:  enabled: true  client:  config:  default:  connectTimeout: 60000  readTimeout: 60000  # NONE BASIC HEADERS FULL  loggerLevel: NONE ribbon:  ConnectTimeout: 10000  ReadTimeout: 10000  MaxAutoRetries: 0  MaxAutoRetriesNextServer: 0# ----------------- swagger --------------------------- swagger:  enable: false # ----------------- 打印请求参数 --------------------------- web-log-filter:  enable: false# ----------------- mybatis-plus --------------------------- mybatis-plus:  mapper-locations: classpath*:mapper/**/*.xml #实体扫描，多个package用逗号或者分号分隔  type-aliases-package: com.yxt.payment.dao  configuration:  map-underscore-to-camel-case: true  cache-enabled: false # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl# ----------------- 自动主从 -------------------------- yxt-mybatis:  auto-master-slave:  enable: false# ----------------- RocketMQ --------------------------- #mq: # topic: # inner: YXT_BASIS_DOMAIN_EVENT_LOCAL_ZB # outer: YXT_BASIS_DOMAIN_EVENT_OUTER_LOCAL_ZB # #监听任务MQ # task: ASSIST_TASK_DOMAIN_EVENT_OUTER_ZB #xrmq: # namesrvAddr: **********:9876;**********:9876 # tags: '*' # disabled: false # reconsumeTimes: 5 # producerGroup: PGROUP_YXT_BASIS #rocketmq: # consumer: # # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值 # pull-batch-size: 10 # name-server: **********:9876;**********:9876 # ----------------- XXL JOB --------------------------- #xxl: # job: # admin: # addresses: # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册； # executor: # appname: # 执行器 AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册 # address: # ip: # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"； # port: # ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口； # logpath: ${user.dir}/logs # logretentiondays: # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能； xxl:  job:  admin:  addresses: [http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/)  executor:  appname: yxt-payment  logpath: /data/k8s/logs/applogs/xxl-job/jobhandler/  logretentiondays: -1  accessToken: sk_token #---------------- 默认健康检测不进行所有的依赖项健康检测 ---------------- management:  health:  defaults:  enabled: false # 默认健康检测不进行所有的依赖项健康检测  endpoints: # 配置暴露和屏蔽的端点  web:  exposure:  include: ["*"] # 暴露所有端点  endpoint:  health:  show-details: "ALWAYS" # 显示所有健康检查信息#-------------------grey------------------------ grey:  enable: false logging:  level:  root: warn  com.baomidou.mybatisplus: warn  org.springframework: warnyxt:  basis:  open-sdk:  appId: "e1403fb50bb637ab"  secretKey: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQD3X/riXyKLGXqK3ZYIYrX/zDrMYI9lgzBNQNdEQufaWzL1igdXRyCBm5JQyFrI4vyw4o5vv8dPKozd12yKkrffHIPaOohBs27t9ri+UpSw9xupByDk32k/t1hQRCaf0isfX/Y6zMJ8u3qUSXOTSoDAYVpd7J/z/Yt3WOcQlSbu6vSK8jB5dJqs9BPSXvEJWg1NrKQ+SQqJh/YuoeFDgRHm0kOpxausL9JEZwqIa8VfCQM2UA/ew6zzANc/NAsgBQev7wsefWSFi7Tsy8i12KhipMFy37kqoVZG50X/mpyMwqjZrjitwgVcfOgCDCWdSJJF7aJxXKUxlxQFsrBoGiY9AgMBAAECggEBAII0S7pg9q6eb4iuDy1Ev6TvGze9z1xQC9ltuqCcUqr0+8KPr0N6FZsChqXsfxRjnTuBVBVvrPl3XQPbQq9BgHR37tKrolJqnoW0KhNLJiZrOmziaqbtDNnTqDE/XdEbb0UpXlIeunOqvl9Utfed7FzWIRl01dA38POVoLFywDZZ/gi6HNA13GIqr5Llu4ZOPbAopawMAZcXoawxRneobA5IPXhUY+zNXQEblX3iUWaKVkDpQPGoNQPDU9Sq/R9OhwS86soylhyp9sRU7XsKKnBov4m2DWAAejYxOk578E0/3K/Mg6NWBkHmnKr4jUf+byT6nEjV3o+Q3CEqSuBYxSECgYEA/LmFDq2CDGnCE16ZNjRPIegvJgwa/6UfZsdsbMF+JCqWB+Kq7t+37jY+s71NfeWeJr4C/Yn74SD7g3jwjWsDpVg3AGCLNcyjeg1VYvI1/vREXchAo4ORv/1iu6dBumK7ywU8P285U2aGV8IpUsqj+sexKY2prTQ7lVLadJxGLOUCgYEA+pS2BUnvXDC5GEU+3Znb57wYVWVHQXBDLjTzozYEeXO9vt83rc5dta7gsja42WqfBSUDxZhMhbYCkct7cTjdCCnmXp/0GEzs+97ofNCxNEQLLXcAXuD4jK+2zx0V9Ywg7ksAAgR223JgOYMA04QxnI0pk1Iv7yPi0X+kO1TSVnkCgYEAg2CZ9/Bg270aw+D4esIkpPbDk97VJjKbqV1gUwUfYwO0bLL7syQfxLj0TZHKN/lnBrLecRkLFJSFy2nSe2G3SnwaU5rco4IXNbq6ua46PaiT5cDVQWuDGDBL5EtlktQC1d6J3Fwgi+ePTQ3FV6G6LwMiaPyYyU6O7uhcYiRfIrUCgYBj/v5YQ/XyGztyFQgOVLzt0CEBywXRdmRIxzbG1eKZOPfqeJd2k5jPzPa8B5xinurQmcHUk39OCKEj30jmmOOTw2y8LCazn9ceeAjc7kcY7WJshUWvlyV4txJDun6t23q0vVdGdtObpRwDN2UYA3Ps3mRN9wlL7E0yfzZhuuqpOQKBgFcxwAsc16HMJLLqUaQUKHRo1UgMlvWAb6P9UjcxSBK0z/0l8PVW3s56H/C7ntQjilz+OJnfAQublHuSrFEzKXV5UfBOhGq8Ksf57oUZduWVWDOs7ULX+B/hpLEgkeTAll/jNcILpxmzXJXYy3sv6nCnwHQ3xFrC5SOcldks7nR5"# d-erp 请求配置 erp:  appTokenUrl: [https://derp.hxyxt.com/ierp/api/getAppToken.do](https://derp.hxyxt.com/ierp/api/getAppToken.do)  accessTokenUrl: [https://derp.hxyxt.com/ierp/api/login.do](https://derp.hxyxt.com/ierp/api/login.do)  queryPayRecordUrl: [https://derp.hxyxt.com/ierp/kapi/v2/iscb/route/script_assistant_24_b2bjm_out_query_pay_reccord](https://derp.hxyxt.com/ierp/kapi/v2/iscb/route/script_assistant_24_b2bjm_out_query_pay_reccord)  appId: HybrisB2C  appSecret: HybrisB2C-09JsFewMi4JzUb6E/t4/5w==  accountId: 1430558998880323584  user: ***********  userType: UserName  #超时时间毫秒  timeout: 60000 |  |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  | # 支付中台  - id: yxt-payment  uri: [lb://yxt-payment](lb://yxt-payment)  predicates:  - Path=/yxt-payment/**  filters:  - StripPrefix=1 | 添加支付中台路由配置 |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 退款中补偿查询 |  | compensateRefundStatus | 0 0 * * * ? |  |  |  |
| 过期任务进行关闭 |  | compensateCloseStatus | 0 0 * * * ? |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
| yxt-payment | 余额支付 | 全新服务上线，如果有问题，解决问题后重新发布 |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 complete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |