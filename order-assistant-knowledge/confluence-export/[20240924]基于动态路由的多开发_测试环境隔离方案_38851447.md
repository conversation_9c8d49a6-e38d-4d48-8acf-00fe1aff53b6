# [20240924]基于动态路由的多开发/测试环境隔离方案

 true未命名绘图falseautotoptrue12421

### 多版本服务路由方案

前端

 jenkins打包时，可填入需求标识，作为前端包相对存放路径和传入前端的打包参数。

 eg： [https://test-merchants.hxyxt.com/cloud/o2o/order/manage](https://test-merchants.hxyxt.com/cloud/o2o/order/manage)

 [https://test-merchants.hxyxt.com/版本标识/cloud/o2o/order/manage](https://test-merchants.hxyxt.com/cloud/o2o/order/manage)

前端请求

 通过打包参数动态往接口中拼接标识路径。不需要前端添加路由header

 eg: [https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/store/bill/config/selectByOrganizationCode](https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/store/bill/config/selectByOrganizationCode) 

 [https://test-merchants.hxyxt.com/businesses-gateway/版本标识/dscloud/1.0/ds/store/bill/config/selectByOrganizationCode](https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/store/bill/config/selectByOrganizationCode)

网关处理

 识别出版本标识，添加路由header

服务端

 jenkins打包时，可填入需求标识，作为启动参数启动容器，服务启动后将需求标识注册到 nacos 服务的meta中，或拼接到应用名后缀，作为路由时的识别标识

### 表数据隔离和数据合并

对于有表升级，数据结构升级导致数据或数据结构不兼容时，需要对mysql表进行数据隔离。

多版本数据复制： 对于不兼容的表直接复制一个新版本出来，数据保持和原来一致，在复制出来的表上做数据结构修改和数据修改。复制的表，表面上含有版本标识。

动态变更操作的数据表： 需要动态切换的表，通过启动参数传入应用中，应用进行动态替换sql表面（需要支持分表插件，分库插件，多数据源插件）

### redis数据隔离

同表数据隔离相似。

### mongoDB数据隔离

同表数据隔离相似。

### ES数据隔离

同表数据隔离相似。

### 配置文件隔离

如果测试版本无法兼容配置文件时，需要进行配置文件隔离。

在项目部署前需要建立好对应的配置文件

jenkins打包时选择了合适的环境参数，通过启动参数传入，根据环境参数读取对应的apollo配置文件。

### 三方回调

三方回调地址通过三方后台配置，根据不同的需求，配置不同的回调地址，由于各平台配置方式不一，需要针对不同的平台进行多环境配置。

### MQ消息路由

消息接收

 由于需要在同一个MQ-Server中进行的不同版本Server，所以需要对TOPIC也进行版本隔离；

 版本信息通过启动参数传入应用中，注册TOPIC是自动动态修改TOPIC，发送消息时也进行动态修改TOPIC；