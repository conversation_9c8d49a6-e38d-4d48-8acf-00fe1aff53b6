# 24年第19周2024-05-24

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参  5月24日进入开发阶段 | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审4月26日进入开发阶段5月17日抖店开发完成，待部署测试5月22日下班前提测，测试中 |  | 下一月的目标：支付宝 + 饿了么 |
| 3 | 优雅发布升级 |  | 4月12日完成待办列表,推动全部门升级 | Done | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  | 已完成版本升级, 周一已经部署到开发、测试环境20240522已完成 | Done | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档 | 暂停 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | 支付中台重构 |  |  | 暂停 |  |
| 8 | Rocketmq |  |  | Done | 规范: 梳理: [历史MQ梳理](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24623077&draftShareId=870cf35b-004e-4bed-8ae3-1a35f3cf2173&) |
| 9 | [需求池](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgFQpYWtHhRbWSdBxb5u?scode=AOsAFQcYAAcFpJng4uAboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 10 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 11 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 12 | [2024Q2-交易生产组](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgA9Okm682QqKMEwhssg?scode=AOsAFQcYAAc3lcl7J9AboAOAYLADg&tab=9cjz4i) |  |  |  |  |


### 二、本周工作情况

**1.总览**

本周项目目标


1 .对接极兔/邮政 5.23第二版提测 @焦钰斌 done

2. 美团隐私/京东下账调整 5.21上线 @王世达 done

3. B2C员工绩效返利 5.24提测 @杨国枫 5-27 提测

4. 科传下账 5.21上线 @王世达 done

5. B2C下账失败 5.23上线 @李洋 done

6.B2C流程及界面优化 5.27技术方案评审 @王世达 

7. 订单优化2 5.27PRD评审 @徐凯@焦钰斌

8. 吉客云 暂停

9. 支付宝 暂停

本周其他目标

[1.mq](http://1.mq)规范调整 国华 done
2.网关分流本周内完成 润康 done
3.整理Q2绩效表 国华 done
4.middleid 改造适配pre 国枫 done
5.组织结构冗余 第一步 志明 done
6.广播模式mq迁移 国枫 



**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1.B2C推广开发 自测联调2.plantuml快速入门3.原新增退款单流程梳理 以及形成plantuml 图4.middle-id预发环境问题 | **计划工作**1.B2C推广开发 自测联调2.plantuml快速入门3.原新增退款单流程梳理 以及形成4.middle-id预发环境问题**㊁实际完成**1.B2C推广开发 自测联调2.plantuml快速入门3.middle-id预发环境问题**㊂遗留问题****1.B2C推广配合测试上线**2.原新增退款单流程梳理 以及形成**㊃风险问题** **㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 2 | 杨润康 | **本周总工时：5d**1. 网关处理 3d   1. businesses-gateway网关版本升级,已上线   2. 4个网关路由禁用/actuator/shutdown,已上线   3. 网关分流(api-gateway-athena/hades),已上线   4. businesses-gateway网关临时处理maxInMemorySize无效问题,已上线   5. 业务网关上传请求体大小指标数据,通过prometheus展示,开发完成 2. businesses-gateway网关版本升级,已上线 3. 4个网关路由禁用/actuator/shutdown,已上线 4. 网关分流(api-gateway-athena/hades),已上线 5. businesses-gateway网关临时处理maxInMemorySize无效问题,已上线 6. 业务网关上传请求体大小指标数据,通过prometheus展示,开发完成 7. 创单服务梳理 1.5d 8. 营销对接线下单,配合,新增字段开发 0.5d | **㊀计划工作** **㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 3 | 杨俊峰 | **本周总工时：5day****1.**产线现有问题修复 （骑手位置上传、加密字段问题）1day**2.**打印联调 1.5day**3.**api迁移项目介入。熟悉代码和网点接口编码 2day | **㊀计划工作****㊁实际完成****㊂遗留问题** **㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 4 |  | **本周总工时：4day**1. **refund_order表冗余机构信息** 2. **.net重构-部署+提测** | **㊀计划工作****㊁实际完成**1. refund_order表冗余机构信息（测试中，下周二可上线） 2. .net重构-部署+提测（测试中，争取月底前上线） **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 |  | **本周总工时：5pd**1. B2C商品推广开发与联调 2. DDD重构梳理代码 3. 线上问题支持 | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：5pd**1.模板配置接口对接（物流单/发货单）2.线上极兔和邮政快递面单打印测试3.平台、快递和模板上传线上服务器，生成url和更新脚本4.线上模板数据迁移的脚本生成5.店铺设置物流公司提测后bug修改6.加密处理面单的指定内容 | **㊀计划工作**1.接口联调完成，功能提测2.线上生成极兔面单和成功打印3.线上数据迁移的脚本生成**㊁实际完成**1.接口联调完成，功能提测2.线上生成极兔面单和成功打印3.线上数据迁移的脚本生成**㊂遗留问题**1.线上面单数据存在相同店铺有两条配置，需要产品界定出需要保留的面单数据2.dev环境执行生成的脚本，核对数据**㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 7 |  | **本周总工时：5d******1. 科传下账修改 已上线 2. 美团隐私-平台配送信息解密 已上线 3. B2C京东订单收货人手机密文问题处理。 已上线 4. v1.1.17 B2C退款/取消流程优化需求评审+技术方案 处理中5. 订单路由问题处理及优化 已修改完成，下周一与前端联调6. 订单问题处理 a. 海典h1宕机，心云已下账，海典h1缺失订单，整理重推 已完成 b. 京东订单运费问题追踪 抛给产品定修改方案 c. 报警日志查看 | **㊀计划工作****㊁实际完成******1. 科传下账修改 已上线 2. 美团隐私-平台配送信息解密 已上线 3. B2C京东订单收货人手机密文问题处理。 已上线**㊂遗留问题****v1.1.17 B2C退款/取消流程优化需求技术方案评审****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 8 |  | **本周总工时：** | **㊀计划工作****㊁实际完成****㊂遗留问题** **㊃风险问题** **㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等** |  |
| 9 |  | **本周总工时：5pd**1. B2C/云仓商品 邮政对接/极兔对接2期开发完成 2. B2C/云仓商品 邮政对接/极兔对接2期联调完成 3. B2C/云仓商品 邮政对接/极兔对接2期提测bug修复 | **㊀计划工作**B2C/云仓商品 邮政对接/极兔对接2期订单优化评审**㊁实际完成****B2C/云仓商品 邮政对接/极兔对接2期以提测**order-web 开发阶段启动优化**㊂遗留问题****订单优化评审** | **㊀需求研发相关** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |