# 1.对外订单结构文档

# 一、订单核心模型

/** 

*包含: 核心实体 
*正向-平台单/心云订单/发货单/下账单 
* 逆向-平台单/售后单/退货单/退款单/下账单
*仅展示外部部门需要关注的核心字段
**/



## 1.单据周期概览

# true新模型-生命周期false600autotoptrue66611

## 2.单据ER关系 true新模型-订单ER图false600autotoptrue223924

# 二、对外暴露字段清单

## 1.主订单信息

| **字段名** | **类型** | **是否必填** | **业务说明** |
| --- | --- | --- | --- |
| order_no | string(50) | 是 | 全局唯一订单号（示例：XD202307150001） |
| transaction_type | string(20) | 是 | 交易类型：online（线上）/offline（线下） |
| business_type | string(20) | 是 | 业务类型 O2O、B2C、B2B |
| company_code | string(20) | 是 | 分公司编码 |
| organization_code | string(20) | 是 | 所属机构编码例如 A002 D100 |
| lock_for_world | int(5) | 是 | 全局拦截锁,0正常 ,非0都需要拦截 |
| order_main_status | string(20) | 是 | 订单状态 创单：CREATED; 待接单：WAIT; 待接单：WAIT; 已接单: Accepted; 待二审审核: WAIT_VERIFY ; 待拣货: WAIT_PICK ; 已拣货: Picked; 待发货: WAIT_DELIVERY ; 部分发货: PART_DELIVERY ; 已发货： ALL_DELIVERY; 运输中: SHIPPED ; 待自提： WAIT_SELF_PICK; 已签收 Received; 完成：DONE ; 已取消：Canceled; |
| payment_status | string(20) | 是 | 订单支付状态 未支付: UNPAY ; 部分支付: PART_PAY; 已支付: PAID ; 支付失败: PAY_FAIL |
| abnormal_type | string(50) | 非 | 异常类型 payment_failure（支付失败）,erp_info_err（商品信息错误）,out_of_stock（缺货）,delivery_anomal (配送异常),system_integration_err(系统集成异常),order_data_err(订单数据异常) |
| order_type | string(20) | 是 | 订单类型.待规划值 |
| order_tag_segment | string(20) | 是 | 订单标记段,每3位代表一个含义详细见字典 |
| source_biz_code | string(20) | 是 | 来源业务线 如POS,ASSIST |
| source_scene | string(20) | 是 | 来源场景 |
| source_channel | string(20) | 是 | 来源渠道,待产品定义 |
| source_device | string(20) | 是 | 来源端 如 PC,APP,POS |
| user_id | varchar(50) | 非 | 会员id |
| actual_pay_amount | decimal | 是 | 实付金额 |
| actual_collect_amount | decimal | 是 | 实收金额 |
| created | datetime | 是 | 创单时间 |
| pay_time | datetime | 非 | 支付时间 |
| bill_time | datetime | 非 | 下账时间 |
| complete_time | datetime | 非 | 完成时间 |


## 2.金额信息

| **字段名** | **类型** | **是否必填** | **业务说明** |
| --- | --- | --- | --- |
| actual_pay_amount | decimal | 是 | 实付金额 |
| actual_collect_amount | decimal | 是 | 应收金额 =累加明细 数量*original_price-merchant_orderDiscount_amount-merchant_commodityDiscount_amount |
| brokerage_amount | decimal | 是 | 交易佣金 |
| total_amount | decimal | 是 | 商品总金额 |
| delivery_amount | decimal | 是 | 应收配送费 |
| pack_amount | decimal | 是 | 应收包装费 |
| merchant_pack_amount | decimal | 是 | 商家包装费 |
| merchant_delivery_amount | decimal | 是 | 商家配送费 |
| merchant_delivery_discount_amount | decimal | 是 | 商家配送费优惠 |
| merchant_order_discount_amount | decimal | 是 | 商家订单级总优惠 =merchant_deliveryDiscount_amount+累加明细 merchant_order_discount_share |
| merchant_commodity_discount_amount | decimal | 是 | 商家商品总优惠=累加 merchant_order_discount_share+merchant_goods_discount_amount |
| platform_pack_amount | decimal | 是 | 平台包装费 |
| platform_delivery_amount | decimal | 是 | 平台配送费 |
| platform_delivery_discount_amount | decimal | 是 | 平台配送费优惠 |
| platform_order_discount_amount | decimal | 是 | 平台订单级优惠汇总 = platform_delivery_discount_amount+ 累加明细 platform_order_discount_share |
| platform_commodity_discount_amount | decimal | 是 | 平台商品优惠金额 =累加明细 platform_order_discount_share+platform_goods_discount_amount |
| medicare_amount | decimal | 是 | 医保金额 |
| remain_brokerage_amount | decimal | 是 | 剩余交易佣金(实时) |


## 3.用户信息

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| user_id | string(50) | 用户唯一标识 |
| user_name | string(255) | 用户姓名（隐私字段需授权获取） |
| user_tag | string(50) | 会员身份标记 |
| user_card_no | string(50) | 会员卡号 |
| receiver_code | string(50) | 收货方编码 |
| receiver_name | string(50) | 收货人 |
| receiver_phone | string(50) | 收货人手机 |
| province | string(50) | 省份 |
| city | string(50) | 城市 |
| district | string(50) | 区 |
| address | string(50) | 详细地址 |


## 4.商品信息

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
|  | JSON Array | 商品清单（结构见下方示例） |
| order_detail_no | string(50) | 内部明细编号,自己生成 |
| third_order_detail_no | string(50) | 平台订单明细编号 |
| platform_sku_id | string(50) | 商品三方平台编码 |
| row_no | string(50) | 商品行号 |
| erp_code | string(20) | 商品编码 |
| erp_name | string(255) | 商品名称 |
| commodity_count | decimal | 商品数量 |
| status | string(20) | 明细状态 NORMAL-正常 exchange-换货 exchanged-换货后 |
| abnormal_type | string(50) | erp_info_err（商品信息错误）,out_of_stock（缺货） |
| swap_no | string(50) | 仅换货有值.换货后的内部明细编号 |
| gift_type | string(10) | 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品 |
| set_type | string(10) | 套装类型 TRUE-套装子商品 ;FALSE 非套装 |
| order_set_detail_no | string(50) | 订单套装明细唯一编码,仅套装时有值 |
| original_price | decimal | 商品原单价 |
| price | decimal | 商品售价 |
| commodity_cost_price | decimal | 商品成本价 |
| total_amount | decimal | 商品总额=price*数量 |
| actual_pay_amount | decimal | 实付金额 (已乘数量) |
| discount_share | decimal | 订单级优惠分摊 |
| discount_amount | decimal | 商品级折扣金额 |
| merchant_order_discount_share | decimal | 商家订单级优惠分摊(已乘数量) |
| platform_order_discount_share | decimal | 平台订单级优惠分摊 (已乘数量) |
| merchant_goods_discount_amount | decimal | 商家商品级折扣金额 (已乘数量) |
| platform_goods_discount_amount | decimal | 平台商品级折扣金额 (已乘数量) |
| merchant_order_discount_share_price | decimal | 商家订单级优惠分摊(单个数量) |
| platform_order_discount_share_price | decimal | 平台订单级优惠分摊 (单个数量) |
| merchant_goods_discount_amount_price | decimal | 商家商品级折扣金额 (单个数量) |
| platform_goods_discount_amount_price | decimal | 平台商品级折扣金额 (单个数量) |
| input_tax_code | string(10) | 进项税编码 |
| input_tax | decimal | 进项税率 |
| output_tax_code | string(10) | 销项税编码 |
| output_tax | decimal | 销项税率 |
| posted_cost_with_tax_price | decimal | 过账含税成本价 |
| posted_cost_price | decimal | 过账成本价 |
| posted_cost_tax | decimal | 过账税率 |
| is_on_promotion | string(10) | 是否参加促销 TRUE、FALSE |
| detachable | string(10) | 是否拆零是买 true,false |
| commodity_spec | string(255) | 商品规格 |
| manufacture | string(255) | 生产商 |
| five_class | string(64) | 商品五级分类编码 |
| five_class_name | string(64) | 商品五级分类Name |
| main_pic | string(255) | 商品图片 |
| saler_id | string(50) | 售货员Id |
| saler_name | string(50) | 售货员Name |
| mic_mark | string(10) | 医保商品标识. TRUE、FALSE |


# 三、状态流转说明

## 1.订单主状态order_main_status

| 枚举值 | 描述 |
| --- | --- |
| CREATED | 创建订单 |
| WAIT | 待接单 |
| ACCEPTED | 已接单 |
| WAIT_VERIFY | 待二审审核 |
| WAIT_PICK | 待拣货 |
| PICKED | 已拣货 |
| WAIT_PICKUP | 待揽收 |
| PART_DELIVERY | 部分发货 |
| ALL_DELIVERY | 已发货 |
| WAIT_SELF_PICK | 待自提 |
| RECEIVED | 已签收 |
| DONE | 已完成 |
| CANCELED | 已取消 |


## 2.发货单状态delivery_order_status

| 枚举值 | 描述 |
| --- | --- |
| WAIT_CONFIRM | 待确认 |
| WAIT_DELIVERY | 待发货 |
| SHIPPING | 运输中 |
| WAIT_PICKUP | 待自提 |
| RECEIVED | 已签收 |


## 3.售后单状态after_order_status

| 枚举值 | 描述 |
| --- | --- |
| WAIT_VERIFY | 待审核 |
| PASSED | 审核通过,处理中 |
| COMPLETE | 售后完成 |
| REJECTED | 售后拒绝 |
| CANCELED | 售后取消 |


## 4.退货单状态return_order_status

| 枚举值 | 描述 |
| --- | --- |
| WAIT_RETURN | 待退货 |
| RETURN_SHIPPED | 退货运输中 |
| COMPLETED | 退货验货通过 |
| REJECTED | 退货拒绝 |


## 5.退款单状态refund_order_status

| 枚举值 | 描述 |
| --- | --- |
| WAIT_VERIFY | 待审核 |
| REVIEW_REJECT | 审核驳回 |
| WAIT_REFUND | 待退款 |
| REFUNDED | 已退款 |


# 四、SDK数据接口说明

## 1.SDK对接指南

见文档 : SDK对接指南-废弃

## 2.SDK接口说明

见文档 : 待补充

举例:
**1.订单查询接口**

**Endpoint**: `/api/v1/orders/{order_no}`
**Method**: GET
**请求示例**：

javacurl -X GET "https://api.trade.com/orders/XD202307150001" \
  -H "Authorization: Bearer {token}"

```

```

**响应示例**：

java{
  "code": 200,
  "data": {
    "order_no": "XD202307150001",
    "status": "DELIVERING",
    "amount": {
      "total": 158.60,
      "pay": 158.60
    },
    "user_info": {
      "user_id": "U1234****",
      "delivery_address": {/* 地址结构 */}
    },
    "skus": [/* 商品清单 */]
  }
}

# 五、敏感数据处理规则

## 1.用户隐私字段:

- user_id 返回脱敏值 (如:U1234****)
- 待补充


# 六、常见问题解答

### Q1：如何判断订单是否完成？

- 检查`order_status`是否为`COMPLETED`
- 终态订单数据将保留3年


### Q2：

### Q3：

# 七、联系支持

| **问题类型** | **对接人** | **联系方式** |
| --- | --- | --- |
| 数据字段释义 | 技术接口人: 杨润康 | 电话: 13955196045 |
| 紧急生产问题 | 值班工程师:[假日值班文档](https://doc.weixin.qq.com/sheet/e3_AR8AOAYLADgqprSmmz0Qh67ISAeTJ?scode=AOsAFQcYAAcGgAtjrIAboAOAYLADg&tab=qj6zib) | 电话：17710036783 |
| 新需求接入 | 沟通:徐国华/王梦君 | 企微群：交易组-外部对接 |