# 【20240410】支付宝小程序订单接入

## 一、 背景

### 1.1 业务背景

### 1.2 痛点分析

需要把支付宝小程序订单接入到OMS，实现OMS对订单的统一管理

### 1.3 系统现状

## 二、 需求分析

### 2.1 业务流程

**支付宝小程序接入 (axshare.com)**

## 三、 目标

### 3.1 本期目标

#### 3.1.1 业务目标

完成支付宝小程序订单的接入

#### 3.1.2 技术目标

### 3.2 中长期目标

## 四、整体设计

### 4.1 流程图

true支付宝对接泳道图falseautotoptrue6013

## 五、 详细设计

### 1、 接口设计

**1.1 新订单接收消息接口（oms提供）**

url: /order/neworder/v1

请求体：

| **字段** | **必填** | **类型** | **说明** |
| data | 是 | string | 订单信息的json数据 |
| sign | 是 | STRING | 对于传输参数的签名，保证数据完整 |


data的json格式如下

| **字段** | **必填** | **类型** | **说明** |
| ORDER | 是 | List<order> | 订单主表信息,list长度为1 |
| ORDERCODE | 是 | STRING | 订单编号 |
| STORECODE | 是 | STRING | 门店编码 |
| ORDERFROM | 是 | STRING | 订单来源。传值需与OMS确认 |
| ORDERINDEX | 是 | STRING | 当日订单流水号，从1开始 |
| BUYERNAME | 是 | STRING | 买家姓名 |
| ORDERTYPE | 是 | STRING | 订单类型。1：O2O，2：B2C,3：服务类型，4：.门店B2C，6：社区团购 |
| ORDERSTATUS | 否 | STRING | 订单状态，默认为待接单（32） |
| PAYTYPECODE | 是 | STRING | 支付方式编码（商城的编码） |
| PAYTYPE | 是 | STRING | 付款方式（明文） |
| TRANTYPE | 是 | STRING | 配送方式（目前仅支持 到店自提 ，门店配送，均为明文） |
| RECEIVEADDRESS | 是 | STRING | 收货地址，包含省市区信息 |
| CREATETIME | 是 | STRING | 下单时间（yyyy-MM-dd hh:mm:ss） |
| RECEIVERNAME | 是 | STRING | 收货人姓名 |
| RECEIVEPHONE | 是 | STRING | 收货人手机号 |
| RECEIVETEL | 否 | STRING | 收货人电话 |
| RECEIVEPROVINCE | 是 | STRING | 收货省份（明文） |
| RECEIVECITY | 是 | STRING | 收货城市（明文） |
| RECEIVEAREA | 是 | STRING | 收货地区（明文） |
| LAT | 是 | STRING | 收货人纬度 |
| LNG | 是 | STRING | 收货人经度 |
| BUYERNOTES | 是 | STRING | 买家备注 |
| PAYTIME | 是 | STRING | 支付时间（yyyy-MM-dd HH:mm:ss） |
| RECEIVEBESTTIME | 是 | STRING | 最佳收货时间（时间区间或 1小时达 类似文字。即时单时传 1小时达 或 两小时达，预订单时传入格式为2021-01-22 12:00:00---2021-01-22 13:00:00，中间分隔符为三个减号） |
| POSTFEE | 是 | STRING | 运费，单位元 |
| POITRANSCOUPON | 否 | STRING | 商家承担运费优惠，单位元 |
| REALPOSTFEE | 是 | STRING | 实际配送费（顾客支付），单位元 |
| PACKAGEFEE | 是 | STRING | 包装费，单位元 |
| SERVICEFEE | 是 | STRING | 服务费，单位元 |
| COUPONSAMOUNT | 是 | STRING | 商家商品优惠金额，单位元 |
| COUPONSAMOUNT_ORIGIN | 是 | STRING | 所有优惠合计金额，单位元 |
| PRODUCTSAMOUNT | 是 | STRING | 商品按原价计算合计金额，单位为元 |
| PRODUCTSAMOUNT_ORIGIN | 是 | STRING | 商品按原价计算合计金额，单位为元 |
| AMOUNT | 是 | STRING | 订单总额，原价总额+运费等，单位元 |
| ORDERAMOUNT | 是 | STRING | 订单总额，原价总额+运费等，单位元 |
| PAYAMOUNT | 是 | STRING | 实际支付金额，单位为元 |
| REALPAYAMOUNT | 是 | STRING | 顾客实际支付金额+平台优惠，单位为元 |
| NOTES | 否 | STRING | 卖家备注，传空即可 |
| ISPAY | 是 | STRING | 是否支付（1：已支付，0：未支付） |
| ISAUDITABLE | 是 | STRING | 订单是否可编辑（0：否，1：是） |
| ISNEEDINVOICE | 是 | STRING | 是否开具发票（0：否，1：是） |
| ISFULLREFUND | 是 | STRING | 是否整单退（0：否；1：是） |
| ISPARTSEND | 是 | STRING | 是否允许部分发货（0：否，1：是） |
| ISOTC | 是 | STRING | 是否含有处方药（0：否，1：是） |
| OTCPIC | 是 | STRING | 处方单图片链接 |
| PICKUPNUMBER | 否 | STRING | 取货码（门店自提使用） |
| DELIVERYORDER | 是 | STRING | 配送优先级排序（1达达，2蜂鸟，3美团，例：3,1,2） |
| USEPOINTS | 否 | STRING | 是否使用积分（0：否，默认值0；1：是） |
| SENDCLASS | 是 | STRING | 配送时效（分钟数，60表示一小时达，30表示半小时达，以此类推） |
| ISGROUP | 否 | STRING | 是否是拼团字段（0：否，1：是） |
| MEMBERSHIPCODE | 是 | STRING | 会员编号 |
| ISSELFDELIVERY | 是 | STRING | 是否是自配送（0：否，订单专送；1：是，订单自送，配送费下账） |
| MALLDELIVERYTYPE | 否 | STRING | 配送编码（自送可不传，专送时必填。0：自送；1：达达专送；2蜂鸟专送；4：美团专送） |
| SATELLITESTORECODE | 否 | STRING | 卫星店的门店编号 |
| PATIENTINFO | 否 | STRING | 用药人信息的json数据字符串字段信息，非处方单可传空字符串| USERNAME | 是 | String | 患者姓名 | | USERPHONE | 是 | String | 患者手机号 | | USERSEX | 是 | String | 患者性别 | | USERAGE | 是 | String | 患者年龄 | | HOSPITAL | 是 | String | 就诊医院 | | DOCTOR | 是 | String | 就诊医生 | | MEDICALDEPART | 是 | String | 就诊科室 | | CLASS | 是 | String | 处方类型：1：普通 2：慢病 | | ORDERTIME | 是 | String | 下单时间 | | DIAGNOSE | 是 | String | 诊断 | | NOTE | 是 | String | 备注 | | USERNAME | 是 | String | 患者姓名 | USERPHONE | 是 | String | 患者手机号 | USERSEX | 是 | String | 患者性别 | USERAGE | 是 | String | 患者年龄 | HOSPITAL | 是 | String | 就诊医院 | DOCTOR | 是 | String | 就诊医生 | MEDICALDEPART | 是 | String | 就诊科室 | CLASS | 是 | String | 处方类型：1：普通 2：慢病 | ORDERTIME | 是 | String | 下单时间 | DIAGNOSE | 是 | String | 诊断 | NOTE | 是 | String | 备注 |
| USERNAME | 是 | String | 患者姓名 |
| USERPHONE | 是 | String | 患者手机号 |
| USERSEX | 是 | String | 患者性别 |
| USERAGE | 是 | String | 患者年龄 |
| HOSPITAL | 是 | String | 就诊医院 |
| DOCTOR | 是 | String | 就诊医生 |
| MEDICALDEPART | 是 | String | 就诊科室 |
| CLASS | 是 | String | 处方类型：1：普通 2：慢病 |
| ORDERTIME | 是 | String | 下单时间 |
| DIAGNOSE | 是 | String | 诊断 |
| NOTE | 是 | String | 备注 |
| GOODS | 是 | List<detail> | 订单明细列表| ORDERCODE | 是 | STRING | 订单编号 | | GOODSCODE | 是 | STRING | 商品编码 | | GOODSNAME | 是 | STRING | 商品名 | | BUYCOUNT | 是 | STRING | 购买数量，int格式 | | GOODSPRICE | 是 | STRING | 商品价格（原价） | | DISCOUNTPRICE | 是 | STRING | 商品折后价（扣除优惠金额后的商品单价），取4位小数 | | SHOWPRICE | 是 | STRING | OMS中的显示价格 | | PLATPRICE | 是 | STRING | 平台价（折扣计算依据价格） | | GOODSAMOUNT | 是 | STRING | 商品实际支付金额（折后价*数量） | | ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） | | ISOTC | 是 | STRING | 是否是否处方药（0：否，1：是） | | BUYERNOTES | 否 | STRING | 买家备注 | | SELLERNOTES | 否 | STRING | 卖家备注 | | ISPART | 是 | STRING | 是否部分发货（0否，1是）组合商品和赠品应为否 | | ORDERCODE | 是 | STRING | 订单编号 | GOODSCODE | 是 | STRING | 商品编码 | GOODSNAME | 是 | STRING | 商品名 | BUYCOUNT | 是 | STRING | 购买数量，int格式 | GOODSPRICE | 是 | STRING | 商品价格（原价） | DISCOUNTPRICE | 是 | STRING | 商品折后价（扣除优惠金额后的商品单价），取4位小数 | SHOWPRICE | 是 | STRING | OMS中的显示价格 | PLATPRICE | 是 | STRING | 平台价（折扣计算依据价格） | GOODSAMOUNT | 是 | STRING | 商品实际支付金额（折后价*数量） | ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） | ISOTC | 是 | STRING | 是否是否处方药（0：否，1：是） | BUYERNOTES | 否 | STRING | 买家备注 | SELLERNOTES | 否 | STRING | 卖家备注 | ISPART | 是 | STRING | 是否部分发货（0否，1是）组合商品和赠品应为否 |
| ORDERCODE | 是 | STRING | 订单编号 |
| GOODSCODE | 是 | STRING | 商品编码 |
| GOODSNAME | 是 | STRING | 商品名 |
| BUYCOUNT | 是 | STRING | 购买数量，int格式 |
| GOODSPRICE | 是 | STRING | 商品价格（原价） |
| DISCOUNTPRICE | 是 | STRING | 商品折后价（扣除优惠金额后的商品单价），取4位小数 |
| SHOWPRICE | 是 | STRING | OMS中的显示价格 |
| PLATPRICE | 是 | STRING | 平台价（折扣计算依据价格） |
| GOODSAMOUNT | 是 | STRING | 商品实际支付金额（折后价*数量） |
| ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） |
| ISOTC | 是 | STRING | 是否是否处方药（0：否，1：是） |
| BUYERNOTES | 否 | STRING | 买家备注 |
| SELLERNOTES | 否 | STRING | 卖家备注 |
| ISPART | 是 | STRING | 是否部分发货（0否，1是）组合商品和赠品应为否 |
| FEE_DETAIL | 是 | List<fee> | 费用信息表（支付的信息和运费各单独作为一条费用信息传入）| ORDERCODE | 是 | STRING | 订单编号 | | FEECODE | 是 | STRING | 费用编码 支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY微信支付 WXPAY支付宝支付 ALIPAY | | FEENAME | 是 | STRING | 费用名称 | | AMOUNT | 是 | STRING | 费用金额，单位元 | | POICHARGE | 是 | STRING | 商家承担优惠金额 | | FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） | | PAYODERENO | 否 | STRING | 第三方支付订单号 | | PAYBATCHNO | 否 | STRING | 交易流水号 | | FEEFORGOODS | 否 | STRING | 费用对应的商品sku（可绑定某一商品上的优惠券信息） | | EXTINFO | 否 | STRING | 其他信息，优惠活动,,买赠满赠信息等 | | ORDERCODE | 是 | STRING | 订单编号 | FEECODE | 是 | STRING | 费用编码 支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY微信支付 WXPAY支付宝支付 ALIPAY | FEENAME | 是 | STRING | 费用名称 | AMOUNT | 是 | STRING | 费用金额，单位元 | POICHARGE | 是 | STRING | 商家承担优惠金额 | FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） | PAYODERENO | 否 | STRING | 第三方支付订单号 | PAYBATCHNO | 否 | STRING | 交易流水号 | FEEFORGOODS | 否 | STRING | 费用对应的商品sku（可绑定某一商品上的优惠券信息） | EXTINFO | 否 | STRING | 其他信息，优惠活动,,买赠满赠信息等 |
| ORDERCODE | 是 | STRING | 订单编号 |
| FEECODE | 是 | STRING | 费用编码 支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY微信支付 WXPAY支付宝支付 ALIPAY |
| FEENAME | 是 | STRING | 费用名称 |
| AMOUNT | 是 | STRING | 费用金额，单位元 |
| POICHARGE | 是 | STRING | 商家承担优惠金额 |
| FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） |
| PAYODERENO | 否 | STRING | 第三方支付订单号 |
| PAYBATCHNO | 否 | STRING | 交易流水号 |
| FEEFORGOODS | 否 | STRING | 费用对应的商品sku（可绑定某一商品上的优惠券信息） |
| EXTINFO | 否 | STRING | 其他信息，优惠活动,,买赠满赠信息等 |
| INVOICE | 是 | List<invoice> | 发票信息，长度为1| ORDERCODE | 是 | STRING | 订单编号 | | INVOICETYPE | 是 | STRING | 发票类型（普通发票或增值费发票）明文 | | TITLE | 是 | STRING | 发票抬头 | | MODE | 是 | STRING | 纸质发票 或 电子发票 | | SIGNAL | 是 | STRING | 开票人表示（个人/公司）明文 | | PURCHASERNAME | 是 | STRING | 购买人姓名（可为空） | | TAXPAYERID | 是 | STRING | 纳税人识别号 | | ORDERCODE | 是 | STRING | 订单编号 | INVOICETYPE | 是 | STRING | 发票类型（普通发票或增值费发票）明文 | TITLE | 是 | STRING | 发票抬头 | MODE | 是 | STRING | 纸质发票 或 电子发票 | SIGNAL | 是 | STRING | 开票人表示（个人/公司）明文 | PURCHASERNAME | 是 | STRING | 购买人姓名（可为空） | TAXPAYERID | 是 | STRING | 纳税人识别号 |
| ORDERCODE | 是 | STRING | 订单编号 |
| INVOICETYPE | 是 | STRING | 发票类型（普通发票或增值费发票）明文 |
| TITLE | 是 | STRING | 发票抬头 |
| MODE | 是 | STRING | 纸质发票 或 电子发票 |
| SIGNAL | 是 | STRING | 开票人表示（个人/公司）明文 |
| PURCHASERNAME | 是 | STRING | 购买人姓名（可为空） |
| TAXPAYERID | 是 | STRING | 纳税人识别号 |


请求体实例：

| data={  "INVOICE": [],  "FEE_DETAIL": [  {  "PAYBATCHNO": "",  "FEETYPE": "1",  "AMOUNT": "21.00",  "PAYODERENO": "",  "FEECODE": "WXPAY",  "FEENAME": "微信支付",  "ORDERCODE": "20201203093950002"  }  ],  "ORDER": [  {  "AMOUNT": "21.00",  "ORDERCODE": "20201203093950002",  "ISOTC": "1",  "BUYERNOTES": "1",  "PAYTIME": "",  "STORECODE": "AW09",  "PAYAMOUNT": "21.00",  "PAYTYPECODE": "offline",  "LNG": "0",  "REALPAYAMOUNT": "21.00",  "RECEIVECITY": "1",  "COUPONSAMOUNT": "0.00",  "NOTES": "1",  "PACKAGEFEE": "0.00",  "ORDERTYPE": "1",  "ISPAY": "1",  "ISAUDITABLE": "0",  "ISNEEDINVOICE": "0",  "SERVICEFEE": "0.00",  "ORDERFROM": "101",  "ORDERAMOUNT": "21.00",  "CREATETIME": "2020-12-03 08:43:46",  "ORDERSTATUS": "32",  "PAYTYPE": "微信支付",  "TRANTYPE": "门店配送",  "RECEIVERNAME": "张三",  "POSTFEE": "0.00",  "REALPOSTFEE": "0.00",  "RECEIVEAREA": "1",  "BUYERNAME": "张三",  "RECEIVEPHONE": "15010001000",  "RECEIVETEL": "15010001000",  "LAT": "0",  "ORDERINDEX": "1",  "ISPARTSEND": "0",  "SENDCLASS": "30",  "ISFULLREFUND": "1",  "PICKUPNUMBER": "",  "OTCPIC": "[http://storage.jd.com/diansongb-rx-img/test.jpg,http://***********:7001/statics/2020/12/10/7924858b1c5fb5851eb5dfaf714a7f21.jpg](http://storage.jd.com/diansongb-rx-img/test.jpg,http://***********:7001/statics/2020/12/10/7924858b1c5fb5851eb5dfaf714a7f21.jpg)",  "DELIVERYORDER": "1,2,3",  "RECEIVEBESTTIME": "一小时达",  "PRODUCTSAMOUNT": "21.00",  "COUPONSAMOUNT_ORIGIN": "0.00",  "POITRANSCOUPON": "0.00",  "RECEIVEADDRESS": "1",  "RECEIVEPROVINCE": "1",  "PRODUCTSAMOUNT_ORIGIN": "21.00",  "ISSELFDELIVERY": "0",  "PATIENTINFO": "{\"USERNAME\":\"\\u5f6d\\u6e58\\u5dfd\",\"USERPHONE\":\"18287704540\",\"USERSEX\":\"\\u672a\\u77e5\",\"USERAGE\":\"\",\"HOSPITAL\":\"\",\"DOCTOR\":\"\",\"MEDICALDEPART\":\"\",\"CLASS\":\"1\",\"ORDERTIME\":\"2021-01-21 15:22:52\",\"DIAGNOSE\":\"\",\"NOTE\":\"\"}"  }  ],  "GOODS": [  {  "ORDERCODE": "20201203093950002",  "GOODSCODE": "154977",  "GOODSNAME": "三维博锐 蔡司三维博锐焕色视界渐进镜树脂定制镜片_1.6灰色钻立方铂金膜 片",  "GOODSPRICE": "21.00",  "BUYCOUNT": "1",  "DISCOUNTPRICE": "21.00",  "SHOWPRICE": "21.00",  "PLATPRICE": "21.00",  "GOODSAMOUNT": "21.000",  "ISGIFT": "0",  "ISOTC": "1",  "SELLERNOTES": "",  "BUYERNOTES": "",  "ISPART": "0"  }  ] }&sign=DgpQmg9nZk3mancfNi4aMg-- |


返回体：

| **参数** | **类型** | **描述** |
| Code | string | 成功标识，0成功；其他值表示写入失败 |
| Msg | string | 描述信息，成功时为succ；失败时是错误信息 |


返回体实例：

| {  "Code": "0",  "Msg": "succ" } |


**1.2 订单取消消息接口（oms提供）**

url: /order/cancel/v1

请求体：

| **字段** | **必填** | **类型** | **说明** |
| ordercode | 是 | string | 订单编号 |
| sign | 是 | STRING | 对于传输参数的签名，保证数据完整 |


请求体示例：

| ordercode=12345&sign=111 |


返回体：

| **参数** | **类型** | **描述** |
| Code | string | 成功标识，0成功；失败时为50001 |
| Msg | string | 描述信息,失败时是错误信息 |


返回体示例：

| {  "Code": "0",  "Msg": "取消成功" } |


**1.3 订单退款消息接口（oms提供）**

url: /order/refund/v1

请求体：

| **字段** | **必填** | **类型** | **说明** |
| data | 是 | string | 订单信息的json数据 |
| sign | 是 | STRING | 对于传输参数的签名，保证数据完整 |


data的json格式如下

| **字段** | **必填** | **类型** | **说明** |
| RETURNORDER | 是 | List<returnorder> | 订单主表信息,list长度为1 |
| APPLYNAME | 是 | STRING | 申请人姓名 |
| APPLYPHONE | 是 | STRING | 申请人手机号 |
| CREATETIME | 是 | STRING | 退单创建时间（yyyy-MM-dd hh:mm:ss） |
| ISFULLREFUND | 是 | STRING | 是否是整单退（0：部分退，1整单退） |
| LAT | 是 | STRING | 经度 |
| LNG | 是 | STRING | 纬度 |
| ORDERCODE | 是 | STRING | 订单编号 |
| ORDERFROM | 是 | STRING | 订单来源。传值需与OMS确认 |
| ORDERTYPE | 是 | STRING | 订单类型（字典类型1：O2O） |
| PACKAGEFEE | 是 | STRING | 包装费 |
| POSTFEE | 是 | STRING | 实际退的运费，单位元 |
| PRODUCTSAMOUNT | 是 | STRING | 退款商品金额，单位为元 |
| RETURNADDRESS | 是 | STRING | 退货地址，包含省市区信息 |
| RETURNAMOUNT | 是 | STRING | 退单金额，单位为元 |
| RETURNCODE | 是 | STRING | 退单编号 |
| RETURNPICTURE | 是 | STRING | 申请退款图片，用【,】分割 |
| RETURNREASON | 是 | STRING | 申请退款原因（明文） |
| RETURNSTATUS | 是 | STRING | 退单状态，默认申请退款（120） |
| RETURNTYPE | 是 | STRING | 退单类型，0：退货退款，1：仅退款 |
| STORECODE | 是 | STRING | 门店编码（线下门店编码） |
| RETURNGOODS | 是 | List<goodsdetail> | 退单明细列表| BUYERNOTES | 否 | STRING | 买家备注 | | DISCOUNTPRICE | 是 | STRING | 实际退款价，单位为元 | | GOODSAMOUNT | 是 | STRING | 商品实际退款金额（退款价*数量） | | GOODSCODE | 是 | STRING | 商品编码 | | GOODSNAME | 是 | STRING | 商品名 | | GOODSPRICE | 是 | STRING | 实际退款价，单位为元 | | GOODSSTATUS | 是 | STRING | 商品退款状态，默认8 | | ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） | | RETURNCODE | 是 | STRING | 退单编号 | | RETURNCOUNT | 是 | STRING | 退货数量 | | SELLERNOTES | 否 | STRING | 卖家备注 | | BUYERNOTES | 否 | STRING | 买家备注 | DISCOUNTPRICE | 是 | STRING | 实际退款价，单位为元 | GOODSAMOUNT | 是 | STRING | 商品实际退款金额（退款价*数量） | GOODSCODE | 是 | STRING | 商品编码 | GOODSNAME | 是 | STRING | 商品名 | GOODSPRICE | 是 | STRING | 实际退款价，单位为元 | GOODSSTATUS | 是 | STRING | 商品退款状态，默认8 | ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） | RETURNCODE | 是 | STRING | 退单编号 | RETURNCOUNT | 是 | STRING | 退货数量 | SELLERNOTES | 否 | STRING | 卖家备注 |
| BUYERNOTES | 否 | STRING | 买家备注 |
| DISCOUNTPRICE | 是 | STRING | 实际退款价，单位为元 |
| GOODSAMOUNT | 是 | STRING | 商品实际退款金额（退款价*数量） |
| GOODSCODE | 是 | STRING | 商品编码 |
| GOODSNAME | 是 | STRING | 商品名 |
| GOODSPRICE | 是 | STRING | 实际退款价，单位为元 |
| GOODSSTATUS | 是 | STRING | 商品退款状态，默认8 |
| ISGIFT | 是 | STRING | 是否是赠品（0：否，1：是） |
| RETURNCODE | 是 | STRING | 退单编号 |
| RETURNCOUNT | 是 | STRING | 退货数量 |
| SELLERNOTES | 否 | STRING | 卖家备注 |
| FEE_DETAIL | 是 | List<fee> | 费用信息表（支付的信息和运费各单独作为一条费用信息传入）| AMOUNT | 是 | STRING | 费用金额，单位元 | | FEECODE | 是 | STRING | 费用编码，支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY | | FEENAME | 是 | STRING | 费用名称 | | FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） | | PAYBATCHNO | 否 | STRING | 交易流水号 | | PAYODERENO | 否 | STRING | 第三方支付订单号 | | RETURNCODE | 是 | STRING | 订单编号 | | AMOUNT | 是 | STRING | 费用金额，单位元 | FEECODE | 是 | STRING | 费用编码，支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY | FEENAME | 是 | STRING | 费用名称 | FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） | PAYBATCHNO | 否 | STRING | 交易流水号 | PAYODERENO | 否 | STRING | 第三方支付订单号 | RETURNCODE | 是 | STRING | 订单编号 |
| AMOUNT | 是 | STRING | 费用金额，单位元 |
| FEECODE | 是 | STRING | 费用编码，支付编码运费POSTFEE优惠 COUPON药联直付 YLPAY柜台收银 GTSY |
| FEENAME | 是 | STRING | 费用名称 |
| FEETYPE | 是 | STRING | 费用类型（0：优惠信息；1：支付信息；2：运费、包装费等其他费用） |
| PAYBATCHNO | 否 | STRING | 交易流水号 |
| PAYODERENO | 否 | STRING | 第三方支付订单号 |
| RETURNCODE | 是 | STRING | 订单编号 |


请求体示例：

| data={  "FEE_DETAIL": [  {  "AMOUNT": "2.50",  "FEECODE": "JDJKPAY",  "FEENAME": "京东健康支付",  "FEETYPE": "1",  "PAYBATCHNO": "",  "PAYODERENO": "",  "RETURNCODE": "TK163163704775"  }  ],  "RETURNGOODS": [  {  "BUYERNOTES": "",  "DISCOUNTPRICE": "0",  "GOODSAMOUNT": "2.50",  "GOODSCODE": "144595",  "GOODSNAME": "白云山 风油精 3ml/瓶",  "GOODSPRICE": "2.50",  "GOODSSTATUS": "8",  "ISGIFT": 0,  "RETURNCODE": "TK163163704775",  "RETURNCOUNT": "1",  "SELLERNOTES": ""  }  ],  "RETURNORDER": [  {  "APPLYNAME": "杨玉福",  "APPLYPHONE": "15969451930",  "CREATETIME": "2021-04-12 16:19:26",  "ISFULLREFUND": "1",  "LAT": "0",  "LNG": "0",  "ORDERCODE": "163163704775",  "ORDERFROM": "101",  "ORDERTYPE": "1",  "PACKAGEFEE": "0",  "POSTFEE": "0",  "PRODUCTSAMOUNT": "2.50",  "RETURNADDRESS": "云南昆明市呈贡区洛羊街道一心堂药业集团股份有限公司",  "RETURNAMOUNT": "2.50",  "RETURNCODE": "TK163163704775",  "RETURNPICTURE": "",  "RETURNREASON": "不想要了",  "RETURNSTATUS": "120",  "RETURNTYPE": "0",  "STORECODE": "AI87"  }  ] }&Sign=********* |


返回体：

| **参数** | **类型** | **描述** |
| Code | string | 成功标识，0成功；其他值表示写入失败 |
| Msg | string | 描述信息，成功时为succ；失败时是错误信息 |


返回体示例：

| {  "Code": "0",  "Msg": "succ" } |


**1.4 订单状态变更接口（平台提供）**

| **参数** | **类型** | **是否必须** | **描述** |
| storecode | string | 是 | ERP门店ID |
| ordercode | string | 是 | 订单编号 |
| status |  |  | 订单状态（字典值）订单类型（order）可用值33：处方单驳回34：处方单审核通过35：已接单37：已拣货40：呼叫骑手45：骑手到店54：骑手取货58：配送完成110：订单取消57：自提码校验 退单类型（returnorder）可用值130：同意退款131：驳回退款126：退单确认收货 |
| ordertype | string | 是 | 订单类型:order，表示订单returnorder,表示退单 |
| transtype | String | 否 | 配送方式 |
| dispatcher_name | String | 否 | 骑手姓名 |
| dispatcher_mobile | String | 否 | 骑手电话 |
| rx_img | String | 否 | 审方后带有签名的处方图片链接。状态值为34时此字段有值 |
| msg | String | 否 | 退单驳回申请时，此字段传出驳回原因 |
| selfpickcode | String | 否 | 自提码 |


请求体示例：

| ordercode=112345&storecode=1102&status=35&ordertype=order&transtype=美团配送&dispatcher_name=美团骑手&dispatcher_mobile=15500009999 |


返回体：

| **参数** | **类型** | **描述** |
| Code | string | 成功标识，0成功；失败时为50001 |
| Msg | string | 描述信息,失败时是错误信息 |


返回体示例：

| {  "Code": "0",  "Msg": "取消成功" } |


### 2、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 3、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

**jira链接：[ORDER-768] 支付宝小程序对接 - 一心数科数字化产研中心-Scrum (hxyxt.com)**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 接收订单 | 新增the3platform接收支付宝O2O/B2C新订单接口 | the3platformhydee-business-orderhydee-business-order-web | p1 |  |  |  |  |
| business-order接收支付宝O2O订单 | p1 |  |  |  |
| business-order-web接收支付宝B2C订单 | p1 |  |  |  |
| 取消订单 | 新增the3platform接收支付宝取消订单接口 | p1 |  |  |  |  |
| business-order接收支付宝取消信息 | p1 |  |  |  |
| 接收退款单 | 新增the3platform接收支付宝O2O/B2C退款单接口 | p1 |  |  |  |  |
| business-order接收支付宝O2O退款单 | p1 |  |  |  |
| business-order-web接收支付宝B2C退款单 | p1 |  |  |  |
| 订单状态变更通知 | 支付宝订单拣货消息通知 | p1 |  |  |  |  |
| 支付宝订单呼叫骑手消息通知 | p1 |  |  |  |
| 支付宝订单骑手取货消息通知 | p1 |  |  |  |
| 支付宝订单配送完成消息通知 | p1 |  |  |  |
| 支付宝订单同意退款消息通知 | p1 |  |  |  |  |
| 支付宝订单拒绝退款消息通知 | p1 |  |  |  |
| 支付宝订单确认收货消息通知 | p1 |  |  |  |
| 全流程测试 | 全流程测试 |  |  |  |  |  |


## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等