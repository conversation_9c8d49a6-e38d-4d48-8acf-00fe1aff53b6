# 2024-11-28 checklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 物流中台一期 | ``` logistics-center hydee-business-order-web hydee-business-order-b2c-third third-platform-logistics third-platform-callback-other ``` |  |  |  |  |  |  |  |  |  |
|  | order-atom-service 需要先发order-sync-service order-service |  |  |  |  |  |  |  |  |  |
| 多物流单 | hydee-business-order-webhydee-business-order-b2c-thirdhydee-oms-logisticcloud-ui |  |  |  |  |  |  |  |  |  |


### 二、快递商平台接口上线

| 快递商地址 | 接口上线情况 | 备注 |
| --- | --- | --- |
| [中通快递](https://open.zto.com/#/Console/DeveloperDetail?appId=%22202212721747%22) | 待申请上线 | 中通相关接口与回调地址配置完毕 |
| [极兔速递](https://open.jtexpress.com.cn/#/control/application) | 新增订单：已上线取消订单：已上线查询订单：已上线物流轨迹订阅：已上线快递轨迹查询：已上线拦截下发：已上线拦截下发回传：待上线物流轨迹回传：待上线 | 极兔速递的应用目前不支持修改回调地址，所以需要使用新的应用【线上应用】拦截下发回传：上线后使用生产环境地址，联调后申请上线物流轨迹回传：上线后使用生产环境地址，联调后申请上线注：更换了应用 需要更新线上的快递商户配置 |
| [圆通快递](https://open.yto.net.cn/linkInterFace/interfaceManage) | 订单创建接口：已上线物流轨迹推送服务：审核中物流轨迹查询接口：审核中物流轨迹订阅接口：审核中拦截件退回上报接口：审核中拦截件推送服务：审核中拦截件更址上报接口：审核中 | 需运营联系网点 |
| [邮政快递](https://api.ems.com.cn/#/xdl) | 待申请上线 | 需上线后配置回调地址 |
| [京东快递](https://open.jdl.com/admin/#/appManager/appTabs/8224?tabName=DockingScheme) | 无需申请，订阅即可使用 | 回调地址已配置 |


### 三、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| order_config_center | ``` INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (96, '2022-06-07 17:20:42', '快递一联单', '3003', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"wpCode\": \"ZTO\", \"merCode\": \"999999\", \"syncTime\": 1654593642780, \"printCode\": \"3003\", \"customAreaId\": 0, \"customAreaUrl\": \"https://pos-file.pinduoduo.com/express-common-no-cache/common/xmltemplate/isvtemplate_0d185172-7e22-4da8-a2e1-72e7de407fd5.xml\", \"standardTemplateId\": 46, \"standardTemplateUrl\": \"https://file-link.pinduoduo.com/zto_one\", \"standardWaybillType\": 3, \"standardTemplateName\": \"快递一联单\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (119, '2022-06-07 17:20:42', '快递一联单', '3003', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"wpCode\": \"YZXB\", \"merCode\": \"999999\", \"syncTime\": 1654593642781, \"printCode\": \"3003\", \"customAreaId\": 0, \"customAreaUrl\": \"https://pos-file.pinduoduo.com/express-common-no-cache/common/xmltemplate/isvtemplate_0d185172-7e22-4da8-a2e1-72e7de407fd5.xml\", \"standardTemplateId\": 53, \"standardTemplateUrl\": \"https://file-link.pinduoduo.com/yzxb_one\", \"standardWaybillType\": 3, \"standardTemplateName\": \"快递一联单\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (100, '2022-06-07 17:20:42', '快递一联单', '3003', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"wpCode\": \"YTO\", \"merCode\": \"999999\", \"syncTime\": 1654593642780, \"printCode\": \"3003\", \"customAreaId\": 0, \"customAreaUrl\": \"https://pos-file.pinduoduo.com/express-common-no-cache/common/xmltemplate/isvtemplate_0d185172-7e22-4da8-a2e1-72e7de407fd5.xml\", \"standardTemplateId\": 51, \"standardTemplateUrl\": \"https://file-link.pinduoduo.com/yto_one\", \"standardWaybillType\": 3, \"standardTemplateName\": \"快递一联单\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (4, '2022-06-07 17:20:42', '中通-无界(76x130双条码)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10001101, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=10001101\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"中通-无界(76x130双条码)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (751, NULL, '通用-隐私联单(76mm*130mm)', 'ZL', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/%E9%9D%A2%E5%8D%95%E9%A2%84%E8%A7%88%E6%A8%A1%E6%9D%BF_%E5%AF%86%E6%96%87.png?Expires=4870033565&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=qv27cOVITIao05osOy5ynunmSZw%3D', NULL, 'COSTUME', 1); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (3, '2022-06-07 17:20:42', '中通-无界(76x130)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10000101, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=10000101\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"中通-无界(76x130)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (596, '2021-08-30 20:33:26', '圆通快递-无界(76*130)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10000114, \"standardTemplateUrl\": \"https://storage.360buyimg.com/jdl-template/template-68111f86-ca23-4bdc-9194-fe2eef934ac4.1627007680725.txt\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"圆通快递-无界(76)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (143, '2022-06-07 17:20:42', '快递一联单', '3003', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"wpCode\": \"JTSD\", \"merCode\": \"999999\", \"syncTime\": 1654593642781, \"printCode\": \"3003\", \"customAreaId\": 0, \"customAreaUrl\": \"https://pos-file.pinduoduo.com/express-common-no-cache/common/xmltemplate/isvtemplate_0d185172-7e22-4da8-a2e1-72e7de407fd5.xml\", \"standardTemplateId\": 87, \"standardTemplateUrl\": \"https://file-link.pinduoduo.com/jtsd_one\", \"standardWaybillType\": 3, \"standardTemplateName\": \"快递一联单\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (2001, '2024-08-07 17:38:44', '极兔速递76*130', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 20000136, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=20000136\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"极兔速递100x150\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (167, '2022-06-07 17:20:58', '圆通一联单', '3002', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"brandCode\": \"default\", \"standardTemplateId\": 290659, \"standardTemplateUrl\": \"http://cloudprint.cainiao.com/template/standard/290659/61\", \"standardWaybillType\": 6, \"standardTemplateName\": \"圆通一联单\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (42, '2022-06-07 17:20:42', '圆通快递-无界(76x130)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10000114, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=10000114\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"圆通快递-无界(76x130)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (750, NULL, '通用-联单(76mm*130mm)', 'ZL', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/%E9%9D%A2%E5%8D%95%E9%A2%84%E8%A7%88%E6%A8%A1%E6%9D%BF_%E6%98%8E%E6%96%87.png?Expires=4870033510&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=vFgTGd2CQm3USMNhFxVAbE8lBRs%3D', NULL, 'COSTUME', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (43, '2022-06-07 17:20:42', '圆通快递-无界(76x130双条码)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10001114, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=10001114\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"圆通快递-无界(76x130双条码)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (1, '2022-06-07 17:20:42', '京东-一联面单(76x130)', '3004_1', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 100101, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=100101\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"京东-一联面单(76x130)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (145, '2022-06-07 17:20:42', '标准模板', '3003', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"wpCode\": \"JD\", \"merCode\": \"999999\", \"syncTime\": 1654593642781, \"printCode\": \"3003\", \"customAreaId\": 0, \"customAreaUrl\": \"https://pos-file.pinduoduo.com/express-common-no-cache/common/xmltemplate/isvtemplate_c09d91b0-6413-4c8a-8b71-8b2926a32155.xml\", \"standardTemplateId\": 62, \"standardTemplateUrl\": \"https://file-link.pinduoduo.com/jd_std\", \"standardWaybillType\": 1, \"standardTemplateName\": \"标准模板\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (2006, NULL, '圆通快递一联单标准模板', '3008', NULL, NULL, 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (27, '2022-06-07 17:20:42', '中国邮政小包-无界(76x130双条码)', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', '{\"standardTemplateId\": 10001110, \"standardTemplateUrl\": \"https://template-content.jd.com/template-content?type=standards&id=10001110\", \"standardWaybillType\": \"6\", \"standardTemplateName\": \"中国邮政小包-无界(76x130双条码)\"}', 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (2021, NULL, '京东快递标准模板76x130', '3004', '//centermerchant-prod.oss-cn-shanghai.aliyuncs.com/template/面单.jpg', NULL, 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (2011, NULL, '邮政快递包裹一联单', '3008', NULL, NULL, 'THIRD', 0); INSERT INTO `order_config_center`.`logistic_stdtemplates`(`id`, `sync_time`, `standard_template_name`, `print_code`, `view_url`, `expanded`, `source`, `encrypt_flag`) VALUES (2009, NULL, '京东一联单', '3008', NULL, NULL, 'THIRD', 0); `````` INSERT INTO `order_config_center`.`deliver_sheet_template`(`id`, `standard_template_name`, `view_url`, `expanded`) VALUES (2000, '发货单基础模板', NULL, NULL); #初始化自定义字段 INSERT INTO `logistic_customtemp_config` VALUES (1, NULL, NULL, 'buyerMessage', '买家留言', 1, 'logistic_custom_std', 'order', 1, 1, '希望918号送达', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (2, NULL, NULL, 'sellerRemark', '卖家留言', 1, 'logistic_custom_std', 'order', 2, 1, 'OK，没问题', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (3, NULL, NULL, 'omsOrderNo', '系统订单编号', 0, 'logistic_custom_std', 'order', 3, 1, '1711204128645722624', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (4, NULL, NULL, 'thirdOrderNo', '平台订单编号', 0, 'logistic_custom_std', 'order', 4, 1, '222926092932', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (5, NULL, NULL, 'seq', '序号', 0, 'logistic_custom_std', 'order_detail', 1, 1, '1', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (6, NULL, NULL, 'commodityName', '商品名称', 0, 'logistic_custom_std', 'order_detail', 2, 1, '严y TBC', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (7, NULL, NULL, 'commoditySpec', '规格', 0, 'logistic_custom_std', 'order_detail', 3, 1, 'UPPLIER', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (8, NULL, NULL, 'platformSkuId', '平台商品编码', 0, 'logistic_custom_std', 'order_detail', 4, 1, '911911889205610', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (9, NULL, NULL, 'erpCode', 'ERP商品编码', 0, 'logistic_custom_std', 'order_detail', 5, 1, '123', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (10, NULL, NULL, 'barCode', '条形码', 0, 'logistic_custom_std', 'order_detail', 6, 1, '34322', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (11, NULL, NULL, 'commodityCountExt1', '商品数量', 0, 'logistic_custom_std', 'order_detail', 7, 1, '1', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (12, NULL, NULL, 'thirdOrderNo', '平台订单编号', 1, 'send_order', 'order', 1, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (13, NULL, NULL, 'omsOrderNo', '系统订单编号', 1, 'send_order', 'order', 2, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (14, NULL, NULL, 'buyerMessage', '买家留言', 1, 'send_order', 'order', 3, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (15, NULL, NULL, 'payTime', '支付时间', 1, 'send_order', 'order', 4, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (16, NULL, NULL, 'onlineStoreName', '店铺名称', 1, 'send_order', 'order', 5, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (17, NULL, NULL, 'printDate', '打印时间', 1, 'send_order', 'order', 6, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (18, NULL, NULL, 'receiverName', '收货人姓名', 1, 'send_order', 'order', 7, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (19, NULL, NULL, 'receiverMobile', '收货人电话', 1, 'send_order', 'order', 8, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (20, NULL, NULL, 'address', '收货人地址', 1, 'send_order', 'order', 9, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (21, NULL, NULL, 'goodsNum', '商品总数', 1, 'send_order', 'order', 10, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (22, NULL, NULL, 'seq', '序号', 1, 'send_order', 'order_detail', 1, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (23, NULL, NULL, 'commodityName', '商品名称', 1, 'send_order', 'order_detail', 2, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (24, NULL, NULL, 'commoditySpec', '规格', 1, 'send_order', 'order_detail', 3, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (25, NULL, NULL, 'platformSkuId', '平台商品编码', 1, 'send_order', 'order_detail', 4, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (26, NULL, NULL, 'erpCode', 'ERP商品编码', 1, 'send_order', 'order_detail', 5, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (27, NULL, NULL, 'barCode', '条形码', 1, 'send_order', 'order_detail', 6, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (28, NULL, NULL, 'price', '商品单价', 1, 'send_order', 'order_detail', 7, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (29, NULL, NULL, 'totalPrice', '商品总价', 1, 'send_order', 'order_detail', 8, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (30, NULL, NULL, 'commodityCount', '商品数量', 1, 'send_order', 'order_detail', 9, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (31, NULL, NULL, 'commodityBatchNoExt1', '批号', 1, 'send_order', 'order_detail', 10, 1, NULL, '商品信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (32, NULL, NULL, 'commodityBatchNo', '批号', 0, 'logistic_custom_std', 'order_detail', 8, 0, '1245', '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (33, NULL, NULL, 'receiverName', '收货人姓名', 0, 'send_order', 'userInfo', 7, 1, NULL, '隐藏隐私信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (34, NULL, NULL, 'receiverMobile', '收货人电话', 0, 'send_order', 'userInfo', 8, 1, NULL, '隐藏隐私信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (35, NULL, NULL, 'address', '收货人地址', 0, 'send_order', 'userInfo', 9, 1, NULL, '隐藏隐私信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (36, NULL, NULL, 'totalAmount', '订单金额', 1, 'send_order', 'order', 11, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (37, NULL, NULL, 'sellerRemark', '卖家留言', 1, 'send_order', 'order', 2, 1, NULL, '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (38, NULL, NULL, 'created', '下单时间', 1, 'send_order', 'order', 12, 1, '2021-12-03 11:11:11', '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (39, NULL, NULL, 'orderSeq', '订单流水号', 1, 'send_order', 'order', 13, 1, '1', '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (40, NULL, NULL, 'created', '下单时间', 0, 'logistic_custom_std', 'order', -1, 1, '2021-12-03 11:11:11', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (41, NULL, NULL, 'orderSeq', '订单流水号', 0, 'logistic_custom_std', 'order', 0, 1, '1', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (42, NULL, NULL, 'is_show_list_title', '列表标题', 0, 'logistic_custom_std', 'order_detail', 0, 1, NULL, '商品信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (43, NULL, NULL, 'platformName', '平台名称', 1, 'send_order', 'order', 0, 1, '天猫', '发货单信息', '发货单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (115, NULL, NULL, 'thirdOrderNo', '平台订单编号', 1, 'send_order_a4', 'order', 30, 1, '1709679189723954689', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (116, NULL, NULL, 'omsOrderNo', '系统订单编号', 1, 'send_order_a4', 'order', 40, 1, '1701598456939823105', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (117, NULL, NULL, 'buyerMessage', '买家留言', 1, 'send_order_a4', 'order', 110, 1, '尽快发货', '发货单信息', '发货单a4', 24, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (118, NULL, NULL, 'payTime', '支付时间', 1, 'send_order_a4', 'order', 65, 1, '2021-08-01', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (119, NULL, NULL, 'onlineStoreName', '店铺名称', 1, 'send_order_a4', 'order', 125, 1, '海典大药房总部店铺', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (120, NULL, NULL, 'printDate', '打印时间', 1, 'send_order_a4', 'order', 63, 1, '2021-12-10 12:11:13', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (121, NULL, NULL, 'receiverName', '收货人姓名', 1, 'send_order_a4', 'order', 70, 1, '小吴', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (122, NULL, NULL, 'receiverMobile', '收货人电话', 1, 'send_order_a4', 'order', 80, 1, '137****2643', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (123, NULL, NULL, 'address', '收货人地址', 1, 'send_order_a4', 'order', 100, 1, '湖南省岳阳市相应xxxxxxxxxxx', '发货单信息', '发货单a4', 24, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (124, NULL, NULL, 'goodsNum', '商品总数', 1, 'send_order_a4', 'order', 67, 1, '5', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (125, NULL, NULL, 'seq', '序号', 1, 'send_order_a4', 'order_detail', 10, 1, '1', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (126, NULL, NULL, 'commodityName', '商品名称', 1, 'send_order_a4', 'order_detail', 20, 1, '感康', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (127, NULL, NULL, 'commoditySpec', '规格', 1, 'send_order_a4', 'order_detail', 80, 1, '1盒装', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (128, NULL, NULL, 'platformSkuId', '平台商品编码', 1, 'send_order_a4', 'order_detail', 50, 0, NULL, '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (129, NULL, NULL, 'erpCode', '商品编码', 1, 'send_order_a4', 'order_detail', 60, 1, 'sku001', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (130, NULL, NULL, 'barCode', '商品条码', 1, 'send_order_a4', 'order_detail', 70, 1, '6926134200588', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (131, NULL, NULL, 'price', '商品单价', 1, 'send_order_a4', 'order_detail', 90, 1, '15', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (132, NULL, NULL, 'totalPrice', '商品总价', 1, 'send_order_a4', 'order_detail', 100, 1, '90', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (133, NULL, NULL, 'commodityCount', '商品数量', 1, 'send_order_a4', 'order_detail', 110, 1, '6', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (134, NULL, NULL, 'commodityBatchNoExt1', '批号', 0, 'send_order_a4', 'order_detail', 120, 1, '\r\n2012001(x3）\r\n\r\n2012002(x3）', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (135, NULL, NULL, 'totalAmount', '订单金额', 1, 'send_order_a4', 'order', 60, 1, '90.00', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (136, NULL, NULL, 'sellerRemark', '卖家留言', 1, 'send_order_a4', 'order', 120, 1, '发赠品', '发货单信息', '发货单a4', 24, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (137, NULL, NULL, 'created', '下单时间', 1, 'send_order_a4', 'order', 15, 1, '2021-12-03 11:11:11', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (138, NULL, NULL, 'orderSeq', '订单流水号', 1, 'send_order_a4', 'order', 20, 1, '1', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (139, NULL, NULL, 'repeatPrintImage', '重复打印提醒', 1, 'send_order_a4', 'base_info', 10, 1, 'http://cdn-cloudprint.cainiao.com/waybill-print/cloudprint-imgs/5246224068cf43a38d969cc4dbb57069.png', '基础信息', '发货单a4', 16, 0, 10, 2, '1'); INSERT INTO `logistic_customtemp_config` VALUES (140, NULL, NULL, 'orderBarCode', '系统订单编号条码', 1, 'send_order_a4', 'base_info', 50, 1, '1701598456939823105', '基础信息', '发货单a4', 24, 0, 10, 3, '1'); INSERT INTO `logistic_customtemp_config` VALUES (141, NULL, NULL, 'postFee', '运费金额', 1, 'send_order_a4', 'order', 90, 1, '10.00', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (142, NULL, NULL, 'printQty', '打印次数', 1, 'send_order_a4', 'order', 11, 1, '2', '发货单信息', '发货单a4', 8, 0, 10, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (143, NULL, NULL, 'platformCommodityName', '平台商品名称', 0, 'send_order_a4', 'order_detail', 30, 1, '感康111', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (144, NULL, NULL, 'manufacture', '生产企业', 0, 'send_order_a4', 'order_detail', 40, 1, NULL, '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (145, NULL, NULL, 'repeatPrintImage', '重复打印提醒', 1, 'send_order_58', 'base_info', 10, 1, 'http://cdn-cloudprint.cainiao.com/waybill-print/cloudprint-imgs/5246224068cf43a38d969cc4dbb57069.png', '基础信息', '发货单a4', 24, 0, 8, 2, '1'); INSERT INTO `logistic_customtemp_config` VALUES (146, NULL, NULL, 'created', '下单时间', 1, 'send_order_58', 'order', 30, 1, '2021-12-03 11:11:11', '发货单信息', '发货单a4', 24, 0, 8, 1, '1'); INSERT INTO `logistic_customtemp_config` VALUES (147, NULL, NULL, 'orderSeq', '订单流水号', 1, 'send_order_58', 'base_info', 15, 1, '1', '基础信息', '发货单a4', 24, 0, 8, 2, '1'); INSERT INTO `logistic_customtemp_config` VALUES (148, NULL, NULL, 'printQty', '打印次数', 1, 'send_order_58', 'base_info', 20, 1, '2', '基础信息', '发货单a4', 24, 0, 8, 1, '1'); INSERT INTO `logistic_customtemp_config` VALUES (149, NULL, NULL, 'thirdOrderNo', '平台订单编号', 1, 'send_order_58', 'order', 60, 1, '1709679189723954689', '发货单信息', '发货单a4', 24, 0, 8, 1, '2'); INSERT INTO `logistic_customtemp_config` VALUES (150, NULL, NULL, 'omsOrderNo', '系统订单编号', 1, 'send_order_58', 'order', 70, 1, '1701598456939823105', '发货单信息', '发货单a4', 24, 0, 8, 1, '2'); INSERT INTO `logistic_customtemp_config` VALUES (151, NULL, NULL, 'totalAmount', '订单金额', 1, 'send_order_58', 'order', 120, 1, '90.00', '发货单信息', '发货单a4', 24, 0, 8, 1, '6'); INSERT INTO `logistic_customtemp_config` VALUES (152, NULL, NULL, 'printDate', '打印时间', 1, 'send_order_58', 'order', 40, 1, '2021-12-10 12:11:13', '发货单信息', '发货单a4', 24, 0, 8, 1, '1'); INSERT INTO `logistic_customtemp_config` VALUES (153, NULL, NULL, 'payTime', '支付时间', 1, 'send_order_58', 'order', 1100, 0, '2021-08-01', '发货单信息', '发货单a4', 8, 0, 8, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (154, NULL, NULL, 'goodsNum', '商品总数', 1, 'send_order_58', 'order', 140, 1, '5', '发货单信息', '发货单a4', 24, 0, 8, 1, '6'); INSERT INTO `logistic_customtemp_config` VALUES (155, NULL, NULL, 'receiverName', '收货人姓名', 1, 'send_order_58', 'order', 75, 1, '小吴', '发货单信息', '发货单a4', 24, 0, 8, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (156, NULL, NULL, 'receiverMobile', '收货人电话', 1, 'send_order_58', 'order', 80, 1, '137****2643', '发货单信息', '发货单a4', 24, 0, 8, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (157, NULL, NULL, 'postFee', '运费金额', 1, 'send_order_58', 'order', 130, 1, '10.00', '发货单信息', '发货单a4', 24, 0, 8, 1, '6'); INSERT INTO `logistic_customtemp_config` VALUES (158, NULL, NULL, 'address', '收货人地址', 1, 'send_order_58', 'order', 90, 1, '湖南省岳阳市相应xxxxxxxxxxx', '发货单信息', '发货单a4', 24, 0, 8, 1, '3'); INSERT INTO `logistic_customtemp_config` VALUES (159, NULL, NULL, 'buyerMessage', '买家留言', 1, 'send_order_58', 'order', 100, 1, '尽快发货', '发货单信息', '发货单a4', 24, 0, 8, 1, '5'); INSERT INTO `logistic_customtemp_config` VALUES (160, NULL, NULL, 'sellerRemark', '卖家备注', 1, 'send_order_58', 'order', 110, 1, '发赠品', '发货单信息', '发货单a4', 24, 0, 8, 1, '5'); INSERT INTO `logistic_customtemp_config` VALUES (161, NULL, NULL, 'onlineStoreName', '店铺名称', 1, 'send_order_58', 'order', 50, 1, '海典大药房总部店铺', '发货单信息', '发货单a4', 24, 0, 8, 1, '2'); INSERT INTO `logistic_customtemp_config` VALUES (162, NULL, NULL, 'seq', '序号', 1, 'send_order_58', 'order_detail', 150, 1, '1', '商品信息', '发货单a4', 1, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (163, NULL, NULL, 'commodityName', '商品名称', 1, 'send_order_58', 'order_detail', 160, 1, '感康', '商品信息', '发货单a4', 1, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (164, NULL, NULL, 'platfromCommodityName', '平台商品名称', 1, 'send_order_58', 'order_detail', 1100, 0, '感康111', '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (165, NULL, NULL, 'manufacture', '生产企业', 0, 'send_order_58', 'order_detail', 175, 1, '紫之隧道企业有限公司', '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (166, NULL, NULL, 'platformSkuId', '平台商品编码', 1, 'send_order_58', 'order_detail', 1100, 0, NULL, '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (167, NULL, NULL, 'erpCode', '商品编码', 0, 'send_order_58', 'order_detail', 180, 1, 'sku001', '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (168, NULL, NULL, 'barCode', '商品条码', 0, 'send_order_58', 'order_detail', 190, 1, '6926134200588', '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (169, NULL, NULL, 'commoditySpec', '规格', 1, 'send_order_58', 'order_detail', 170, 1, '1盒装', '商品信息', '发货单a4', 1, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (170, NULL, NULL, 'price', '单价', 1, 'send_order_58', 'order_detail', 220, 1, '15', '商品信息', '发货单a4', 8, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (171, NULL, NULL, 'totalPrice', '总价', 1, 'send_order_58', 'order_detail', 230, 1, '90', '商品信息', '发货单a4', 8, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (172, NULL, NULL, 'commodityCount', '数量', 1, 'send_order_58', 'order_detail', 210, 1, '6', '商品信息', '发货单a4', 8, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (173, NULL, NULL, 'commodityBatchNoExt1', '批号', 1, 'send_order_58', 'order_detail', 200, 1, '\r\n2012001(x3）\r\n\r\n2012002(x3）', '商品信息', '发货单a4', 8, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (174, NULL, NULL, 'printQty', '打印次数', 0, 'logistic_custom_std', 'order', 5, 1, '11', '面单信息', '面单', 24, 0, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (175, NULL, NULL, 'alloc', '货位', 0, 'send_order_58', 'order_detail', 191, 1, 'A01/名称', '商品信息', '发货单a4', 24, 0, 8, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (176, NULL, NULL, 'alloc', '货位', 0, 'send_order_a4', 'order_detail', 521, 1, 'A01<br/>名称', '商品信息', '发货单a4', 24, 0, 10, 1, '4'); INSERT INTO `logistic_customtemp_config` VALUES (179, NULL, NULL, 'platformStoreName', '平台店铺名称', 0, 'logistic_custom_std', 'order', 0, 1, '淘宝-海典旗舰店', '面单信息', '面单', 24, 1, 8, NULL, NULL); INSERT INTO `logistic_customtemp_config` VALUES (180, NULL, NULL, 'orderBarCode', '系统订单编码条码', 0, 'send_order_58', 'order', 72, 1, '1701598456939823105', '发货单信息', '发货单a4', 24, 0, 8, 3, '2'); INSERT INTO `logistic_customtemp_config` VALUES (181, NULL, NULL, 'printSeq', '打印序号', 1, 'send_order_58', 'base_info', 2, 1, NULL, '基础信息', '发货单a4', 24, 0, 8, 2, '1'); INSERT INTO `logistic_customtemp_config` VALUES (182, NULL, NULL, 'platformName', '平台名称', 1, 'send_order_58', 'base_info', 0, 1, '天猫', '基础信息', '发货单a4', 24, 0, 8, 2, '1'); #初始化平台商户 `````` INSERT INTO `order_config_center`.`express_merchant`(`id`, `express_account_name`, `plat_code`, `status`, `link_type`, `config_info`, `created_by`, `updated_by`, `created_time`, `updated_time`, `version`) VALUES (1001, '快手平台', 'KUAI_SHOU', 'OPEN', '3008', '{}', NULL, NULL, '2024-11-13 18:34:51', '2024-11-15 16:23:27', 1); INSERT INTO `order_config_center`.`express_merchant`(`id`, `express_account_name`, `plat_code`, `status`, `link_type`, `config_info`, `created_by`, `updated_by`, `created_time`, `updated_time`, `version`) VALUES (1002, '京东无界', 'JDWJ', 'OPEN', '3004', '{"lopdn":"ECAP","appKey":"603FE0D5D4A60E1144D6EAE5009A505A","appSecret":"d61d49e3e8d34c83af63dede9ae54602","accessToken":"a8b24fe44cfc4e5bbc1be1c9f4833b1b"}', NULL, NULL, '2024-11-27 15:15:29', '2024-11-27 15:43:43', 1); INSERT INTO `order_config_center`.`express_merchant`(`id`, `express_account_name`, `plat_code`, `status`, `link_type`, `config_info`, `created_by`, `updated_by`, `created_time`, `updated_time`, `version`) VALUES (1003, '拼多多平台', 'PDD', 'OPEN', '3003', '{}', NULL, NULL, '2024-11-28 09:51:17', '2024-11-28 09:51:17', 1); ``` | 初始化模板sql |
| 物流中台 | /*  Navicat Premium Data Transfer Source Server : test  Source Server Type : MySQL  Source Server Version : 80028  Source Host : **********:3306  Source Schema : logistics_center Target Server Type : MySQL  Target Server Version : 80028  File Encoding : 65001 Date: 28/11/2024 14:33:58 */SET NAMES utf8mb4; SET FOREIGN_KEY_CHECKS = 0;-- ---------------------------- -- Table structure for express_logistics_order -- ---------------------------- DROP TABLE IF EXISTS `express_logistics_order`; CREATE TABLE `express_logistics_order` (  `id` bigint(0) NOT NULL AUTO_INCREMENT,  `online_store_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺编码\r\n',  `oms_order_no` bigint(0) NOT NULL COMMENT '系统订单号',  `third_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方订单号',  `ext_oms_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统订单扩展单号 格式 oms_order_no + _create_num 用于请求物流平台',  `logistics_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流单号',  `logistic_status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'WAIT_COLLECT' COMMENT 'WAIT_COLLECT-待揽收 WAIT_DIST-待配送 DISTING-配送中 FINISH-已完成 CANCEL-已取消 RETURNED-已退回',  `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台code',  `express_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司Code',  `express_store_config_id` bigint(0) NULL DEFAULT NULL COMMENT '店铺快递配置id',  `intercept_tag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NO_INTERCEPT' COMMENT '拦截标记 未拦截-NO_INTERCEPT 拦截中- INTERCEPT_ING 拦截成功 INTERCEPT_SUCCCESS\r\n拦截失败 INTERCEPT_FAIL',  `intercept_fail_msg` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拦截失败原因',  `finish_time` datetime(0) NULL DEFAULT NULL COMMENT '物流完成时间 已签收 已退回',  `up_address_tag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NONE' COMMENT '修改地址标识 NONE-未修改 MODIFYING-修改中 MODIFY_FAIL-修改失败 MODEIFY_SUCCESS- 修改成功',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',  `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',  `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',  `deleted` bigint(0) NOT NULL DEFAULT 0 COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint(0) NOT NULL DEFAULT 1 COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  INDEX `logistic_no`(`logistics_no`) USING BTREE,  INDEX `oms_order_no_third_no_index`(`oms_order_no`, `third_order_no`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 352 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流信息表' ROW_FORMAT = Dynamic;-- ---------------------------- -- Table structure for express_logistics_sheet -- ---------------------------- DROP TABLE IF EXISTS `express_logistics_sheet`; CREATE TABLE `express_logistics_sheet` (  `id` bigint(0) NOT NULL AUTO_INCREMENT,  `logistics_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流单号',  `three_segment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三段码',  `bag_addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '集包地',  `document` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '面单内容',  `print_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '打印平台 ZL-直连 3002-菜鸟云栈 3003-拼多多 3004-京东无界',  `print_num` int(0) NULL DEFAULT 0 COMMENT '打印次数',  `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',  `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',  `deleted` bigint(0) NOT NULL DEFAULT 0 COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint(0) NOT NULL DEFAULT 1 COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  INDEX `logistic_no`(`logistics_no`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 338 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流面单表' ROW_FORMAT = Dynamic;-- ---------------------------- -- Table structure for express_track -- ---------------------------- DROP TABLE IF EXISTS `express_track`; CREATE TABLE `express_track` (  `id` bigint(0) NOT NULL AUTO_INCREMENT,  `logistics_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流单号',  `unique_idx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单号轨迹索引，用于确认当前操作下唯一记录 使用md5生成',  `express_state` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流状态',  `operation_action` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'WAIT_COLLECT' COMMENT '物流操作动作',  `express_desc` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流状态描述',  `location_province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快件所在省',  `location_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快件所在市',  `location_district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快件所在区',  `network_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快件所在网点',  `operation_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',  `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',  `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',  `version` bigint(0) NOT NULL DEFAULT 1 COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  UNIQUE INDEX `logistic_no_unique_idx`(`logistics_no`, `unique_idx`) USING BTREE,  INDEX `logistic_no`(`logistics_no`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 513 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流轨迹表' ROW_FORMAT = Dynamic;SET FOREIGN_KEY_CHECKS = 1; |  |
| hydee_aurora_basic.t_base_client | INSERT INTO t_base_client (id, ectype, groupid, clientid, clientname, sellerid, appkey, partner, accesstoken, refreshtoken, `key`, appsecret, sign_secret, apiversion, serverurl, expirestime, reexpirestime, is_o2o, is_auto_confirm, is_down_item, is_down_order, is_down_refund, isenabled, createuser, updateuser, createtime, updatetime, stamp, isv, o2o_auto_confirm) VALUES('YZXB_500001_100200300400500600700800900', 'YZXB', '500001', '100200300400500600700800900', '邮政小包', '', 'UmpVfwT9zizBpP3P', '', '', NULL, 'SndVdXZBbnIzTEdTSVE5UA==', 'SndVdXZBbnIzTEdTSVE5UA==', '', NULL, NULL, NULL, NULL, 2, 1, 0, 1, 1, 1, NULL, NULL, '2024-10-23 17:23:06.000', '2024-11-08 14:16:19.000', '2024-11-08 14:16:19.378', 0, 1); | 配置邮政小包回调url解密配置信息 |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| third-platform-logistics | application.yml | #快递配置 kd:  jd:  baseurl: [https://api.jdl.com](https://api.jdl.com)  zt:  baseurl: [https://japi.zto.com](https://japi.zto.com/zto.open.createOrder)  jtsd:  baseurl: [https://openapi.jtexpress.com.cn/webopenplatformapi](https://openapi.jtexpress.com.cn/webopenplatformapi)  yzxb:  baseurl: [https://api.ems.com.cn/amp-prod-api/f/amp/api/open](https://api.ems.com.cn/amp-prod-api/f/amp/api/open)  yto:  baseurl: [https://openapi.yto.net.cn:11443/open](https://openapi.yto.net.cn:11443/open) message:  topic: TP_LOGISTICS_THIRD-PLATFORM-LOGISTICS-CALLBACK-OTHER_CALLBACK  # tag区分，多个使用 || 分割，如：9002||11  tag: TAG_JD||TAG_JTSD||TAG_YZXB||TAG_YTO||TAG_ZTO  max-retry-times: 3  notify-message:  logistics-intercept-topic: TP_LOGISTICS_THIRD-PLATFORM_LOGISTICS-INTERCEPT  logistics-track-topic: TP_LOGISTICS_THIRD-PLATFORM_LOGISTICS-TRACK |  |
| third-platform-callback-other | application.yml | callback-message: - businessType: logistics  platformCodeList: ["JD","ZTO","YZXB","YTO","JTSD"]  topic: TP_LOGISTICS_THIRD-PLATFORM-LOGISTICS-CALLBACK-OTHER_CALLBACK |  |
| hydee-api-gateway | application.yml | # 接口中台消息回调  - id: yxt-third-platform-callback-other  uri: [lb://third-platform-callback-other](lb://third-platform-callback-other)  predicates:  - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/**,/third-platform/callback/3008/**,/third-platform/callback/ics/**,/third-platform/callback/3008/**,/third-platform/callback/ZTO/**,/third-platform/callback/JD/**,/third-platform/callback/JTSD/**,/third-platform/callback/YZXB/**,/third-platform/callback/YTO/** |  |
| hydee-business-order-web hydee-business-order-b2c-third | application.yml | oms: b2c:  useLogisticCenter: false  openStoreExpressMerId: |  |
| third-platform-gateway | application.yml | - id: third-platform-logistics  uri: [lb://third-platform-logistics](lb://third-platform-logistics)  predicates:  - Path=/third-platform/logistics/** | 上线需要删除旧的网关服务pod 否则不生效 |


#### 2.3 网关配置变更

| 变更内容 | 变更前 | 修改内容 | 备注 |
| --- | --- | --- | --- |
| 新增接口中台物流回调URL | # 接口中台消息回调  - id: yxt-third-platform-callback-other  uri: [lb://third-platform-callback-other](lb://third-platform-callback-other)  predicates:  - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/**,/third-platform/callback/ics/**,/third-platform/callback/3008/** | # 接口中台消息回调  - id: yxt-third-platform-callback-other  uri: [lb://third-platform-callback-other](lb://third-platform-callback-other)  predicates:  - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/**,/third-platform/callback/ics/**,/third-platform/callback/3008/**,/third-platform/callback/ZTO/**,/third-platform/callback/JD/**,/third-platform/callback/JTSD/**,/third-platform/callback/YZXB/**,/third-platform/callback/YTO/** |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 店铺快递商配置同步 | logistics-center | expressStoreConfigSyncJobHandler | 0 0/3 * * * ? |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
| rocket_mq | ``` TP_LOGISTICS_INTERCEPT_STATUS_NOTIFY ``` | 拦截状态回调心云 |
| rocket_mq | ``` TP_LOGISTICS_UP_ADDRESS_STATUS_NOTIFY ``` | 修改地址状态回调心云 |
| rocket_mq | ``` TP_LOGISTICS_THIRD-PLATFORM_LOGISTICS-TRACK ``` | 轨迹监听 |
| rocket_mq | ``` TP_LOGISTICS_THIRD-PLATFORM_LOGISTICS-INTERCEPT ``` | 拦截状态监听 |
| kafka | ``` STORE_EXPRESS_MERCHANT_CONFIG_CANAL_TO_KAFKA_TOPIC ``` | cancal 监听快递商变更 |
| rocket_mq | TP_LOGISTICS_THIRD-PLATFORM-LOGISTICS-CALLBACK-OTHER_CALLBACK | 三方回调下发到接口中台 |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |