# 【20241111】 MDM组织员工数据应用--订单

## 一、 背景

原来心云组织数据从sap同步，现在要使用新的金蝶HCM人事系统，HCM会同步数据到MDM，一心助手会通过MDM同步数据到心云；订单组需要梳理新老数据源切换以及一部分组织架构变化对订单所产生的影响与相应的解决方案

### 1.1 相关内容

**【20241017】组织机构+员工现状梳理 - 后端研发部 - 一心数科数字化产研中心-wiki**

**MDM组织员工数据应用-推进方案 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)**

## 二、 影响范围梳理

**文档：导出数据_行政组织_1025084219 - DERP组织架构核对v0.1.xlsx**

**组织影响内容：**

|  | 分类 | 数量 |
| --- | --- | --- |
| 1 | 总数 | 15315 |
| 2 | 没有变化的组织 | 12899 |
| 3 | 取消的组织 | 705 |
| 4 | 新增组织 | 12 |
| 5 | 暂时保留的组织 | 432 |
| 6 | 组织编码变更 | 2 |
| 7 | 组织编码、类型、上级关系变更 | 2 |
| 8 | 组织编码、类型变更 | 2 |
| 9 | 组织编码、名称、上级关系变更 | 43 |
| 10 | 组织编码、上级关系变更 | 19 |
| 11 | 组织编码变更 | 2 |
| 12 | 组织类型变更 | 1 |
| 13 | 组织名称、上级关系变更 | 212 |
| 14 | 组织名称变更 | 5 |
| 15 | 组织上级关系变更 | 981 |


### 2.1 变更点梳理

| 变更类型 | 对订单影响 |  | 解决方案 |
| --- | --- | --- | --- |
| 组织编码变更 | 1. 组织全路径变化，调拨单相关数据无法查询 | 无 | 1. 研发手动刷数 |
| 组织名称变更 | 无 | 无 |  |
| 组织上级关系变更 | 1. 非门店层级人员数据权限变化，属于前节点的无法查看数据 2. 组织全路径变化，调拨单相关数据无法查询 | 1.调拨单 ,调拨单的查询依赖组织全路径,变更后需要跟进变动. | 1. 需要产品对变更后的信息进行重新维护 2. 按照维护后的全路径进行刷数 |
| 组织取消 | 1. 运营人员对变更后组织下的门店暂无查询权限 2. 组织全路径变化，调拨单相关数据无法查询 | 1.调拨单 ,调拨单的查询依赖组织全路径,变更后需要跟进变动. | 1. 需要产品对变更后的信息进行重新维护 2. 按照维护后的全路径进行刷数 |
| 组织新增 | 无影响 |  |  |


### 2.2 接口梳理

绿色：新增接口 红色：重点接口

| 所属功能 | 调用baseinfo接口 | 接口分类 | 接口功能 | 影响效果 | 历史数据解决方案 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| POS类型管理 |  |  |  | 子公司与分部上下级关系变更 | 根据新组织层级刷历史数据 |  |
| O2O运维工具/B2C运维工具 | /1.0/baseinfo/getEmployee/{userId} | 员工 | 根据用户ID查询员工数据 |  |  |  |
| B2C/O2O订单拣货 | /1.0/employee/_search | 员工 | 拣货查询员工信息 | 店员拣货（页面调用） |  |  |
| 拣货/下账 | /1.0/employee/{id} | 员工 | 查询账号信息 |  |  |  |
| 下账 | /1.0/employee/queryEmpInfoByCode/{merCode}/{empCode} | 员工 | 根据商户编号和员工编码查询信息 |  |  |  |
|  | /1.0/acc/{id} | 账号 | 根据id查询账户 | todo:这个接口yxt-org无法支持，这是账号接口。不是员工 |  |  |
|  | /1.0/acc/_searchAccount | 账号 | 查询账号信息 | todo:这个接口yxt-org无法支持，这是账号接口。不是员工 |  |  |
|  | /1.0/ewxemp/batchSearchEmpInfoByEmpCodes | 员工 | 通过员工编码集合查询员工名称 |  |  |  |
| 订单导出 | /1.0/organization/searchOrCodesParent | 门店 | 查询门店上级机构 |  |  | 海典原逻辑，导出查询上级机构信息 |
| 门店管理--服务同步日志 | /1.0/employee/getList | 员工 | 根据员工id查询员工名称 |  |  |  |
| 调拨单/下账单 | /1.0/organization/batchGetOrgByCodes | 门店 | 根据门店编码查询全路径 | 创建调拨单/下账单入参 | 根据新组织层级刷历史数据，新数据不变 |  |
| /1.0/organization/queryOrgByCodes | 门店 | 根据子公司id查询其下门店信息 | 调拨单/下账单查询条件缺失 | 根据新组织层级刷历史数据，新数据不变 |  |
| /1.0/api/user/organization/orClass/list | 权限 | 查询当前用户所属的组织层级信息 | 调拨单/下账单查询条件变更 |  |  |
| /1.0/api/store/info | 门店 | 查询门店信息 | 创建下账单入参 |  |  |
|  | /1.0/baseinfo/queryOrganization | 门店 | 分页查询机构信息 |  |  | 订单提供给外部，不清楚调用方 |
|  | /1.0/organization/searchOrgTreeByPid | 权限 | 根据当前用户查询组织机构树 |  |  | 订单提供给外部，不清楚调用方 |
|  | /1.0/store/my | 权限 | 根据用户id，查询用户可查看的门店 |  |  | 订单未调用（可废弃） |
|  | /1.0/baseinfo/queryOrganizations | 组织 | 查询所有子公司以及其分部信息 | //todo:这个原来没有如果只是组织数据,需要yxt-org可以写一下出入参 |  |  |
| O2O权限 | /1.0/store/_queryOrgSubStoreList | 权限 | 根据机构树的父节点获取叶子节点（机构信息） | O2O订单全局 | 历史数据主要落在门店编码上面，订单无需刷数 | 商品查询用户有权限的店铺会调用此接口 |
| /1.0/store/myUsingCache | 权限 | 根据用户id，查询用户可查看的门店 | O2O订单全局 |  |
| B2C权限 | /1.0/store/myB2c | 权限 | 根据用户id，查询用户可查看的门店 | B2C订单全局 |  |


**结论: 员工/组织/账号查询: yxt-org提供新接口 门店/权限查询: baseinfo 兼容升级**

**下账: 无影响,仅依赖门店编码.门店编码不变.**

**OMS订单: 操作权限受阻后, 联系徐凯重新授权即可**

## 三、 详细设计

### 3.1 新接口兼容设计

**trueMDM数据切换falseautotoptrue6414**

### 3.2 历史数据刷数设计

**trueMDM组织变动历史数据刷数falseautotoptrue155011**

## 四、 切换方案

**3.1 切换前提**

1. yxt-org与会员切换完毕
2. 相关接口兼容完毕（接口如上）


**3.2 切换流程**

1. yxt-org与会员切换完毕
2. 订单中台刷新涉及到组织机构的相关数据（调拨单、下账单——cancl自动刷新es）
3. 业务验证


**3.3 风险点**

1. 全局替换员工组织相关接口，影响点较多，有一定风险


## 五、项目排期

| 替换yxt-org接口 | 1. 替换business-order服务员工相关接口 2. 替换business-order服务组织相关接口 3. 替换hydee-business-order-web服务员工相关接口 4. 替换hydee-business-order-web服务相关接口 5. 替换hydee-middle-order服务员工相关接口 6. 替换hydee-middle-payment服务员工相关接口 |  | 2pd |  |  |  |
| 刷数定时任务 | 全路径订单任务 |  | 0.5pd |  |  |  |
| Pos类型定时任务 |  | 0.5pd |  |  |  |
| 功能自测 |  |  |  |  |  |  |
| 功能模块 | 功能项 | 优先级 | 工时(PD) | 主R | 计划完成日期 | 研发进展 |