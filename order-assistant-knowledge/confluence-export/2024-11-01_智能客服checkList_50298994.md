# 2024-11-01 智能客服checkList

- [一、上线内容](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38836816#id-20240731checkList%E6%99%BA%E8%83%BD%E5%AE%A2%E6%9C%8D%E4%B8%80%E6%9C%9F-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 智能客服一期 | ``` hydee-business-order ``` | 2 | ``` feature/order-2026/zhinengkefu ``` | 焦钰斌 | 徐国华 | 汪骁 |  |  |  |  |
| ``` hydee-middle-order ``` | 1 | feature/order-2026/zhinengkefu | 焦钰斌 | 徐国华 | 汪骁 |  |  |  |  |
| hydee-business-order-web | 1 | feature/order-2026/zhinengkefu | 焦钰斌 | 徐国华 | 汪骁 |  |  |  |  |
| 文件上传aksk验证 | hydee-api-gateway | 1 | ``` feature/uploadFile-sign-check ``` | 焦钰斌 | 徐国华 |  |  |  |  |  |
| 智能客服二期 | third-platform-callback-other | 1 | feature/order-2026/zhinengkef |  |  |  |  |  |  |  |
| third-platform-other | 1 | feature/order-2026/zhinengkef |  |  |  |  |  |  |  |
| ydjia-merchant-manager | 1 | feature/order-2026/zhinengkef |  |  |  |  |  |  |  |
| message-icsim-server | 1 |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| ``` ALTER TABLE `middle_order`.`order_info`     ADD COLUMN `seller_remark` varchar(255) NULL COMMENT '客服备注',ALGORITHM = INPLACE, LOCK = NONE; ALTER TABLE `middle_order`.`order_info`     ADD COLUMN seller_remark_time datetime null COMMENT '卖家备注时间',ALGORITHM = INPLACE, LOCK = NONE; ``` |
| --- |


```

```

```
create table ics_store_session (     id                  bigint auto_increment         primary key,     type                tinyint      not null comment '类型(0-单聊,1-多客户群聊)',     third_platform_code varchar(20)  not null comment '平台编码',     online_client_code  varchar(50)  null comment '网店编码',     online_store_code   varchar(60)  not null comment '线上门店编码',     store_code          varchar(50)  not null comment '门店编码',     third_origin_id     bigint       not null comment '三方会话发起人id',     last_message_id     bigint       null comment '会话中的最后一次消息id',     extent_info         varchar(255) null comment '扩展信息',     updated_day         date         null comment '更新日',     create_time         datetime     null comment '创建时间',     constraint uk_platform_store_id         unique (third_platform_code, online_store_code, store_code, third_origin_id) )     comment '智能客服消息会话表'; create index idx_time_store     on ics_store_session (updated_day, store_code);
```

```
create table ics_message (     id           bigint auto_increment         primary key,     msg_source   int                                null comment '消息来源：1-商家 2-用户',     from_id      varchar(50)                        not null comment '发送方',     receive_id   varchar(50)                        not null comment '接收方',     session_id   varchar(100)                       not null comment '会话id',     msg_type     int                                null comment '消息类型',     msg_context  text                               null comment '消息内容',     msg_status   int                                null comment '消息状态(0-未读,1-已读)',     send_status  int      default 1                 null comment '发送状态 0-失败,1-成功',     created_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',     updated_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',     push_time    datetime default CURRENT_TIMESTAMP not null comment '推送时间' )     comment '智能客服聊天消息表'; create index idx_session_time     on ics_message (session_id, created_time);
```

#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| third-platform-other |  | ```     ``` | ``` ics:     topic: TP_ICS_THIRD-PLATFORM-OTHER-CALLBACK-ICS_CALLBACK     #  tag区分，多个使用 || 分割，如：TAG_9002||TAG_11     tag: TAG_27 ``` |
|  | ``` notify-message ``` | ``` customer-service-message-topic: TP_ORDER_THIRD-PLATFORM_ICS-PUSH ``` |
| third-platform-callback-other |  | ``` callback-message:   topicList: ``` | ``` - businessType: ics       platformCodeList: ["27"]       topic: TP_ICS_THIRD-PLATFORM-OTHER-CALLBACK-ICS_CALLBACK ``` |
| hydee-api-gateway |  |  | + - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/**,/third-platform/callback/ics/** |
| third-platform-gateway |  |  | + - Path=/third-platform/system/**,/third-platform/store/**,/third-platform/ics/** |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
| - id: zhiNengKeFu  uri: [lb://hydee-business-order](lb://hydee-business-order)  predicates:  - Path=/ics/order/**  filters:  - Sign  - StripPrefix=1 | api-gateway |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 定时删除智能客服聊天消息 | 【会员】药店加小前台 | icsMessageDeleteJobHandler | 0 0 1 * * ? |  | 30 |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  | }, |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |