# 【20240229】下账时机修改以及生成运费单

# 一、背景

## 1.1 业务背景

目前系统中有多种下账配置，并且当配送方式由平台配送改为商家自配送时，下账相关金额需要重新计算，结合当前背景，平台配送转为自配送时候需要生成 一条配送单

## 1.2 痛点分析

系统目前下账是配送出库再下账，会导致1%的订单会下账失败，需要门店重新选择批号下账,当前下账配置中的自动下账配置未起效代码写死的

## 1.3 系统现状

目前下账是配送出库再下账,平台配送改为商家自配送后, 重新计算下账金额

# 二、需求分析

## 2.1 业务流程

[V 1.5.2 - 产品部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/display/prd/V+1.5.2)

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

**前置说明**

**界面**

****

**数据库字段**

**海典下账时机流程图（海典H1、H2都是主动推送）**

**true海典H1、H2正单下账falseautotoptrue6285**

**科传下账时机流程图（定时拉取）**

**true海典H1、H2正单下账falseautotoptrue5863**

**正单下账运费单生成流程图**

**true运费单生成逻辑falseautotoptrue10012**

**主要生成的表：**

**order_info**

**order_detail**

**erp_bill_info**

**order_pay_info**

**逆向单下账运费单生成流程图**

true未命名绘图falseautotoptrue7813

**主要生成的表：**

erp_refund_info

# 五、详细设计

## 5.1 详细模块设计

## 5.3 接口设计

### 5.3.1 前端交互接口

#### 1 O2O订单列表查询新增订单类型

****

1. [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search)
2. 请求类型：POST
3. 请求体新增订单类型 orderType=3 运费订单


#### 2 O2O订单详情新增关联运费单

****

1. [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1792651505377378051?_t=0.7608379427835545&orderNo=1792651505377378051](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1792651505377378051?_t=0.7608379427835545&orderNo=1792651505377378051)
2. 请求类型：GET
3. 返回新增freightOrderNo 运费订单编号


```
/**  * 运费订单编号  */ private String freightOrderNo; 5.3.2 单纯后台接口
```

```
1.定时任务转自配送   /order/changeSelfDelivery/autoChangeSelfDelivery 2.待配送修改配送商家(配送方式)/ds/order/delivery/merchant 3.强制将订单转为自配送/ds/inner/devOps/changeSelfDelivery
```

## 5.4 涉及数据库

## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.7 问题

1.订单下账失败，运费单下账成功（不影响）

2.运费单正单下账后订单状态直接改为已完成 ，退款单下账后订单状态改为已退款 ，未下账取消直接已取消 （解决）

3.美团企客配问题（转自配送的时候校验下账配置是否是404，是就不生成运费单，不是才生成）（解决）

4.O2O订单明细中的商品明细给出运费的字段列表

5.拣货信息问题 

新生成的运费单 商品 明细 商品ERP 编码为 777832 名称：商家运费收入；

下账流程问题

6.关联问题新增运费单号（原来正单的系统单号 orderNo）（解决）

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等