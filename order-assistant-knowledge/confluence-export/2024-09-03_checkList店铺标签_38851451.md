# 2024-09-03 checkList店铺标签

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 店铺标签 | hydee-business-order | 1 | store-label | 李洋 |  | 徐凯 | 张柳 |  | 2024-09-03 |  |
| 店铺标签 | cloud-ui | 2 |  |  | 谢元罡 | 徐凯 | 张柳 |  | 2024-09-03 |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| dscloud | ``` CREATE TABLE `online_store_tag_group` (                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',                                           `parent_id` bigint NOT NULL COMMENT '父类ID',                                           `group_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签组名称',                                           `group_level` int NOT NULL COMMENT '层级',                                           `creator_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人id',                                           `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',                                           `modifier_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人id',                                           `modifier_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称',                                           `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',                                           PRIMARY KEY (`id`),                                           KEY `idx_parent_id` (`parent_id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺标签组'; CREATE TABLE `online_store_tag` (                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',                                     `group_id` bigint NOT NULL COMMENT '所属标签组ID',                                     `tag_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',                                     `tag_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签类型（SYSTEM_TAG：系统标签，USER_DEFINE_TAG：自定义标签）',                                     `tag_note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签注释',                                     `store_num` int DEFAULT '0' COMMENT '打标店铺数量',                                     `creator_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人id',                                     `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人名称',                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',                                     `modifier_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人id',                                     `modifier_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人名称',                                     `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',                                     PRIMARY KEY (`id`),                                     KEY `idx_group_id` (`group_id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺标签'; CREATE TABLE `online_store_tag_record` (                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',                                            `tag_id` bigint DEFAULT NULL COMMENT '标签ID',                                            `online_store_id` bigint DEFAULT NULL COMMENT '线上门店id',                                            `operate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作 HANDLE_TAG:手动打标、SYSTEM_TAG:系统自动打标、TAG_UNBIND:标签解绑',                                            `tag_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签名称',                                            `operator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人id',                                            `operator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作人名称',                                            `operate_time` datetime DEFAULT NULL COMMENT '操作时间',                                            PRIMARY KEY (`id`),                                            KEY `idx_online_store_id` (`online_store_id`) USING BTREE,                                            KEY `idx_tag_id` (`tag_id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺打标记录表'; CREATE TABLE `online_store_tag_relate` (                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',                                            `tag_id` bigint NOT NULL COMMENT '标签ID',                                            `online_store_id` bigint DEFAULT NULL COMMENT '线上门店id',                                            PRIMARY KEY (`id`),                                            UNIQUE KEY `uk_tag_store_id` (`tag_id`,`online_store_id`) USING BTREE,                                            KEY `idx_online_store_id` (`online_store_id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺标签关联表' ``` | 调整是否需要审方字段注释 |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |