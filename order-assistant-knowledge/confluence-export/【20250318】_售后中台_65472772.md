# 【20250318】 售后中台

## 一、背景

### 1.1 业务背景

### 1.2 痛点分析

云系统中，售后目前是没有很好的界限，没有区分退款单，售后系统混乱

1.O2O订单和B2C订单流程不规范 

2. 退款审核 退款下账 不规范 存在无法退款 无法下账等问题

3.O2O订单部分退款后部分订单金额存在错误存在差异

4.O2O订单多条退款明细问题

5.代码质量 可读性 可维护性差 等

针对以上问题：

业务方面：统一作业流程 优化作业流程 提高作业效率 提高运营效率 提升员工满意度 

代码方面 : 优化代码 提升可读性 提高可维护性 增强可扩展性 优化性能 减少技术债务

### 1.3 系统现状

业务现有流程图、架构等，为下述技术方案设计提供背景依据，尤其是已有功能的迭代改造。

运行架构

true老售后单架构falseautotoptrue13813

## 二、目标

### 2.1 本期目标

#### 2.1.1 业务目标

能够给业务带来的价值，目标应该是可量化衡量的，符合SMART的。
衡量ROI

#### 2.1.2 技术目标

优化代码 提升可读性 提高可维护性 增强可扩展性 优化性能 减少技术债务

### 2.1.3 中长期目标

愿景、长远规划

## 三、需求分析

### 3.1 场景分析

**需求分析环节，研发消化需求后从研发视角将需求讲清楚，项目团队产研测再次达成对业务和需求的一致理解。**
一般采用用例图、需求功能表格等形式归纳涉及模块和功能点，帮助项目团队人员对齐、消化需求。

一般的需求功能点表格具有局限性，要准确表达一个需求点，可以参考[用例表](https://www.docin.com/p-2627101093.html)，但不如用例图方便、清晰，所以采用用例图加必要的功能归纳表格来分析需求。

**（1）传统项目采用例驱动设计**：从需求场景出发，**用用例图****帮助团队理解系统的功能边界、功能范围与业务流程，确保团队成员和利益相关者对系统功能有共同的理解。**

**（2）****DDD项目采用领域驱动设计**：从业务出发，探求业务本质，**推荐事件风暴表格分析过程，**参考资料：****

**功能清单：**

| 客户端 | 模块 | 类型 | 功能描述 | 备注 |
| --- | --- | --- | --- | --- |
| 接单服务 | 新增B2C售后消息接收 | 优化 |  |  |
| 售后状态查询 | 新增 |  |  |
| 售后审核（同意/拒绝） | 优化 |  |  |
| 售后中台 | 新增申请售后流程（部分/全额） | 新增 |  |  |
| 售后审核（同意/拒绝） | 新增 |  |  |
| 生成逆向下账单流程 | 新增 |  |  |
| 逆向单下账流程 | 新增 |  |  |
| 售后单列表查询 | 新增 |  |  |
| 售后单导出 | 新增 |  |  |
| 手动新增售后单 | 新增 |  |  |
| 售后单归档 | 新增 |  |  |
| 物理中台 | 取消物流单 | 优化 |  |  |
| 新增物流单 | 优化 |  |  |
| 查询物流单状态 | 新增 |  |  |
| 订单中台 | 修改订单退款标识 | 新增 |  |  |
| 新增订单售后日志 | 新增 |  |  |
| 通知WMS取消订单 | 优化 |  |  |


**售后申请流程**

| 流程 | 说明 | 流程说明 | 备注 |
| --- | --- | --- | --- |
| 正单有 退单无 | 心云有正向，订单无售后的状态 ，平台下发待审核的售后单消息 | 1. 平台下发售后消息 2. 接口中台构建参数区分消息类型 3. 售后中台接受全额退款售后消息 4. 判断平台售后是否存在 存在--直接更新平台售后 新增平台售后单日志 5. 不存–在新增平台售后单 6. 查询订单发货状态--订单中台 7. 根据订单发货状态 新增售后单 新增售后日志 8. 通知订单中台修改退款标识 新增订单日志--订单中台 9. 总仓(WMS)订单推送取消消息 记录日志--订单中台 10. 判断是否取消配送单 调用物流中台取消物流单--物流中台 |  |
| 正单无 退单先至 | 因为平台原因MQ消息延迟 ，正向单，晚于售后单的状态 | 1. 平台下发售后消息 2. 接口中台构建参数区分消息类型 3. 售后中台接受全额退款售后消息 4. 判断平台售后是否存在 存在--直接更新平台售后 新增平台售后单日志 5. 不存--在新增平台售后单 6. 默认生成平台售后状态一致的系统售后单（内部单号缺失 无法通知订单中台修改订单退款标识） 7. 新增售后日志 | 订单退款标识何时修改方案：a.新增正向订单时查询是否有售后单 b.先只新增平台售后单 定时任务同步系统售后单 c.MQ直接重试不新增平台售后单等新订单落库后再新增售后单 |
| 正单有 平台直接下发已完成退款单 | 心云有正向，心云无售后单 平台直接下发最终状态的售后单消息 | 同流程1 | 状态待审核  虽然平台已完成但是系统售后单也需要审核 主要做记录作用 在审核时候校验售后单在平台状态 |
| 正单有 心云售后单A处理中 平台直接下发已完成退款单 | 心云有正向，心云一直未处理，售后单处于中间态，平台直接下发已完成 | 1. 平台下发售后消息 2. 接口中台构建参数区分消息类型 3. 售后中台接受全额退款售后消息 4. 判断平台售后是否存在 存在--直接更新平台售后 新增平台售后单日志 |  |
| 正单有 心云售后单A已完成 平台下发B待审核 | 心云有正向。多次申请售后 | 同流程1 | 平台不存在于同一个订单有多条中态的售后单状态 |


### 2.2 业务流程

产品优化后的业务流程，往往体现出基于现状的变化，分析是否解决业务的问题？是临时解决还是长期方案？把控需求的价值和合理程度

**全额退款申请处理流程**

true售后-全额退款falseautotoptrue215110

流程说明

1. 平台下发售后消息
2. 接口中台构建参数区分消息类型
3. 售后中台接受全额退款售后消息
4. 判断平台售后是否存在 存在--直接更新平台售后 新增平台售后单日志
5. 不存–在新增平台售后单
6. 查询订单发货状态--订单中台
7. 根据订单发货状态 新增售后单 新增售后日志
8. 通知订单中台修改退款标识 新增订单日志--订单中台
9. 总仓(WMS)订单推送取消消息 记录日志--订单中台
10. 判断是否取消配送单 调用物流中台取消物流单--物流中台


**部分退款申请处理流程**

true售后-部分退款falseautotoptrue21514

流程说明

**售后审核（同意/拒绝）**

true同意拒绝退款falseautotoptrue19115

流程说明 

### 2.3 非功能性需求

需求的流量、用户量、数据量、性能要求、核心技术挑战等非功能需求识别。

## 四、整体设计（概要设计/战略设计）

### 4.1统一语言定义

业务、技术名词解释等，为方案文档创造统一的上下文语境。

### 4.2 架构设计

[架构五视图](https://blog.csdn.net/yb223731/article/details/119613815?spm=1001.2101.3001.6650.10&utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-119613815-blog-5417210.235%5Ev43%5Epc_blog_bottom_relevance_base6&depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-119613815-blog-5417210.235%5Ev43%5Epc_blog_bottom_relevance_base6&utm_relevant_index=20)：逻辑架构、开发架构、物理架构、运行架构、数据架构，根据需要进行选择.后期主要是不断迭代演进的架构推演，改动的或新增的模块特殊颜色标识。

true售后新增架构falseautotoptrue9514

### 4.3 业务模型设计

（不仅仅是一个图，最好有业务建模分析的过程）

传统项目的ER实体关系图；

DDD项目的领域模型图。

| 业务域 | 领域模型 | 聚合 | 领域对象 | 领域类型 |
| --- | --- | --- | --- | --- |
| 用户 | 申请售后 |  |  |  |
| 员工 |  |  |  |  |
|  |  |  |  |  |


### 4.4 核心技术问题

需要探讨的核心技术问题，扩展性、数据迁移、兼容等核心技术挑战和解决方案

## 五、 详细设计（战术设计）

（以下模板可以根据情况调整目录结构）

### 1、 模块详细设计

分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比

### 2、 存储数据库设计

新增、修改的字段DDL；索引设计；数据量预估，分库分表设计；有时需要存储选型分析方案：缓存、es等

### 3、 接口设计

新增、修改的接口定义；流量预估，接口性能设计；

### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、项目排期

项目工时、分工等，贴jira连接

## 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等