# 20250605-checkList

- [一、上线内容](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appolo配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.2appolo%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 xxl-job配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.3xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 mq变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.4mq%E5%8F%98%E6%9B%B4)
  - [2.5 es配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.5es%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.6 其它配置变更...](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.6%E5%85%B6%E5%AE%83%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4...)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appolo配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.2appolo%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 xxl-job配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.3xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 mq变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.4mq%E5%8F%98%E6%9B%B4)
- [2.5 es配置变更](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.5es%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.6 其它配置变更...](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-2.6%E5%85%B6%E5%AE%83%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4...)
- [三、上线影响](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32330510#id-%E4%BC%98%E6%83%A0%E5%88%B8%E6%95%B0%E6%8D%AE%E6%9D%83%E9%99%90%E5%AE%A1%E6%89%B9%E6%B5%81%E4%B8%8A%E7%BA%BFchecklist-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 序号 | 需求或技术优化名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | 评价中台 | ydjia-merchant-customerevaluation-center | 并行 |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 数据库 | DDL / DML | 备注 | 负责人 |
| --- | --- | --- | --- | --- |
| 1 | evaluation_center | CREATE TABLE `evaluation_store` (  `id` bigint NOT NULL AUTO_INCREMENT,  `store_id` varchar(50) DEFAULT NULL COMMENT '门店',  `organization_code` varchar(20) DEFAULT NULL COMMENT '机构编码',  `organization_name` varchar(50) DEFAULT NULL COMMENT '机构名称',  `questionnaire_no` varchar(50) DEFAULT NULL COMMENT '关联问卷业务编码',  `redirect_url` varchar(200) DEFAULT NULL COMMENT '跳转url',  `qrcode_url` varchar(500) DEFAULT NULL COMMENT '小程序/二维码链接\n',  `total_evaluation_count` int DEFAULT '0' COMMENT '累计评价数量\r\n',  `positive_count` int DEFAULT '0' COMMENT '好评数量',  `negative_count` int DEFAULT '0' COMMENT '差评数量',  `other_count` int DEFAULT '0' COMMENT '其他评价数量',  `last_evaluate_time` datetime DEFAULT NULL COMMENT '最近评价时间',  `created` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `updated` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `version` bigint DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  UNIQUE KEY `uk_store` (`store_id`),  KEY `idx_questionnaire_no` (`questionnaire_no`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=13111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评价门店表'; |  | ## |
| 2 |  | CREATE TABLE `evaluation_questionnaire` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',  `questionnaire_no` varchar(50) NOT NULL COMMENT '问卷业务编号',  `questionnaire_name` varchar(20) DEFAULT NULL COMMENT '问卷名称',  `subject_type` varchar(10) NOT NULL COMMENT '适用主体类型（STORE-门店,COMMODITY-商品,ORDER-订单）',  `source_channel` varchar(10) DEFAULT NULL COMMENT '投放渠道(APPLET-小程序)',  `questionnaire_title` varchar(20) DEFAULT NULL COMMENT '问卷标题',  `description` varchar(500) DEFAULT NULL COMMENT '描述',  `person_type` varchar(10) DEFAULT NULL COMMENT '评价人类型(MEMBER-会员,NONMEMBER-非会员,ALL-所有人))',  `status` varchar(10) DEFAULT 'CLOSE' COMMENT '模版状态(OPEN-开启,CLOSE-关闭)',  `created` datetime DEFAULT NULL COMMENT '平台创建时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_questionnaire_no` (`questionnaire_no`) ) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='评价问卷表'; |  |  |
| 3 |  | CREATE TABLE `questionnaire_question` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',  `question_no` varchar(50) NOT NULL COMMENT '问题业务编号',  `questionnaire_no` varchar(50) DEFAULT NULL COMMENT '问卷业务编号',  `question_text` varchar(100) NOT NULL COMMENT '问题内容',  `question_type` varchar(20) NOT NULL COMMENT '问题类型(RATING-评分题,SINGLE_CHOICE-单选题，MULTIPLE_CHOICE-多选题，TEXT-文字题,PICTIURES-图片题)',  `is_required` tinyint DEFAULT '0' COMMENT '是否必答(0-否,1-是)',  `display_order` int DEFAULT NULL COMMENT '显示顺序',  `options` json DEFAULT NULL COMMENT '选项配置(适用于选择题)',  `min_rating` int DEFAULT NULL COMMENT '最小评分(适用于评分题)',  `max_rating` int DEFAULT NULL COMMENT '最大评分(适用于评分题)',  `is_valid` tinyint DEFAULT '1' COMMENT '是否有效(0-否，1-是)',  `created` datetime DEFAULT NULL COMMENT '平台创建时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_questionnaire_question_no` (`questionnaire_no`,`question_no`) ) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问卷问题表'; |  |  |
| 4 |  | CREATE TABLE `questionnaire_response` (  `id` bigint NOT NULL AUTO_INCREMENT,  `response_no` varchar(50) NOT NULL COMMENT '回答业务编码',  `questionnaire_no` varchar(50) DEFAULT NULL COMMENT '问卷业务编码',  `questionnaire_type` varchar(10) DEFAULT NULL COMMENT '问卷适用类型(STORE-门店,COMMODITY-商品,ORDER-订单)',  `organization_code` varchar(20) DEFAULT NULL COMMENT '所属门店',  `organization_name` varchar(50) DEFAULT NULL COMMENT '所属门店名称',  `source_channel` varchar(20) DEFAULT NULL COMMENT '评价来源渠道(APPLET-小程序)',  `user_id` varchar(50) DEFAULT NULL COMMENT '用户ID',  `user_type` varchar(10) DEFAULT NULL COMMENT '用户类型(MEMBER-会员,NONMEMBER-非会员)',  `user_mobile` varchar(50) DEFAULT NULL COMMENT '用户手机号',  `evaluate_time` datetime NOT NULL COMMENT '评价时间',  `evaluation_grade` varchar(10) DEFAULT NULL COMMENT '评价等级（POSITIVE-好评，NEGATIVE-差评，NEUTRAL-中评）',  `is_reply` varchar(10) DEFAULT 'FALSE' COMMENT '门店是否回复（TRUE-是，FALSE-否）',  `reply_content` varchar(255) DEFAULT NULL COMMENT '门店回复内容',  `created` datetime DEFAULT NULL COMMENT '平台创建时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_response_no` (`response_no`),  KEY `idx_questionnaire_no` (`questionnaire_no`) ) ENGINE=InnoDB AUTO_INCREMENT=32003 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问卷回答表'; |  |  |
| 5 |  | CREATE TABLE `questionnaire_response_detail` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',  `response_no` varchar(50) NOT NULL COMMENT '回答业务编号',  `question_no` varchar(50) DEFAULT NULL COMMENT '问题业务编号',  `rating_value` int DEFAULT NULL COMMENT '评分值(适用于评分题)',  `choice_value` varchar(100) DEFAULT NULL COMMENT '选项值(用于选项题)',  `text_value` text COMMENT '文本回答(用于开放题)',  `response_type` varchar(20) DEFAULT NULL COMMENT '回答类型(ORDER-订单，COMMODITY-商品)',  `relation_no` varchar(50) DEFAULT NULL COMMENT '关联编号（平台订单号/商品编码）',  `is_valid` tinyint DEFAULT '1' COMMENT '是否有效(0-否，1-是)',  `created` datetime DEFAULT NULL COMMENT '平台创建时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_response_no` (`response_no`) ) ENGINE=InnoDB AUTO_INCREMENT=191943 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问卷回答明细表'; |  |  |
| 6 |  | CREATE TABLE `evaluation_export_history` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',  `export_no` varchar(64) NOT NULL COMMENT '导出记录编号',  `file_name` varchar(255) NOT NULL COMMENT '文件名称',  `file_url` varchar(1000) DEFAULT NULL COMMENT '文件URL',  `export_condition` text COMMENT '导出条件(JSON格式)',  `export_count` int DEFAULT '0' COMMENT '导出数量',  `export_status` varchar(32) NOT NULL COMMENT '导出状态(PROCESSING-处理中,SUCCESS-成功,FAIL-失败)',  `fail_reason` varchar(1000) DEFAULT NULL COMMENT '失败原因',  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',  `created` datetime DEFAULT NULL COMMENT '创建时间',  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',  `updated` datetime DEFAULT NULL COMMENT '更新时间',  PRIMARY KEY (`id`),  UNIQUE KEY `uk_export_no` (`export_no`) USING BTREE,  KEY `idx_created_by` (`created_by`) USING BTREE,  KEY `idx_created` (`created`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导出历史记录表'; |  |  |


#### 2.2 Appolo 配置变更

| 序号 | 服务 | 配置文件 | 变更方式(新增/修改) | 变更内容 | 负责人 |
| --- | --- | --- | --- | --- | --- |
| 1 | evaluation-center | ``` evaluation:   page: -    store: activity/commentDetail/index+    store: activity/commentDetail/index   store: b6b1bee45c2b421f9a4dad2486d2ec5c ``` |  |  |  |
| 2 | businesses-gateway | ``` # 评价中台         - id: evaluation-center           uri: lb://evaluation-center           predicates:             - Path=/evaluation-center/**           filters:             - StripPrefix=1 ``` |  |  |  |


#### 2.3 xxl-job 配置变更

| 序号 | **操作类型** | **名称(KEY)** | **方法参数** | **Cron** | **描述** | **负责人** |
| --- | --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |  |
| 2 |  |  |  |  |  |  |
| 3 |  |  |  |  |  |  |


#### 2.4 MQ 变更

#### 2.5 ES 配置变更

| 序号 | **操作类型** | **KEY** | **value** | **描述** | **负责人** |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


#### 2.6 数据修复任务

#### 2.7 其它配置变更

| 序号 | **操作类型** | **KEY** | **value** | **描述** | **负责人** |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |
| 2 |  |  |  |  |  |


### 三、上线影响

### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 complete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 complete |  |
| Master打tag | 21 complete |  |
| 代码合并Master | 22 complete |  |
| 预发配置变更 | 23 complete |  |
| 预发验证 | 24 complete |  |
| 依赖check | 6 complete |  |
| 上线周知产研 | 30 complete |  |
| 生产配置变更 | 25 complete |  |
| 生产发布 | 26 complete |  |
| 生产验证 | 27 complete |  |
| 日志、告警观察 | 28 complete |  |
|  |  |  |