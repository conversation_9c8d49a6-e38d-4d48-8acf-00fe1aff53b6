# 25年第12周2025-03-28

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | business-gateway timeout [url:/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001](http://url/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001) |  |  | 暂不处理， 获取用户可用平台编码，接口中只有一个查询会员项目组接口的逻辑，已经和会员组说过了 ，后面可能通过增加缓存优化性能。目前这个接口已经没有出现慢接口，考虑是当时服务不稳定导致的。 |
| 2 | business-gateway timeout [url:/dscloud/1.0/duty-cash](http://url/dscloud/1.0/duty-cash) |  |  | 订单拣货记录（订单班次收银员信息），大表4000万数据，下账时会查询拣货人员信息，预计删除今年以前的数据（分批次删除-今天先删除一批次） |
| 3 | trueSELECT  id,order_no,order_state,erp_state,third_platform_code,third_order_no, third_order_id,third_order_state,off_state,freight_order_no,mer_code,client_code, online_store_code,online_store_name,organization_code,organization_name, delivery_time_type,delivery_time_desc,buyer_remark,buyer_message,seller_remark, lock_flag,lock_msg,locker_id,remind_flag,buyer_name,receiver_lat,receiver_lng, acceptor_id,acceptor_name,accept_time,picker_id,picker_name,pick_operator_id ,pick_operator_name,pick_time,canceller_id,canceller_name,cancel_reason, cancel_time,ex_operator_id,ex_operator_name,ex_operator_time,complete_time, created,day_num,modified,erp_adjust_no,erp_sale_no,self_verify_code, prescription_flag,bill_time,create_time,modify_time,call_erp_flag, member_no,transfer_delivery,client_conf_id,bill_operator,appointment, appointment_business_flag,appointment_business_type,request_deliver_goods_result, deliver_goods_refuse_reason,is_prescription,prescription_status, is_push_check,new_customer_flag,integral_flag,need_invoice,invoice_title, invoice_type,invoice_content,taxer_id,source_online_store_code, source_online_store_name,source_organization_code,source_organization_name, complex_modify_flag,medical_insurance,service_mode,cancel_bill_times, wsc_ext_json,top_hold,order_type,order_is_new,order_pick_type, source_channel_type,migration_order_no,extend_info,data_version, remark,deleted,pay_time  FROM order_info   WHERE  deleted=0 AND erp_state IN (30,99)  AND order_state IN (30,40,100) AND create_time >= '2025-03-01 00:00:00.0'   AND service_mode = 'O2O' LIMIT 0,100;   SELECT COUNT(1) FROM order_info WHERE deleted = 0 AND erp_state IN (30, 99)  AND order_state IN (30, 40, 100) AND create_time >= '2025-03-01 00:00:00.0' AND service_mode = 'O2O'; |  |  |  |
| 4 | trueselect 1 status,count(1) as num  from  oms_order_info a  where  1=1   and a.mer_code='500001'   and a.order_status in (5,10,30,40)   and a.deleted=0    and a.is_post_fee_order = 0   and a.ex_status = 1  and a.created >= ADDDATE( ADDDATE( curdate(), INTERVAL - 1 MONTH ), INTERVAL 1 DAY ) and a.created < ADDDATE(CURDATE(),INTERVAL 1 day)and a.warehouse_id in   ( 'WSC1111'    ) and a.online_store_code in   ( 'WSC1111'   , |  |  | 大in,暂不处理，可以改代码走es查询 |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
|  | 微商城-交易链路切换 | 交易中台    支付中台 |  |  |
|  | 微商城-小前台能力替换 |  |  |  |
|  | 新老模型,数据双向同步 |  |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** 1.售后中台技术方案 70 %2.积分订单问题3.商品库存占用释放问题修改 10% | **遗留问题**售后中台技术方案**风险问题** | **需求研发**售后中台技术方案**技术建设** |  |
| 2 |  | **本周总工时：4d**1. 会员消费记录支持POS查询联调 2. 删除海典订单迁移重复数据,DB数据已删除，ES数据处理中 3. 配合大数据排查线下单漏单 4. 线下单管理后台开发,5% | **遗留问题** **风险问题**迁移订单平台判断不准,导致单子相同但是平台不同，进而重复（历史已知问题）。在处理海典重复数据时,发现一个海典订单在hana存储的数据特征,正在确认是否可以利用这个特征做精确的平台判断 | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：4d**1. B2B上线+pre验证支持 2. 其他线上问题支持   1. 订单转正常单异常   2. 订单配送取消异常 3. 订单转正常单异常 4. 订单配送取消异常 | **遗留问题**  3 incomplete **PDD复盘、切店流程（没有处理）**   4 incomplete **接新平台流程文档**   **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. B2B上线+pre验证支持 2. 合单退款-parentAfterSale字段维护 3. ES查询优化，easy-es使用 filter 查询调研+验证(must、filter、should、must_not)----验证中 | **遗留问题****风险问题** | **需求研发****技术建设** |  |
| 5 |  | **本周总工时：5d**1. 慢接口排查优化 2. 梳理店铺中台切换接口 3. 评价中台需求评审，编写代码 4. 线上问题支持 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 6 |  | **本周总工时：3d**1.慢病福利支持和订单标签技术文档2.下账主库查询更新、海典下账补偿任务调整3.business-order 拣货流程升级4.线上B2C订单问题处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1. B2B 上线 + pre验证支持 + 问题修改 2. B2C下账请求海典报异常导致下账失败问题处理+问题下账单处理 3. ydjia-merchant-customer 项目购物相关代码梳理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 平台对账开发（60%） 2. O2O已换货商品退款单下账失败bug修复 3. B2C查询金额范围条件失效、物流拦截成功bug修复 4. 日常事务：3月份B2C备注单数据支持 5. 平台对账测试用例评审 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. bug修复   1. 关闭支付单时，补偿更新支付状态，关闭后发送回调   2. 回调失败问题解决   3. 定时关闭优先处理最新的单子   4. 取消订单参数错误，查询过期的sql改写   5. 取消订单bug修复，修改提示语   6. 取消支付失败，更新为支付失败的用待支付代替   7. 查询支付详情接口修改   8. 定时关闭超时支付单，同步关闭三方支付单问题修复 2. 关闭支付单时，补偿更新支付状态，关闭后发送回调 3. 回调失败问题解决 4. 定时关闭优先处理最新的单子 5. 取消订单参数错误，查询过期的sql改写 6. 取消订单bug修复，修改提示语 7. 取消支付失败，更新为支付失败的用待支付代替 8. 查询支付详情接口修改 9. 定时关闭超时支付单，同步关闭三方支付单问题修复 10. B2B余额支付项目上线 11. 支付中台总体设计 12. 无正向单退款,保存退款失败单 13. 无正向单退款,支持正常退款 14. 动态路由   1. 功能自测   2. 业务网关添加配置取消动态路由   3. 合并master,对外提供使用 15. 功能自测 16. 业务网关添加配置取消动态路由 17. 合并master,对外提供使用 | **遗留问题**动态路由上master**风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) | 无 |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 | 处理情况 |
| --- | --- | --- | --- | --- |
| 1 | trueSELECT  id,order_no,order_state,erp_state,third_platform_code,third_order_no, third_order_id,third_order_state,off_state,freight_order_no,mer_code,client_code, online_store_code,online_store_name,organization_code,organization_name, delivery_time_type,delivery_time_desc,buyer_remark,buyer_message,seller_remark, lock_flag,lock_msg,locker_id,remind_flag,buyer_name,receiver_lat,receiver_lng, acceptor_id,acceptor_name,accept_time,picker_id,picker_name,pick_operator_id ,pick_operator_name,pick_time,canceller_id,canceller_name,cancel_reason, cancel_time,ex_operator_id,ex_operator_name,ex_operator_time,complete_time, created,day_num,modified,erp_adjust_no,erp_sale_no,self_verify_code, prescription_flag,bill_time,create_time,modify_time,call_erp_flag, member_no,transfer_delivery,client_conf_id,bill_operator,appointment, appointment_business_flag,appointment_business_type,request_deliver_goods_result, deliver_goods_refuse_reason,is_prescription,prescription_status, is_push_check,new_customer_flag,integral_flag,need_invoice,invoice_title, invoice_type,invoice_content,taxer_id,source_online_store_code, source_online_store_name,source_organization_code,source_organization_name, complex_modify_flag,medical_insurance,service_mode,cancel_bill_times, wsc_ext_json,top_hold,order_type,order_is_new,order_pick_type, source_channel_type,migration_order_no,extend_info,data_version, remark,deleted,pay_time  FROM order_info   WHERE  deleted=0 AND erp_state IN (30,99)  AND order_state IN (30,40,100) AND create_time >= '2025-03-01 00:00:00.0'   AND service_mode = 'O2O' LIMIT 0,100;   SELECT COUNT(1) FROM order_info WHERE deleted = 0 AND erp_state IN (30, 99)  AND order_state IN (30, 40, 100) AND create_time >= '2025-03-01 00:00:00.0' AND service_mode = 'O2O'; |  | 徐国华 | 已解决 |
| 2 | SELECT id,create_by,create_time,update_by,update_time,oms_order_no,waybill_code,document,platform_code,wp_code,logistic_config_id,ds_online_store_express_merchant_id,ext_oms_order_no,print_num,status FROM logistic_order WHERE status=1 AND waybill_code IN ('73547594423007') AND status = 1; |  | 杨俊峰 |  |
| 3 | select lo.waybill_code, [ltm.id](http://ltm.id) as logisticTemplatesManageId, [ltm.name](http://ltm.name) as logisticTemplatesManageName from logistic_order lo left join ds_online_store_express_merchant dosem on lo.ds_online_store_express_merchant_id = [dosem.id](http://dosem.id) left join logistic_templates_manage ltm on [ltm.id](http://ltm.id) = dosem.logistic_template_manage_id where lo.waybill_code in ( '9475616526541' ); |  | 杨俊峰 |  |
| 4 | SELECT COUNT(1) FROM platform_refund_order pro, ds_online_store dos WHERE pro.refund_type IN (0, 1, 2) AND pro.clientid = dos.online_client_code AND dos.platform_code = pro.ectype AND service_mode = 'B2C' AND pro.groupid = '500001' AND pro.groupid IN ('500001') AND EXISTS (SELECT 1 FROM oms_order_info o WHERE o.third_order_no = pro.olorderno AND o.third_platform_code = pro.ectype AND o.order_owner_type = 0) AND pro.`status` = '10' AND pro.clientid IN ('WSC1111', '4d5025a7286f4423b3f3bf8ab5594297', '501b12cbc3fe4becab17c47c8ba57638', '7e37d69a6ef64d13817111ae4f3f879f', '83fe15fb0c0b43469b2276a13e212dc7', '73958d36e72047b4b6d9d7682711c74c', '72393f4b6b684c4ab771b625fdfe247f', 'b4921ae5141143a0971b642c51865d1c', '16c7cdaa8a4f4fd08feef2771f475fb3', '7deac791e40d4f7493044bf355528dd7', 'b483154f34da431fbed7918152df493d', 'f7ce86cec17f4462adce92f8a7d3373c', 'af306308a6c74b3bb5fa4e8906c4f6c5', 'edca6231d45449829ccce1114e7d50ee', 'f4a7b88bc216471fa09006e1617b5b73', 'e8fa6d017fee4795a5f7d06758e1be7b', '5a216d92736a49c0b1e69c73ffec8c39', '5eb468c72fc144a5aaa6d68b1d670bc3', 'd2b4b75093824d92af441e2e6a4660d9', '06e33a96f09c4895a42eb868ec6b10be', 'e477615eedb148cc93e66d82936fb011', '8998ad4207f1496ebc98a91599f19512', 'fe4b24eece3c4e29910bbd324c13b1c3', 'c5b3ff4929274602a72b07d665a13a06', 'ed7087a1ea9f42c4b6bd8aeffa798570', 'eee5219664f94019983cf3c2fdcb1e86', '8b27b47861304b259ac232179b571d66', '4af07c5377b842cb993f6652c69f65e4', '4db39e0d90b34658b70b4ee10290dee2', '9f746a1a71714ffaadf1c3a2888224e1', '6d640f7c17014c76be489bb16eaa1c07', 'a2b3b59c41ba41f0b40d15d94c4c368d', '3f7c518923e548e5ac3fc6e8f31fbae0', 'c2c2648caa934449b8c1f62d0adbd583', '030ec62676fe452bb65a001784d16c59', 'edea4a43cd484b0c9e46c50c5d154695', '2c1bd00956204313b43feb813c9d4aec', '6b7b54cff6364f0d80c028252afc3bc2', '2acd7fc7fbc948cba92fdc6e29af60f7', 'de0cf57630d743a1891e47d7a468487f', 'a1960862a25c40feb4d9b3f7d0db2c6b', '31f7998daa4d4859979678822056a8ab', '11f75f47651e43b094e593a4e277a7d5', '3272d73a64c44236b7405ea220729293', '4a1b11c54d5748aea6bbf337ff7188d2', 'f0c39d8e942a4a57a663a4061ef1be5a', '79c08768179844fe848288e8bf60878f', '91b6709fab3e427cb9c7602df8ec3e52', 'aa2d6de522074d4280d09ea94f85e06f', 'aaf7849a86f0416f87b11e75f34f2105', '6de803cc48ca487f96b7e6bf90ea9a2d', 'ed28d8c6bffb4c96a7b29639d572125b', '0a767d0ab4f44129a88d5ddd3f4cd384', '5707ab60babe410682623450bcd102b6', 'b24e28310fec4026ac34eb6b43128623', 'a0d0e219578b4aacb030810323c26c27', 'c0822bdbbbbb4fee8150d3cdaffba8f3', '1416c3766b82427984fb1f2c6fd3b33a', 'da278abf67ff4eb7a54eb00313f5c77f', '38ab2ef06d664a968f575b01f3914353', '83456e3f4f1546fa9559a5ea4299dcb6', '50d4c86dcc294562bb1675a80822c1ce', '160a585d8af24bdfa0c47a056785a431') AND pro.ectype IN ('24', '3008', '27', '3003', '43'); |  | 李洋 | 暂不处理，B2C平台退款单列表查询条件（带权限） |
| 5 | SELECT COUNT(1) FROM oms_order_info a WHERE 1 = 1 AND a.deleted = 0 AND a.mer_code = '500001' AND a.order_owner_type = 0 AND a.order_status IN (40, 100) AND a.ship_time >= '2025-03-28 00:00:00' AND a.ship_time <= '2025-03-28 23:59:59' AND a.is_post_fee_order = 0 AND a.ex_status = 0 AND a.express_id = 1004 AND a.warehouse_id IN ('WSC1111', '100400069', '10064406', '4100960245304549721') AND a.online_store_code IN ('WSC1111', '501b12cbc3fe4becab17c47c8ba57638', 'b24e28310fec4026ac34eb6b43128623', '5a216d92736a49c0b1e69c73ffec8c39', 'ed7087a1ea9f42c4b6bd8aeffa798570', '160a585d8af24bdfa0c47a056785a431') AND a.third_platform_code IN ('24', '3008', '27', '3004', '3003', '43'); |  | 王世达 | 刷数关闭ES查询出现的问题，已开启ES查询 |
| 6 | select 1 status,count(1) as num from oms_order_info a where 1=1 and a.mer_code='500001' and a.order_status in (5,10,30,40) and a.deleted=0 and a.is_post_fee_order = 0 and a.ex_status = 1 and a.created >= ADDDATE( ADDDATE( curdate(), INTERVAL - 1 MONTH ), INTERVAL 1 DAY ) and a.created < ADDDATE(CURDATE(),INTERVAL 1 day) and a.warehouse_id in ( 'WSC1111' , '100130089' , '10059892' ) and a.third_platform_code in ( '24' , '27' , '3004' , '3003' , '43' ) and a.online_store_code in ( 'WSC1111' , '38ab2ef06d664a968f575b01f3914353' , '8998ad4207f1496ebc98a91599f19512' , '83456e3f4f1546fa9559a5ea4299dcb6' , '79c08768179844fe848288e8bf60878f' , 'c2c2648caa934449b8c1f62d0adbd583' , '0a767d0ab4f44129a88d5ddd3f4cd384' , '5eb468c72fc144a5aaa6d68b1d670bc3' , '1416c3766b82427984fb1f2c6fd3b33a' , '6d640f7c17014c76be489bb16eaa1c07' ); |  | 王世达 | 刷数关闭ES查询出现的问题，已开启ES查询 |
| 7 | /api-gateway/posOrderApi/orderaccounting/v1 |  | 蒋一良 |  |
| 8 | /businesses-gateway/b2c/1.0/order/page/normal |  | 杨国枫 | 分页查询 已经是走了ES了 在优化涉及代码大改 |
| 9 | /businesses-gateway/b2c/1.0/export/commodity_summary_detail_export/action |  | 杨国枫 | B2C商品汇总导出 无须处理 |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |