# 2024-07-04 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1.需求-微商城云仓虚拟商品订单 | ydjia-merchant-customer | 1 | feature-ORDER-297 |  |  | @汪骁 |  |  |  |  |
| hydee-middle-order | 2 |  |  |  |  |  |  |  |
| hydee-business-order-web |  |  |  |  |  |  |  |  |
| middle-datasync-message |  |  |  |  |  |  |  |  |
| h3-pay-finance |  |  |  |  |  |  |  |  |
| hydee-business-order |  |  |  |  |  |  |  |  |
| ydjia-merchant-manager | 3 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | dscloud.dict_order_export_column |  | INSERT INTO dict_order_export_column (service_type, column_code, column_name, seq, status, create_time,  modify_time, `require`, need_merge, value_mapping, default_choose) VALUES  (25, 'orderId', '订单号', 1, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'commodityName', '核销商品名称', 2, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'commodityCode', '核销商品编码', 3, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'spCode', '服务商编码', 4, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'createdTime', '核销时间', 5, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'verifyQuantity', '核销/撤销数量', 6, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'residualQuantity', '剩余数量', 7, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'remark', '核销备注', 8, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'status', '核销状态', 9, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'createdBy', '核销人', 10, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'updatedTime', '撤销时间', 11, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1),  (25, 'updatedBy', '撤销人', 12, 1, '2020-12-03 20:23:55', '2020-12-03 20:23:55', 0, 1, null, 1); |
|  | verify_record | 虚拟商品核销表 | CREATE TABLE `verify_record` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',  `order_id` bigint NOT NULL COMMENT 'middler库order_info id order_id',  `order_detail_id` bigint NOT NULL COMMENT 'middler库order_detail_id',  `commodity_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编码',  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',  `status` tinyint NOT NULL COMMENT '核销状态(1.已核销2.撤销)',  `verify_quantity` int NOT NULL COMMENT '核销/撤销数量',  `residual_quantity` int NOT NULL COMMENT '剩余核销数量',  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '核销备注',  `sp_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商编码',  `isvalid` tinyint NOT NULL DEFAULT '1' COMMENT 'isvalid',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `version` int NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_order_id` (`order_id`) USING BTREE,  KEY `idx_status` (`status`) USING BTREE,  KEY `idx_commodity_code` (`commodity_code`) USING BTREE,  KEY `idx_created_time` (`created_time`) USING BTREE,  KEY `idx_order_detail_id` (`order_detail_id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='虚拟商品核销记录表'; |
|  | middle_order | middle_order | ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `verification_quantity` int(0) NULL DEFAULT 0 COMMENT '虚拟商品待核销数量' AFTER `promotion_ratio`,ALGORITHM = instant;ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `is_virtual` tinyint(0) NULL DEFAULT 0 COMMENT '发货类型(0：实物发货，1：虚拟核销)' AFTER `verification_quantity`,ALGORITHM = instant; |
|  | middle_order | middle_order | ALTER TABLE `middle_order`.`order_info`  ADD COLUMN `is_virtual_goods_order` tinyint(255) NULL DEFAULT 0 COMMENT '是否虚拟订单，0：否，1：是' AFTER `emp_code`,ALGORITHM = instant; |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |