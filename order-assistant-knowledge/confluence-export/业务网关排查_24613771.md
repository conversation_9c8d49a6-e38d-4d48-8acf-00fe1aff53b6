# 业务网关排查

ERROR|r.n.http.server.HttpServerOperations:319|[id: 0xa8c3fa05, L:/10.42.8.225:9000 - R:/10.42.5.0:38494] Error finishing response. Closing connection
io.netty.handler.codec.EncoderException: io.netty.util.IllegalReferenceCountException: refCnt: 0, decrement: 1

初步判断是多释放了一次,框架会自己处理释放,不用手动释放

  public DefaultDataBuffer join(List<? extends DataBuffer> dataBuffers) {
    Assert.notEmpty(dataBuffers, "DataBuffer List must not be empty");
    int capacity = dataBuffers.stream().mapToInt(DataBuffer::readableByteCount).sum();
    DefaultDataBuffer result = this.allocateBuffer(capacity);
    dataBuffers.forEach((xva$0) -> {
      result.write(new DataBuffer[]{xva$0});
    });
    dataBuffers.forEach(DataBufferUtils::release); // 这里框架会自动释放
    return result;
  }

-Dio.netty.leakDetectionLevel=advanced

```
databuffer-release-twice
```