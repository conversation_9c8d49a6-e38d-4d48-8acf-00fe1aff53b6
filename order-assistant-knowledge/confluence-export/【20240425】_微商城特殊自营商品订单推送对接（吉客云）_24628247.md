# 【20240425】 微商城特殊自营商品订单推送对接（吉客云）

- [一、 背景](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%B8%80%E3%80%81%E8%83%8C%E6%99%AF)
  - [1.1 业务背景](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.1%E4%B8%9A%E5%8A%A1%E8%83%8C%E6%99%AF)
  - [1.2 痛点分析](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.2%E7%97%9B%E7%82%B9%E5%88%86%E6%9E%90)
  - [1.3 系统现状](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.3%E7%B3%BB%E7%BB%9F%E7%8E%B0%E7%8A%B6)
- [1.1 业务背景](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.1%E4%B8%9A%E5%8A%A1%E8%83%8C%E6%99%AF)
- [1.2 痛点分析](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.2%E7%97%9B%E7%82%B9%E5%88%86%E6%9E%90)
- [1.3 系统现状](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-1.3%E7%B3%BB%E7%BB%9F%E7%8E%B0%E7%8A%B6)
- [二、 需求分析](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%BA%8C%E3%80%81%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90)
  - [2.1 业务流程](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-2.1%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B)
  - [2.2 需求功能点](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-2.2%E9%9C%80%E6%B1%82%E5%8A%9F%E8%83%BD%E7%82%B9)
- [2.1 业务流程](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-2.1%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B)
- [2.2 需求功能点](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-2.2%E9%9C%80%E6%B1%82%E5%8A%9F%E8%83%BD%E7%82%B9)
- [三、目标](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%B8%89%E3%80%81%E7%9B%AE%E6%A0%87)
  - [3.1 本期目标](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-3.1%E6%9C%AC%E6%9C%9F%E7%9B%AE%E6%A0%87)
  - [四、整体设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E5%9B%9B%E3%80%81%E6%95%B4%E4%BD%93%E8%AE%BE%E8%AE%A1)
  - [4.1统一语言定义](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-4.1%E7%BB%9F%E4%B8%80%E8%AF%AD%E8%A8%80%E5%AE%9A%E4%B9%89)
- [3.1 本期目标](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-3.1%E6%9C%AC%E6%9C%9F%E7%9B%AE%E6%A0%87)
- [四、整体设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E5%9B%9B%E3%80%81%E6%95%B4%E4%BD%93%E8%AE%BE%E8%AE%A1)
- [4.1统一语言定义](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-4.1%E7%BB%9F%E4%B8%80%E8%AF%AD%E8%A8%80%E5%AE%9A%E4%B9%89)
- [五、 详细设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%BA%94%E3%80%81%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1)
  - [5.1 详细模块设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.1%E8%AF%A6%E7%BB%86%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1)
  - [5.2 存储数据库设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.2%E5%AD%98%E5%82%A8%E6%95%B0%E6%8D%AE%E5%BA%93%E8%AE%BE%E8%AE%A1)
  - [5.3 接口设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.3%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1)
- [5.1 详细模块设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.1%E8%AF%A6%E7%BB%86%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1)
- [5.2 存储数据库设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.2%E5%AD%98%E5%82%A8%E6%95%B0%E6%8D%AE%E5%BA%93%E8%AE%BE%E8%AE%A1)
- [5.3 接口设计](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-5.3%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1)
- [六、 质量效率](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E5%85%AD%E3%80%81%E8%B4%A8%E9%87%8F%E6%95%88%E7%8E%87)
- [七、 里程碑](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%B8%83%E3%80%81%E9%87%8C%E7%A8%8B%E7%A2%91)
- [八、 项目排期](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E5%85%AB%E3%80%81%E9%A1%B9%E7%9B%AE%E6%8E%92%E6%9C%9F)
- [九、 上线方案](https://yxtcf.hxyxt.com/pages/resumedraft.action?draftId=24628267&draftShareId=e0a587cb-952d-406d-9800-521c94255fc9&#id-%E3%80%9020240425%E3%80%91%E5%BE%AE%E5%95%86%E5%9F%8E%E7%89%B9%E6%AE%8A%E8%87%AA%E8%90%A5%E5%95%86%E5%93%81%E8%AE%A2%E5%8D%95%E6%8E%A8%E9%80%81%E5%AF%B9%E6%8E%A5%EF%BC%88%E5%90%89%E5%AE%A2%E4%BA%91%EF%BC%89-%E4%B9%9D%E3%80%81%E4%B8%8A%E7%BA%BF%E6%96%B9%E6%A1%88)


## 一、 背景

### 1.1 业务背景

1. 腾药牙膏作为当前私域准备主要推广的分销商品，该供应商由吉客云对接订单推送的需求（一心到家产生的腾药订单数据提供接口给对方查询并进行发货）


### 1.2 痛点分析

1. 腾药商品作为三方商品,在老系统中有其特有的销售方式,但在心云当中还未对接


### 1.3 系统现状

1. 腾药商品作为公域流量转为私域流量的重要商品,并未在微商城中有合适的售卖方式


## 二、 需求分析

### 2.1 业务流程

1.1.9 微商城特殊自营商品订单推送对接（吉客云）&分栏装修组件&装修组件通用方案

### 2.2 需求功能点

1. 吉客云商品配置文件;
2. 吉客云订单正向流程接口对接;
3. 腾药订单退款消息通知;
4. 门店打卡领心币活动


## 三、目标

### 3.1 本期目标

1. 增加腾药商品对码配置文件用于腾药订单识别；
2. 提供腾药订单批量下载接口；
3. 提供物流信息更新接口；
4. 提供吉客云校验订单退款状态接口


### 四、整体设计

### 4.1统一语言定义

**4.2 流程图**

**4.2.1 腾药订单正向流程接口对接**

true订单正向流程false600autotoptrue7813

备注：芒果数据保存7天，是否拉取 拉取次数，最近一次拉取时间 

B2C 发货后才能运行吉客云拉取订单

**4.2.2 腾药订单下载流程**

true吉客云下载订单流程false600autotoptrue3712

**4.2.3 更新发货信息流程**

true吉客云发货流程false600autotoptrue9415

**4.2.4 校验订单退款状态接口流程**

**备注：吉客云查询心云订单状态判断是否允许发货（心云没任何处理）**

trueCopy of 吉客云发货流程falseautotoptrue4615

**4.2.5 补推吉客云订单到芒果**

## 五、 详细设计

### 5.1 详细模块设计

### 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | oms_package_info | 包裹信息表 | alter table oms_package_info add express_id int null comment '快递id';  alter table oms_package_info add express_name varchar(50) null comment '快递名称'; |


### 5.3 接口设计

1. **API-订单下载接口**
  1. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| OrderStatus | string | 条件 | 订单交易状态(等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05，其他=JH_98（根据订单号查详情时），所有订单=JH_99)（吉客云默认下载JH_01,JH_02,JH_05三种状态的订单, 网店管家默认下载JH_02 状态下订单但ERP只会处理JH_02待发货的订单）如果想要下载JH_04已完成订单需要在网店配置中进行配置 | JH_02 |
| PlatOrderNo | string | 可选 | 平台订单号，若不为空，则代表查询单个订单的数据，查询单个订单时，可不传时间、状态等 | NO2545661 |
| StartTime | datetime | 条件 | 开始时间(格式:yyyy-MM-dd HH:mm:ss) | 2016-06-15 12:23:32 |
| EndTime | datetime | 条件 | 截止时间(格式:yyyy-MM-dd HH:mm:ss) | 2016-07-15 08:32:00 |
| TimeType | string | 可选 | 订单时间类别(订单修改时间=JH_01，订单创建时间=JH_02) (ERP抓单默认JH_02) | JH_02 |
| PageIndex | int | 必填 | 页码 | 1 |
| PageSize | int | 必填 | 每页条数 | 30（默认） |
  2. 请求示例```
{  
     "OrderStatus":"JH_02",  
     "PlatOrderNo":"",  
     "StartTime":"2016-07-26 10:59:10",  
     "EndTime":"2016-08-02 10:59:10",  
     "TimeType":"JH_02",  
     "PageIndex":"1",  
     "PageSize":"20"  
 }
```
  3. **响应参数**| numtotalorder | int | 必填 | 订单总数量(这里返回的是符合条件的所有订单的总数，而不是当页订单的数量，例如总数为51，请求参数中每页为30，则应返回51)。第一页会去取两次，第一次去是为了获取订单总数，第二次去取才会按照正常分页走。 | 51 |
| **orders** | **OrderItem[]** | **必填** | **订单 集合********若没有订单也必须返回空数组** | **-** |
| PlatOrderNo | string | 必填 | 订单号 | NO2545661 |
| tradeStatus | string | 必填 | 订单交易状态(等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05，卖家部分发货=JH_08)（返参中的订单状态需要跟请求参数中的订单状态保持一致，否则会被过滤）（目前会抓取JH_01,JH_02,JH_04三种状态的订单, 但ERP只会处理JH_02待发货的订单） | JH_02 |
| tradeStatusdescription | string | 可选 | 订单交易说明 | 等待卖家发货 |
| tradetime | datetime | 必填 | 交易时间(格式:yyyy-MM-dd HH:mm:ss) 起始时间格式不可以为0000-00-00 | 1900-01-01 00:00:00 |
| payorderno | string | 可选 | 支付单号（跨境场景必填，申报海关用的支付流水号） | 60551570518 |
| innerTransactionId | string | 可选 | 支付申报单号 | 60551570518 |
| country | string | 可选 | 国家二位简码或国家名称 | CN |
| province | string | 必填 | 州/省 | 浙江省 |
| city | string | 必填 | 城市 | 杭州市 |
| area | string | 必填 | 区县 | 西湖区 |
| town | string | 可选 | 镇/街道 | 三墩镇 |
| address | string | 必填 | 地址 | 尚坤生态创业园A211 |
| zip | string | 可选 | 邮编 | 310000 |
| phone | string | 必填 | 电话（电话、手机必填一个） | 0571-89845712 |
| mobile | string | 必填 | 手机（电话、手机必填一个） | 15067888888 |
| email | string | 可选 | Email | [<EMAIL>](mailto:<EMAIL>) |
| customerremark | string | 可选 | 买家备注 | 包装好 |
| sellerremark | string | 可选 | 卖家备注 | 我会的 |
| postfee | decimal | 可选 | 邮资 | 0 |
| goodsfee | decimal | 必填 | 货款金额 | 500 |
| totalmoney | decimal | 必填 | 合计应收（针对卖家） | 500 |
| realpaymoney | decimal | 可选 | 实际支付金额（用户支付金额，已减去优惠金额，开发票给用户时可用此金额） |  |
| favourablemoney | decimal | 必填 | 订单优惠金额（针对整个订单的优惠） | 100 |
| platdiscountmoney | decimal | 可选 | 平台优惠金额（由平台承担，优惠金额平台会返给商家，开发票给平台时可使用此金额） |  |
| commissionvalue | decimal | 可选 | 佣金 | 10 |
| taxamount | decimal | 可选 | 订单税费总额 | 77.21 |
| tariffamount | decimal | 可选 | 订单关税金额 | 77.21 |
| addedvalueamount | decimal | 可选 | 订单增值税金额 | 77.21 |
| consumptiondutyamount | decimal | 可选 | 订单消费税金额 | 77.21 |
| sendstyle | string | 可选 | 货运方式（物流方式匹配 见常见问题解析七）(适用于网店管家） | 申通快递、申通快递带纸盒、顺丰普惠、顺丰普惠带纸盒 |
| sendtype | string | 可选 | 配送方式 JH_ExpressSend=快递，JH_FetchSend=到店自提，JH_LocalSend=同城配送，JH_PlatSend=平台配送，JH_BusinessMen=商家配送 | JH_ExpressSend |
| qq | string | 可选 | QQ | ********* |
| paytime | datetime | 可选 | 支付时间(格式:yyyy-MM-dd HH:mm:ss) | 1900-01-01 00:00:00 |
| invoicetitle | string | 可选 | 发票抬头 | 杭州笛佛软件有限公司 |
| taxpayerident | string | 可选 | 纳税人识别号 | 9133047250399591T |
| invoicetype | string | 可选 | 发票类型（JH_NONE ：不开票，JH_ 01：纸质发票，JH_ 02：电子发票 ，JH_ 03：纸质专票 JH_ 04：电子专票） | JH_ 03 |
| invoicecontent | string | 可选 | 发票内容 | 办公用品 |
| registeredaddress | string | 可选 | 公司注册地址 | 杭州西湖区西园路 |
| registeredphone | string | 可选 | 公司注册电话 | 0571-******** |
| depositbank | string | 可选 | 开户行 | 中国建设银行宁波支行 |
| bankaccount | string | 可选 | 开户行账号 | **************** |
| codservicefee | decimal | 可选 | COD服务费 | 0 |
| currencycode | string | 可选 | 货币类型 | USD |
| cardtype | string | 可选 | 证件类型(身份证=JH_01，护照=JH_02，港澳通行证=JH_03) 跨境场景必填 | JH_01 |
| idcard | string | 可选 | 证件号码（跨境场景必填） | 312055199001014872 |
| idcardtruename | string | 可选 | 证件真实姓名（跨境场景必填） | 张三 |
| receivername | string | 必填 | 收货人姓名 | 张三 |
| nick | string | 必填 | 买家昵称（一般为买家网名） | J1274551574 |
| whsecode | string | 可选 | 仓库编码（商品所在ERP系统中的仓库编码） | KU002 |
| IsHwgFlag | int | 可选 | 是否为海外购(是=1；否=0)在客户端该字段没有体现，暂时无法区分哪些是海外购 | 1 |
| isPreSaleOrder | int | 可选 | 是否为预售单（是=1；否=0）（用于吉客云） | 1 |
| preSaleOrder |  | 可选 | 预售单状态 定金未付,尾款未付=JH_NODEPOSIT_NOBALANCE,定金已付,尾款未付=JH_DEPOSIT_NOBALANCE,定金,尾款都付=JH_DEPOSIT_BALANCE,未知状态=JH_UNKNOW | JH_DEPOSIT_NOBALANCE |
| firstPayment | string | 可选 | 预售金额 |  |
| paytype | string | 必填 | 付款方式 支付方式默认JH_Other:其他=JH_Other,支付宝=JH_Alipay,蚂蚁花呗=JH_AliMayi,微信支付=JH_WeiXin,银联=JH_UnionPay,财付通=JH_Tenpay,百度钱包=JH_BaiDu,货到付款=JH_COD,邮局汇款=jh_bypostoffice,公司转账=jh_bycompany,银行转账=JH_ByBank,在线支付=JH_Online,担保交易=JH_SecTrans,京东在线=JH_JDOnline,现金=JH_Cash,PayPal=JH_PayPal,积分=JH_Point,预存款支付=JH_Predeposit,抵扣券=JH_DiscountCoupon,购物卡=JH_ShoppingCard,免费=JH_Free,健康金=JH_Health,便利店支付=JH_Store,代金券/卡=JH_Vouchers,代付=JH_PeerPay,杉德支付=JH_SANDPAY,微信⼩程序⽀付=JH_WXApp,微信公众号⽀付=JH_WXWeb,微信收付通⽀付=JH_WXSFT,建行融易付=JH_CCBRYF,微信h5⽀付=JH_WXH5,⽀付宝h5⽀付=JH_AliPayWeb,佣金=JH_Commission,重庆易极付=JH_YIJI,商盟=JH_SHANGMENG,汇付=JH_HUIFU,通联=JH_TONGLIAN,联动支付=JH_LIANDONGZHIFU,云闪付=JH_YUNSHANFU,易付宝=JH_YIFUBAO,盛付通=JH_SHENGFUTONG,建设银行=JH_CCB,中国银行=JH_BOC,农业银行=JH_ABC,甬易支付=JH_YONGYI,富友支付=JH_FUYOU,连连支付=JH_LIANLIAN,快钱=JH_KUAIQIAN,网易宝=JH_WANGYI,银盈通支付=JH_YINYINGTONG,鄞州银行=JH_YINZHOU,智惠支付=JH_ZHIHUI,拉卡拉=JH_LAKALA,北京银联=JH_BEIJING,杭州银行（网银）=JH_HANGZHOU,易宝支付=JH_YIBAO,广州银联=JH_GUANGZHOU,上海银联=JH_SHANGHAI,首信易支付=JH_SHOUXINYI,浙江银商=JH_ZHEJIANGYINSHANG,易票联支付=JH_YIPIAOLIAN,浙江农信=JH_ZHEJIANGNONGXIN,招商银行=JH_CMB,平安付=JH_PINGANFU,易联支付=JH_YILIAN,四川商通=JH_SHANGTONG,高汇通=JH_GAOHUITONG,开联通=JH_KAILIANTONG,钱宝科技=JH_QIANBAO,云商优付=JH_YUNSHANG,智付=JH_ZHIFU,爱农=JH_AINONG,翼支付=JH_YIZHIFU,现代金控=JH_JINKONG,宝付=JH_BAOFU,交通银行宁波分行=JH_BCM,汇元银通=JH_YINTONG,唯品会支付=JH_WEIPINHUI,工商银行=JH_ICBC,易宝支付=JH_YIBAOZHIFU,高汇通支付=JH_GAOHUITONGZHIFU,汇聚支付=JH_HUIJU,合利宝支付=JH_HELIBAO,线下支付=JH_XIANXIA,有赞支付=JH_YOUZAN,国际支付宝=JH_GUOJIALIPAY,多多支付=JH_DUODUOPAY,银盛支付=JH_YINSHENG,摩宝支付=JH_MOBAO,新生支付=JH_NEWPAY,美团支付=JH_MEITUAN | JH_WeiXin |
| Shouldpaytype | string | 必填 | 结算方式【可选值仅有：担保交易(例：支付宝担保交易，买家收货后支付宝打款给卖家)；银行收款；现金收款(现钞、微信支付宝转账)；货到付款；欠款计应收 ；客户预存款（充值余额）；多种结算（例：余额现金混合支付）】 | 担保交易 （吉客云优先payType） |
| payID | string | 可选 | 支付公司海关备案号(仅适用于吉客云跨境模块) | 44219809TC |
| payName | string | 可选 | 支付公司海关备案名称(仅适用于跨境场景) | 杭州巨无霸支付有限公司 |
| businessPlatCode | string | 可选 | 电商平台海关备案编码(仅适用于跨境场景) | 44304641HD |
| businessPlatName | string | 可选 | 电商平台海关备案名称(仅适用于跨境场景 ) | 杭州巨无霸有限公司 |
| ordertype | string | 可选 | 平台订单类型（JH_01=普通订单，JH_02=预售订单（发货日期有效才生效)， JH_WHOLESALE=批发订单（B2B) ） | JH_WHOLESALE |
| LeftsendDate | string | 可选 | 发货日期（预计发货时间） | 2020-10-10 10:10:10 （对应承诺发货时间） |
| logisticno | string | 可选 | 物流单号（适用于商城已经预约好快递单号的场景） | 130182172111 |
| logisticname | string | 可选 | 物流公司名称 | 圆通快递 |
| IsStoreOrder | bool | 可选 | 是否外部仓发货（适用于商城使用外部仓已经发货的订单进行标记） | true |
| Customization | string | 可选 | 定制信息 | xxxx |
| platParentOrderNo | string | 可选 | 父订单号（应用场景：自建商城作为复合型平台的时候用的，比如用自建商城，获取淘宝或者抖音的订单） | TB123458141456 |
| encryptPlatform | string | 可选 | 菠萝派平台枚举 （即父订单平台来源 ） | 淘宝=1,阿里健康=1086 |
| oaid | string | 可选 | 淘宝oaid | qweasdzxc |
| customAttr | string | 可选 | 销售单自定义字段 | 示例：customAttr {  customizeTradeColumn1:""  customizeTradeColumn2:""  .........  customizeTradeColumn30:""  } json字符串里面的字段名必须是customizeTradeColumn1 ~ customizeTradeColumn30 |
| clerkName | string | 可选 | 业务员 | 张三，李四 |
| **goodinfos** | **GoodInfo[]** | **必填** | **商品信息 集合** |  |
| ProductId | string | 必填 | 平台商品ID或SKUID(SKUID优先)（此为平台自动生成的编码或者序号） | 1231（不能为0） |
| suborderno | string | 必填 | 子订单号（若不填，ERP里用户拆单后会无法发货,对应的是subplatorderno）（拆单逻辑是按照商品来拆分，有几种商品就有几个子订单号。子订单号可以填写货品编码或者sku的编码只要保证同一主订单号下不重复即可） | 2017shoe43 |
| tradegoodsno | string | 必填 | 货品编码或SKU编码(SKU编码优先)。（一般单规格商品返回货品编码，多规格商品返回能对应到该商品某一子规格的子规格编码。）（用于网店管家对接吉客云可以不传） | 2017shoe43 |
| platgoodsid | string | 必填 | 平台商品ID（用于吉客云对接） | 确保唯一 |
| platskuid | string | 必填 | 平台规格ID（用于吉客云对接） | 确保唯一 |
| OutItemID | string | 必填 | 外部商家编码（用于吉客云对接） | 商家在电商平台手动维护的货品编码 |
| OutSkuID | String | 必填 | 外部规格编码（用于吉客云对接） | 商家在电商平台手动维护的货品规格编码 |
| tradegoodsname | string | 必填 | 交易商品名称 | 新款连衣裙 |
| tradegoodsspec | string | 必填 | 交易商品规格 | 颜色：白，尺寸：XL |
| goodscount | int | 必填 | 商品数量 | 1 |
| price | decimal | 必填 | 单价 | 500 |
| isgift | int | 可选 | 是否是赠品 | 1是，0不是 |  |
| discountmoney | decimal | 可选 | 子订单优惠金额（按照商品级别拆单） | 订单中某商品单价50数量6，优惠20，该子订单金额合计为50*6-20=280 |
| taxamount | decimal | 可选 | 子订单商品税费 | 12.21 |
| Customization | string | 可选 | 定制信息 | XXXXX |  |
| refundStatus | string | 可选 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，其他=JH_99) | JH_01 |
| Status | string | 可选 | 子订单交易状态(其他=JH_99，等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05) | JH_01 |
| remark | string | 可选 | 货品备注 | 记得检查 |
  4. 响应示例```
{  
     "code":"10000",  
     "message":"SUCCESS",  
     "numtotalorder":51,  
     "orders":[  
        {  
            "PlatOrderNo":"20492364655",  
            "tradeStatus":"JH_02",  
             "tradeStatusdescription":"WAIT_SELLER_DELIVERY",  
             "tradetime":"2016-07-23T13:26:58",  
             "payorderno":"20496764655",  
             "country":"CN",  
             "province":"广东",  
             "city":"潮州市",  
             "area":"湘桥区",  
             "town":"",  
             "address":"广东潮州市湘桥区城区潮枫路米兰婚纱",  
             "zip":"",  
             "phone":"13553758444",  
             "mobile":"13553758444",  
             "email":"",  
             "customerremark":"",  
             "sellerremark":"已经告知没有货 等待买家退款 杜鹃 买家要求继续等",  
             "postfee":10,  
             "goodsfee":0,  
             "totalmoney":55,  
             "favourablemoney":0,  
             "commissionvalue":0,  
             "sendstyle":"4-在线支付",  
             "qq":"",  
             "paytime":"2016-07-23T13:35:14",  
             "invoicetitle":"1",  
             "codservicefee":0,  
             "couponprice":0,  
             "cardtype":"JH_02",  
             "idcard":"",  
              customAttr {
                       customizeTradeColumn1：""
                       customizeTradeColumn2：""
  
                }
             "goodinfos":[  
                {  
                    "ProductId":"1964645294",  
                    "suborderno":"1954687529",  
                     "tradegoodsno":"1954687529",  
                    "tradegoodsname":"【满299减30】Innisfree绿茶去角质啫喱150ml",  
                    "tradegoodsspec":"",  
                    "goodscount":1,  
                    "price":55,  
                    "discountmoney":0,  
                     "refundStatus":"JH_07",  
                    "Status":"JH_02",  
                     "remark":""  
                 },  
                {  
                     "ProductId":"1964645295",  
                     "suborderno":"1954687530",  
                     "tradegoodsno":"1954687530",  
                     "tradegoodsname":"【满299减30】Innisfree绿茶去角质啫喱150ml",  
                     "tradegoodsspec":"",  
                     "goodscount":1,  
                     "price":55,  
                     "discountmoney":0,  
                     "refundStatus":"JH_07",  
                     "Status":"JH_02",  
                     "remark":""  
                }  
            ]  
         }  
    ]  
 }
```
2. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| OrderStatus | string | 条件 | 订单交易状态(等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05，其他=JH_98（根据订单号查详情时），所有订单=JH_99)（吉客云默认下载JH_01,JH_02,JH_05三种状态的订单, 网店管家默认下载JH_02 状态下订单但ERP只会处理JH_02待发货的订单）如果想要下载JH_04已完成订单需要在网店配置中进行配置 | JH_02 |
| PlatOrderNo | string | 可选 | 平台订单号，若不为空，则代表查询单个订单的数据，查询单个订单时，可不传时间、状态等 | NO2545661 |
| StartTime | datetime | 条件 | 开始时间(格式:yyyy-MM-dd HH:mm:ss) | 2016-06-15 12:23:32 |
| EndTime | datetime | 条件 | 截止时间(格式:yyyy-MM-dd HH:mm:ss) | 2016-07-15 08:32:00 |
| TimeType | string | 可选 | 订单时间类别(订单修改时间=JH_01，订单创建时间=JH_02) (ERP抓单默认JH_02) | JH_02 |
| PageIndex | int | 必填 | 页码 | 1 |
| PageSize | int | 必填 | 每页条数 | 30（默认） |
3. 请求示例```
{  
     "OrderStatus":"JH_02",  
     "PlatOrderNo":"",  
     "StartTime":"2016-07-26 10:59:10",  
     "EndTime":"2016-08-02 10:59:10",  
     "TimeType":"JH_02",  
     "PageIndex":"1",  
     "PageSize":"20"  
 }
```
4. **响应参数**| numtotalorder | int | 必填 | 订单总数量(这里返回的是符合条件的所有订单的总数，而不是当页订单的数量，例如总数为51，请求参数中每页为30，则应返回51)。第一页会去取两次，第一次去是为了获取订单总数，第二次去取才会按照正常分页走。 | 51 |
| **orders** | **OrderItem[]** | **必填** | **订单 集合********若没有订单也必须返回空数组** | **-** |
| PlatOrderNo | string | 必填 | 订单号 | NO2545661 |
| tradeStatus | string | 必填 | 订单交易状态(等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05，卖家部分发货=JH_08)（返参中的订单状态需要跟请求参数中的订单状态保持一致，否则会被过滤）（目前会抓取JH_01,JH_02,JH_04三种状态的订单, 但ERP只会处理JH_02待发货的订单） | JH_02 |
| tradeStatusdescription | string | 可选 | 订单交易说明 | 等待卖家发货 |
| tradetime | datetime | 必填 | 交易时间(格式:yyyy-MM-dd HH:mm:ss) 起始时间格式不可以为0000-00-00 | 1900-01-01 00:00:00 |
| payorderno | string | 可选 | 支付单号（跨境场景必填，申报海关用的支付流水号） | 60551570518 |
| innerTransactionId | string | 可选 | 支付申报单号 | 60551570518 |
| country | string | 可选 | 国家二位简码或国家名称 | CN |
| province | string | 必填 | 州/省 | 浙江省 |
| city | string | 必填 | 城市 | 杭州市 |
| area | string | 必填 | 区县 | 西湖区 |
| town | string | 可选 | 镇/街道 | 三墩镇 |
| address | string | 必填 | 地址 | 尚坤生态创业园A211 |
| zip | string | 可选 | 邮编 | 310000 |
| phone | string | 必填 | 电话（电话、手机必填一个） | 0571-89845712 |
| mobile | string | 必填 | 手机（电话、手机必填一个） | 15067888888 |
| email | string | 可选 | Email | [<EMAIL>](mailto:<EMAIL>) |
| customerremark | string | 可选 | 买家备注 | 包装好 |
| sellerremark | string | 可选 | 卖家备注 | 我会的 |
| postfee | decimal | 可选 | 邮资 | 0 |
| goodsfee | decimal | 必填 | 货款金额 | 500 |
| totalmoney | decimal | 必填 | 合计应收（针对卖家） | 500 |
| realpaymoney | decimal | 可选 | 实际支付金额（用户支付金额，已减去优惠金额，开发票给用户时可用此金额） |  |
| favourablemoney | decimal | 必填 | 订单优惠金额（针对整个订单的优惠） | 100 |
| platdiscountmoney | decimal | 可选 | 平台优惠金额（由平台承担，优惠金额平台会返给商家，开发票给平台时可使用此金额） |  |
| commissionvalue | decimal | 可选 | 佣金 | 10 |
| taxamount | decimal | 可选 | 订单税费总额 | 77.21 |
| tariffamount | decimal | 可选 | 订单关税金额 | 77.21 |
| addedvalueamount | decimal | 可选 | 订单增值税金额 | 77.21 |
| consumptiondutyamount | decimal | 可选 | 订单消费税金额 | 77.21 |
| sendstyle | string | 可选 | 货运方式（物流方式匹配 见常见问题解析七）(适用于网店管家） | 申通快递、申通快递带纸盒、顺丰普惠、顺丰普惠带纸盒 |
| sendtype | string | 可选 | 配送方式 JH_ExpressSend=快递，JH_FetchSend=到店自提，JH_LocalSend=同城配送，JH_PlatSend=平台配送，JH_BusinessMen=商家配送 | JH_ExpressSend |
| qq | string | 可选 | QQ | ********* |
| paytime | datetime | 可选 | 支付时间(格式:yyyy-MM-dd HH:mm:ss) | 1900-01-01 00:00:00 |
| invoicetitle | string | 可选 | 发票抬头 | 杭州笛佛软件有限公司 |
| taxpayerident | string | 可选 | 纳税人识别号 | 9133047250399591T |
| invoicetype | string | 可选 | 发票类型（JH_NONE ：不开票，JH_ 01：纸质发票，JH_ 02：电子发票 ，JH_ 03：纸质专票 JH_ 04：电子专票） | JH_ 03 |
| invoicecontent | string | 可选 | 发票内容 | 办公用品 |
| registeredaddress | string | 可选 | 公司注册地址 | 杭州西湖区西园路 |
| registeredphone | string | 可选 | 公司注册电话 | 0571-******** |
| depositbank | string | 可选 | 开户行 | 中国建设银行宁波支行 |
| bankaccount | string | 可选 | 开户行账号 | **************** |
| codservicefee | decimal | 可选 | COD服务费 | 0 |
| currencycode | string | 可选 | 货币类型 | USD |
| cardtype | string | 可选 | 证件类型(身份证=JH_01，护照=JH_02，港澳通行证=JH_03) 跨境场景必填 | JH_01 |
| idcard | string | 可选 | 证件号码（跨境场景必填） | 312055199001014872 |
| idcardtruename | string | 可选 | 证件真实姓名（跨境场景必填） | 张三 |
| receivername | string | 必填 | 收货人姓名 | 张三 |
| nick | string | 必填 | 买家昵称（一般为买家网名） | J1274551574 |
| whsecode | string | 可选 | 仓库编码（商品所在ERP系统中的仓库编码） | KU002 |
| IsHwgFlag | int | 可选 | 是否为海外购(是=1；否=0)在客户端该字段没有体现，暂时无法区分哪些是海外购 | 1 |
| isPreSaleOrder | int | 可选 | 是否为预售单（是=1；否=0）（用于吉客云） | 1 |
| preSaleOrder |  | 可选 | 预售单状态 定金未付,尾款未付=JH_NODEPOSIT_NOBALANCE,定金已付,尾款未付=JH_DEPOSIT_NOBALANCE,定金,尾款都付=JH_DEPOSIT_BALANCE,未知状态=JH_UNKNOW | JH_DEPOSIT_NOBALANCE |
| firstPayment | string | 可选 | 预售金额 |  |
| paytype | string | 必填 | 付款方式 支付方式默认JH_Other:其他=JH_Other,支付宝=JH_Alipay,蚂蚁花呗=JH_AliMayi,微信支付=JH_WeiXin,银联=JH_UnionPay,财付通=JH_Tenpay,百度钱包=JH_BaiDu,货到付款=JH_COD,邮局汇款=jh_bypostoffice,公司转账=jh_bycompany,银行转账=JH_ByBank,在线支付=JH_Online,担保交易=JH_SecTrans,京东在线=JH_JDOnline,现金=JH_Cash,PayPal=JH_PayPal,积分=JH_Point,预存款支付=JH_Predeposit,抵扣券=JH_DiscountCoupon,购物卡=JH_ShoppingCard,免费=JH_Free,健康金=JH_Health,便利店支付=JH_Store,代金券/卡=JH_Vouchers,代付=JH_PeerPay,杉德支付=JH_SANDPAY,微信⼩程序⽀付=JH_WXApp,微信公众号⽀付=JH_WXWeb,微信收付通⽀付=JH_WXSFT,建行融易付=JH_CCBRYF,微信h5⽀付=JH_WXH5,⽀付宝h5⽀付=JH_AliPayWeb,佣金=JH_Commission,重庆易极付=JH_YIJI,商盟=JH_SHANGMENG,汇付=JH_HUIFU,通联=JH_TONGLIAN,联动支付=JH_LIANDONGZHIFU,云闪付=JH_YUNSHANFU,易付宝=JH_YIFUBAO,盛付通=JH_SHENGFUTONG,建设银行=JH_CCB,中国银行=JH_BOC,农业银行=JH_ABC,甬易支付=JH_YONGYI,富友支付=JH_FUYOU,连连支付=JH_LIANLIAN,快钱=JH_KUAIQIAN,网易宝=JH_WANGYI,银盈通支付=JH_YINYINGTONG,鄞州银行=JH_YINZHOU,智惠支付=JH_ZHIHUI,拉卡拉=JH_LAKALA,北京银联=JH_BEIJING,杭州银行（网银）=JH_HANGZHOU,易宝支付=JH_YIBAO,广州银联=JH_GUANGZHOU,上海银联=JH_SHANGHAI,首信易支付=JH_SHOUXINYI,浙江银商=JH_ZHEJIANGYINSHANG,易票联支付=JH_YIPIAOLIAN,浙江农信=JH_ZHEJIANGNONGXIN,招商银行=JH_CMB,平安付=JH_PINGANFU,易联支付=JH_YILIAN,四川商通=JH_SHANGTONG,高汇通=JH_GAOHUITONG,开联通=JH_KAILIANTONG,钱宝科技=JH_QIANBAO,云商优付=JH_YUNSHANG,智付=JH_ZHIFU,爱农=JH_AINONG,翼支付=JH_YIZHIFU,现代金控=JH_JINKONG,宝付=JH_BAOFU,交通银行宁波分行=JH_BCM,汇元银通=JH_YINTONG,唯品会支付=JH_WEIPINHUI,工商银行=JH_ICBC,易宝支付=JH_YIBAOZHIFU,高汇通支付=JH_GAOHUITONGZHIFU,汇聚支付=JH_HUIJU,合利宝支付=JH_HELIBAO,线下支付=JH_XIANXIA,有赞支付=JH_YOUZAN,国际支付宝=JH_GUOJIALIPAY,多多支付=JH_DUODUOPAY,银盛支付=JH_YINSHENG,摩宝支付=JH_MOBAO,新生支付=JH_NEWPAY,美团支付=JH_MEITUAN | JH_WeiXin |
| Shouldpaytype | string | 必填 | 结算方式【可选值仅有：担保交易(例：支付宝担保交易，买家收货后支付宝打款给卖家)；银行收款；现金收款(现钞、微信支付宝转账)；货到付款；欠款计应收 ；客户预存款（充值余额）；多种结算（例：余额现金混合支付）】 | 担保交易 （吉客云优先payType） |
| payID | string | 可选 | 支付公司海关备案号(仅适用于吉客云跨境模块) | 44219809TC |
| payName | string | 可选 | 支付公司海关备案名称(仅适用于跨境场景) | 杭州巨无霸支付有限公司 |
| businessPlatCode | string | 可选 | 电商平台海关备案编码(仅适用于跨境场景) | 44304641HD |
| businessPlatName | string | 可选 | 电商平台海关备案名称(仅适用于跨境场景 ) | 杭州巨无霸有限公司 |
| ordertype | string | 可选 | 平台订单类型（JH_01=普通订单，JH_02=预售订单（发货日期有效才生效)， JH_WHOLESALE=批发订单（B2B) ） | JH_WHOLESALE |
| LeftsendDate | string | 可选 | 发货日期（预计发货时间） | 2020-10-10 10:10:10 （对应承诺发货时间） |
| logisticno | string | 可选 | 物流单号（适用于商城已经预约好快递单号的场景） | 130182172111 |
| logisticname | string | 可选 | 物流公司名称 | 圆通快递 |
| IsStoreOrder | bool | 可选 | 是否外部仓发货（适用于商城使用外部仓已经发货的订单进行标记） | true |
| Customization | string | 可选 | 定制信息 | xxxx |
| platParentOrderNo | string | 可选 | 父订单号（应用场景：自建商城作为复合型平台的时候用的，比如用自建商城，获取淘宝或者抖音的订单） | TB123458141456 |
| encryptPlatform | string | 可选 | 菠萝派平台枚举 （即父订单平台来源 ） | 淘宝=1,阿里健康=1086 |
| oaid | string | 可选 | 淘宝oaid | qweasdzxc |
| customAttr | string | 可选 | 销售单自定义字段 | 示例：customAttr {  customizeTradeColumn1:""  customizeTradeColumn2:""  .........  customizeTradeColumn30:""  } json字符串里面的字段名必须是customizeTradeColumn1 ~ customizeTradeColumn30 |
| clerkName | string | 可选 | 业务员 | 张三，李四 |
| **goodinfos** | **GoodInfo[]** | **必填** | **商品信息 集合** |  |
| ProductId | string | 必填 | 平台商品ID或SKUID(SKUID优先)（此为平台自动生成的编码或者序号） | 1231（不能为0） |
| suborderno | string | 必填 | 子订单号（若不填，ERP里用户拆单后会无法发货,对应的是subplatorderno）（拆单逻辑是按照商品来拆分，有几种商品就有几个子订单号。子订单号可以填写货品编码或者sku的编码只要保证同一主订单号下不重复即可） | 2017shoe43 |
| tradegoodsno | string | 必填 | 货品编码或SKU编码(SKU编码优先)。（一般单规格商品返回货品编码，多规格商品返回能对应到该商品某一子规格的子规格编码。）（用于网店管家对接吉客云可以不传） | 2017shoe43 |
| platgoodsid | string | 必填 | 平台商品ID（用于吉客云对接） | 确保唯一 |
| platskuid | string | 必填 | 平台规格ID（用于吉客云对接） | 确保唯一 |
| OutItemID | string | 必填 | 外部商家编码（用于吉客云对接） | 商家在电商平台手动维护的货品编码 |
| OutSkuID | String | 必填 | 外部规格编码（用于吉客云对接） | 商家在电商平台手动维护的货品规格编码 |
| tradegoodsname | string | 必填 | 交易商品名称 | 新款连衣裙 |
| tradegoodsspec | string | 必填 | 交易商品规格 | 颜色：白，尺寸：XL |
| goodscount | int | 必填 | 商品数量 | 1 |
| price | decimal | 必填 | 单价 | 500 |
| isgift | int | 可选 | 是否是赠品 | 1是，0不是 |  |
| discountmoney | decimal | 可选 | 子订单优惠金额（按照商品级别拆单） | 订单中某商品单价50数量6，优惠20，该子订单金额合计为50*6-20=280 |
| taxamount | decimal | 可选 | 子订单商品税费 | 12.21 |
| Customization | string | 可选 | 定制信息 | XXXXX |  |
| refundStatus | string | 可选 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，其他=JH_99) | JH_01 |
| Status | string | 可选 | 子订单交易状态(其他=JH_99，等待买家付款=JH_01，等待卖家发货=JH_02，等待买家确认收货=JH_03，交易成功=JH_04，交易关闭=JH_05) | JH_01 |
| remark | string | 可选 | 货品备注 | 记得检查 |
5. 响应示例```
{  
     "code":"10000",  
     "message":"SUCCESS",  
     "numtotalorder":51,  
     "orders":[  
        {  
            "PlatOrderNo":"20492364655",  
            "tradeStatus":"JH_02",  
             "tradeStatusdescription":"WAIT_SELLER_DELIVERY",  
             "tradetime":"2016-07-23T13:26:58",  
             "payorderno":"20496764655",  
             "country":"CN",  
             "province":"广东",  
             "city":"潮州市",  
             "area":"湘桥区",  
             "town":"",  
             "address":"广东潮州市湘桥区城区潮枫路米兰婚纱",  
             "zip":"",  
             "phone":"13553758444",  
             "mobile":"13553758444",  
             "email":"",  
             "customerremark":"",  
             "sellerremark":"已经告知没有货 等待买家退款 杜鹃 买家要求继续等",  
             "postfee":10,  
             "goodsfee":0,  
             "totalmoney":55,  
             "favourablemoney":0,  
             "commissionvalue":0,  
             "sendstyle":"4-在线支付",  
             "qq":"",  
             "paytime":"2016-07-23T13:35:14",  
             "invoicetitle":"1",  
             "codservicefee":0,  
             "couponprice":0,  
             "cardtype":"JH_02",  
             "idcard":"",  
              customAttr {
                       customizeTradeColumn1：""
                       customizeTradeColumn2：""
  
                }
             "goodinfos":[  
                {  
                    "ProductId":"1964645294",  
                    "suborderno":"1954687529",  
                     "tradegoodsno":"1954687529",  
                    "tradegoodsname":"【满299减30】Innisfree绿茶去角质啫喱150ml",  
                    "tradegoodsspec":"",  
                    "goodscount":1,  
                    "price":55,  
                    "discountmoney":0,  
                     "refundStatus":"JH_07",  
                    "Status":"JH_02",  
                     "remark":""  
                 },  
                {  
                     "ProductId":"1964645295",  
                     "suborderno":"1954687530",  
                     "tradegoodsno":"1954687530",  
                     "tradegoodsname":"【满299减30】Innisfree绿茶去角质啫喱150ml",  
                     "tradegoodsspec":"",  
                     "goodscount":1,  
                     "price":55,  
                     "discountmoney":0,  
                     "refundStatus":"JH_07",  
                     "Status":"JH_02",  
                     "remark":""  
                }  
            ]  
         }  
    ]  
 }
```
6. **API-退款检测接口**
  1. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| OrderID | string | 必填 | 平台订单号 | NO2545661 |
  2. 请求示例```
{  
    "OrderID":"316346734396336091"  
 }
```
  3. **响应参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| refundStatus | string | 必填 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，JH_09=部分退款，其他=JH_99) | JH_01 |
| refundStatusdescription | string | 可选 | 退款状态说明 | 退款中 |
| **childrenrefundStatus** | **ChildrenRefundStatusItem[]** | **可选** | **子订单****退款状态 集合** | **-** |
| Suborderno | String | 可选 | 子订单号 **（对应订单下载接口 ：suborderno ）**（拆单逻辑是按照商品来拆分，有几种商品就有几个子订单号，子订单号可以填写货品编码或者sku的编码只要保证同一主订单号下不重复即可） |  |
| refundno | string | 可选 | 退款订单号 | RO2545661 |
| ProductName | string | 可选 | 商品名称 | 鞋子 |
| refundStatus | string | 可选 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，JH_09=部分退款，其他=JH_99) | JH_01 |
| refundStatusdescription | string | 可选 | 退款状态说明 | 退款中 |
  4. 响应示例
    1. 有退款的情况```
{  
     " refundStatus":"JH_01",  
     " refundStatusdescription":"买家已经申请退款等待卖家同意",  
     "childrenrefundStatus":[  
         {  
             "refundno":"5890793",  
             "ProductName":"迪迪龙垃圾桶家用有压圈厨房卫生间创意时尚塑料大号垃圾桶卫生桶",  
             "refundStatus":99,  
             "refundStatusdescription":"316346734396336091"  
        }  
     ],  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"退款成功"  
 }
```
    2. 没有退款的情况```
{  
     "refundStatus":"JH_07",  
     "refundStatusdescription":"没有退款",  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"没有退款"  
 }
```
  5. 有退款的情况```
{  
     " refundStatus":"JH_01",  
     " refundStatusdescription":"买家已经申请退款等待卖家同意",  
     "childrenrefundStatus":[  
         {  
             "refundno":"5890793",  
             "ProductName":"迪迪龙垃圾桶家用有压圈厨房卫生间创意时尚塑料大号垃圾桶卫生桶",  
             "refundStatus":99,  
             "refundStatusdescription":"316346734396336091"  
        }  
     ],  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"退款成功"  
 }
```
  6. 没有退款的情况```
{  
     "refundStatus":"JH_07",  
     "refundStatusdescription":"没有退款",  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"没有退款"  
 }
```
  1. 有退款的情况```
{  
     " refundStatus":"JH_01",  
     " refundStatusdescription":"买家已经申请退款等待卖家同意",  
     "childrenrefundStatus":[  
         {  
             "refundno":"5890793",  
             "ProductName":"迪迪龙垃圾桶家用有压圈厨房卫生间创意时尚塑料大号垃圾桶卫生桶",  
             "refundStatus":99,  
             "refundStatusdescription":"316346734396336091"  
        }  
     ],  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"退款成功"  
 }
```
  2. 没有退款的情况```
{  
     "refundStatus":"JH_07",  
     "refundStatusdescription":"没有退款",  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"没有退款"  
 }
```
7. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| OrderID | string | 必填 | 平台订单号 | NO2545661 |
8. 请求示例```
{  
    "OrderID":"316346734396336091"  
 }
```
9. **响应参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| refundStatus | string | 必填 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，JH_09=部分退款，其他=JH_99) | JH_01 |
| refundStatusdescription | string | 可选 | 退款状态说明 | 退款中 |
| **childrenrefundStatus** | **ChildrenRefundStatusItem[]** | **可选** | **子订单****退款状态 集合** | **-** |
| Suborderno | String | 可选 | 子订单号 **（对应订单下载接口 ：suborderno ）**（拆单逻辑是按照商品来拆分，有几种商品就有几个子订单号，子订单号可以填写货品编码或者sku的编码只要保证同一主订单号下不重复即可） |  |
| refundno | string | 可选 | 退款订单号 | RO2545661 |
| ProductName | string | 可选 | 商品名称 | 鞋子 |
| refundStatus | string | 可选 | 退款状态(没有退款=JH_07，买家已经申请退款等待卖家同意=JH_01，卖家已经同意退款等待买家退货=JH_02，买家已经退货等待卖家确认收货=JH_03，卖家拒绝退款=JH_04，退款关闭=JH_05，退款成功=JH_06，JH_09=部分退款，其他=JH_99) | JH_01 |
| refundStatusdescription | string | 可选 | 退款状态说明 | 退款中 |
10. 响应示例
  1. 有退款的情况```
{  
     " refundStatus":"JH_01",  
     " refundStatusdescription":"买家已经申请退款等待卖家同意",  
     "childrenrefundStatus":[  
         {  
             "refundno":"5890793",  
             "ProductName":"迪迪龙垃圾桶家用有压圈厨房卫生间创意时尚塑料大号垃圾桶卫生桶",  
             "refundStatus":99,  
             "refundStatusdescription":"316346734396336091"  
        }  
     ],  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"退款成功"  
 }
```
  2. 没有退款的情况```
{  
     "refundStatus":"JH_07",  
     "refundStatusdescription":"没有退款",  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"没有退款"  
 }
```
11. 有退款的情况```
{  
     " refundStatus":"JH_01",  
     " refundStatusdescription":"买家已经申请退款等待卖家同意",  
     "childrenrefundStatus":[  
         {  
             "refundno":"5890793",  
             "ProductName":"迪迪龙垃圾桶家用有压圈厨房卫生间创意时尚塑料大号垃圾桶卫生桶",  
             "refundStatus":99,  
             "refundStatusdescription":"316346734396336091"  
        }  
     ],  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"退款成功"  
 }
```
12. 没有退款的情况```
{  
     "refundStatus":"JH_07",  
     "refundStatusdescription":"没有退款",  
     "code":"10000",  
     "message":"SUCCESS",  
     "submessage":"没有退款"  
 }
```
13. **API-订单发货接口 吉客云--》心云**
  1. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| SendType | string | 可选 | 订单发货类别(自己联系物流=JH_01，在线下单=JH_02，无需物流=JH_03，自定义物流=JH_04，家装发货=JH_05，国际物流=JH_06,修改已发货订单物流信息=JH_08，换货单发货=JH-09 ，物流多包裹通知=JH_14（追加包裹时IsSplit=0）) | JH_01 |
| LogisticName | string | 必填 | 快递名称 | 申通快递 |
| LogisticType | string | 必填 | 快递类别(JH前缀为国内快递 ，JHI为国际快递)详见物流公司代码对照表 | LogisticType |
| LogisticNo | string | 必填 | 快递运单号**（****无需物流则返回空****）** | LogisticNo |
| PlatOrderNo | string | 必填 | 平台订单号 | NO2545661 |
| IsSplit | int | 必填 | 是否拆单发货(拆单=1 ，不拆单=0) | 0 |
| SubPlatOrderNo | string | 条件 | 平台子订单交易单号；支持订单拆分为不同商品不同数量发货,多个商品用"|"隔开, 为空则视为整单发货包含子订单编号和商品发货数量，格式suborderno1:count1|suborderno2:count2发货数量需为大于0的整数**（****无需物流场景、无拆单发货该字段返回空****）**suborderno：订单推送/下载中返回得子订单号count：发货数量 | 2017shoe43:2|2017shoe44:5 |
| SenderName | string | 可选 | 发货人姓名 | 张三 |
| SenderTel | string | 可选 | 发货人联系电话 | 15047788954 |
| SenderAddress | string | 可选 | 发货人地址(省市区之间以空格分隔) | 上海 上海市 宝山区 逸仙路2816号华滋奔腾大厦A栋14楼 |
| IsHwgFlag | int | 可选 | 是否为海外购(是=1；否=0) | 1 |
| Feature | string | 可选 | 商品识别码 | 序列号 |
| Goods | Goods[] | 可选 | 商品集合(只有需要回传批次号时，才会有值，和拆单发货无关） |  |
| PlatProductId | string | 可选 | 平台商品id | 99 |
| batchNum | string | 可选 | 批号 | agbiagharbg |
| validityTime | string | 可选 | 有效期 |  |
| Remark | stinrg | 可选 | 货品备注 | 222 |
| SubOrderNo | string | 可选 | 子订单号(对应抓到请求里面SubOrderNo) | oid |
| SkuId | string | 可选 | skuId | 31111 |
  2. 请求示例```
{  
    "SendType":"JH_01",  
    "LogisticName":"韵达速递",  
    "LogisticType":"JH_003",  
    "LogisticNo":"4304132500000",  
    "PlatOrderNo":"9QC20031511016",  
    "IsSplit":"1",  
    "SubPlatOrderNo":"2017shoe43:1.000|2017shoe44:2.000",  
    "SenderName":"小笛",  
    "SenderTel":"18818818181",  
    "SenderAddress":"杭州笛佛软件"  
 }
```
  3. **响应示例**```
{  
     "code":"10000",  
    "message":"Success",  
    "submessage":"发货成功"  
 }
```
14. **请求参数**| 参数 | 类型 | 是否必填 | 描述 | 示例值 |
| SendType | string | 可选 | 订单发货类别(自己联系物流=JH_01，在线下单=JH_02，无需物流=JH_03，自定义物流=JH_04，家装发货=JH_05，国际物流=JH_06,修改已发货订单物流信息=JH_08，换货单发货=JH-09 ，物流多包裹通知=JH_14（追加包裹时IsSplit=0）) | JH_01 |
| LogisticName | string | 必填 | 快递名称 | 申通快递 |
| LogisticType | string | 必填 | 快递类别(JH前缀为国内快递 ，JHI为国际快递)详见物流公司代码对照表 | LogisticType |
| LogisticNo | string | 必填 | 快递运单号**（****无需物流则返回空****）** | LogisticNo |
| PlatOrderNo | string | 必填 | 平台订单号 | NO2545661 |
| IsSplit | int | 必填 | 是否拆单发货(拆单=1 ，不拆单=0) | 0 |
| SubPlatOrderNo | string | 条件 | 平台子订单交易单号；支持订单拆分为不同商品不同数量发货,多个商品用"|"隔开, 为空则视为整单发货包含子订单编号和商品发货数量，格式suborderno1:count1|suborderno2:count2发货数量需为大于0的整数**（****无需物流场景、无拆单发货该字段返回空****）**suborderno：订单推送/下载中返回得子订单号count：发货数量 | 2017shoe43:2|2017shoe44:5 |
| SenderName | string | 可选 | 发货人姓名 | 张三 |
| SenderTel | string | 可选 | 发货人联系电话 | 15047788954 |
| SenderAddress | string | 可选 | 发货人地址(省市区之间以空格分隔) | 上海 上海市 宝山区 逸仙路2816号华滋奔腾大厦A栋14楼 |
| IsHwgFlag | int | 可选 | 是否为海外购(是=1；否=0) | 1 |
| Feature | string | 可选 | 商品识别码 | 序列号 |
| Goods | Goods[] | 可选 | 商品集合(只有需要回传批次号时，才会有值，和拆单发货无关） |  |
| PlatProductId | string | 可选 | 平台商品id | 99 |
| batchNum | string | 可选 | 批号 | agbiagharbg |
| validityTime | string | 可选 | 有效期 |  |
| Remark | stinrg | 可选 | 货品备注 | 222 |
| SubOrderNo | string | 可选 | 子订单号(对应抓到请求里面SubOrderNo) | oid |
| SkuId | string | 可选 | skuId | 31111 |
15. 请求示例```
{  
    "SendType":"JH_01",  
    "LogisticName":"韵达速递",  
    "LogisticType":"JH_003",  
    "LogisticNo":"4304132500000",  
    "PlatOrderNo":"9QC20031511016",  
    "IsSplit":"1",  
    "SubPlatOrderNo":"2017shoe43:1.000|2017shoe44:2.000",  
    "SenderName":"小笛",  
    "SenderTel":"18818818181",  
    "SenderAddress":"杭州笛佛软件"  
 }
```
16. **响应示例**```
{  
     "code":"10000",  
    "message":"Success",  
    "submessage":"发货成功"  
 }
```
17. **B2C系统订单查询接口**
  1. **接口地址:/b2c/1.0/order/detail/{omsOrderNo}**
  2. **返参示例**
    1. {
 "code": "10000",
 "msg": "操作成功",
 "data": {
 "orderNo": "1798267334755840773",
 "omsOrderNo": "1798267334861747205",
 "thirdPlatformCode": "43",
 "thirdPlatformName": null,
 "clientCode": "WSC1111",
 "onlineStoreCode": "WSC1111",
 "onlineStoreName": "云仓商城",
 "thirdOrderNo": "1798267334013984775",
 "thirdOrderState": "3",
 "buyerMessage": "",
 "sellerRemark": null,
 "remark": null,
 "buyerName": "— —",
 "prescriptionStatus": null,
 "clientConfId": 359074,
 "isPrescription": false,
 "payType": "1",
 "buyerActualAmount": 0,
 "created": "2024-05-06 10:09:30",
 "payTime": "2024-05-06 10:09:30",
 "receiverName": "李燕",
 "receiverMobile": "",
 "originalFullAddress": "",
 "province": "云南省",
 "city": "昆明市",
 "district": "官渡区",
 "town": null,
 "address": "",
 "needInvoice": false,
 "invoiceType": null,
 "invoiceName": null,
 "vatTaxpayerNumber": null,
 "refundCount": 0,
 "orderDetails": [
 {
 "id": 29190801,
 "platformSkuId": "109072000000544",
 "thirdDetailId": "1798267334755840773_1",
 "erpCode": "109072000000544",
 "barCode": null,
 "commodityName": "蓝漂蜜桃香型洗洁精2斤瓶装LP-366980",
 "commoditySpec": "瓶",
 "commodityCount": 4,
 "originalPrice": 25,
 "price": 25,
 "totalAmount": 100,
 "discountAmount": 0,
 "adjustAmount": 0,
 "discountShare": 0,
 "actualNetAmount": 100,
 "billPrice": 25,
 "platformDiscountFee": 0,
 "merchantDiscountFee": 0,
 "platformDiscount": null,
 "merchantDiscount": null,
 "brokerageAmount": 0,
 "isGift": 0,
 "erpGift": 1,
 "status": 0,
 "oldErpCode": null,
 "goodsType": 1,
 "drugType": 3,
 "outerId": null,
 "rebateratio": 0,
 "vipDifferentAmt": 0,
 "settlePrice": 0,
 "originalOmsOrderNo": null,
 "batchNos": null,
 "refundStatus": "正常",
 "averagePrice": 14.5,
 "detailSettlementStatus": 0
 }
 ],
 "orderPayInfo": {
 "payType": "1",
 "buyerActualAmount": 0,
 "merchantActualAmount": 100,
 "totalAmount": 100,
 "totalDiscount": 0,
 "platformDiscount": 0,
 "merchantDiscountSum": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "postFee": null,
 "buyerCodServiceFee": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "sellerCodServiceFee": 0,
 "brokerageAmount": 0,
 "buyerCodAmount": 0,
 "platformFeeCollection": 0,
 "manualFixAmount": 0,
 "adjustAmount": 0,
 "detailDiscountCollect": 0,
 "differentAmount": 0
 },
 "prescription": null,
 "orderType": 1,
 "splitStatus": 0,
 "isRefund": false,
 "orderStatus": 30,
 "warehouseName": "云仓商城",
 "warehouseId": "WSC1111",
 "orderPackageInfos": [
 {
 "expressName": null,
 "expressNumber": null
 },
 {
 "expressName": null,
 "expressNumber": null
 }
 ],
 "auditOperatorName": "系统自动",
 "auditTime": "2024-05-06 10:09:31",
 "shipOperatorName": null,
 "shipTime": null,
 "billOperatorName": null,
 "billTime": null,
 "exOperatorName": null,
 "exOperatorTime": null,
 "completeTime": null,
 "cancelTime": null,
 "erpBillInfo": {
 "clientConfId": "359074",
 "billTotalAmount": 100,
 "billCommodityAmount": 100,
 "platformDiscount": 0,
 "merchantDiscount": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "platBrokerageAmount": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "detailDiscountAmount": 0,
 "adjustAmount": 0
 },
 "storeBillConfig": {
 "id": 359074,
 "name": "初始系统配置",
 "merCode": "SPHYDEE",
 "platformCode": "43",
 "clientCode": "WSC1111",
 "storeCode": "WSC1111",
 "storeName": "云仓商城",
 "freightFeeFetch": 1,
 "freightFeeInventory": false,
 "packageFeeFetch": 2,
 "packageFeeInventory": true,
 "mcDiscountInventory": true,
 "mcDiscountShare": false,
 "ptfDiscountInventory": false,
 "ptfDiscountShare": null,
 "ptfCommissionInventory": true,
 "ptfCommissionShare": false,
 "mcDtlDiscountInventory": true,
 "mcDtlDiscountShare": true,
 "cashierSource": 2,
 "creator": "系统自动",
 "createTime": "2023-11-20 15:11:54",
 "modifyTime": "2023-11-20 15:13:10",
 "confType": 1,
 "platformName": "微商城",
 "checkerType": null,
 "frequencyType": null,
 "billErpSetting": 1,
 "isBatchNoStock": 1,
 "autoEnterAccountFlag": 0,
 "billOrganizationFlag": 0
 },
 "isPostFeeOrder": 0,
 "isSupplier": false,
 "mergeStatus": null,
 "emptyStatus": null,
 "preSellStatus": null,
 "organizationName": "云仓商城",
 "organizationCode": "WSC1111",
 "orderOwnerType": 1,
 "merCode": "500001",
 "merName": "一心堂集团",
 "supplierCode": "T1D0B",
 "supplierName": "一心便利连锁（云南）有限公司",
 "spreadStoreCode": null,
 "spreadStoreName": null,
 "tag": null,
 "stockState": null,
 "settlementStatus": null
 },
 "timestamp": *************
}
  3. {
 "code": "10000",
 "msg": "操作成功",
 "data": {
 "orderNo": "1798267334755840773",
 "omsOrderNo": "1798267334861747205",
 "thirdPlatformCode": "43",
 "thirdPlatformName": null,
 "clientCode": "WSC1111",
 "onlineStoreCode": "WSC1111",
 "onlineStoreName": "云仓商城",
 "thirdOrderNo": "1798267334013984775",
 "thirdOrderState": "3",
 "buyerMessage": "",
 "sellerRemark": null,
 "remark": null,
 "buyerName": "— —",
 "prescriptionStatus": null,
 "clientConfId": 359074,
 "isPrescription": false,
 "payType": "1",
 "buyerActualAmount": 0,
 "created": "2024-05-06 10:09:30",
 "payTime": "2024-05-06 10:09:30",
 "receiverName": "李燕",
 "receiverMobile": "",
 "originalFullAddress": "",
 "province": "云南省",
 "city": "昆明市",
 "district": "官渡区",
 "town": null,
 "address": "",
 "needInvoice": false,
 "invoiceType": null,
 "invoiceName": null,
 "vatTaxpayerNumber": null,
 "refundCount": 0,
 "orderDetails": [
 {
 "id": 29190801,
 "platformSkuId": "109072000000544",
 "thirdDetailId": "1798267334755840773_1",
 "erpCode": "109072000000544",
 "barCode": null,
 "commodityName": "蓝漂蜜桃香型洗洁精2斤瓶装LP-366980",
 "commoditySpec": "瓶",
 "commodityCount": 4,
 "originalPrice": 25,
 "price": 25,
 "totalAmount": 100,
 "discountAmount": 0,
 "adjustAmount": 0,
 "discountShare": 0,
 "actualNetAmount": 100,
 "billPrice": 25,
 "platformDiscountFee": 0,
 "merchantDiscountFee": 0,
 "platformDiscount": null,
 "merchantDiscount": null,
 "brokerageAmount": 0,
 "isGift": 0,
 "erpGift": 1,
 "status": 0,
 "oldErpCode": null,
 "goodsType": 1,
 "drugType": 3,
 "outerId": null,
 "rebateratio": 0,
 "vipDifferentAmt": 0,
 "settlePrice": 0,
 "originalOmsOrderNo": null,
 "batchNos": null,
 "refundStatus": "正常",
 "averagePrice": 14.5,
 "detailSettlementStatus": 0
 }
 ],
 "orderPayInfo": {
 "payType": "1",
 "buyerActualAmount": 0,
 "merchantActualAmount": 100,
 "totalAmount": 100,
 "totalDiscount": 0,
 "platformDiscount": 0,
 "merchantDiscountSum": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "postFee": null,
 "buyerCodServiceFee": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "sellerCodServiceFee": 0,
 "brokerageAmount": 0,
 "buyerCodAmount": 0,
 "platformFeeCollection": 0,
 "manualFixAmount": 0,
 "adjustAmount": 0,
 "detailDiscountCollect": 0,
 "differentAmount": 0
 },
 "prescription": null,
 "orderType": 1,
 "splitStatus": 0,
 "isRefund": false,
 "orderStatus": 30,
 "warehouseName": "云仓商城",
 "warehouseId": "WSC1111",
 "orderPackageInfos": [
 {
 "expressName": null,
 "expressNumber": null
 },
 {
 "expressName": null,
 "expressNumber": null
 }
 ],
 "auditOperatorName": "系统自动",
 "auditTime": "2024-05-06 10:09:31",
 "shipOperatorName": null,
 "shipTime": null,
 "billOperatorName": null,
 "billTime": null,
 "exOperatorName": null,
 "exOperatorTime": null,
 "completeTime": null,
 "cancelTime": null,
 "erpBillInfo": {
 "clientConfId": "359074",
 "billTotalAmount": 100,
 "billCommodityAmount": 100,
 "platformDiscount": 0,
 "merchantDiscount": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "platBrokerageAmount": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "detailDiscountAmount": 0,
 "adjustAmount": 0
 },
 "storeBillConfig": {
 "id": 359074,
 "name": "初始系统配置",
 "merCode": "SPHYDEE",
 "platformCode": "43",
 "clientCode": "WSC1111",
 "storeCode": "WSC1111",
 "storeName": "云仓商城",
 "freightFeeFetch": 1,
 "freightFeeInventory": false,
 "packageFeeFetch": 2,
 "packageFeeInventory": true,
 "mcDiscountInventory": true,
 "mcDiscountShare": false,
 "ptfDiscountInventory": false,
 "ptfDiscountShare": null,
 "ptfCommissionInventory": true,
 "ptfCommissionShare": false,
 "mcDtlDiscountInventory": true,
 "mcDtlDiscountShare": true,
 "cashierSource": 2,
 "creator": "系统自动",
 "createTime": "2023-11-20 15:11:54",
 "modifyTime": "2023-11-20 15:13:10",
 "confType": 1,
 "platformName": "微商城",
 "checkerType": null,
 "frequencyType": null,
 "billErpSetting": 1,
 "isBatchNoStock": 1,
 "autoEnterAccountFlag": 0,
 "billOrganizationFlag": 0
 },
 "isPostFeeOrder": 0,
 "isSupplier": false,
 "mergeStatus": null,
 "emptyStatus": null,
 "preSellStatus": null,
 "organizationName": "云仓商城",
 "organizationCode": "WSC1111",
 "orderOwnerType": 1,
 "merCode": "500001",
 "merName": "一心堂集团",
 "supplierCode": "T1D0B",
 "supplierName": "一心便利连锁（云南）有限公司",
 "spreadStoreCode": null,
 "spreadStoreName": null,
 "tag": null,
 "stockState": null,
 "settlementStatus": null
 },
 "timestamp": *************
}
  1. {
 "code": "10000",
 "msg": "操作成功",
 "data": {
 "orderNo": "1798267334755840773",
 "omsOrderNo": "1798267334861747205",
 "thirdPlatformCode": "43",
 "thirdPlatformName": null,
 "clientCode": "WSC1111",
 "onlineStoreCode": "WSC1111",
 "onlineStoreName": "云仓商城",
 "thirdOrderNo": "1798267334013984775",
 "thirdOrderState": "3",
 "buyerMessage": "",
 "sellerRemark": null,
 "remark": null,
 "buyerName": "— —",
 "prescriptionStatus": null,
 "clientConfId": 359074,
 "isPrescription": false,
 "payType": "1",
 "buyerActualAmount": 0,
 "created": "2024-05-06 10:09:30",
 "payTime": "2024-05-06 10:09:30",
 "receiverName": "李燕",
 "receiverMobile": "",
 "originalFullAddress": "",
 "province": "云南省",
 "city": "昆明市",
 "district": "官渡区",
 "town": null,
 "address": "",
 "needInvoice": false,
 "invoiceType": null,
 "invoiceName": null,
 "vatTaxpayerNumber": null,
 "refundCount": 0,
 "orderDetails": [
 {
 "id": 29190801,
 "platformSkuId": "109072000000544",
 "thirdDetailId": "1798267334755840773_1",
 "erpCode": "109072000000544",
 "barCode": null,
 "commodityName": "蓝漂蜜桃香型洗洁精2斤瓶装LP-366980",
 "commoditySpec": "瓶",
 "commodityCount": 4,
 "originalPrice": 25,
 "price": 25,
 "totalAmount": 100,
 "discountAmount": 0,
 "adjustAmount": 0,
 "discountShare": 0,
 "actualNetAmount": 100,
 "billPrice": 25,
 "platformDiscountFee": 0,
 "merchantDiscountFee": 0,
 "platformDiscount": null,
 "merchantDiscount": null,
 "brokerageAmount": 0,
 "isGift": 0,
 "erpGift": 1,
 "status": 0,
 "oldErpCode": null,
 "goodsType": 1,
 "drugType": 3,
 "outerId": null,
 "rebateratio": 0,
 "vipDifferentAmt": 0,
 "settlePrice": 0,
 "originalOmsOrderNo": null,
 "batchNos": null,
 "refundStatus": "正常",
 "averagePrice": 14.5,
 "detailSettlementStatus": 0
 }
 ],
 "orderPayInfo": {
 "payType": "1",
 "buyerActualAmount": 0,
 "merchantActualAmount": 100,
 "totalAmount": 100,
 "totalDiscount": 0,
 "platformDiscount": 0,
 "merchantDiscountSum": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "postFee": null,
 "buyerCodServiceFee": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "sellerCodServiceFee": 0,
 "brokerageAmount": 0,
 "buyerCodAmount": 0,
 "platformFeeCollection": 0,
 "manualFixAmount": 0,
 "adjustAmount": 0,
 "detailDiscountCollect": 0,
 "differentAmount": 0
 },
 "prescription": null,
 "orderType": 1,
 "splitStatus": 0,
 "isRefund": false,
 "orderStatus": 30,
 "warehouseName": "云仓商城",
 "warehouseId": "WSC1111",
 "orderPackageInfos": [
 {
 "expressName": null,
 "expressNumber": null
 },
 {
 "expressName": null,
 "expressNumber": null
 }
 ],
 "auditOperatorName": "系统自动",
 "auditTime": "2024-05-06 10:09:31",
 "shipOperatorName": null,
 "shipTime": null,
 "billOperatorName": null,
 "billTime": null,
 "exOperatorName": null,
 "exOperatorTime": null,
 "completeTime": null,
 "cancelTime": null,
 "erpBillInfo": {
 "clientConfId": "359074",
 "billTotalAmount": 100,
 "billCommodityAmount": 100,
 "platformDiscount": 0,
 "merchantDiscount": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "platBrokerageAmount": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "detailDiscountAmount": 0,
 "adjustAmount": 0
 },
 "storeBillConfig": {
 "id": 359074,
 "name": "初始系统配置",
 "merCode": "SPHYDEE",
 "platformCode": "43",
 "clientCode": "WSC1111",
 "storeCode": "WSC1111",
 "storeName": "云仓商城",
 "freightFeeFetch": 1,
 "freightFeeInventory": false,
 "packageFeeFetch": 2,
 "packageFeeInventory": true,
 "mcDiscountInventory": true,
 "mcDiscountShare": false,
 "ptfDiscountInventory": false,
 "ptfDiscountShare": null,
 "ptfCommissionInventory": true,
 "ptfCommissionShare": false,
 "mcDtlDiscountInventory": true,
 "mcDtlDiscountShare": true,
 "cashierSource": 2,
 "creator": "系统自动",
 "createTime": "2023-11-20 15:11:54",
 "modifyTime": "2023-11-20 15:13:10",
 "confType": 1,
 "platformName": "微商城",
 "checkerType": null,
 "frequencyType": null,
 "billErpSetting": 1,
 "isBatchNoStock": 1,
 "autoEnterAccountFlag": 0,
 "billOrganizationFlag": 0
 },
 "isPostFeeOrder": 0,
 "isSupplier": false,
 "mergeStatus": null,
 "emptyStatus": null,
 "preSellStatus": null,
 "organizationName": "云仓商城",
 "organizationCode": "WSC1111",
 "orderOwnerType": 1,
 "merCode": "500001",
 "merName": "一心堂集团",
 "supplierCode": "T1D0B",
 "supplierName": "一心便利连锁（云南）有限公司",
 "spreadStoreCode": null,
 "spreadStoreName": null,
 "tag": null,
 "stockState": null,
 "settlementStatus": null
 },
 "timestamp": *************
}
18. **接口地址:/b2c/1.0/order/detail/{omsOrderNo}**
19. **返参示例**
  1. {
 "code": "10000",
 "msg": "操作成功",
 "data": {
 "orderNo": "1798267334755840773",
 "omsOrderNo": "1798267334861747205",
 "thirdPlatformCode": "43",
 "thirdPlatformName": null,
 "clientCode": "WSC1111",
 "onlineStoreCode": "WSC1111",
 "onlineStoreName": "云仓商城",
 "thirdOrderNo": "1798267334013984775",
 "thirdOrderState": "3",
 "buyerMessage": "",
 "sellerRemark": null,
 "remark": null,
 "buyerName": "— —",
 "prescriptionStatus": null,
 "clientConfId": 359074,
 "isPrescription": false,
 "payType": "1",
 "buyerActualAmount": 0,
 "created": "2024-05-06 10:09:30",
 "payTime": "2024-05-06 10:09:30",
 "receiverName": "李燕",
 "receiverMobile": "",
 "originalFullAddress": "",
 "province": "云南省",
 "city": "昆明市",
 "district": "官渡区",
 "town": null,
 "address": "",
 "needInvoice": false,
 "invoiceType": null,
 "invoiceName": null,
 "vatTaxpayerNumber": null,
 "refundCount": 0,
 "orderDetails": [
 {
 "id": 29190801,
 "platformSkuId": "109072000000544",
 "thirdDetailId": "1798267334755840773_1",
 "erpCode": "109072000000544",
 "barCode": null,
 "commodityName": "蓝漂蜜桃香型洗洁精2斤瓶装LP-366980",
 "commoditySpec": "瓶",
 "commodityCount": 4,
 "originalPrice": 25,
 "price": 25,
 "totalAmount": 100,
 "discountAmount": 0,
 "adjustAmount": 0,
 "discountShare": 0,
 "actualNetAmount": 100,
 "billPrice": 25,
 "platformDiscountFee": 0,
 "merchantDiscountFee": 0,
 "platformDiscount": null,
 "merchantDiscount": null,
 "brokerageAmount": 0,
 "isGift": 0,
 "erpGift": 1,
 "status": 0,
 "oldErpCode": null,
 "goodsType": 1,
 "drugType": 3,
 "outerId": null,
 "rebateratio": 0,
 "vipDifferentAmt": 0,
 "settlePrice": 0,
 "originalOmsOrderNo": null,
 "batchNos": null,
 "refundStatus": "正常",
 "averagePrice": 14.5,
 "detailSettlementStatus": 0
 }
 ],
 "orderPayInfo": {
 "payType": "1",
 "buyerActualAmount": 0,
 "merchantActualAmount": 100,
 "totalAmount": 100,
 "totalDiscount": 0,
 "platformDiscount": 0,
 "merchantDiscountSum": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "postFee": null,
 "buyerCodServiceFee": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "sellerCodServiceFee": 0,
 "brokerageAmount": 0,
 "buyerCodAmount": 0,
 "platformFeeCollection": 0,
 "manualFixAmount": 0,
 "adjustAmount": 0,
 "detailDiscountCollect": 0,
 "differentAmount": 0
 },
 "prescription": null,
 "orderType": 1,
 "splitStatus": 0,
 "isRefund": false,
 "orderStatus": 30,
 "warehouseName": "云仓商城",
 "warehouseId": "WSC1111",
 "orderPackageInfos": [
 {
 "expressName": null,
 "expressNumber": null
 },
 {
 "expressName": null,
 "expressNumber": null
 }
 ],
 "auditOperatorName": "系统自动",
 "auditTime": "2024-05-06 10:09:31",
 "shipOperatorName": null,
 "shipTime": null,
 "billOperatorName": null,
 "billTime": null,
 "exOperatorName": null,
 "exOperatorTime": null,
 "completeTime": null,
 "cancelTime": null,
 "erpBillInfo": {
 "clientConfId": "359074",
 "billTotalAmount": 100,
 "billCommodityAmount": 100,
 "platformDiscount": 0,
 "merchantDiscount": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "platBrokerageAmount": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "detailDiscountAmount": 0,
 "adjustAmount": 0
 },
 "storeBillConfig": {
 "id": 359074,
 "name": "初始系统配置",
 "merCode": "SPHYDEE",
 "platformCode": "43",
 "clientCode": "WSC1111",
 "storeCode": "WSC1111",
 "storeName": "云仓商城",
 "freightFeeFetch": 1,
 "freightFeeInventory": false,
 "packageFeeFetch": 2,
 "packageFeeInventory": true,
 "mcDiscountInventory": true,
 "mcDiscountShare": false,
 "ptfDiscountInventory": false,
 "ptfDiscountShare": null,
 "ptfCommissionInventory": true,
 "ptfCommissionShare": false,
 "mcDtlDiscountInventory": true,
 "mcDtlDiscountShare": true,
 "cashierSource": 2,
 "creator": "系统自动",
 "createTime": "2023-11-20 15:11:54",
 "modifyTime": "2023-11-20 15:13:10",
 "confType": 1,
 "platformName": "微商城",
 "checkerType": null,
 "frequencyType": null,
 "billErpSetting": 1,
 "isBatchNoStock": 1,
 "autoEnterAccountFlag": 0,
 "billOrganizationFlag": 0
 },
 "isPostFeeOrder": 0,
 "isSupplier": false,
 "mergeStatus": null,
 "emptyStatus": null,
 "preSellStatus": null,
 "organizationName": "云仓商城",
 "organizationCode": "WSC1111",
 "orderOwnerType": 1,
 "merCode": "500001",
 "merName": "一心堂集团",
 "supplierCode": "T1D0B",
 "supplierName": "一心便利连锁（云南）有限公司",
 "spreadStoreCode": null,
 "spreadStoreName": null,
 "tag": null,
 "stockState": null,
 "settlementStatus": null
 },
 "timestamp": *************
}
20. {
 "code": "10000",
 "msg": "操作成功",
 "data": {
 "orderNo": "1798267334755840773",
 "omsOrderNo": "1798267334861747205",
 "thirdPlatformCode": "43",
 "thirdPlatformName": null,
 "clientCode": "WSC1111",
 "onlineStoreCode": "WSC1111",
 "onlineStoreName": "云仓商城",
 "thirdOrderNo": "1798267334013984775",
 "thirdOrderState": "3",
 "buyerMessage": "",
 "sellerRemark": null,
 "remark": null,
 "buyerName": "— —",
 "prescriptionStatus": null,
 "clientConfId": 359074,
 "isPrescription": false,
 "payType": "1",
 "buyerActualAmount": 0,
 "created": "2024-05-06 10:09:30",
 "payTime": "2024-05-06 10:09:30",
 "receiverName": "李燕",
 "receiverMobile": "",
 "originalFullAddress": "",
 "province": "云南省",
 "city": "昆明市",
 "district": "官渡区",
 "town": null,
 "address": "",
 "needInvoice": false,
 "invoiceType": null,
 "invoiceName": null,
 "vatTaxpayerNumber": null,
 "refundCount": 0,
 "orderDetails": [
 {
 "id": 29190801,
 "platformSkuId": "109072000000544",
 "thirdDetailId": "1798267334755840773_1",
 "erpCode": "109072000000544",
 "barCode": null,
 "commodityName": "蓝漂蜜桃香型洗洁精2斤瓶装LP-366980",
 "commoditySpec": "瓶",
 "commodityCount": 4,
 "originalPrice": 25,
 "price": 25,
 "totalAmount": 100,
 "discountAmount": 0,
 "adjustAmount": 0,
 "discountShare": 0,
 "actualNetAmount": 100,
 "billPrice": 25,
 "platformDiscountFee": 0,
 "merchantDiscountFee": 0,
 "platformDiscount": null,
 "merchantDiscount": null,
 "brokerageAmount": 0,
 "isGift": 0,
 "erpGift": 1,
 "status": 0,
 "oldErpCode": null,
 "goodsType": 1,
 "drugType": 3,
 "outerId": null,
 "rebateratio": 0,
 "vipDifferentAmt": 0,
 "settlePrice": 0,
 "originalOmsOrderNo": null,
 "batchNos": null,
 "refundStatus": "正常",
 "averagePrice": 14.5,
 "detailSettlementStatus": 0
 }
 ],
 "orderPayInfo": {
 "payType": "1",
 "buyerActualAmount": 0,
 "merchantActualAmount": 100,
 "totalAmount": 100,
 "totalDiscount": 0,
 "platformDiscount": 0,
 "merchantDiscountSum": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "postFee": null,
 "buyerCodServiceFee": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "sellerCodServiceFee": 0,
 "brokerageAmount": 0,
 "buyerCodAmount": 0,
 "platformFeeCollection": 0,
 "manualFixAmount": 0,
 "adjustAmount": 0,
 "detailDiscountCollect": 0,
 "differentAmount": 0
 },
 "prescription": null,
 "orderType": 1,
 "splitStatus": 0,
 "isRefund": false,
 "orderStatus": 30,
 "warehouseName": "云仓商城",
 "warehouseId": "WSC1111",
 "orderPackageInfos": [
 {
 "expressName": null,
 "expressNumber": null
 },
 {
 "expressName": null,
 "expressNumber": null
 }
 ],
 "auditOperatorName": "系统自动",
 "auditTime": "2024-05-06 10:09:31",
 "shipOperatorName": null,
 "shipTime": null,
 "billOperatorName": null,
 "billTime": null,
 "exOperatorName": null,
 "exOperatorTime": null,
 "completeTime": null,
 "cancelTime": null,
 "erpBillInfo": {
 "clientConfId": "359074",
 "billTotalAmount": 100,
 "billCommodityAmount": 100,
 "platformDiscount": 0,
 "merchantDiscount": 0,
 "merchantDeliveryFee": 0,
 "platformDeliveryFee": 0,
 "platBrokerageAmount": 0,
 "merchantPackFee": 0,
 "platformPackFee": 0,
 "detailDiscountAmount": 0,
 "adjustAmount": 0
 },
 "storeBillConfig": {
 "id": 359074,
 "name": "初始系统配置",
 "merCode": "SPHYDEE",
 "platformCode": "43",
 "clientCode": "WSC1111",
 "storeCode": "WSC1111",
 "storeName": "云仓商城",
 "freightFeeFetch": 1,
 "freightFeeInventory": false,
 "packageFeeFetch": 2,
 "packageFeeInventory": true,
 "mcDiscountInventory": true,
 "mcDiscountShare": false,
 "ptfDiscountInventory": false,
 "ptfDiscountShare": null,
 "ptfCommissionInventory": true,
 "ptfCommissionShare": false,
 "mcDtlDiscountInventory": true,
 "mcDtlDiscountShare": true,
 "cashierSource": 2,
 "creator": "系统自动",
 "createTime": "2023-11-20 15:11:54",
 "modifyTime": "2023-11-20 15:13:10",
 "confType": 1,
 "platformName": "微商城",
 "checkerType": null,
 "frequencyType": null,
 "billErpSetting": 1,
 "isBatchNoStock": 1,
 "autoEnterAccountFlag": 0,
 "billOrganizationFlag": 0
 },
 "isPostFeeOrder": 0,
 "isSupplier": false,
 "mergeStatus": null,
 "emptyStatus": null,
 "preSellStatus": null,
 "organizationName": "云仓商城",
 "organizationCode": "WSC1111",
 "orderOwnerType": 1,
 "merCode": "500001",
 "merName": "一心堂集团",
 "supplierCode": "T1D0B",
 "supplierName": "一心便利连锁（云南）有限公司",
 "spreadStoreCode": null,
 "spreadStoreName": null,
 "tag": null,
 "stockState": null,
 "settlementStatus": null
 },
 "timestamp": *************
}
21. **B2C更新快递单号接口**
  1. ****
  2. **接口地址:****/1.0/scanShip/updateExpress**
22. ****
23. **接口地址:****/1.0/scanShip/updateExpress**
24. **B2C管理平台回调接口**
  1. **接口地址:/omsOrder/status/alteration**
  2. **请求参数**
    1. {
 "omsOrderNo":"1798215846943323136",
 "operateStatus":"",
 "orderkind":"0",
 "expressNo":"**************,**************",
 "expressCompany":"05,05"
}
    2. 其中快递单号与快递公司为一对一关系
  3. {
 "omsOrderNo":"1798215846943323136",
 "operateStatus":"",
 "orderkind":"0",
 "expressNo":"**************,**************",
 "expressCompany":"05,05"
}
  4. 其中快递单号与快递公司为一对一关系
  1. {
 "omsOrderNo":"1798215846943323136",
 "operateStatus":"",
 "orderkind":"0",
 "expressNo":"**************,**************",
 "expressCompany":"05,05"
}
  2. 其中快递单号与快递公司为一对一关系
25. **接口地址:/omsOrder/status/alteration**
26. **请求参数**
  1. {
 "omsOrderNo":"1798215846943323136",
 "operateStatus":"",
 "orderkind":"0",
 "expressNo":"**************,**************",
 "expressCompany":"05,05"
}
  2. 其中快递单号与快递公司为一对一关系
27. {
 "omsOrderNo":"1798215846943323136",
 "operateStatus":"",
 "orderkind":"0",
 "expressNo":"**************,**************",
 "expressCompany":"05,05"
}
28. 其中快递单号与快递公司为一对一关系


## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

**接口文档输出：2024年5月6日;**

**研发时间：2024年5月7日-2024年5月17日；**

**联调时间：2024年4月15日-2024年4月18日(含自测)；**

**测试时间：2024年4月19日(提测)；**

## 九、 上线方案