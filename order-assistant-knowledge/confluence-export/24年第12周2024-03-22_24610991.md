# 24年第12周2024-03-22

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构-DDD项目 |  |  | 下周二技术方案评审 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  |  | 周三开了一个头，正在按业务梳理分批次开发的功能。 | .net代码黑盒,无法多人同时开发. 按平台迁移对接代码 |
| 3 | 优雅发布升级-mq |  |  |  | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  |  |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | [https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq)  上线计划 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 | 郭志明 | **本周总工时：5pd**1. 一心到家V1.1.6-会订货补单 2. 一心到家V1.1.6-支付配置 | **㊀计划工作****㊁实际完成**1. 一心到家V1.1.6-会订货补单（待上线） 2. 一心到家V1.1.6-支付配置（开发中） **㊂遗留问题** **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 |  | **本周总工时：4pd**1.下账时机修改以及生成运费单2.云仓迁移订单刷新商品ID3.线上运维 | **㊀计划工作**1.下账时机修改以及生成运费单2.云仓迁移订单刷新商品ID3.线上运维**㊁实际完成**1.下账时机修改以及生成运费单2.云仓迁移订单刷新商品ID3.线上运维**㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 |  | **本周总工时：5pd**1. 订单中台重构方案补充: 应用架构图、部署架构图、替换完.net之后的整体数据交互简图、价值收益等 2. api网关移除local-mappings配置,服务之间调用不走svc调用。配合运维、商品确认上线之后的效果。(运维层面也调整了负载均衡) 3. mq优雅发布准备工作: 应用发布期间mq消费报获取数据源异常,场景复现。 4. 订单域的服务整理,承接目前订单的流量(目前的订单流量可以理解为订单的小前台)。提供订单领域层面的核心服务能力   **** | **㊀计划工作** **㊁实际完成** **㊂遗留问题**  **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 4 |  | **本周总工时：5pd** 1. v.1.6 订单路由 技术方案文档设计 数据库表建立及部分数据初始化 部分代码开发 2. O2O及B2C 订单问题追踪解决 3. 支付模块，微商城店铺未配置医保通道 但是店铺详情展示及可拉出医保支付问题 处理 | **㊀计划工作** **㊁实际完成****1. v.1.6 订单路由 技术方案文档设计 已完成****数据库表建立及部分数据初始化****2. O2O及B2C 订单问题追踪解决 已解决****3. 支付模块，微商城店铺未配置医保通道，****但是店铺详情展示及可拉出医保支付问题 已解决****** **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 5 |  | **本周总工时：5pd**1.医保订单功能上线、历史医保单数据刷入2.线上医保订单导出、显示bug修复3.订单路由用例图、时序图4.订单路由场景配置实现和接口测试 | **㊀计划工作**1.医保订单功能上线、历史医保单刷入2.订单路由场景配置功能实现**㊁实际完成**1.医保订单功能上线、历史医保单刷入2.订单路由场景配置功能实现**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：5pd**1. 订单路由需求评审 2. 海典H1pos下账实收金额调整（有退款情况时传的是重算前的） 3. 京东到家部分退款生成全额退款单问题排查 4. 微商城有两个订单存在分摊折扣为负数的错误 5. 会员mq消费消息异常问题排查 6. 修数工具originPrice字段赋值调整 7. 订单路由需求配送费计算规则开发（70%） | **㊀计划工作**1. 订单路由需求开发 **㊁实际完成****㊂遗留问题** **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 订单路由需求开发 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 7 |  | **本周总工时：5**1.下账时机开发2.生产问题订单 | **㊀计划工作**1.下账时机开发和需求单2.生产问题订单**㊁实际完成**1.下账时机开发 2.生产问题订单**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1.下账时机开发2.生产问题订单**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 8 |  | **本周总工时：5pd****1.线上运维 ，主要是徐凯这边找过来的问题。 1 day****2.饿了么商品通知，目前完成。 1 day****3. 商品1.6 ，诺和订单0元单 和心币订单导出 1day****4. 美团消息推送。1 day。****5. .NET docker build 测试。 1 day** | **㊀计划工作****㊁实际完成****㊂遗留问题****1.心币订单 没有处理。****2.美团消息推送，删除信息美团有bug 已经提交工单。****3. .net docker 测试 微商城 订单 无法调用成功。****4.处方审方流程变更。****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx  **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

1.分支管理 删除多余分支.
2.沟通表达 - 接着日常发现的问题发现. 结论先行. 抓重点反馈, 带着问题的答案选项 去提供反馈
3.重构思路转变: 中台+DDD+可插拔方案.

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |