# 【20240423】 B2C/云仓商品 邮政对接/极兔对接、设置默认快递

## 一、 背景

### 1.1 业务背景

 公司不断开通B2C店铺，B2C履约为快递发货，那么运费的高低成了是否盈利的关键；为了降低发货成本，需要一些处理办法来解决这个事情。

### 1.2 痛点分析

不同的地域不同的线上门店线下门店可使用的快递商选择范围不同，价格也不同，为了降低运输成本，需要能方便的选择出合适的快递公司至关重要。本次方案是通过在店铺上配置快递和快递优先级的方式自动选择最便宜的快递作为发货商，以此来降低发货成本。

### 1.3 系统现状

手动创建面单时，通过用户选择快递公司去生成面单；非手动创建面单时，根据订单中的快递信息去生成面单。

## 二、需求分析

## 三、 现状分析

### 3.1 业务流程分析

**trueefalseautotoptrue5217**

### 3.2 业务数据关系

生成面单当前版本是根据面单配置的相关信息进行生成，修改后会变更根据快递公司店铺映射关系来生成。

truebfalseautotoptrue9318

### 3.3 数据关系

trueafalseautotoptrue129312

### 3.4 快递相关接口调用关系

true未命名绘图falseautotoptrue9144

### 3.5 目前快递接入情况

## 四、业务风险

trueffalseautotoptrue8711

4.1 其他一些业务操作会影响面单配置数据的变更，但是无法影响到生成面单

4.2 面单数据除了生成面单以外，还会对业务造成什么影响？比如

- 配置其他业务数据时，会根据面单配置信息做相关联动
- 查询或修改业务数据时关联了面单配置信息数据


4.3 在当前版本没有修改快递地址接口实现，新增两个修改快递地址不会被任何流程调用到。

## 五、 目标

### 5.1 本期目标

#### 5.1.1 业务目标

1. 接入邮政和极兔快递
2. 对快递做管理
3. 做店铺和快递映射管理，将地址放到店铺维度。
4. 优化获取快递策略，从主动选择的方式修改成根据配置优先级自动选择。


## 六、 详细设计

### 6.1 对接邮政和极兔

通过在hydee-third-inside中实现统一接口，完成对邮政和极兔的对接 

### 6.2 快递公司管理

#### 6.2.1 表结构设计

express_merchant

truecfalseautotoptrue7307

平台表新增logo地址

alter table ds_platform
add logo_path text null comment 'logo地址';

根据现有快递公司数据进行初始化数据



#### 6.2.2 接口设计

##### 6.2.2.1 分页接口

uri:*/1.0/express/merchant/page*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| expressName | String | 是 | 快递名称 |
| platformCode | String | 是 | 平台Code |
| currentPage | Integer | 是 | 页码 |
| ``` pageSize ``` | Integer | 是 | 每页数量 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | id |
| expressName | Integer | 快递名称 |
| expressCode | List | 快递编码 |
| status | Integer | 快递状态，0禁用，1启用 |
| logoPath | String | 快递logo |
| platformLogoPaths | List | 平台logo |
| expanded | Map | 直连开发者信息 |


##### 6.2.2.2 明细接口

uri:*/1.0/express/merchant/detail/{id}*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 是 | 快递id |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | id |
| expressName | Integer | 快递名称 |
| expressCode | List | 快递编码 |
| status | Integer | 快递状态，0禁用，1启用 |
| logoPath | String | 快递logo |
| platformList | List | [{  "platformLogoPaths":"平台logo地址",  "platformCode":"平台code" }] |
| expanded | Map | 直连开发者信息 |


##### 6.2.2.3 新增接口

uri:*/1.0/express/merchant/create*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| expressName | String | 否 | 快递名称 |
| expressCode | String | 否 | 快递编码 |
| platformIds | List | 否 | 适配平台id |
| extended | Map | 否 | 直连开发者信息，动态字段 |
| logoPath | String | 否 | 快递logo |


##### 6.2.2.4 修改接口

uri:*/1.0/express/merchant/update*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 否 | 快递id |
| expressName | String | 否 | 快递名称 |
| expressCode | String | 否 | 快递编码 |
| platformIds | List | 否 | 适配平台id |
| extended | Map | 否 | 直连开发者信息，动态字段 |
| logoPath | String | 否 | 快递logo |


##### 6.2.2.5 删除接口

uri:*/1.0/express/merchant/delete*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 否 | 快递id |


##### 6.2.2.6 平台分页查询

uri:*/1.0/ds/platform/list*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| currentPage | Integer | 是 | 页码 |
| ``` pageSize ``` | Integer | 是 | 每页数量 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | 平台id |
| platformCode | String | 平台Code |
| platformName | String | 平台名称 |
| logoPath | String | 平台logo |


##### 6.2.2.7 根据平台查询快递公司

uri:*/1.0/express/merchant/base/page*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| platformCode | String | 是 | 平台Code |
| currentPage | Integer | 是 | 页码 |
| ``` pageSize ``` | Integer | 是 | 每页数量 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | 平台id |
| expressName | String | 快递名称 |
| expressCode | String | 快递编码 |
| logoPath | String | 平台logo |


### 6.3 快递公司店铺映射管理

#### 6.3.1 表结构设计

truedfalseautotoptrue75110

#### 6.3.2 接口设计

##### 6.3.2.1 分页接口

uri:*/1.0/online/store/express/merchant/page*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| dsOnlineStoreId | String | 是 | 店铺id |
| platformCode | String | 是 | 平台Code |
| expressMerchantId | Long | 是 | 快递id |
| currentPage | Integer | 是 | 页码 |
| ``` pageSize ``` | Integer | 是 | 每页数量 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | id |
| platformName | String | 平台名称 |
| expressName | String | 快递名称 |
| expressLogoPath | String | 快递公司logo |
| ``` warehouseName ``` | String | 仓库名称 |
| linkTypeName | String | 链接模式名称 |
| priority | Integer | 优先级 |
| status | Integer | 0禁用，1启用 |
| orderStandardTemplateId | Long | 发货单模板管理id |


##### 6.3.2.2 明细接口

uri:*/1.0/online/store/express/merchant/detail/{id}*

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 否 | 业务数据id |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | id |
| platformId | Long | 平台id |
| platformName | String | 平台名称 |
| expressMerchantId | Long | 快递id |
| expressMerchantName | String | 快递名称 |
| expressLogoPath | String | 快递公司logo |
| ``` warehouseName ``` | String | 仓库名称 |
| linkTypeName | String | 链接模式名称 |
| linkTypeCode | String | 链接模式Code |
| priority | Integer | 优先级 |
| status | Integer | 0禁用，1启用 |
| standardTemplateId | Long | 面单模板管理id |
| orderStandardTemplateId | Long | 发货单模板管理id |
| expanded | Map | 动态字段（网点设置信息） |


##### 6.3.2.2 新增接口

uri:*/1.0/online/store/express/merchant/create*

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| platformCode | String | 否 | 平台Code |
| platformId | Long | 否 | 平台ID |
| dsOnlineStoreId | Long | 否 | 店铺id |
| dsOnlineStoreCode | String | 是 | 店铺Code |
| ``` expressMerchantId ``` | Integer | 是 | 快递公司id |
| linkType | String | 否 | 链接模式 |
| standardTemplateId | Long | 否 | 面单模板管理id |
| logisticStdtemplatesId | Long | 否 | 面单模板id |
| orderStandardTemplateId | Long | 否 | 发货单模板管理id |
| orderLogisticStdtemplatesId | Long | 否 | 发货单模板id |
| priority | Integer | 否 | 优先级(0-9) |
| expanded | Map | 否 | 动态字段（网点设置信息） |


##### 6.3.2.2 修改接口

uri:*/1.0/online/store/express/merchant/create*

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 否 | 主键id |
| platformCode | String | 否 | 平台Code |
| platformId | Long | 否 | 平台ID |
| dsOnlineStoreId | Long | 否 | 店铺id |
| dsOnlineStoreCode | String | 是 | 店铺Code |
| ``` expressMerchantId ``` | Integer | 是 | 快递公司id |
| linkType | String | 否 | 链接模式 |
| standardTemplateId | Long | 否 | 面单模板管理id |
| logisticStdtemplatesId | Long | 否 | 面单模板id |
| orderStandardTemplateId | Long | 否 | 发货单模板管理id |
| orderLogisticStdtemplatesId | Long | 否 | 发货单模板id |
| priority | Integer | 否 | 优先级(0-9) |
| expanded | Map | 否 | 动态字段（网点设置信息） |


##### 6.3.2.3 删除接口

uri:*/1.0/online/store/express/merchant/delete/{id}*

请求体：

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| id | Long | 否 | 数据id |


##### 6.3.2.4 查询平台接口

uri:*/1.0/online/store/express/merchant/platform/page*

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` serviceMode ``` | String | 否 | ``` O2O B2C ``` |
| ``` currentPage ``` | Integer | 是 | 页码 |
| pageSize | Integer | 是 | 每页数量 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| id | Long | 平台id |
| platformName | String | 平台名称 |
| platformCode | String | 平台Code |


##### 6.3.2.5 根据平台查询店铺接口

*uri: /1.0/online/store/express/merchant/query/dsOnlineStore*

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| platformId | Long | 否 | 平台id |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| dsOnlineStoreId | Long | 店铺id |
| dsOnlineStoreName | String | 店铺名称 |


##### 6.3.2.6 查询面单模板

返回通用-联单和通用-联单隐私面单

*uri: /1.0/online/store/express/merchant/query***/logisticStdtemplate/**

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| platformId | Long | 否 | 平台id |
| expressMerchantId | Long | 否 | 快递公司id |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| logisticStdtemplatesId | Long | 面单模板id |
| logisticStdtemplatesName | String | 面单模板名称 |
| ``` logisticStdtemplatesUrl ``` | String | ``` 模板url ``` |


##### 6.3.2.7 创建面单模板管理接口

*uri: /1.0/online/store/express/merchant/create***/templateManage/**

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| logisticStdtemplatesId | Long | 是 | 面单模板id：默认为基础模板 |
| platformId | Long | 否 | 平台id |
| expressMerchantId | Long | 否 | 快递公司id |
| standardTemplateName | String | 否 | 模板名称 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| standardTemplateId | Long | 面单模板管理id |


##### 6.3.2.8 创建货单模板管理接口

*uri: /1.0/online/store/express/merchant/create***/orderTemplateManage/**

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| logisticStdtemplatesId | Long | 是 | 面单模板id：默认为基础模板 |
| platformId | Long | 否 | 平台id |
| expressMerchantId | Long | 否 | 快递公司id |
| standardTemplateName | String | 否 | 模板名称 |


响应：

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| orderStandardTemplateId | Long | 面单模板管理id |


### 6.4 WMS作业订单新增下账优化【略】

### 6.5 B2C订单下账失败，增加修改批号【略】

### 6.6 直连电子面单设置

 1.电子面单模板以H5原生代码构建，采用最基础div + css 实现页面面单布局。所有平台、所有快递公司使用同一个基础模板。

 动态内容包含：

 快递公司logo、 打印时间、快递单号条形码、集包地文字、收件人信息、寄件人信息、自定义区域

 所有区域固定宽度和高度。（自定义区域是否固定高度等待确认。）

2. 本地打印程序 增加发货单打印机和面单打印机。（只支持驱动打印）

3.打印程序定时上报打印机状态信息（5min/次）（不足：5分钟刷新一次状态，好处：心云和打印程序可以随意部署，实现简单）

 3.1 本地打印程序启动一个websocket 服务供心云页面直连。需要打印是发送消息检测打印状态。（好处：实时，不足点：登录心云和打印程序必须在同一台电脑，实现更麻烦）

### 6.7 推送打印消息

****

 1 前端B2C 页面选择打印面单或者发货单时判断是否选择了直连订单，如果选择直连订单需要调用接口判断打印机是否连接正确。

 2 调用接口传入需要打印直连面单或者发货单的订单号、打印模板信息，推送websocket 消息至门店打印程序

 2.1 检查模板和订单信息正确性

 2.2 根据模板掩码具体消息

 2.3 发送websocket 消息 ，更新订单打印状态

3 门店打印程序

 3.1 获取打印模板信息（H5文件），定时缓存至本地。

 3.2 生成快递单号条形码图片缓存至本地，替换H5 模板中的图片、订单信息以及快递公司logo图片（使用oss 网络图片）。第三方插件 Zen.Barcode.Rendering

 3.3 将H5 信息转为PDF调用打印机打印。第三方插件 HtmlRenderer.PdfSharp

 3.3 清理本地图片以及pdf 缓存信息

**获取电子面单**

true获取电子面单falseautotoptrue6011

**打印电子面单**

true打印面单信息falseautotoptrue9204

4 接口以及数据推送

 4.1 检查打印机是否就绪

 URL: **1.0/ds/print/statuscheck** 

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| storeCode | String | 否 | 门店编码 |
| merCode | String | 否 | 企业编码 |
| printType | Int | 否 | 1：电子面单 2：发货单 |


**响应：**

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| data | Boolean | true : 打印机在线 false : 打印状态不对 |
| msg | String | 打印机未设置 |


 4.2 推送打印请求

 URL: **1.0/ds/print/logistics**

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| storeCode | String | 否 | 门店编码 |
| merCode | String | 否 | 企业编码 |
| printType | Int | 否 | 1：电子面单 2：发货单 |
| orderList | List<String> | 否 | 订单列表 |


**响应：**

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| data | Boolean | true false |
| msg | String | 订单 213124214 没有设置电子面单模板 |


4.3 拉取电子面单模板

 URL: **1.0/ds/print/logistics/template**

*请求体：*

| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| storeCode | String | 否 | 门店编码 |
| merCode | String | 否 | 企业编码 |
| printType | Int | 否 | 1：电子面单 2：发货单 |


**响应：**

| 字段 | 类型 | 字段描述 |
| --- | --- | --- |
| msg | String | 提示信息 |
| data | List<template> |  |
| template |
| templateId | Int | 模板id |
| express | String | ZTO YT 等字母标识，于心云保持一致 |
| content | String | 模板内容，完整的html 代码 |


4.4 **发送的websocket 消息内容**

{
 "merCode": "500001",
 "storeCode": "A002",
 "deviceId": "",
 "msgId": "89a8167d9e4748bc8423449cb8645ba2",
 "msgType": "print", 
 "msgFromType": "2",
 "cmd": 1,
 "data":
 [
 {
 "printTemplateId": 2278, // 模板id 和下载的模板保持一致
 "printType": 1, //1 系统打印 2 手工补推

 "dataType":"order", // 数据类型 order 来单小票 item 发货单 logistics 物流单
 "orderNo": 1796744922027594752,
 "modifyTime": "",
 "organizationCode": "A002",
 "createTime": "",
 "id": 60026,
 "type": 0,
 "body": "{\"serial\":\"1\",\"thirdPlatformName\":\"饿百\",\"onlineStoreName\":\"test_719095_22253263\",\"thirdOrderNo\":\"4077210146428979844\",\"payType\":\"线上支付\",\"storeAddress\":\"云南省昆明市官渡区昆明市官渡区东郊141号\",\"orderRemark\":\"(缺货时电话与我联系)\",\"commodityAmount\":4.80,\"discountAmount\":0.00,\"postFee\":1,\"packageFee\":0.5,\"created\":1713509824000,\"receiverName\":\"李****\",\"receiverPhone\":\"15624995766,828\",\"receiverBackupPhone\":\"15624995766,828\",\"receiverAddress\":\"北京北京市海淀区上地街道中国电子彩虹集团(东门)102\",\"totalAmount\":6.30,\"servicePhone\":\"\",\"totalCommodityCount\":2,\"merchantActualAmount\":6.04,\"orderDetailList\":[{\"orderNo\":1796744922027594752,\"name\":\"(兴)麝香壮骨膏_7cm*9.5cm*6贴*2袋_安阳中智\",\"spec\":\"7CM*9.5CM*6贴*2袋\",\"manufacture\":\"安阳中智药业有限责任公司\",\"price\":2.00,\"count\":1,\"totalAmount\":2.00,\"storageType\":\"阴凉\",\"barCode\":\"6930691261562\",\"orderPickInfoList\":[{\"id\":\"\",\"orderDetailId\":\"\",\"erpCode\":\"191055\",\"commodityBatchNo\":\"\",\"count\":1,\"purchasePrice\":\"\",\"createTime\":\"\",\"modifyTime\":\"\",\"isValid\":\"\",\"commodityAmount\":2.00}],\"serial\":1},{\"orderNo\":1796744922027594752,\"name\":\"永康医疗 医用棉签 10cm*0.25cm*0.6cm*50支\",\"spec\":\"10CM*50支\",\"manufacture\":\"湖南永康医疗器械有限公司\",\"price\":2.80,\"count\":1,\"totalAmount\":2.80,\"storageType\":\"常温\",\"barCode\":\"6954642000017\",\"orderPickInfoList\":[{\"id\":\"\",\"orderDetailId\":\"\",\"erpCode\":\"133463\",\"commodityBatchNo\":\"\",\"count\":1,\"purchasePrice\":\"\",\"createTime\":\"\",\"modifyTime\":\"\",\"isValid\":\"\",\"commodityAmount\":2.80}],\"serial\":2}],\"platformDiscount\":0.0,\"merchantDisCount\":0,\"commodityDetailDiscount\":0,\"deliveryType\":\"商家自配送\",\"serviceType\":\"立即送达\",\"writeReceiptFlag\":\"不开发票\",\"sameCityShopNo\":\"\"}",
 "orderState": 10,
 "status": 1
 }
 ],
 "sign": "",
 "timestamp": 1713509836346
}

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

约15/人天

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| 新对接快递商 | 邮政创建面单 | hydee-third-inside | 2 | 3h |  |  |  |
| 邮政取消面单 | 2 | 3h |  |  |
| 邮政物流轨迹查询 | 2 | 3h |  |  |
| 邮政接收快递信息 | 2 | 1h |  |  |
| 邮政地址修改接口 | 2 | 2h |  |  |
| 极兔创建面单 | 2 | 3h |  |  |
| 极兔取消面单 | 2 | 3h |  |  |
| 极兔物流轨迹查询 | 2 | 3h |  |  |
| 极兔接收快递信息 | 2 | 1h |  |  |
| 极兔地址修改接口 | 2 | 2h |  |  |
| 初始化邮政和极兔快递枚举和快递code转码数据 | hydee-business-order-web | 2 | 3h |  |  |
| 快递公司管理 | 分页接口 | hydee-business-order-web | 1 | 1h |  |  |  |
| 明细接口 | 1 | 1h |  |  |
| 新增接口 | 1 | 2h |  |  |
| 修改接口 | 1 | 2h |  |  |
| 删除接口 | 1 | 1h |  |  |
| 平台分页查询接口 | 1 | 1h |  |  |
| 查询快递公司接口 | 1 | 1h |  |  |
| 梳理并同步已有的快递公司数据sql | 1 | 3h |  |  |
| 初始化所有快递公司直连开发者信息动态表单数据 | 1 | 4h |  |  |
| 统一图片上传接口 | 1 | 2h |  |  |
| 清洗快递枚举和快递码值映射数据,清除无用数据 | 1 | 4h |  |  |
| 快递公司店铺映射管理 | 分页接口 | 1 | 1h |  |  |  |
| 明细接口 | 1 | 1h |  |  |
| 新增接口 | 1 | 1.5h |  |  |
| 修改接口(中通同步开发者账号信息) | 1 | 1.5h |  |  |
| 删除接口 | 1 | 1h |  |  |
| 查询平台数据接口 | 1 | 1h |  |  |
| 查询店铺数据接口 | 1 | 1h |  |  |
| 查询面单模板接口(联单和通用-联单隐私面单) | 1 | 1h |  |  |
| 创建面单模板管理接口 | 1 | 1h |  |  |
| 创建货单模板管理接口 | 1 | 1h |  |  |
| 初始化联单和通用-联单隐私面单数据 | 1 | 2h |  |  |
| 初始化所有快递公司网点设置动态表单数据 | 1 | 4h |  |  |
| 根据现有面单配置数据初始化快递公司店铺映射数据 | 1 | 4h |  |  |
| 创建面单 | 选择快递策略变化为通过优先级选择快递 | 2 | 3h |  |  |  |
| 直连参数转换 | 2 | 3h |  |  |
| 百世汇通快递，创建面单 | 2 | 1.5h |  |  |
| 菜鸟物流面单，创建面单 | 2 | 1.5h |  |  |
| JD无界物流，创建面单 | 2 | 1.5h |  |  |
| JD快递物流，创建面单 | 2 | 1.5h |  |  |
| 拼多多物流，创建面单 | 2 | 1.5h |  |  |
| 顺丰快递，创建面单 | 2 | 1.5h |  |  |
| 申通快递，创建面单 | 2 | 1.5h |  |  |
| 韵达快递，创建面单 | 2 | 1.5h |  |  |
| 圆通快递，创建面单 | 2 | 1.5h |  |  |
| 中通快递，创建面单 | 2 | 1.5h |  |  |
| 药房网快递，创建面单 | 2 | 1.5h |  |  |
| 打印面单 | 打印程序设置界面改造包含主界面和设置明细界面 | business-order-web printservice | 1 | 4h |  |  |  |
| 打印机状态同步到 心云 | 1 | 3h |  |  |  |
| 面单自定义内容拼接 | 1 | 1d |  |  |  |
| 模板内容替换 | 1 | 4h |  |  |  |
| 模板打印 数据清理 | 1 | 4h |  |  |  |
|  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |


## 九、 上线方案

### 9.1 回滚方案

由于本期需求不涉及对原表数据进行操作，只是新增表，回滚可直接回滚上一个版本服务。