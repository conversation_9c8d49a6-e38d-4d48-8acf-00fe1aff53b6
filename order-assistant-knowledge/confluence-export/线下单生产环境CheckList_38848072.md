# 线下单生产环境CheckList

  15 complete 合并分支到master   34 complete 生产建库、建表. 已在预发完成,预发和生产共用数据库   16 complete 新建生成RocketMQ , 将这个工单复制一份,提到生产 [https://jira.hxyxt.com/browse/DEVOPS-50](https://jira.hxyxt.com/browse/DEVOPS-50); 生产jr: [https://jira.hxyxt.com/browse/DEVOPS-66](https://jira.hxyxt.com/browse/DEVOPS-66)   52 complete 停预发order-sync   56 complete 清理生产库数据(之前预发跑的数据)、清理mongo   17 complete 生产环境Job添加, 从测试环境迁移过来  18 complete mqMsgHandler   19 complete mqMsgDeleteHandler   20 complete autoCreateOfflineOrderTableHandler   21 complete refundOrderFindOrder404Handler   22 complete user404Handler   23 complete migrationHanaDataRefreshProcessHandler   24 complete migrationHanaDataReHandler   54 complete 上线后启动除非迁移的所有任务     25 complete 生产环境配置  42 complete order-servicetrueserver:
  port: 8080
api:
  version: 1.0
  version2: 2.0
compensator:
  enabled: true
spring:
  profiles:
    active: pro
    robot-send: true
  application:
    name: order-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    sentinel:
      filter:
        enabled: true
      web-context-unify: false # 关闭context整合 避免链路失效 默认会由context为根链路
      eager: true #启动立即加载规则，而不是懒加载
      transport:
        dashboard: sentinel.hxyxt.com
        port: 8719 #dashboard通信端口，如果冲突则会自动+1 寻找可用端口
        heartbeat-interval-ms: 5000 #心跳秒数
      datasource:
        flow-rule:
          nacos:
            #流控规则的nacos配置文件 dashbard上报推送的是这个格式
            data-id: ${spring.application.name}-flow-rules.json
            #流控规则格式 XML OR JSON dashbard配置的json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            #规则类型控制规则更新的策略
            rule-type: flow
        param-flow-rule:
          nacos:
            data-id: ${spring.application.name}-param-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: param-flow
        authority-rule:
          nacos:
            data-id: ${spring.application.name}-authority-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: authority
        system-rule:
          nacos:
            data-id: ${spring.application.name}-system-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: system
    nacos:
      discovery:
        server-addr: http://sk-prod-nacos.nacos.cse.com:8848; # pre
        namespace: d04f590f-6926-4e84-becd-a62f369686a2 # pre
        metadata:
          department: NR
        register-enabled: true
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  redis:
    password: ${myEncrypt.des(65d5b0df64714707076972bc2bc1a443216acc162845f052769669c42c20a90e)}
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster:                                                                                # 此处新增
      nodes: redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com.:6379      # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  redisson:
    config: |
      clusterServersConfig:
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500
        failedSlaveReconnectionInterval: 3000
        failedSlaveCheckInterval: 60000
        password: yxt_redis123
        subscriptionsPerConnection: 5
        clientName: null
        loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
        subscriptionConnectionMinimumIdleSize: 1
        subscriptionConnectionPoolSize: 50
        slaveConnectionMinimumIdleSize: 24
        slaveConnectionPoolSize: 64
        masterConnectionMinimumIdleSize: 24
        masterConnectionPoolSize: 64
        readMode: "SLAVE"
        subscriptionMode: "SLAVE"
        nodeAddresses:
          - "redis://"
        scanInterval: 1000
        pingConnectionInterval: 30000
        keepAlive: false
        tcpNoDelay: true
      threads: 16
      nettyThreads: 32
      codec: !<org.redisson.codec.Kryo5Codec> {}
      transportMode: "NIO"      
mq:
  topic:
    # 消费消息
    consumer:
    # 生产消息
    producer:
      # 线下单对账topic 通过不同的tag来区分正单还是退单
      offlineOrderReconciliationTopic: TP_ORDER_OFFLINE_ORDER-RECONCILIATION
      # 科传通过接口同步,转发到MQ,异步处理
      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC


rocketmq:
  producer:
    group: order-service-group
  name-server: ************:8100;************:8100



management:
  health:
    defaults:
      enabled: false # 默认健康检测不进行所有的依赖项健康检测
  endpoints: # 配置暴露和屏蔽的端点
    web:
      exposure:
        include: ["*"] # 暴露所有端点
  endpoint:
    health:
      show-details: "ALWAYS" # 显示所有健康检查信息      
    redis:
      enabled: true #自定义加入redis健康检测列表，如果项目有过于强依赖，redis不可用间接导致应用服务不可用，这个各自根据自己项目申请添加
    db:
      enabled: true


swagger:
  enable: true


feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000


alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015
  sendErrorLog:
    # 322交易组tag
    totag: 322|330
    touser: 1152993
    whitelist: 记录 KC 请求日志错误|||||getStoreOrganizationCodeList] is ignored|||||name for 'gs' operator not found in resources|||||xxl-rpc netty_http client caught exception
    agentid: 1000476
    corpse: _z9y7We6b8PFn1QfUubPxq3eeMhtasU_8WqVZNKuEU8


logging:
  level:
    com.xxl.rpc.remoting.net.impl.netty_http.client.NettyHttpClientHandler: off



# 线程池配置
threadpool:
  asyncCoreSize: 8
  asyncMaxSize: 48
  asyncKeepAliveTime: 0
  asyncCapacity: 128

grey:
  enable: true


   43 complete order-sync-servicetrueserver:
  port: 8080
api:
  version: 1.0
  version2: 2.0
compensator:
  enabled: true
spring:
  profiles:
    active: pro
    robot-send: true
  application:
    name: order-sync-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://sk-prod-nacos.nacos.cse.com:8848;
        namespace: d04f590f-6926-4e84-becd-a62f369686a2
        metadata:
          department: NR
        register-enabled: true
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  redis:
    password: ${myEncrypt.des(65d5b0df64714707076972bc2bc1a443216acc162845f052769669c42c20a90e)}
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster:                                                                                # 此处新增
      nodes: redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com.:6379      # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  redisson:
    config: |
      clusterServersConfig:
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500
        failedSlaveReconnectionInterval: 3000
        failedSlaveCheckInterval: 60000
        password: yxt_redis123
        subscriptionsPerConnection: 5
        clientName: null
        loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
        subscriptionConnectionMinimumIdleSize: 1
        subscriptionConnectionPoolSize: 50
        slaveConnectionMinimumIdleSize: 24
        slaveConnectionPoolSize: 64
        masterConnectionMinimumIdleSize: 24
        masterConnectionPoolSize: 64
        readMode: "SLAVE"
        subscriptionMode: "SLAVE"
        nodeAddresses:
          - "redis://"
        scanInterval: 1000
        pingConnectionInterval: 30000
        keepAlive: false
        tcpNoDelay: true
      threads: 16
      nettyThreads: 32
      codec: !<org.redisson.codec.Kryo5Codec> {}
      transportMode: "NIO"      

mq:
  topic:
    # 消费消息
    consumer:
      # 海典线下单同步
      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD
      # 异常消息消费
      exceptionHandler: TP_ORDER_OFFLINE_EXCEPTION-HANDLE
      # 科传线下单同步topic
      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC
    # 生产消息
    producer:
      # 兜底异常消息生产
      exceptionHandler: TP_ORDER_OFFLINE_EXCEPTION-HANDLE
      # 同步线下单消息到下游
      offlineOrderProducer: TP_ORDER_OFFLINE_ORDER-DATA
      # 同步线下单退单消息到下游
      offlineRefundOrderProducer: TP_ORDER_OFFLINE_REFUND-ORDER-DATA


rocketmq:
  producer:
    group: order-sync-group # 统一使用order-service-group
  name-server: ************:8100;************:8100

xxl:
  job:
    admin:
      addresses: http://xxl-job-xxl-job-admin-service:9080/xxl-job-admin
    executor:
      appname: order-sync-service
      port: 9998
      ip:
      logpath: ${user.dir}/logs
      logretentiondays: -1


management:
  health:
    defaults:
      enabled: false # 默认健康检测不进行所有的依赖项健康检测
  endpoints: # 配置暴露和屏蔽的端点
    web:
      exposure:
        include: ["*"] # 暴露所有端点
  endpoint:
    health:
      show-details: "ALWAYS" # 显示所有健康检查信息      
    redis:
      enabled: true #自定义加入redis健康检测列表，如果项目有过于强依赖，redis不可用间接导致应用服务不可用，这个各自根据自己项目申请添加
    db:
      enabled: true


swagger:
  enable: true


feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000


alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015
  sendErrorLog:
    # 322交易组tag
    totag: 322|330
    touser: 1152993
    whitelist: 记录 KC 请求日志错误|||||getStoreOrganizationCodeList] is ignored|||||name for 'gs' operator not found in resources|||||xxl-rpc netty_http client caught exception
    agentid: 1000476
    corpse: _z9y7We6b8PFn1QfUubPxq3eeMhtasU_8WqVZNKuEU8

logging:
  level:
    com.xxl.rpc.remoting.net.impl.netty_http.client.NettyHttpClientHandler: off



# 开发联调时打开
grey:
  enable: true


   44 complete order-atom-servicetrueserver:
  port: 8080
api:
  version: 1.0
compensator:
  enabled: true
spring:
  data:
    mongodb:
      uri: mongodb://oper_agent:${myEncrypt.des(5552ad0b0baa96a50a2182b9e00d0e12d809e27dbd7f883e)}@10.100.5.99:8635,10.100.5.53:8635/sys_org_operator_log
  profiles:
    active: pro
    robot-send: true
  application:
    name: order-atom-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    sentinel:
      filter:
        enabled: true
      web-context-unify: false # 关闭context整合 避免链路失效 默认会由context为根链路
      eager: true #启动立即加载规则，而不是懒加载
      transport:
        dashboard: sentinel.hxyxt.com
        port: 8719 #dashboard通信端口，如果冲突则会自动+1 寻找可用端口
        heartbeat-interval-ms: 5000 #心跳秒数
      datasource:
        flow-rule:
          nacos:
            #流控规则的nacos配置文件 dashbard上报推送的是这个格式
            data-id: ${spring.application.name}-flow-rules.json
            #流控规则格式 XML OR JSON dashbard配置的json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            #规则类型控制规则更新的策略
            rule-type: flow
        param-flow-rule:
          nacos:
            data-id: ${spring.application.name}-param-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: param-flow
        authority-rule:
          nacos:
            data-id: ${spring.application.name}-authority-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: authority
        system-rule:
          nacos:
            data-id: ${spring.application.name}-system-rules.json
            data-type: json
            namespace: d04f590f-6926-4e84-becd-a62f369686a2
            group-id: SENTINEL_GROUP
            server-addr: sk-prod-nacos.nacos.cse.com:8848
            rule-type: system
    nacos:
      discovery:
        server-addr: http://sk-prod-nacos.nacos.cse.com:8848;
        namespace: d04f590f-6926-4e84-becd-a62f369686a2
        metadata:
          department: NR
        register-enabled: true
  shardingsphere:
    datasource:
      names: order-offline
      order-offline:
        url: ********************************************************************************************************************************************************  #开发
        username: dscloud_offline_agent
        password: ${myEncrypt.des(940315444486df8db30f55e95528bb136501fa6db98f21cd)}
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        druid:
          initial-size: 40
          min-idle: 40
          max-active: 100
          max-wait: 10000
          wall:
            multi-statement-allow: true
    sharding:
      tables:
        offline_order:
          actual-data-nodes: order-offline.offline_order_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_cashier_desk:
          actual-data-nodes: order-offline.offline_order_cashier_desk_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail:
          actual-data-nodes: order-offline.offline_order_detail_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_coupon:
          actual-data-nodes: order-offline.offline_order_detail_coupon_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_pick:
          actual-data-nodes: order-offline.offline_order_detail_pick_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_promotion:
          actual-data-nodes: order-offline.offline_order_detail_promotion_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_organization:
          actual-data-nodes: order-offline.offline_order_organization_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_pay:
          actual-data-nodes: order-offline.offline_order_pay_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_prescription:
          actual-data-nodes: order-offline.offline_order_prescription_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_user:
          actual-data-nodes: order-offline.offline_order_user_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order:
          actual-data-nodes: order-offline.offline_refund_order_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_detail:
          actual-data-nodes: order-offline.offline_refund_order_detail_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_pay:
          actual-data-nodes: order-offline.offline_refund_order_pay_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_med_ins_settle:
          actual-data-nodes: order-offline.offline_order_med_ins_settle_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_med_ins_settle:
          actual-data-nodes: order-offline.offline_refund_order_med_ins_settle_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_user:
          actual-data-nodes: order-offline.offline_refund_order_user_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
    props:
      sql.show: false #是否开启SQL显示，默认值: false
      # executor.size: #工作线程数量，默认值: CPU核数
      max.connections.size.per.query: 10 # 每个查询可以打开的最大连接数量,默认为1
      check.table.metadata.enabled: false #是否在启动时检查分表元数据一致性，默认值: false

  datasource:
    dynamic:
      primary: ordermaster
      strict: false
      datasource:
        order_offline:
          url: *******************************************************************************************************************************************************  #开发
          username: dscloud_offline_agent
          password: ${myEncrypt.des(940315444486df8db30f55e95528bb136501fa6db98f21cd)}
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 40
            min-idle: 40
            max-active: 100
            max-wait: 10000
            wall:
              multi-statement-allow: true
        order_offline_archive:
          url: ***************************************************************************************************************************************************************  #开发
          username: dscloud_offline_archive_agent
          password: ${myEncrypt.des(a88753f3e95a34d4b321b70abe64f582ca73ed8d2e5bcdb3)}
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 40
            min-idle: 40
            max-active: 100
            max-wait: 10000
            wall:
              multi-statement-allow: true
        ordermaster:
          url: ***********************************************************************************************************************************************  #开发
          username: dscloud_agent
          password: ${myEncrypt.des(2163fa9e48d35d815c81b6afa23d4ee0d809e27dbd7f883e)}
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 40
            min-idle: 40
            max-active: 100
            max-wait: 10000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL  
            wall:
                multi-statement-allow: true
    druid:
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  redis:
    password: ${myEncrypt.des(65d5b0df64714707076972bc2bc1a443216acc162845f052769669c42c20a90e)}
    lettuce:
      pool:
        max-idle: 200
        min-idle: 50
        max-active: 5000
        max-wait: 1000
    timeout: 2000
    cluster:                                                                                # 此处新增
      nodes: redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com.:6379      # 此处新增，集群地址（此处为测试环境地址）
      max-redirects: 3
  redisson:
    config: |
      clusterServersConfig:
        idleConnectionTimeout: 10000
        connectTimeout: 10000
        timeout: 3000
        retryAttempts: 3
        retryInterval: 1500
        failedSlaveReconnectionInterval: 3000
        failedSlaveCheckInterval: 60000
        password: yxt_redis123
        subscriptionsPerConnection: 5
        clientName: null
        loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
        subscriptionConnectionMinimumIdleSize: 1
        subscriptionConnectionPoolSize: 50
        slaveConnectionMinimumIdleSize: 24
        slaveConnectionPoolSize: 64
        masterConnectionMinimumIdleSize: 24
        masterConnectionPoolSize: 64
        readMode: "SLAVE"
        subscriptionMode: "SLAVE"
        nodeAddresses:
          - "redis://"
        scanInterval: 1000
        pingConnectionInterval: 30000
        keepAlive: false
        tcpNoDelay: true
      threads: 16
      nettyThreads: 32
      codec: !<org.redisson.codec.Kryo5Codec> {}
      transportMode: "NIO"      


mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-value: 0
      logic-not-delete-value: 1
      update-strategy: not_null



management:
  health:
    defaults:
      enabled: false # 默认健康检测不进行所有的依赖项健康检测
  endpoints: # 配置暴露和屏蔽的端点
    web:
      exposure:
        include: ["*"] # 暴露所有端点
  endpoint:
    health:
      show-details: "ALWAYS" # 显示所有健康检查信息      
    redis:
      enabled: true #自定义加入redis健康检测列表，如果项目有过于强依赖，redis不可用间接导致应用服务不可用，这个各自根据自己项目申请添加
    db:
      enabled: true


swagger:
  enable: true

feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000




alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015
  sendErrorLog:
    # 322交易组tag
    totag: 322|330
    touser: 1152993
    whitelist: 记录 KC 请求日志错误|||||getStoreOrganizationCodeList] is ignored|||||name for 'gs' operator not found in resources|||||xxl-rpc netty_http client caught exception
    agentid: 1000476
    corpse: _z9y7We6b8PFn1QfUubPxq3eeMhtasU_8WqVZNKuEU8



xxl:
  job:
    admin:
      addresses: http://xxl-job-xxl-job-admin-service:9080/xxl-job-admin
    executor:
      appname: order-atom-service
      port: 9999
      ip:
      logpath: ${user.dir}/logs
      logretentiondays: -1

#logging:
#  level:
#    com.xxl.rpc.remoting.net.impl.netty_http.client.NettyHttpClientHandler: off
# logging:
#  level:
#     root: info

# grey:
#   enable: false
#logging:
#  level:
#    root: info

migration-config:
  schema-list:
    - schema: YNHX_DATA01
      enable: true
      start-time: '2024-07-27 00:00:00'
      end-time: '2024-07-31 23:23:00'
    - schema: GXHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: GZHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SCHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SXHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: CQHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: CDHX_USERS
      enable: false
      start-time: '2024-06-22 00:00:00'
      end-time: '2024-06-24 00:00:00'
    - schema: SHHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: TJHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HNHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HENHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: SXGSHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: TJQCHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: HENNYHX_DATA01
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
    - schema: ZYHX_USERS
      enable: false
      start-time: '2024-06-21 14:30:00'
      end-time: '2024-07-21 14:30:00'
      
migration-third-platform-config:
  item-list:
    # 攀枝花 3月4日之后
    - schema: SCHX_USERS
      specific-date: '2024-03-05 00:00:00'
    # 重庆 3月4日之后
    - schema: CQHX_USERS
      specific-date: '2024-03-05 00:00:00'
    # 四川 6月30日之后
    - schema: CDHX_USERS
      specific-date: '2024-07-01 00:00:00'
      store-codes: 'H382,H383,H384,H385,H386,H387,H388,H389,H390,H391,H392,H393,H394,H395,H696,H720,H724,H732,H735,H802,H871,H999,HA00,HA30,HA31,HA32,HA33,HA34,HA35,HA36,HA37,HA38,HA39,HA40,HA41,HA42,HA43,HA44,HA45,HA46,HA47,HA48,HA49,HB04,HB05,HC36,HC59,HF16,HF19,HF74,HF82,HG21,HG74,HG75,HH78,HH86,HH96,HI11,HI25,HI36,HI47,HI51,HI55,HI68,HI79,HX01,HX02,HX03,HX04,HX05,HX06,HX07,HX08,HX09,HX10,HX11,HX12,HX13,HX14,HX15,HX16,HX17,HX18,HX19,HX20,HX21,HX22,HX23,HX24,HX25,HX26,HX27,HX28,HX29,HX30,HX31,HX32,HX33'


rocketmq:
  producer:
    group: order-atom-group
  name-server: ************:8100;************:8100

mq:
  topic:
    producer:
      migrationHanaData: TP_ORDER_ATOM_MIGRATION-HANA
      migrationHanaDataReHandle: TP_ORDER_ATOM_MIGRATION-RE-HANDLE-HANA
      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD
      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC


# 非会员表起始时间,按照该时间,进行遍历非会员表 该配置任务已经注销
# nonVipShardingStartDate: 2024-08-08 00:00:01

# 告警配置
offlineAlarmWebhook: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2208c6fc-b339-4d6b-9d6f-9b636abc7a39'
autoCreateTableNotifyWebhook: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5866035b-92a2-430b-95fc-5d962e113ecb'
     35 complete 部署生产  60 complete order-atom-service   61 complete order-service   62 complete order-sync-service     36 complete 配置科传  37 complete api-gateway-hades        - id: order-service-offlineOrder
          uri: lb://order-service
          predicates:
            - Path=/order-aggregate/offlineOrder/**
          filters:
            - AppKey
            - StripPrefix=1     
   38 complete 配置完重启     47 complete mongo加索引  50 complete 
truedb.offline_staging_order.createIndex({ 
	numberType: 1,
	stagingType:1,
	storeCode:1,
	thirdPlatformCode:1,
	data:1,
});

db.offline_staging_order.createIndex({ 
	stagingType:1
});
   64 complete 迁移脚本上线时执行
备注: 该项已经维度到消费记录的需求上了，所以标记为完成truedb.sync_stat.createIndex({ taskKey: 1 });

db.sync_stat_error.createIndex({ result: 1,createTime:1 });

db.migration_delay_parent_order_mapping.createIndex({ 
	parentThirdOrderNo:1,
	thirdPlatformCode:1,
	storeCode:1
});