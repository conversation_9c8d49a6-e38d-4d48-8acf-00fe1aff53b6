# 2025-03-04 checklist 订单一致性保障服务

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| B2C 转正常单BUG | hydee-business-order-web |  |  |  |  |  |  |  |  |  |
| 0原单风控 | hydee-middle-order |  |  |  |  |  |  |  |  |  |
| jddj小时达账单拉取问题 | third-platform-other |  |  |  |  |  |  |  |  |  |
| 订单一致性服务 | third-platform-order-other |  |  |  |  |  |  |  |  |  |
| 订单一致性服务 | third-platform-order-mt |  |  |  |  |  |  |  |  |  |
| 订单一致性服务 | third-platform-order-elm |  |  |  |  |  |  |  |  |  |
| 订单一致性服务 | third-platform-order-jddj |  |  |  |  |  |  |  |  |  |
| ``` 订单一致性服务 ``` | ``` order-consistent-center ``` |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| dsclod | CREATE TABLE `order_difference` (  `id` bigint NOT NULL AUTO_INCREMENT,  `difference_type` tinyint NOT NULL COMMENT '差异类型 0订单状态差异 1订单数据丢失或缺失的差异',  `order_no` bigint DEFAULT NULL COMMENT '订单号，雪花算法',  `order_state` tinyint DEFAULT NULL COMMENT '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编码',  `third_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单号',  `third_order_state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单状态',  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编码',  `client_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网店编码',  `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '下单线上门店编码',  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '末次修改时间',  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统备注',  `data_version` bigint NOT NULL DEFAULT '0' COMMENT '数据版本,update订单信息默认+1',  `is_platform_exists` tinyint NOT NULL COMMENT '平台是否存在 (0：不存在；1：存在)',  `is_xy_exists` tinyint NOT NULL COMMENT '心云是否存在 (0：不存在；1：存在)',  `is_handler` tinyint NOT NULL COMMENT '是否需要处理 (0：不需要；1：需要)',  PRIMARY KEY (`id`) USING BTREE,  KEY `idx_difference_type` (`difference_type`) USING BTREE,  KEY `idx_third_platform_code` (`third_platform_code`) USING BTREE,  KEY `idx_create_time` (`create_time`) USING BTREE,  KEY `idx_online_store_code` (`online_store_code`) USING BTREE,  KEY `idx_is_platform_exists` (`is_platform_exists`) USING BTREE,  KEY `idx_is_xy_exists` (`is_xy_exists`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=13238 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单差异信息表'; | 已完成 |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| ``` 心云订单和平台的订单进行对比 ``` |  | ``` consistentOrderJobconsistentOrderJobProcessHandler ``` |  |  |  |  |
| ``` 定时任务处理差异表数据 主要处理订单不存在的差异 ``` |  | ``` orderDifferenceJobProcessHandler ``` |  |  |  |  |
| ``` 定时任务晚上2点执行抓取前一天心云订单和平台的订单进行对比  对比订单状态 ``` |  | ``` consistentOrderStateJobProcessHandler ``` |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |