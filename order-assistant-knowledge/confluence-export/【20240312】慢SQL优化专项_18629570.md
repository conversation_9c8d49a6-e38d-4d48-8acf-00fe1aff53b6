# 【20240312】慢SQL优化专项

# 一、背景

## 1.1 业务背景

## 1.2 痛点分析

## 1.3 系统现状

# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

### 4.2.1 门店信息变更MQ监听

### 4.2.2 POS配置流程

#### 4.2.2.1 xxx

#### 4.2.2.2 xxx

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
|  |  |  |  |


## 5.3 接口设计

### 5.3.1 退款单下账列表接口

1. 数量查询接口
  1. 原SQL**：**
    1. SELECT COUNT(1) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = '500001' AND ro.state = 100 AND ro.erp_state >= 20 AND ro.erp_state <= 100 AND oinfo.service_mode = 'O2O' AND oinfo.organization_code IN ('B244');
    2. 原sql执行时间基本处于1.15s
    3. 原SQL的执行计划：
    4. 
  2. SELECT COUNT(1) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = '500001' AND ro.state = 100 AND ro.erp_state >= 20 AND ro.erp_state <= 100 AND oinfo.service_mode = 'O2O' AND oinfo.organization_code IN ('B244');
  3. 原sql执行时间基本处于1.15s
  4. 原SQL的执行计划：
  5. 
  6. 优化sql：
    1. SELECT COUNT(1)
FROM refund_order ro
         INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no
WHERE ro.mer_code = '500001'
  AND ro.state = 100
  AND ro.erp_state >= 20
  AND ro.erp_state <= 100
  AND oinfo.service_mode = 'O2O'
  AND oinfo.mer_code = '500001'
  AND oinfo.organization_code IN ('B244');
    2. 优化之后的sql执行计划：
    3. 
    4. 优化后的耗时在 170ms左右
      1. 
    5. 
    1. 
  7. SELECT COUNT(1)
FROM refund_order ro
         INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no
WHERE ro.mer_code = '500001'
  AND ro.state = 100
  AND ro.erp_state >= 20
  AND ro.erp_state <= 100
  AND oinfo.service_mode = 'O2O'
  AND oinfo.mer_code = '500001'
  AND oinfo.organization_code IN ('B244');
  8. 优化之后的sql执行计划：
  9. 
  10. 优化后的耗时在 170ms左右
    1. 
  11. 
  12. 原因：没有利用到order_info的联合索引
  1. SELECT COUNT(1) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = '500001' AND ro.state = 100 AND ro.erp_state >= 20 AND ro.erp_state <= 100 AND oinfo.service_mode = 'O2O' AND oinfo.organization_code IN ('B244');
  2. 原sql执行时间基本处于1.15s
  3. 原SQL的执行计划：
  4. 
  1. SELECT COUNT(1)
FROM refund_order ro
         INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no
WHERE ro.mer_code = '500001'
  AND ro.state = 100
  AND ro.erp_state >= 20
  AND ro.erp_state <= 100
  AND oinfo.service_mode = 'O2O'
  AND oinfo.mer_code = '500001'
  AND oinfo.organization_code IN ('B244');
  2. 优化之后的sql执行计划：
  3. 
  4. 优化后的耗时在 170ms左右
    1. 
  5. 
  1. 
2. 原SQL**：**
  1. SELECT COUNT(1) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = '500001' AND ro.state = 100 AND ro.erp_state >= 20 AND ro.erp_state <= 100 AND oinfo.service_mode = 'O2O' AND oinfo.organization_code IN ('B244');
  2. 原sql执行时间基本处于1.15s
  3. 原SQL的执行计划：
  4. 
3. SELECT COUNT(1) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = '500001' AND ro.state = 100 AND ro.erp_state >= 20 AND ro.erp_state <= 100 AND oinfo.service_mode = 'O2O' AND oinfo.organization_code IN ('B244');
4. 原sql执行时间基本处于1.15s
5. 原SQL的执行计划：
6. 
7. 优化sql：
  1. SELECT COUNT(1)
FROM refund_order ro
         INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no
WHERE ro.mer_code = '500001'
  AND ro.state = 100
  AND ro.erp_state >= 20
  AND ro.erp_state <= 100
  AND oinfo.service_mode = 'O2O'
  AND oinfo.mer_code = '500001'
  AND oinfo.organization_code IN ('B244');
  2. 优化之后的sql执行计划：
  3. 
  4. 优化后的耗时在 170ms左右
    1. 
  5. 
  1. 
8. SELECT COUNT(1)
FROM refund_order ro
         INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no
WHERE ro.mer_code = '500001'
  AND ro.state = 100
  AND ro.erp_state >= 20
  AND ro.erp_state <= 100
  AND oinfo.service_mode = 'O2O'
  AND oinfo.mer_code = '500001'
  AND oinfo.organization_code IN ('B244');
9. 优化之后的sql执行计划：
10. 
11. 优化后的耗时在 170ms左右
  1. 
12. 
13. 原因：没有利用到order_info的联合索引
14. 列表接口
  1. 原sql
    1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
    2. 原sql耗时平均1.1s左右
    3. 原sql的执行计划：
      1. 
    4. 
    1. 
  2. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  3. 原sql耗时平均1.1s左右
  4. 原sql的执行计划：
    1. 
  5. 
  6. 优化后sql：
    1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and oinfo.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
    2. 优化后sql执行计划：
      1. 
    3. 
    4. 优化后的执行时间：120ms左右
      1. 
    5. 
    1. 
    1. 
  7. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and oinfo.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  8. 优化后sql执行计划：
    1. 
  9. 
  10. 优化后的执行时间：120ms左右
    1. 
  11. 
  12. 原因：没有利用到order_info表的联合索引
  1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  2. 原sql耗时平均1.1s左右
  3. 原sql的执行计划：
    1. 
  4. 
  1. 
  1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and oinfo.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  2. 优化后sql执行计划：
    1. 
  3. 
  4. 优化后的执行时间：120ms左右
    1. 
  5. 
  1. 
  1. 
15. 原sql
  1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  2. 原sql耗时平均1.1s左右
  3. 原sql的执行计划：
    1. 
  4. 
  1. 
16. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
17. 原sql耗时平均1.1s左右
18. 原sql的执行计划：
  1. 
19. 
20. 优化后sql：
  1. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and oinfo.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
  2. 优化后sql执行计划：
    1. 
  3. 
  4. 优化后的执行时间：120ms左右
    1. 
  5. 
  1. 
  1. 
21. select ro.*,
       oinfo.order_state,
       oinfo.online_store_code,
       oinfo.online_store_name,
       oinfo.organization_code,
       oinfo.organization_name,
       oinfo.day_num
from refund_order ro
         inner join order_info oinfo
                    on ro.order_no = oinfo.order_no
where ro.mer_code = '500001'
  and oinfo.mer_code = '500001'
  and ro.state = 100
  and ro.erp_state >= 20
  and ro.erp_state <= 100
  and oinfo.service_mode = 'O2O'
  and oinfo.organization_code in
      (
          'B898'
          )
order by ro.id desc
LIMIT 0,20;
22. 优化后sql执行计划：
  1. 
23. 
24. 优化后的执行时间：120ms左右
  1. 
25. 
26. 原因：没有利用到order_info表的联合索引


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 开发时间 | 负责人 | 进度 |
| --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等