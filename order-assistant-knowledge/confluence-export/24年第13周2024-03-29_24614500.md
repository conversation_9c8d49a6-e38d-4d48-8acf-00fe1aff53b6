# 24年第13周2024-03-29

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构-DDD项目 |  |  | 下周二技术方案评审 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  |  | 周三开了一个头，正在按业务梳理分批次开发的功能。 | .net代码黑盒,无法多人同时开发. 按平台迁移对接代码 |
| 3 | 优雅发布升级-mq |  |  | 已完成,待上线 | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  |  |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | [https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq)  上线计划 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 | 郭志明 | **本周总工时：5day**1. 子公司支付配置测试配合 2. 订单导出增加心币兑换订单标识 | **㊀计划工作****㊁实际完成**1. 子公司支付配置测试配合（测试中） 2. 订单导出增加心币兑换订单标识（待上线） **㊂遗留问题** **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 |  | **本周总工时：4.5day**1.内购订单下账失败问题2.下账时机修改生成运费单bug修复3.积分兑换商城历史遗留问题4.线上运维5.v1.6.2.订单运维工具 | **㊀计划工作**1.内购订单下账失败问题2.下账时机修改生成运费单bug修复3.积分兑换商城历史遗留问题4.线上运维5.v1.6.2.订单运维工具 技术方案**㊁实际完成**1.内购订单下账失败问题2.下账时机修改生成运费单bug修复3.积分兑换商城历史遗留问题4.线上运维**㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. v1.6.2.订单运维工具 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 |  | **本周总工时：5pd**1. rocket-mq优雅消费处理 1d 2. 网关问题处理 2d   1. businesses-gateway业务网关引用计数异常排查，处理   2. api网关线上问题处理 3. businesses-gateway业务网关引用计数异常排查，处理 4. api网关线上问题处理 5. 线下单开发,待对接数据；科传线下单接口文档调整; 1d 6. 日常事务处理。会议、es超时时间设置、springboot actuator安全问题(运维统一处理)、线上问题排查、面试等 1d  **** | **㊀计划工作** **㊁实际完成** **㊂遗留问题**   **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 4 |  | **本周总工时：****** 1. 订单路由- 规则校验 2. 线上运维 | **㊀计划工作** 1. 订单路由需求开发**㊁实际完成** 1. 订单路由 -规则校验 90% 剩余主要为请求平台查询配送信息及商品中台还未提供批量库存查询接口 **㊂遗留问题****㊃风险问题****1. 海典调拨单无进展 已和凯哥沟通，先开发正常流程，该部分由他推动海典处理****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 5 |  | **本周总工时：5pd****1.订单路由-场景配置****2.订单路由-策略配置****3.部分接口联调** | **㊀计划工作**1.订单路由场景和策略需求开发2.已有接口联调**㊁实际完成**1.订单路由场景和策略需求开发2.已有接口联调**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：** | **㊀计划工作****㊁实际完成****㊂遗留问题** **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 订单路由需求开发 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 7 |  | **本周总工时：5day**1.下账时机修改生成运费单bug修复2.线上运维3.v1.6.2.订单运维工具4.微商城B2C订单库存占用释放问题 | **㊀计划工作**1.下账时机修改生成运费单bug修复2.线上运维3.v1.6.2.订单运维工具4.微商城B2C订单库存占用释放问题**㊁实际完成**1.下账时机修改生成运费单bug修复2.线上运维4.微商城B2C订单库存占用释放问题**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1.v1.6.2.订单运维工具**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 8 |  | **本周总工时：5 day**1. 处方单审核需求改造测试 1.5day2 饿了么自配送配送链路补全 编码和发布 1.5 day3 美团商品变更成产异常消费事故追踪 1 day4 .NET MQ 消费调整 0.5 day5 诺和优惠券 帮助测试走流程 0.5 day | **㊀计划工作****㊁实际完成****㊂遗留问题**5 诺和测试由于营销组对优惠券改版 影响了我们测试，目前处于阻断阶段**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx  **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

1.分支管理 删除多余分支.
2.沟通表达 - 接着日常发现的问题发现. 结论先行. 抓重点反馈, 带着问题的答案选项 去提供反馈
3.重构思路转变: 中台+DDD+可插拔方案.

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |