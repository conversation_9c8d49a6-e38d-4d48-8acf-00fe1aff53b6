# 【20250228】归档库订单迁移-容量评估

使用AI进行评估

### offline_order_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 64 + 2字节（长度前缀）= 66字节
  - 其他VARCHAR字段：约 `32+32+32+32+255+32+8+8+8+8+9+9+1024+64+64+8+8+8+64+32+6+6+255` ≈ **1,750字节**
  - 总计：≈ **1,800字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 64 + 2字节（长度前缀）= 66字节
- 其他VARCHAR字段：约 `32+32+32+32+255+32+8+8+8+8+9+9+1024+64+64+8+8+8+64+32+6+6+255` ≈ **1,750字节**
- 总计：≈ **1,800字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 唯一索引 (`uk_order_no`): 66字节
  - 其他索引：约 200字节（组合索引字段长度）
  - 索引总开销：≈ **300字节**
- 主键 (`id`): 8字节
- 唯一索引 (`uk_order_no`): 66字节
- 其他索引：约 200字节（组合索引字段长度）
- 索引总开销：≈ **300字节**
- **单条总占用**：≈ **2.1 KB**


---

### 2. offline_order_cashier_desk_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他VARCHAR字段：约 `255+128+128+128+128+128+8+8` ≈ **900字节**
  - 总计：≈ **1,000字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他VARCHAR字段：约 `255+128+128+128+128+128+8+8` ≈ **900字节**
- 总计：≈ **1,000字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 二级索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 二级索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **1.1 KB**


---

### 3. offline_order_coupon_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `128+9+32+128+128+128+32+9+64+64+8+8+8+text` ≈ **800字节**
  - 总计：≈ **900字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `128+9+32+128+128+128+32+9+64+64+8+8+8+text` ≈ **800字节**
- 总计：≈ **900字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no_erp_code`): 66 + 128 ≈ **200字节**
  - 索引总开销：≈ **250字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no_erp_code`): 66 + 128 ≈ **200字节**
- 索引总开销：≈ **250字节**
- **单条总占用**：≈ **1.2 KB**


---

### 4. offline_order_detail_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `64+64+128+128+128+9+32+32+9+9+9+9+9+64+64+8+8+8+6+6+255+255+64+64+255+20+20` ≈ **1,500字节**
  - 总计：≈ **1,600字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `64+64+128+128+128+9+32+32+9+9+9+9+9+64+64+8+8+8+6+6+255+255+64+64+255+20+20` ≈ **1,500字节**
- 总计：≈ **1,600字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 唯一索引 (`uk_order_no_order_detail_no`): 66 + 64 ≈ **130字节**
  - 其他索引：约 **200字节**
  - 索引总开销：≈ **350字节**
- 主键 (`id`): 8字节
- 唯一索引 (`uk_order_no_order_detail_no`): 66 + 64 ≈ **130字节**
- 其他索引：约 **200字节**
- 索引总开销：≈ **350字节**
- **单条总占用**：≈ **1.9 KB**


---

### 5. offline_order_detail_pick_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `64+128+128+9+64+64+8+8+8` ≈ **400字节**
  - 总计：≈ **500字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `64+128+128+9+64+64+8+8+8` ≈ **400字节**
- 总计：≈ **500字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no_detail_no`): 66 + 64 ≈ **130字节**
  - 索引总开销：≈ **150字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no_detail_no`): 66 + 64 ≈ **130字节**
- 索引总开销：≈ **150字节**
- **单条总占用**：≈ **0.6 KB**


---

### 6. offline_order_med_ins_settle_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `50+50+50+100+100+50+50+50+50+50+9+9+9+50+50+50+10+20+8+50+50+50+50+8` ≈ **800字节**
  - 总计：≈ **900字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `50+50+50+100+100+50+50+50+50+50+9+9+9+50+50+50+10+20+8+50+50+50+50+8` ≈ **800字节**
- 总计：≈ **900字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **1.0 KB**


---

### 7. offline_order_organization_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `32+128+32+128+32+64+64+8+8` ≈ **500字节**
  - 总计：≈ **600字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `32+128+32+128+32+64+64+8+8` ≈ **500字节**
- 总计：≈ **600字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.7 KB**


---

### 8. offline_order_pay_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `255+255+9+64+64+8+8+8` ≈ **600字节**
  - 总计：≈ **700字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `255+255+9+64+64+8+8+8` ≈ **600字节**
- 总计：≈ **700字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.8 KB**


---

### 9. offline_order_prescription_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `32+64+64+64+8+8` ≈ **240字节**
  - 总计：≈ **300字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `32+64+64+64+8+8` ≈ **240字节**
- 总计：≈ **300字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.4 KB**


---

### 10. offline_order_promotion_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `128+9+32+128+128+32+9+64+64+8+8+8+text` ≈ **600字节**
  - 总计：≈ **700字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `128+9+32+128+128+32+9+64+64+8+8+8+text` ≈ **600字节**
- 总计：≈ **700字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no_erp_code`): 66 + 128 ≈ **200字节**
  - 索引总开销：≈ **250字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no_erp_code`): 66 + 128 ≈ **200字节**
- 索引总开销：≈ **250字节**
- **单条总占用**：≈ **0.9 KB**


---

### 11. offline_order_user_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `32+64+64+32+64+64+8+8` ≈ **300字节**
  - 总计：≈ **400字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `32+64+64+32+64+64+8+8` ≈ **300字节**
- 总计：≈ **400字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_order_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_order_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.5 KB**


---

### 12. offline_refund_order_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `255+64+32+32+255+32+32+32+512+8+8+8+8+9+9+9+64+64+8+8+8+6+6+255` ≈ **1,800字节**
  - 总计：≈ **1,900字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `255+64+32+32+255+32+32+32+512+8+8+8+8+9+9+9+64+64+8+8+8+6+6+255` ≈ **1,800字节**
- 总计：≈ **1,900字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 唯一索引 (`uk_refund_no`): 255字节
  - 其他索引：≈ **300字节**
  - 索引总开销：≈ **600字节**
- 主键 (`id`): 8字节
- 唯一索引 (`uk_refund_no`): 255字节
- 其他索引：≈ **300字节**
- 索引总开销：≈ **600字节**
- **单条总占用**：≈ **2.4 KB**


---

### 13. offline_refund_order_cashier_desk_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `refund_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `255+128+128+128+128+128+8+8` ≈ **900字节**
  - 总计：≈ **1,000字节**
- `id` (BIGINT): 8字节
- `refund_no` (VARCHAR(64)): 66字节
- 其他字段：约 `255+128+128+128+128+128+8+8` ≈ **900字节**
- 总计：≈ **1,000字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_refund_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_refund_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **1.1 KB**


---

### 14. offline_refund_order_detail_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `order_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `255+255+64+128+128+9+32+32+9+9+9+9+9+64+64+8+8+8+6+6+64+64+255+255+255+20+20` ≈ **1,800字节**
  - 总计：≈ **1,900字节**
- `id` (BIGINT): 8字节
- `order_no` (VARCHAR(64)): 66字节
- 其他字段：约 `255+255+64+128+128+9+32+32+9+9+9+9+9+64+64+8+8+8+6+6+64+64+255+255+255+20+20` ≈ **1,800字节**
- 总计：≈ **1,900字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 唯一索引 (`uk_refund_no_refund_detail_no`): 255 + 255 ≈ **510字节**
  - 其他索引：≈ **300字节**
  - 索引总开销：≈ **850字节**
- 主键 (`id`): 8字节
- 唯一索引 (`uk_refund_no_refund_detail_no`): 255 + 255 ≈ **510字节**
- 其他索引：≈ **300字节**
- 索引总开销：≈ **850字节**
- **单条总占用**：≈ **2.7 KB**


---

### 15. offline_refund_order_med_ins_settle_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `refund_no` (VARCHAR(255)): 255 + 2 = 257字节
  - 其他字段：约 `50+50+50+100+100+50+50+50+50+50+9+9+9+50+50+50+10+20+8+50+50+50+50+8` ≈ **800字节**
  - 总计：≈ **1,100字节**
- `id` (BIGINT): 8字节
- `refund_no` (VARCHAR(255)): 255 + 2 = 257字节
- 其他字段：约 `50+50+50+100+100+50+50+50+50+50+9+9+9+50+50+50+10+20+8+50+50+50+50+8` ≈ **800字节**
- 总计：≈ **1,100字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_refund_no`): 257字节
  - 索引总开销：≈ **300字节**
- 主键 (`id`): 8字节
- 索引 (`idx_refund_no`): 257字节
- 索引总开销：≈ **300字节**
- **单条总占用**：≈ **1.4 KB**


---

### 16. offline_refund_order_organization_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `refund_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `32+128+32+128+32+64+64+8+8` ≈ **500字节**
  - 总计：≈ **600字节**
- `id` (BIGINT): 8字节
- `refund_no` (VARCHAR(64)): 66字节
- 其他字段：约 `32+128+32+128+32+64+64+8+8` ≈ **500字节**
- 总计：≈ **600字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_refund_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_refund_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.7 KB**


---

### 17. offline_refund_order_pay_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `refund_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `255+255+9+64+64+8+8` ≈ **600字节**
  - 总计：≈ **700字节**
- `id` (BIGINT): 8字节
- `refund_no` (VARCHAR(64)): 66字节
- 其他字段：约 `255+255+9+64+64+8+8` ≈ **600字节**
- 总计：≈ **700字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_refund_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_refund_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.8 KB**


---

### 18. offline_refund_order_user_2503

- **字段类型与行大小估算**：
  - `id` (BIGINT): 8字节
  - `refund_no` (VARCHAR(64)): 66字节
  - 其他字段：约 `32+64+64+32+64+64+8+8` ≈ **300字节**
  - 总计：≈ **400字节**
- `id` (BIGINT): 8字节
- `refund_no` (VARCHAR(64)): 66字节
- 其他字段：约 `32+64+64+32+64+64+8+8` ≈ **300字节**
- 总计：≈ **400字节**
- **索引开销**：
  - 主键 (`id`): 8字节
  - 索引 (`idx_refund_no`): 66字节
  - 索引总开销：≈ **100字节**
- 主键 (`id`): 8字节
- 索引 (`idx_refund_no`): 66字节
- 索引总开销：≈ **100字节**
- **单条总占用**：≈ **0.5 KB**


---

### 总占用空间估算

将每个表的单条数据占用相加：

复制

```
2.1 + 1.1 + 1.2 + 1.9 + 0.6 + 1.0 + 0.7 + 0.8 + 0.4 + 0.9 + 0.5 + 2.4 + 1.1 + 2.7 + 1.4 + 0.7 + 0.8 + 0.5 ≈ **19.7 KB**
```

---

### 结论

每个表插入一条数据后，**总占用空间约为 20 KB**。
实际占用可能因数据内容、索引填充率、存储引擎优化等略有差异，但可作为粗略参考。