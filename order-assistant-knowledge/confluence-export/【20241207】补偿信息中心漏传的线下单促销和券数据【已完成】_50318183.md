# 【20241207】补偿信息中心漏传的线下单促销和券数据【已完成】

**一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3808**

**服务:**

order-atom-service

order-sync-service

**分支:**

fix-hd-coupon-promotion-data-20241206



**SDK:**

  12 complete release  

<dependency>
   <groupId>com.yxt.order.atom.sdk</groupId>
   <artifactId>order-atom-sdk</artifactId>
   <version>compensateHdMissPromotionCoupon-SNAPSHOT</version> 
</dependency>


**建表:**

  5 incomplete dev   6 complete test   7 complete prod  

CREATE TABLE `business_compensate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '需要修复的数据',
  `data_type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据的分配 HD_PRO_COU-海典促销和券信息',
  `result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'UN_HANDLE' COMMENT '处理结果 UN_HANDLE,HANDLING,SUCCESS,EXISTS,ERROR',
  `reason` text COLLATE utf8mb4_general_ci COMMENT '异常原因',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`),
  KEY `idx_data_type` (`data_type`) USING BTREE,
  KEY `idx_result` (`result`) USING BTREE,
  KEY `idx_create_time` (`created_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务补偿表';

**补偿信息中心漏传的促销和券信息:**

  15 complete prod配置   18 incomplete prod执行  

compensateHdOfflineOrderMissPromotionCouponInfoHandler

**附带的执行任务:**

  16 incomplete prod执行  

# 手动补偿2412分表退款数据缺少的收银员和组织信息 3-条数据
completeRefundDataHandler

**CheckList:**

  8 complete 导入数据到生产库 D:\wxwork\WXWork\1688856321556893\Cache\File\2024-12\sale_jsons.xls   10 complete 导入数据完之后,执行update business_compensate set data_type = 'HD_PRO_COU';