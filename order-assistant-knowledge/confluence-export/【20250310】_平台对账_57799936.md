# 【20250310】 平台对账

# 一、业务背景

## 1.1 业务背景

1. 提升财务工作效率：通过自动化计算对账释放财务人力，避免人工对账风险，提升财务工作效率
2. 优化业务流程：通过对对账结果的分析，可以发现业务流程中存在的问题和瓶颈，及时进行优化和改进，提高业务效率和服务质量
3. 保障交易准确性：可以及时发现和纠正订单信息和支付信息中的错误，确保每一笔交易的准确性和完整性


## 1.2 痛点分析

1. 每月财务都要通过人力去比对平台与心云的账单数据，数据相对较多，繁琐且对比容易出现缺漏


## 1.3 系统现状

1. 暂无订单对账相关功能


# 二、需求分析

## 2.1 业务流程

**V2.33.1平台对账 - 产品部 - 一心数科数字化产研中心-wiki**

# 四、整体设计

## 4.1 统一语言定义

| 名词 | 说明 |
| --- | --- |
| 平台结算单 | 订单完成后&退款完成后，公域平台会生成对应的结算单（订单级） |
| 心云下账单 | 心云下账生成的单据（订单级） |
| 金蝶应收单 | 心云下账到金蝶后，金蝶会每日按门店汇总生成应收单数据（门店级） |
| 对账 | 财务之前会下载平台结算单数据和金蝶应收单数据，然后对比金额，如果有差异就计算差异。 |
| 调差 | 上述对账时计算差异的过程就叫调差。 |


## 4.2 流程图

**true订单对账结果任务falseautotoptrue8518**

# 五、详细设计

## 5.1 项目结构

**true对账服务模块falseautotoptrue9111**

## 5.2 涉及数据库

### 5.2.1表设计

| 表名 | sql |
| --- | --- |
| 码值表 | sqlEmacs码值表CREATE TABLE `code_value` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `code_type` varchar(64) NOT NULL COMMENT '码值类型',   `code_value` varchar(64) DEFAULT NULL COMMENT '编码值',   `value_desc` varchar(100) DEFAULT NULL COMMENT '文字描述',   `third_platform_code` varchar(10) DEFAULT NULL COMMENT '三方平台编码',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   PRIMARY KEY (`id`),   KEY `idx_type_code` (`code_type`,`code_value`) ) ENGINE=InnoDB COLLATE=utf8mb4_general_ci COMMENT='码值表'; |
| 批量任务表 | sqlEmacsCREATE TABLE `batch_task` (   `id` bigint NOT NULL COMMENT '任务ID',   `business_type` varchar(128) NOT NULL COMMENT '业务类型',   `task_name` varchar(255) DEFAULT NULL COMMENT '任务名称',   `status` tinyint NOT NULL COMMENT '1-待处理 2-处理中 3-已完成 4-失败',   `task_type` tinyint NOT NULL COMMENT '任务类型：1-异步导入 2-同步导出 3-异步导出',   `dto_class_name` varchar(128) DEFAULT NULL COMMENT 'DTO类名称',   `success_record` int DEFAULT NULL COMMENT '成功条数',   `fail_record` int DEFAULT NULL COMMENT '失败条数',   `result` tinyint DEFAULT NULL COMMENT '导入结果：1-全部成功、2-部分成功部分失败、3-全部失败',   `original_path` varchar(255) DEFAULT NULL COMMENT '原始文件',   `result_path` varchar(255) DEFAULT NULL COMMENT '结果文件',   `tips` varchar(255) DEFAULT NULL COMMENT '提示信息',   `finish_time` datetime DEFAULT NULL COMMENT '完成时间',   `biz_type_id` varchar(50) DEFAULT NULL COMMENT '业务id',   `file_name` varchar(100) DEFAULT NULL COMMENT '任务列表展示的文件名',   `extra` json DEFAULT NULL COMMENT '额外信息',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   KEY `idx_business_type_task_type` (`business_type`,`task_type`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='批量任务表'; |
| 导出任务表 | EmacsCREATE TABLE `export_task` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',   `request_json` longtext NOT NULL COMMENT '请求json内容',   `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1.待执行 2.执行中 3.执行完成 4.执行失败 5.取消)',   `export_type` varchar(100) NOT NULL DEFAULT '' COMMENT '导出类型',   `export_count` int NOT NULL COMMENT '导出数量',   `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',   `is_delete` tinyint DEFAULT '0' COMMENT '是否删除(0.未删除 1.删除)',   `create_name` varchar(50) NOT NULL COMMENT '创建人姓名',   `create_user_id` varchar(50) NOT NULL COMMENT '创建人id',   `message` varchar(200) DEFAULT '' COMMENT '导出信息',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   KEY `Idx_status` (`status`),   KEY `Idx_eType_userId` (`export_type`,`create_user_id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导出任务'; |
| 对账配置表 | sqlEmacs对账配置表CREATE TABLE `reconciliation_config` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `config_id` varchar(20) DEFAULT NULL COMMENT '配置Id',   `third_platform_code` varchar(20) DEFAULT NULL COMMENT '平台编码',   `field_name` varchar(60) DEFAULT NULL COMMENT '字段名称',   `calculate_rule` json DEFAULT NULL COMMENT '计算规则',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_config_id` (`config_id`) ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对账配置表'; |
| 平台结算核算表 | sqlEmacs平台结算核算表CREATE TABLE `platform_settle_account` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `third_settle_no` varchar(20) DEFAULT NULL COMMENT '平台结算单号',   `third_order_no` varchar(20) DEFAULT NULL COMMENT '平台订单号',   `third_refund_no` varchar(20) DEFAULT NULL COMMENT '平台退款单号',   `third_platform_code` varchar(20) DEFAULT NULL COMMENT '三方平台编码',   `settle_type` varchar(10) DEFAULT NULL COMMENT '核算类型  ORDER:销售单 REFUND:退款单',   `company_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',   `organization_code` varchar(20) DEFAULT NULL COMMENT '机构编码',   `platform_start_time` datetime DEFAULT NULL COMMENT '平台下单时间',   `settle_date` date DEFAULT NULL COMMENT '结算日',   `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',   `merchant_freight_subside` decimal(16,6) DEFAULT NULL COMMENT '商家承担运费补贴',   `brokerage_amount` decimal(16,6) DEFAULT NULL COMMENT '交易佣金',   `platform_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '平台配送费',   `qisuda_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '企速达配送费',   `timely_insurance` decimal(16,6) DEFAULT NULL COMMENT '准时宝险',   `reward` decimal(16,6) DEFAULT NULL COMMENT '奖励',   `customer_compensate_fee` decimal(16,6) DEFAULT NULL COMMENT '客服赔付费用',   `due_amount` decimal(16,6) DEFAULT NULL COMMENT '回款金额',   `subside` decimal(16,6) DEFAULT NULL COMMENT '补贴',   `merchant_tip` decimal(16,6) DEFAULT NULL COMMENT '商家承担小费',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_third_settle_no` (`third_settle_no`),   KEY `idx_third_platform_code` (`third_platform_code`),   KEY `idx_organization_code` (`organization_code`),   KEY `idx_third_order_no` (`third_order_no`),   KEY `idx_third_refund_no` (`third_refund_no`),   KEY `idx_platform_start_time` (`platform_start_time`),   KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台结算核算表'; |
| 订单对账表 | sqlEmacs订单对账表CREATE TABLE `order_reconciliation` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `third_order_no` varchar(20) DEFAULT NULL COMMENT '平台订单号',   `third_platform_code` varchar(20) DEFAULT NULL COMMENT '平台编码',   `company_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分公司编码',   `organization_code` varchar(20) DEFAULT NULL COMMENT '机构编码',   `platform_start_time` datetime DEFAULT NULL COMMENT '平台下单时间',   `settle_date` date DEFAULT NULL COMMENT '结算日',   `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',   `account_amount` decimal(16,6) DEFAULT NULL COMMENT '下账金额',   `differ_amount` decimal(16,6) DEFAULT NULL COMMENT '差异金额',   `close_account_flag` tinyint DEFAULT NULL COMMENT '关账标记   0：未关账   1：已关账',   `exception_flag` tinyint DEFAULT NULL COMMENT '异常标记   0：非异常单  1：异常单',   `exception_status` tinyint DEFAULT NULL COMMENT '异常状态     0：未处理   1：已处理',   `exception_type` varchar(10) DEFAULT NULL COMMENT '异常类型',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_third_order_no` (`third_order_no`),   KEY `idx_third_platform_code` (`third_platform_code`),   KEY `idx_organization_code` (`organization_code`),   KEY `idx_platform_start_time` (`platform_start_time`),   KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单对账表'; |
| 店铺对账表 | sqlEmacs对账表设计CREATE TABLE `store_reconciliation` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `third_platform_code` varchar(20) DEFAULT NULL COMMENT '平台编码',   `company_code` varchar(20) DEFAULT NULL COMMENT '分公司编码',   `organization_code` varchar(20) DEFAULT NULL COMMENT '机构编码',   `settle_date` date DEFAULT NULL COMMENT '结算日',   `order_count` bigint DEFAULT NULL COMMENT '总订单数',   `effict_order_count` bigint DEFAULT NULL COMMENT '有效订单数',   `differ_order_count` bigint DEFAULT NULL COMMENT '差异订单数',   `exception_order_count` bigint DEFAULT NULL COMMENT '异常订单数  差异订单数初始值快照',   `exception_order_count_handled` bigint DEFAULT NULL COMMENT '已处理异常订单数',   `exception_not_account_count` bigint DEFAULT NULL COMMENT '未下账异常订单数',   `exception_calculate_error_count` bigint DEFAULT NULL COMMENT '计算公式异常订单数',   `settle_amount` decimal(16,6) DEFAULT NULL COMMENT '结算金额',   `account_amount` decimal(16,6) DEFAULT NULL COMMENT '下账金额',   `differ_amount` decimal(16,6) DEFAULT NULL COMMENT '差异金额',   `merchant_freight_subside` decimal(16,6) DEFAULT NULL COMMENT '商家承担运费补贴',   `brokerage_amount` decimal(16,6) DEFAULT NULL COMMENT '交易佣金',   `platform_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '平台配送费',   `qisuda_delivery_fee` decimal(16,6) DEFAULT NULL COMMENT '企速达配送费',   `timely_insurance` decimal(16,6) DEFAULT NULL COMMENT '准时宝险',   `reward` decimal(16,6) DEFAULT NULL COMMENT '奖励',   `customer_compensate_fee` decimal(16,6) DEFAULT NULL COMMENT '客服赔付费用',   `due_amount` decimal(16,6) DEFAULT NULL COMMENT '回款金额',   `subside` decimal(16,6) DEFAULT NULL COMMENT '补贴',   `merchant_tip` decimal(16,6) DEFAULT NULL COMMENT '商家承担小费',   `close_account_flag` tinyint DEFAULT NULL COMMENT '关账标记   0：未关账   1：已关账',   `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',   `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',   `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_settle_plat_organization` (`settle_date`,`third_platform_code`,`organization_code`),   KEY `idx_third_platform_code` (`third_platform_code`),   KEY `idx_organization_code` (`organization_code`),   KEY `idx_settle_date` (`settle_date`) ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='店铺对账表'; |


### 5.2.2 ER图

true对账服务erp图falseautotoptrue16013

### 5.2.3 初始配置sql

枚举配置：

sqlEmacs对账表设计INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'settleAmount', '商家应收款');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'totalFoodAmount', '商品总价（不含商品包装盒费）');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'boxAmount', '商品包装盒费总价');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'activityPoiAmount', '商家活动总支出金额（含赠品成本）');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'activityMeituanAmount', '美团活动补贴总金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'activityAgentAmount', '代理商活动承担金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'platformChargeFee', '佣金');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'performanceServiceFee', '订单配送服务费金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'userPayShippingAmount', '用户支付配送费');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'userOnlinePayAmount', '用户在线支付金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'rate', '佣金的费率');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'bottom', '保底金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'settleMilli', '结算金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'wmDonationAmount', '公益捐赠金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'wmDoggyBagAmount', '打包袋金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'dealTip', '配送小费');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'productPreferences', '商家活动支出分摊到商品上的优惠总金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'notProductPreferences', '商家活动支出的未分摊到商品上的总金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'medicalInsuranceFee', '医保报销费用');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'medicalInsuranceCash', '医保自费金额');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'agreementCommissionRebateAmount', '无门槛订单的返利');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'skuActSubsidy', '商家活动支出-商品分摊');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'shippingActSubsidy', '商家活动支出-配送费分摊');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'bagActSubsidy', '商家活动支出-打包袋分摊');
INSERT INTO code_value
(code_type, third_platform_code, code_value, value_desc)
VALUES('ACCOUNT_CONFIG', '27', 'businessPlatformChargeFee', '企业版佣金');

INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'adjust_fee', '调账金额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'agent_rate', '代理商补贴', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'cold_box_fee', '冷链加工费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'commission', '实收佣金', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'guarantee_fee', '保底抽佣金额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'package_fee', '包装费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'platform_rate', '平台补贴', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'product_fee', '商品总金额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'send_fee', '配送费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'shop_rate', '商户补贴', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'user_fee', '用户实付金额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'zhongbao_call_fee', '众包呼单费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'actual_base_logistics_amount', '履约服务费（基础物流费）', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'baseLogisticsAmount', '基础物流费总额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantCouponAmount', '商户补贴-商家券', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantDeliveryCouponAmount', '商户补贴-配送费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantDeliverySubsidyAmount', '商户补贴-配送费活动', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantCashGiftAmount', '商户补贴-礼金', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantSubsidyAmount', '商户补贴-活动', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantItemCouponAmount', '商户补贴-单品券', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'originDeliverFee', '配送费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'deliveryTipAmount', '呼单小费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeCouponAmount', '饿了么平台补贴-商家券', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeDeliveryCouponAmount', '饿了么平台补贴-配送费券', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeDeliverySubsidyAmount', '饿了么平台补贴-配送费活动', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeCashGiftAmount', '饿了么平台补贴-礼金', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeSubsidyAmount', '饿了么平台补贴-活动', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'elemeRedPacket', '饿了么平台补贴-红包', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'merchantRedPacket', '商户补贴-红包', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'addition_service_price', '增值服务费总额', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'medical_card_pay', '医保卡支付', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'pay_channel_fee', '支付服务费', '24');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'shipping_cost', '应收运费', '24');

INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'goodsBill', '用户支付货款', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'dueAmount', '应结金额', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'freightBill', '运费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'packagingFeeAmount', '商家收包装费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'platformSubsidySuspend', '平台补贴暂扣', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'packageBill', '餐盒费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'commission', '总佣金(货款佣金+运费佣金+餐盒费佣金)', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'storeSubsidy', '商家承担补贴(货款+运费)', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'subsidy', '总补贴(平台+商家承担补贴)', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'marketBill', '平台承担补贴(市场费)', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'marketingServiceFee', '营销服务费用', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'originalAmount', '订单原价', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'paymentSubsidies', '商家承担货款补贴', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'storeFreightSubsidy', '商家承担运费补贴', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'storeFreightAmount', '商家自送配送费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'pickupServiceAmount', '商家承担小费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'performanceServiceFee', '基础服务费', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'invoiceFee', '开票金额', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'platformFreightSubsidy', '平台运费补贴', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'fundPay', '医保统筹金额', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'psnAcctPay', '医保个账金额', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'ownPayAmt', '医保自付金额', '11');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'supplierServiceAmount', '商家服务费', '11');

INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'orderAmount', '订单金额', '43');
INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_CONFIG', 'refundAmount', '退款金额', '43');

INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_EXCEPTION', 'omsNotAccountException', '心云未下账', null);

INSERT INTO reconciliation.code_value
(code_type, code_value, value_desc, third_platform_code)
VALUES('ACCOUNT_EXCEPTION', 'calculateException', '计算公式异常', null);

对账配置：

sqlEmacs对账配置INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(14, '1783052553662110001', '11', '结算金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(15, '1783052553662110002', '11', '商家承担运费补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(16, '1783052553662110003', '11', '佣金', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(17, '1783052553662110004', '11', '平台运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(18, '1783052553662110005', '11', '企速达运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(19, '1783052553662110006', '11', '准时宝险', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(20, '1783052553662110007', '11', '奖励', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(21, '1783052553662110008', '11', '客服赔付费用', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(22, '1783052553662110009', '11', '回款金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(23, '1783052553662110010', '11', '补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(24, '1783052553662110011', '11', '商家承担小费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(25, '1783052553662240001', '24', '结算金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(26, '1783052553662240002', '24', '商家承担运费补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(27, '1783052553662240003', '24', '佣金', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(28, '1783052553662240004', '24', '平台运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(29, '1783052553662240005', '24', '企速达运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(30, '1783052553662240006', '24', '准时宝险', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(31, '1783052553662240007', '24', '奖励', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(32, '1783052553662240008', '24', '客服赔付费用', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(33, '1783052553662240009', '24', '回款金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(34, '1783052553662240010', '24', '补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(35, '1783052553662240011', '24', '商家承担小费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(36, '1783052553662270001', '27', '结算金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(37, '1783052553662270002', '27', '商家承担运费补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(38, '1783052553662270003', '27', '佣金', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(39, '1783052553662270004', '27', '平台运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(40, '1783052553662270005', '27', '企速达运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(41, '1783052553662270006', '27', '准时宝险', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(42, '1783052553662270007', '27', '奖励', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(43, '1783052553662270008', '27', '客服赔付费用', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(44, '1783052553662270009', '27', '回款金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(45, '1783052553662270010', '27', '补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(46, '1783052553662270011', '27', '商家承担小费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(47, '1783052553662430001', '43', '结算金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(48, '1783052553662430002', '43', '商家承担运费补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(49, '1783052553662430003', '43', '佣金', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(50, '1783052553662430004', '43', '平台运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(51, '1783052553662430005', '43', '企速达运费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(52, '1783052553662430006', '43', '准时宝险', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(53, '1783052553662430007', '43', '奖励', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(54, '1783052553662430008', '43', '客服赔付费用', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(55, '1783052553662430009', '43', '回款金额', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(56, '1783052553662430010', '43', '补贴', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);
INSERT INTO reconciliation.reconciliation_config
(id, business_no, third_platform_code, field_name, calculate_rule, created_by, updated_by, sys_create_time, sys_update_time, version)
VALUES(57, '1783052553662430011', '43', '商家承担小费', NULL, 'system', 'system', '2025-03-12 13:42:16.000', '2025-03-12 13:42:16.000', 1);

## 5.3 接口设计

| 所属模块 | 接口URL | 描述 | 入参 | 出参 |
| --- | --- | --- | --- | --- |
| 公式配置 | /1.0/reconciliation/config/getConfigEnum | 查询配置枚举值 | {  "codeType" //枚举类型 } |  |
| /1.0/reconciliation/config/Update | 更新配置 | {  "calculateRule" //计算规则 } |  |
| /1.0/reconciliation/config/queryPage | 对账配置列表查询 | {  "thirdPlatformCode" //三方平台编码 } |  |
| 金额核算结果 | /1.0/reconciliation/amount/queryPage | 金额核算结果列表查询 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "thirdOrderNo": "", //平台订单号  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd } |  |
| /1.0/reconciliation/amount/queryPageExport | 金额核算结果列表信息导出 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "thirdOrderNo": "", //平台订单号  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd } |  |
| 订单对账结果 | /1.0/reconciliation/order/queryPage | 订单对账结果列表查询 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "thirdOrderNo": "", //平台订单号  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd  "exceptionFlag": "", //是否异常  "exceptionReason": "" //异常原因 } |  |
| /1.0/reconciliation/order/queryPageExport | 订单对账结果列表信息导出 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "thirdOrderNo": "", //平台订单号  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd  "exceptionFlag": "", //是否异常  "exceptionReason": "" //异常原因 } |  |
| 店铺对账结果 | /1.0/reconciliation/store/queryPage | 店铺对账结果列表查询 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd  "differenceFlag": "" //是否有差异 } |  |
| /1.0/account/store/queryPageExport | 店铺对账结果列表信息导出 | {  "thirdPlatformCode": "", //三方平台编码  "subCompanyCode": "", //子公司编码  "organizationCode": [], //门店编码  "beginStartTime": "", //下单开始时间 yyyy-MM-dd  "endStartTime": "", //下单结束时间 yyyy-MM-dd  "beginSettleTime": "", //结算开始时间 yyyy-MM-dd  "endSettleTime": "" //结算结束时间 yyyy-MM-dd  "differenceFlag": "" //是否有差异 } |  |
| 对账结果统计 | /1.0/reconciliation/statistic/queryCalendar | 对账日历统计 | {  "thirdPlatformCode": "", //三方平台编码  "startTime": "", //起始时间  "endTime": "" //结束时间 } | {  "accountDate": [  {  "dayTime": "2025-03-09 00:00:00", //几号  "differenceFlag": false //是否有差异  },  {  "dayTime": "2025-03-10 00:00:00", //几号  "differenceFlag": true //是否有差异  }  ] } |
| /1.0/reconciliation/statistic/queryOrder | 店铺对账异常统计订单对账异常统计异常对账处理统计对账金额统计异常原因统计（死值，不变） | {  "thirdPlatformCode": "", //三方平台编码  "startTime": "", //起始时间  "endTime": "" //结束时间 } |  |
| /1.0/reconciliation/statistic/queryAmountCheck | 金额核算结果统计 | {  "thirdPlatformCode": "", //三方平台编码  "startTime": "", //起始时间  "endTime": "" //结束时间 } |  |
| 其他 |  | 导出记录列表查询 |  |  |
|  | 下账失败订单重新下账时更新对账相关信息（只在原金额上进行累加） |  |  |
|  | 配置变更重新计算与统计本月或者上月所有金额数据接口（待定） |  |  |


# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等