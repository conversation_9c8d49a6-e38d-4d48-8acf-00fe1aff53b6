# 【20240312】医保订单增加类型查询和显示以及导出

# 一、背景

## 1.1 业务背景

订单查询时需要获取医保订单类型的订单数据，导出配置需要配置医保金额，订单详情页面需要展示医保金额

## 1.2 痛点分析

系统目前筛选订单只有普通订单和处方订单，导出配置不能配置医保金额，订单详情中没有展示医保金额

## 1.3 系统现状

无法单独筛选出医保订单以及展示医保金额

# 二、需求分析

## 2.1 业务流程

[医保订单查询 (axshare.com)](https://3a66mj.axshare.com/?id=pdmjrc&p=%E5%8C%BB%E4%BF%9D%E8%AE%A2%E5%8D%95%E6%9F%A5%E8%AF%A2&g=1)

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

# 五、详细设计

## 5.1 详细模块设计

## 5.3 接口设计

### 5.3.1 前端交互接口

#### 1.订单查询接口

**接口地址：[POST]https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search**

**详细实现：新增新增请求参数orderType = 4 表示医保订单，对于数据库实现，需要order_pay_info的medicare_amount的值大于0，同时order_info的medical_insurance值为1**

**订单查询界面如下：**

**2.订单列表新增订单类型字段显示**

**接口地址：[POST]https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search**

#### 详细实现：返回结果中新增orderTypeDesc字段标识订单类型：对于同时有多种类型订单，通过逗号分隔显示，如医保订单,普通订单

**订单显示界面如下：**

#### 3.导出配置中，财务配置新增医保金额

**接口地址：[POST]https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/export/config/list**

**详细实现：在dict_export_column表中把医保金额的列分类中文名修改成财务金额信息，执行脚本如下：** 

```
update dict_export_column set category = '财务金额信息' where id = 218;
```

 **导出配置界面如下：**

**4.导出Excel中医保金额字段赋值**

**详细实现：更新OrderInfoExportMapper.xml中医保金额值获取，同步更新ES中医保金额值存储**

**5.订单详情增加医保金额显示**

**接口地址：[POST]https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all**

**详细实现：通过接口返回对象中的medicareAmount字段展示医保金额**

**订单详情界面如下：**

****

**6.历史医保订单数据刷新**

**数据库：根据order_pay_info表中的medicare_amount字段判断是否是医保订单，如果medicare_amount大于0就是医保单，更新order_info表中medical_insurance字段值为1**

**ES：刷新历史数据，对于是医保订单的数据，刷入billInssuranceAmount字段及对应值到ES中**

```
5.3.2 单纯后台接口 1.新增通过判断order_pay_info的医保金额字段是否有值，给order_info的医保订单标识字段赋值：cn.hydee.middle.business.order.v2.manager.OrderSaveHandlerManager#saveOrderBase 2.根据medicalInsurance、prescriptionFlag判断订单所属类型，赋值到orderTypeDesc字段：cn.hydee.middle.business.order.service.impl.OrderInfoServiceImpl#searchOrderAllPage 3.更新OrderInfoMapper.xml中的searchOrderAllPage查询条件，当order_type = 4 时，追加medical_insurance = 1 4.更新cn.hydee.middle.business.order.elasticsearch.requestbuild.OrderAllQueryBuild#buildQuery中查询医保订单的条件构造 5.在同步到es中时添加医保金额字段billInsuranceAmount赋值 6.在导出的订单excel中，更新订单类型显示的内容，cn.hydee.middle.business.order.batch.export.processor.impl.OrderInfoExportTaskProcessor#transfer中实现拼接订单类型
```

## 5.4 涉及数据库

order_info、order_pay_info、dict_export_column 

## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年03月12日**

**研发时间：2024年03月12日-2024年03月13日（含研发自测）；联调时间：2024年03月13日-2023年03月14日；测试时间：2024年03月14日-2024年03月15日；上线时间：2024年03月15日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 订单查询 | 医保类型订单查询 | [hydee-business-order](https://yxtgit.hxyxt.com/order/hydee-business-order) |  |  |  |  |  |
| 订单类型显示 |  |  |  |
| 新增医保金额导出配置 |  |  |  |
| 订单导出excel根据配置显示医保金额 |  |  |  |
| 订单详情新增医保金额显示 |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等