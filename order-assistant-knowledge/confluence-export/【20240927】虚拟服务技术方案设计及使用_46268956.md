# 【20240927】虚拟服务技术方案设计及使用

## 

## 一、什么是虚拟服务

虚拟服务提供服务接口级别的MOCK能力。去除测试时由于三方服务不可控因素带来的影响。

举个例子：运营在心云上操作缺货退款，退款时会调用三方的退款服务，三方服务返回成功后心云会走后续操作；但是同一个订单只允许退款一次，为了不需要重复下单就能完成多次退款，这个时候我们在调用三方平台退款服务的请求就可以发到虚拟服务上，虚拟服务配置好mock数据，就能实现多次退款操作。

true1falseautotoptrue4141

## 二、应用架构

true2falseautotoptrue9119

### 2.1 执行流程

true3falseautotoptrue4991

### 2.2 模块划分

## 三、接口设计

### 3.1 新增规则

调用场景说明：新增规则

URL地址：[https://test-api.hxyxt.com/zeus/](https://test-merchants.hxyxt.com/businesses-gateway/)mock/mock/config/addRule

请求方式：POST

入参信息：

| 字段名 | 类型 | 是否必填 | 示例 | 备注说明 |
| name | string | 是 |  | 规则名称 |
| order | int | 否 |  | 排序，越小优先级越高，可以为负数 |
| predicates | map | 是 |  | 校验规则 |
| dataHandle | map | 否 |  | 数据库处理器，默认是DBDataHandler |
| responseHandlers | map | 否 |  | 返回数据处理器 |


响应参数：

| 字段名 | 类型 | 示例 | 备注说明 |
| data | boolean |  | 是否成功 |


请求示例：

``` 
{
    "name": "测试rule",
    "order": 1,
    "predicates": {
        "header": {
            "someHeader": "someHeader"
        },
        "path": {
            "pattern": "/api/cloud"
        }
    },
    "dataHandle": {
        "dBMock": {
            "mockKey": "${mockKey}"
        }
    },
    "responseHandlers": {
        "header": {
            "content-type-some-header": "application/json"
        }
    }
}
 ```

返回示例：

``` 
{
  "code": "10000",
  "msg": "操作成功",
  "data": true
}
 ```

### 3.2 新增mock数据

调用场景说明：新增规则

URL地址：[https://test-api.hxyxt.com/zeus/](https://test-merchants.hxyxt.com/businesses-gateway/)mock/mock/data/addData

请求方式：POST

入参信息：

| 字段名 | 类型 | 是否必填 | 示例 | 备注说明 |
| mockKey | string | 否 |  | 用来查询mock数据的唯一key，自定义时最好规划一下，默认值： mock数据的id |
| data | string | 是 |  | mock返回的数据 |
| contentType | string | 否 |  | 默认application/json |
| remark | string | 否 |  | 备注 |


响应参数：

| 字段名 | 类型 | 示例 | 备注说明 |
| data | long |  | mock数据id |


请求示例：

``` 
{
    "data": "123",
    "mockKey": "testKey",
    "data": "{\"name]": \"testName\"}",
    "contentType": "application/json"
}
```

返回示例：

``` 
{
  "code": "10000",
  "msg": "操作成功",
  "data": 3
}
 ```

### 3.2 刷新规则

调用场景说明：新增或删除规则后不会影响正在执行的规则，需要调用刷新规则

URL地址：[https://test-api.hxyxt.com/zeus/](https://test-merchants.hxyxt.com/businesses-gateway/)mock/rule/refreshRules

请求方式：POST

入参信息：

| 字段名 | 类型 | 是否必填 | 示例 | 备注说明 |


响应参数：

| 字段名 | 类型 | 示例 | 备注说明 |
| data | boolean |  | mock数据id |


请求示例：无

返回示例：

``` 
{
  "code": "10000",
  "msg": "操作成功",
  "data": true
}
 ```

## 四、组件介绍

### 4.1 匹配组件(Predicates)

#### 4.1.1 Header

介绍： 匹配设置的header，配置多个代表需要都满足

配置：

``` 
{
  "predicates": {
    "header": {
      "someHeader": "someHeaderValue",
      "someHeader2": "someHeaderValue2"
    }
  }
}
```

#### 4.1.2 Path

介绍：匹配请求path，Ant通配符匹配路径规则

ant通配符规则：[https://blog.csdn.net/fygwfygyiq/article/details/51077573](https://blog.csdn.net/fygwfygyiq/article/details/51077573)

配置：

``` 
{
  "predicates": {
    "path": {
      "pattern": "/api/homse"
    }
  }
}
```

#### 4.1.3 Host

介绍：host匹配，请求host的匹配器。如果有不同的server提供了相同的接口，但是功能不同，这时可以通过host进行区分。

配置：host匹配是 全匹配，http 和https也是严格校验的

``` 
{
  "predicates": {
    "host": {
      "pattern": "http://xxx.xx.com:8090"
    }
  }
}
```

### 4.2 数据查询器

只能配置一个数据查询器

#### 4.2.1 DBMock

介绍： 获取mock数据的组件，目前只有这个，通过mock-server的数据库mockKey和path字段匹配数据，如果有相同的多条数据时，返回id最小的数据。

配置：mockKey 字段是用来作为查询mockData中mockKey的匹配字段，可以填写任意字符串，与mockData中配置的一致时可以查询出来。

动态配置： 语法： ${动态字段} eg: ${mockKey},代表从header或body中取出key为mockKey的字段作为查询数据。 eg: ${body.mockKey} 中从body中取出字段为mockKey的数据。 ${body.obj.mockKey} 取出多层嵌套对象obj 中的mockKey字段作为查询数据。

``` 
{
  "dataHandle": {
    "dBMock": {
      "mockKey": "${mockKey}"
    }
  }
}
```

### 4.3 返回处理器

#### 4.3.1 Header

介绍：可以设置任意响应头，可以设置多个。

配置：

```  
{
 "responseHandlers": {
  "header": {
   "someHeader": "someHeaderValue",
   "someHeader2": "someHeaderValue2",
  }
 }
}
```

## 五、用例

1. 配置规则
2. postMan请求接口配置路由header和mockKey
3. 发起请求


### 5.1 一个测试接口， 后续只会触发一次或多次三方服务调用

调用三方的接口地址: /test

调用心云地址 ：https://test-api.hxyxt.com/business-gateway/order/refund

#### 5.1.1 需要做的配置

【新增mock数据】

``` 
{
    "data": "123",
    "data": "{\"name]": \"testName\"}",
    "path": "/test"
}
```

返回

``` 
{
  "code": "10000",
  "msg": "操作成功",
  "data": 3
}
 ```

#### 5.1.2 请求

[https://test-api.hxyxt.com/business-gateway/order/refund](https://test-api.hxyxt.com/business-gateway/order/refund)

mockKey: 3