# 【20241028】海典、科传促销优惠记录及券信息记录【已完成】

需求: 

[https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38852024#](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38852024)

对接人: 张涛、刘宇鑫

### 科传文档: https://doc.weixin.qq.com/sheet/e3_AfYAogayAA8QFWUoZftQq6oroA6cE?scode=AOsAFQcYAAcr3Kr84kAZ0AgQYfAIE&tab=bbde6r

分支:

feature-promotion-discount-20241028 废弃

从master重新拉取: feature-promotion-and-coupon-kc-hd-20241112

项目:

order-sync-service

order-atom-service

order-service 修改详情接口、科传

order-framework

执行sql:

  2 complete 12月的分表，下面的sql没有,上线是注意12月的分表是否有生成  

true# 开发环境已经删除offline_order_detail_promotion_${seq}表sub_promotion_no和extend_json字段; 测试环境未删除
# todo 删除测试环境


# 删除明细促销表和明细券信息表 DROP TABLE IF EXISTS offline_order_detail_promotion_0;
DROP TABLE IF EXISTS offline_order_detail_coupon_0;


# 新增促销信息和券信息,同明细表解耦 
CREATE TABLE `offline_order_promotion_${seq}` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部订单号,自己生成',
  `erp_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',
  `commodity_count` decimal(16,6) DEFAULT NULL COMMENT '商品数量',
  `third_order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单号',
  `promotion_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '促销编码',
  `sub_promotion_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子促销编码',
  `promotion_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '促销类型',
  `promotion_amount` decimal(16,6) DEFAULT NULL COMMENT '促销金额',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  `extend_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '拓展字段',
  `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'ORDER,DETAIL',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no_erp_code` (`order_no`,`erp_code`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下单促销'; 

CREATE TABLE `offline_order_coupon_${seq}` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部订单号,自己生成',
  `erp_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',
  `commodity_count` decimal(16,6) DEFAULT NULL COMMENT '商品数量',
  `third_order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单号',
  `coupon_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券编码',
  `open_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券导出编码',
  `coupon_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券名称',
  `coupon_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券',
  `coupon_denomination` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券面值(String)',
  `used_coupon_amount` decimal(16,6) DEFAULT NULL COMMENT '使用优惠券金额',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  `extend_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '拓展字段',
  `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'ORDER,DETAIL',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no_erp_code` (`order_no`,`erp_code`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下单coupon';




Apollo配置:

# 修改sharding分表配置,记住有2处！！！
offline_order_detail_promotion --> offline_order_promotion
offline_order_detail_coupon --> offline_order_coupon



  5 complete order-types 版本: 1.3.0-SNAPSHOT  

 <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>1.3.0-SNAPSHOT</version>
    </dependency>

  6 complete order-atom-sdk `1.4``.``0``-SNAPSHOT`  

  <groupId>com.yxt.order.atom.sdk</groupId>
  <artifactId>order-atom-sdk</artifactId>
  <version>1.4.0-SNAPSHOT</version>

外部依赖升级

  8 complete 通知会员营销升级  

<dependency>
      <groupId>com.yxt.order.open.message</groupId>
      <artifactId>order-open-message</artifactId>
      <version>1.5.0-SNAPSHOT</version>
    </dependency>

<dependency>
      <groupId>com.yxt.order.open.sdk</groupId>
      <artifactId>order-open-sdk</artifactId>
      <version>1.5.0-SNAPSHOT</version>
    </dependency>

营销和券SDK:

  <dependency>
      <groupId>com.yxt.marketing</groupId>
      <artifactId>yxt-middle-promotion-sdk</artifactId>
      <version>1.0.2-SNAPSHOT</version>
  </dependency>

解析类HdPromExtendInfoRespDTO

<dependency>
    <groupId>com.yxt</groupId>
    <artifactId>yxt-middle-coupon-sdk</artifactId>
    <version>1.1.4-SNAPSHOT</version>
</dependency> 

样例:

// 5
// 1 订单级别,记录拓展信息json

-- 促销
{
    "erpCode": "653712",
    "promActivityInfoList": [
        {
            "activityId": 348,
            "subActivityId": null,
            "promType": 11,
            "groupNo": "bb1c7e68-1801-488c-b656-f156e8648282", // 不用记录
            "totalDiscountAmount": 50.4000,
            "statisticData": {
                "number": 3,
                "discountAmount": 50.4000,
                "extraNumber": null,
                "extraDiscountAmount": null
            }
        }
    ]
}


科传和海典
{
    "discountList": // 优惠券列表 （现金券和礼品券商品不分摊优惠金额）
        [
            "discountCode":"S912870633",    // 优惠券唯一编码
            "openCode":"ZKD912870633",      // 优惠券导出编码    
            "discountType": 1,      // 优惠券类型 1-心云折扣券，2-心云满减券，3-心云礼品券，5-心云现金券，20-商品级折扣券模板，21-整单级折扣券模板，22-代金券模板，23- 礼品券模板    
            "discountAmount":2,     // 优惠券优惠金额
            "discountDetailList":   // 优惠券商品分摊明细
            [
                {
                    "goodsCode":"10001",    // 商品erp编码
                    "count":"5",            // 商品数量
                    "discountAmount":1      // 商品优惠分摊金额
                },
                {
                    "goodsCode":"10002",    // 商品erp编码
                    "count":"5",            // 商品数量
                    "discountAmount":1      // 商品优惠分摊金额
                }          
            ]
        ],
    "extendJsoin": // 扩展信息，考虑未来自主可定义，全链路传递需要的信息到订单中台  暂时没有,暂时不处理
        [
        ]
 }

 明细 2 条
 订单 1 条  - 订单级别json

XY_DISCOUNT-心云折扣券，XY_FULL_REDUCTION-心云满减券，XY_GIFT-心云礼品券，XY_CASH-心云现金券，ITEM_DISCOUNT-商品级折扣券模板，ORDER_DISCOUNT-整单级折扣券模板，VOUCHER-代金券模板，GIFT- 礼品券模板

### 线下单模型枚举字段统一处理

使用新字段,在equals是使用枚举的name()值去equals

**正单**

| 原字段 | 新字段 |  |
| --- | --- | --- |
| baseOrderInfo |  |
| thirdPlatformCode | thirdPlatformCodeValue | 1 |
| orderState | orderStateValue | 1 |
| **basePrescriptionInfo** | **** |
| prescriptionType | prescriptionTypeValue | 1 |
| orderDetailList.baseOrderDetailInfo |  |
| status | statusValue | 1 |
| giftType | giftTypeValue | 1 |
| baseOrganizationInfo |  |
| storeDirectJoinType | storeDirectJoinTypeValue | 1 |


**退单**

| 原字段 | 新字段 |  |
| --- | --- | --- |
| baseRefundInfo |  |
| thirdPlatformCode | thirdPlatformCodeValue | 1 |
| refundType | refundTypeValue | 1 |
| afterSaleType | afterSaleTypeValue | 1 |
| refundState | refundStateValue | 1 |
| baseOrganizationInfo |  |
| storeDirectJoinType | storeDirectJoinTypeValue | 1 |
| **refundDetailList** | **** |
| refundStatus | refundStatusValue | 1 |
| giftType | giftTypeValue | 1 |