# 2024-06-13 checkListB2C退款优化

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  | hydee-business-order |  | dev | 李洋 |  | 徐凯 | 杨飞 |  | 2024-06-04 |  |
|  | hydee-business-order-web |  |  |  |  |  |  |  |  |  |
|  | hydee-business-order-b2c-third |  |  |  |  |  |  |  |  |  |
|  | ds-service-mt |  |  |  |  |  |  |  |  |  |
|  | third-plaform-order-elm |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
| ds_online_store_config 表新增 | ALTER TABLE ds_online_store_config ADD account_pay_code varchar(10) NULL COMMENT '下账支付编码'; ALTER TABLE ds_online_store_config ADD default_batch_no tinyint(1) default 0 COMMENT '是否开启默认批次号(按有效期最近原则默认) 0-否，1-是'; | 门店配置表 |
| order_info表调整 | ALTER TABLE order_info ADD deleted bigint default 0 COMMENT '逻辑删除字段 默认0-未删除'; |  |
| oms_order_info表调整 | `alter` `table` `oms_order_info``add` `column` `deleted``bigint` `default` `0 comment``'逻辑删除字段 默认0-未删除'``;` |  |


新增表：

销售单下账表

| CREATE TABLE `account_order` (  `id` bigint NOT NULL AUTO_INCREMENT,  `third_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '三方平台订单号',  `order_no` bigint NOT NULL COMMENT '系统订单号',  `service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'O2O' COMMENT 'O2O / B2C',  `order_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NORMAL' COMMENT '订单类型 NORMAL-普通订单 POST_FEE-邮费单',  `pos_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'HD_H1' COMMENT 'HD_H1-海典H1 HD_H2-海典H2 KC-科传',  `pick_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'OMS' COMMENT 'OMS-心云作业 WMS-erp作业 O2O统一默认为 OMS',  `third_plat_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康',  `organization_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属机构编码',  `org_parent_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织机构父路径id链路 1000-1100-1110-',  `acc_organization_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下账机构编码 传入到pos下账的机构编码',  `acc_org_parent_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下账机构父路径链路',  `acc_online_store_id` bigint DEFAULT NULL COMMENT '下账机构店铺id',  `buyer_actual_amount` decimal(16,4) DEFAULT NULL COMMENT '买家实付金额',  `merchant_actual_receive` decimal(16,4) DEFAULT NULL COMMENT '商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠+平台收取佣金',  `goods_total_amount` decimal(16,4) DEFAULT NULL COMMENT '商品总额 = 商品明细的商品金额汇总',  `bill_commodity_amount` decimal(16,4) DEFAULT NULL COMMENT '下账商品总金额',  `delivery_fee` decimal(16,4) DEFAULT NULL COMMENT '商家配送费，金额大于等于0',  `package_fee` decimal(16,4) DEFAULT NULL COMMENT '订单总额 = 商品明细的商品金额汇总+商家配送+平台配送费+商家包装费+平台包装费',  `merchant_discount` decimal(16,4) DEFAULT NULL COMMENT '商家优惠金额',  `medicare_amount` decimal(16,4) DEFAULT NULL COMMENT '医保金额',  `pay_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付方式编码',  `pay_channel` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '支付渠道 如微信支付',  `order_accept_time` datetime DEFAULT NULL COMMENT '订单接单时间',  `order_operator_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单操作人 O2O-拣货人 id B2C-发货人id',  `member_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微商城会员编号',  `cost_center_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'B2C订单下账专用字段 成本中心编码',  `state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'WAIT' COMMENT '下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败',  `account_time` datetime DEFAULT NULL COMMENT '下账时间',  `sale_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'erp零售流水号 下账成功返回',  `account_err_msg` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下账失败原因 下账失败返回',  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint NOT NULL DEFAULT '1' COMMENT ' 数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  UNIQUE KEY `order_no_index` (`order_no`) USING BTREE COMMENT '系统订单号唯一',  KEY `third_order_no_plat_index` (`third_order_no`,`third_plat_code`) USING BTREE COMMENT '三方单号+平台类型' ) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='销售单下账表' |


销售单下账明细表

| CREATE TABLE `account_order_detail` (  `id` bigint NOT NULL AUTO_INCREMENT,  `order_no` bigint NOT NULL COMMENT '系统订单号',  `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',  `goods_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品类型, 普通商品- NORMAL、赠品-GIFT',  `batch_no` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产批号',  `goods_count` int DEFAULT NULL COMMENT '商品数量',  `price` decimal(16,4) DEFAULT NULL COMMENT '商品单价',  `purchase_price` decimal(10,4) DEFAULT NULL COMMENT '商品进价',  `goods_amount` decimal(16,4) DEFAULT NULL COMMENT '商品金额',  `share_amount` decimal(16,4) DEFAULT NULL COMMENT '分摊金额',  `bill_price` decimal(16,4) DEFAULT NULL COMMENT '下账单价',  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint NOT NULL DEFAULT '1' COMMENT ' 数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  KEY `account_order_id_index` (`order_no`) USING BTREE COMMENT '系统订单号' ) ENGINE=InnoDB AUTO_INCREMENT=595 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售单下账明细表' |


退款单下账表

| CREATE TABLE `account_refund` (  `id` bigint NOT NULL AUTO_INCREMENT,  `order_no` bigint NOT NULL COMMENT '系统订单号',  `refund_no` bigint NOT NULL COMMENT '系统退款单号',  `third_refund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台退款单号',  `third_plat_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康',  `service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'O2O' COMMENT 'O2O / B2C',  `pos_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'HD_H1' COMMENT 'HD_H1-海典H1 HD_H2-海典H2 KC-科传',  `pick_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'OMS' COMMENT 'OMS-心云作业 WMS-erp作业 O2O统一默认为 OMS',  `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属机构编码',  `org_parent_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组织机构父路径id链路 1000-1100-1110-',  `acc_organization_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下账机构编码 传入到pos下账的机构编码',  `acc_org_parent_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下账机构父路径链路',  `acc_online_store_id` bigint DEFAULT NULL COMMENT '下账机构店铺id',  `refund_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退货类型 ：仅退款-ONLY_REFUND 退货退款-RETURN_GOODS_REFUND',  `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款类型 PART-部分退款 ALL-全额退款',  `refund_amount` decimal(16,4) NOT NULL COMMENT '退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额',  `refund_goods_total` decimal(16,4) DEFAULT NULL COMMENT '退款商品总金额',  `refund_post_fee` decimal(16,4) DEFAULT NULL COMMENT '商家配送费，金额大于等于0',  `discount_amount` decimal(16,4) DEFAULT NULL COMMENT '商品明细优惠',  `package_fee` decimal(16,4) DEFAULT NULL COMMENT '包装费',  `refund_accept_time` datetime DEFAULT NULL COMMENT '退款单接收时间',  `refund_reason` text COLLATE utf8mb4_general_ci COMMENT '退款理由',  `state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'WAIT' COMMENT '下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败',  `sale_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款下账流水号-pos返回',  `account_time` datetime DEFAULT NULL COMMENT '下账时间',  `account_err_msg` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下账失败原因 下账失败返回',  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint NOT NULL DEFAULT '1' COMMENT ' 数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  UNIQUE KEY `refund_no_index` (`refund_no`) USING BTREE COMMENT '退款单订单号唯一',  KEY `order_no_index` (`order_no`) USING BTREE COMMENT '销售单订单号' ) ENGINE=InnoDB AUTO_INCREMENT=208 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款单下账表' |


退款单下账单明细表

| CREATE TABLE `account_refund_detail` (  `id` bigint NOT NULL AUTO_INCREMENT,  `refund_no` bigint NOT NULL COMMENT '系统退款单号',  `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',  `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',  `goods_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品类型, 普通商品- NORMAL、赠品-GIFT',  `batch_no` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产批号',  `goods_count` int DEFAULT NULL COMMENT '商品数量',  `price` decimal(16,4) DEFAULT NULL COMMENT '商品售价',  `refund_price` decimal(16,4) DEFAULT NULL COMMENT '退款单价',  `refund_goods_amount` decimal(16,4) DEFAULT NULL COMMENT '退款金额 (退款数量*单价)',  `share_amount` decimal(16,4) DEFAULT NULL COMMENT '分摊金额',  `goods_order_price` decimal(16,4) DEFAULT NULL COMMENT '退款商品原价',  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',  `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',  `version` bigint NOT NULL DEFAULT '1' COMMENT ' 数据版本，每次update+1',  PRIMARY KEY (`id`) USING BTREE,  KEY `account_refund_detail_refund_no_idx` (`refund_no`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=351 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款单下账单明细表' |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |