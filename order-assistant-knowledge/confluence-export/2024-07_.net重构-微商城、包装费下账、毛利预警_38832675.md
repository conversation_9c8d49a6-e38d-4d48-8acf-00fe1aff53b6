# 2024-07 .net重构-微商城、包装费下账、毛利预警

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 微商城迁移 | third-platform-order-other third-platform-callback-otherthird-platform-otherthird-platform-order-jddj |  |  |  |  |  |  |  |  |  |
| business-orderbusiness-order-web |  |  |  |  |  |  |  |  |  |
|  | middle-datasync-message |  |  |  |  |  |  |  |  |  |
| deploy | hydee-api-gateway |  |  |  |  |  |  |  |  |  |
|  | third-platform-gateway |  |  |  |  |  |  |  |  |  |
| 毛利预警 | hydee-business-orderhydee-business-order-webhydee-business-order-b2c-third |  |  |  |  |  |  |  |  |  |
| B2C指定店铺包装费下账 | hydee-business-orderhydee-business-order-webhydee-business-order-b2c-third |  |  |  |  |  |  |  |  |  |
| 前端 | cloud-ui |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-business-order |  |  | third-platform:  migrated-platform: ["9002","9003","24","43"]  migrated-config-list:  - platform-code: "9002"  migrated-store-code-list: []  - platform-code: "9003"  migrated-store-code-list: []  - platform-code: "24"  migrated-store-code-list: []  - platform-code: "11"  migrated-store-code-list: []  - platform-code: "43"  migrated-store-code-list: [] |
| hydee-business-order-webhydee-business-order-b2c-third |  |  | third-platform:  #已经迁移到新接口中台的平台编码  migrated-platform: ["9002","9003","24","43"] |
| hydee-api-gateway |  |  | - id: yxt-third-platform-callback-other  uri: [lb://third-platform-callback-other](lb://third-platform-callback-other)  predicates:  - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/** |
| third-platform-gateway |  |  | - id: third-platform-order-other  uri: [lb://third-platform-order-other](lb://third-platform-order-other)  predicates:  - Path=/third-platform/order/9002/**,/third-platform/order/9003/**,/third-platform/order/43/** |
| middle-datasync-message |  |  | hems:  api:  server: [https://api.hxyxt.com/zeus/third-platform](https://test-api.hxyxt.com/zeus/third-platform)  url:   order: /callback/43/order/500001/48e245e5f85a4f2b85a20e8a183f4ee1 # HEMS订单接口URL  refund: /callback/43/refundOrder/500001/48e245e5f85a4f2b85a20e8a183f4ee1 # HEMS订单退货接口URL |
| hydee-business-order-webhydee-business-order-b2c-third |  |  | eBai:  packFee:  account: 4db39e0d90b34658b70b4ee10290dee2 |
| hydee-business-order-webhydee-business-order-b2c-third |  |  | storeTypeRobotMap: '{  "44": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3835393a-dcef-48ed-ad0d-7e5213e8a2a9](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3835393a-dcef-48ed-ad0d-7e5213e8a2a9)",   "46": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a805d4fb-ec79-4c22-81f9-d536b930bd17](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a805d4fb-ec79-4c22-81f9-d536b930bd17)",   "47": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c6cd31-18d5-411f-bc6a-cf51d8ebc2d9](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=16c6cd31-18d5-411f-bc6a-cf51d8ebc2d9)",   "48": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=def802e0-8bf8-4159-86b6-3e9f60f7454c](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=def802e0-8bf8-4159-86b6-3e9f60f7454c)",   "56": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bf149a2-5ea0-41a3-a0b6-bf1508d42368](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bf149a2-5ea0-41a3-a0b6-bf1508d42368)",   "57": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80972e48-feb1-4200-ba49-da0aab21513c](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=80972e48-feb1-4200-ba49-da0aab21513c)",   "58": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5700fa5d-0d18-4abf-bb5c-9eab78a54deb](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5700fa5d-0d18-4abf-bb5c-9eab78a54deb)",   "59": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ef413380-689d-43c1-9705-d99316b9988d](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ef413380-689d-43c1-9705-d99316b9988d)",   "60": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0796ef9b-7c94-4324-bc35-a25b5d96e965](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0796ef9b-7c94-4324-bc35-a25b5d96e965)",   "61": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f3fd33e3-3eae-46f8-a845-abaa43c37a37](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f3fd33e3-3eae-46f8-a845-abaa43c37a37)",   "62": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2d7418d5-ff01-41be-993c-7286eeb801a8](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2d7418d5-ff01-41be-993c-7286eeb801a8)",   "63": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=be172db6-36a7-400f-ac57-771187d7f9cf](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=be172db6-36a7-400f-ac57-771187d7f9cf)",  "64": "[https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=db386bd2-fb92-4d1e-9350-d75bed4832cf](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=db386bd2-fb92-4d1e-9350-d75bed4832cf)"  }' |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
| RocketMQ | ```  ``` |  |
| ```  ``` |  |
| ```  ``` |  |
|  |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |