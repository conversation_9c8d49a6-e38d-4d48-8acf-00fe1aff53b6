# 【20240528】 v1.1.19 订单效率优化

## 一、 背景

基于实现主动/主动部分退款、复杂换货、评价拉回、B2C组合商品分摊优化、预警提示功能的实现提升订单作业的效率

### 1.1 需求清单

| 功能 | 功能描述 |
| --- | --- |
| 主动取消/主动部分退款 | 商家主动发起取消和主动部分退款 |
| 复杂换货 | 复杂换货 |
| 评价拉回 | 评价拉回，并增加商家回复内容显示 |
| B2C组合商品分摊优化 | 金额计算优化 |
| 预警提示 | 毛利或者金额差异过多则进行提示，O2O和B2C均需要提示 |


### 1.2 痛点分析z

1. 目前系统不支持商家主动取消/主动部分退款
2. 将现有的差价弹窗需要更新成换货数量确认弹窗
3. 更新定时拉取规则
4. 优化B2C组合商品的取值
5. 通过毛利率控制全局O2O和B2C的预警


## 二、 需求分析

### 2.1 业务流程

[v1.1.19订单效率优化](https://fm7a9k.axshare.com/#id=43ith2&p=%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86-%E5%95%86%E5%AE%B6%E4%B8%BB%E5%8A%A8%E5%8F%91%E8%B5%B7%E9%83%A8%E5%88%86%E9%80%80%E6%AC%BE&g=1)

## 三、 目标

### 3.1 本期目标

实现业务相关功能，保证系统稳定运行

## 四、整体设计

### 4.1取消订单流程图

**true取消流程图falseautotoptrue6221**

### 4.2 部分退货流程图

true部分退款流程falseautotoptrue15325

**4.3 O2O异常订单换货**

**true换货新增处理falseautotoptrue6672**

## 五、 详细设计

### 1、 模块详细设计

分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比

**5.1.1 主动取消/主动部分退款**

**5.1.2 复杂换货**

true换货流程falseautotoptrue6011

**5.1.3 评价拉回**

**5.1.4 B2C组合商品分摊优化**

**5.1.5 预警提示**

### 2、 存储数据库设计

| 表名 | sql |
| --- | --- |
| 异常订单换货日志 | sqlcreate table order_detail_swap_log (    id                       int auto_increment comment '主键'         primary key,     order_no                 bigint                             not null comment '订单号',     source_order_detail_id   bigint                             not null comment '原订单明细主键',     source_erp_code          varchar(60)                        null comment '原商品erp编码',     source_commodity_name    varchar(255)                       null comment '原商品名称',     source_commodity_spec    varchar(255)                       null comment '原商品规格',     source_commodity_count   int                                null comment '原商品数量',     source_commodity_price   decimal(16, 2)                     null comment '原商品售价',     before_swap_total_amount decimal(16, 2)                     null comment '换货前总价',     swap_erp_code            varchar(60)                        null comment '换货的商品erp编码',     swap_commodity_name      varchar(255)                       null comment '换货的商品名称',     swap_commodity_spec      varchar(255)                       null comment '换货的商品规格',     swap_commodity_count     int                                null comment '换货的商品数量',     swap_commodity_price     decimal(16, 2)                     null comment '换货商品售价',     after_swap_total_amount  decimal(16, 2)                     null comment '换货后总价',     difference_price         decimal(16, 2)                     null comment '换货差价',     swap_scale               decimal(4, 2)                      null comment '换货比例',     create_by                varchar(64)                        not null comment '创建人',     create_time              datetime default CURRENT_TIMESTAMP null comment '创建时间',     status                   int      default 1                 null comment '状态：1有效 0无效'  )     ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单换货日志' ROW_FORMAT = Dynamic;  create index idx_order_no     on exception_order_swap_log (order_no); |
| ``` 运维预警设置 ``` | sqlcreate table devops_forewarn_set (     id               bigint auto_increment comment '主键'         primary key,     profit           decimal(16, 2)                     null comment '毛利率：(下账成本/成本单价)/成本单价',     profit_quota     decimal(16, 2)                     null comment '毛利额：下账单价-成本单价',     forewarn_content text                               null comment '预警内容',     service_type     varchar(10)                        null comment '服务类型：O2O、B2C',     create_by        varchar(64)                        not null comment '创建人',     create_time      datetime default CURRENT_TIMESTAMP not null comment '创建时间',     update_by        varchar(64)                        not null comment '更新人',     update_time      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间' )  ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运维预警设置' ROW_FORMAT = Dynamic;     comment '运维预警设置';    扩展：按照当前需求只存在两条数据，后期做功能更新迭代，可添加子公司code、门店code、店铺code、推送人字段实现按照不同维度做推送 |


### 3、 接口设计

新增、修改的接口定义；流量预估，接口性能设计；

#### 3.1 部分退款接口设计

订单部分退款校验接口

部分退款接口

最新三方商品详情查询接口

**3.2 O2O异常订单换货**

a.涉及到商品数量发生改变，即非等比换货时，换货后的金额也需要做变更

original_price 商品原单价 
price 商品售价 
total_amount 小计金额售价*数量 
discount_amount 促销优惠金额 
actual_amount 成交总额小计-优惠 
discount_share 优惠分摊 
actual_net_amount 下账金额 
bill_price 下账价格 
payment 商品实付金额 

brokerage_amount 交易佣金 
platform_discount_fee 平台优惠 
merchant_discount_fee 商家优惠

different_share 差异分摊
adjust_amount 调整金额
settle_price 平安结算单价
modify_price_diff 换货价差
health_value 健康贝换算金额
average_price 商品加权成本价

换货后，计算单个商品的单价、促销优惠金额、分摊金额和下账价格，如果存在除不尽的情况下，会产生两条明细记录，第一条是用平均价和数量计算获取，第二条通过减法获取

b.换货校验

①换货数量不允许超过可用库存

②换货商品总价超过原商品总价20%允许换货，但是会弹窗提醒；超过100%就不允许换货（可以配置修改）

③已退款的商品不可以换货、数量为0的商品不能换货、换货商品和原商品不能相同

b.异常订单换货接口添加换货记录的日志操作

c.部分退款后需要同步作废对应的换货记录

**3.3 评价拉回**

 a.修改拉取评价的规则，目前是拉取前一天，修改为拉取七天前到昨天的（通过开关控制）

 b.目前不存在同一订单有多条评价情况，需要造数据

**3.4 B2C组合商品分摊优化**

 1.组合商品拆分：以组合商品总价作为分母，子商品价格（单价*数量）作为分子，向下保留6位小数作为组合商品比例，通过比例分别计算出originalPrice、price、discountShare，

 当最后一条子商品时，通过减法获取对应的值

 2.拆零商品的拆分：计算出平均的金额值，校验在保留两位小数前提下能否除尽，如果不能就进行拆分，拆分逻辑是第一条的数量为总数量-1，第二条数量为1，第二条的金额数据通过减法实现

 3.生成退单明细重构，对于组合商品，如果正单明细拆分了，那么退单明细也需要做拆分

 3.美团的全额退款：平台无明细，需要构造退款明细，生成退款明细时，做金额计算，如果存在多个组合商品，部分退的时候，需要处理退单明细取值

 4.下账处理：B2C下账时处理换货逻辑屏蔽掉，在生成退款明细时就是换货后的商品了，更新全额退款的下账金额和单价取值

**3.5 预警设置**

 a.预警查询接口

 b.预警新增接口

 c.预警更新接口

 d.O2O和B2C预警提示

### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

项目工时、分工等，贴jira连接

## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等