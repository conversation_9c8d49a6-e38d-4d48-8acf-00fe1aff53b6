# 售后服务

```

```

INLINE@startuml
'https://plantuml.com/activity-diagram-beta
title 售后服务现有流程活动图

'skinparam ConditionEndStyle hline
'垂直模式
!pragma useVerticalIf on

start
: 卖家审核退款;
partition "数据库操作" {
    : 根据退款单号查询退款单信息;
    : 获取订单基本信息;
}
if (正向单网店配置ID不为null) is (yes) then
  : 根据ID获取网店下账配置;
  : 设置退款单中的下账配置;
end if
: 根据系统退款单号获取退款信息明细;
: 设置退款单中的原始退款消息列表，用于复杂换货;
if (正向单参与复杂换货) is (yes) then
  if (退款单中的原始退款消息列表不为空) is (yes) then
    if (原始退款信息中退款状态为同意退款申请) is (yes) then
      : 设置退款单中的退款单状态为已完成;
    end if
  end if
end if

if (退款单为正单退完) is (yes) then
  : 设置退款单中的退款类型为全额退款;
endif

if (退款单的第三方平台编码不为43and下账配置不为空and线上支付下账配置为1) is (yes) then
  note
  第三方平台编码为43代表微商城
  1-按顾客实际支付的支付方式下账
  end note
  partition "数据库操作" {
        : 根据订单信息查询下账单多支付方式;
        : 根据退款详情查询退款单多支付方式;
    }
  : 构建退款单详情下账金额多支付方式展示;
end if
: 处理收货地址隐私信息;

if (退款单为空) is (yes) then
  : 退款订单不存在！;
  kill
end if

if (退款审核类型不为3并且退款单的退款类型为part并且退款明细为空) is (yes) then
  note
  退款审核类型:3-拒绝
  退款类型:part-部分退款
  end note
  : 退款单无商品信息;
  kill
end if

switch(退款审核类型)
case (仅退款)
  note
  卖家同意仅退款
  end note
  : 加订单操作锁;
  partition "校验是否全额退款" {
    : 根据退款单号查询退款单信息;
    if (退款单为空) is (yes) then
      : 订单号或退款单号输入错误/不存在;
      kill
    endif
    if (退款类型为部分退款) is (yes) then
      : 根据系统订单号查询所有全额退款并且状态小于已拒绝的退款单;
      if (退款单列表不为空) is (yes) then
        : 请先操作全额退款单;
        kill
      endif
    endif
  }
  partition "退款单处理" {
    partition "1.校验操作人信息" {
        : 根据userId获取系统操作人信息;
        if(系统操作人信息为空) is (yes) then
            : 用户不存在;
            kill
        endif
    }
    partition "2.校验退款单信息" {
        : 根据退款单号获取退款单;
        if(退款单为空) is (yes) then
            : 记录不存在;
            kill
        endif
        if(退款单状态不为待退款) is (yes) then
            : 非待审核退款单状态不允许审核;
            kill
        endif
        : 将退款单信息放入退款单上下文信息中;
    }
    partition "3.获取订单信息" {
        : 根据订单号获取原订单信息;
        : 将原订单信息放入退款单上下文信息中;
    }
    partition "4.获取门店信息" {
        : 根据订单号信息获取门店信息;
    }
    if (是否为第一次弹框) is (yes) then
        note
        第一次弹框要尝试取消骑手配送
        end note
        if (退款单的退款类型为全额退款) is (yes) then
            if (原订单信息为空) is (yes) then
                stop;
            endif
            : 根据系统订单号查询订单配送记录;
             if (订单配送记录为空) is (yes) then
                stop;
            endif
            if (配送方式不为商家自配送) is (yes) then
                stop;
            endif
            if (配送平台不在已有平台中) is (yes) then
                note
                蜂鸟骑手,美团骑手,达达骑手,顺丰同城,饿百外卖,饿百外卖,其他配送中心,蜂鸟即配
                end note
                stop;
            endif
            : 根据系统订单号查询配送ID;
            if (配送单状态小于等于未呼叫或者大于配送中) is (yes) then
                stop;
            endif
            : 设置配送取消原因;
            : 封装取消骑手请求实体;
            partition "取消第三方骑手" {
                note
                取消骑手消息的逻辑是先调cancelRider再收到骑手消息
                end note
                if (骑手订单号为空) is (yes) then
                  stop;
                endif
                if (门店关联开放配送平台) is (yes) then
                  : 调用hems骑手订单取消接口;
                else (no)
                  : 直接调用开放配送平台取消骑手url;
                endif
            }
            if (取消骑手失败) is (yes) then
              : 更新订单配送记录;
              : 取消呼叫骑手失败;
              kill
            else (no)
              : 更新订单配送记录;
              : 记录订单配送日志;
            endif
        endif
    endif
    : 通知第三方退款审核;
    : 饿百同意部分退款，需要获取佣金;
    if (退款单状态为已拒绝) is (yes) then
      : 更新退款单状态为待退款待下账;
    endif
    switch(退款类型)
    case (全额退款)
        : 修改退款单状态;
        : 修改订单状态;
        if (原单已下账) is (yes) then
          if (之前是否有未下账的部分退款单) is (yes) then
            : 将其他的部分退款单全部取消;
            : 退款单终态时，机器自动拣货订单尝试再次自动拣货;
          else (no)
            stop;
          endif
        endif
        if (老订单并且退款类型为退货退款) is (yes) then
          : 更新状态为待退货;
        endif
        : 全部退款,原单取消;
        : 取消骑手;
        : 推送代发订单取消发货消息给服务商平台;
        if (退款单信息更新成功) is (yes) then
          : 机器自动拣货订单尝试再次自动拣货;
        endif
        : 更新订单信息;
        : 移除redis锁;
        : 根据退款单号查询退款商品明细;
        if (退款商品明细为空) is (yes) then
        note
        没有订单详情数据说明，前面在创建全部退款单时，
        有未下账的部分退款退款单，这里进行补充创建
        end note
          if (三方平台为京东到家) is (yes) then
            : 为京东创建剩余明细全额退款;
          endif;
          : 创建剩余明细全部退款,且更新退款单主表金额信息;
        else (no)
          : 设置退款单上下文信息中的退款商品明细;
        endif
        :  更新明细状态为已退款;
    case (部分退款)
        : 修改退款单状态;
        : 更新部分退款单状态;
        : 处理下账状态;
        if (老订单并且退款类型为退货退款) is (yes) then
          : 更新状态为待退货;
        endif
        : 更新退款单信息;
        if (退款单信息更新成功)) is (yes) then
          : 兼容京东到家多个部分退的情况;
          : 推送代发订单取消发货消息给服务商平台;
          : 退款单终态时，机器自动拣货订单尝试再次自动拣货;
          : 更新订单异常表示;
            note
            明细全部替换正常之后才可把订单的异常标识清理
            ，可能存在多个商品不存在的明细只替换了其中一个明细，则该订单还属于异常
            end note
          : 更新订单信息;
        else (no)
          :false;
        endif
    endswitch
    : 同意退款后判是否生成逆向运费单下账;
    }
    if (同意退款) is (yes) then
      : 保存订单日志;
      : 更新完成时间;
      : 退款单日志;
      : 保存审核记录;
      : erp零售流水相关下账;
      partition "统一退款处理ERP操作" {
        if (美团部分退款) is (yes) then
          : 将美团远程退款单详情记录中的退款金额值，赋值给OMS美团退款单;
        endif
        if (销售单已下账，若非美团部分退款，则下账) is (yes) then
          note
          erp零售流水相关
          end note
          if (订单含有不调用erp标识) is (yes) then
            stop;
          endif
          : 根据订单的机构编码查询内部字典表;
          if (内部字典表不为空并且POS机模式不为科传) is (yes) then
            : 海典退款单下账;
            kill;
          endif
          switch (下账状态)
          note left
          默认是待下账,下账失败也走待下账逻辑
          end note
          case (待锁定)
            note 
            待拣货部分退款
            end note
            : 获取退款单上下文信息中的订单信息;
            : 获取退款单上下文信息中的退款单信息;
            : 根据系统订单号查询订单商品明细;
            : 根据退款单号查询退款商品明细;
            if (部分退款) is (yes) then
              : 重新获取所有商品的数量;
            endif
            : 根据退款详情中的订单号+erpCode找到原单详情;
            : 释放库存;
            if (erp 货位调整单号不为空) is (yes) then
                :接单,进行锁库存与库存释放操作;
            endif
            if (退款单的退款类型为全额退款) is (yes) then
                : 将订单下账状态修改为已取消;
            else (no)
                if (订单状态为已关闭或者已取消) is (yes) then
                  : 将订单下账状态修改为已取消;
                endif
            endif
          case (待下账)
            note right : 已拣货部分退款
            : 获取退款单上下文信息中的订单信息;
            : 获取退款单上下文信息中的退款单信息;
            : 根据系统订单号查询订单商品明细;
            : 根据退款单号查询退款商品明细;
            if (部分退款) is (yes) then
              : 删除退款商品的拣货信息;
              : 获取退货数量;
              if (退货数量 < 拣货数量) is (yes) then
                : 当前批号满足退款数量,修改拣货数量;
              else (no)
                : 当前批号不满足退款数量,该批号所有药品移除;
              endif
            else (no)
                : 全部退款,取消所有拣货;
            endif
            : 根据退款详情中的订单号+erpCode找到原单详情;
            : 释放库存;
            if (erp 货位调整单号不为空) is (yes) then
                :接单,进行锁库存与库存释放操作;
            endif
            if (退款单的退款类型为全额退款) is (yes) then
                : 将订单下账状态修改为已取消;
            else (no)
                if (订单状态为已关闭或者已取消) is (yes) then
                  : 将订单下账状态修改为已取消;
                endif
            endif
            if (订单为老订单) is (yes) then
              note right: 此处逻辑为转发雨诺逻辑,可舍弃
              : 将订单下账状态修改为待下账;
            endif
            : 更新退款单下账状态为已取消;
          case (已下账)
            : 根据订单号查询订单详情信息;
            partition "换货订单下账后退款处理" {
                if (复杂换货) is (yes) then
                    stop;
                endif
                if (不存在换货并且非同商品替换) is (yes) then
                    stop;
                endif
                : 过滤非换货不同商品的明细;
            }
            : 根据订单号查询订单支付信息;
            : 根据网店配置ID查询网店下账配置;
            : 饿百退款单的佣金校验;
            : 校验饿百佣金，会抛出异常;
            : 处理饿百零佣金退款单，获取到佣金并重算退款单后下账ERP数据错误问题;
            : 查询ERP支付方式;
            : 查询下账金额信息;
            : 设置退款流水;
            : 退款单下账预统计处理;
          case (其他状态)
            : 修改退款单下账状态为已取消;
            if (老订单) is (yes) then
                : 修改退款单下账状态为待下账;
            endif
          endswitch
        endif
      }
      : 根据userId获取系统操作人信息;
      : 根据订单号查询订单信息;
      :  当前订单是已关闭时,下账状态改为已取消;
      : 记录订单操作日志;
      : 记录退款单操作日志;
      : 单个退款单完成时,重算剩余退款单;
      : 更新商品加权成本;
      : 同意退款请求商品主动拉取erp库存;
    endif
case (退款类型为退货退款)
    note right: 卖家同意退货退款
    : 加订单操作锁;

case (退款类型为拒绝退款)
    : 加订单操作锁;
endswitch
stop

@enduml
  