# 24年第35周2024-09-27

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 8月13上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 8月13上线 17. 微商城：   - 预计7月初进入开发阶段   - 8月22上线 18. 预计7月初进入开发阶段 19. 8月22上线 20. 美团：   - 7月29号开始开发   - 8月16号提测   - 预计9月中旬上线 21. 7月29号开始开发 22. 8月16号提测 23. 预计9月中旬上线 24. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 25. 7月29号开始开发 26. 预计8月19号提测 27. 快手：   1. 9月27日开始开发   2. 预计10月12日提测 28. 9月27日开始开发 29. 预计10月12日提测 | 1. 配送：   1. 消息回调-100%   2. 接口对接-100%   3. 接口中台改造-100%   4. 27号提测   5. 待上线 2. 消息回调-100% 3. 接口对接-100% 4. 接口中台改造-100% 5. 27号提测 6. 待上线 7. 美团：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 待上线 8. 消息回调-100% 9. 接口对接-100% 10. 订单中台接口替换-100% 11. 待上线 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务: 自测中（90%）  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0% 3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 50% - 订单同步服务重构, 自测95% - 配送信息 更新 30% - 售后审核 10% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫售后审核 志明    todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕9月27日订单/商品已上线完毕 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | [心云开发支持](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgwwtYh9GKRfKBvwQ7ff?scode=AOsAFQcYAAcUR2EJBBAboAOAYLADg&tab=iv14v4) |  |  |  |  |
| 5 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 6 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 7 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 美团.net迁移 | 十一之后上线 |  |  |
| 配送.net迁移 | 十一之后上线 |  |  |
| 美团接收B2C门店商品变更 | 十一之后上线 |  |  |
| 慢病查询 |  | 已上线 |  |
| 处方单履约流程 |  | 已上线 |  |
| 京东到家医保字段更新 |  | 已上线 |  |
| 最优配送费导出功能 |  | 已上线 |  |
| Websocket增加监控日志 |  | 已上线 |  |
| B2C-WMS预占库存 |  | 已上线 |  |
| O2O金额明细单价bug |  | 已上线 |  |
| 医保对接历史原价改为促销 | 待评审 |  | 产品汪晓 |
| 物流中台 |  | 已评审,技术方案设计中; |  |
| 快手平台对接 |  | 开发中; |  |
| 对接客服系统二期IM |  | 开发中 |  |
| 线上单对接,增加成本/税率/含税成本 开发中 |  | 开发中 |  |
| 小程序购物车二期 |  | 联调中 |  |
| 内购订单支持处方药 |  | 待评审 |  |
| 对接客服系统一期 | 十一之后上线 | 测试中 | 黎琳 |
| 京东到家取消退款单 |  | 测试中 | 杨飞 |
| 临时问题 协助山西对账B2C 一良 ; 内购商城优惠券Bug 杨花; 上海历史单修复 李洋; |  |  |  |
| 测试工具-深度路由 |  | 验证中 |  |
| B2C售后单流程优化 |  |  |  |
| 测试工具-虚拟服务 |  |  |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| XXL-JOB迁移升级 | H3例外,仅剩下润康的新系统; 商品营销已上线. 应用/会员十一之后 |  |
| 网关优化 | 十一后上线验证 |  |
| 订单重构一期 | O2O订单落库流程-自测中; |  |
| Sentinel对接 | 已完成,(H3项目及order-web Jar包冲突) |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d 1,小程序购买链路优化需求开发联调 2.B2C WMS发货订单占用库存开发 提测 上线 3.O2O订单金额下账问题正逆向单 4.MQ消息堆积问题-完结** | **遗留问题** | **需求研发** **技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 慢病支持门店商品正逆单查询上线 2. 订单同步自测,95% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：** 5**d**1.商品变更B2C 门店通知，带上线2. websocket 老域名门店定位 修改 3. 产线订单问题修复 3.1 正单无法落库 代码修复上线 3.2 退款单无法进系统 url过长 .NET 代码版本太老需要升级web application 版本。 这个就不改了 节后 java 代码上线以及处理了这个问题。 3.3 9月9号美团接口出现很多获取订单详情 限流问题导致有部分订单没有落库， 对比了9号所有订单补单 5单。4. 快手对接 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：5d**1. 京东到家   1. 医保金额   2. 用户撤销订单取消消息 2. 医保金额 3. 用户撤销订单取消消息 4. 快手对接 5. 值班 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d**1. 内购优惠券退款bug 2. xxl-job迁移 完成 3. sdp服务 上线完成 4. 链路优化遗留问题 + 内购门店自提 5. IM对接 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5d**1.O2O最优配送费导出优化（已上线）2.线上B2C订单运维3.发货履约统计导出（sql方式，已完结）4.退款下账单明细生成校验refundDetail数量（已上线）5.B2C美团订单下账金额一分钱浮动原因查询6.线上单增加成本开发（开发30%）7.hydee-middle-sdp项目xx-job迁移（已上线） | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. 物流中台技术方案设计 2. xxl-job迁移 完成 3. 【B2C】指定商品指定店铺推D-ERP优化 已上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. O2O/B2C处方单流程优化 已上线 2. middle-order\payment模块xx-job迁移 已上线 3. B2C下账列表系统订单号ES查询异常Bug 已上线 4. O2O下账列表新增海典下账按钮 测试中 5. B2C海典H1中400未推送退款下账历史订单处理 处理中 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 虚拟服务开发   1. 请求url匹配器   2. 请求头匹配器   3. 数据库查询器   4. 响应头返回数据处理器 2. 请求url匹配器 3. 请求头匹配器 4. 数据库查询器 5. 响应头返回数据处理器 6. 深度路由二期开发   1. 支持非spring管理线程池，跨线程传递数据   2. rocketmq传递数据   3. 非spring管理okHttp传递数据 7. 支持非spring管理线程池，跨线程传递数据 8. rocketmq传递数据 9. 非spring管理okHttp传递数据 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |