# 科传打印小票异常问题情况汇总

1、驱动安装是选择【自定义安装模式】

说明：安装小票打印机驱动时，如果选择了驱动【推荐安装】或者【自动安装】的步骤，一般驱动会自动创建一个新的并口出来，此种情况下，科传打印小票推送到并口的功能就会出现异常，因为系统里面此时存在两个并口了，所以安装打印机驱动的时候一定要选择自定义安装，选择添加本地的并口LPD1打印机

异常处理：如果选择推荐安装，或者安装过程未出现可供选择的模式，安装完成后打开【控制面板】查看刚刚安装好的打印机，点击右键，选择【打印机属性】

选择【端口】查看打印机使用的端口，修改为系统原有LDP1端口，并删除新创建的ESDPON LDP1端口。

将端口调整到LPT1，删除下面新创建的并口

2、安装驱动后科传无法打印小票

（1）打开控制版面中【打印机列表】，查看安装的打印机正在打印的任务

如果任务里面有打印任务卡顿，尝试删除打印任务，然后重新执行打印，如果删除无效或者无法删除打印任务，此时需要重启计算机（此种情况是打印机卡机了）；

（2）如果打印机任务列表为空，此种情况直接将新安装的打印机删除了，此时科传打印小票就会正常，测试科传小票打印看看是否已经恢复，完了再到 D:\Program Files\yxtClient\printDrive 目录重新安装小票驱动，安装完成后再次测试科传和心云的小票打印；

（3）门店本来已经有USB口的小票打印机，如果门店再次安装打印机驱动，且端口选成了LPT1，这种情况就会导致门店科传无法打印小票（原则上门店本来就有USB的打印机，那么门店肯定已经安装过相关驱动了，心云就不用再安装了），解决办法：删除新安装的打印机，测试科传能否打印小票，如果不能，打开打印机列表找到USB口打印机，共享此打印机（打印机上点右键，选择“打印机属性”，在打开的打印机属性弹框中找到“共享”栏，共享此打印机），共享完成后再测试科传看看能否打印小票，如果此时还不能，就在电脑桌面新建一个xxx.bat的文件，用记事本打开文件，将以下代码粘贴到文件中

@echo off
ping 127.0.0.1
net use lpt1 /d
net use lpt1 \\%computername%\FK58

pause

注意第四行\后面的名称就是共享的打印机的名称，此名称不能带空格，完了保存，然后双击运行此文件，完了再去测试科传小票打印。