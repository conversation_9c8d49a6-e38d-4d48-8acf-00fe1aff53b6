# 【20240723】Redis迁移

# JIRA

[[ORDER-2283] Redis迁移 - 一心数科数字化产研中心-Scrum (hxyxt.com)](https://jira.hxyxt.com/browse/ORDER-2283)

# 涉及项目

| 项目名 | 原链接 | Redis Key | 备注 | 迁移情况 |
| --- | --- | --- | --- | --- |
| key | 描述 |
| business-order | redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com:6379 |  |  |  | Green无需迁移 |
| business-order-web | redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com:6379 |  |  |  | Green无需迁移 |
| business-order-b2c-third | redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com:6379 |  |  |  | Green无需迁移 |
| middle-order | redis-d732537d-8335-4503-b83e-d50834a62d33.cn-southwest-2.dcs.myhuaweicloud.com:6379  ${myEncrypt.des(eb430460fe55e1e935850eb8fb44ca7271840be95ccd12aac62a4de177c522ac)} >> ${myEncrypt.des(65d5b0df64714707076972bc2bc1a443216acc162845f052769669c42c20a90e)} | mall:order:occupyStock:{merCode}:{erpCode} | 未付款订单商品库存占用，有过期时间；hydee-middle-data-sync调用接口：/order-stock/occupyStock获取数据，查询可用库存-占用库存 |  |  |
| mall:b2cOrder:occupyStock:{merCode}:{erpCode} | 只存，没用 |  |  |
| order:COMMODITY_VIRTUAL_SALE_COUNT1:{merCode}:{commodityId} | 数据库兜底，可以迁移 |  |  |
| order:COMMODITY_VIRTUAL_SALE_COUNT1:{storeId}:{commodityId} |  |  |
| order:SETUP | 订单设置redis key数据库兜底，可以迁移 |  |  |
| order:DISTRIBUTOR_INCR_TIMESTAMP | 分销员信息缓存主要缓存distributor_info表中的增量数据，现在数据库没有数据，可以迁移 |  |  |
| order:DISTRIBUTOR_INFO:%s |  |  |
| SIGN_CONTRACT_ADDRESS | 汇付信息缓存redis拿不到会重新获取，可迁移 |  |  |
| SIGN_CONTRACT_BANK |  |  |
| mall:micro:token:{merCode} | 微问诊token缓存redis拿不到会重新获取，可迁移 |  |  |
| order:share_name_last_id | 补偿订单分享人根据redis中的order_id增量刷新订单分享人，可迁移 |  |  |
| mall:order:dailySerialNumber | 订单的每日序列流水号需讨论 |  |  |
| middle-payment | 使用redis-d732537d-8335-4503-b83e-d50834a62d33.cn-southwest-2.dcs.myhuaweicloud.com:6379 |  |  |  | Grey未使用Redis |
| pay-core | redis-44bb3ec3-0fdd-4fb2-8b87-8e5bbe2ecd6d.cn-southwest-2.dcs.myhuaweicloud.com:6379 ``` 旧：ENC(u52lDY08w8lZhMJGofBOpbeG+/WqCXQVYCv9fXE8oIU=) ``` >> ENC(a9MQHIUiD/kuoUcGPzXVMT84e8yL7iIQuTvBWpBDWWk=)pre: | 统一前缀：pay_center:service:pay_service: |  | 该项目建议凌晨迁移 |  |
| channel_convert_platform:{payChannelCode}:{payType} | ``` 通道编码缓存 大key，数据库兜底，可迁移 ``` |  |  |
| order_cache:{orderCode} | 缓存商户订单号有支付中的订单迁移有风险 |  |  |
| platform_order_cache:{OrderCode} | 退款流水\支付流水 缓存用于兜底回调的job中GET：http://h3-pay-core-center.svc.k8s.pro.hxyxt.com/public/secondPhase/test/v1.1 |  |  |
| sign_order_cache: | H5收银台签名缓存 |  |  |
| qrcoded_order_cache: | 支付二维码数据缓存 |  |  |
| channel_source: | 缓存渠道来源数据库兜底 |  |  |
| access_token: | 医保支付token缓存没有则重新获取 |  |  |
| pay_center:service:make_up: | 补单处理逻辑 临时key |  |  |
| pay_center:service:pay_ticket: | 小票信息缓存有风险 |  |  |
| pay_center:service:user_info: | 国标医保用户信息缓存 |  |  |
| pay-finance | redis-44bb3ec3-0fdd-4fb2-8b87-8e5bbe2ecd6d.cn-southwest-2.dcs.myhuaweicloud.com:6379  ``` ENC(u52lDY08w8lZhMJGofBOpbeG+/WqCXQVYCv9fXE8oIU=) ``` | 统一前缀：pay_center:service:pay_service: |  |  |  |
| config_result_cache: | 缓存客户支付配置 job定时刷入|支付配置修改触发更新POST：http://h3-pay-finance-center.svc.k8s.pro.hxyxt.com/open-api/operation/enableJob |  |  |