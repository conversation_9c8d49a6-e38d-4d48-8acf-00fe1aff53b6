# 25年第24周2025-06-13

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
  - [1、重点项目/专项周进展与风险概况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-1%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE/%E4%B8%93%E9%A1%B9%E5%91%A8%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9%E6%A6%82%E5%86%B5)
  - [2、重点项目/专项进展与风险](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-2%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE/%E4%B8%93%E9%A1%B9%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9)
  - [3、成员工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-3%E3%80%81%E6%88%90%E5%91%98%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [1、重点项目/专项周进展与风险概况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-1%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE/%E4%B8%93%E9%A1%B9%E5%91%A8%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9%E6%A6%82%E5%86%B5)
- [2、重点项目/专项进展与风险](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-2%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE/%E4%B8%93%E9%A1%B9%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9)
- [3、成员工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-3%E3%80%81%E6%88%90%E5%91%98%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、系统运行监控](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E4%B8%89%E3%80%81%E7%B3%BB%E7%BB%9F%E8%BF%90%E8%A1%8C%E7%9B%91%E6%8E%A7)
- [四、质量与效率](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E5%9B%9B%E3%80%81%E8%B4%A8%E9%87%8F%E4%B8%8E%E6%95%88%E7%8E%87)
  - [1、本周发布质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-1%E3%80%81%E6%9C%AC%E5%91%A8%E5%8F%91%E5%B8%83%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
  - [2、本周代码质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-2%E3%80%81%E6%9C%AC%E5%91%A8%E4%BB%A3%E7%A0%81%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
    - [（1）本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%881%EF%BC%89%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
    - [（2）本周Sonar代码扫描质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%882%EF%BC%89%E6%9C%AC%E5%91%A8Sonar%E4%BB%A3%E7%A0%81%E6%89%AB%E6%8F%8F%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
  - [（1）本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%881%EF%BC%89%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
  - [（2）本周Sonar代码扫描质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%882%EF%BC%89%E6%9C%AC%E5%91%A8Sonar%E4%BB%A3%E7%A0%81%E6%89%AB%E6%8F%8F%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
  - [3、本周bug情况回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-3%E3%80%81%E6%9C%AC%E5%91%A8bug%E6%83%85%E5%86%B5%E5%9B%9E%E9%A1%BE)
  - [4、本周技术方案评审情况回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-4%E3%80%81%E6%9C%AC%E5%91%A8%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88%E8%AF%84%E5%AE%A1%E6%83%85%E5%86%B5%E5%9B%9E%E9%A1%BE)
  - [（1）本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%881%EF%BC%89%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
  - [（2）本周Sonar代码扫描质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%882%EF%BC%89%E6%9C%AC%E5%91%A8Sonar%E4%BB%A3%E7%A0%81%E6%89%AB%E6%8F%8F%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
- [1、本周发布质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-1%E3%80%81%E6%9C%AC%E5%91%A8%E5%8F%91%E5%B8%83%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
- [2、本周代码质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-2%E3%80%81%E6%9C%AC%E5%91%A8%E4%BB%A3%E7%A0%81%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
  - [（1）本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%881%EF%BC%89%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
  - [（2）本周Sonar代码扫描质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%882%EF%BC%89%E6%9C%AC%E5%91%A8Sonar%E4%BB%A3%E7%A0%81%E6%89%AB%E6%8F%8F%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
- [（1）本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%881%EF%BC%89%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [（2）本周Sonar代码扫描质量回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%EF%BC%882%EF%BC%89%E6%9C%AC%E5%91%A8Sonar%E4%BB%A3%E7%A0%81%E6%89%AB%E6%8F%8F%E8%B4%A8%E9%87%8F%E5%9B%9E%E9%A1%BE)
- [3、本周bug情况回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-3%E3%80%81%E6%9C%AC%E5%91%A8bug%E6%83%85%E5%86%B5%E5%9B%9E%E9%A1%BE)
- [4、本周技术方案评审情况回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-4%E3%80%81%E6%9C%AC%E5%91%A8%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88%E8%AF%84%E5%AE%A1%E6%83%85%E5%86%B5%E5%9B%9E%E9%A1%BE)
- [五、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E4%BA%94%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [六、本周成长与分享](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E4%B8%8E%E5%88%86%E4%BA%AB)
- [七、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775#id-25%E5%B9%B4%E7%AC%AC22%E5%91%A820250530-%E4%B8%83%E3%80%81%E6%9C%AC%E5%91%A8TODO)


**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 预计完成时间 | 负责人 | 备注 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

| 重点项目/专项 | 本周进展 | 下周计划 |
| --- | --- | --- |
| 微商城-交易链路切换 | 交易中台 一、购物车模块链路迁移 （区分 O2O、B2C 【内购、积分 融合至一套流程】）70%  1. 购物车基础操作（获取、加购、减购、清空、删除） 100%  2. 营销优惠券计算 （营销接口调用、优惠计算赋值） 80%  3. 营销活动计算 80%  4. 会员优惠价格计算 30%  5. 换购商品处理 0%  6. 门店处理 （已确定， 按照最近单门店方式）100%  7. OBC 模式 （待定）0%  8. 处方单处理 （是否优化原有流程待定）0%  9. 用户购物车信息迁移 0%二、结算流程迁移 70%  1. 营销活动计算   2. 会员优惠价格计算 3. O2O运费+自提运费计算 100% 4. 快递运费计算 80% 三、下单流程 0%  1. 资源扣减  2. 创订单接口  3. 创支付单接口  4. 资源回滚  5. 再来一单兼容 四、customer 对接 交易中台 | 1. 结算流程链路完成 （除去营销活动计算）  2. 下单流程 40% |
| 微商城-交易链路切换 | 支付中台 (暂停)1. 完成产品设计评审 |  |
| 微商城-小前台能力替换 | 能力迁移:  开发ing1.重写微商城新增订单流程-O2O hydee-business-order服务 开发 95% 自测 10% 2.微商城新增订单流程O2O/B2C融合到 hydee-business-order服务 0% 3.剔除微商接口中台转发 0%  3.1剔除middle-datasync-message  3.2剔除 yxt-xframe 4.整合微商城新增订单 给2整合一起 0% 5.复写middle-order能力 0% 6.剔除middle-order 0% |  |
| 新老模型,数据双向同步 | 数据同步:  开发ing，正单数据单向同步（历史数据+增量数据）可提测1. 同步框架-100% 2. 老模型 → 新模型   1. O2O正单-90%(含自测)   2. O2O逆单-90%(含自测)   3. B2C正单-90%(含自测)   4. B2C逆单-90%   5. 物流相关 3. O2O正单-90%(含自测) 4. O2O逆单-90%(含自测) 5. B2C正单-90%(含自测) 6. B2C逆单-90% 7. 物流相关 | 1. 微商城-未支付已取消订单迁移 |


## 3、成员工作情况

| 成员 | 本周 | 下周 |
| --- | --- | --- |
|  | **本周工作进展:** - **业务需求** **适老化改造 开发自测 联调**- **技术专项****重写微商城新增订单流程-O2O 开发 自测** - **其他**   **风险遗留项:** | **下周重点工作:****重写微商城新增订单流程-O2O 自测** |
|  | **本周工作进展**1. 网关优化问题解决,压测。已提测。目前无测试资源,准备下周推上线 2. 编写。已完成 3. DDD分层规范文档(module) 。文档已完成。分层骨架开发中 4. 线下订单问题线上运维 5. 线下单增加真实收银员字段需求开发  **技术专项** **其他**  **风险遗留项:** | **下周重点工作:**1. DDD分层骨架开发完成 2. 业务网关优化上线 3. 线下单增加真实收银员提测、上线 |
|  | **本周工作进展:** - **业务需求** - **O2O下账业务 ，进度不理想，考虑明天加班 45%。** - **拼多多一网店多门店调研。 整体流程变更巨大 ，需要全面调整。** - **技术专项******  - **其他**    **风险遗留项:** | **下周重点工作:**争取下周提测，原计划下周18号提测，目前看来至少延期到周五。 |
|  | **本周工作进展:** - **业务需求** - **技术专项**   - B2C退单-90% - B2C退单-90% - **其他**  **风险遗留项:** | **下周重点工作:**1. 微商城-未支付已取消订单迁移 2. canal监听配套代码完善 3. 雨诺时期订单迁移自测 |
|  | **本周工作进展:** - **业务需求**   - 店铺中台SDK封装，日志开发   - 数据迁移，双向同步，sdk接口，日志介入自测（98%） - 店铺中台SDK封装，日志开发 - 数据迁移，双向同步，sdk接口，日志介入自测（98%） - **技术专项**  - **其他**  **风险遗留项:** | **下周重点工作:**- 评价中台-门店评价一心助手端开发联调 - 门店中台一期测试支持 - 门店中台二期开发 |
|  | **本周工作进展:** - **业务需求** ****1.预警二期开发 2.用药福利接口-门店订单查询接口 3.异业联盟bug处理- **技术专项** - **其他** ****- **风险遗留项:** | **下周重点工作:** |
|  | **本周工作进展:** - **业务需求**  - **技术专项**  微商城交易链路切换： 1. 结算链路迁移开发- **其他**   **风险遗留项:** | **下周重点工作:****1. 完成结算链路开发（除营销活动计算）****2. 下单链路 40%** |
|  | **本周工作进展:** - **业务需求**  1、唯品会开发完成 2、对账二期开发（30%）- **技术专项**1、sonar扫描完成  - **其他**  1、天猫取消查询物流状态空指针问题修复   **风险遗留项:** | **下周重点工作:**1、优先唯品会（如果店铺下来提测）2、对账二期开发 |
|  | **本周工作进展:** - **业务需求**  1、云闪付接入 1.1 开发进度 80% 1.2 联调进度 60%- **技术专项******  - **其他**    **风险遗留项:**- **云闪付的账号密码迟迟未给,影响提测进度** | **下周重点工作:**``` 搭建云闪付测试环境完成真实支付流程,联调,提测 ``` |
|  | **本周工作进展:**- **业务需求**   - **B2C增加追溯码录入功能，涉及拣货复核、扫描发货、退货审核、正逆向下账单；进度：已提测；0618发版**   - **追溯码回传公域平台；进度：整理技术方案中，30%；** - **B2C增加追溯码录入功能，涉及拣货复核、扫描发货、退货审核、正逆向下账单；进度：已提测；0618发版** - **追溯码回传公域平台；进度：整理技术方案中，30%；** - **技术专项** - **其他**    **风险遗留项:****** | **下周重点工作：****追溯码回传公域平台，进入开发阶段。** |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 |
| --- | --- | --- |
| 1 | 系统资源 | 需要 |
| 2 | 稳定性建设 | 需要 |
| 3 | 风险预警 | 暂定 |
| 需要 |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |
| 5 | CaseStudy |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**[线上发布质量统计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=46274968)

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 | SELECT id,create_by,create_time,update_by,update_time,oms_order_no,waybill_code,document,platform_code,wp_code,logistic_config_id,ds_online_store_express_merchant_id,ext_oms_order_no,print_num,status FROM logistic_order WHERE status=1 AND waybill_code IN ('YT8763095988417') AND status = 1; |  |  |  |
| 2 | business-gateway timeout [url:/b2c/1.0/order/page/getGoodsCounts](http://url/b2c/1.0/order/page/getGoodsCounts) |  |  | ``` 关联查询数据库获取总量 ``````  `````` select <include refid="orderJoinColumnsB2C"/> <include refid="orderDeliveryAddressColumns"/>, <include refid="orderPayInfoColumns"/> FROM oms_order_info a LEFT JOIN platform_order_info poi on poi.olorderno = a.third_order_no AND poi.ectype = a.third_platform_code LEFT JOIN order_delivery_address oda ON a.oms_order_no = oda.oms_order_no LEFT JOIN order_pay_info opi ON a.oms_order_no = opi.oms_order_no where a.mer_code=#{param.merCode} and deleted=0 and exists ( select 1 from order_detail d where d.oms_order_no = a.oms_order_no and d.status != 2 <if test="param.commodityCode != null and param.commodityCode != ''">     and d.erp_code LIKE CONCAT('%',#{param.commodityCode,jdbcType=VARCHAR},'%') </if> <if test="param.commodityName != null and param.commodityName != ''">     and d.commodity_name LIKE CONCAT('%',#{param.commodityName,jdbcType=VARCHAR},'%') </if> ) <![CDATA[ and   NOT EXISTS (     SELECT         1     FROM         oms_order_ex ex     WHERE         ex.operate_status = 0         AND ex.oms_order_no = a.oms_order_no     ) and a.order_status< 100 and a.order_status != 20 ]]> <if test="param.orderState != null ">     and a.order_status=#{param.orderState} </if> <if test="param.warehouseId != null and param.warehouseId != ''">     and a.warehouse_id=#{param.warehouseId} </if> <if test="param.onlineStoreCode != null and param.onlineStoreCode != ''">     and a.online_store_code=#{param.onlineStoreCode} </if> <if test="param.isPrescription != null ">     and a.is_prescription=#{param.isPrescription} </if> <if test="param.platformCode != null and param.platformCode != ''">     and a.third_platform_code=#{param.platformCode} </if> <if test="param.warehouseIdSet != null and param.warehouseIdSet.size() > 0">     and a.warehouse_id in     <foreach collection="param.warehouseIdSet" item="id" index="index" open="(" close=")" separator=",">         #{id}     </foreach> </if> <if test="param.onlineStoreCodeSet != null and param.onlineStoreCodeSet.size() > 0">     and a.online_store_code in     <foreach collection="param.onlineStoreCodeSet" item="id" index="index" open="(" close=")" separator=",">         #{id}     </foreach> </if> <if test="param.platformCodeSet != null and param.platformCodeSet.size() > 0">     and a.third_platform_code in     <foreach collection="param.platformCodeSet" item="id" index="index" open="(" close=")" separator=",">         #{id}     </foreach> </if> <if test="param.orderNo != null and param.orderNo != ''">     and a.oms_order_no=#{param.orderNo} </if> <if test="param.expressNo != null and param.expressNo != ''">     and a.express_number=#{param.expressNo} </if> <if test="param.receiverPhone != null and param.receiverPhone != ''">     and oda.receiver_mobile=#{param.receiverPhone} </if> <if test="param.thirdOrderNo != null and param.thirdOrderNo != ''">     and a.third_order_no=#{param.thirdOrderNo} </if> <if test="param.receiverName != null and param.receiverName != ''">     and oda.receiver_name LIKE CONCAT('%',#{param.receiverName,jdbcType=VARCHAR},'%') </if> <if test="param.timeType == 1 and param.beginTime != null ">     <![CDATA[ and a.created >= #{param.beginTime, jdbcType=TIMESTAMP}  ]]> </if> <if test="param.timeType == 1  and param.endTime != null">     <![CDATA[ and  a.created <= #{param.endTime,jdbcType=TIMESTAMP} ]]> </if> <if test="param.timeType == 2 and param.beginTime != null ">     <![CDATA[ and a.pay_time >= #{param.beginTime, jdbcType=TIMESTAMP}  ]]> </if> <if test="param.timeType == 2  and param.endTime != null">     <![CDATA[  and a.pay_time <= #{param.endTime,jdbcType=TIMESTAMP} ]]> </if> <if test="param.timeType == 3 and param.beginTime != null ">     <![CDATA[ and a.audit_time >= #{param.beginTime, jdbcType=TIMESTAMP}  ]]> </if> <if test="param.timeType == 3 and param.endTime != null">     <![CDATA[  and a.audit_time <= #{param.endTime,jdbcType=TIMESTAMP} ]]> </if> <if test="param.timeType == 4 and param.beginTime != null ">     <![CDATA[ and a.ship_time >= #{param.beginTime, jdbcType=TIMESTAMP}  ]]> </if> <if test="param.timeType == 4  and param.endTime != null">     <![CDATA[  and a.ship_time <= #{param.endTime,jdbcType=TIMESTAMP} ]]> </if> <if test="param.timeType == 5 and param.beginTime != null ">     <![CDATA[ and a.cancel_time >= #{param.beginTime, jdbcType=TIMESTAMP}  ]]> </if> <if test="param.timeType == 5  and param.endTime != null">     <![CDATA[  and a.cancel_time <= #{param.endTime,jdbcType=TIMESTAMP} ]]> </if> <if test="param.orderType == 1">     <![CDATA[ and a.is_prescription = 1 ]]> </if> <if test="param.orderType == 2">     <![CDATA[ and a.order_type = 2 ]]> </if> <if test="param.orderType == 3">     <![CDATA[ and a.order_type = 3 ]]> </if> <if test="param.orderType == 4">     <![CDATA[ and a.order_type = 4 ]]> </if> <if test="param.areas != null and param.areas != ''">     and     <foreach collection="param.areas.split(',')" index="index" item="item" open="(" separator="  " close=")">         <if test="index != param.areas.split(',').length - 1">             oda.full_address like concat('%',#{item},'%') or         </if>         <if test="index == param.areas.split(',').length - 1">             oda.full_address like concat('%',#{item},'%')         </if>     </foreach> </if> <if test="param.remark != null and param.remark != ''">     and a.remark like concat('%',#{param.remark},'%') </if> ``` |
| 3 | business-gateway timeout [url:/dscloud/1.0/ds/order/upOrderBatchNo](http://url/dscloud/1.0/ds/order/upOrderBatchNo) |  | 拣货更新批次号 |
| 4 | SELECT id,sub_company_code,store_code,store_name,rider_order_no,third_platform_code,third_order_no,order_no,order_created,delivery_type,delivery_fee_json,predict_delivery_plat_name,predict_economize_delivery_fee,real_delivery_plat_code,real_delivery_plat_name,real_delivery_fee,real_economize_delivery_fee,create_time,modify_time FROM delivery_fee_economize_record WHERE order_created >= '2025-05-12 00:00:00' AND order_created <= '2025-06-10 23:59:59' ORDER BY order_created DESC; |  |  |  |
| 5 | SELECT ooe.`id`, ooe.`oms_order_no`, ooe.`order_status`, ooe.`ex_type`, ooe.`ex_type_desc`, ooe.`ex_reason`, ooe.`operate_status`, ooe.`operator`, ooe.`create_time`, ooe.`operate_time`, ooe.`busi_time` FROM oms_order_ex ooe WHERE ooe.operate_status = 0 AND ooe.oms_order_no IN ( SELECT ooe.`oms_order_no` FROM oms_order_ex ooe LEFT JOIN oms_order_info ooi ON ooi.oms_order_no = ooe.oms_order_no WHERE ooe.ex_type = 33 AND ooe.operate_status = 0 AND ooi.mer_code = '500001' AND ooi.order_status < 100 ); |  |  |  |
| 6 | select order_state as status, count(*) as num  from order_info  where  mer_code='500001' and lock_flag=0 and order_state<100 and order_state != 20 and service_mode =  'O2O'  and organization_code in  (   'AJG5'  )     group by `status`; |  |  |  |
| 7 | SELECT IF  ( dp.platform_name IS NULL, ooi.third_platform_code, dp.platform_name ) AS thirdPlatformName,  ooi.online_store_name,  ooi.third_order_no AS third_order_no,  ifnull( poi.STATUS, '' ) AS third_order_state,  ooi.buyer_message,  ooi.seller_remark,  ooi.member_no AS buyer_name,  ooi.oms_ship_no,  ooi.created,  ooi.pay_time, IF  ( ooi.is_post_fee_order = 1, NULL, oda.receiver_name ) AS receiver_name, IF  ( ooi.is_post_fee_order = 1, NULL, oda.receiver_mobile ) AS receiver_mobile, IF  ( ooi.is_post_fee_order = 1, NULL, oda.province ) AS province, IF  ( ooi.is_post_fee_order = 1, NULL, oda.city ) AS city, IF  ( ooi.is_post_fee_order = 1, NULL, oda.district ) AS district, IF  ( ooi.is_post_fee_order = 1, NULL, oda.address ) AS address, IF  ( poi.invoice_info IS NULL, NULL, poi.invoice_info ->> '$.invoice_type' ) AS invoice_type, IF  ( poi.invoice_info IS NULL, NULL, poi.invoice_info ->> '$.invoice_name' ) AS invoice_name, IF  ( poi.invoice_info IS NULL, NULL, poi.invoice_info ->> '$.vat_taxpayer_number' ) AS vat_taxpayer_number, IF  (  poi.invoice_info IS NULL,  0,  IF  (  poi.invoice_info ->> '$.invoice_name' IS NULL,  0,  IF  ( poi.invoice_info ->> '$.invoice_name' = '', 0, 1 ))) AS need_invoice,  ooi.oms_order_no, IF  (  ooi.is_post_fee_order = 1,  6,  IF  ( ooi.is_prescription = 1, 7, ooi.order_type )) AS order_type,  ooi.is_prescription,  (  SELECT  COUNT( [ro.id](http://ro.id) )   FROM  refund_order ro   WHERE  ro.state != 102   AND ro.state != 103   AND ro.oms_order_no = ooi.oms_order_no   ) AS is_refund,  ooi.order_status,  ooi.remark, IF  ( ooi.is_post_fee_order = 1, NULL, ooi.warehouse_name ) AS warehouse_name, IF  ( ooi.is_post_fee_order = 1, NULL, ooi.express_number ) AS express_number, IF  ( ooi.is_post_fee_order = 1, NULL, ooi.express_name ) AS express_name,  ooi.audit_operator_name,  ooi.audit_time,  ooi.ship_operator_name,  ooi.ship_time,  ooi.bill_operator_name,  ooi.bill_time,  ooi.ex_operator_name,  ooi.ex_operator_time,  ooi.complete_time,  ooi.cancel_time,  ooi.is_post_fee_order,  ooi.erp_sale_no,  ooi.mer_code,  ( SELECT mer_name FROM ds_merchant_group_info mg WHERE ooi.mer_code = mg.mer_code ) AS mer_name,  ooi.order_owner_type,  ooi.spread_store_code,  ooi.supplier_code,  ooi.buyer_message,  ooi.ship_time,  ooi.express_name,  ooi.express_number,  ooi.tag,  ooi.stock_state,  ooi.settlement_status, IF  ( ooi.is_post_fee_order = 1, NULL, od.platform_sku_id ) AS platform_sku_id, IF  ( ooi.is_post_fee_order = 1, NULL, [od.id](http://od.id) ) AS order_detail_id, IF  ( ooi.is_post_fee_order = 1, NULL, od.erp_code ) AS erp_code, IF  ( ooi.is_post_fee_order = 1, NULL, od.bar_code ) AS bar_code, IF  ( ooi.is_post_fee_order = 1, NULL, od.commodity_name ) AS commodity_name, IF  ( ooi.is_post_fee_order = 1, NULL, od.commodity_spec ) AS commodity_spec,  od.commodity_count, IF  ( ooi.is_post_fee_order = 1, NULL, od.average_price ) AS commodity_cost,  od.detail_settlement_status, IF  ( ooi.is_post_fee_order = 1, NULL, od.original_price ) AS original_price, IF  ( ooi.is_post_fee_order = 1, NULL, od.price ) AS price, IF  (  ooi.is_post_fee_order = 1,  NULL,  ifnull( od.total_amount, 0.00 )) AS detailTotalAmount, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.discount_amount, 0.00 ) ) AS discount_amount, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.actual_amount, 0.00 ) ) AS actual_amount, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.adjust_amount, 0.00 ) ) AS adjust_amount,  od.discount_share,  od.actual_net_amount,  od.bill_price, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.platform_discount_fee, 0.00 ) ) AS platform_discount_fee, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.merchant_discount_fee, 0.00 ) ) AS merchant_discount_fee, IF  ( ooi.is_post_fee_order = 1, NULL, ifnull( od.brokerage_amount, 0.00 ) ) AS brokerage_amount,  (  SELECT  ro.third_status   FROM  refund_detail red  INNER JOIN refund_order ro ON ro.refund_no = red.refund_no   WHERE  ro.oms_order_no = ooi.oms_order_no   AND ro.third_status IN ( 10, 20, 30, 100, 105 )   AND ro.state != 102   AND red.order_detail_id = [od.id](http://od.id)   LIMIT 1   ) AS commodityStatus,  ( SELECT GROUP_CONCAT( DISTINCT o.ex_type_desc ) FROM oms_order_ex o WHERE o.oms_order_no = ooi.oms_order_no AND o.operate_status = 0 ) exTypeDesc,  oi.integral_flag,  opi.health_num  FROM  oms_order_info ooi FORCE INDEX ( idx_merCode_orderStatus )  LEFT JOIN order_detail od ON od.oms_order_no = ooi.oms_order_no   AND od.oms_order_no != 1   AND od.is_joint != 2   AND od.goods_type != 4   AND od.STATUS != 2  LEFT JOIN order_delivery_address oda ON oda.oms_order_no = ooi.oms_order_no   AND oda.oms_order_no != 0  LEFT JOIN platform_order_info poi ON poi.olorderno = ooi.third_order_no   AND poi.ectype = ooi.third_platform_code  LEFT JOIN ds_platform dp ON dp.platform_code = ooi.third_platform_code   AND dp.service_mode = 'B2C'  LEFT JOIN order_pay_info opi ON opi.oms_order_no = ooi.oms_order_no   AND opi.oms_order_no != 0  LEFT JOIN order_info oi ON oi.order_no = ooi.order_no   AND oi.service_mode = 'B2C'  WHERE  ooi.deleted = 0   AND ooi.order_status IN ( 5, 10, 15, 30, 40, 100, 101, 102, 0, 91, 92, 93 )   AND ooi.mer_code = '500001'   AND ooi.ship_time >= '2025-06-12 00:00:00'   AND ooi.ship_time <= '2025-06-12 23:59:59'   AND ooi.warehouse_id IN ( 'WSC1111', '10062647', '100140089', '100517649', '10063287', '10056760', '1775450640261033985' )   AND ooi.online_store_code IN ( 'WSC1111',  'c5b3ff4929274602a72b07d665a13a06'   ) | 订单明细导出，暂不处理``` selectExportListMaskDetail查询sql ``` |  |  |
| 8 | SELECT  a.oms_order_no,  a.client_code  FROM  oms_order_info a  WHERE  1 = 1   AND a.deleted = 0   AND a.mer_code = '500001'   AND a.order_status IN ( 10, 30, 40, 100 )   AND a.is_post_fee_order = 0   AND a.ex_status = 0   AND a.oms_order_no LIKE concat( '1834776152794148871', '%' )   AND a.warehouse_id IN ( 'WSC1111', '10064406' )   AND a.online_store_code IN ( 'WSC1111', '501b12cbc3fe4becab17c47c8ba57638', 'b24e28310fec4026ac34eb6b43128623', '5a216d92736a49c0b1e69c73ffec8c39', 'ed7087a1ea9f42c4b6bd8aeffa798570', '160a585d8af24bdfa0c47a056785a431' )   AND a.third_platform_code IN ( '24', '3008', '27', '3004', '3003', '43' )  ORDER BY  a.created DESC; |  |  | 服务：business-order-web位置：OrderInfoMapper >> selectOrderPage<select id="selectOrderPage" resultMap="OrderPageResultMap">     select     a.oms_order_no,     a.client_code     FROM     oms_order_info a     where     1=1     <include refid="selectOrderPageWhere"/> </select>这段代码主要是为了查询出符合条件的oms订单号，问题主要在于 a.oms_order_no LIKE concat( '1834776152794148871', '%' ) 排查了调用方，1 是系统内部调用，但参数传的都是完整的omsOrder2 B2C订单页面列表查询，不过现在都是走ES查询，兜底会走数据库，不过也无需做成右模糊查询 所以可以直接修改 oms_order_no 的匹配方式，直接使用 = 即可 |


[赞](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=73441775)成为第一个赞同者