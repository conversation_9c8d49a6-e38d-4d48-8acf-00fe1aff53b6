# 24年第19周2024-05-31

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参  5月24日进入开发阶段 | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段 7. 5月28号进入开发阶段 8. 支付宝：   - TBD 9. TBD |  | 下一月的目标：支付宝 + 饿了么 |
| 3 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 4 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档 | 暂停 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 5 | 支付中台重构 |  |  | 暂停 |  |
| 6 | [需求池](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgFQpYWtHhRbWSdBxb5u?scode=AOsAFQcYAAcFpJng4uAboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 7 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 8 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 9 | [2024Q2-交易生产组](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgA9Okm682QqKMEwhssg?scode=AOsAFQcYAAc3lcl7J9AboAOAYLADg&tab=9cjz4i) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 对接极兔/邮政 | 周四上线 | 周四上线 | 钰斌 |
| B2C员工业绩返利 | 周四上线 | 周四上线 | 国枫 |
| B2C退款流程优化 | 周一技术方案评审 | 开发中30% 6月19日提测 |  |
| 订单优化2效率提升 | 周二PRD评审 | 下周三技术方案评审 |  |
| 虚拟商品需求 | 周二PRD评审 | 开发中0% 6月19日提测 |  |
| B2C手工单 功能修复 | 周内上线 | 周二上线 | 国华 |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| B2C手工单修复 | 已处理 | 国华 |
| 组织结构冗余 | 已处理.历史数据已修复, ；/正逆单全路径冗余 |  |
| es/mongo/db 数据量文档填写 | 已处理 | 国华 润康 |
| 订单推送消息改造指定 userid | 已处理 | 国华 |
| 云仓订单物流信息回传重写逻辑 | 开发中20% | 国华 |
| 广播模式mq迁移 | 进度0% |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d****1.员工推广BUG修改以及上线****2.云仓虚拟商品核销PRD评审****3.云仓虚拟商品核销技术方案编写/评审/表设计****3.订单线上金额问题 B2C商品库存扣减问题 erp下账问题** | **遗留问题** **风险问题**   **** | **需求研发****1.云仓虚拟商品核销****技术建设** **** |  |
| 2 | 杨润康 | **本周总工时：5d**1. 网关相关   1. 解决businesses-gateway上传请求体大小导致的部分接口异常的问题   2. 【网关token配置统一收口】[梳理api网关token配置后续流程文档](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18625449) 2. 解决businesses-gateway上传请求体大小导致的部分接口异常的问题 3. 【网关token配置统一收口】[梳理api网关token配置后续流程文档](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18625449) 4. [创单服务原逻辑文档梳理完成](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=31203448) 5. 线下单   1. 分表数量扩大到256张表   2. 新增字段开发   3. [同步hana线下单数据字段映射文档,验证字段数据正确性](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32311628)(下周二会找赵兴堂再核对一遍)   4. 迁移会员查消费记录接口,已完成 6. 分表数量扩大到256张表 7. 新增字段开发 8. [同步hana线下单数据字段映射文档,验证字段数据正确性](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32311628)(下周二会找赵兴堂再核对一遍) 9. 迁移会员查消费记录接口,已完成 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** | **遗留问题** **风险问题** **** | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：5day**1. **.net重构**   1. **抖店部分配合测试（订单部分已测试完，商品90%）**   2. **饿了么进入开发阶段** 2. **抖店部分配合测试（订单部分已测试完，商品90%）** 3. **饿了么进入开发阶段** | **遗留问题**抖店下周二上线**风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：**1. 修复B2C员工推广Bug 2. 云仓虚拟商品 3. 完善配送信息更新活动图 4. 修复拼团下单失败问题 5. 查询es同步订单字段缺失问题 | **遗留问题**1. 云仓虚拟商品 2. 订单中台重构 **风险问题**1. es同步订单字段缺失导致无法下账 **** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：** | **遗留问题** **风险问题** **** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. B2C下账增加成本中心 已上线 2. 路由遗留问题(下账列表未全部使用下单门店) 已上线 3. B2C退款流程优化  a. 技术方案评审 已完成 b. B2C下账单生成 开发完成 4. 订单线上运维 a. 微商城退款 b. b2c快递编码维护 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：**1. B2c取消订单/部分退款优化  a. 售后单流程梳理 b. B2C店铺配置调整 2. 海典调拨单对接 开发完成 3. 下账失败Bug修改 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：45phd**1. B2C/云仓商品 邮政对接/极兔对接、设置默认快递二期上线 2. hydee-business-order-b2c-third读取配置优化 3. 拼多多，京东订单测试环境数据打通 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |