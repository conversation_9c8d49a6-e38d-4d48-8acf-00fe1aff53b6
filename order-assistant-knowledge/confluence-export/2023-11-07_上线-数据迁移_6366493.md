# 2023-11-07 上线-数据迁移

- ## 数据库 DDL


| 相关服务 | SQL执行脚本 | 负责人 |
| --- | --- | --- |
| hydee-business-order-web | ``` alter table dscloud.biz_action_log modify column biz_no varchar(255) comment '业务单据号'; `````` alter table dscloud.order_info     alter column order_is_new set default '1'; ``` |  |
| hydee-business-order | 1. ALTER TABLE `dscloud`.`refund_detail`  MODIFY COLUMN `actual_net_amount` decimal(16, 4) NOT NULL COMMENT '下账金额' AFTER `refund_count`, MODIFY COLUMN `bill_price` decimal(16, 4) NOT NULL COMMENT '下账价格' AFTER `actual_net_amount`, MODIFY COLUMN `origin_detail_price` decimal(16, 4) NULL COMMENT '订单原价' AFTER `unit_refund_price`;2.``` CREATE TABLE `migration_log` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',   `old_order_no` varchar(100) DEFAULT '' COMMENT '老系统订单号',   `migration_type` varchar(10) DEFAULT '' COMMENT 'O2O ;B2C;CLOUD; ',   `order_type` varchar(10) DEFAULT '' COMMENT 'order; refund ',   `old_create_time` datetime  default null COMMENT '老系统创建时间,有就填,没就忽略',   `new_order_no` varchar(100) DEFAULT '' COMMENT '新系统内部订单号',   `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',   `organization_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店名称',   `req_content` json NOT NULL COMMENT '请求参数json',   `data_version` bigint  DEFAULT '0' COMMENT '数据版本,有就填,没就忽略',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',   `message_id`  varchar(200)  NULL COMMENT '消息队列ID',   PRIMARY KEY (`id`),   KEY `idx_old_order_no` (`old_order_no`) USING BTREE COMMENT '老系统订单号' ) ENGINE=InnoDB AUTO_INCREMENT=23728021 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='迁移日志表'; 3. `````` alter table dscloud.inner_store_dictionary     add token varchar(255) null comment '生成token'; alter table dscloud.inner_store_dictionary     add expiration_time bigint null comment 'token过期时间'; ``` |  |
| hydee-middle-order | 1. ALTER TABLE `middle_order`.`return_quest`  ADD COLUMN `migration_refund_no` varchar(64) NULL COMMENT '平台迁移退款单号-旧系统迁移数据的退款单号' AFTER `wx_refund_no`; |  |


- ## 代码整体封板上线


| 项目名称 | 合并至release | 提交合并请求到master | 负责人 |
| --- | --- | --- | --- |
| hydee-business-order-web | -   -     -       - 7 complete     - 7 complete     - 7 complete   -     - 7 complete   - 7 complete   -     - 7 complete   - 7 complete   - 7 complete -   -     - 7 complete   - 7 complete   - 7 complete -   - 7 complete - 7 complete | -   -     -       -         - 8 complete       - 8 complete       - 8 complete     -       - 8 complete     - 8 complete     -       - 8 complete     - 8 complete     - 8 complete   -     -       - 8 complete     - 8 complete     - 8 complete   -     - 8 complete   - 8 complete   -     -       - 8 complete     - 8 complete     - 8 complete   -     - 8 complete   - 8 complete   -     - 8 complete   - 8 complete   - 8 complete -   -     -       - 8 complete     - 8 complete     - 8 complete   -     - 8 complete   - 8 complete   -     - 8 complete   - 8 complete   - 8 complete -   -     - 8 complete   - 8 complete   - 8 complete -   - 8 complete - 8 complete |  |
| hydee-business-order | -   -     -       - 9 incomplete     - 9 incomplete     - 9 incomplete   -     - 9 incomplete   - 9 incomplete   -     - 9 incomplete   - 9 incomplete   - 9 incomplete -   -     - 9 incomplete   - 9 incomplete   - 9 incomplete -   - 9 incomplete - 9 incomplete | -   -     -       -         - 10 incomplete       - 10 incomplete       - 10 incomplete     -       - 10 incomplete     - 10 incomplete     -       - 10 incomplete     - 10 incomplete     - 10 incomplete   -     -       - 10 incomplete     - 10 incomplete     - 10 incomplete   -     - 10 incomplete   - 10 incomplete   -     -       - 10 incomplete     - 10 incomplete     - 10 incomplete   -     - 10 incomplete   - 10 incomplete   -     - 10 incomplete   - 10 incomplete   - 10 incomplete -   -     -       - 10 incomplete     - 10 incomplete     - 10 incomplete   -     - 10 incomplete   - 10 incomplete   -     - 10 incomplete   - 10 incomplete   - 10 incomplete -   -     - 10 incomplete   - 10 incomplete   - 10 incomplete -   - 10 incomplete - 10 incomplete |  |
| hydee-middle-order | -   -     -       - 11 complete     - 11 complete     - 11 complete   -     - 11 complete   - 11 complete   -     - 11 complete   - 11 complete   - 11 complete -   -     - 11 complete   - 11 complete   - 11 complete -   - 11 complete - 11 complete | -   -     -       -         - 12 complete       - 12 complete       - 12 complete     -       - 12 complete     - 12 complete     -       - 12 complete     - 12 complete     - 12 complete   -     -       - 12 complete     - 12 complete     - 12 complete   -     - 12 complete   - 12 complete   -     -       - 12 complete     - 12 complete     - 12 complete   -     - 12 complete   - 12 complete   -     - 12 complete   - 12 complete   - 12 complete -   -     -       - 12 complete     - 12 complete     - 12 complete   -     - 12 complete   - 12 complete   -     - 12 complete   - 12 complete   - 12 complete -   -     - 12 complete   - 12 complete   - 12 complete -   - 12 complete - 12 complete |  |


- ## 配置校验


**1. appolo 配置变更**

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-business-order-web | application.yml | #b2c 订单类型与商铺映射   storeInfoList:  - storeNo: '1234567'  onlineClientCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  onlineStoreCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  orderType: '08'  paymentMethod: 'WXPAY'  desc: '一心到家（微信支付）' - storeNo: '1234567'  onlineClientCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  onlineStoreCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  orderType: '08'  paymentMethod: 'VOUCHER'  desc: '一心到家（现金券支付）' - storeNo: '1234567'  onlineClientCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  onlineStoreCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  orderType: '09'  paymentMethod: 'INTEGRAL'  desc: '一心到家（积分支付）' - storeNo: '638993'  onlineClientCode: '8b1ce16a83ec41e99dbd982ec5afa6cc'  onlineStoreCode: '8a81391afd4941cf996c3cc097b729d8'  orderType: '84'  paymentMethod: 'GN'  desc: '京东鸿翔官方旗舰店' - storeNo: '89803'  onlineClientCode: '8afb30d5882245d6a56b031f276bf45e'  onlineStoreCode: '8a81391afd4941cf996c3cc097b729d8'  orderType: '88'  paymentMethod: 'GN'  desc: '京东一心堂大药房旗舰店' - storeNo: '11437628'  onlineClientCode: '8afb30d5882245d6a56b031f276bf45e'  onlineStoreCode: '8a81391afd4941cf996c3cc097b729d8'  orderType: '8F'  paymentMethod: 'GN'  desc: '京东一心堂大药房拼购旗舰店' - storeNo: '149394973'  onlineClientCode: '228f78df4b0e4fbabf40bf421c519fb8'  onlineStoreCode: '228f78df4b0e4fbabf40bf421c519fb8'  orderType: '87'  paymentMethod: 'GP'  desc: '拼多多鸿翔大药房旗舰店' - storeNo: '309799774'  onlineClientCode: '228f78df4b0e4fbabf40bf421c519fb8'  onlineStoreCode: '228f78df4b0e4fbabf40bf421c519fb8'  orderType: '89'  paymentMethod: 'GP'  desc: '拼多多一心堂大药房旗舰店' - storeNo: '678165843'  onlineClientCode: '228f78df4b0e4fbabf40bf421c519fb8'  onlineStoreCode: '228f78df4b0e4fbabf40bf421c519fb8'  orderType: '8B'  paymentMethod: 'GP'  desc: '拼多多一心堂慢病用药大药房旗舰店' - storeNo: '620348558'  onlineClientCode: '228f78df4b0e4fbabf40bf421c519fb8'  onlineStoreCode: '228f78df4b0e4fbabf40bf421c519fb8'  orderType: '66'  paymentMethod: 'GP'  desc: '拼多多药盟大药房旗舰店' - storeNo: '997172509'  onlineClientCode: '228f78df4b0e4fbabf40bf421c519fb8'  onlineStoreCode: '228f78df4b0e4fbabf40bf421c519fb8'  orderType: '65'  paymentMethod: 'GP'  desc: '拼多多仟禧堂大药房旗舰店' - storeNo: '40712939891761152'  onlineClientCode: 'a95ce729ac0e4ac6954fd8ece55eb1b3'  onlineStoreCode: '6a90b3e98cc64dfda4d596dc2612cabf'  orderType: '8E'  paymentMethod: 'GK'  desc: '百度健康一心堂大药房旗舰店' - storeNo: '10445121'  onlineClientCode: '9e826b2defd649928c42b682cfc59a05'  onlineStoreCode: '9e826b2defd649928c42b682cfc59a05'  orderType: '8A'  paymentMethod: 'GO'  desc: '美团一心堂大药房旗舰店' - storeNo: '107522281'  onlineClientCode: '00ac9399402e4e6cbab3b432000d2b45'  onlineStoreCode: 'c846d7622e294c31b464ea96f5e872c8'  orderType: '86'  paymentMethod: 'GU'  desc: '天猫一心堂大药房旗舰店' - storeNo: '506167124'  onlineClientCode: '6a90b3e98cc64dfda4d596dc2612cabf'  onlineStoreCode: '6a90b3e98cc64dfda4d596dc2612cabf'  orderType: '8H'  paymentMethod: 'GZ'  desc: '饿了么一心堂大药房旗舰店' - storeNo: '4189554271071373'  onlineClientCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  onlineStoreCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  orderType: '8J'  paymentMethod: 'DY'  desc: '抖音一心堂医药专营店' - storeNo: 'wxf2e287575f5ee688'  onlineClientCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  onlineStoreCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  orderType: '8K'  paymentMethod: 'SP'  desc: '视频号一心堂店' - storeNo: '16342645'  onlineClientCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  onlineStoreCode: 'cfd72f8f55ec4977b7918d3f2bb5d977'  orderType: '71'  paymentMethod: 'GO'  desc: '上海-美团一心堂大药房旗舰店' | 订单类型与店铺的映射，店铺迁移后需要重新对一遍线上店铺编码 |
| hydee-business-order | application.yml | ``` message-notify:  o2o-order-middle-topic: BUSINESS_CLOUD_O2O_ORDER_MIDDLE_MESSAGE  o2o-refund-order-middle-topic: BUSINESS_CLOUD_O2O_REFUND_ORDER_MIDDLE_MESSAGE ``` | 往订单前台迁移使用MQ的topic |
| threadpool节点新增： ynOrderMoveCoreSize: 8 ynOrderMoveMaxSize: 32 ynOrderMoveKeepAliveTime: 100 ynOrderMoveCapacity: 64 | o2o逆向单迁移线程池配置 |
| hydee-middle-order | application.yml | ``` datasync:  o2o-order-middle-topic: BUSINESS_CLOUD_O2O_ORDER_MIDDLE_MESSAGE  o2o-refund-order-middle-topic: BUSINESS_CLOUD_O2O_REFUND_ORDER_MIDDLE_MESSAGE ``` | 往订单前台迁移使用MQ的topic |


 **2. MQ 配置变更**

**2.1 ROCKETMQ 配置变更**

| 变更项 | 说明 |
| --- | --- |
| TOPIC: BUSINESS_CLOUD_O2O_ORDER_MIDDLE_MESSAGE | 新增正向单迁移订单前台TOPIC |
| TOPIC: BUSINESS_CLOUD_O2O_REFUND_ORDER_MIDDLE_MESSAGE | 新增逆向单迁移订单前台TOPIC |


**3. xxl-job配置变更**

| 任务 | 变更点 | 备注 |
| --- | --- | --- |
| B2C订单拉取 |  | 执行一次 |
| B2C订单解析 |  | 循环执行 |
| 云仓订单拉取 |  | 循环执行 |
| 云仓订单解析 |  | 循环执行 |


**4. 三方回调地址**

| 项目 | 配置内容 | dev回调地址 | prd回调地址 | 备注 |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |


- ## 数据迁移


**1. 数据清理脚本**

| 数据库 | 脚本 |
| --- | --- |
| mysql |  |
| mongo |  |
| es |  |
| redis |  |


**2.迁移触发方式** 

| 迁移项 | 触发方式 | 说明 | 备注 |
| --- | --- | --- | --- |
| B2C | xxl-job | 共四个任务，分别为正向单拉取，逆行单拉取，正向单解析，逆向单解析 | 先处理正向单，后处理逆向单 |
| 云仓订单 | xxl-job | 共四个任务，分别为正向单拉取，逆行单拉取，正向单解析，逆向单解析 | 先处理正向单，后处理逆向单 |


 **3.初始化配置**

| 初始化项 | 初始化方式 | 说明 |
| --- | --- | --- |
|  |  |  |
|  |  |  |


 **4. 迁移进度评估**

| 订单类型 | 迁移范围(切店未切店) | 迁移体量(数据主表多少条) | 迁移耗时（执行迁移任务到结束总耗时） | 迁移预备启动时间（细化到某一时刻，迁移哪个时间段的哪个渠道数据） | 说明 |
| --- | --- | --- | --- | --- | --- |
| B2C | 所有店铺统一迁移 | 第三方平台：10月1日之前：2879925 10月1日至今：209278微商城：2022-07至2023-10-01：261936 10月后：1422 | 预估33小时 |  |  |
| 云仓 |  | 微商城:11月1日之前 : 25932311月之后 : 581 |  |  |  |


**5. 迁移数据校验**

| 迁移项 | 校验脚本或方式 （条数校验，订单牵扯金额校验） | 说明 |
| --- | --- | --- |
| B2C | select count(1) from assignment_engine_assignment where business_type = 6005;select count(1) from assignment_engine_assignment where business_type = 6006;select sum(merchant_actual_amount) from order_pay_info where order_no in ( select order_no from order_info where service_mode = 'B2C' and online_store_code != 'WSC1111' and order_is_new = 2 );select sum(total_amount) from refund_order where order_no in ( select order_no from order_info where service_mode = 'B2C' and online_store_code != 'WSC1111' and order_is_new = 2 ); | 检查任务表拉取总数 检查订单金额总计 检查退款总金额 |
| 云仓 | select count(1) from assignment_engine_assignment where business_type = 7002;select count(1) from assignment_engine_assignment where business_type = 7004;select sum(merchant_actual_amount) from order_pay_info where order_no in ( select order_no from order_info where service_mode = 'B2C' and online_store_code = 'WSC1111' and order_is_new = 2 );select sum(total_amount) from refund_order where order_no in ( select order_no from order_info where service_mode = 'B2C' and online_store_code = 'WSC1111' and order_is_new = 2 ); |  |


**6. 迁移中异常数据的监控**