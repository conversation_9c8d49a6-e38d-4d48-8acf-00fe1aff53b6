# 【20240511】api-gateway启动过程,确认是否了使用tomcat作为容器-否

SpringBoot启动时判定Web应用的类型

步骤:

1. 如果只能找到WebFlux相关的类，则SpringBoot应用类型为`REACTIVE`；
2. 如果找不到Servlet相关的上下文类，则SpringBoot应用类型为`NONE`；
3. 如果上述条件都不满足，则SpringBoot应用类型为`SERVLET`；


网关使用的是webflux，一种非阻塞的web框架，对应spring-webflux，需要启动支持reactive的web容器，即 webApplicationType = REACTIVE

2.,会通过工厂创建

后面就是常规的启动流程了。在创建webServer时

启动完后输出日志

结论： api-gateway不使用tomcat,webflux使用netty作为容器

------------

那为什么api-gateway会配置tomcat配置,且启动时会被读取?

原因: springboot自动装配的属性配置，并不代表使用了tomcat

验证方法,移除掉tomcat配置,api-gateway网关正常