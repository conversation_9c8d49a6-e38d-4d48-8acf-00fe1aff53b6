# 24年第01周 2024-01-05

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：4day**1. 配合商品组修改自动化配置 2. 雨诺逆单迁移-金额优化 3. 同步京东到家门店时保存京东到家侧的门店id 4. middle-id项目加锁优化上线 | **㊀计划工作**1. 店铺同步自动化配置修改 2. 购物车优化1期上线 3. middle-id加锁优化上线 4. 抖店o2o开发 **㊁实际完成**1. 店铺同步自动化配置修改 2. 购物车优化1期上线 3. middle-id加锁优化上线 4. 雨诺逆单迁移-金额优化 5. 同步京东到家门店时保存京东到家侧的门店id **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 切店支持 2. 抖店o2o开发 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 | 王世达 | **本周总工时：4day**1. 海典pos 对接 1.5day 2. 门店B2C需求 1.5day 3. 保山医保退款授权码失效问题修复 1 day | **㊀计划工作**1. 海典pos 对接 2. 门店B2C需求 3. 保山医保退款问题 **㊁实际完成**1. 海典pos 对接 自测完成 2. 门店B2C需求 40% 3. 保山医保退款问题 100% **㊂遗留问题**保山医保拉取订单明细传值需要修改等待产品确认**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：3day**1. 迁移订单金额重算 2. 退款消息异常问题 | **㊀计划工作**1. 迁移订单金额重算 2. 退款消息异常问题 **㊁实际完成**1. 迁移订单金额重算 2. 退款消息异常问题 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 4 |  | **本周总工时：4day**1.处方信息透传以及处方图片的重新命名2.配合商品组修改自动化配置3.线上生产BUG | **㊀计划工作**1. 1.处方信息透传以及处方图片的重新命名2.配合商品组修改自动化配置 **㊁实际完成**1.处方信息透传以及处方图片的重新命名2.配合商品组修改自动化配置3.线上生产BUG**㊂遗留问题**1.订单成功转为自配送,但是没有操作日志2.美团的处方信息新接口只允许拉前一天的信息**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 | 杨俊峰 | **本周总工时：4day**1.京东到家门店id存储以及接口修改。1.5 day2.医保信息以及处方信息透传。 2day3.其他线上支持。 0.5 day | **㊀计划工作****㊁实际完成****2.医保信息以及处方信息透传 80%****㊃风险问题**发现美团处方信息不能获取当天的。**** | **** |  |
| 6 | 李洋 | **本周总工时：**1. pos对接 2. b2c拣货复合 3. 日常bug修改 | **㊀计划工作**1. pos对接 **㊁实际完成**1. pos对接（用测试数据对接） 2. b2c拣货复合--新增拣货标记 3. 日常bug修改--dev环境微商城无法创建订单 **㊂遗留问题** | **㊀需求研发相关**1. pos对接 2. b2c拣货复合 |  |
| 7 | 杨润康 | **本周总工时：4day**1. 根据新人指南完整相关账号申请、环境按照等 2d 2. 阅读B2C拉单代码和O2O拉单代码 1d 3. B2C拣货复核走O2O下账需求熟悉,阅读批量发货代码 1d | **㊀计划工作**1、完成新人指南事项2、阅读B2C和O2O拉单代码**㊁实际完成**1、已完成2、B2C拉单逻辑仔细阅读了一遍,逻辑较为复杂,后面有空了再仔细看一遍。O2O拉单逻辑大概看了下3、熟悉了B2C拣货复核走O2O下账的基本数据流向及页面操作,熟悉了批量发货的代码**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****1.完成B2C拣货复核走O2O下账需求以下两个功能点:**1. 获取面单 2. 批量发货 **2.借助需求熟悉整体研发流程,从开发到上线全阶段****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 切换值班

| 时间 | 值班人员 |  |
| --- | --- | --- |
| 1月9号 | 郭志明,杨国枫 |  |
| 1月10号 | 徐国华 |  |
|  |  |  |
|  |  |  |
|  |  |  |