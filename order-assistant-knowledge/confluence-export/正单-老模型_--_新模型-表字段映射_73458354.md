# 正单-老模型 --> 新模型-表字段映射

# dscloud_offline库

## offline_order

| 字段 | O2O取值来源/逻辑 | B2C |
| --- | --- | --- |
| id | 数据库自增id |
| order_no | 如果 order_info 中的 member_no 不为空，查询 member 服务获取userId，然后根据id生成order_no。否则根据 order_info >> created 生成order_no |
| parent_order_no | 无 |
| user_id | 如果 order_info 中的 member_no 不为空，查询 member 服务获取userId |
| third_platform_code | order_info >> third_platform_code |  |
| third_order_no | order_info >> third_order_no |  |
| parent_third_order_no | 无 |  |
| day_num | order_info >> day_num |  |
| order_state | 无，以 order_main_status 为主 |  |
| created | order_info >> created |  |
| created_day |  |
| updated | order_info >> modify_time |  |
| pay_time | order_info >> pay_time |  |
| bill_time | 无，以 account_order 为准 |  |
| complete_time | order_info >> complete_time |  |
| actual_pay_amount | 无，以 order_amount 为准 |  |
| actual_collect_amount | 无，以 order_amount 为准 |  |
| coupon_codes | 无 |  |
| created_by | System |  |
| updated_by | System |  |
| created_time | order_info >> created_time |  |
| updated_time | order_info >> modify_time |  |
| version | 无 |  |
| serial_no | 无 |  |
| store_code | 无，以 organization_code 为准 |  |
| migration | 无 |  |
| is_on_promotion | 无 |  |
| transaction_channel | online |  |
| business_type | order_info >> service_mode |  |
| launch_organization_code | 无 |  |
| launch_organization_name | 无 |  |
| launch_user_id | 无 |  |
| organization_code | order_info >> organization_code |  |
| organization_name | order_info >> organization_name |  |
| company_code | 根据 organization_code 调用 yxt-org-read 服务的 【/1.0/listOrgByCache】接口获取 |  |
| company_name |
| store_direct_join_type |
| lock_for_world | order_info >> lock_flag |  |
| order_main_status | RDarktrueswitch (orderStatus) {   case 5:   case 10:     return OrderMainStatus.WAIT.getStatus();   case 20:     return OrderMainStatus.WAIT_PICK.getStatus();   case 30:     return OrderMainStatus.PICKED.getStatus();   case 40:     return OrderMainStatus.SHIPPED.getStatus();   case 100:     return OrderMainStatus.DONE.getStatus();   case 102:     return OrderMainStatus.CANCELED.getStatus();   case 101:     return OrderMainStatus.CLOSED.getStatus();   default:     return OrderMainStatus.DONE.getStatus(); } |  |
| payment_status | PAID微商城：RDarktrueswitch (middle_order >> order_info >> pay_status){     case 0:         return OrderPaymentStatus.UN_PAY.getStatus();     case 1:         return OrderPaymentStatus.PAID.getStatus();     case 2:         return OrderPaymentStatus.PAYING.getStatus();     case 3:         return OrderPaymentStatus.PART_PAY.getStatus(); } |  |
| order_refund_progress | 无 |  |
| abnormal_type | RDarktrueswitch (lockFlag) {   case 31:     return OrderAbnormalType.COMMODITY_LACK.getCode();   case 32:     return OrderAbnormalType.COMMODITY_NOT_EXIST.getCode();   case 33:     return OrderAbnormalType.ORDER_DATA_ERR.getCode();   case 34:     return OrderAbnormalType.DELIVERY_ABNORMAL.getCode();   case 35:     return OrderAbnormalType.SHOP_MATCH_EX.getCode();   case 36:     return OrderAbnormalType.PRESCRIPTION_NOT_PASS.getCode();   case 37:     return OrderAbnormalType.ERP_INFO_ERR.getCode();   case 40:     return OrderAbnormalType.THIRD_ERP_EMPTY.getCode();   case 41:   case 42:     return OrderAbnormalType.NOT_MATCHED.getCode();   default:     return OrderAbnormalType.UNKNOWN.getCode(); } |  |
| order_type | 不维护，合并到 order_tag_segment 中 |  |
| order_tag_segment | RDarktrueif (StrUtil.equals("1", oldOrderInfo.getIntegralFlag())) {   orderTagSegment.addTag(OrderTagType.INTEGRAL); } if (ObjectUtil.equals(1, oldOrderInfo.getAppointment())) {   orderTagSegment.addTag(OrderTagType.APPOINTMENT); } if (ObjectUtil.equals(1, oldOrderInfo.getMedicalInsurance())) {   orderTagSegment.addTag(OrderTagType.MEDICARE); } if (ObjectUtil.equals(OrderTypeEnum.FREIGHT_ORDER.getCode(), oldOrderInfo.getOrderType())) {   orderTagSegment.addTag(OrderTagType.FREIGHT); } if (StrUtil.equals("1", oldOrderInfo.getNewCustomerFlag())) {   orderTagSegment.addTag(OrderTagType.NEW_CUSTOMER); } if (ObjectUtil.equals(1, oldOrderInfo.getPrescriptionFlag())) {   orderTagSegment.addTag(OrderTagType.PRESCRIPTION); } if(ObjectUtil.equals(OrderTypeEnum.VIP_ORDER.getCode(), oldOrderInfo.getOrderType())){   orderTagSegment.addTag(OrderTagType.RECHARGE_VIP); }  对于微商城，还有如下逻辑： String orderType = this.getOrderType(); switch (orderType){     case "G":         orderTagSegment.addTag(OrderTagType.GROUP);         break;     case "I":         orderTagSegment.addTag(OrderTagType.INTEGRAL);         break;     case "A":         orderTagSegment.addTag(OrderTagType.APPOINTMENT);         break;     case "F":         orderTagSegment.addTag(OrderTagType.DISTRIBUTION);         break;     case "C":         orderTagSegment.addTag(OrderTagType.CLOUD);         break;     case "IP":         orderTagSegment.addTag(OrderTagType.IN_PURCHASE);         break;     case "VIP":         orderTagSegment.addTag(OrderTagType.RECHARGE_VIP);         break;     case "MW":         orderTagSegment.addTag(OrderTagType.MEDICATION_WELFARE);         break; } if(this.getIsNewCustomer() != null && this.getIsNewCustomer() == 1){     orderTagSegment.addTag(OrderTagType.NEW_CUSTOMER); } if(this.getPrescriptionSheetMark() != null && this.getPrescriptionSheetMark() == 1){     orderTagSegment.addTag(OrderTagType.PRESCRIPTION); } if(this.getPresaleOrderType() != null && this.getPresaleOrderType() != -1){     orderTagSegment.addTag(OrderTagType.PRE_SALE); } |  |
| source_biz_code | 无 |  |
| source_scene | B2C：RDarktrue    Integer orderType = oldOmsOrderInfo.getOrderType();     if(ObjectUtil.isNull(orderType)){       return null;     }     switch (orderType){       case 2:         return OrderSourceScene.IMPORT_ORDER.getSourceScene();       case 3:         return OrderSourceScene.MANUAL_ORDER.getSourceScene();       case 5:         return OrderSourceScene.MANUAL_REISSUE.getSourceScene();       default:         return null;     } |  |
| source_channel | 无 |  |
| source_device | 无 |  |
| mer_code | order_info >> mer_code |  |
| online_store_code | order_info >> online_store_code |  |
| online_store_name | order_info >> online_store_code |  |
| prescription_audit_status | RDarktrue    if (orderPrescription.getStatus() == null) {       return OrderPrescriptionAuditStatus.WAIT_REVIEW.getStatus();     } else if (orderPrescription.getStatus() == 1) {       return OrderPrescriptionAuditStatus.REVIEW_PASS.getStatus();     } else {       return OrderPrescriptionAuditStatus.REVIEW_REJECT.getStatus();     } |  |
| booking_flag | 不维护，合并到 order_tag_segment 中 |  |
| booking_time_start | order_info >> delivery_time_desc |  |
| booking_time_end |  |
| abnormal_message | order_info >> lock_msg |  |
| buyer_message | order_info >> buyer_message |  |
| seller_remark | order_info >> seller_remark |  |
| is_valid | 1 |  |
| sys_create_time | 当前时间 |  |
| sys_update_time | 当前时间 |  |
| **extend_info** |
| deliveryType | order_info >> extend_info |  |
| isMeiTuanQKP |  |
| orderTagExt |  |
| procurementNo | 无 |  |
| freightOrderNo | 在进行订单同步时，如果 order_info >> freight_order_no 有值，则先将 运费单 构建出来，取 新运费单 的orderNo |  |
| buyerName | order_info >> buyer_name |  |
| joinWms | 无 |  |
| warehouseId | 无 |  |
| warehouseName | 无 |  |
| ydjOrderExt.spreadStoreCode | middle_order >> order_info >> spread_store_code |  |
| ydjOrderExt.spCode | middle_order >> order_info >> sp_code |  |
| ydjOrderExt.shareName | middle_order >> order_info >> share_name | **** |
| ydjOrderExt.empCode | middle_order >> order_info >> emp_code | **** |
| ydjOrderExt.invitationCode | middle_order >> order_info >> invitation_code | **** |
| ydjOrderExt.staff | middle_order >> order_info >> is_staff | **** |
| ydjOrderExt.cancelName | middle_order >> order_info >> cancel_name | **** |
| ydjOrderExt.cancelTime | middle_order >> order_info >> cancel_time | **** |
| ydjOrderExt.cancelReason | middle_order >> order_info >> cancel_reason | **** |


## offline_order_amount

| 字段 | O2O取值来源/逻辑 | B2C取值来源/逻辑 |
| --- | --- | --- |
| id | 数据库自增id |  |
| order_no | offline_order_info >> order_no |
| actual_pay_amount | order_pay_info >> buyer_actual_amount |  |
| actual_collect_amount | order_pay_info >> merchant_actual_amount |  |
| brokerage_amount | order_pay_info >> brokerage_amount |  |
| total_amount | order_pay_info >> total_amount |  |
| delivery_amount | order_pay_info >> delivery_fee |  |
| pack_amount | order_pay_info >> pack_fee |  |
| merchant_pack_amount | order_pay_info >> merchant_pack_fee |  |
| merchant_delivery_amount | order_pay_info >> merchant_delivery_fee |  |
| merchant_delivery_discount_amount | order_pay_info >> merchant_delivery_fee_discount |  |
| merchant_order_discount_amount | order_pay_info >> merchant_total_discount_sum |  |
| merchant_commodity_discount_amount | order_pay_info >> merchant_total_discount_sum_not_delivery_fee |  |
| platform_pack_amount | order_pay_info >> platform_pack_fee |  |
| platform_delivery_amount | order_pay_info >> platform_delivery_fee |  |
| platform_delivery_discount_amount | order_pay_info >> platform_delivery_fee_discount |  |
| platform_order_discount_amount | order_pay_info >> platform_discount |  |
| platform_commodity_discount_amount | order_pay_info >> platform_discount - platform_delivery_fee_discount |  |
| medicare_amount | order_pay_info >> medicare_amount |  |
| remain_brokerage_amount | order_pay_info >> remain_brokerage_amount |  |
| created | order_pay_info >> create_time |  |
| updated | order_pay_info >> modify_time |  |
| created_by | System |  |
| updated_by | System |  |
| sys_create_time | 当前时间 |  |
| sys_update_time | 当前时间 |  |
| version | 1 |  |


## offline_order_cashier_desk

| 字段 | O2O取值来源/逻辑 | B2C取值来源/逻辑 |
| --- | --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| pos_cashier_desk_no | 无 |
| cashier | 无 |
| cashier_name | 无 |
| picker | order_info >> pick_operator_id |
| picker_name | order_info >> pick_operator_name |
| shift_id | 无 |
| shift_date | 无 |
| created_by | 无 |
| updated_by | 无 |
| created_time | offline_order_info >> created_time |
| updated_time | offline_order_info >> updated_time |
| version | 无 |
| reseller | 无 |
| reseller_name | 无 |
| created | offline_order_info >> created |
| updated | offline_order_info >> updated |
| sys_create_time | System |
| sys_update_time | System |
| real_cashier | 无 |
| real_cashier_name | 无 |


## offline_order_coupon

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| erp_code | 无 |
| commodity_count | 无 |
| third_order_no | offline_order_info >> third_order_no |
| coupon_no | middle_order >> order_coupon >> coupon_code |
| open_code | null |
| coupon_name | middle_order >> order_coupon >> coupon_name |
| coupon_type | middle_order >> order_coupon >> coupon_typeRDarktrue  case 1:     return OrderCouponType.DISCOUNT.name();   case 2:     return OrderCouponType.PRICE_DEDUCTION.name();   case 4:   case 5:     return OrderCouponType.CASH.name();   default:     return null; |
| coupon_denomination | middle_order >> order_coupon >> denomination |
| used_coupon_amount | RDarktrue  case 1:     return middle_order >> order_coupon >> coupon_deduction;   case 2:     return middle_order >> order_coupon >> coupon_money;   case 4:   case 5:     return middle_order >> order_coupon >> coupon_money;   default:     return null; |
| extend_json | middle_order >> order_coupon |
| type | ORDER |


## offline_order_promotion

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| erp_code | 无 |
| commodity_count | 无 |
| third_order_no | offline_order_info >> third_order_no |
| promotion_no | middle_order >> order_market_activity >> activity_id |
| sub_promotion_no | 无 |
| promotion_type | middle_order >> order_market_activity >> activity_type |
| promotion_amount | middle_order >> order_market_activity >> activity_money |
| extend_json | middle_order >> order_market_activity |
| type | ORDER |


## offline_order_delivery_address

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| receiver_code | 无 |
| receiver_name | order_delivery_address >> receiver_name |
| receiver_phone | order_delivery_address >> receiver_mobile |
| receiver_landline_phone | order_delivery_address >> receiver_telephone |
| province | order_delivery_address >> province |
| city | order_delivery_address >> city |
| district | order_delivery_address >> district |
| town | order_delivery_address >> town |
| address | order_delivery_address >> address |
| zip_code | order_delivery_address >> zip_code |
| full_address | order_delivery_address >> full_address |
| original_full_address | order_delivery_address >> original_full_address |
| third_oaid | order_delivery_address >> oaid |
| receiver_name_privacy | order_delivery_address >> receiver_name_privacy |
| receiver_address_privacy | order_delivery_address >> receiver_address_privacy |
| receiver_phone_privacy | order_delivery_address >> receiver_phone_privacy |
| receiver_landline_phone_privacy | order_delivery_address >> receiver_phone_privacy |
| created | order_delivery_address >> create_time |
| updated | order_delivery_address >> modify_time |
| created_by | System |
| updated_by | System |
| sys_create_time | 当前时间 |
| sys_update_time | 当前时间 |
| version | 1 |


## offline_order_pay

| 字段 | 微商城取值来源/逻辑 | 其他 |
| --- | --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| pay_type | 1. middle_order >> order_info >> pay_medical_amount != null   1. pay_type = BAOSHAN-MEDICAL   2. pay_name = 保山医保支付   3. pay_amount = pay_medical_amount 2. pay_type = BAOSHAN-MEDICAL 3. pay_name = 保山医保支付 4. pay_amount = pay_medical_amount 5. middle_order >> order_info >> pay_sale_info != null   1. pay_type = COUPON   2. pay_name = 现金券支付   3. pay_amount = SUM(saleAmount) 6. pay_type = COUPON 7. pay_name = 现金券支付 8. pay_amount = SUM(saleAmount) 9. middle_order >> order_info >> actually_paid - pay_medical_amount - SUM(saleAmount) > 0   1. pay_type = WECHAT   2. pay_name = 微信支付   3. pay_amount = actually_paid - pay_medical_amount - SUM(saleAmount) 10. pay_type = WECHAT 11. pay_name = 微信支付 12. pay_amount = actually_paid - pay_medical_amount - SUM(saleAmount) | UNKNOW |
| pay_name | 未知 |
| pay_amount | order_pay_info >> buyer_actual_amount |
| created_by | System |
| updated_by | System |
| order_pay_no | 无 |


## offline_order_prescription

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| prescription_type | order_prescription >> prescription_type == 1 ? "WESTERN" : "EAST" |
| prescription_no | 雪花算法生成 |
| third_prescription_no | order_prescription >> rp_id |
| drug_user_name | order_prescription >> usedrug_name |
| drug_user_sex | order_prescription >> sex |
| drug_user_birthday | order_prescription >> birthday |
| drug_user_age | order_prescription >> use_age |
| drug_user_phone | order_prescription >> phone_number |
| drug_user_id_card_no | order_prescription >> identity_number |
| prescription_third_url | order_prescription >> picurl |
| prescription_url | order_prescription >> cfpicurl |
| prescription_writing_time | order_prescription >> open_time |
| prescription_check_time | order_prescription >> check_time |
| prescription_checker | order_prescription >> pres_no |
| prescription_checker_name | order_prescription >> check_name |
| prescription_check_status | order_prescription >> statusif (status == null) {   return OrderPrescriptionAuditStatus.WAIT_REVIEW.getStatus(); } else if (status == 1) {   return OrderPrescriptionAuditStatus.REVIEW_PASS.getStatus(); } else {   return OrderPrescriptionAuditStatus.REVIEW_REJECT.getStatus(); } |
| prescription_remark | order_prescription >> remark |
| created | order_prescription >> create_time |
| updated | order_prescription >> modify_time |
| sys_create_time | 当前时间 |
| sys_update_time | 当前时间 |
| created_by | System |
| updated_by | System |
| created_time | order_prescription >> create_time |
| updated_time | order_prescription >> modify_time |
| version | 1 |


## offline_order_user

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| user_id | offline_order_info >> user_id |
| launch_user_id | 无 |
| user_name | 查询 member 服务获取 |
| user_card_no | 查询 member 服务获取 |
| user_mobile | 查询 member 服务获取 |
| user_tag | 无 |


## offline_order_set_detail

| 字段 | 取值来源/逻辑 |
| --- | --- |
| 需要根据 order_assemble_commodity_relation >> original_erp_code 进行分组处理 |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| order_set_detail_no | 雪花算法生成 |
| third_order_detail_no | order_info >> third_order_no |
| row_no | 无 |
| platform_sku_id | order_assemble_commodity_relation >> third_sku_id |
| erp_code | order_assemble_commodity_relation >> original_erp_code |
| erp_name | order_assemble_commodity_relation >> name |
| commodity_count | order_assemble_commodity_relation >> assemble_commodity_count |
| **从 order_detail 匹配出其 original_erp_code = order_assemble_commodity_relation >> original_erp_code 的数据进行累加** |
| original_price | sum(order_detail >> original_price) |
| price | sum(order_detail >> price) |
| commodity_cost_price | 根据 order_detail >> erp_code 从 order_commodity_detail_cost_price 中过滤数据 sum(order_commodity_detail_cost_price >> cost_price) |
| total_amount | sum(order_detail >> total_amount) |
| actual_pay_amount | sum(order_detail >> actual_amount) |
| discount_share | sum(order_detail >> discount_share) |
| discount_amount | sum(order_detail >> discount_amount) |
| merchant_order_discount_share | 无 |
| platform_order_discount_share | 无 |
| merchant_goods_discount_amount | 无 |
| platform_goods_discount_amount | 无 |
| merchant_order_discount_share_price | 无 |
| platform_order_discount_share_price | 无 |
| merchant_goods_discount_amount_price | 无 |
| platform_goods_discount_amount_price | 无 |


## offline_order_detail

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| order_detail_no | 雪花算法生成 |
| row_no | 无 |
| platform_sku_id | order_detail >> platform_sku_id |
| erp_code | order_detail >> erp_code |
| erp_name | order_detail >> commodity_name |
| commodity_count | order_detail >> commodity_count |
| status | RDarktrueswitch (order_detail >> status) {       case 0:         return OrderDetailStatus.NORMAL.getCode();       case 11:         return OrderDetailStatus.REFUND.getCode();       case 10:         return OrderDetailStatus.EXCHANGE.getCode();       default:         return OrderDetailStatus.NORMAL.getCode();     } |
| gift_type | order_detail >> is_gift == 1 ？"GIFT" : "NOT_GIFT" |
| original_price | order_detail >> original_price |
| price | order_detail >> price |
| total_amount | order_detail >> total_amount |
| discount_share | order_detail >> discount_share |
| discount_amount | order_detail >> discount_amount |
| bill_price | order_detail >> bill_price |
| bill_amount | order_detail >> actual_net_amount |
| commodity_cost_price | 根据 order_detail >> erp_code 从 order_commodity_detail_cost_price 中过滤数据 order_commodity_detail_cost_price >> cost_price |
| created_by | System |
| updated_by | System |
| created_time | order_detail >> created_time |
| updated_time | order_detail >> modify_time |
| version | 1L |
| is_on_promotion | null |
| detachable | order_detail >> chailing |
| saler_id | order_info >> pick_operator_id |
| saler_name | order_detail >> pick_operator_name |
| commodity_spec | order_detail >> commodity_spec |
| manufacture | order_detail >> manufacture |
| five_class | order_detail >> five_class |
| five_class_name | order_detail >> five_class_name |
| main_pic | order_detail >> main_pic |
| third_order_detail_no | order_detail >> third_detail_id |
| abnormal_type | RDarktrueswitch (order_detail >> status) {   case 1:     return OrderDetailAbnormalType.OUT_OF_STOCK.getCode();   case 2:     return OrderDetailAbnormalType.NOT_EXIST.getCode();   default:     return null; } |
| swap_no | 根据 order_detail >> swap_id 获取最新的order_detail_no注意，这里存的是换货后的明细编号，如:A 换成了 B，那么之前是 B 中存 A 的detailNo，现在是 A 中存 B 的detailNo |
| set_type | 根据 offline_order_set_detail >> erpCode == order_detail >> original_erp_code 条件进行匹配，获取到 offline_order_set_detail >> order_set_detail_no |
| order_set_detail_no |
| actual_pay_amount | order_detail >> actual_amount |
| merchant_order_discount_share | order_detail >> detail_discount >> merchantDiscount |
| merchant_order_discount_share_price | merchant_order_discount_share / commodity_count |
| platform_order_discount_share | order_detail >> detail_discount >> platformDiscount |
| platform_order_discount_share_price | platform_order_discount_share / commodity_count |
| merchant_goods_discount_amount | 0 |
| merchant_goods_discount_amount_price | 0 |
| platform_goods_discount_amount | 0 |
| platform_goods_discount_amount_price | 0 |
| input_tax_code | 无 |
| input_tax | 无 |
| output_tax_code | 无 |
| output_tax | 无 |
| posted_cost_with_tax_price | 无 |
| posted_cost_price | 无 |
| posted_cost_tax | 无 |
| mic_mark | order_detail >> is_medicare_item |
| created | order_detail >> create_time |
| updated | order_detail >> modify_time |
| **extend_info** |
| refundCount | order_detail >> refund_count |
| 以下为微商城专有，先根据 order_detail >> third_detail_id = middle_order >> order_detail >> id 进行匹配，若没有匹配到，则根据 erp_code + commodity_count 进行匹配 |
| ydjOrderDetailExt.packageId | middle_order >> order_detail >> package_id |
| ydjOrderDetailExt.promotionRatioId | middle_order >> order_detail >> promotion_ratio_id |
| ydjOrderDetailExt.promotionRatio | middle_order >> order_detail >> promotion_ratio |
| ydjOrderDetailExt.verificationQuantity | middle_order >> order_detail >> verification_quantity |
| ydjOrderDetailExt.isVirtual | middle_order >> order_detail >> is_virtual |
| ydjOrderDetailExt.expiring | middle_order >> order_detail >> expiring |
| ydjOrderDetailExt.vipDiscount | middle_order >> order_detail >> vip_discount |
| ydjOrderDetailExt.vipDiscountPlus | middle_order >> order_detail >> vip_discount_plus |
| ydjOrderDetailExt.spCode | middle_order >> order_detail >> sp_code |


## offline_order_detail_coupon

### 微商城

| 字段 | 取值来源/逻辑 |
| --- | --- |
|  | 根据 middle_order >> order_coupon >> coupon_products 进行构建 |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| order_detail_no | offline_order_detail >> order_detail_no |
| third_order_no | offline_order_info >> third_order_no |
| coupon_no | middle_order >> order_coupon >> coupon_code |
| coupon_name | middle_order >> order_coupon >> coupon_name |
| coupon_type | middle_order >> order_coupon >> coupon_typeRDarktrue  case 1:     return OrderCouponType.DISCOUNT.name();   case 2:     return OrderCouponType.PRICE_DEDUCTION.name();   case 4:   case 5:     return OrderCouponType.CASH.name();   default:     return null; |
| coupon_denomination | middle_order >> order_coupon >> denomination |
| used_coupon_amount | middle_order >> order_coupon >> coupon_products >> saleAmount |


## offline_order_detail_promotion

### 微商城

| 字段 | 取值来源/逻辑 |
| --- | --- |
|  | 根据 middle_order >> order_detail>> activity_discount_json 进行构建 |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| order_detail_no | offline_order_detail >> order_detail_no，根据 erp_code 进行匹配 |
| third_order_no | offline_order_info >> third_order_no |
| promotion_no | middle_order >> order_detail>> activity_discount_json >> id |
| promotion_type | 根据 middle_order >> order_detail>> activity_discount_json >> id 与 middle_order >> order_market_activity >> activity_id 进行匹配middle_order >> order_market_activity >> activity_type |
| promotion_amount | middle_order >> order_detail>> activity_discount_json >> amount |


# order_account库

## account_order

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| company_code | offline_order_info >> company_code |
| company_name | offline_order_info >> company_name |
| organization_code | offline_order_info >> organization_code |
| organization_name | offline_order_info >> organization_name |
| third_platform_code | offline_order_info >> third_platform_code |
| account_order_no | 如果 offline_order_info 中的 userId不为空，根据id生成order_no。否则根据 offline_order_info >> created 生成order_no |
| order_no | offline_order_info >> order_no |
| pos_code | POS类型经历了两个阶段，刚开始是科传，后面切换成了海典下账，O2O没有保存下账时的类型，所以这里需要特殊判断if (StrUtil.isNotBlank(oldOrderInfo.getErpSaleNo()) && StrUtil.startWithIgnoreEquals(oldOrderInfo.getErpSaleNo(), "S")) {   accountOrder.setPosCode(AccountOrderPosModeEnum.POS_KC.getCode()); } else {   InnerStoreDictionary innerStoreDictionary = oldOrderRelated.getInnerStoreDictionary();   switch (innerStoreDictionary.getPosMode()) {     case 1:       accountOrder.setPosCode(AccountOrderPosModeEnum.POS_HD_H1.getCode());       break;     case 2:       accountOrder.setPosCode(AccountOrderPosModeEnum.POS_HD_H2.getCode());       break;     default:       accountOrder.setPosCode(AccountOrderPosModeEnum.POS_KC.getCode());       break;   } } |
| pos_account_order_no | order_info >> erp_sale_no |
| account_order_status | switch (order_info >> erp_state) {   case 20:   case 30:     return AccountOrderStatus.WAIT.getStatus();   case 99:     return AccountOrderStatus.FAIL.getStatus();   case 100:     return AccountOrderStatus.SUCCESS.getStatus();   case 110:     return AccountOrderStatus.CANCEL.getStatus();   default:     return AccountOrderStatus.SUCCESS.getStatus(); } |
| buyer_actual_amount | offline_order_amount >> actual_pay_amount |
| goods_total_amount | offline_order_amount >> total_amount |
| bill_commodity_amount | erp_bill_info >> bill_commodity_amount |
| merchant_actual_receive | erp_bill_info >> merchant_actual_amount |
| merchant_delivery_fee | erp_bill_info >> merchant_delivery_fee |
| merchant_total_discount | erp_bill_info >> merchant_discount |
| total_amount | erp_bill_info >> order_total_amount |
| package_fee | SUN(erp_bill_info >> merchant_pack_fee, erp_bill_info >> platform_pack_fee) |
| order_accept_time | order_info >> accept_time |
| bill_time | order_info >> bill_time |
| order_operator_id | order_info >> pick_operator_id |
| user_id | offline_order_info >> user_id |
| cost_center_code | null |
| mer_code | offline_order_info >> mer_code |
| account_err_msg | offline_order_info >> extend_info >> orderAccountFailReason |
| account_config_no | erp_bill_info >> client_conf_id |
| is_valid | 1 |
| business_type | offline_order_info >> business_type |
| third_order_no | offline_order_info >> third_order_no |
| error_type | 根据 account_err_msg 进行判断 |
| bill_total_amount | erp_bill_info >> bill_total_amount |
| online_store_code | offline_order_info >> online_store_code |


## account_order_detail

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| account_order_no | account_order >> account_order_no |
| account_order_detail_no | 雪花算法 |
| erp_code | order_detail >> erp_code |
| erp_name | order_detail >> commodity_name |
| commodity_count | order_detail >> commodity_count |
| gift_type | order_detail >> is_gift == 1 ？"GIFT" : "NOT_GIFT" |
| original_price | order_detail >> original_price |
| price | order_detail >> price |
| commodity_cost_price | 根据 order_detail >> erp_code 从 order_commodity_detail_cost_price 中过滤数据 order_commodity_detail_cost_price >> cost_price |
| total_amount | order_detail >> total_amount |
| discount_share | order_detail >> discount_share |
| discount_amount | order_detail >> discount_amount |
| bill_price | order_detail >> bill_price |
| bill_total_amount | order_detail >> actual_net_amount |


## account_order_detail_pick

| 字段 | 取值来源/逻辑 |
| --- | --- |
|  | 在 order_pick_info 中筛选出 order_detail_id = order_detail >> id 的拣货数据 |
| id | 数据库自增id |
| account_order_no | account_order >> account_order_no |
| account_order_detail_no | account_order_detail >> account_order_detail_no |
| account_order_detail_pick_no | 雪花算法 |
| erp_code | account_order_detail >> erp_code |
| make_no | order_pick_info >> commodity_batch_no |
| count | order_pick_info >> count |


## account_order_detail_trace

| 字段 | 取值来源/逻辑 |
| --- | --- |
|  | 在 medical_trace_code 中筛选出 order_detail_id = order_detail >> id 的追溯码数据 |
| id | 数据库自增id |
| account_order_no | account_order >> account_order_no |
| account_order_detail_no | account_order_detail >> account_order_detail_no |
| account_order_detail_pick_no | account_order_detail_pick >> account_order_detail_pick_no |
| erp_code | account_order_detail >> erp_code |
| make_no | medical_trace_code >> commodity_batch_no |
| trace_code | medical_trace_code >> ecode |
| nhsa_report_flag | RDarktrueswitch (medical_trace_code >> flag) {   case 1:     accountOrderDetailTrace.setNhsaReportFlag("TRUE");     break;   case 2:     accountOrderDetailTrace.setDraReportFlag("TRUE");     break;   case 3:     accountOrderDetailTrace.setDraReportFlag("TRUE");     accountOrderDetailTrace.setNhsaReportFlag("TRUE");     break; } |
| dra_report_flag |


## account_order_pay

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| account_order_no | account_order >> account_order_no |
| order_pay_no | offline_order_pay >> order_pay_no |
| pay_type | offline_order_pay >> pay_type |
| pay_name | offline_order_pay >> pay_name |
| pay_amount | offline_order_pay >> pay_amount |


# order_delivery库

## delivery_order

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| order_no | offline_order_info >> order_no |
| delivery_order_no | 如果 offline_order_info 中的 userId不为空，根据id生成order_no。否则根据 offline_order_info >> created 生成order_no |
| third_delivery_order_no | order_delivery_record >> rider_order_no |
| user_id | offline_order_info >> user_id |
| delivery_date | order_delivery_record >> call_time |
| is_on_booking | offline_order_info >> booking_flag |
| booking_time_start | offline_order_info >> booking_time_start |
| booking_time_end | offline_order_info >> booking_time_end |
| transaction_channel | offline_order_info >> transaction_channel |
| business_type | offline_order_info >> business_type |
| launch_organization_code | 无 |
| launch_organization_name | 无 |
| launch_user_id | 无 |
| sender_plan_organization_code | order_info >> source_organization_code |
| sender_plan_organization_name | order_info >> source_organization_name |
| sender_real_organization_code | order_info >> organization_code |
| sender_real_organization_name | order_info >> organization_name |
| delivery_order_total_amount | SUM(offline_order_detail >> total_amount) + order_delivery_record >> actual_delivery_fee |
| delivery_plan_amount | order_delivery_record >> actual_delivery_fee |
| delivery_transfer | NO_TRANSFER |
| delivery_status | RDarktrueswitch (order_delivery_record >> state){   case 0:     return OrderDeliveryStatus.WAIT_DELIVERY.getStatus();   case 1:     return OrderDeliveryStatus.WAIT_ALLOCATE.getStatus();   case 2:     return OrderDeliveryStatus.WAIT_TAKE.getStatus();   case 3:     return OrderDeliveryStatus.SHIPPING.getStatus();   case 4:     return OrderDeliveryStatus.RECEIVED.getStatus();   case 5:     return OrderDeliveryStatus.CANCELED.getStatus();   default:     return OrderDeliveryStatus.EXCEPTION.getStatus(); } |
| sender_address | 无 |
| sender_phone | 无 |
| receiver_code | 无 |
| receiver_name | order_delivery_address >> receiver_name |
| receiver_address | order_delivery_address >> address |
| receiver_phone | order_delivery_address >> receiver_mobile |
| receiver_landline_phone | order_delivery_address >> receiver_telephone |
| receiver_third_oaid | order_delivery_address >> oaid |
| receiver_province | order_delivery_address >> province |
| receiver_city | order_delivery_address >> city |
| receiver_district | order_delivery_address >> district |
| receiver_town | order_delivery_address >> town |
| receiver_full_address | order_delivery_address >> full_address |
| receiver_original_full_address | order_delivery_address >> original_full_address |
| receiver_zip_code | order_delivery_address >> zip_code |
| receiver_name_privacy | order_delivery_address >> receiver_name_privacy |
| receiver_address_privacy | order_delivery_address >> receiver_address_privacy |
| receiver_phone_privacy | order_delivery_address >> receiver_phone_privacy |
| receiver_landline_phone_privacy | order_delivery_address >> receiver_phone_privacy |
| remarks | order_delivery_record >> extra_info |
| mer_code | offline_order_info >> mer_code |
| company_code | offline_order_info >> company_code |
| company_name | offline_order_info >> company_name |


## delivery_order_detail

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id |  |
| delivery_order_no | delivery_order >> delivery_order_no |
| order_detail_no | offline_order_detail >> order_detail_no |
| logistics_tracking_no | order_delivery_record >> logistics_no |
| delivery_order_detail_no | 雪花算法 |
| erp_code | order_detail >> erp_code |
| erp_name | order_detail >> commodity_name |
| commodity_count | order_detail >> commodity_count |
| original_price | order_detail >> original_price |
| price | order_detail >> price |
| total_amount | order_detail >> total_amount |
| status | NORMAL |
| swap_no | 无 |
| gift_type | order_detail >> is_gift == 1 ？"GIFT" : "NOT_GIFT" |


## delivery_order_detail_pick

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| delivery_order_no | delivery_order >> delivery_order_no |
| delivery_order_detail_no | delivery_order_detail >> delivery_order_detail_no |
| delivery_order_detail_pick_no | 雪花算法 |
| erp_code | delivery_order_detail >> erp_code |
| make_no | order_pick_info >> commodity_batch_no |
| count | order_pick_info >> count |


## delivery_order_detail_trace

| 字段 | 取值来源/逻辑 |
| --- | --- |
| id | 数据库自增id |
| delivery_order_no | delivery_order >> delivery_order_no |
| delivery_order_detail_no | delivery_order_detail >> delivery_order_detail_no |
| delivery_order_detail_pick_no | delivery_order_detail_pick >> delivery_order_detail_pick_no |
| erp_code | delivery_order_detail >> erp_code |
| make_no | medical_trace_code >> commodity_batch_no |
| trace_code | medical_trace_code >> ecode |
| nhsa_report_flag | RDarktrueswitch (medical_trace_code >> flag) {   case 1:     accountOrderDetailTrace.setNhsaReportFlag("TRUE");     break;   case 2:     accountOrderDetailTrace.setDraReportFlag("TRUE");     break;   case 3:     accountOrderDetailTrace.setDraReportFlag("TRUE");     accountOrderDetailTrace.setNhsaReportFlag("TRUE");     break; } |
| dra_report_flag |