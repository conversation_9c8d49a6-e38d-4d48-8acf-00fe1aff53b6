# 【20240719】订单中台提供：通过三方平台编码+三方单号查询订单信息

| 服务名称 | 订单中台 |
| --- | --- |
| 接口描述 | ``` 通过三方平台编码+三方单号查询订单信息 同订单中台消息 ``` |
| 接口地址 | ``` /1.0/ds/order/third/order-detail  服务名 hydee-business-order ``` |
| 请求方式 | POST |
| ContentType | JSON |
| 请求参数 | String | thirdOrderNo | 三方平台订单号 | true |
| String | thirdPlatformCode | 三方平台编码 | true |
| 响应参数 | String | code | 请求返回码，10000-成功，其他失败 |  |
| String | msg | 请求返回信息 |  |
| OrderModelVo | data |  | true |


OrderModelVo

| 响应参数 | String | orderNo | 订单中台订单号 |  |
| String | thirdOrderNo | 三方平台订单号 |  |
| String | thirdPlatformCode | 三方平台编码 |  |
| Integer | orderStatus | 订单状态 |  |
| Integer | orderType | 订单类型 |  |
| String | storeId | 下单门店Id |  |
| String | sourceOrganizationCode | ``` （接单）线下门店编码-来源 ``` |  |
| String | payType | 支付类型 |  |
| Date | payTime | 支付时间 |  |
| String | deliveryType | 配送方式 |  |
| BigDecimal | totalActualOrderAmount | 订单总金额 |  |
| BigDecimal | actualFreightAmount | 实际运费金额 |  |
| String | memberCard | 会员卡号 cn.hydee.middle.business.order.kafka.event.model.OrderModel#memberCard |  |
| Long | memberId | 会员ID |  |
| Date | completeTime | 完成时间 |  |
| String | serviceMode | 服务模式 |  |
| List<OrderDetailModelVo> | detailList | 整单明细(实时明细) |  |


OrderDetailModelVo

| 响应参数 | String | commodityCode | 商品编码 |  |
| String | commodityName | 商品名称 |  |
| Integer | commodityCount | 商品数量 |  |
| BigDecimal | totalActualAmount | 成交总额 |  |
| BigDecimal | billPrice | 下账单价 |  |
| Integer | isGift | 是否是赠品（0，不是赠品,1是赠品） |  |


响应对象


public class ThirdOrderDetailResDto {

  
  private Long orderNo;

  private String thirdOrderNo;
  
  private Integer orderStatus;

  private Integer orderType;

  // （接单）线下门店编码-来源
  private String sourceOrganizationCode;

  private String memberCard;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date completeTime;

  // 服务模式 O2O B2C
  private String serviceMode;


  // 整单明细
  private List<OrderDetailDto> detailList;


  /**
   * 订单明细
   */
  @Data
  public static class OrderDetailDto {
    private String commodityCode;
    private String commodityName;
    private Integer commodityCount;
    private Integer isGift;
    private BigDecimal billPrice;

  }
}