# 24年第15周2024-04-19

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构-DDD项目 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分 | 下周确定对接模型 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月18日方案评审 | 技术方案设计 | .net代码黑盒,无法多人同时开发. 按平台迁移对接代码 |
| 3 | 优雅发布升级-mq |  | 4月12日完成待办列表,推动全部门升级 | 已完成,待上线 | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表,推动全部门升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | RocketMQ梳理，以及快速入门 |  |  |  |  |
| 8 | 每周- [上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 9 | [生产值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcI2UHkD5) |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5pd**1. 接口中台技术方案设计 2. 接口中台demo搭建 3. 接口中台技术方案评审 | **㊀计划工作****㊁实际完成**1. demo已基本按技术方案搭建完毕 2. 第一次技术方案评审 **㊂遗留问题**1. 项目模块命名需要调整 2. 考虑将该项目上传成项目初始化模板（archetype模板） **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 |  | **本周总工时：5pd**1. V1.6.2订单运维工具 2. 线上BUG 3. 商品分摊优化 | **㊀计划工作**1. V1.6.2订单运维工具 2. 线上BUG 3. 商品分摊优化 **㊁实际完成**1. V1.6.2订单运维工具 2. 线上BUG **㊂遗留问题**1. **商品分摊优化** **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 |  | **本周总工时：**- 线下单兜底逻辑开发&封装SDK&分表数据倾斜问题测试 - 云仓商品补充字段(正单、退单) - 订单中台重构会议:主要讨论每个服务大概的入参信息 - businesses-gateway上线 - 推进优雅发布接入工作(大数据接入中、商品待上线) - 其他   - 《满减劵、折扣劵支持云仓商品》产品会议   - DDD项目骨架分享讨论   - Q1绩效填写   - 部署order-job-starter   - 面试 - 《满减劵、折扣劵支持云仓商品》产品会议 - DDD项目骨架分享讨论 - Q1绩效填写 - 部署order-job-starter - 面试      **** | **㊀计划工作****㊁实际完成****㊂遗留问题**  **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 4 |  | **本周总工时：5pd** 1. 订单路由  a. 商品中台联调 b. bug 修改 2.支付宝对接 3. 海典乾昌对接支持 4. 保山医保测试门店支持 5. 优惠券增加类型（支付券） 6. 线上问题处理 | **㊀计划工作** **㊁实际完成******1. 订单路由联调 现有bug已修复 2. 支付宝对接 40% 3. 海典乾昌对接支持 正式环境下单流程已走通 4. 保山医保测试门店支持 已有问题处理完毕，已测试3家门店 5. 优惠券增加类型（支付券）梳理完部分修改点 （剩余小前台优惠计算部分）**㊂遗留问题****1. 订单路由 对接商品****2. 订单路由 对接海典****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 5 |  | **本周总工时：5pd**1.订单路由bug2.内购商城和兑换商城周期限购，内购商城总部仓自提3.场景、策略CodeReview4.修改根据分单门店和接单门店经纬度计算逻辑 | **㊀计划工作**1.订单路由bug2.内购商城和兑换商城周期限购，内购商城总部仓自提3.场景、策略CodeReview**㊁实际完成**1.订单路由bug2.内购商城和兑换商城周期限购，内购商城总部仓自提3.场景、策略CodeReview**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **** |  |
| 6 |  | **本周总工时：**1、线上门店运维 2、新增订单同步ES定时任务（已上线） 3、海典重复拣货问题修复（已上线） 4、订单路由优化与问题bug修复 5、科传部分退款下账金额问题 | **㊀计划工作**1. 订单路由优化 2. 订单同步ES定时任务 3. 支付宝小程序订单接入 **㊁实际完成**1. 订单同步ES定时任务 **㊂遗留问题**1. 订单路由权限优化 2. 支付宝小程序订单接入 **㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 7 |  | **本周总工时：5****1.订单运维工具开发****2.RocketMQ梳理****3.订单运维工具配合测试****4.订单线上运维-微商城订单支付问题****5.订单线上运维-海典逆向运费单问题****6.订单线上运维-B2C云仓订单问题** | **㊀计划工作****1.订单运维工具开发****2.订单运维工具配合测试****3.订单线上运维-微商城订单支付问题****4.订单线上运维-海典逆向运费单问题****5.订单线上运维-B2C云仓订单问题****㊁实际完成****1.订单运维工具开发****2.订单运维工具配合测试****3.订单线上运维-微商城订单支付问题****4.订单线上运维-海典逆向运费单问题****5.订单线上运维-B2C云仓订单问题****㊂遗留问题****1.RocketMQ梳理****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1.商品分摊优化**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 8 |  | **本周总工时：5 day**1. 京东到家 商品变更推送。2day  2. .NET 重构。 2day 3. 订单优惠重算。0.5day 4. test 环境打印程序连接失败问题排查。 0.5day | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx  **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  | [Jackson的多态反序列化 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24613490) |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 | 无 | 1.权限校验2.记录日志3.性能优化 | 待办 |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

 1.绩效填写 下周二之前 [绩效文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgCzO9VcGeRHSRMAWb8r?scode=AOsAFQcYAAcmdwBqSuAboAOAYLADg&tab=wxu9jk)

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |