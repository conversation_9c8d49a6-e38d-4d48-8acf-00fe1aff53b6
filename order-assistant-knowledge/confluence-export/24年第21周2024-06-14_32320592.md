# 24年第21周2024-06-14

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参  6月1日进入开发阶段 | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测 7. 5月28号进入开发阶段 8. 6月17号提测 9. 支付宝：   - 联调阶段 预计20号提测 10. 联调阶段 预计20号提测 | 支付宝：6月17号提测。 |  |
| 3 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 4 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 5 | 支付中台重构 |  |  | 暂停 |  |
| 6 | [需求池](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgFQpYWtHhRbWSdBxb5u?scode=AOsAFQcYAAcFpJng4uAboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 7 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 8 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 9 | [2024Q2-交易生产组](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgA9Okm682QqKMEwhssg?scode=AOsAFQcYAAc3lcl7J9AboAOAYLADg&tab=9cjz4i) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| B2C退款流程优化 |  | 开发中80% 6月19日提测 |  |
| 订单优化2效率提升 |  | 联调，6月18日提测 |  |
| 需求-微商城云仓虚拟商品订单 |  | 开发中90% 6月19日提测 |  |
| 线下单迁移 |  | 开发中40% |  |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 | 进度20% |  |
| 对外门店信息查询 | 开发中30% | 国华 |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：3.5d**1.京东到家处方展示问题 2.饿了么订单退款问题 3.慢SQL治理 4.京东订单售后 5.联调微商城云仓虚拟商品订单 6.新订单中台售后 puml编写 | **遗留问题**1..联调微商城云仓虚拟商品订单 2..新订单中台售后 puml编写**风险问题**   **** | **需求研发**1.联调微商城云仓虚拟商品订单 2..新订单中台售后 puml编写**技术建设** **** |  |
| 2 | 杨润康 | **本周总工时：4d**1. 线上问题处理、配合大数据开发同学解释相关字段 2. 预发布环境部署 3. 配合线下单、线上单测试,根据会议结果调整单号生成规则和取值逻辑 4. 迁移线下单开发 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** 4 day1.支付宝接口对接和联调 1.5day2.3个平台 订单详情、退款接口支持 1 day3.商品信息变更 信息增加 0.5 day4. 打印以及线上问题处理 1 day | **遗留问题**1.拼多多 京东 平台打印面单和发货单乱序。需要前端改为循环调用。**风险问题** **** | **需求研发****技术建设****** |  |
| 4 |  | **本周总工时：4day**接口中台重构-饿了么：- 订单中台 - 饿了么请求接入新接口中台 - 接口中台 - 补充遗漏的接口对接 - 部分接口自测 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：4 day**1. 虚拟商品前端联调 2. 订单中台重构-配送信息更新 3. 订单中台重构-售后服务 4. 线上问题处理 5. 分销流程支持 | **遗留问题** **风险问题** **** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：4day**1.异常换货联调2.评价拉回3.B2C组合商品分摊优化4.全局预警设置 | **遗留问题**1.评价拉回需要造数据2.B2C组合商品分摊优化，进度60%**风险问题** **** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：3d**1. B2C取消/退款优化：  a. 订单取消流程部分代码修改 b. 整体流程测试+问题修改 c. 平台单详情明细增加退款状态数据开发 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：4d**1. 海典调拨单对接  a. 异步换成同步 b. 相关bug修复 c. 新增调拨补偿任务2. B2C取消/退款优化： WMS发货/退款流程新增创建下账单 | **遗留问题**海典异步接口未对接**风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：4d**1. 财务信息，下账金额信息更新 2. 普通商品，拆零商品，组合商品，赠品数量根据退款重算，并释放相应库存 3. 商家部分退款安全校验 4. 商家部分退款测试用例 5. 商家部分退款功能前端联调 6. 京东无界获取面单问题解决 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |