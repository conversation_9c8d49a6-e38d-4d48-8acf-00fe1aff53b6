# 【20240226】门店POS配置

# 一、背景

## 1.1 业务背景

目前我们系统中已有3种pos机：科传、海典H1、海典H2，对于海典的pos机，需要配置对应的请求地址

## 1.2 痛点分析

科传无需配置地址，我们属于被动调用；

但对于海典的pos机，需要我们主动调用，而目前由于使用海典POS机的门店相对较少，我们手动改数据即可处理。

但后续铺开海典POS的使用，用手动改数据属实费时费力，大大增加了运维成本，并且目前的扩展性太差。

## 1.3 系统现状

手动配置门店POS机地址

# 二、需求分析

## 2.1 业务流程

[V 1.5.2 - 产品部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/display/prd/V+1.5.2)

# 三、目标

**3.1 本期目标**

- 完成需求内容


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

### 4.2.1 门店信息变更MQ监听

true门店信息变更消息处理falseautotoptrue3291

### 4.2.2 POS配置流程

#### 4.2.2.1 门店配置

**true门店POS配置falseautotoptrue5461**

#### 4.2.2.2 组织机构配置

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | ds_organization_pos_config | 组织结构pos配置表 | CREATE TABLE `ds_organization_pos_config` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',   `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构码',   `organization_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构名',   `organization_type` tinyint NOT NULL COMMENT '机构类型 1 子公司 2 部门',   `parent_organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级机构id',   `pos_mode` tinyint DEFAULT NULL COMMENT 'POS类型,1：海典H1，2：海典H2，3：科传',   `pos_url` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'POS机地址',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',   `update_by` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_organization_code` (`organization_code`) USING BTREE,   KEY `ids_organization_code_type` (`organization_code`,`organization_type`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |
| 2 | inner_store_dictionary | 门店字典表 | ALTER TABLE `dscloud`.`inner_store_dictionary`  ADD COLUMN `parent_organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级机构编码，关联ds_organization_pos_config' AFTER `pos_url`, DROP INDEX `organization_code_idx`, ADD UNIQUE INDEX `organization_code_idx`(`organization_code`) USING BTREE COMMENT '线下门店编码索引';ALTER TABLE `dscloud`.`inner_store_dictionary`  MODIFY COLUMN `pos_mode` tinyint(1) NULL DEFAULT 3 COMMENT 'pos机模式（1：海典H1，2：海典H2，3：科传）' AFTER `auto_bill_timestamp`; |
| 3 | store_change_mq_consume_log | 门店信息变更消息日志表 | CREATE TABLE `store_change_mq_consume_log` (   `id` bigint NOT NULL AUTO_INCREMENT,   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,   `message_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'mq消息id',   `message_tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'mq消息tag',   `message_topic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'mq消息topic',   `message_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'mq消息体',   `exception_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '报错信息',   `store_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店编码',   PRIMARY KEY (`id`) ) ENGINE=InnoDB AUTO_INCREMENT=431 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |


## 5.3 接口设计

### 5.3.1 前端交互接口（新增）

#### 1 API-组织机构POS配置列表

1. url:/ds/organization/pos/config/list
2. 请求类型：POST
3. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` organizationName ``` | String | 是 | 组织机构名称 |
| ``` posMode ``` | Integer | 是 | pos类型 1：海典H1，2：海典H2，3：科传 |
  2. 示例{
  "organizationName": "string",
  "posMode": 0
}
4. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` organizationName ``` | String | 是 | 组织机构名称 |
| ``` posMode ``` | Integer | 是 | pos类型 1：海典H1，2：海典H2，3：科传 |
5. 示例{
  "organizationName": "string",
  "posMode": 0
}
6. 响应体：
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` organizationCode ``` | String | ``` 组织机构编码 ``` | ds_organization_pos_config >> organization_code |
| ``` organizationName ``` | String | 组织机构名称 | ds_organization_pos_config >> organization_name |
| ``` organizationType ``` | ``` Integer ``` | 组织机构类型1 子公司 2 部门 | ds_organization_pos_config >> organization_type |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
| ``` modifyTime ``` | String | 最后修改时间 | ds_organization_pos_config >> modify_time |
| ``` updateBy ``` | String | 修改人 | ds_organization_pos_config >> update_by |
| ``` childPosConfigList ``` | List<Object> | 子集 |  |
  2. 示例值{
  "code": "string",
  "data": [
    {
      "childOrganizationPosConfigList": [
        {
          "childOrganizationPosConfigList": [
            null
          ],
          "modifyTime": "string",
          "organizationCode": "string",
          "organizationName": "string",
          "organizationType": 0,
          "parentOrganizationCode": "string",
          "posMode": 0,
          "posUrl": 0,
          "updateBy": "string"
        }
      ],
      "modifyTime": "string",
      "organizationCode": "string",
      "organizationName": "string",
      "organizationType": 0,
      "parentOrganizationCode": "string",
      "posMode": 0,
      "posUrl": 0,
      "updateBy": "string"
    }
  ],
  "msg": "string",
  "timestamp": 0
}
7. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` organizationCode ``` | String | ``` 组织机构编码 ``` | ds_organization_pos_config >> organization_code |
| ``` organizationName ``` | String | 组织机构名称 | ds_organization_pos_config >> organization_name |
| ``` organizationType ``` | ``` Integer ``` | 组织机构类型1 子公司 2 部门 | ds_organization_pos_config >> organization_type |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
| ``` modifyTime ``` | String | 最后修改时间 | ds_organization_pos_config >> modify_time |
| ``` updateBy ``` | String | 修改人 | ds_organization_pos_config >> update_by |
| ``` childPosConfigList ``` | List<Object> | 子集 |  |
8. 示例值{
  "code": "string",
  "data": [
    {
      "childOrganizationPosConfigList": [
        {
          "childOrganizationPosConfigList": [
            null
          ],
          "modifyTime": "string",
          "organizationCode": "string",
          "organizationName": "string",
          "organizationType": 0,
          "parentOrganizationCode": "string",
          "posMode": 0,
          "posUrl": 0,
          "updateBy": "string"
        }
      ],
      "modifyTime": "string",
      "organizationCode": "string",
      "organizationName": "string",
      "organizationType": 0,
      "parentOrganizationCode": "string",
      "posMode": 0,
      "posUrl": 0,
      "updateBy": "string"
    }
  ],
  "msg": "string",
  "timestamp": 0
}


#### 2 API-POS配置

1. url: /ds/organization/pos/config
2. 请求类型：POST
3. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` organizationCode ``` | String | ``` 组织机构编码 ``` | ds_organization_pos_config >> organization_code |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
  2. 请求示例：{
  "organizationCode": "string",
  "organizationType": 0,
  "posMode": 0,
  "posUrl": "string"
}
4. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` organizationCode ``` | String | ``` 组织机构编码 ``` | ds_organization_pos_config >> organization_code |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
5. 请求示例：{
  "organizationCode": "string",
  "organizationType": 0,
  "posMode": 0,
  "posUrl": "string"
}
6. 响应体：无


### 5.3.2 前端交互接口（更新）

#### 1 API-门店详情

1. url: merchant/1.0/store/{storeId}
2. 请求类型：GET
3. 变化点：
  1. 响应体新增字段| 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
4. 响应体新增字段| 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |


#### 2 API-门店编辑保存

1. url: /merchant/1.0/store/online_update
2. 请求类型：POST
3. 变化点：
  1. 请求体新增字段| 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |
4. 请求体新增字段| 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` posMode ``` | Integer | pos类型1：海典H1，2：海典H2，3：科传 | ds_organization_pos_config >> pos_mode |
| ``` posUrl ``` | String | pos地址 | ds_organization_pos_config >> pos_url |


5.3.3 门店信息变化MQ监听

TODO

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 开发时间 | 负责人 | 进度 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 会员组MQ消息监听 | 监听组织机构变动消息，并新增/更新我们的业务表 | business-order |  |  | 暂定，依赖会员组 |  |  |
| 门店配置 | 详情回显，新增字段：POS类型 | ydjia-merchant-platformbusiness-order |  |  |  |  |
| 编辑保存，新增字段：POS类型 |  |  |  |
| 组织机构-POS机设置 | 表结构修改重新刷inner表数据？ | business-order |  |  |  |  |
|  | 列表查询显示 |  |  |  |  |  |
|  | POS回显/编辑保存 |  |  |  | - |  |
| 缓存（暂定） | 增加列表缓存 | business-order |  |  | 时间足够可写 |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等