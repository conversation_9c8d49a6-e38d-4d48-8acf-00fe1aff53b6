package com.yxt.order.assistant.server.knowledge;

import java.util.List;

/**
 * Confluence子页面列表响应
 */
public class ConfluenceChildrenResponse {
    private List<ConfluencePageSummary> results;
    private int start;
    private int limit;
    private int size;
    private ConfluenceLinks _links;
    
    public static class ConfluencePageSummary {
        private String id;
        private String type;
        private String status;
        private String title;
        private ConfluenceLinks _links;
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public ConfluenceLinks get_links() {
            return _links;
        }
        
        public void set_links(ConfluenceLinks _links) {
            this._links = _links;
        }
    }
    
    public static class ConfluenceLinks {
        private String self;
        private String base;
        private String context;
        
        public String getSelf() {
            return self;
        }
        
        public void setSelf(String self) {
            this.self = self;
        }
        
        public String getBase() {
            return base;
        }
        
        public void setBase(String base) {
            this.base = base;
        }
        
        public String getContext() {
            return context;
        }
        
        public void setContext(String context) {
            this.context = context;
        }
    }
    
    // Getters and Setters
    public List<ConfluencePageSummary> getResults() {
        return results;
    }
    
    public void setResults(List<ConfluencePageSummary> results) {
        this.results = results;
    }
    
    public int getStart() {
        return start;
    }
    
    public void setStart(int start) {
        this.start = start;
    }
    
    public int getLimit() {
        return limit;
    }
    
    public void setLimit(int limit) {
        this.limit = limit;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public ConfluenceLinks get_links() {
        return _links;
    }
    
    public void set_links(ConfluenceLinks _links) {
        this._links = _links;
    }
}
