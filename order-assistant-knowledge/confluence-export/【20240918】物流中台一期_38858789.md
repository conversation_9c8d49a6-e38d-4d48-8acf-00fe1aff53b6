# 【20240918】物流中台一期

# 背景

## 业务背景

a. 当前心云系统，没有物流中台，只有物流单，物流单与订单高度藕合，现将物流单抽出来，完成物流中台的建设；整合骑手配送和快递配送；

b.心云系统，没有对接对应的物流轨迹查询；

c.取消面单，拦截快递，订单修改地址，都是手动操作，效率过低和易操作错；提升人效，每个子公司可节约0.5个人力，按4500/人，一年节省：30万/年的人力成本；

d.一心到家现使用快递100，每年接口费近2万，若走心云物流中台每年可节省2万；

e.wms使用创建快递时，新增一家快递，则需要新对接一家，统一物流中台后，wms或其他的外部平台均可调用；可节省对接费，2万/个；

## 系统现状

业务现有流程图、架构等


true系统现状false400autotoptrue13712

目前快递接入情况 注：目前是只有创建面单正式使用，其余接口虽然已经支持，但是并未使用且未经过测试

hydee-third-inside 当前直连模式下请求三方快递商模块，该项目类似于通道只做转发，无业务逻辑

/expressSheet/generate/batch 批量生成电子面单

当前面单生成流程图

true获取面单falseautotoptrue11611

# 需求分析

prd: 

## 业务流程

## 需求功能

|  | 功能点 | 功能说明 | 优先级 |
| --- | --- | --- | --- |
| 1 | 创建物流单 | - 请求三方快递商创建面单 - 保存物流信息 |  |
| 2 | 取消面单 | - 对成功请求的面单进行取消操作 |  |
| 3 | 拦截物流 | - 对已发货的物流进行拦截 |  |
| 4 | 修改地址 | - 对已有物流信息进行收货地址修改 |  |
| 5 | 物流轨迹 | - 监听快递商物流轨迹推送 - 查询物流轨迹 - 存储物流轨迹信息 |  |


# 目标

## 本期目标

### 业务目标

 a. 物流相关拆除，构建物流中台。

 b. 完成物流中台基础流程。

### 技术目标

一般是技术指标（SLA）、沉淀基础服务、节约开发成本等

## 中长期目标

 a. 建设完全

# 整体设计

## 领域图

true领域划分falseautotoptrue7214

## 分层架构图

true物流中台架构falseautotoptrue10767

数据流向

true未命名绘图falseautotoptrue10412

项目结构

true项目结构falseautotoptrue8611

## 统一语言定义

*业务、技术名词解释等*

| **物流单状态** | 名词释义 | O2O取值 |
| --- | --- | --- |
| 待揽收 | 面单创建成功，快递未揽件 | 待接单 |
| 已揽收 | 快递揽件 | 骑手接单 |
| 配送中 | 快递已发货，正在配送路途 | 配送中 |
| 签收成功（已完成） | 顾客已签收成功 | 配送完成 |
| 已退回 | 快递将货品退回 |  |
| 已取消 | 面单已取消 | 已取消 |


## 核心技术问题

*扩展性、数据迁移、兼容等核心技术挑战和解决方案等。*

|  | 核心问题 | 解决方案 |
| --- | --- | --- |
| 1 | 快递商、电子面单配置、店铺网点配置数据迁移 | 1. 历史数据：清洗脏数据，转换为新表结构体，批量导入 2. 增量数据：使用cancel监听数据变化，增加添加或修改 |


# 详细设计

## 模块详细设计

*分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比。1*

创建物流单流程

true创建物流单流程falseautotoptrue12514

物流单关联流程

true关联物流单falseautotoptrue6213

取消面单流程

true取消面单流程falseautotoptrue11612

修改地址流程

true地址修改流程falseautotoptrue8111

物流轨迹查询流程

true物流轨迹流程图falseautotoptrue8413

## 存储数据库设计

| 物流中台表关系-新 | 心云原物流相关表关系-旧 |
| --- | --- |
| true物流中台表关系falseautotoptrue17419 | true配置关联表关系falseautotoptrue16114 |


*新增、修改的字段DDL；索引设计；数据量预估，分库分表设计；有时需要存储选型分析方案：缓存、es等。*

| 表名 | sql |
| --- | --- |
| 物流单表 | sqlCREATE TABLE `express_logistic_order` (   `id` bigint NOT NULL AUTO_INCREMENT,   `oms_order_no` bigint DEFAULT NULL COMMENT '系统订单号',   `third_order_no` bigint DEFAULT NULL COMMENT '三方订单号',      `logistic_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流单号',   `logistic_status` varcahr(100)utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'WAIT_COLLECT' COMMENT 'WAIT_COLLECT-待揽收 WAIT_DIST-待配送  DISTING-配送中  FINISH-已完成  CANCEL-已取消 RETURNED-已退回',   `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台code',   `express_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流公司Code',   `express_store_config_id` bigint DEFAULT NULL COMMENT '店铺快递配置id',   `order_source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单来源 WMS-仓库、 YXT-心云、HD-海典',    `document` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '面单内容',   `print_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打印平台 ZL-直连 3002-菜鸟云栈 3003-拼多多 3004-京东无界',     `created_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',      `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',      PRIMARY KEY (`id`) USING BTREE,   KEY `logistic_no` (`logistic_no`) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='物流信息表'; |
| 物流轨迹 | sqlCREATE TABLE `express_track` (   `id` bigint NOT NULL AUTO_INCREMENT,     `logistic_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流单号',   `action` varcahr(100)utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'WAIT_COLLECT' COMMENT '物流动作 WAIT_COLLECT-待揽收 WAIT_DIST-待配送  DISTING-配送中  FINISH-已完成  CANCEL-已取消 RETURNED-已退回',   `action_msg` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流变动详情',   `express_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流公司Code',     `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `deleted` bigint NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 时间戳-已删除',      `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',      PRIMARY KEY (`id`) USING BTREE,   KEY `logistic_no` (`logistic_no`) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='物流轨迹表'; |
| 快递店铺配置 | sqlCREATE TABLE `express_store_merchant` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `platform_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编码',   `org_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '线上店铺code',   `express_merchant_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递商户id',   `link_type` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接模式 3002:菜鸟云栈 3003:拼多多 3004:京东无界 ZL:直连',   `status` tinyint NOT NULL DEFAULT 'OPEN' COMMENT 'CLOSE-禁用，OPEN-启用',   `express_sheet_stdtemplates_id` bigint DEFAULT NULL COMMENT '面单模板ID',   `deliver_sheet_stdtemplates_id` bigint DEFAULT NULL COMMENT '发货单模板id',   `priority` tinyint NOT NULL DEFAULT '1' COMMENT '优先级',   `extend` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段（存放网点设置所有信息）',   `created_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_store_express_link_unique` (`online_store_code`,`dict_express_merchant_id`,`link_type`),   KEY `idx_online_store_code` (`online_store_code`) ) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺快递配置表'; |
| 快递商户表 | sqlCREATE TABLE `express_merchant` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `express_account_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '快递账号名称',   `status` varchar(20) DEFAULT 'OPEN' COMMENT '快递状态，CLOSE-禁用，OPEN-启用',   `link_type` varchar(20) DEFAULT 'ZL' COMENT 'ZL-直连 3002-菜鸟云栈 3003-拼多多 3004-京东无界'   `express_code` int DEFAULT NULL COMMENT '快递编码',   `config_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '快递商配置信息',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'   PRIMARY KEY (`id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='快递商户'; |
| 电子面单模板表 | sqlCREATE TABLE `express_sheet_template` (   `id` bigint NOT NULL AUTO_INCREMENT,   `sync_time` datetime DEFAULT NULL COMMENT '同步时间',   `standard_template_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板名称',   `print_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打印平台code',   `express_code` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '快递编码',   `standard_template_url` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '标准模板url',   `custom_area_url` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '自定义区域url',   `expanded` json DEFAULT NULL COMMENT '扩展json',   `view_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览url',   `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'COSTUME' COMMENT '模板来源 THIRD-三方模板 COSTUME-系统生成',   `encrypt_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '加密标识 0不加密 1加密',   PRIMARY KEY (`id`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=2046 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='电子面单模板表' |
| 发货单模板表 | sqlCREATE TABLE `deliver_sheet_template` (   `id` bigint NOT NULL AUTO_INCREMENT,   `standard_template_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模板名称',   `expanded` json DEFAULT NULL COMMENT '扩展json',   `view_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预览url',   PRIMARY KEY (`id`) ) ENGINE=InnoDB AUTO_INCREMENT=2002 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin; |
| 物流模板自定义区域和发货单配置表 | sqlCREATE TABLE `logistic_customtemp_config` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',   `update_time` datetime DEFAULT NULL COMMENT '更新日期',   `column_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列编码（数据库字段）',   `column_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列中文名',   `selected` int DEFAULT NULL COMMENT '是否选中',   `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型',   `group_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段组名',   `seq` int DEFAULT NULL COMMENT '序号',   `status` int DEFAULT NULL COMMENT '状态（0未启用，1启用）',   `example` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样例数据',   `group_name_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字段组名备注',   `type_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类型备注',   `col` int DEFAULT '24' COMMENT '一个元素可以跨多列（自定义发货单有效）',   `bold` tinyint(1) DEFAULT '0' COMMENT '是否加粗',   `font_size` int DEFAULT '8' COMMENT '字号',   `column_type` int DEFAULT NULL COMMENT '列类型 1.文本，2.图片，3.条码',   `position` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '元素位置 title_top,title,content_up,table,conent_down',   PRIMARY KEY (`id`) ) ENGINE=InnoDB AUTO_INCREMENT=183 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='物流模板自定义区域和发货单配置表'; |
| 物流模板自定义区域和发货单配置关系表 | sqlCREATE TABLE `logistic_customtemp_config_map` (   `id` bigint NOT NULL AUTO_INCREMENT,   `templates_manage_id` int DEFAULT NULL COMMENT '电子面单模板id',   `customtemp_config_id` int DEFAULT NULL COMMENT '自定义属性id',   `selected` int DEFAULT NULL COMMENT '是否选择',   `bold` tinyint(1) DEFAULT NULL COMMENT '是否加粗',   `font_size` int DEFAULT NULL COMMENT '字号',   `seq` int DEFAULT NULL COMMENT '自定义序号',   PRIMARY KEY (`id`) USING BTREE,   KEY `uk_config_map` (`templates_manage_id`,`customtemp_config_id`) ) ENGINE=InnoDB AUTO_INCREMENT=7595 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='物流模板自定义区域和发货单配置关系表'; |
| 物流平台配置表 | sql |


## 快递商一期对接平台

| 快递商 | 文档地址 | 创单 | 取消面单 | 拦截面单 | 修改地址 | 轨迹查询 | 物流轨迹订阅 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 中通 | [中通开放平台 (zto.com)](https://open.zto.com/#/interfaces?resourceGroup=20&apiName=zto.open.createOrder) | 25 complete | 26 complete | 27 complete | 28 complete | 29 complete | 30 complete |
| 极兔 | [极兔速递-开放平台 (jtexpress.com.cn)](https://open.jtexpress.com.cn/#/apiDoc/orderserve/statusFeedback) | 31 complete | 32 complete | 33 complete | 34 complete | 35 complete | 36 complete |
| 圆通 | [开放平台 (yto.net.cn)](https://open.yto.net.cn/interfaceDocument/menu250/submenu358) | 37 complete | 38 complete | 39 complete | 40 complete | 41 complete | 42 complete |
| 邮政 | [国内协议客户API开放平台 (ems.com.cn)](https://api.ems.com.cn/#/xdl) | 43 complete | 44 complete | 45 complete | 46 complete | 47 complete | 48 complete |
| 京东 | [京东物流开放平台 (jdl.com)](https://open.jdl.com/#/open-business-document/api-doc/267/841) | 55 complete | 56 complete | 57 complete | 58 complete | 59 complete | 60 complete |


## 通用字段转换

| 接口 | 通用请求参数 | 通用返回参数 |
| --- | --- | --- |
| 创单 | ``` {    "express_merchat_id": "2",     "oms_order": "20220704210006",     "sender": {         "name": "小九",         "mobile": "***********",         "phone": "",         "countryCode": "CHN",         "prov": "上海",         "city": "上海市",         "area": "青浦区",         "address": "庆丰三路28号"     },     "receiver": {         "name": "田丽",         "mobile": "***********",         "phone": "",         "countryCode": "CHN",         "prov": "上海",         "city": "上海市",         "area": "嘉定区",         "address": "站前西路永利酒店斜对面童装店"     },     "weight": "0.02",     "totalQuantity": 0,     "goods": [         {             "goodName": "药品",             "number": 1,         }     ] } ``` | ``` {     "logisticNo": "TEST20220704210006",     "createOrderTime": "2022-07-04 12:00:53", } ``` |
| 取消面单 | ``` {     "logisticNo": "10000000001299",     "reason": "退款" } ``` | { "result": "success",  "message": "成功"} |
| 拦截面单 | {  "logisticNo": "10000000001299",  "reason": "退款" } | { "result": "success",  "message": "成功"} |
| 修改地址 | ``` {     "logisticNo": "UT0000456908252",     "reason": "修改原因",     "receiveProvince": "上海",     "receiveCity": "上海市",     "receiveDistrict": "浦东新区",     "receiveAddress": "详细地址",     "receiveUsername": "收件⼈姓名",     "receiveMobilPhone": "18812345678",     "receivePhone": "" } ``` | { "result": "success",  "message": "成功"} |
| 轨迹查询 | {  "logisticNo": "10000000001299" } | { "result": "success", "curr_address": "漳州市", "next_address": "", "message": "【漳州市】快件已被【妈妈驿站的学府花园三通一达店妈妈驿站】代收，如有问题请电联（13860810307），感谢使用中通快递，期待再次为您服务!"} |


## 接口设计

*新增、修改的接口定义；流量预估，接口性能设计。*

## 安全设计

*时刻警惕资损问题；数据一致性、接口防刷、幂等设计等。*

## 监控报警

*需要思考上线后如何监控，及时响应止损、回滚、降级等方案。*

## 质量效率

*本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。*

项目开发规范、项目结构按照订单重构DDD方式进行

参考：

# 里程碑

备注：日期统一使用日期组件进行填写；如无特殊情况，里程碑日期、里程碑起止时间、工时等都需要填写。

|  | **里程碑** | 里程碑日期（填写完成日期） | 里程碑起止时间 | 工时(pd) | 备注 |
| --- | --- | --- | --- | --- | --- |
| 开始日期 | 结束日期 |
| 1 | PRD评审 |  |  |  |  |  |
| 2 | 技术方案-设计 |  |  |  |  |  |
| 3 | 技术方案-评审 |  |  |  |  |  |
| 4 | 研发 |  |  |  |  |  |
| 5 | 自测 |  |  |  |  |  |
| 6 | 联调 |  |  |  |  |  |
| 7 | 提测 |  |  |  |  |  |
| 8 | 测试 |  |  |  |  |  |
| 9 | 上线 |  |  |  |  |  |


# 项目排期

| 1 | 快递商配置 | 1.快递商列表查询2.快递商配置添加修改 |  |  |  |  |  |
| 2 | 电子面单模板 | 1.模板添加2.模板查询 |  |  |  |  |  |
| 3 | 发货单模板 | 1.模板添加2.模板查询 |  |  |  |  |  |
| 4 | 店铺快递配置 | 1.B2C店铺关联平台查询(feign调用)2.新增店铺快递配置3.修改店铺基础配置4.电子面单自定义配置5.发货单自定义配置6.分页查询店铺快递配置 |  |  |  |  |  |
| 5 | 创建物流单 | 1.物流单创建流程 |  |  |  |  |  |
| 6 | 取消物流单 | 1.物流取消流流程2.物流单拦截流程 |  |  |  |  |  |
| 7 | 物流地址修改 | 1. 请求三方修改物流地址2.记录修改日志 |  |  |  |  |  |
| 8 | 物流轨迹 | 1.监听三方快递商推送物流消息2.查询三方快递商物流信息3.物流轨迹查询 |  |  |  |  |  |
| 9 | 三方快递商接口对接 | 1.创单接口2.修改接口3.取消接口4.拦截接口5.物流查询接口6.物流轨迹监听接口7.标准电子模板拉取 |  |  |  |  |  |
| 10 | 面单打印 | 1.推送面单打印消息 |  |  |  |  |  |
| 11 | 定时任务 | 1.物流轨迹定时查询2.物流状态定时查询 |  |  |  |  |  |
| 12 | 数据迁移 | 1.迁移配置数据 |  |  |  |  |  |
| 13 | 功能自测 |  |  |  |  |  |  |
| 14 | 联调 | 1.内部联调 |  |  |  |  |  |
|  | 功能模块 | 功能项 | 优先级 | 工时(PD) | 主R | 计划完成日期 | 研发进展 |


# 上线方案

备注：技术方案设计期间，需要提前建立好需求对应的提测清单（必须）、上线清单（可选）文档。

|  | 清单项 | 清单地址 | 说明 |
| --- | --- | --- | --- |
| 1 | 提测清单 |  | 1. 提测清单建立为技术方案的子文档，命名为：xxx(通常为需求名称)-提测清单。 2. 提测前RD需要及时维护最新信息，并识别不同环境之间的差异。 |
| 2 | 上线清单 |  |  |