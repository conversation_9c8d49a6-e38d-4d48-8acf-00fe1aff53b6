# ####-1 上线清单模板 (多个需求)

****

**模板适用场景：模板适用于多个“业务需求”或“技术专项”需要在同一个发布日上线时使用，做为一个总的纲要性文档来描述上线信息，但各个需求需要拆分到子文档来具体说明。**

# 一、迭代概要

| **迭代内容** | 示例：（1）常用设备校验，（2）通用ORG组件能力， |
| --- | --- |
| **上线项目** | 示例：（1）assist-hcm （2）yxt-basis |
| **上线时间** |  |
| **产研负责人****** | 产品（PM） |  |
| 研发（RD） |  |
| 测试（QA） |  |
| 代码审查（Code Reviewer） |  |
| **迭代总负责人****--组织迭代发布** |  |
| **产品PRD** |  |
| **技术方案** |  |
| **是否测试介入** |  |
| *******测试报告** |  |
| *******回退方案** |  |
| **其他(注意事项）** |  |


# 二、迭代依赖

## 2.1 迭代·依赖上游服务

|  | Git项目名称 | 最新Jar版本 | 服务情况(风险/性能/可用性) | 上游·主R |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |


## 2.2 迭代·影响下游服务

|  | 我方·Git项目名称 | 我方·最新Jar版本 | 我方·提供的新(变化)接口 | 我方·主R | 下游服务·主R |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |
| 2 |  |  |  |  |  |


# 三、迭代项目

|  | GIT项目名称（按照项目发布顺序填写） | 干系人（哪些同学有此项目的代码变更） |
| --- | --- | --- |
| 1 |  |  |
| 2 |  |  |
| 3 |  |  |


# 四、发版检查项或操作步骤

|  | **发版检查项或操作步骤** | **补充说明** |
| --- | --- | --- |
| 1 | Red强制检查项 迭代所需代码->已正确合并到master分支 |  |
| 2 | Red强制检查项 迭代所需配置->已正确配置到预发或生产环境 |  |
| 3 | Red强制检查项 预发环境->QA测试通过 |  |
| 4 | Red强制检查项 上游项目->已正确发布 |  |
| 5 | xx其他，自行填写 |  |


# 五、风险评估（必填）

|  | **风险问题** | 风险评估说明 | **主R** |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |
| 3 |  |  |  |