# OSS存储规范

****

# 1. 目的

统一对象存储服务（OSS）的使用标准，确保数据存储路径清晰、层级合理、易于维护，提升文件检索效率与系统可扩展性。

# 2. 背景

1. sk-pro-dscloud中存在过多历史无用数据
2. sk-pro-dscloud中的文件归属划分不明确
  1. 
3. 


# 3. 适用范围

适用于所有用过OSS存储的业务文件，包括不限于：

1. 业务系统导出的数据文件（如订单、报表）
2. 中间处理文件或临时文件
3. 静态资源文件（如图片、文档）


当前存在于sk-pro-dscloud中的文件如下：[https://doc.weixin.qq.com/sheet/e3_AZcARQYcAOwRITfsxpRSkat5MHRgB?scode=AOsAFQcYAAckExijUYAZcARQYcAOw&tab=BB08J2](https://doc.weixin.qq.com/sheet/e3_AZcARQYcAOwRITfsxpRSkat5MHRgB?scode=AOsAFQcYAAckExijUYAZcARQYcAOw&tab=BB08J2)

# 4. 存储路径规范

## 4.1 路径格式

文件存储路径需严格按照以下层级结构定义：{Bucket}/{Domain}/{Application}/{Function}/{YYYYMM}/FileName

1. 层级说明：| 层级 | 说明 | 示例 |
| --- | --- | --- |
| Bucket | 桶名 | sk-pro-dscloud |
| Domain | 所属业务领域（如订单、用户、支付） | order |
| Application | 系统或服务名称（需与系统设计文档命名一致） | hydee-business-order |
| Function | 功能模块或操作类型（如导出、备份、日志） | 导出：export，导入：import ，模版：template |
| YYYYMM | 文件生成年月（格式为6位数字，如202503） | 202503 |
2. 完整示例：sk-pro-dscloud/order/hydee-business-order/export/202503/订单导出列表-20250311-64fb98fac1a947e596c5782de86cc3a8.zip


## 4.2 命名规则

### 4.2.1 文件命名格式

文件名需包含以下信息（按顺序拼接，以连字符 `-` 分隔）：{功能标识}-{日期}-{UUID}.{扩展名}

1. 业务表示：文件的业务类型（如订单导出列表）
2. 日期：文件生成日期，格式为 `YYYYMMDD`（如20250311）
3. UUID：全局唯一标识符（推荐使用 UUID v4，全小写，无连字符）
4. 扩展名：文件类型后缀（如 `.zip`, `.csv`, `.log`）


### 4.2.2 禁止行为

1. 禁止使用中文、空格或特殊字符（如 `!@#$%^&*()`）。
2. 禁止在路径中插入冗余层级（如 `/temp/` 或 `/backup/`）。


## 4.3 生命周期策略（定期删除规则）

OSS定时删除任务依赖于生命周期规则。生命周期规则定义了存储桶内对象的生命周期，包括对象的过期时间、删除时间等。

当对象达到生命周期规则中定义的条件（如 `export`）时，OSS会自动执行删除操作。

### 4.3.1 规则定义

1. **删除周期：**
  1. 功能目录（如 `export`）下的文件保留 **1个月**，生成满30天后自动删除。
2. 功能目录（如 `export`）下的文件保留 **1个月**，生成满30天后自动删除。
3. **规则范围**：
  1. 按路径前缀匹配目标目录（如 `{Bucket}/{Domain}/{Application}/export/`）。
  2. 仅对功能目录生效，不影响其他层级（如 `backup`、`log` 需单独配置）。
4. 按路径前缀匹配目标目录（如 `{Bucket}/{Domain}/{Application}/export/`）。
5. 仅对功能目录生效，不影响其他层级（如 `backup`、`log` 需单独配置）。
6. **时间基准：**
  1. 以文件最后修改时间（Last Modified Time）为保留周期起始时间。
7. 以文件最后修改时间（Last Modified Time）为保留周期起始时间。


说明：

- 此规则通过 OSS 原生生命周期功能实现，无需额外开发成本。
- 若业务需保留超期文件（如处方图片需要），可提交豁免申请，将文件迁移至归档存储桶。


# 5. 定时删除优点

1. **降低存储成本**：自动清理过期文件，避免不必要的存储费用。
2. **提升管理效率**：减少手动清理工作的繁琐，自动化管理文件生命周期。
3. **灵活性高**：支持细粒度的规则配置，能满足各种不同需求。


通过本规范，可确保 OSS 存储路径的标准化与可追溯性，降低运维成本。实际使用中需结合业务需求定期复审优化。