# DDD资料

### DDD示例

trueDDD详细示例falseautotoptrue21601

### 基于业务分包

基于业务分包,不基于技术分包 —— 对应DDD中的聚合根

├── order
│   ├── OrderApplicationService.java
│   ├── OrderController.java
│   ├── OrderNotFoundException.java
│   ├── OrderRepository.java
│   ├── OrderService.java
│   └── model
│       ├── Order.java
│       ├── OrderFactory.java
│       ├── OrderId.java
│       ├── OrderItem.java
│       └── OrderStatus.java

在order包下我们直接放置了OrderController和OrderRepository等类，而**没有必要再为这些类划分单独的子包**。

而对于领域模型Order来讲，**由于包含了多个对象，因此基于内聚性原则将它们归到model包中**。但是这并不是一个必须，如果业务足够简单，我们甚至可以将所有类直接放到业务包下。

> 内聚性：又称块内联系。指模块的功能强度的度量，即一个模块内部各个元素彼此结合的紧密程度的度量。若一个模块内各元素（语名之间、程序段之间）联系的越紧密，则它的内聚性就越高。

#### 业务分包的优点

- 只需要在单个统一的包下修改代码，减少了代码导航成本
- 如果哪天我们需要将某个业务迁移到另外的项目（比如识别出了独立的微服务），那么直接整体移动业务包即可。


优先进行业务分包，然后对于一些不隶属于任何业务的代码可以单独分包，比如一些util类、公共配置等。比如我们依然可以创建一个common包，下面放置了Spring公共配置、异常处理框架和日志等子包：

└── common
    ├── configuration
    ├── exception
    ├── loggin
    └── utils

**总结:** 在DDD的战略设计中，我们关注于从一个宏观的视角俯视整个软件系统，然后通过一定的原则对系统进行子域和限界上下文的划分。在战术实践中，我们也通过类似的提纲挈领的方法进行整体的代码结构的规划，所采用的原则依然逃离不了“内聚性”和“职责分离”等基本原则

### 领域驱动设计(Domain Driven Design,DDD)

DDD尝试通过其自有的原则与套路来解决软件的复杂性问题，它将研发者的目光首先聚焦在业务本身上，使技术架构和代码实现成为软件建模过程中的“副产品”.

- 战略设计: 核心是子域和限界上下文，通过一定的手段使软件系统在人的大脑中更加有条理地呈现，让作为“目的”的人能够更简单地了解进而掌控软件系统
- 战术设计: 偏向于编码实现。DDD战术设计的目的是使得业务能够从技术中分离并突显出来，让代码直接表达业务的本身，其中包含了聚合根、应用服务、资源库、工厂等概念。


**子域**: 子域是对领域从业务需求方面进行拆分，是**逻辑上的**。比如一个电商系统，我们可以很明确地可以把它拆分成商品子域、销售子域、仓储子域、客服子域……

**限界上下文**: 限界上下文依然是软件模块化的一种体现。就简单理解为模块

> 更好的理解子域和限界上下文: https://www.yasinshaw.com/articles/79

#### 三种实现业务的方式

##### “Service + 贫血模型”

> 目前订单OMS系统就是这种模式。

存在一个贫血的“领域对象”，业务逻辑通过一个Service类实现，然后通过setter方法更新领域对象，最后通过DAO(多数情况下可能使用诸如Hibernate之类的ORM框架)保存到数据库中

@Transactional
public void changeProductCount(String id, ChangeProductCountCommand command) {
    Order order = DAO.findById(id);
    if (order.getStatus() == PAID) {
        throw new OrderCannotBeModifiedException(id);
    }
    OrderItem orderItem = order.getOrderItem(command.getProductId());
    orderItem.setCount(command.getCount());
    order.setTotalPrice(calculateTotalPrice(order));
    DAO.saveOrUpdate(order);
}

以上代码存在的问题:

- 面向过程的编程范式，违背了最基本的OO原则
- 职责划分模糊不清，使本应该内聚在Order中的业务逻辑泄露到了其他地方(OrderService)
- Order只是充当数据容器的贫血模型(Anemic Model)，并不是真正意义上的领域模型
- 这类代码如果过多,业务逻辑会分散在不同的Service类中，最终的结果是代码变得越来越难以理解进而逐渐丧失扩展能力


##### 基于事务脚本的实现

计算出的结果直接保存到数据库(或者有时都没有Service类，直接通过SQL实现业务逻辑)

@Transactional
public void changeProductCount(String id, ChangeProductCountCommand command) {
    OrderStatus orderStatus = DAO.getOrderStatus(id);
    if (orderStatus == PAID) {
        throw new OrderCannotBeModifiedException(id);
    }
    DAO.updateProductCount(id, command.getProductId(), command.getCount()); // 直接通过SQL去变更...
    DAO.updateTotalPrice(id);
}

可以看到，DAO中多出了很多方法，此时的DAO不再只是对持久化的封装，而是也会包含业务逻辑。另外，DAO.updateTotalPrice(id)方法的实现中将直接调用SQL来实现Order总价的更新。与“Service+贫血模型”方式相似，事务脚本也存在业务逻辑分散的问题。

##### 基于领域对象的实现

核心的业务逻辑被内聚在行为饱满的领域对象(Order)中

├── order
│   ├── OrderApplicationService.java
│   ├── OrderController.java
│   ├── OrderPaymentProxy.java
│   ├── OrderPaymentService.java
│   ├── OrderRepository.java
│   ├── command
│   │   ├── ChangeAddressDetailCommand.java
│   │   ├── CreateOrderCommand.java
│   │   ├── OrderItemCommand.java
│   │   ├── PayOrderCommand.java
│   │   └── UpdateProductCountCommand.java
│   ├── exception
│   │   ├── OrderCannotBeModifiedException.java
│   │   ├── OrderNotFoundException.java
│   │   ├── PaidPriceNotSameWithOrderPriceException.java
│   │   └── ProductNotInOrderException.java
│   ├── model
│   │   ├── Order.java
│   │   ├── OrderFactory.java
│   │   ├── OrderId.java
│   │   ├── OrderIdGenerator.java
│   │   ├── OrderItem.java
│   │   └── OrderStatus.java
│   └── representation
│       ├── OrderItemRepresentation.java
│       ├── OrderRepresentation.java
│       └── OrderRepresentationService.java

**从上面可以看出有以下分包原则:**

- 在所有类已经被内聚在了order包下的情况下，如果代码结构足够的简单，那么没有必要再次进行子包的划分
- 而如果多个类需要做再次的内聚，那么需要另行分包. 例如: representation
- model包用于放置所有与Order聚合根相关的领域对象
- 基于同类型相聚原则，创建command包和exception包分别用于放置请求类和异常类


**ApplicationService:**

在DDD中，由于业务被提到了第一优先级，那么自然地我们希望**对业务的处理能够显现出来**，为了达到这样的目的，DDD专门提供了一个名为应用服务(ApplicationService)的抽象层。ApplicationService采用了门面模式，**作为领域模型向外提供业务功能的总出入口**

ApplicationService的**实现遵循一个很简单的原则**，即一个业务用例对应ApplicationService上的一个业务方法。

调用关系: OrderController调用OrderApplicationService

ApplicationService:

@Transactional
public void changeProductCount(String id, ChangeProductCountCommand command) {
    Order order = orderRepository.byId(orderId(id));
    order.changeProductCount(ProductId.productId(command.getProductId()), command.getCount());
    orderRepository.save(order);
}

OrderController调用OrderApplicationService:
@PostMapping("/{id}/products")
public void changeProductCount(@PathVariable(name = "id") String id, @RequestBody @Valid ChangeProductCountCommand command) {
    orderApplicationService.changeProductCount(id, command);
}

**ApplicationService需要遵循以下原则:**

- 业务方法与业务用例一一对应(业务用例就是具体的业务)
- 业务方法与事务一一对应：也即每一个业务方法均构成了独立的事务边界
- 本身不应该包含业务逻辑：业务逻辑应该放在领域模型中实现，更准确的说是放在聚合根中实现。故ApplicationService应该是很薄的一层
- 与UI或通信协议无关：ApplicationService的定位并不是整个软件系统的门面，而是领域模型的门面，这意味着ApplicationService不应该处理诸如UI交互或者通信协议之类的技术细节。与UI交互或者通信协议之类的是Controller参与
- 接受原始数据类型：**ApplicationService作为领域模型的调用方**，领域模型的实现细节对其来说应该是个黑盒子，因此ApplicationService不应该引用领域模型中的对象。此外，ApplicationService接受的请求对象中的数据仅仅用于描述本次业务请求本身，在能够满足业务需求的条件下应该尽量的简单。因此，ApplicationService通常处理一些比较原始的数据类型。在本例中，OrderApplicationService所接受的Order ID是Java原始的String类型，在调用领域模型中的Repository时，才被封装为OrderId对象。


### 业务的载体——聚合根

聚合根(Aggreate Root, AR)就是软件模型中那些最重要的以名词形式存在的领域对象，DDD中所有的战术实现都围绕着聚合根展开。

所谓“聚合”，顾名思义，即需要将领域中高度内聚的概念放到一起组成一个整体。至于哪些概念才能聚到一起，需要我们对业务本身有很深刻的认识.

示例代码:

public void changeProductCount(ProductId productId, int count) {
    if (this.status == PAID) {
        throw new OrderCannotBeModifiedException(this.id);
    }

    OrderItem orderItem = retrieveItem(productId);
    orderItem.updateCount(count);
    this.totalPrice = calculateTotalPrice();
}

private BigDecimal calculateTotalPrice() {
    return items.stream()
            .map(OrderItem::totalPrice)
            .reduce(ZERO, BigDecimal::add);
}

private OrderItem retrieveItem(ProductId productId) {
    return items.stream()
            .filter(item -> item.getProductId().equals(productId))
            .findFirst()
            .orElseThrow(() -> new ProductNotInOrderException(productId, id));
}

聚合根应该保证业务上的一致性。在DDD中，业务上的一致性被称为不变条件(Invariants)。 核心关键词是因果。订单商品数量的调整必然会影响价格,所以需要保证这种必然。

聚合根简单的理解就是将与领域相关的聚合在一次，包含对象、方法等。**聚合根的一致性,就是将一次业务的聚合完全交由聚合根处理,而不是有Service层把控**,因为如果由Service层把控,则会失控,因为这是朝着“Service + 贫血模型”的方向走了

对聚合根的设计需要提防上帝对象(God Object)，也即用一个大而全的领域对象来实现所有的业务功能。这样会让代码描述的业务逻辑又渐渐的不清晰了。聚合根不应该有二义性，如果使用God Object就存在二义性,因为什么都能表示,什么都能处理…

要解决这样的问题依然需要求助于**限界上下文(Context)，不同限界上下文使用各自的通用语言(Ubiquitous Language)，通用语言要求一个业务概念不应该有二义性**，在这样的原则下，不同的限界上下文可能都有自己的Product类，虽然名字相同，却体现着不同的业务。

这里的理解就是进一步的聚合 + Product适配不同的业务场景

除了内聚性和一致性，聚合根还有以下特征：

- 聚合根的实现应该与框架无关，最好是POJO。这样如果换技术框架,就方便了,只需拷贝聚合根到新的框架就OK
- 聚合根内部的所有变更都必须通过聚合根完成
- 聚合根之间的引用通过ID完成。在聚合根边界设计合理的情况下，一次业务用例只会更新一个聚合根
- 如果一个事务需要更新多个聚合根，首先思考一下自己的聚合根边界处理是否出了问题，因为在设计合理的情况下通常不会出现一个事务更新多个聚合根的场景。如果这种情况的确是业务所需，那么考虑引入消息机制和事件驱动架构，保证一个事务只更新一个聚合根，然后通过消息机制异步更新其他聚合根。—— 订单OMS在前期实践DDD的过程中,这块很难处理。
- 聚合根不应该引用基础设施。
- 外界不应该持有聚合根内部的数据结构
- 尽量使用小聚合


### 实体 vs 值对象

实体对象表示的是具有一定生命周期并且拥有全局唯一标识(ID)的对象，比如本文中的Order和Product，而值对象表示用于起描述性作用的，没有唯一标识的对象，比如Address对象。

聚合根一定是实体对象，但是并不是所有实体对象都是聚合根，同时聚合根还可以拥有其他子实体对象。聚合根的ID在整个软件系统中全局唯一，而其下的子实体对象的ID只需在单个聚合根下唯一即可

#### 实体、值对象的相等性

- 实体对象的相等性是通过ID来完成的
- 值对象来说，相等性的判断是通过属性字段来完成的


值对象还有一个特点是不变的(Immutable)，也就说**一个值对象一旦被创建出来了便不能对其进行变更**，如果要变更，必须重新创建一个新的值对象整体替换原有的。值对象的不变性使得程序的逻辑变得更加简单，你不用去维护复杂的状态信息，需要的时候创建，不要的时候直接扔掉即可，使得值对象就像程序中的过客一样。在DDD建模中，一种受推崇的做法便是将业务概念尽量建模为值对象。

> 什么对象是值对象，什么对象是实体对象,并没有一个严格的定义，具体是什么根据业务需要来限定。业务有要求就是实体对象，没有要求就是值对象

实体和值对象的划分并不是一成不变的，而应该根据所处的限界上下文来界定，相同一个业务名词，在一个限界上下文中可能是实体，在另外的限界上下文中可能是值对象。

### 资源库Repository

资源库(Repository)就是用来持久化聚合根的。

DAO的设计初衷只是对数据库的一层很薄的封装，而Repository是更偏向于领域模型。在所有的领域对象中，只有聚合根才“配得上”拥有Repository，而DAO没有这种约束。

Repository只是负责将其状态从计算机的内存同步到持久化机制中，从这个角度讲，Repository只需要一个类似save()的方法便可完成同步操作

DDD中读操作和写操作是两种很不一样的过程，建议是尽量将此二者分开实现，由此查询功能将从Repository中分离出去。

**应用服务作为总体协调者，先通过资源库获取到聚合根，然后调用聚合根中的业务方法，最后再次调用资源库保存聚合根。**

#### 如何建聚合根? — 可以直接放到领域包下

聚合根的创建过程可简单可复杂，有时可能直接调用构造函数即可，而有时却存在一个复杂的构造流程，比如需要调用其他系统获取数据等。通常来讲，Factory有两种实现方式：

- 直接在聚合根中实现Factory方法，常用于简单的创建过程
- 独立的Factory类，用于有一定复杂度的创建过程，或者创建逻辑不适合放在聚合根上


创建聚合根通常通过设计模式中的工厂(Factory)模式完成，这一方面可以享受到工厂模式本身的好处，另一方面，DDD中的Factory还具有将“聚合根的创建逻辑”显现出来的效果

public static Product create(String name, String description, BigDecimal price) { // 显现出来，与业务相关
    return new Product(name, description, price); //并不包含创建逻辑，而是将创建过程直接代理给了Product的构造函数
}

private Product(String name, String description, BigDecimal price) { //构造函数本身是一个非常技术的东西，和业务无关
    this.id = ProductId.newProductId();
    this.name = name;
    this.description = description;
    this.price = price;
    this.createdAt = Instant.now();
}

稍微复杂点的聚合根创建

@Component // Bean
public class OrderFactory {
    private final OrderIdGenerator idGenerator;

    public OrderFactory(OrderIdGenerator idGenerator) {
        this.idGenerator = idGenerator;
    }

    public Order create(List items, Address address) {
        OrderId orderId = idGenerator.generate();
        return Order.create(orderId, items, address);
    }
}

### 必要的妥协——创建领域服务

前面我们提到，聚合根是业务逻辑的主要载体，也就是说业务逻辑的实现代码应该尽量地放在聚合根或者聚合根的边界之内。但有时，有些业务逻辑并不适合于放在聚合根上，比如前文的OrderIdGenerator便是如此，在这种“迫不得已”的情况下，我们引入领域服务(Domain Service) —— 简单的理解就是解耦

**这种妥协越少越好,如果多，就要考虑业务边界是不是不清晰**

### Command对象

通常来说，DDD中的写操作并不需要向客户端返回数据，在某些情况下(比如新建聚合根)可以返回一个聚合根的ID，这意味着ApplicationService或者聚合根中的写操作方法通常返回void即可。

建议参数少的时候，也用Command来封装。保持统一

**总结: 创建聚合根通过Factory完成；业务逻辑优先在聚合根边界内完成；聚合根中不合适放置的业务逻辑才考虑放到DomainService中。**

****

**在DDD的写操作中，我们需要严格地按照“应用服务 -> 聚合根 -> 资源库”的结构进行编码**

### DDD的读操作

3种读操作的方式：

- 基于领域模型的读操作
- 基于数据模型的读操作
- CQRS


无论哪种读操作方式，都需要遵循一个原则：领域模型中的对象不能直接返回给客户端，因为这样领域模型的内部便暴露给了外界，而对领域模型的修改将直接影响到客户端(因为有时我们要根据业务修改一些字段)。

在读操作中，我们通过Representation后缀进行展现数据的统一，这里的Representation也即REST中的“R”

#### 基于领域模型的读操作(读写操作糅合在了一起，不推荐)

@Transactional(readOnly = true)
public OrderRepresentation byId(String id) {
    Order order = orderRepository.byId(orderId(id));
    return orderRepresentationService.toRepresentation(order);
}

public OrderRepresentation toRepresentation(Order order) {
    List itemRepresentations = order.getItems().stream()
            .map(orderItem -> new OrderItemRepresentation(orderItem.getProductId().toString(),
                    orderItem.getCount(),
                    orderItem.getItemPrice()))
            .collect(Collectors.toList());

    return new OrderRepresentation(order.getId().toString(),
            itemRepresentations,
            order.getTotalPrice(),
            order.getStatus(),
            order.getCreatedAt());
}

这种方式的优点是非常直接明了，也不用创建新的数据读取机制，直接使用Repository读取数据即可。然而缺点也很明显：

- 一是读操作完全束缚于聚合根的边界划分，比如，如果客户端需要同时获取Order及其所包含的Product，那么我们需要同时将Order聚合根和Product聚合根**加载到内存再做转换操作**，这种方式既繁琐又低效；
- 二是在读操作中，通常需要基于不同的查询条件返回数据，比如通过Order的日期进行查询或者通过Product的名称进行查询等，这样导致的结果是Repository上**处理了太多的查询逻辑，变得越来越复杂**，也逐渐偏离了Repository本应该承担的职责。


#### 基于数据模型的读操作(绕过聚合根和资源库，直接返回数据，推荐)

这种方式绕开了资源库和聚合，直接从数据库中读取客户端所需要的数据，此时写操作和读操作共享的只是数据库。

@Transactional(readOnly = true)
public PagedResource listProducts(int pageIndex, int pageSize) {
    MapSqlParameterSource parameters = new MapSqlParameterSource();
    parameters.addValue("limit", pageSize);
    parameters.addValue("offset", (pageIndex - 1) * pageSize);

    List products = jdbcTemplate.query(SELECT_SQL, parameters,
            (rs, rowNum) -> new ProductSummaryRepresentation(rs.getString("ID"),
                    rs.getString("NAME"),
                    rs.getBigDecimal("PRICE")));

    int total = jdbcTemplate.queryForObject(COUNT_SQL, newHashMap(), Integer.class);
    return PagedResource.of(total, pageIndex, products);
}

这种方式的优点是读操作的过程不用囿于领域模型，而是基于读操作本身的需求直接获取需要的数据即可，一方面简化了整个流程，另一方面大大提升了性能。但是，由于读操作和写操作共享了数据库，而此时的数据库主要是对应于聚合根的结构创建的，因此读操作依然会受到写操作的数据模型的牵制。不过这种方式是一种很好的折中，微软也提倡过这种方式。

#### CQRS(读写操作分别使用不同的数据库)

Command Query Responsibility Segregation,命令查询职责分离

在CQRS中写操作和读操作**使用了不同的数据库**.数据从写模型数据库**同步到**读模型数据库，通常通过领域事件的形式同步变更信息。这样一来，读操作便可以根据自身所需独立设计数据结构，而不用受写模型数据结构的牵制。—— 目前订单OMS不做这个操作,不然数据一致性是一个问题

CQRS旨在解决软件中日益复杂的查询问题。如果不做CQRS,将查询逻辑与业务逻辑糅合在一起会使软件迅速腐化，诸如逻辑混乱、可读性变差以及可扩展性降低等等一些列问题。我们经常遇到的一个问题是，为了一个查询在订单表上加的字段越来越多，而这些操作只是为了一个或者某些字段的查询

#### 约定

- 客户端的请求数据类统一使用相同后缀，比如Command
- 返回给客户端的数据统一使用相同后缀，比如Represetation


### DDD中的异常形式

- 层级式: 每种具体的异常都对应了一个异常类，这些类最终继承自某个父异常
- 单一式: 整个程序中只有一个异常类，再以一个字段来区分不同的异常场景


#### 最佳实践

- 大部分场景: 单一式 + 表意清楚的枚举 + Description
- 个别场景: 可以使用层级式异常


#### 异常内容

- 统一异常格式
- 异常信息不包含上下文,最好是结构化的数据,应为便于解析,做后续处理


示例:

{
  requestId: "d008ef46bb4f4cf19c9081ad50df33bd",
  error: {
    code: "ORDER_NOT_FOUND",
    status: 404,
    message: "没有找到订单",
    path: "/order",
    timestamp: 1555031270087,
    data: {
      orderId: "123456789"
    }
  }
}

ORDER_NOT_FOUND与data中的数据结构是一一对应的，也即对于客户端来讲，如果发现了ORDER_NOT_FOUND，那么便可确定data中一定存在orderId字段，进而完成精确的结构化解析。

---

DDD抽象出了更多的概念与原则，遵守这些概念和原则就很容易实现DDD

其他资料:

[https://zhuanlan.zhihu.com/p/482234876](https://zhuanlan.zhihu.com/p/482234876)