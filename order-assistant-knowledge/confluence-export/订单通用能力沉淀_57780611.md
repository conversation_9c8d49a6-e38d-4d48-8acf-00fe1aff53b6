# 订单通用能力沉淀

### 已经沉淀到部门通用组件中:

### 背景

在实际项目开发中，我们经常会遇到大量的通用业务逻辑横贯于不同的业务模块中。由于缺乏合理的抽象设计和统一的规范，这些共性逻辑往往被开发人员重复实现，导致代码高度重复、职责不清晰。这种情况不仅违背了DRY(Don't Repeat Yourself)原则，也给后期的代码维护带来了巨大的技术债务。

具体表现为：

1. 相似的业务逻辑分散在不同的类中，违背了面向对象设计中的单一职责原则(SRP)
2. 代码复用性差，存在大量的Copy-Paste代码，增加了代码的维护成本
3. 当需要修改某个通用逻辑时，需要全局搜索并逐一修改，容易出现遗漏，增加了系统的脆弱性
4. 对测试团队造成了额外的负担，需要重复验证相似的业务场景
5. 代码质量难以把控，每次修改都存在潜在的regression风险


这种情况亟需引入设计模式和架构改进，比如：

- 运用模板方法模式(Template Method Pattern)抽象通用流程
- 使用策略模式(Strategy Pattern)处理差异化逻辑
- 通过AOP切面来处理横切关注点
- 建立统一的业务组件库
- 完善单元测试覆盖


### 目标

提升代码的可维护性，降低系统的复杂度。提升代码的可测试性

### 通用能力

借着会员消费记录需求,新系统已经实现以下逻辑的抽象:

- 抽象Canal消息统一处理逻辑 *AbstractCanalHandler*
- 抽象基于游标的刷数逻辑 *AbstractFlash*
- 抽象ES操作 *AbstractEsOperate, 与Canal消息处理解耦*
- 抽象ES数据与DB数据一致性校验及自动补偿逻辑 *AbstractConsistencyCheck\AbstractConsistencyCheckEfficientCount*
- 抽象超期数据清理逻辑 *AbstractClean*


*trueAbstractfalseautotoptrue9414*

**AbstractCanalHandler**

**关键设计特点**：

- 模板方法模式：通过抽象方法check()/assemble()实现业务逻辑扩展
- 线程安全：使用ThreadLocal隔离不同线程的消息数据
- 策略模式：通过abstractEsOperateList实现多种ES操作扩展
- 双入口设计：支持实时消息处理和人工触发同步


*trueAbstractCanalHandler falseautotoptrue12617*

**AbstractFlash**

**关键设计特点:**

- 双重路由机制


通过抽象类+模板方法的设计，将业务无关的通用逻辑（分片路由、游标管理、监控告警）与业务逻辑（数据获取、格式转换）解耦，提高了系统的可维护性和扩展性。

*trueAbstractFlashfalseautotoptrue5795*

**AbstractEsOperate**

该设计实现了ES操作的标准模板，保证系统可靠性：

1. **类型安全机制**：在check()阶段过滤非法数据类型
2. **上下文隔离机制**：ThreadLocal+泛型实现多维度隔离


*trueAbstractEsOperatefalseautotoptrue2591*

**AbstractConsistencyCheck\AbstractConsistencyCheckEfficientCount**

**双保险校验机制**：

- 采用补偿前检查+补偿后复查的双重校验
- 设置不同级别的告警提示（补偿中告警 vs 人工介入告警）


trueAbstractConsistencyCheckAbstractConsistencyCheckEfficientCountfalseautotoptrue9934

订单通用能力串联

true订单通用能力串联falseautotoptrue13115