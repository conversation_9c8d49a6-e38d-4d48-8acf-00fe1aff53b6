# 第53周 2023-12-28

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：4day**1. 购物车优化一期开发 2.5day 2. 美团配送方式获取bug修复 0.5day 3. 京东到家自配送方式修改 0.5day 4. 抖店o2o配送相关接口梳理 0.5day | **㊀计划工作**1. 购物车优化1期上线 2. 抖店o2o接口完善 **㊁实际完成**1. 购物车优化1期进入测试环节 2. 抖店o2o配送接口梳理 3. 美团配送方式获取bug修复 4. 京东到家自配送方式修改 **㊂遗留问题**1. 购物车优化1期因为共享库存问题，汪骁决定下周二上线 **㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 抖店o2o开发 2. 购物车优化2期 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 | 王世达 | **本周总工时：4day**1. 海典pos 对接 2day 2. AJG5旗舰店订单部分走O2O问题处理 1day 3. 云仓订单零元购未调用三方服务商问题处理 1day | **㊀计划工作**1. 海典pos 对接 2. AJG5旗舰店订单部分走O2O问题处理 3. 云仓订单零元购未调用三方服务商问题处理 **㊁实际完成**1. 海典pos 对接 退款单下账多情况联调通过 2. AJG5旗舰店订单部分走O2O问题处理 处理完成，dev环境验证通过，上线延迟到元旦后 3. 云仓订单零元购未调用三方服务商问题处理 已上线且验证通过 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：4day**1. 抖店O2O新订单流程 2. 平台配送转自配送配合 3. 修复未释放库存数据 4. 多条重复配送修复 5. 释放库存并发问题定位 6. 迁移代码修改 7. 雨诺迁移订单任务创建调整 8. 订单发生部分退款后，下账总金额计算错误 | **㊀计划工作**1. 抖店O2O新订单流程 2. 平台配送转自配送配合 3. 修复未释放库存数据 4. 多条重复配送修复 5. 释放库存并发问题定位 6. 迁移代码修改 7. 雨诺迁移订单任务创建调整 8. 订单发生部分退款后，下账总金额计算错误 **㊁实际完成**1. 抖店O2O新订单流程 2. 平台配送转自配送配合 3. 修复未释放库存数据 4. 多条重复配送修复 5. 释放库存并发问题定位 6. 迁移代码修改 7. 雨诺迁移订单任务创建调整 8. 订单发生部分退款后，下账总金额计算错误 **㊂遗留问题**1. 订单发生部分退款后，下账总金额计算错误 2. 抖店O2O对接 **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 4 |  | **本周总工时：4day**1.订单导出优化 | **㊀计划工作**1. 雨诺下账状态刷新修复 2. 订单导出优化 **㊁实际完成**1. 订单导出优化 90% **㊂遗留问题** **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 | 杨俊峰 | **本周总工时：4day****1.饿了么接口替换 2 day****2.技术支持 1.5 day****3.商品同步接口改造 0.5 day** | **㊀计划工作****1.饿了么接口替换 100%****2.产线支持** **㊃风险问题** 饿了么需要多观察**** | **** |  |
| 6 | 李洋 | **本周总工时：4day**1. 线上微信支付冻结资金 1day 2. pos对接 3day | **㊀计划工作** 1. pos对接 **㊁实际完成**1. 线上微信支付冻结资金 2. pos对接正逆向单定时任务编写，科传下账过滤海典pos下账，支持根据门店自动下账设置进行下账 **㊂遗留问题**1.前端需要增加因库存不足而自动下账失败的手动下账按钮 | **** |  |
| 7 | 崔建波 | **本周总工时：4day**1. 经营分析bug修复 3day 2. 经营分析性能同步测试 1day | **㊀计划工作**1、经营分析功能上线**㊁实际完成**1、经营分析完成开发并上线**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 元旦值班

| 时间 | 值班人员 | 请假人员 |
| --- | --- | --- |
| 12月29号 | 郭志明 | 徐国华 |
| 12月30号 | 李洋 |  |
| 12月31号 | 崔建波 |  |
| 1月1号 | 徐国华 |  |
| 1月2号 | 徐国华 |  |