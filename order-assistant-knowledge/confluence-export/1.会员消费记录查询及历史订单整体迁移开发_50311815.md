# 1.会员消费记录查询及历史订单整体迁移开发

联调接口**SDK:**<dependency>
  <groupId>com.yxt.order.open.sdk</groupId>
  <artifactId>order-open-sdk</artifactId>
  <version>memberTransactionHistory-SNAPSHOT</version>
</dependency>接口见: SDK使用方法见: 

#### 项目:

order-atom-service

order-service

order-framework

order-sync

order-batch-processing 有空再处理

hydee-business-order

hydee-business-order-web

#### 统一分支:

feature-member-transaction-history

**基于之前的开发分支继续开发,将feature-member-transaction-history内容合并进来**

migration-step-1-archive 已完成

  12 complete 合并feature-member-transaction-history至migration-step-1-archive  

**技术方案:**

**Apollo配置:**

order-atom-service

下面是测试环境的

spring:
  shardingsphere:
    datasource:
      # order-offline-0 线下单主业务库,order-offline-1 线下单主业务归档库
      names: order-offline-0,order-offline-1
      order-offline-0:
        url: ******************************************************************************************************************************************************  #开发
        username: agent
        password: WrHNOhOGHR8yzMEgKvao
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
      order-offline-1:
        url: **************************************************************************************************************************************************************  #开发
        username: agent
        password: WrHNOhOGHR8yzMEgKvao
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
    sharding:
      default-data-source-name: order-offline-0
      tables:
        offline_order:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_cashier_desk:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_cashier_desk_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_coupon:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_coupon_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_detail_pick:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_pick_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_promotion:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_promotion_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_organization:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_organization_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_pay:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_pay_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_prescription:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_prescription_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_user:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_user_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_detail:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_pay:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_pay_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_order_med_ins_settle:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_med_ins_settle_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_med_ins_settle:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_med_ins_settle_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_user:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_user_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_cashier_desk:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_cashier_desk_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_organization:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_organization_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm

  datasource:
    dynamic:
      datasource:
        # todo 需要改到高斯库
        order_offline_archive:
          url: **************************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao


# 会员消费记录topic
canal:
  member-transaction: order_member_transaction_record_topic

# 年月归档表 起始年月
yyMMDatabaseStartValue: 2406

# 订单一致性告警
esOrderMemberTransactionRecordAlertWebhook: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5a46a096-03b8-4590-a41d-684263021025

  32 complete 索引改成手动创建配置待整理  

easy-es:
  global-config:
    process-index-mode: manual

**SDK升级:**

    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>memberTransactionHistory-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.open.sdk</groupId>
      <artifactId>order-open-sdk</artifactId>
      <version>memberTransactionHistory-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.common</groupId>
      <artifactId>order-common</artifactId>
      <version>memberTransactionHistory-SNAPSHOT</version>
    </dependency>

**数据库变更**

  6 complete dev   7 complete test   8 complete prod  48 complete after_sale_order idx_refund_no   49 complete order_detail 无锁变更执行中   50 complete offline_order_detail_${seq},offline_refund_order_detail_${seq} 工单申请,执行 **单号 2294cd62-4ed4-4435-8f61-673edceab1a2**    

# 申请运维工单,使用华为云无锁变更工具执行,否则会出现主从数据同步延迟过大问题
ALTER TABLE `dscloud`.`order_detail`
ADD COLUMN `five_class` varchar(64) NULL COMMENT '商品五级分类编码',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `five_class_name` varchar(64) NULL COMMENT '商品五级分类Name',ALGORITHM=INPLACE, LOCK=NONE;
 
# 线下单明细添加字段
ALTER TABLE `dscloud_offline`.`offline_order_detail_${seq}`
ADD COLUMN `commodity_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格' ,ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `manufacture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生产商' ,ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `five_class` varchar(64) NULL COMMENT '商品五级分类编码',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `five_class_name` varchar(64) NULL COMMENT '商品五级分类Name',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',ALGORITHM=INPLACE, LOCK=NONE;
 
#需要添加字段,因为线下单有无小票退款,关联不到正单
ALTER TABLE `dscloud_offline`.`offline_refund_order_detail_${seq}`
ADD COLUMN `five_class` varchar(64) NULL COMMENT '商品五级分类编码',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `five_class_name` varchar(64) NULL COMMENT '商品五级分类Name',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `commodity_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品规格',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `manufacture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生产商',ALGORITHM=INPLACE, LOCK=NONE,
ADD COLUMN `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',ALGORITHM=INPLACE, LOCK=NONE;
 
ALTER TABLE `dscloud`.`after_sale_order`
ADD INDEX `idx_refund_no`(`refund_no`) USING BTREE COMMENT 'refund_no索引',ALGORITHM=INPLACE, LOCK=NONE;

**XXL-JOB**

> 0/50 * * * * ?

# 定时清理超期的ES数据
esExpiredDataCleanJob
# ES与DB数量一致性检查
consistencyCheckHandler

**刷现有数据到消费记录ES的脚步**

/1.0/es-member-order/flashDataToEs

**Canal配置:**

下面是模板,没有配置ip、用户名那些

#instance名: order_memberTransactionRecord_online prod
#ok

#################################################
## mysql serverId , v1.0.26+ will autoGen
# canal.instance.mysql.slaveId=0

# enable gtid use true/false
canal.instance.gtidon=false

# position info
canal.instance.master.address=ip:port
canal.instance.master.journal.name=mysql-bin.xxxxxxx
canal.instance.master.position=xxxxxx
canal.instance.master.timestamp=
canal.instance.master.gtid=

# rds oss binlog
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# table meta tsdb info
canal.instance.tsdb.enable=true
#canal.instance.tsdb.url=jdbc:mysql://127.0.0.1:3306/canal_tsdb
#canal.instance.tsdb.dbUsername=canal
#canal.instance.tsdb.dbPassword=canal

#canal.instance.standby.address =
#canal.instance.standby.journal.name =
#canal.instance.standby.position =
#canal.instance.standby.timestamp =
#canal.instance.standby.gtid=

# username/password
canal.instance.dbUsername=canal
canal.instance.dbPassword=Canal_123!
canal.instance.connectionCharset = UTF-8
# enable druid Decrypt database password
canal.instance.enableDruid=false
#canal.instance.pwdPublicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALK4BUxdDltRRE5/zXpVEVPUgunvscYFtEip3pmLlhrWpacX7y7GCMo2/JM6LeHmiiNdH1FWgGCpUfircSwlWKUCAwEAAQ==

# table regex https://github.com/alibaba/canal/wiki/AdminGuide
canal.instance.filter.regex=dscloud\\.order_info,dscloud\\.refund_order
# table black regex
canal.instance.filter.black.regex=
# table field filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.field=test1.t_product:id/subject/keywords,test2.t_company:id/name/contact/ch
# table field black filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.black.field=test1.t_product:subject/product_image,test2.t_company:id/name/contact/ch


# mq config
canal.mq.topic=order_member_transaction_record_topic
# dynamic topic route by schema or table regex
#canal.mq.dynamicTopic=mytest1.user,mytest2\\..*,.*\\..*
#canal.mq.partition=0
# hash partition config
canal.mq.partitionsNum=3
#canal.mq.partitionHash=test.table:id^name,.*\\..*
canal.mq.partitionHash=dscloud.order_info:order_no,dscloud.refund_order:order_no

#################################################

#instance名: order_memberTransactionRecord_offline prod

#################################################
## mysql serverId , v1.0.26+ will autoGen
# canal.instance.mysql.slaveId=0

# enable gtid use true/false
canal.instance.gtidon=false

# position info
canal.instance.master.address=ip:port
canal.instance.master.journal.name=mysql-bin.xxxxxxx
canal.instance.master.position=xxxxxxx
canal.instance.master.timestamp=
canal.instance.master.gtid=

# rds oss binlog
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# table meta tsdb info
canal.instance.tsdb.enable=true
#canal.instance.tsdb.url=jdbc:mysql://127.0.0.1:3306/canal_tsdb
#canal.instance.tsdb.dbUsername=canal
#canal.instance.tsdb.dbPassword=canal

#canal.instance.standby.address =
#canal.instance.standby.journal.name =
#canal.instance.standby.position =
#canal.instance.standby.timestamp =
#canal.instance.standby.gtid=

# username/password
canal.instance.dbUsername=canal
canal.instance.dbPassword=Canal_123!
canal.instance.connectionCharset = UTF-8
# enable druid Decrypt database password
canal.instance.enableDruid=false
#canal.instance.pwdPublicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALK4BUxdDltRRE5/zXpVEVPUgunvscYFtEip3pmLlhrWpacX7y7GCMo2/JM6LeHmiiNdH1FWgGCpUfircSwlWKUCAwEAAQ==

# table regex https://github.com/alibaba/canal/wiki/AdminGuide
canal.instance.filter.regex=dscloud_offline\\.offline_order.*,dscloud_offline\\.offline_refund_order.*
# table black regex
canal.instance.filter.black.regex=
# table field filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.field=test1.t_product:id/subject/keywords,test2.t_company:id/name/contact/ch
# table field black filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.black.field=test1.t_product:subject/product_image,test2.t_company:id/name/contact/ch

# mq config
canal.mq.topic=order_member_transaction_record_topic
# dynamic topic route by schema or table regex
#canal.mq.dynamicTopic=mytest1.user,mytest2\\..*,.*\\..*
#canal.mq.partition=0
# hash partition config
canal.mq.partitionsNum=3
#canal.mq.partitionHash=test.table:id^name,.*\\..*
canal.mq.partitionHash=dscloud_offline\\.offline_order.*:order_no,dscloud_offline\\.offline_refund_order.*:refund_no
#################################################

**上游服务(需要优先发版)**

  10 complete 商品服务: 通过erpCode获取商品五级分类编码等信息 /api/commodity/queryCommoditySourceInfo 接口地址: [http://hydee-middle-merchandise.svc.k8s.test.hxyxt.com/doc.html#/default/%E5%95%86%E5%93%81%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AFAPI/queryCommoditySourceInfoUsingPOST](http://hydee-middle-merchandise.svc.k8s.test.hxyxt.com/doc.html#/default/%E5%95%86%E5%93%81%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AFAPI/queryCommoditySourceInfoUsingPOST)   52 complete 原商品接口添加五级分类,开关打开    

---

### 历史订单迁移

**RocketMQ队列**

| Topic | 作用 | 详细说明 | 生产是否新建 |
| --- | --- | --- | --- |
| TP_ORDER_ATOM_MIGRATION-HANA | 发送迁移订单到该队列 |  | 15 complete |
| TP_ORDER_ATOM_MIGRATION-RE-HANDLE-HANA | 重新处理迁移异常的迁移订单 | 处理成功后会在redis计数,并清理sync_stat_error数据 sync_stat_error.result = true表明发送到重试队列成功。如果消费失败,则改回false,并记录异常原因 | 16 complete |


**迁移过程Redis Key**

| 场景 | RedisKey 主 | RedisKey 子 | 说明 | 核对 |
| --- | --- | --- | --- | --- |
| 发送迁移数据到MQ | ${taskKey}_SEND_MQ_COUNT_ | SEND_MQ_FAILED | 发送到mq失败的个数 | Mongo表: sync_stat条件: ${taskKey}sync_stat.total = SEND_MQ_FAILED+ SEND_MQ_SUCCESS |
| SEND_MQ_SUCCESS | 发送到mq成功的个数 |
| 实际迁移流程 | ${taskKey}_SEND_MQ_COUNT_ | EXISTS | 数据在数据库中已经存在 | Mongo表: sync_stat_error、sync_stat条件: ${taskKey}sync_stat.total = EXISTS+SUCCESS+ONLINE_ORDER+count(sync_stat_error) db['sync_stat_error'].aggregate([ 		{         $match: {             statTaskKey: "hanaMigration_ynhx_data01_true_20240712000000_20240712100000",         }     },     {         $group: {             _id: "$condition"         }     },     {         $count: "total"     } ]); |
| SUCCESS | 数据迁移成功 |
|  | ONLINE_ORDER | 迁移的订单是线上订单 |


**迁移过程Mongo Documnet**

| Mongo Document | 说明 |
| --- | --- |
| sync_stat | 同步主表,主要把控总数、进度等整体迁移情况 |
| sync_stat_error | 迁移错误的明细 |
| migration_delay_parent_order_mapping | 延迟处理拆单的订单,因为迁移过程中有的订单在后面, 所以所有的拆单都要Delay一下处理 |


**原数据(hana原结构)归档表**

迁移脚本筛选的时间SQL为: >= and <,不是<=

| 字段 |  |  |
| --- | --- | --- |
| migration | 0 | 未迁移/未成功迁移的 |
| 1 | 迁移成功 |
| 2 | 校验已存在,不迁移 |
| 3 | 线上订单,不迁移 |


Job:

# 会重新处理sync_stat_error表中的错误数据。理论上不用重新设置resumeStartId为0,来重跑
migrationHanaDataReHandler

接口:

/migration-hana 触发迁移,理论上只要触发一次;只有程序终止了,才要触发续传
/migration-updateAllFailed 将sync_stat_error的数据重置为false，让定时任务migrationHanaDataReHandler重跑

检测SQL:

select c.count 迁移数,
		CASE 
        WHEN migration = 0 THEN '未迁移成功(0)'
        WHEN migration = 1 THEN '迁移成功(1)'
        WHEN migration = 2 THEN '校验已存在,不迁移(2)'
        WHEN migration = 3 THEN '线上订单,不迁移(3)'
        ELSE '未知状态'
    END AS 各状态迁移进度
	from (
select count(1) count ,migration from xf_transsalestotal_ynhx_data01 where XF_CREATETIME >= '2024-07-21 00:00:00' and XF_CREATETIME < '2024-07-21 10:00:00' GROUP BY migration
) as c order by migration asc; # 需要加正单和退单

生产添加索引(从维护过来)

  18 incomplete prod  

db.sync_stat.createIndex({ taskKey: 1 });
 
db.sync_stat_error.createIndex({ result: 1,createTime:1 });
 
db.migration_delay_parent_order_mapping.createIndex({
    parentThirdOrderNo:1,
    thirdPlatformCode:1,
    storeCode:1
});

**发版顺序:**

  33 complete 同步master代码到feature分支  65 complete order-atom-service   66 complete order-service   67 complete order-framework   68 complete order-sync   69 complete hydee-business-order   70 complete hydee-business-order-web     34 complete SNAPSHOT 到 RELEASE  80 complete order-atom-sdk   81 complete order-open-sdk   82 complete order-common     35 complete 替换各个项目的SDK版本  96 complete order-atom-service   97 complete order-service   98 complete order-framework   99 complete order-sync   100 complete hydee-business-order   101 complete hydee-business-order-web     36 complete feature分支合并到master分支. PR  71 complete order-atom-service   72 complete order-service   73 complete order-framework   74 complete order-sync   75 complete hydee-business-order   76 complete hydee-business-order-web     102 complete master分支合并到pre  103 complete order-atom-service   104 complete order-service   105 complete order-framework   106 complete order-sync   107 complete hydee-business-order   108 complete hydee-business-order-web     37 complete apollo配置修改  38 complete 特别注意: es不自动创建,改后手动触发   115 complete pre   116 complete prod 配置已修改，未发布   120 complete prod 配置发布     39 complete DB字段提前一天申请   40 complete 上游服务发布: 商品   41 complete 线上发版  121 complete 工单申请     42 complete 手动创建索引  127 complete pre   128 complete prod     43 complete canal配置-prod   44 incomplete 刷数据  129 complete pre   130 incomplete prod es是2年,迁移是所有 trueGreen刷数中     111 complete 迁移rocketmq   112 incomplete 迁移mongo索引   122 incomplete 配置job   132 complete 让商品打开开关