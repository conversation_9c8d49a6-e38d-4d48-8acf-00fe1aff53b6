# 【20250313】hydee-middle-order下线 能力沉淀重写

****

## 一、背景

### 1.1 业务背景

重构下线hydee-middle-order服务 将功能能力优化 沉底到新订单中台中

### 1.2 痛点分析

1.代码逻辑混乱

2.调用复杂

3.不符合DDD

4.模型整合

### 1.3 系统现状

truemiddle-order老流程falseautotoptrue11244

## 二、目标

### 2.1 本期目标（边界）

正向下单流程调整 

逆向退款流程调整

界面查询调整

对外调用替换 替为 order-open-sdk

流程步骤说明：

1.迁移整合微商城B2C正向下单流程到 hydee-business-order 不在转发MQ到hydee-business-order-web创单

2.迁移整合微商城B2C逆向退款单流程到 hydee-business-order 不在转发MQ到hydee-business-order-web创单

3.middle-order直接调用hydee-business-order创建微商城订单去掉third-platform middle-datasync-message

4.yxt-trade-center直接调用hydee-business-order创建微商城订单去掉middle-order

#### 2.1.1 重构后系统

**Q1版本**

true2版本falseautotoptrue11044

问题：

待付款订单问题 解决：新增订单状态

**MQ通知外部 会员营销回滚–达哥可能需要去对接**

**middle-datasync-message项目基本和 middle-order强绑定需要一起下线**

最终版本

**true新订单流程falseautotoptrue11276**

#### 2.1.2 技术目标

#### 2.1.3 中长期目标

# 3.需要调整接口

**3.1接口信息统计**

| 请求方式 | 数量 |
| --- | --- |
| POST | 166 |
| GET | 59 |
| PUT | 8 |
| DELETE | 1 |


**3.2外部项目调用hydee-middle-order接口说明**

| 调用服务 | 接口地址 | 调用方式 | 说明 |
| --- | --- | --- | --- |
| hydee-business-order | /1.0/intelligent/customer/logistic | POST | ``` 客服端-物流信息 ``` |
| /1.0/intelligent/customer/order/list | POST | ``` 客服端订单列表 ``` |
| /1.0/intelligent/customer/order/listById | POST | ``` 客服端订单列表 ``` |
| /1.0/intelligent/customer/remark | POST | ``` 客服端-备注 ``` |
| /1.0/order-info/updateOrderStatusToFinish | POST | ``` 批量修改订单至完成终态已完成=12，退款完成=30 ``` |
| hydee-business-order-web | /1.0/express-subscribe/external | POST | ``` 快递100物流信息推送订阅接口 ``` |
| /1.0/order-detail/get/{id} | GET | ``` 根据订单id获取订单明细信息 ``` |
| hydee-middle-data-sync | /1.0/order-stock/occupyStock | POST | ``` 查询商品的库存占用 ``` |
| hydee-middle-market | /1.0/coupon/{orderId} | GET | ``` 查询订单优惠券信息 ``` |
| /1.0/order-info/{orderId} | GET | ``` 通过订单ID查订单信息 ``` |
| /1.0/order-query/queryOrderPush | POST | ``` 通过订单IDS查订单信息-用于推送 ``` |
| hydee-middle-member | /1.0/order-query/queryOrderPush | POST | ``` 通过订单IDS查订单信息-用于推送 ``` |
| hydee-middle-member-data | /1.0/order-info/{orderId} | POST | 通过订单ID查订单信息 |
| hydee-middle-merchandise | /1.0/order-stock/getUnpaidSharedWarehouseOccupyStock | POST | ``` 查询未付款订单共享仓商品占用库存 ``` |
| /1.0/order-stock/occupyStock | POST | ``` 查询商品的库存占用 ``` |
| hydee-middle-payment | /1.0/order-detail/get/{id} | GET | ``` 根据订单id获取订单明细信息 ``` |
| /1.0/order-info/{orderId} | GET | 通过订单ID查订单信息 |
| /1.0/order-info/unifiedPayNotify | POST | ``` 统一支付回调 ``` |
| /1.0/order-return/_unifiedRefundNotify | POST | ``` 统一支付退款回调通知 ``` |
| middle-datasync-message | /1.0/coupon/_search | POST | ``` 查询优惠信息 ``` |
| /1.0/express-company/_query | POST | ``` 查询物流公司列表 ``` |
| /1.0/order-detail/get/{id} | GET | 根据订单id获取订单明细信息 |
| /1.0/order-detail/_send | POST | ``` 订单发货 ``` |
| /1.0/order-info/{orderId} | GET | ``` 通过订单ID查订单信息 ``` |
| /1.0/order-info/confirm | POST | ``` 订单确认收货 ``` |
| /1.0/order-info/needSendHb/{orderId} | GET | ``` 是否需要送海贝 ``` |
| /1.0/order-info/storeSelf | POST | ``` 门店自提提货 ``` |
| /1.0/order-info/storeSelfPickMsg | POST | ``` 门店自提通知接口 ``` |
| /1.0/order-query/queryOrderPush | POST | ``` 通过订单IDS查订单信息-用于推送 ``` |
| /1.0/order-query/queryPrescriptionPush | POST | ``` 通过订单IDS查订单需求单信息-用于推送 ``` |
| /1.0/order-return/queryByOrderId | POST | ``` 根据订单id查询退货退款申请单 ``` |
| /1.0/order-return/_agree | POST | ``` 同意退货退款 ``` |
| /1.0/order-return/_apply | POST | ``` 退货退款申请 ``` |
| /1.0/order-return/_get/{retId} | GET | ``` 根据申请单id查询退货退款申请单 ``` |
| /1.0/order-return/_query | POST | ``` 查询退货退款申请 ``` |
| /1.0/order-return/_reject | POST | ``` 拒绝退款 ``` |
| /1.0/order-setup/{merCode} | GET | ``` 获取商家订单配置 ``` |
| /1.0/orderOmsSet/{merCode} | GET | ``` 获取商家订单配置 ``` |
| ydjia-merchant-customer | /1.0/coupon/_search | POST | ``` 查询优惠信息 ``` |
| /1.0/exchangeRuleSet/{merCode} | GET | ``` 查询商户消费送健康贝规则 ``` |
| /1.0/express-record/get | POST | ``` 根据包裹id获取物流信息列表 ``` |
| /1.0/order-activity/countMemberSpec | POST |  |
| /1.0/order-count/commoditySaleCount | GET | ``` 统计活动订单相关数据 ``` |
| /1.0/order-detail/get/{id} | GET | ``` 根据订单id获取订单明细信息 ``` |
| /1.0/order-detail/getByOrderIds | POST | ``` 根据订单id集合查询订单明细 ``` |
| /1.0/order-detail/getReturnList | POST | ``` 根据订单明细id集合查询售后订单明细 ``` |
| /1.0/order-detail/getZeroByUserId | GET | ``` 根据会员ID查询0元单明细 ``` |
| /1.0/order-detail/get_sub/{id} | GET | ``` 根据订单id获取子订单明细信息 ``` |
| /1.0/order-detail/_query | POST | ``` 查询订单明细 ``` |
| /1.0/order-info/{merCode}/{orderId} | GET | ``` 获取订单详情 ``` |
| /1.0/order-info/afterOrderCommit | POST | ``` 状态变更推送mq ``` |
| /1.0/order-info/cancel | POST | ``` 订单取消 ``` |
| /1.0/order-info/checkMemberOrder | GET | ``` 判断会员是否下过订单 ``` |
| /1.0/order-info/confirm | POST | ``` 订单确认收货 ``` |
| /1.0/order-info/countNumberByTime | POST | ``` 统计用户购买记录 ``` |
| /1.0/order-info/countState/{merCode}/{memberId} | GET | ``` 统计订单状态数量 ``` |
| /1.0/order-info/getMedicalPaymentDetails/{orderId} | GET | ``` 医保支付订单，查询医保和自费明细 ``` |
| /1.0/order-info/getPayDetail | POST | ``` 获取支付/退款明细 ``` |
| /1.0/order-info/isExistVipOrder | POST | ``` 查询是否有待支付的订单 ``` |
| /1.0/order-info/listCustomer | POST | ``` C端订单列表 ``` |
| /1.0/order-info/order/listByCode | POST | ``` 根据商品编码查询订单 ``` |
| /1.0/order-info/refundOrderList | POST | ``` 售后订单列表 ``` |
| /1.0/order-package/get/{orderId} | GET | ``` 根据订单id获取包裹列表 ``` |
| /1.0/order-package/list | POST | ``` 获取包裹列表 ``` |
| /1.0/order-query/queryOrderPush | POST | ``` 通过订单IDS查订单信息-用于推送 ``` |
| /1.0/order-return/_apply | POST | ``` 退货退款申请 ``` |
| /1.0/order-return/_check | POST | ``` 验证明细退款是否是最后一笔退款明细 ``` |
| /1.0/order-return/_get/{retId} | GET | ``` 根据申请单id查询退货退款申请单 ``` |
| /1.0/order-return/_queryReturnDetailList | POST | ``` 根据条件查询售后明细列表 ``` |
| /1.0/order-setup/{merCode} | GET | ``` 获取商家订单配置 ``` |
| /1.0/request-approval | PUT | ``` 修改状态 ``` |
| /1.0/request-approval/{orderId} | GET | ``` 根据订单id获取需求单信息 ``` |
| /1.0/request-approval/getByPresNo | GET | ``` 根据订单id获取需求单信息 ``` |
| /1.0/request-approval/getPresFromMedicalCloud | GET | ``` 从药事云获取处方信息 ``` |
| /1.0/request-approval/isMedicalCloudVersion/{merCode} | GET | ``` 商户是否升级药事云 ``` |
| /1.0/request-approval/newGetByOrderId/{orderId} | GET | ``` 根据订单id获取需求单信息 ``` |
| /1.0/staffPromotionRecord/income_statistics | POST | ``` 当前员工收益统计 ``` |
| /1.0/staffPromotionRecord/query/listCustomer | POST | ``` C端分页查询推广记录 ``` |
| /1.0/staffPromotionRecord/_getTotal | POST | ``` 获取当前员工累计推广订单信息 ``` |
| /1.0/video-consultation/config/{merCode}/{platformType} | GET | ``` 查询商户配置信息 ``` |
| /1.0/video-consultation/record/{id} | GET | ``` 查询问诊记录 ``` |
| ydjia-merchant-manager | /1.0/coupon/_search | POST | ``` 查询优惠信息 ``` |
| /1.0/express-company/{companyCode} | GET | ``` 根据物流公司编码获取物流公司 ``` |
| /1.0/express-company/_query | POST | ``` 查询物流公司列表 ``` |
| /1.0/express-record/get | POST | ``` 根据包裹id获取物流信息列表 ``` |
| /1.0/express-record/update | POST | ``` 更新物流消息 ``` |
| /1.0/order-detail/get/{id} | GET | ``` 根据订单id获取订单明细信息 ``` |
| /1.0/order-detail/getByOrderIds | POST | ``` 根据订单id集合查询订单明细 ``` |
| /1.0/order-detail/getReturnList | POST | ``` 根据订单明细id集合查询售后订单明细 ``` |
| /1.0/order-info/{merCode}/{orderId} | GET | ``` 获取订单详情 ``` |
| /1.0/order-info/countCheckPendingWithoutAfterSaleOrder/{merCode} | GET | ``` 统计待审核处方单数量,排除售后订单 ``` |
| /1.0/order-info/countStatusOrderNum | POST | ``` 统计各状态订单数量[待付款,待发货,已发货,待提货] ``` |
| /1.0/order-info/getPayDetail | POST | ``` 获取支付/退款明细 ``` |
| /1.0/order-info/getReceiverInfo/{merCode}/{orderId}/{userId} | GET | ``` 根据商家编码 订单id获取订单详情 ``` |
| /1.0/order-info/listMerchant | POST | ``` B端订单列表 ``` |
| /1.0/order-info/refundOrderList | POST | ``` 售后订单列表 ``` |
| /1.0/order-package/list | POST | ``` 获取包裹列表 ``` |
| /1.0/order-payment-report/orderList | POST | ``` 订单支付报表/订单列表 ``` |
| /1.0/order-return/_query | POST | ``` 查询退货退款申请 ``` |
| /1.0/order-return/_queryReturnDetailList | POST | ``` 根据条件查询售后明细列表 ``` |
| /1.0/order-setup/{merCode} | GET | ``` 获取商家订单配置 ``` |
| /1.0/request-approval/getPresFromMedicalCloud | GET | ``` 从药事云获取处方信息 ``` |
| /1.0/request-approval/newGetByOrderId/{orderId} | GET | ``` 根据订单id获取需求单信息 ``` |
| /1.0/staffPromotionRecord/query/listMerchant | POST | ``` B端查询推广记录 ``` |
| /1.0/staffPromotionRecord/query/statisticHeader | POST | ``` B端查询当月业绩总额 ``` |
| /1.0/staffPromotionRecord/query/statisticList | POST | ``` B端分页查询当月业绩总额 ``` |
| /1.0/task/_executeSql | POST | ``` 执行sql ``` |
| /1.0/task/_executeSqlCount | POST | ``` 执行统计sql ``` |
| ydjia-merchant-promote | /1.0/order-activity/countMemberActivity/{memberId} | POST | ``` 统计活动参与次数 ``` |
| /1.0/order-activity/countMemberSpec | POST |  |


**3.3hydee-middle-order调用接口外部项目说明**

| 调用服务 | 接口地址 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| hydee-middle-market | ``` /1.0/activityCoupon/saleCoupon ``` | POST | ``` 核销优惠券 ``` |
| ``` /1.0/activityDelivery/sendPrize ``` | POST | ``` 配送有礼 ``` |
| ydjia-merchant-promote | ``` /1.0/out/outController/updateFollowOrder ``` | POST | ``` 参团动作订单回写接口 ``` |
| ``` /1.0/out/outController/updateLeaderOrder ``` | POST | ``` 开团动作订单回写接口 ``` |
| ``` /1.0/act-rule/_delStock ``` | POST | ``` 扣减活动库存 ``` |
| ``` /1.0/act-rule/_delStockForCompensation ``` | POST | ``` 活动规则增加库存,订单特殊补偿，不在tcc控制 ``` |
| ``` /1.0/group-customer/addFinalPayAppointment ``` | POST | ``` 新增尾款支付预约 ``` |
| ``` /1.0/purchase/deductStock ``` | POST | ``` 扣减内购库存 ``` |
| ``` /1.0/act/_search ``` | POST | 活动搜索 |
| ``` /1.0/act-third/update ``` | POST | ``` 更新活动订单表状态 ``` |
| ``` /1.0/act-third/_search ``` | POST | ``` 诺和支付成功查询数据记录信息 ``` |
| ``` /1.0/act-third/_search/orderIds ``` | POST | ``` 获取订单促销信息 ``` |
| hydee-middle-baseinfo | ``` /1.0/mer/merCode/all ``` | GET | ``` 查询所有商户编码 ``` |
| ``` /1.0/store/{id} ``` | GET | ``` 查询所有商户编码 ``` |
| ``` //1.0/mer-conf/checkCooperation/{merCode}/{type} ``` | GET | ``` 查询商户配置信息 ``` |
| ``` /1.0/sp/merchantQry/getSPDetailByList ``` | POST | ``` 查询服务商信息 ``` |
| ``` /1.0/employee/queryEmpInfoByCode/{merCode}/{empCode} ``` | GET | ``` 根据商户编码和员工编码查询员工信息 ``` |
| ``` /1.0/ewxemp/batchSearchEmpInfoByEmpCodes ``` | POST | ``` 根据员工编码批量查询员工信息 ``` |
| ``` /${api.version}/mer/{merCode} ``` | POST | ``` 查询商户运营平台基本信息 ``` |
| ``` /${api.version}/mer/_queryMerchantByCode ``` | POST | ``` 查询商户运营平台基本信息 ``` |
| ``` /${api.version}/store/{id} ``` | GET | ``` 根据条件查门店信息 ``` |
| ``` /${api.version}/store/_search ``` | POST | ``` 根据条件查门店信息 ``` |
| hydee-middle-merchandise | ``` /1.0/srm-comm-spec/updateStockBatch ``` | POST | ``` 商品增减库存 ``` |
| ``` /1.0/comm-spec/refreshOrderSp ``` | POST | ``` 刷新订单云仓商品Id ``` |
| ``` /1.0/store-spec/stock ``` | POST | ``` 商品增减库存 ``` |
| ``` /1.0/comm-spec/_search ``` | POST | ``` 查询商品规格 ``` |
| ``` /1.0/ds/stock ``` | POST | ``` 线上门店商品 - 扣减库存/还原库存 ``` |
| ``` /virtual-sale-config/queryByMerCode/{merCode} ``` | GET | ``` 获取商品虚拟销量配置 ``` |
| h3-orgmanager-v2 | ``` /private/customer/query_third_config_list ``` | GET | ``` 获取供应商（服务商）类型 ``` |
| yxt-medical-prescription | ``` /private/medical/pres/batchQueryDetail ``` | POST | ``` 获取处方申请单详情 ``` |
| ``` /private/medical/pres/queryDetail ``` | POST | ``` 处方申请详情查询（单个） `````` 先查开方申请表，如果是完成开方则再查询审方申请表（微商城） ``` |
| ``` /private/medical/pres/submitPres ``` | POST | ``` 处方（开方）申请提交（微商城B端场景重新开方）。 ``` |
| ``` /private/medical/store/staff/hasPharmacistOnline ``` | POST | ``` 查询门店是否配置在线审方药师 ``` |
| ``` /private/medical/pres/preConfirm ``` | POST | ``` 确认提交处方 ``` |
| ``` /private/medical/appConfig/getMerConfigChannel ``` | POST | ``` 查询商户开方渠道 ``` |
| ``` /private/medical/pres/getPresResult ``` | POST | ``` 获取处方状态 ``` |
| hydee-middle-member | ``` /1.0/memberManage/detail ``` | GET | ``` 会员详情 ``` |
| ``` /1.0/member/detailByMemberCard ``` | GET | ``` 会员详情 ``` |
| ``` /1.0/member/addMemberOnlineIntegral ``` | POST | ``` 增加会员健康贝 ``` |
| ``` /1.0/memberManage/adjustIntegral ``` | POST | ``` 扣减健康贝 ``` |
| ``` /1.0/member/payment ``` | POST | ``` 余额支付 ``` |
| ``` /1.0/member/refund ``` | POST | ``` 余额退还回 ``` |
| ``` /1.0/member/queryMemberListByMemberCards ``` | POST | ``` 批量查询会员信息 ``` |
| ``` /1.0/member/getMemberBaseInfo ``` | GET | ``` 查询会员基本信息 ``` |
| ``` /1.0/member/queryMemberByMemberCard ``` | POST | 查询会员基本信息 |
| ``` /1.0/member/queryMemberByMemberPhone ``` | POST | 查询会员基本信息 |
| hydee-middle-goods | ``` /1.0//share-commodity-stock/updateStock ``` | POST | ``` 共享库存扣减和返还 ``` |
| hydee-middle-payment | ``` /1.0/payAfter/queryMessages ``` | POST | ``` 通过订单IDS查订单支付信息-用于推送 ``` |
| ``` /1.0/payAfter/getLastPaymentLog ``` | POST | ``` 获取分账订单详情 ``` |
| ``` /1.0/payAfter/getPaymentLogs ``` | POST | 获取支付流水 |
| ``` /1.0/payAfter/qryPaymentDetails ``` | POST | ``` 获取支付明细 ``` |
| ``` /1.0/unifiedPay/refund ``` | POST | ``` 调用统一退款接口 ``` |
| ``` /1.0/unifiedPay/payCancel ``` | POST | ``` 已付定金，关闭尾款支付 ``` |
| hydee-middle-sdp | ``` /sdp/orderinfo/add ``` | POST | ``` 增加分销订单 ``` |
| ``` /sdp/orderinfo/close ``` | POST | ``` 分销订单需要关闭分销中台订单 ``` |
| ``` /sdp/orderinfo/finish ``` | POST | ``` 分销订单分账完成 ``` |
| h3-orgmanager-center | ``` /public/top/customer/find_commission_config_by_merchant_code ``` | GET | ``` 查询商户是否在分账期 ``` |
| ydjia-srm-bills | ``` /1.0/bills/{thirdOrderNo}/billFlag ``` | GET | ``` 订单是否已经分账 ``` |
| ``` /1.0/merchant/config/{merCode}/getSaleFlag ``` | GET | ``` 查询云仓商品分销活动开关状态 ``` |
| video-consultation-micro | ``` /api/verify/token ``` | GET | ``` 视频问诊-微问诊相关外部调用接口--获取token ``` |
| ``` /api/pres/getWxPresInfo ``` | GET | ``` 视频问诊-微问诊相关外部调用接口--根据问诊单号查询微问诊处方信息 ``` |
| hydee-middle-third | ``` /1.0/api/messagePush ``` |  | ``` 订单提交消息推送 ``` |
| ``` /1.0/wxApp/getWxAppInfoListWithDefault ``` | GET | ``` 获取小程序appid ``` |
| ``` /1.0/ma/getSelfMinAppid ``` | GET | ``` 获取商户独立小程序appid ``` |
| ``` /1.0/novo/thirdOrderCallback ``` | POST | ``` 诺和订单微信支付成功通知 ``` |


# 4.内部

**XXlJOB**

| JobHandler | 任务描述 | 定时任务重试频率 | 备注 |
| --- | --- | --- | --- |
| ``` CloudOrderHandleDeliveryHandler ``` | 云仓物流回调 | 5分钟一次 |  |
| ``` DeliveryOrderCancleJobHandler ``` | 货到付款门店自提订单定时取消 | 1分钟一次 |  |
| ``` GroupOrderCancleJobHandler ``` | 拼团超时支付取消跑批 | 1分钟一次 |  |
| ``` GroupReturnRefundJobHandler ``` | 拼团失败自动退款 | 1分钟一次 |  |
| ``` MedicalCloudSyncStatusHandler ``` | 药事云处方状态同步 | 1分钟一次 |  |
| ``` OrderCancleJobHandler ``` | 自动取消订单跑批 | 5分钟一次 |  |
| ``` OrderConfirmJobHandler ``` | 自动确认收货跑批 | 3分钟一次 |  |
| ``` OrderDistributorSyncRedisJobHandler ``` | 分销员信息redis同步任务 | 5分钟一次 |  |
| ``` OrderPushPaymentNoticeJobHandler ``` | 订单未付款取消提醒 | 1分钟一次 |  |
| ``` PreSaleOrderCancelHandler ``` | 预售订单取消定时任务 | 30秒一次 |  |
| ``` PrescriptionRefundHandler ``` | 未审批通过处方单自动退款 | 1分钟一次 |  |
| ``` VideoConsultationSyncJobHandler ``` | 视频问诊同步平台信息 | 1分钟一次 |  |


**MQ**

| topic | ``` Tag ``` | 描述 | 发送方 | 接受方 |
| --- | --- | --- | --- | --- |
| TOPIC_MIDDLE_ORDER_DATA_PRO | SRM_CONFIRM_ORDER | 第三方云仓订单确认消息监听 | ``` hydee-middle-order ``` | ``` hydee-middle-order ``` |
| TOPIC_SRM_ADAPTER_DATA_PRO | SRM_CANCEL_ORDER | 第三方云仓订单取消消息监听 | ``` ydjia-srm-adapter ``` | ``` hydee-middle-order ``` |
| TOPIC_SRM_ADAPTER_DATA_PRO | SRM_DELIVERY_ADAPTER_DELIVERY_STATUS | 第三方云仓订单物流消息监听 | ``` ydjia-srm-adapter ``` | ``` hydee-middle-order ``` |
|  |  |  |  |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | ORDER_STATUS | 订单状态变更通知 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | GROUP_ORDER_MESSGE | 拼团 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | SRM_CANCEL_ORDER | 如果是云仓订单，且非供应商平台主动发起取消，则需要通知第三方云仓平台取消订单 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | COUPON_MESSGE | 优惠券相关 退券 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | ORDER_DETAIL_REFUND | 推送退款成功mq消息 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | WX_MESSGE | 订单推送下游发送手机短信 | hydee-middle-order |  |
|  |  |  |  |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | ORDER_STOCK_RETURN | 推送mq 库存回退，供商品中台消费 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | PROMOTION_RETURN_STOCK | 推送mq 供活动中台消费 返还商品活动库存 | hydee-middle-order |  |
| TOPIC_MIDDLE_ORDER_DATA_PRO | IN_PURCHASE_RETURN_STOCK | 推送mq 供活动中台消费 返还商品活动库存 | hydee-middle-order |  |
| TP_MIDDLE_ORDER_CREATE_VIP_MEMBER | TAG_CREATE_VIP_TO_MEMBER | 付费会员卡订单推送到会员中台 | hydee-middle-order |  |


## 五、 详细设计（战术设计）

### 1、 模块详细设计

一心到家新增正向订单新流程

true微商城订单新下单流程falseautotoptrue15863

跳过middle-order问题：

1.虚拟订单标识

2.疫情管控信息

3.分享人

4.佣金问题

5.分销金额

6.是否是员工下单标识

7.预约单云货架明细

8活动商品扣减库存问题 ydjia-merchant-promote

9.拼团订单推送拼团中台 拼团订单

微商城O2O\B2C流程整合需要新增流程：

1.保存platform_order等信息

2.保存 oms_order_info信息

3.wms发货通知WMS

一心到家新增退款单订单新流程

true新微商订单退款falseautotoptrue8012

### 2、 存储数据库设计

**middle新增订单流程写入表**

| middle-order | 说明 | 对应business-order | 说明 | 操作 |
| --- | --- | --- | --- | --- |
| epidemic_registration | 疫情登记表 |  |  | 作废 |
| prescription_approval | 需求单审批表 | order_prescription | 订单处方信息表 |  |
| order_info_extra | 订单扩展信息表 |  |  |  |
| order_info | 订单信息表 | order_info | 订单信息表 |  |
| order_log | 订单日志表 | order_log | 订单日志表 |  |
| order_detail | 订单明细表 | order_detail | 订单明细表 |  |
| order_detail_extra_supplier | 云货架订单明细扩展表 |  |  |  |
| order_coupon | 订单对应优惠券 | order_coupon_info | 订单-优惠券信息 |  |
| order_market_activity | 订单对应活动表 |  |  |  |
| order_delivery_address | 订单收货地址表 | order_delivery_address | 订单收货地址表 |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |


问题：

1.云仓订单

2.分销

流程说明：

1.重写微商城新增订单流程-O2O hydee-business-order服务
2.微商城新增订单流程O2O/B2C融合 hydee-business-order服务
3.剔除微商接口中台转发
 3.1剔除middle-datasync-message

 3.2剔除 yxt-xframe

4. 整合微商城新增订单 给2整合一起

5.剔除middle-order