# 25年第7周2025-02-14

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | middle-order提交订单回滚问题(处方单-订单无法取消) |  |  | 待处理 |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 | B2B加盟商城 | **交易中台**:  技术方案评审：已完成。  一心助手对接：  代码开发进度：完成40%代码开发。   **支付中台**:  技术方案评审：已完成。  一心助手对齐：已完成与一心助手对于D-ERP支付流程的对齐工作。  代码开发进度：5%。   **订单中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，对接ing。  ERP/POS对接: 接口已经和开发核对了一次。现在进入开发阶段  order-service开发进度：50%。 order-atom开发进度：10% |  |  |
| 2 | 客服中台搭建 |  |  |  |
| 3 | 订单监控-(一致性保障) | 订单一致性保障方案 60% |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** 1.【库存释放失败】优化开发\上线2.订单一致性保障方案 60%  a.美团自动补单 80%  b.饿了么自动补单 80%  c.京东到家自动补单 80%  d.差异性报表 0% 3.线上问题（京东负金额，下账重推） | **遗留问题** **风险问题** | **需求研发**.订单一致性保障方案 开发完成，自测，提测 **技术建设** |  |
| 2 |  | **本周总工时：5d**1. 刷历史180天订单商品五级分类数据,已上线 2. 会员消费记录支持一心助手需求,已上线 3. 会员慢病查询接口支持五级分类需求,已上线。 线上ES刷数处理中 4. 经营分析报表topic下线,待上线 5. 业务通用能力建设: 数据一致性校验重构开发,已上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：5d**1**.**B2B订单支持，erp 接口对接。50%2.支付宝商城测试完毕 待上线。3.线上运维 | **遗留问题****风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. B2B订单中台支持（上层业务逻辑）：   1. 订单查询相关接口 80%   2. 订单创建 70%   3. 订单状态变更接口 70%   4. 售后单查询相关接口 80%   5. 售后单创建 40% 2. 订单查询相关接口 80% 3. 订单创建 70% 4. 订单状态变更接口 70% 5. 售后单查询相关接口 80% 6. 售后单创建 40% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 客服中台搭建：   1. websokcet交互 95% 待联调   2. 对接美团IM 100%   3. 会话消息查询相关接口 20%   4. 测试用例评审 2. websokcet交互 95% 待联调 3. 对接美团IM 100% 4. 会话消息查询相关接口 20% 5. 测试用例评审 6. 请货单问题排查 7. 年前一件代发和员工推广问题优化上线 | **遗留问题****风险问题** | **需求研发**1. 完成对接智能客服部分 2. 与前端联调 **技术建设** |  |
| 6 |  | **本周总工时：4d**1.B2B购物车功能调整2.线上下账单金额异常BUG处理以及数据修复3.B2C订单导出优化4.O2O下账错误订单的修复 | **遗留问题**1.虚拟订单B2C下账2.redis迁移**风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d** 1. B2B交易中台支持： a. 结算流程80% b.提单流程70% c.支付超时取消 90% d.退款流程 50%2. 线上问题 （DERP卡顿导致心云订单状态未扭转问题处理、中转平台v单金额问题处理） | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：2d**1. B2C订单ES重构kafka老topic消息堆积问题处理 2. web服务B2C订单同步ES老topic下线 3. O2O京东到家订单配送费为负数问题单处理   1. 科传：43单（已处理）   2. 海典：60单（处理中） 4. 科传：43单（已处理） 5. 海典：60单（处理中） 6. 订单新模型原子服务搭建 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. B2B支付中台支持   1. 支付域 90%   2. 支付账单 70%   3. 退款域 70%   4. 退款账单 50%   5. 支付异常处理 10%   6. 分库表 0% 2. 支付域 90% 3. 支付账单 70% 4. 退款域 70% 5. 退款账单 50% 6. 支付异常处理 10% 7. 分库表 0% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) (订单组redis cluster) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等
1.全员体验KIMI浏览器插件.融入日常开发过程内.
kimi 支持爬取网页review代码
豆包 不支持网页爬取 
结论优先考虑使用kimi 浏览器插件

辅助编码 可以考虑[https://help.aliyun.com/zh/lingma/user-guide/ai-developer-guidlines](https://help.aliyun.com/zh/lingma/user-guide/ai-developer-guidlines) AI程序员 







# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 | business-gateway timeout [url:/dscloud/1.0/ds/order/page/exception](http://url/dscloud/1.0/ds/order/page/exception) | 134 | 李洋 |
| 2 | business-gateway timeout [url:/b2c/1.0/billList/page/orderLedgerList](http://url/b2c/1.0/billList/page/orderLedgerList) | 109 | 徐国华 |
| 3 | business-gateway timeout [url:/b2c/1.0/order/state/count/500001/-99](http://url/b2c/1.0/order/state/count/500001/-99) | 61 | 杨俊峰 |
| 4 | business-gateway timeout [url:/b2c/1.0/billList/page/orderLedgerCount](http://url/b2c/1.0/billList/page/orderLedgerCount) | 40 | 徐国华 |
| 5 | business-gateway timeout [url:/b2c/1.0/batchShip/singleShip/batchExpressNos](http://url/b2c/1.0/batchShip/singleShip/batchExpressNos) | 38 | 杨花 |
| 6 | business-gateway timeout [url:/b2c/1.0/order/page/normal](http://url/b2c/1.0/order/page/normal) | 20 | 李洋 |
| 7 | business-gateway timeout [url:/b2c/1.0/order/unDeliveryOrUnTake/batchCancelOrder](http://url/b2c/1.0/order/unDeliveryOrUnTake/batchCancelOrder) | 15 | 王世达 |
| 8 | business-gateway timeout [url:/b2c/1.0/order/list](http://url/b2c/1.0/order/list) | 12 | 李洋 |
| 9 | business-gateway timeout [url:/dscloud/2.0/ds/order/pick/confirm](http://url/dscloud/2.0/ds/order/pick/confirm) | 12 | 杨俊峰 |
| 10 | business-gateway timeout [url:/dscloud/1.0/ds/refund/RefundLedgerList](http://url/dscloud/1.0/ds/refund/RefundLedgerList) | 11 | 徐国华 |
| 11 | select id from cloud_print_content where create_time < '2025-02-08 00:00:00.0' order by id desc limit 1; |  | 徐国华 |
| 12 | select id from `cloud_sound_content` where `create_time` < '2025-02-03 00:00:00.0' order by id desc limit 1; |  | 徐国华 |
| 13 | middle-order提交订单回滚问题(处方单-订单无法取消) |  |  |