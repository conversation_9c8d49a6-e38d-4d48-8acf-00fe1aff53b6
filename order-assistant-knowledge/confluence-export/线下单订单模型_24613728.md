# 线下单订单模型

### 线下单ER图

true正向订单ER图falseautotoptrue10018

true退单ER图falseautotoptrue6312

### 科传、海典字段映射

科传对接: [https://doc.weixin.qq.com/sheet/e3_AfYAogayAA8QFWUoZftQq6oroA6cE?scode=AOsAFQcYAAcQ0csF0cAZ0AgQYfAIE&tab=bbde6r](https://doc.weixin.qq.com/sheet/e3_AfYAogayAA8QFWUoZftQq6oroA6cE?scode=AOsAFQcYAAcQ0csF0cAZ0AgQYfAIE&tab=bbde6r)

海典对接:

| 字段名 | 字段解释 |
| --- | --- |
| baseOrderInfo订单基础信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | orderNo | ``` OfflineOrderNo ``` | 内部订单号 | 一心堂内部 | | parentOrderNo | OfflineOrderNo | 内部父订单号 | 一心堂内部 20240805主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentOrderNo = orderNo一致则表明是主单,否则就是子单 | | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 一心堂内部 haidian | | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | saleno销售单号 | | parentThirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号（主） | srcsaleno 主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdOrderNo = thirdOrderNo一致则表明是主单,否则就是子单 | | dayNum |  | 每日号 | 无 | | orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 | | offlineSource | OfflineSource |  | 一心堂内部 haidian | | created |  | 创单时间 | finaltime结束时间 | | payTime |  | 支付时间 | finaltime结束时间 | | billTime |  | 下账时间 | finaltime结束时间 | | completeTime |  | 完成时间 | finaltime结束时间 | | dataVersion |  | 数据版本号 | 一心堂内部 | | actualPayAmount |  | 实付金额 |  | | actualCollectAmount |  | 实收金额 |  | | orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | (科传给到) || 海典是codes | | serialNo | String | 优惠券核销流水号 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | orderTagList | List<String> | 订单标签 | PRE_ORDER-预售订单 需求: | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | orderNo | ``` OfflineOrderNo ``` | 内部订单号 | 一心堂内部 | parentOrderNo | OfflineOrderNo | 内部父订单号 | 一心堂内部 20240805主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentOrderNo = orderNo一致则表明是主单,否则就是子单 | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 一心堂内部 haidian | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | saleno销售单号 | parentThirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号（主） | srcsaleno 主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdOrderNo = thirdOrderNo一致则表明是主单,否则就是子单 | dayNum |  | 每日号 | 无 | orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 | offlineSource | OfflineSource |  | 一心堂内部 haidian | created |  | 创单时间 | finaltime结束时间 | payTime |  | 支付时间 | finaltime结束时间 | billTime |  | 下账时间 | finaltime结束时间 | completeTime |  | 完成时间 | finaltime结束时间 | dataVersion |  | 数据版本号 | 一心堂内部 | actualPayAmount |  | 实付金额 |  | actualCollectAmount |  | 实收金额 |  | orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | (科传给到) || 海典是codes | serialNo | String | 优惠券核销流水号 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | orderTagList | List<String> | 订单标签 | PRE_ORDER-预售订单 需求: |
| 字段名 | 类型 | 字段解释 | 海典对应的字段 |
| orderNo | ``` OfflineOrderNo ``` | 内部订单号 | 一心堂内部 |
| parentOrderNo | OfflineOrderNo | 内部父订单号 | 一心堂内部 20240805主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentOrderNo = orderNo一致则表明是主单,否则就是子单 |
| thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 | 一心堂内部 haidian |
| thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | saleno销售单号 |
| parentThirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号（主） | srcsaleno 主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdOrderNo = thirdOrderNo一致则表明是主单,否则就是子单 |
| dayNum |  | 每日号 | 无 |
| orderState | OfflineOrderState | 订单状态 DONE-已完成 | 已完成 |
| offlineSource | OfflineSource |  | 一心堂内部 haidian |
| created |  | 创单时间 | finaltime结束时间 |
| payTime |  | 支付时间 | finaltime结束时间 |
| billTime |  | 下账时间 | finaltime结束时间 |
| completeTime |  | 完成时间 | finaltime结束时间 |
| dataVersion |  | 数据版本号 | 一心堂内部 |
| actualPayAmount |  | 实付金额 |  |
| actualCollectAmount |  | 实收金额 |  |
| orderCouponList | List<OrderCoupon> | OrderCoupon.couponCode 优惠劵编码 | (科传给到) || 海典是codes |
| serialNo | String | 优惠券核销流水号 |  |
| isOnPromotion | Boolean | 是否参加促销 | 20240813添加 |
| orderTagList | List<String> | 订单标签 | PRE_ORDER-预售订单 需求: |
| baseOrganizationInfo 订单归属组织信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | storeCode |  | 门店编码 | busno 关联s_busi取值mdm_busno对应字段: mdmBusno | | storeName |  | 门店名称 | s_busi 内有全称/简称对应字段: orgname | | companyCode |  | 分公司Code | compid | | companyName |  | 分公司名称 |  | | storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营 JOIN - 加盟 | s_busi 内有类型 默认是直营 DIRECT_SALES ,无对应字段 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | storeCode |  | 门店编码 | busno 关联s_busi取值mdm_busno对应字段: mdmBusno | storeName |  | 门店名称 | s_busi 内有全称/简称对应字段: orgname | companyCode |  | 分公司Code | compid | companyName |  | 分公司名称 |  | storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营 JOIN - 加盟 | s_busi 内有类型 默认是直营 DIRECT_SALES ,无对应字段 |
| 字段名 | 类型 | 字段解释 | 海典对应的字段 |
| storeCode |  | 门店编码 | busno 关联s_busi取值mdm_busno对应字段: mdmBusno |
| storeName |  | 门店名称 | s_busi 内有全称/简称对应字段: orgname |
| companyCode |  | 分公司Code | compid |
| companyName |  | 分公司名称 |  |
| storeDirectJoinType | StoreDirectJoinType | 门店直营加盟类型DIRECT_SALES-直营 JOIN - 加盟 | s_busi 内有类型 默认是直营 DIRECT_SALES ,无对应字段 |
| baseCashierDeskInfo 订单归属收银台信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | posno | | cashier |  | 收银员编码 | payee | | cashierName |  | 收银员姓名 | payeename 20241217提供字段,待对接 | | picker |  | 拣货员编码 | 无 | | pickerName |  | 拣货员姓名 |  | | shiftId | ShiftId | 班次 | shiftid | | shiftDate |  | 班次日期 | shiftdate | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | posno | cashier |  | 收银员编码 | payee | cashierName |  | 收银员姓名 | payeename 20241217提供字段,待对接 | picker |  | 拣货员编码 | 无 | pickerName |  | 拣货员姓名 |  | shiftId | ShiftId | 班次 | shiftid | shiftDate |  | 班次日期 | shiftdate |
| 字段名 | 类型 | 字段解释 | 海典对应的字段 |
| posCashierDeskNo | PosCashierDeskNo | pos收银台编码 | posno |
| cashier |  | 收银员编码 | payee |
| cashierName |  | 收银员姓名 | payeename 20241217提供字段,待对接 |
| picker |  | 拣货员编码 | 无 |
| pickerName |  | 拣货员姓名 |  |
| shiftId | ShiftId | 班次 | shiftid |
| shiftDate |  | 班次日期 | shiftdate |
| baseUserInfo 订单归属会员信息 | | 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 | | --- | --- | --- | --- | | userId | OfflineUserId | 会员id |  | | userName |  | 会员名称 | member_name | | userCardNo | OfflineUserCardNo | 会员卡号 | membercardno | | userMobile | OfflineUserMobile | 会员手机号 | member_tel |  支持无卡购物,没有则不传 | 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 | userId | OfflineUserId | 会员id |  | userName |  | 会员名称 | member_name | userCardNo | OfflineUserCardNo | 会员卡号 | membercardno | userMobile | OfflineUserMobile | 会员手机号 | member_tel |
| 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 |
| userId | OfflineUserId | 会员id |  |
| userName |  | 会员名称 | member_name |
| userCardNo | OfflineUserCardNo | 会员卡号 | membercardno |
| userMobile | OfflineUserMobile | 会员手机号 | member_tel |
| basePrescriptionInfo 订单归属处方信息 | | 字段名 | 类型 | 字段解释 | 无处方单信息??????  海典对应的字段 | | --- | --- | --- | --- | | prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 |  | | prescriptionNo | OfflinePrescriptionNo | 处方单号 |  | | 字段名 | 类型 | 字段解释 | 无处方单信息??????  海典对应的字段 | prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 |  | prescriptionNo | OfflinePrescriptionNo | 处方单号 |  |
| 字段名 | 类型 | 字段解释 | 无处方单信息??????  海典对应的字段 |
| prescriptionType | OfflineRescriptionType | 订单处方单类型 WESTERN-西药 EAST中药 |  |
| prescriptionNo | OfflinePrescriptionNo | 处方单号 |  |
| payInfoList 订单多支付信息 | | 字段名 | 类型 | 字段解释 | ### t_sale_pay 零售收款表 海典对应的字段### | | --- | --- | --- | --- | | payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币 MEDICAL_HEALTH_INSURANCE - 医保 CASH - 现金 WECHAT_PAY - 微信支付 ZHI_FU_BAO_PAY - 支付宝 BANK_CARD - 银行卡 TOP_UP_CARDS - 充值卡 | paytype 找DERP确定类型 | | payAmount |  | 支付金额 | netsum 实价金额 | | payName |  | 支付名称 | paytypen | | 字段名 | 类型 | 字段解释 | ### t_sale_pay 零售收款表 海典对应的字段### | payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币 MEDICAL_HEALTH_INSURANCE - 医保 CASH - 现金 WECHAT_PAY - 微信支付 ZHI_FU_BAO_PAY - 支付宝 BANK_CARD - 银行卡 TOP_UP_CARDS - 充值卡 | paytype 找DERP确定类型 | payAmount |  | 支付金额 | netsum 实价金额 | payName |  | 支付名称 | paytypen |
| 字段名 | 类型 | 字段解释 | ### t_sale_pay 零售收款表 海典对应的字段### |
| payType | OfflineOrderPayType | 支付类型：心币/医保/现金（微信、支付宝、银行卡、充值卡）XIN_COIN - 心币 MEDICAL_HEALTH_INSURANCE - 医保 CASH - 现金 WECHAT_PAY - 微信支付 ZHI_FU_BAO_PAY - 支付宝 BANK_CARD - 银行卡 TOP_UP_CARDS - 充值卡 | paytype 找DERP确定类型 |
| payAmount |  | 支付金额 | netsum 实价金额 |
| payName |  | 支付名称 | paytypen |
| orderDetailList 订单明细 | | 字段名 | 字段解释 | | --- | --- | | baseOrderDetailInfo 明细基础信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | | rowNo |  | 商品行号 | rowno | | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | | erpCode | OfflineErpCode | 商品编码 | wareid | | erpName |  | 商品名称 | 内部关联获取 | | commodityCount | BigDecimal | 商品数量 | wareqty | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | | originalPrice |  | 商品原单价 | stdprice | | price |  | 商品售价(实际) | netprice | | totalAmount |  | 商品总额 | 自己算 price*数量 | | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | | discountAmount |  | 折扣金额 | 总的discount | | billPrice |  | 下账单价(实际) | netprice | | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | | ``` commodityCostPrice ``` |  | 商品成本价 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | rowNo |  | 商品行号 | rowno | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | erpCode | OfflineErpCode | 商品编码 | wareid | erpName |  | 商品名称 | 内部关联获取 | commodityCount | BigDecimal | 商品数量 | wareqty | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | originalPrice |  | 商品原单价 | stdprice | price |  | 商品售价(实际) | netprice | totalAmount |  | 商品总额 | 自己算 price*数量 | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | discountAmount |  | 折扣金额 | 总的discount | billPrice |  | 下账单价(实际) | netprice | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | ``` commodityCostPrice ``` |  | 商品成本价 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | | rowNo |  | 商品行号 | rowno | | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | | erpCode | OfflineErpCode | 商品编码 | wareid | | erpName |  | 商品名称 | 内部关联获取 | | commodityCount | BigDecimal | 商品数量 | wareqty | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | | originalPrice |  | 商品原单价 | stdprice | | price |  | 商品售价(实际) | netprice | | totalAmount |  | 商品总额 | 自己算 price*数量 | | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | | discountAmount |  | 折扣金额 | 总的discount | | billPrice |  | 下账单价(实际) | netprice | | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | | ``` commodityCostPrice ``` |  | 商品成本价 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | | pickInfoList 明细拣货信息 | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | | --- | --- | --- | --- | | rowNo |  | 商品行号 | rowno | | erpCode | OfflineErpCode | 商品编码 | wareid | | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | | count | BigDecimal | 数量 | wareqty | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | rowNo |  | 商品行号 | rowno | erpCode | OfflineErpCode | 商品编码 | wareid | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | count | BigDecimal | 数量 | wareqty | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | | rowNo |  | 商品行号 | rowno | | erpCode | OfflineErpCode | 商品编码 | wareid | | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | | count | BigDecimal | 数量 | wareqty | | 字段名 | 字段解释 | baseOrderDetailInfo 明细基础信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | | rowNo |  | 商品行号 | rowno | | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | | erpCode | OfflineErpCode | 商品编码 | wareid | | erpName |  | 商品名称 | 内部关联获取 | | commodityCount | BigDecimal | 商品数量 | wareqty | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | | originalPrice |  | 商品原单价 | stdprice | | price |  | 商品售价(实际) | netprice | | totalAmount |  | 商品总额 | 自己算 price*数量 | | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | | discountAmount |  | 折扣金额 | 总的discount | | billPrice |  | 下账单价(实际) | netprice | | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | | ``` commodityCostPrice ``` |  | 商品成本价 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | rowNo |  | 商品行号 | rowno | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | erpCode | OfflineErpCode | 商品编码 | wareid | erpName |  | 商品名称 | 内部关联获取 | commodityCount | BigDecimal | 商品数量 | wareqty | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | originalPrice |  | 商品原单价 | stdprice | price |  | 商品售价(实际) | netprice | totalAmount |  | 商品总额 | 自己算 price*数量 | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | discountAmount |  | 折扣金额 | 总的discount | billPrice |  | 下账单价(实际) | netprice | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | ``` commodityCostPrice ``` |  | 商品成本价 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | pickInfoList 明细拣货信息 | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | | --- | --- | --- | --- | | rowNo |  | 商品行号 | rowno | | erpCode | OfflineErpCode | 商品编码 | wareid | | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | | count | BigDecimal | 数量 | wareqty | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | rowNo |  | 商品行号 | rowno | erpCode | OfflineErpCode | 商品编码 | wareid | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | count | BigDecimal | 数量 | wareqty |
| 字段名 | 字段解释 |
| baseOrderDetailInfo 明细基础信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | | rowNo |  | 商品行号 | rowno | | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | | erpCode | OfflineErpCode | 商品编码 | wareid | | erpName |  | 商品名称 | 内部关联获取 | | commodityCount | BigDecimal | 商品数量 | wareqty | | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | | originalPrice |  | 商品原单价 | stdprice | | price |  | 商品售价(实际) | netprice | | totalAmount |  | 商品总额 | 自己算 price*数量 | | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | | discountAmount |  | 折扣金额 | 总的discount | | billPrice |  | 下账单价(实际) | netprice | | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | | ``` commodityCostPrice ``` |  | 商品成本价 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 | rowNo |  | 商品行号 | rowno | platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid | erpCode | OfflineErpCode | 商品编码 | wareid | erpName |  | 商品名称 | 内部关联获取 | commodityCount | BigDecimal | 商品数量 | wareqty | status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 | originalPrice |  | 商品原单价 | stdprice | price |  | 商品售价(实际) | netprice | totalAmount |  | 商品总额 | 自己算 price*数量 | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 | discountAmount |  | 折扣金额 | 总的discount | billPrice |  | 下账单价(实际) | netprice | billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 | ``` commodityCostPrice ``` |  | 商品成本价 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | salerId | String | 售货员编码 | saler 20241217提供字段,待对接 | salerName | String | 售货员姓名 | salername20241217提供字段,待对接 |
| 字段名 | 类型 | 字段解释 | 海典对应的字段 |
| orderNo | OfflineOrderNo | 内部订单号 | 一心堂内部 |
| rowNo |  | 商品行号 | rowno |
| platformSkuId | PlatformSkuId | 商品三方平台编码 | wareid |
| erpCode | OfflineErpCode | 商品编码 | wareid |
| erpName |  | 商品名称 | 内部关联获取 |
| commodityCount | BigDecimal | 商品数量 | wareqty |
| status | OfflineDetailStatus | 明细状态 NORMAL-正常 | 无 |
| giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 | 无 |
| originalPrice |  | 商品原单价 | stdprice |
| price |  | 商品售价(实际) | netprice |
| totalAmount |  | 商品总额 | 自己算 price*数量 |
| totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 |
| discountShare |  | 优惠分摊 | 商品数量*(shareprice分摊前价格-netprice)商品数量*(stdprice-netprice)20240514调整，问了宇鑫老师，shareprice都是null,让使用stdpricediscountShare 20240614调整为海典计算 |
| discountAmount |  | 折扣金额 | 总的discount |
| billPrice |  | 下账单价(实际) | netprice |
| billAmount |  | 下账金额 | 自己算billAmount 20240614调整为海典计算 |
| ``` commodityCostPrice ``` |  | 商品成本价 |  |
| isOnPromotion | Boolean | 是否参加促销 | 20240813添加 |
| salerId | String | 售货员编码 | saler 20241217提供字段,待对接 |
| salerName | String | 售货员姓名 | salername20241217提供字段,待对接 |
| pickInfoList 明细拣货信息 | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | | --- | --- | --- | --- | | rowNo |  | 商品行号 | rowno | | erpCode | OfflineErpCode | 商品编码 | wareid | | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | | count | BigDecimal | 数量 | wareqty | | 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) | rowNo |  | 商品行号 | rowno | erpCode | OfflineErpCode | 商品编码 | wareid | makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno | count | BigDecimal | 数量 | wareqty |
| 字段名 | 类型 | 字段解释 | 海典1品多批号会拆成 多行(品+批号) |
| rowNo |  | 商品行号 | rowno |
| erpCode | OfflineErpCode | 商品编码 | wareid |
| makeNo | OfflineMarkNo | 商品批号 | 通过batid t_store_i 关联取makeno |
| count | BigDecimal | 数量 | wareqty |
| ``` medInsSettle 医保信息 ``` | | 字段名 | 字段类型 | 字段注释 | | --- | --- | --- | | orderNo | String | 订单编号 | | serialNo | String | 流水号 | | thirdRefundNo | String | 销售单号 | | invoiceNo | String | 发票号码(结算ID setl_id) | | hospitalName | String | 定点医疗机构名称 | | name | String | 人员姓名(psn_name) | | personType | String | 人员类别(psn_type) | | personTypeName | String | 人员类别名称 | | personNo | String | 个人编号(人员编号 psn_no) | | prescriptionNo | String | 处方号(就诊ID mdtrt_id) | | icCardNo | String | IC卡号 | | acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) | | fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) | | cashPay | BigDecimal | 自付现金 | | medType | String | 医疗类别 | | medTypeName | String | 医疗类别名称 | | transactionType | String | 交易类型(1-付款 2-退款) | | transactionCode | String | 交易编码 | | hospitalCode | String | 定点医疗机构编码 | | setlTime | Long | 结算时间 | | clearingType | String | 清算类别 | | clearingTypeName | String | 清算类别名称 | | isRefunded | String | 是否已退款(0-未退款 1-已退款) | | refundTime | Long | 退款时间 | | origSerialNo | String | 原流水号 | | billTime | Long | 下账时间 | | createdBy | String | 创建人 | | updatedBy | String | 更新人 | | createdTime | Date | 创建时间 | | updatedTime | Date | 更新时间 | | version | Long | 版本号 | | 字段名 | 字段类型 | 字段注释 | orderNo | String | 订单编号 | serialNo | String | 流水号 | thirdRefundNo | String | 销售单号 | invoiceNo | String | 发票号码(结算ID setl_id) | hospitalName | String | 定点医疗机构名称 | name | String | 人员姓名(psn_name) | personType | String | 人员类别(psn_type) | personTypeName | String | 人员类别名称 | personNo | String | 个人编号(人员编号 psn_no) | prescriptionNo | String | 处方号(就诊ID mdtrt_id) | icCardNo | String | IC卡号 | acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) | fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) | cashPay | BigDecimal | 自付现金 | medType | String | 医疗类别 | medTypeName | String | 医疗类别名称 | transactionType | String | 交易类型(1-付款 2-退款) | transactionCode | String | 交易编码 | hospitalCode | String | 定点医疗机构编码 | setlTime | Long | 结算时间 | clearingType | String | 清算类别 | clearingTypeName | String | 清算类别名称 | isRefunded | String | 是否已退款(0-未退款 1-已退款) | refundTime | Long | 退款时间 | origSerialNo | String | 原流水号 | billTime | Long | 下账时间 | createdBy | String | 创建人 | updatedBy | String | 更新人 | createdTime | Date | 创建时间 | updatedTime | Date | 更新时间 | version | Long | 版本号 |
| 字段名 | 字段类型 | 字段注释 |
| orderNo | String | 订单编号 |
| serialNo | String | 流水号 |
| thirdRefundNo | String | 销售单号 |
| invoiceNo | String | 发票号码(结算ID setl_id) |
| hospitalName | String | 定点医疗机构名称 |
| name | String | 人员姓名(psn_name) |
| personType | String | 人员类别(psn_type) |
| personTypeName | String | 人员类别名称 |
| personNo | String | 个人编号(人员编号 psn_no) |
| prescriptionNo | String | 处方号(就诊ID mdtrt_id) |
| icCardNo | String | IC卡号 |
| acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) |
| fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) |
| cashPay | BigDecimal | 自付现金 |
| medType | String | 医疗类别 |
| medTypeName | String | 医疗类别名称 |
| transactionType | String | 交易类型(1-付款 2-退款) |
| transactionCode | String | 交易编码 |
| hospitalCode | String | 定点医疗机构编码 |
| setlTime | Long | 结算时间 |
| clearingType | String | 清算类别 |
| clearingTypeName | String | 清算类别名称 |
| isRefunded | String | 是否已退款(0-未退款 1-已退款) |
| refundTime | Long | 退款时间 |
| origSerialNo | String | 原流水号 |
| billTime | Long | 下账时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |
| createdTime | Date | 创建时间 |
| updatedTime | Date | 更新时间 |
| version | Long | 版本号 |
| orderPromotionList线下单促销表 | | 字段名 | Java类型 | 字段注释 | | --- | --- | --- | | id | Long | 主键 | | orderNo | String | 内部订单号,自己生成 | | erpCode | String | 商品编码 | | commodityCount | BigDecimal | 商品数量 | | thirdOrderNo | String | 第三方平台订单号 | | promotionNo | String | 促销编码 | | subPromotionNo | String | 子促销编码 | | promotionType | String | 促销类型  true/**      * 限时特惠      */     FLASH_SALE_RULE(10, "限时特惠", "限时"),      /**      * 单品满减/折      */     SINGLE_FULL_RULE(11,"单品满减/折", "单品满减"),      /**      * 单品买赠      */     BUYING_GIFT_RULE(12,"单品买赠", "买赠"),      /**      * 拼团      */     GROUP(13,"拼团", ""),      /**      * 套装促销      */     PACKAGE_RULE(14,"套装促销", "套装"),      /**      * 满减/折      */     FULL_RULE(20,"满减/折", "满减"),      /**      * 满减/折（进阶）      */     FULL_PLUS_RULE(21,"满减/折（进阶）", ""),      /**      * 满减/折+满赠      */     FULL_SEND_RULE(22,"满减/折+满赠", "满减赠"),      /**      * 加价购      */     MARKUP_RULE(23,"加价购", "换购"),      /**      * 返利活动      */     REBATE_RULE(24,"返利活动", "返利"),      /**      * 商品池促销      */     PROD_POOL_RULE(25,"商品池促销", "商品池"),      /**      * 满赠      */     FULL_SINGLE_SEND_RULE(26,"满赠", "满赠"),      /**      * 返利+满赠      */     REBATE_SEND_RULE(27,"返利+满赠", "返利赠"),      /**      * 免运促销      */     COST_FREE_RULE(31,"免运促销", ""),      /**      * 商品预售      */     ADVANCE_SALE_RULE(32,"商品预售", ""),      /**      * 叠加赠礼      */     OVERLAY_GIFT_RULE(41, "叠加赠礼", "叠加赠"),      /**      * 心钻折扣      */     CORE_DRILL_DISCOUNT_RULE(61, "心钻折扣", "心钻折扣")     ; | | promotionAmount | BigDecimal | 促销金额 | | createdBy | String | 创建人 | | updatedBy | String | 更新人 | | createdTime | Date | 创建时间 | | updatedTime | Date | 更新时间 | | version | Long | 数据版本，每次update+1 | | extendJson | String | 拓展字段 | | type | String | ORDER,DETAIL,ORIGIN | | 字段名 | Java类型 | 字段注释 | id | Long | 主键 | orderNo | String | 内部订单号,自己生成 | erpCode | String | 商品编码 | commodityCount | BigDecimal | 商品数量 | thirdOrderNo | String | 第三方平台订单号 | promotionNo | String | 促销编码 | subPromotionNo | String | 子促销编码 | promotionType | String | 促销类型  true/**      * 限时特惠      */     FLASH_SALE_RULE(10, "限时特惠", "限时"),      /**      * 单品满减/折      */     SINGLE_FULL_RULE(11,"单品满减/折", "单品满减"),      /**      * 单品买赠      */     BUYING_GIFT_RULE(12,"单品买赠", "买赠"),      /**      * 拼团      */     GROUP(13,"拼团", ""),      /**      * 套装促销      */     PACKAGE_RULE(14,"套装促销", "套装"),      /**      * 满减/折      */     FULL_RULE(20,"满减/折", "满减"),      /**      * 满减/折（进阶）      */     FULL_PLUS_RULE(21,"满减/折（进阶）", ""),      /**      * 满减/折+满赠      */     FULL_SEND_RULE(22,"满减/折+满赠", "满减赠"),      /**      * 加价购      */     MARKUP_RULE(23,"加价购", "换购"),      /**      * 返利活动      */     REBATE_RULE(24,"返利活动", "返利"),      /**      * 商品池促销      */     PROD_POOL_RULE(25,"商品池促销", "商品池"),      /**      * 满赠      */     FULL_SINGLE_SEND_RULE(26,"满赠", "满赠"),      /**      * 返利+满赠      */     REBATE_SEND_RULE(27,"返利+满赠", "返利赠"),      /**      * 免运促销      */     COST_FREE_RULE(31,"免运促销", ""),      /**      * 商品预售      */     ADVANCE_SALE_RULE(32,"商品预售", ""),      /**      * 叠加赠礼      */     OVERLAY_GIFT_RULE(41, "叠加赠礼", "叠加赠"),      /**      * 心钻折扣      */     CORE_DRILL_DISCOUNT_RULE(61, "心钻折扣", "心钻折扣")     ; | promotionAmount | BigDecimal | 促销金额 | createdBy | String | 创建人 | updatedBy | String | 更新人 | createdTime | Date | 创建时间 | updatedTime | Date | 更新时间 | version | Long | 数据版本，每次update+1 | extendJson | String | 拓展字段 | type | String | ORDER,DETAIL,ORIGIN |
| 字段名 | Java类型 | 字段注释 |
| id | Long | 主键 |
| orderNo | String | 内部订单号,自己生成 |
| erpCode | String | 商品编码 |
| commodityCount | BigDecimal | 商品数量 |
| thirdOrderNo | String | 第三方平台订单号 |
| promotionNo | String | 促销编码 |
| subPromotionNo | String | 子促销编码 |
| promotionType | String | 促销类型  true/**      * 限时特惠      */     FLASH_SALE_RULE(10, "限时特惠", "限时"),      /**      * 单品满减/折      */     SINGLE_FULL_RULE(11,"单品满减/折", "单品满减"),      /**      * 单品买赠      */     BUYING_GIFT_RULE(12,"单品买赠", "买赠"),      /**      * 拼团      */     GROUP(13,"拼团", ""),      /**      * 套装促销      */     PACKAGE_RULE(14,"套装促销", "套装"),      /**      * 满减/折      */     FULL_RULE(20,"满减/折", "满减"),      /**      * 满减/折（进阶）      */     FULL_PLUS_RULE(21,"满减/折（进阶）", ""),      /**      * 满减/折+满赠      */     FULL_SEND_RULE(22,"满减/折+满赠", "满减赠"),      /**      * 加价购      */     MARKUP_RULE(23,"加价购", "换购"),      /**      * 返利活动      */     REBATE_RULE(24,"返利活动", "返利"),      /**      * 商品池促销      */     PROD_POOL_RULE(25,"商品池促销", "商品池"),      /**      * 满赠      */     FULL_SINGLE_SEND_RULE(26,"满赠", "满赠"),      /**      * 返利+满赠      */     REBATE_SEND_RULE(27,"返利+满赠", "返利赠"),      /**      * 免运促销      */     COST_FREE_RULE(31,"免运促销", ""),      /**      * 商品预售      */     ADVANCE_SALE_RULE(32,"商品预售", ""),      /**      * 叠加赠礼      */     OVERLAY_GIFT_RULE(41, "叠加赠礼", "叠加赠"),      /**      * 心钻折扣      */     CORE_DRILL_DISCOUNT_RULE(61, "心钻折扣", "心钻折扣")     ; |
| promotionAmount | BigDecimal | 促销金额 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |
| createdTime | Date | 创建时间 |
| updatedTime | Date | 更新时间 |
| version | Long | 数据版本，每次update+1 |
| extendJson | String | 拓展字段 |
| type | String | ORDER,DETAIL,ORIGIN |
| orderCouponList线下单coupon表 | | 字段名 | Java类型 | 字段注释 | | --- | --- | --- | | id | Long | 主键 | | orderNo | String | 内部订单号,自己生成 | | erpCode | String | 商品编码 | | commodityCount | BigDecimal | 商品数量 | | thirdOrderNo | String | 第三方平台订单号 | | couponNo | String | 优惠券编码 | | openCode | String | 优惠券导出编码 | | couponName | String | 优惠券名称 | | couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 | | couponDenomination | String | 优惠券面值(String) | | usedCouponAmount | BigDecimal | 使用优惠券金额 | | createdBy | String | 创建人 | | updatedBy | String | 更新人 | | createdTime | Date | 创建时间 | | updatedTime | Date | 更新时间 | | version | Long | 数据版本，每次update+1 | | extendJson | String | 拓展字段 | | type | String | ORDER,DETAIL,ORIGIN | | 字段名 | Java类型 | 字段注释 | id | Long | 主键 | orderNo | String | 内部订单号,自己生成 | erpCode | String | 商品编码 | commodityCount | BigDecimal | 商品数量 | thirdOrderNo | String | 第三方平台订单号 | couponNo | String | 优惠券编码 | openCode | String | 优惠券导出编码 | couponName | String | 优惠券名称 | couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 | couponDenomination | String | 优惠券面值(String) | usedCouponAmount | BigDecimal | 使用优惠券金额 | createdBy | String | 创建人 | updatedBy | String | 更新人 | createdTime | Date | 创建时间 | updatedTime | Date | 更新时间 | version | Long | 数据版本，每次update+1 | extendJson | String | 拓展字段 | type | String | ORDER,DETAIL,ORIGIN |
| 字段名 | Java类型 | 字段注释 |
| id | Long | 主键 |
| orderNo | String | 内部订单号,自己生成 |
| erpCode | String | 商品编码 |
| commodityCount | BigDecimal | 商品数量 |
| thirdOrderNo | String | 第三方平台订单号 |
| couponNo | String | 优惠券编码 |
| openCode | String | 优惠券导出编码 |
| couponName | String | 优惠券名称 |
| couponType | String | 优惠券类型 CASH-现金卷 FULL_DISCOUNT - 满减券 DISCOUNT - 折扣券 FULL_REBATE - 满返券 |
| couponDenomination | String | 优惠券面值(String) |
| usedCouponAmount | BigDecimal | 使用优惠券金额 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |
| createdTime | Date | 创建时间 |
| updatedTime | Date | 更新时间 |
| version | Long | 数据版本，每次update+1 |
| extendJson | String | 拓展字段 |
| type | String | ORDER,DETAIL,ORIGIN |
| 退单 |
| baseRefundInfo 退单基础信息 | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | | --- | --- | --- | --- | | orderNo | OfflineOrderNo | 内部正单订单号 |  | | refundNo | OfflineRefundNo | 内部退款单号 |  | | parentRefundNo | OfflineRefundNo | 内部父退款单号20240805 | 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentRefundNo = refundNo一致则表明是主单,否则就是子单 | | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 |  | | thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 |  | | parentThirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号(主) | srcsaleno主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdRefundNo = thirdRefundNo一致则表明是主单,否则就是子单 | | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | 待定 | | refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | 整单退/部分退 海典无区分.只能自己逻辑判断 | | afterSaleType | AfterSaleType | 售后单类型AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 | 海典无区分 | | refundState | RefundState | 退单状态 REFUNDED 已退款 |  | | reason |  | 退款原因 | 待定 | | created |  | 退单创单时间 | finaltime | | applyTime |  | 退单申请时间 | finaltime | | completeTime |  | 退单完成时间 | finaltime | | billTime |  | 下账时间 | finaltime | | dataVersion |  | 数据版本号 |  | | totalAmount |  | 退款商品金额 | 自己算 | | shopRefund |  | 商家退款总金额 | 自己算 | | consumerRefund |  | 退买家总金额 | 自己算 | | serialNo |  | 优惠券核销流水号 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | ``` refundOrderTagList ``` | List<String> | 退单标签 | PRE_ORDER-预售订单 需求: | | 字段名 | 类型 | 字段解释 | 海典对应的字段 | orderNo | OfflineOrderNo | 内部正单订单号 |  | refundNo | OfflineRefundNo | 内部退款单号 |  | parentRefundNo | OfflineRefundNo | 内部父退款单号20240805 | 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentRefundNo = refundNo一致则表明是主单,否则就是子单 | thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 |  | thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 |  | parentThirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号(主) | srcsaleno主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdRefundNo = thirdRefundNo一致则表明是主单,否则就是子单 | thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | 待定 | refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | 整单退/部分退 海典无区分.只能自己逻辑判断 | afterSaleType | AfterSaleType | 售后单类型AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 | 海典无区分 | refundState | RefundState | 退单状态 REFUNDED 已退款 |  | reason |  | 退款原因 | 待定 | created |  | 退单创单时间 | finaltime | applyTime |  | 退单申请时间 | finaltime | completeTime |  | 退单完成时间 | finaltime | billTime |  | 下账时间 | finaltime | dataVersion |  | 数据版本号 |  | totalAmount |  | 退款商品金额 | 自己算 | shopRefund |  | 商家退款总金额 | 自己算 | consumerRefund |  | 退买家总金额 | 自己算 | serialNo |  | 优惠券核销流水号 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | ``` refundOrderTagList ``` | List<String> | 退单标签 | PRE_ORDER-预售订单 需求: |
| 字段名 | 类型 | 字段解释 | 海典对应的字段 |
| orderNo | OfflineOrderNo | 内部正单订单号 |  |
| refundNo | OfflineRefundNo | 内部退款单号 |  |
| parentRefundNo | OfflineRefundNo | 内部父退款单号20240805 | 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentRefundNo = refundNo一致则表明是主单,否则就是子单 |
| thirdPlatformCode | OfflineThirdPlatformCode | 平台编码 |  |
| thirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号 |  |
| parentThirdRefundNo | OfflineThirdRefundNo | 第三方平台退款单号(主) | srcsaleno主POS销售单号: 1.没有拆单的订单，该字段就是空 2. 该字段有值,则表明拆了单,若parentThirdRefundNo = thirdRefundNo一致则表明是主单,否则就是子单 |
| thirdOrderNo | OfflineThirdOrderNo | 第三方平台订单号 | 待定 |
| refundType | RefundType | 退款类型PART-部分退款ALL-全额退款 | 整单退/部分退 海典无区分.只能自己逻辑判断 |
| afterSaleType | AfterSaleType | 售后单类型AMOUNT-退款售后 AFTER_SALE_GOODS - 退货售后 | 海典无区分 |
| refundState | RefundState | 退单状态 REFUNDED 已退款 |  |
| reason |  | 退款原因 | 待定 |
| created |  | 退单创单时间 | finaltime |
| applyTime |  | 退单申请时间 | finaltime |
| completeTime |  | 退单完成时间 | finaltime |
| billTime |  | 下账时间 | finaltime |
| dataVersion |  | 数据版本号 |  |
| totalAmount |  | 退款商品金额 | 自己算 |
| shopRefund |  | 商家退款总金额 | 自己算 |
| consumerRefund |  | 退买家总金额 | 自己算 |
| serialNo |  | 优惠券核销流水号 |  |
| isOnPromotion | Boolean | 是否参加促销 | 20240813添加 |
| ``` refundOrderTagList ``` | List<String> | 退单标签 | PRE_ORDER-预售订单 需求: |
| baseRefundOrganizationInfo 退款订单归属组织信息 | 复用baseOrganizationInfo |
| baseRefundUserInfo 退单归属会员信息 | | 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 | | --- | --- | --- | --- | | userId | OfflineUserId | 会员id |  | | userName |  | 会员名称 | member_name | | userCardNo | OfflineUserCardNo | 会员卡号 | membercardno | | userMobile | OfflineUserMobile | 会员手机号 | member_tel | | 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 | userId | OfflineUserId | 会员id |  | userName |  | 会员名称 | member_name | userCardNo | OfflineUserCardNo | 会员卡号 | membercardno | userMobile | OfflineUserMobile | 会员手机号 | member_tel |
| 字段名 | 类型 | 字段解释 | 会员信息可能为空??? 海典对应的字段 |
| userId | OfflineUserId | 会员id |  |
| userName |  | 会员名称 | member_name |
| userCardNo | OfflineUserCardNo | 会员卡号 | membercardno |
| userMobile | OfflineUserMobile | 会员手机号 | member_tel |
| baseRefundCashierDeskInfo 退单归属收银台信息 | 复用baseCashierDeskInfo |
| 退单明细 refundDetailList | | 字段名 | 类型 | 字段解释 |  | | --- | --- | --- | --- | | refundNo | OfflineRefundNo | 内部退款单号 |  | | rowNo |  | 商品行号 |  | | platformSkuId | PlatformSkuId | 商品三方平台编码 |  | | erpCode | OfflineErpCode | 商品编码 |  | | erpName |  | 商品名称 |  | | refundCount | BigDecimal | 退款数量 |  | | refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 |  | | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 |  | | originalPrice |  | 商品原单价 |  | | price |  | 商品售价(实际) |  | | commodityCostPrice |  | 商品成本价 |  | | totalAmount |  | 商品总额 |  | | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | | discountShare |  | 优惠分摊 |  | | discountAmount |  | 折扣金额 |  | | billPrice |  | 下账单价(实际) |  | | billAmount |  | 下账金额 |  | | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 | | 字段名 | 类型 | 字段解释 |  | refundNo | OfflineRefundNo | 内部退款单号 |  | rowNo |  | 商品行号 |  | platformSkuId | PlatformSkuId | 商品三方平台编码 |  | erpCode | OfflineErpCode | 商品编码 |  | erpName |  | 商品名称 |  | refundCount | BigDecimal | 退款数量 |  | refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 |  | giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 |  | originalPrice |  | 商品原单价 |  | price |  | 商品售价(实际) |  | commodityCostPrice |  | 商品成本价 |  | totalAmount |  | 商品总额 |  | totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 | discountShare |  | 优惠分摊 |  | discountAmount |  | 折扣金额 |  | billPrice |  | 下账单价(实际) |  | billAmount |  | 下账金额 |  | isOnPromotion | Boolean | 是否参加促销 | 20240813添加 |
| 字段名 | 类型 | 字段解释 |  |
| refundNo | OfflineRefundNo | 内部退款单号 |  |
| rowNo |  | 商品行号 |  |
| platformSkuId | PlatformSkuId | 商品三方平台编码 |  |
| erpCode | OfflineErpCode | 商品编码 |  |
| erpName |  | 商品名称 |  |
| refundCount | BigDecimal | 退款数量 |  |
| refundStatus | OfflineRefundDetailStatus | 明细状态 NORMAL-正常 |  |
| giftType | OfflineDetailGiftType | 赠品类型GIFT-赠品 NOT_GIFT - 非赠品 |  |
| originalPrice |  | 商品原单价 |  |
| price |  | 商品售价(实际) |  |
| commodityCostPrice |  | 商品成本价 |  |
| totalAmount |  | 商品总额 |  |
| totalOriginalAmount |  | 商品原价总额 | 自己算 originalPrice*数量 20240618添加,只添加到消息体中，不入库 |
| discountShare |  | 优惠分摊 |  |
| discountAmount |  | 折扣金额 |  |
| billPrice |  | 下账单价(实际) |  |
| billAmount |  | 下账金额 |  |
| isOnPromotion | Boolean | 是否参加促销 | 20240813添加 |
| ``` refundMedInsSettle ``` | | 字段名 | 字段类型 | 字段注释 | | --- | --- | --- | | refundNo | String | 退款编号 | | serialNo | String | 流水号 | | thirdRefundNo | String | 销售单号 | | invoiceNo | String | 发票号码(结算ID setl_id) | | hospitalName | String | 定点医疗机构名称 | | name | String | 人员姓名(psn_name) | | personType | String | 人员类别(psn_type) | | personTypeName | String | 人员类别名称 | | personNo | String | 个人编号(人员编号 psn_no) | | prescriptionNo | String | 处方号(就诊ID mdtrt_id) | | icCardNo | String | IC卡号 | | acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) | | fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) | | cashPay | BigDecimal | 自付现金 | | medType | String | 医疗类别 | | medTypeName | String | 医疗类别名称 | | transactionType | String | 交易类型(1-付款 2-退款) | | transactionCode | String | 交易编码 | | hospitalCode | String | 定点医疗机构编码 | | setlTime | Long | 结算时间 | | clearingType | String | 清算类别 | | clearingTypeName | String | 清算类别名称 | | isRefunded | String | 是否已退款(0-未退款 1-已退款) | | refundTime | Long | 退款时间 | | origSerialNo | String | 原流水号 | | billTime | Long | 下账时间 | | createdBy | String | 创建人 | | updatedBy | String | 更新人 | | createdTime | Date | 创建时间 | | updatedTime | Date | 更新时间 | | version | Long | 版本号 | | 字段名 | 字段类型 | 字段注释 | refundNo | String | 退款编号 | serialNo | String | 流水号 | thirdRefundNo | String | 销售单号 | invoiceNo | String | 发票号码(结算ID setl_id) | hospitalName | String | 定点医疗机构名称 | name | String | 人员姓名(psn_name) | personType | String | 人员类别(psn_type) | personTypeName | String | 人员类别名称 | personNo | String | 个人编号(人员编号 psn_no) | prescriptionNo | String | 处方号(就诊ID mdtrt_id) | icCardNo | String | IC卡号 | acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) | fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) | cashPay | BigDecimal | 自付现金 | medType | String | 医疗类别 | medTypeName | String | 医疗类别名称 | transactionType | String | 交易类型(1-付款 2-退款) | transactionCode | String | 交易编码 | hospitalCode | String | 定点医疗机构编码 | setlTime | Long | 结算时间 | clearingType | String | 清算类别 | clearingTypeName | String | 清算类别名称 | isRefunded | String | 是否已退款(0-未退款 1-已退款) | refundTime | Long | 退款时间 | origSerialNo | String | 原流水号 | billTime | Long | 下账时间 | createdBy | String | 创建人 | updatedBy | String | 更新人 | createdTime | Date | 创建时间 | updatedTime | Date | 更新时间 | version | Long | 版本号 |
| 字段名 | 字段类型 | 字段注释 |
| refundNo | String | 退款编号 |
| serialNo | String | 流水号 |
| thirdRefundNo | String | 销售单号 |
| invoiceNo | String | 发票号码(结算ID setl_id) |
| hospitalName | String | 定点医疗机构名称 |
| name | String | 人员姓名(psn_name) |
| personType | String | 人员类别(psn_type) |
| personTypeName | String | 人员类别名称 |
| personNo | String | 个人编号(人员编号 psn_no) |
| prescriptionNo | String | 处方号(就诊ID mdtrt_id) |
| icCardNo | String | IC卡号 |
| acctPay | BigDecimal | 个人账户支付(个人账户支出 acct_pay) |
| fundPay | BigDecimal | 统筹支出(基金支付总额 fund_pay_sumamt) |
| cashPay | BigDecimal | 自付现金 |
| medType | String | 医疗类别 |
| medTypeName | String | 医疗类别名称 |
| transactionType | String | 交易类型(1-付款 2-退款) |
| transactionCode | String | 交易编码 |
| hospitalCode | String | 定点医疗机构编码 |
| setlTime | Long | 结算时间 |
| clearingType | String | 清算类别 |
| clearingTypeName | String | 清算类别名称 |
| isRefunded | String | 是否已退款(0-未退款 1-已退款) |
| refundTime | Long | 退款时间 |
| origSerialNo | String | 原流水号 |
| billTime | Long | 下账时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |
| createdTime | Date | 创建时间 |
| updatedTime | Date | 更新时间 |
| version | Long | 版本号 |


海典医保消息到订单表字段映射

| 海典MQ消息 | 映射到订单表 |
| --- | --- |
| "inhosno" IS '流水号' | serial_no |
| "saleno" IS '销售单号' | 正单→ thirdOrderNo退单 → thirdRefundNo |
| "invoiceno" IS '发票号码(结算ID)' | invoice_no |
| "yljg" IS '定点医疗机构名称' | hospital_name |
| "xm" IS '人员姓名(psn_name)' | name |
| "rylb" IS '人员类别(psn_type)' | person_typeperson_type_name |
| "grbh" IS '个人编号(人员编号 psn_no)' | person_no |
| "recipe" IS '处方号(就诊ID)' | prescription_no |
| "ic_card" IS 'IC卡号' | ic_card_no |
| "zh" IS '个人账户支付( acct_pay)' | acct_pay |
| "tc" IS '统筹支出(基金支付总额)' | fund_pay |
| "xj" IS '自付现金' | cash_pay |
| "med_type" IS '医疗类别' | med_typemed_type_name |
| "status" IS '1-付款 2-退款' | transaction_type |
| "infno" IS '交易编码' | transaction_code |
| "fixmedins_code" IS '定点医疗机构编码' | hospital_code |
| "setl_time" IS '结算时间' | setl_time |
| "clr_type" IS '清算类别' | clearing_typeclearing_type_name |
| "isreturn" IS '是否已退款 0-未退款 1-已退款' | is_refunded |
| "returntime" IS '退款时间' | refund_time |
| "retinhosno" IS '原流水号' | orig_serial_no |
| "saletime" IS '下账时间' | bill_time |


正单MQ报文示例

true{
        "baseOrderInfo": {
            "orderNo": {
                "orderNo": "1384758028433950223"
            },
            "parentOrderNo": null,
            "thirdPlatformCodeValue": "HAIDIAN",
            "thirdOrderNo": {
                "thirdOrderNo": "1025050800054066"
            },
            "parentThirdOrderNo": null,
            "dayNum": "",
            "orderStateValue": "DONE",
            "created": "2025-05-08 16:00:00",
            "payTime": "2025-05-08 16:00:00",
            "completeTime": "2025-05-08 16:00:00",
            "billTime": "2025-05-08 16:00:00",
            "dataVersion": 1,
            "actualPayAmount": 420.000000,
            "actualCollectAmount": 420.000000,
            "orderCouponList": [],
            "serialNo": null,
            "isOnPromotion": false
        },
        "basePrescriptionInfo": {
            "prescriptionTypeValue": null,
            "prescriptionNo": null
        },
        "payInfoList": [
            {
                "payType": {
                    "payType": "102"
                },
                "payName": "收钱吧微信",
                "payAmount": 420.000000
            }
        ],
        "orderDetailList": [
            {
                "baseOrderDetailInfo": {
                    "orderNo": {
                        "orderNo": "1384758028433950223"
                    },
                    "rowNo": "1",
                    "platformSkuId": {
                        "platformSkuId": "106676"
                    },
                    "erpCode": {
                        "erpCode": "106676"
                    },
                    "erpName": "人血白蛋白_10G(20%,50ML)*1瓶_美国杰特贝林",
                    "commodityCount": 1.000000,
                    "statusValue": "NORMAL",
                    "giftTypeValue": "NOT_GIFT",
                    "originalPrice": 420.000000,
                    "price": 420.000000,
                    "commodityCostPrice": 297.223523,
                    "totalAmount": 420.000000,
                    "totalOriginalAmount": 420.000000000000,
                    "discountShare": 0.000000,
                    "discountAmount": 0.000000,
                    "billPrice": 420.000000,
                    "billAmount": 420.000000,
                    "isOnPromotion": false,
                    "fiveClass": "A020316002"
                },
                "pickInfoList": [
                    {
                        "erpCode": {
                            "erpCode": "106676"
                        },
                        "makeNo": {
                            "makeNo": "P100604311"
                        },
                        "count": 1.000000
                    }
                ]
            }
        ],
        "baseUserInfo": {
            "userId": {
                "userId": "1831538435873138181"
            },
            "userName": "刁思林",
            "userCardNo": {
                "userCardNo": "250002841983"
            },
            "userMobile": {
                "userMobile": "13982795890"
            }
        },
        "baseOrganizationInfo": {
            "storeCode": "HX28",
            "storeName": "[HX28]一心堂古蔺东新街店",
            "companyCode": "1006",
            "companyName": "四川一心堂医药连锁有限公司",
            "storeDirectJoinTypeValue": "DIRECT_SALES"
        },
        "baseCashierDeskInfo": {
            "posCashierDeskNo": {
                "posCashierDeskNo": "101"
            },
            "cashier": "1176057",
            "cashierName": "罗燕",
            "picker": null,
            "pickerName": null,
            "shiftId": {
                "shiftId": "1"
            },
            "shiftDate": "2025-05-08 16:00:00"
        },
        "medInsSettle": null,
        "orderPromotionList": [],
        "orderCouponList": []
    }

退单MQ报文示例

true{
        "baseRefundInfo": {
            "orderNo": {
                "orderNo": "1389104003072030093"
            },
            "refundNo": {
                "refundNo": "1389995468892210093"
            },
            "parentRefundNo": null,
            "thirdPlatformCodeValue": "HAIDIAN",
            "thirdRefundNo": {
                "thirdRefundNo": "1025050800053097"
            },
            "parentThirdRefundNo": null,
            "thirdOrderNo": {
                "thirdOrderNo": "1025050800059733"
            },
            "refundTypeValue": "ALL",
            "afterSaleTypeValue": "AFTER_SALE_AMOUNT_GOODS",
            "refundStateValue": "REFUNDED",
            "reason": "xc",
            "created": "2025-05-08 16:08:19",
            "applyTime": "2025-05-08 16:08:19",
            "completeTime": "2025-05-08 16:08:19",
            "billTime": "2025-05-08 16:08:19",
            "dataVersion": 1,
            "totalAmount": 108.320000,
            "shopRefund": 108.320000,
            "consumerRefund": 108.320000,
            "serialNo": null,
            "isOnPromotion": false
        },
        "baseRefundOrganizationInfo": {
            "storeCode": "HI19",
            "storeName": "[HI19]一心堂成都市新都区兴业大道药店",
            "companyCode": "1006",
            "companyName": "四川一心堂医药连锁有限公司",
            "storeDirectJoinTypeValue": "DIRECT_SALES"
        },
        "baseRefundUserInfo": {
            "userId": {
                "userId": "1790928694121961477"
            },
            "userName": "马",
            "userCardNo": {
                "userCardNo": "900076748979"
            },
            "userMobile": {
                "userMobile": "15198037473"
            }
        },
        "baseRefundCashierDeskInfo": {
            "posCashierDeskNo": {
                "posCashierDeskNo": "101"
            },
            "cashier": "1162310",
            "cashierName": "胡晓丹",
            "picker": null,
            "pickerName": null,
            "shiftId": {
                "shiftId": "1"
            },
            "shiftDate": "2025-05-08 16:08:19"
        },
        "refundDetailList": [
            {
                "refundNo": {
                    "refundNo": "1389995468892210093"
                },
                "rowNo": "1",
                "platformSkuId": {
                    "platformSkuId": "118872"
                },
                "erpCode": {
                    "erpCode": "118872"
                },
                "erpName": "(兴)糠酸莫米松凝胶_仙琚_0.1%(5G:5MG)*20G*1支",
                "refundCount": 1.000000,
                "refundStatusValue": "REFUNDED",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 49.800000,
                "price": 49.800000,
                "commodityCostPrice": 10.442500,
                "totalAmount": 49.800000,
                "totalOriginalAmount": 49.************,
                "discountShare": 0.000000,
                "discountAmount": 0.000000,
                "billPrice": 49.800000,
                "billAmount": 49.800000,
                "isOnPromotion": false,
                "fiveClass": "A010711001"
            },
            {
                "refundNo": {
                    "refundNo": "1389995468892210093"
                },
                "rowNo": "2",
                "platformSkuId": {
                    "platformSkuId": "134404"
                },
                "erpCode": {
                    "erpCode": "134404"
                },
                "erpName": "丹皮酚软膏_10G*2支_长春普华",
                "refundCount": 1.000000,
                "refundStatusValue": "REFUNDED",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 24.500000,
                "price": 24.500000,
                "commodityCostPrice": 4.911928,
                "totalAmount": 24.500000,
                "totalOriginalAmount": 24.************,
                "discountShare": 0.000000,
                "discountAmount": 0.000000,
                "billPrice": 24.500000,
                "billAmount": 24.500000,
                "isOnPromotion": false,
                "fiveClass": "A040710001"
            },
            {
                "refundNo": {
                    "refundNo": "1389995468892210093"
                },
                "rowNo": "3",
                "platformSkuId": {
                    "platformSkuId": "132479"
                },
                "erpCode": {
                    "erpCode": "132479"
                },
                "erpName": "杀菌止痒洗剂_150ML*1瓶_贵州长生",
                "refundCount": 1.000000,
                "refundStatusValue": "REFUNDED",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 37.800000,
                "price": 34.020000,
                "commodityCostPrice": 9.911668,
                "totalAmount": 34.020000,
                "totalOriginalAmount": 37.************,
                "discountShare": 3.780000,
                "discountAmount": 3.780000,
                "billPrice": 34.020000,
                "billAmount": 34.020000,
                "isOnPromotion": false,
                "fiveClass": "A040802001"
            }
        ],
        "refundMedInsSettle": null,
        "refundPayInfoList": [
            {
                "payType": {
                    "payType": "107"
                },
                "payName": "市医保_线下_",
                "payAmount": 108.320000
            }
        ]
    }