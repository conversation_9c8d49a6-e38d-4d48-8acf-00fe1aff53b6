# B2B-订单中台 上线cheklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| B2B 上线 | order-service (延后上线)order-atom-serviceorder-delivery-atomorder-after-sale-atom |  | 4158/B2B_V2 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| order-service | application.yml | mq:   topic:     event:       refund-audit: TP_ORDER_ORDER-SERVICE_REFUND-AUDIT-EVENT       after-sale-create: TP_ORDER_ORDER-SERVICE_AFTER-SALE-CREATE-EVENT       after-sale-status-update: TP_ORDER_ORDER-SERVICE_AFTER-SALE-STATUS-UPDATE-EVENT       refund-create: TP_ORDER_ORDER-SERVICE_AFTER-SALE-REFUND-CREATE-EVENT       return-create: TP_ORDER_ORDER-SERVICE_AFTER-SALE-RETURN-CREATE-EVENT       order-pay-status-update: TP_ORDER_ORDER-SERVICE_ORDER-PAY-STATUS-UPDATE-EVENT       delivery-create: TP_ORDER_ORDER-SERVICE_DELIVERY-ORDER-CREATE-EVENT       order-main-status-update: TP_ORDER_ORDER-SERVICE_ORDER-MAIN-STATUS-UPDATE-EVENT | 领域事件上线前需要  创建topic |
| order-atom-service | application.yml | offline_order_amount:           actual-data-nodes: order-offline-$->{0..1}.offline_order_amount_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_order_delivery_address:           actual-data-nodes: order-offline-$->{0..1}.offline_order_delivery_address_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_order_delivery_address_ext:           actual-data-nodes: order-offline-$->{0..1}.offline_order_delivery_address_ext_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_order_detail_ext:           actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_ext_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_order_detail_trace:           actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_trace_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_order_ext:           actual-data-nodes: order-offline-$->{0..1}.offline_order_ext_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_refund_order_detail_ext:           actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_ext_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_refund_order_ext:           actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_ext_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm         offline_refund_order_amount:           actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_amount_$->{0..255}           database-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm           table-strategy:             hint:               algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm     selects[9]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectList     selects[10]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectOne     selects[11]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.selectById      addUpdates[6]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.insert     addUpdates[7]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.update     addUpdates[8]: com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper.updateById |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
| 路由配置 | # 订单新模型  - id: order-service  uri: [lb://order-service](lb://order-service)  predicates:  - Path=/order-world/**  filters:  - StripPrefix=1  - YxtLoginAccess |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
| canal创建 | 新建order_world_order_sync |  |
|  | 新建order_world_after_sale_order_sync |  |
|  |  |  |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |