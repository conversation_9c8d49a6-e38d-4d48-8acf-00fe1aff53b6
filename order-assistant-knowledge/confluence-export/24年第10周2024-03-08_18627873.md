# 24年第10周2024-03-08

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [2、重点项目周进展与风险概况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-2%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE%E5%91%A8%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9%E6%A6%82%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 | 杨润康  王世达  杨国枫 |  |  |  |
| 2 | .net接口中台迁移至JAVA | 杨俊峰 |  |  |  |
| 3 | 优雅发布升级 | 杨润康 |  |  | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 | 杨润康 |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 | 杨国枫 郭志明 |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) 上线计划 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1. POS类型配置开发并配合测试 | **㊀计划工作****㊁实际完成**1. POS类型配置（待上线） **㊂遗留问题**1. 平台网店换店问题-等凯哥周一回来商讨 **㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 | 王世达 | **本周总工时：5d**1. 海典H2上线发版验证 2. 省/市医保标识发版验证 3. 海典H2遗留问题处理 4. 海典H1走B2C门店仓下账验证配合 | **㊀计划工作******1. 海典H2上线发版验证 2. 省/市医保标识发版验证**㊁实际完成******1. 海典H2上线发版验证 完成 2. 省/市医保标识发版验证 完成**㊂遗留问题******1. 海典H2金额不匹配导致下账失败，海典已修改，等待推送订单验证，今日完成**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：5d**1.库存优化2.异常订单退款问题3.下账时机技术方案4.内购商城 | **㊀计划工作**1.库存优化2.异常订单退款问题3.下账时机技术方案4.内购商城**㊁实际完成**1.库存优化 上线2.异常订单退款问题 上线3.下账时机技术方案 待评审**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1.下账时机代码编写**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 4 |  | **本周总工时：5 day**1.内购商城测试2.云仓迁移订单刷历史商品数据3.多组处方图片只显示一张 | **㊀计划工作**1.内购商城测试2.云仓迁移订单刷历史商品数据3.多组处方图片只显示一张**㊁实际完成**1.内购商城测试2.云仓迁移订单刷历史商品数据3.多组处方图片只显示一张**㊂遗留问题**1.修补历史处方图片数据(待确认)**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 5 | 杨俊峰 | **本周总工时：5 day**1.NET CORE 项目容器化配合运维调试。 1day2.处理线上问题。 1day3. 京东到家处方图片 、小票优惠金额、导出订单没有导出配送员工编号。 2day4. 商品变更接入、订单配送员工历史数据刷入。 1day | **㊀计划工作**1.NET CORE 项目容器化配合运维调试。 2.处理线上问题。 3. 京东到家处方图片 、小票优惠金额、导出订单没有导出配送员工编号。 4. 商品变更接入、订单配送员工历史数据刷入。 **㊁实际完成****㊂遗留问题**1.net core 容器化目前卡住了，在linux 服务器无法发布代码。主要还是加密文件的问题。2.商品变更估计剩余0.5day 工时工作量。主要是自测部分。**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 | 李洋 | **本周总工时：5day**1. 海典H2遗留问题处理 2. 海典H2切换POS运维 3. 2月财务数据相关支持 | **㊀计划工作**1. 海典POS上线 **㊁实际完成**1. 海典POS上线 **㊂遗留问题** | **** |  |
| 7 | 杨润康 | **本周总工时：5day**1. 线下单结构接口文档、会议 2. DDD应用架构和鹏哥沟通,重新调整项目结构, DDD分享（MapStruct接入，聚合根的change-tracking等） 3. 经营分析金额计算逻辑[梳理](https://jira.hxyxt.com/browse/ORDER-184) 4. [网关token配置](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18625449)方式文档 | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 8 | 蒋一良 | **本周总工时：5day**1. 电脑开发环境配置安装、了解团队工作项目、配置账号 2. 熟悉O2O订单流程（Mq接受订单消息，是否转B2C单，组合商品拆分，推送审方，骑手配送，订单信息保存） 3. 熟悉订单相关数据表结构（order_info,order_pay_info,order_detail,order_delivery_address等） | **** | **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  | 网关token |  |
| 2 |  | 1. [告别BeanUtils，Mapstruct从入门到精通 - 掘金 (juejin.cn)](https://juejin.cn/post/7140149801991012365) 2. idea实体转换插件-Bean converter |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[③后端研发部-线上值班模板](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6371159)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

1. [https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18627731](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18627731) 工时和工作内容
2. [https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) 上线计划

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |