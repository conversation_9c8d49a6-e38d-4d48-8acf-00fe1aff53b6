# 24年第03周 2024-01-19

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5day**1. 购物车优化2期开发 2. 抖店O2O开发 3. 购物车错误数据清洗 4. 第四批切店数据校验 | **㊀计划工作**1. 购物车优化2期开发测试 2. 抖店O2O开发 **㊁实际完成**1. 购物车优化2期开发测试（测试中） 2. 抖店O2O开发（自测中） 3. 购物车错误数据清洗（已上线） 4. 第四批切店数据校验（进行中） **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 切店支持 2. 抖店o2o联调 3. 购物车二期上线 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 | 王世达 | **本周总工时：4.5day**1. 海典pos 联调 1.5day 2. 门店B2C拣货复核联调 1day 3. 门店B2C拣货复核bug修改 0.5day 4. 门店B2C订单下帐问题修改 2 day | **㊀计划工作**1. 海典pos 对接 2. 门店B2C需求 **㊁实际完成**1. 海典pos 对接 测试中 2. 门店B2C需求 测试中 **㊂遗留问题** 门店B2C退款单下问题，B2C与O2O的下单、拣货、退款流程不一致导致**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：5day**1. 抖店O2O开发 2. 线上问题查看 | **㊀计划工作**1. 抖店O2O开发 **㊁实际完成**1. 抖店O2O开发（自测） 2. 线上BUG修改 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | 1. 切店支持 2. 抖店O2O提测 **** |  |
| 4 |  | **本周总工时：5day**1.拣货复核效期管理2.拣货复核同步平台订单状态3.增加平台转自配送按钮4.定时增量同步门店5.页面显示心跳时间 | **㊀计划工作**1.拣货复核效期管理2.拣货复核同步平台订单状态3.增加平台转自配送按钮4.定时增量同步门店5.页面显示心跳时间**㊁实际完成**1.拣货复核效期管理2.拣货复核同步平台订单状态3.增加平台转自配送按钮4.定时增量同步门店**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 | 杨俊峰 | **本周总工时：4.5day**1. B2C 面单打印 2day 2. 京东到家遗留问题。1.5 day   1. 转自配送功能失效   2. 自配送状态通知异常 3. 转自配送功能失效 4. 自配送状态通知异常 5. 修复饿了么门店自提功能。0.5day 6. 其他门店异常情况远程支持。0.5day | **㊀计划工作****㊁实际完成**京东到家自配送状态通知异常 60%**㊃风险问题** **** | **** |  |
| 6 | 李洋 | **本周总工时：5day**1. 门店切店运维 2 day 2. 完成新增三方平台营业设置需求 0.5 day 3. 海典H1\H2POS下账调整 0.5day 4. B2C订单走科传O2O下账调整（主达哥）2day | **㊀计划工作**1. POS H1\H2对接测试 2. 三方平台营业设置需求 3. B2C订单走O2O下账 **㊁实际完成**1. 三方平台营业设置需求 **㊂遗留问题**1. POS H1\H2订单详情表billPrice字段精度缺失 | **㊀需求研发相关**1. POS H1\H2对接 2. 三方平台营业设置需求 3. B2C订单走O2O下账 |  |
| 7 | 杨润康 | **本周总工时：5d**1. 切店代码优化,已上线 1d 2. 线上服务稳定性保障 2.5d 3. 线上问题排查 1d 4. 网关超时日志添加 0.5d | **㊀计划工作**1. 慢SQL日志、接口耗时优化（分页,异步等） 2. 线上问题跟进 **㊁实际完成** **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****2.借助需求熟悉整体研发流程,从开发到上线全阶段****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 切换值班

| 时间 | 值班人员 |  |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |