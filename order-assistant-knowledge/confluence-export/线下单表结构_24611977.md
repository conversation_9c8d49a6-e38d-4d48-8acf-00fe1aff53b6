# 线下单表结构

### 对接人: 刘宇鑫

退款单也是以下数据结构,只是数据的正负不一致

### 海典

### t_sale_h零售销售主表

| 字段名 | 字段 | 备注 |
| --- | --- | --- |
| saleno | 销售单号 |  |
| busno | 业务机构编码 |  |
| posno | POS机号 |  |
| accdate | 会计日 | '2024-01-01' |
| starttime | 开始时间 |  |
| finaltime | 结束时间 |  |
| payee | 收银员 |  |
| discounter | 打折授权人 | json结构中无 |
| crediter | 赊销授权人 | json结构中无 |
| returner | 退货授权人 | json结构中无 |
| warranter1 | 拆零授权人 | json结构中无 |
| warranter2 | 备用授权人2 | json结构中无 |
| warranter3 | 备用授权人3 | json结构中无 |
| warranter4 | 备用授权人4 | json结构中无 |
| warranter5 | 备用授权人5 | json结构中无 |
| stdsum | 标价总金额 |  |
| netsum | 实价金额 |  |
| loss | 兑换损失 |  |
| membercardno | 会员卡号 | json结构中无 |
| precash | 收现金额 |  |
| stamp | 时间戳 | json结构中无 |
| shiftid | 班次 | json结构中无 |
| shiftdate | 班次日期 | json结构中无 |
| yb_saleno | 医保接口专用销售单号 | json结构中无 |
| compid | 企业编号 |  |
| weather | 天气 | json结构中无 |
| doctorid | 医生 | json结构中无 |
| olshopid | 网店编码 | json结构中无 |
| olpickno | 网店拣货单号 | json结构中无 |
| notes | 备注 | json结构中无 |
| pstplannos | 促销赠送方案 | json结构中无 |
| register_no | 挂号单号 | json结构中无 |
| srcsaleno | 原销售单号 | json结构中无 |
| indentsource | 网上订单来源 | json结构中无 |
| ext_str1 | 扩展属性短字符1 | json结构中无 |
| ext_str2 | 扩展属性短字符2 | json结构中无 |
| ext_str3 | 扩展属性短字符3 | json结构中无 |
| ext_str4 | 扩展属性短字符4 | json结构中无 |
| ext_str5 | 扩展属性短字符5 | json结构中无 |
| ext_num1 | 扩展属性小数1 | json结构中无 |
| ext_num2 | 扩展属性小数2 | json结构中无 |
| ext_num3 | 扩展属性小数3 | json结构中无 |
| ext_num4 | 扩展属性小数4 | json结构中无 |
| ext_num5 | 扩展属性小数5 | json结构中无 |
| ext_date1 | 扩展属性日期1 | json结构中无 |
| ext_date2 | 扩展属性日期2 | json结构中无 |
| ext_date3 | 扩展属性日期3 | json结构中无 |
| printcount | 打印次数 | json结构中无 |
| is_comcfplatporm | 是否来源于处方平台的处方销售 | json结构中无 |
| doctor_rate | 医生加价率 | json结构中无 |
| upflag_honey | 是否上传了小蜜 | json结构中无 |
| uptime_honey | 上传小蜜的时间 | json结构中无 |
| cfno_honey | 小蜜处方编号 | json结构中无 |
| orderno | 扫码支付挂单号 | json结构中无 |
| derpbillno | DERP单号 | json结构中无 |
| w_orderno | 外部单号 | json结构中无 |
| w_ordertype | 外部单号类型 | json结构中无 |
| js_flag | 预费结算标志 | json结构中无 |
| saleflag | 零售类型 | json结构中无 |
| coupon_type | 优惠券类型 | json结构中无 |
| coupon_code | 优惠券编号 | json结构中无- 可能是saleCouponDetails |
| doctorid_new | 医生(仅作为标记) | json结构中无 |
| saletype | 销售单类型 | json结构中无 |
| ysd_saleno | 预售订金零单号 | json结构中无 |
| member_name | 会员姓名 | json结构中无 |
| member_tel | 会员电话 | json结构中无 |


finaltime

### t_sale_d零售销售明细表

| 字段名 | 字段 | 备注 |
| --- | --- | --- |
| saleno | 销售单号 |  |
| rowno | 行号 |  |
| busno | 业务机构编号 |  |
| accdate | 会计日 |  |
| wareid | 商品编号 |  |
| stallno | 货位编号 |  |
| makeno | 生产批号 |  |
| stdprice | 标价 |  |
| netprice | 实价 |  |
| minprice | 拆零售价 |  |
| wareqty | 商品数量 | json结构中无 |
| groupid | 组编码 |  |
| saler | 营业员 |  |
| times | 组剂数 |  |
| invalidate | 有效期至 |  |
| minqty | 拆零数量 |  |
| stdtomin | 拆零比率 |  |
| distype | 打折类型 | json结构中无 |
| disno | 促销单号 | json结构中无 |
| message | 消息 | json结构中无 |
| purprice | 进价 |  |
| purtax | 进项税率 |  |
| avgpurprice | 加权进价 |  |
| rowtype | 记录类型 |  |
| insno | 医保卡号 | json结构中无 |
| pile | 销售积分 |  |
| saletax | 销项税 |  |
| storeqty | 库存数量 | json结构中无 |
| batid | 批次号 |  |
| isrestrict | 是否特殊药品 | json结构中无 |
| medicationreminder | 用药提醒 | json结构中无 |
| disrate | 折率 |  |
| pstplanno | 促销赠送方案编码 | json结构中无 |
| notintegral | 不参与积分 | json结构中无 |
| resprice | 恢复原价 | json结构中无 |
| stdminprice | 拆零标价 |  |
| batchno | 原系统批次号 | json结构中无 |
| idno | 原系统批次识别号 | json结构中无 |
| profit_flag | 标记为不计毛利 | json结构中无 |
| notes | 备注 | json结构中无 |
| uniteuseid | 联合用药组ID | json结构中无 |
| cfno | 中医馆处方单号 | json结构中无 |
| unite_rate | 联营商品折率 | json结构中无 |
| unite_netprice | 联营商品折后价格 | json结构中无 |
| ext_str1 | 扩展属性短字符1 | json结构中无 |
| ext_str2 | 扩展属性短字符2 | json结构中无 |
| ext_str3 | 扩展属性短字符3 | json结构中无 |
| ext_str4 | 扩展属性短字符4 | json结构中无 |
| ext_str5 | 扩展属性短字符5 | json结构中无 |
| ext_num1 | 扩展属性小数1 | json结构中无 |
| ext_num2 | 扩展属性小数2 | json结构中无 |
| ext_num3 | 扩展属性小数3 | json结构中无 |
| ext_num4 | 扩展属性小数4 | json结构中无 |
| ext_num5 | 扩展属性小数5 | json结构中无 |
| ext_date1 | 扩展属性日期1 | json结构中无 |
| ext_date2 | 扩展属性日期2 | json结构中无 |
| ext_date3 | 扩展属性日期3 | json结构中无 |
| multiple_integral | 积分倍数 | json结构中无 |
| old_rowno | 原销售行号 | json结构中无 |
| medphys_type | 是否理疗项目0否；1是 | json结构中无 |
| bindwareid | 捆绑商品ID | json结构中无 |
| max_multiple_integral | 最大积分倍数 | json结构中无 |
| uniteuseno | 联合用药单据号 | json结构中无 |
| before_netprice | 调整前实价 | json结构中无 |
| netamt | 金额 |  |
| ext_str11 | 扩展字段11 | json结构中无 |
| ext_str12 | 扩展字段12 | json结构中无 |
| ext_str13 | 扩展字段13 | json结构中无 |
| ext_str14 | 扩展字段14 | json结构中无 |
| ext_str15 | 扩展字段15 | json结构中无 |
| ext_str16 | 扩展字段16 | json结构中无 |
| ext_str17 | 扩展字段17 | json结构中无 |
| ext_num18 | 扩展字段18 | json结构中无 |
| ext_num19 | 扩展字段19 | json结构中无 |
| ext_num20 | 扩展字段20 | json结构中无 |
| ext_num21 | 扩展字段21 | json结构中无 |
| ext_num22 | 扩展字段22 | json结构中无 |
| ext_date23 | 扩展字段23 | json结构中无 |
| ext_date24 | 扩展字段24 | json结构中无 |
| ext_date25 | 扩展字段25 | json结构中无 |
| split_no | 拆零单号 | json结构中无 |
| promoplanno | 超量单号 | json结构中无 |
| payee | 收款员 | json结构中无 |
| unitey_rate | 联营商品折率 | json结构中无 |
| reject_compute | 销量计算剔除 ,1剔除 | json结构中无 |
| discount | 折扣金额 | json结构中无 |
| shareprice | 分摊前价格 | json结构中无 |
| medical_insurance_ware | 是否医保商品 | json结构中无 |
| flbwh | 返利备忘号 | json结构中无 |
| hdfs | 活动方式 | json结构中无 |


### t_sale_pay 零售收款表

| 字段名 | 字段 | 备注 |
| --- | --- | --- |
| saleno | 销售单号 |  |
| paytype | 付款账期 |  |
| cardno | 卡号 | json结构中无 |
| netsum | 实价金额 |  |
| netsum_bak | 备份实价金额 | json结构中无 |
| pricetype | 金额状态 | json结构中无 |
| ext_str1 | 扩展属性短字符1 | json结构中无 |
| ext_str2 | 扩展属性短字符2 | json结构中无 |
| ext_str3 | 扩展属性短字符3 | json结构中无 |
| ext_str4 | 扩展属性短字符4 | json结构中无 |
| ext_str5 | 扩展属性短字符5 | json结构中无 |
| ext_num1 | 扩展属性小数1 | json结构中无 |
| ext_num2 | 扩展属性小数2 | json结构中无 |
| ext_num3 | 扩展属性小数3 | json结构中无 |
| ext_num4 | 扩展属性小数4 | json结构中无 |
| ext_num5 | 扩展属性小数5 | json结构中无 |
| ext_date1 | 扩展属性日期1 | json结构中无 |
| ext_date2 | 扩展属性日期2 | json结构中无 |
| ext_date3 | 扩展属性日期3 | json结构中无 |
| cash_integral | 抵现扣减积分 | json结构中无 |
| accdate | 会计日 | json结构中无 |
| ybfl | 是否异地医保 | json结构中无 |


### json

{
  "shiftid": 1,
  "membercardno": "900000101798",
  "stdsum": 76,
  "posno": "101",
  "starttime": "2024-02-20 04:17:04.000",
  "tableEnd": "_202402",
  "saleno": "1024021900001268",
  "payee": "1037203",
  "loss": 0,
  "compid": 1003,
  "precash": 66,
  "busno": *********,
  "accdate": "2024-02-19",
  "coupon_code": "622511053154886",
  "saleCouponDetails": [],
  "saleDetails": [
    {
      "avgpurprice": 0,
      "stdtomin": 1,
      "commonName": "清热解毒口服液_诺金_10ML*12支",
      "mdmBusno": "E049",
      "wareid": 100703,
      "ware_code": "",
      "pile": 0,
      "stdprice_new": "",
      "saler": 1037203,
      "stallno": "11*********",
      "disrate": 0.868421,
      "saleno": "1024021900001268",
      "saletax": 13,
      "times": 1,
      "minprice": 0,
      "orgname": "[E049]一心堂盐边鑫盛广场店",
      "compid": 1003,
      "ware_qty": 2,
      "busno": *********,
      "compname": "四川鸿翔一心堂医药连锁有限公司(测试)",
      "barcode": "6909717000010",
      "minqty": 0,
      "accdate": "2024-02-19",
      "rowno": 1,
      "gainType": 0,
      "batid": 111116428272,
      "specDesc": "",
      "netprice": 33,
      "row_type": "1",
      "makeno": "2F36011",
      "groupid": 500001,
      "invalidate": "2024-05-31",
      "approvalNumber": "国药准字Z20043570",
      "isChinaMedicine": 0,
      "distype": 0,
      "paidamount": 0,
      "netamt": 66,
      "purprice": 0,
      "stdminprice": 38,
      "stdprice": 38,
      "purtax": 0,
      "classType": ""
    }
  ],
  "ordersource": 1,
  "netsum": 66,
  "group_id": 500001,
  "originalGroupId": 500001,
  "salePayDetails": [
    {
      "netsum": 66,
      "paytypen": "现金",
      "paytype": "1",
      "saleno": "1024021900001268"
    }
  ],
  "shiftdate": "2024-02-20 00:02:14.000",
  "finaltime": "2024-02-20 04:31:29.064",
  "coupon_type": "整单级折扣券模板"
}

原始文件:


 —— 里面含json