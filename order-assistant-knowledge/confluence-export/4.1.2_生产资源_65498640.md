# 4.1.2 生产资源

# 1. 网关(ALL)

| 分类 | 网关 | 域名与URI规则 | 示例 |
| --- | --- | --- | --- |
| 一心助手网关*--一心助手所有业务服务* | 波塞冬（poseidon）--对应网关项目：api-gateway-assist | 线上环境：[api.hxyxt.com/poseidon](http://api.hxyxt.com/)/网关路由配置/接口path |  |
| 测试环境：[test-api.hxyxt.com/poseidon](http://test-api.hxyxt.com/)/网关路由配置/接口path |  |
| 开发环境：[dev-api.hxyxt.com/poseidon](http://dev-api.hxyxt.com/)/网关路由配置/接口path |  |
| **对外网关***--对外（pos、erp）等提供接口********* | 宙斯（**zeus**）*--对应网关项目：api-gateway* | **线上环境**：[api.hxyxt.com/](http://api.hxyxt.com/)**zeus**/网关路由配置/接口path |  |
| **测试环境：**[test-api.hxyxt.com/](http://test-api.hxyxt.com/)**zeus**/网关路由配置/接口path |  |
| **开发环境**：[dev-api.hxyxt.com/](http://dev-api.hxyxt.com/)**zeus**/网关路由配置/接口path |  |
| **对内心云服务网关***--对前端提供接口********* | 赫拉（**hera**）*--对应网关项目：*business-gateway | [线上环境：api.hxyxt.com/](http://api.hxyxt.com/)**hera**/网关路由配置/接口path 路由二: [merchants.hxyxt.com/businesses-gateway](http://merchants.hxyxt.com/businesses-gateway) |  |
| [测试环境：test-api.hxyxt.com/](http://test-api.hxyxt.com/)**hera**/网关路由配置/接口path 路由二: [test-merchants.hxyxt.com/businesses-gateway](http://merchants.hxyxt.com/businesses-gateway) |  |
| [开发环境：dev-api.hxyxt.com/](http://dev-api.hxyxt.com/)**hera**/网关路由配置/接口path 路由二: [dev-merchants.hxyxt.com/businesses-gateway](http://merchants.hxyxt.com/businesses-gateway) |  |
| **对大数据网关***--大数据埋点提供接口* | 阿瑞斯（**ares**）*--对应网关项目：bigdata-gateway* | **线上环境：**[api.hxyxt.com/](http://api.hxyxt.com/)**ares**/网关路由配置/接口path |  |
| **测试环境：**[test-api.hxyxt.com/](http://test-api.hxyxt.com/)**ares**/网关路由配置/接口path |  |
| **开发环境：**[dev-api.hxyxt.com/](http://dev-api.hxyxt.com/)**ares**/网关路由配置/接口path |  |
| **直接对外接口***--美团、饿了么等* | -- | [open-api.hxyxt.com/微服务名/接口path](http://open-api.hxyxt.com/%E5%BE%AE%E6%9C%8D%E5%8A%A1%E5%90%8D/%E6%8E%A5%E5%8F%A3path) |  |


详情见 网关集群划分&后端接口域名规范

# 2. 服务器

## 2.1. 基础服务

|  | Git项目名称 | 描述 | Git地址 | 运维部署使用的服务名称 | 实例数 | JVM |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | hydee-api-gateway | 宙斯（**zeus**），默认对外网关 | [https://yxtgit.hxyxt.com/basicservice/hydee-api-gateway](https://yxtgit.hxyxt.com/basicservice/hydee-api-gateway) | hydee-api-gateway |  |  |
| 2 | hydee-api-gateway | 雅典娜（**athena**） **海典POS相关对接** |  | api-gateway-athena |  |  |
| 3 | hydee-api-gateway | 哈迪斯（**hades**） 科传POS相关对接 |  | api-gateway-hades |  |  |
| 4 | businesses-gateway |  | [https://yxtgit.hxyxt.com/basicservice/businesses-gateway](https://yxtgit.hxyxt.com/basicservice/businesses-gateway) | middle-businesses-gateway |  |  |
| 5 | middle-id | 分布式ID统一分布式唯一id生成，应用于订单号、分库分表唯一id、唯一编码等 | [https://yxtgit.hxyxt.com/basicservice/middle-id](https://yxtgit.hxyxt.com/basicservice/middle-id) | middle-middle-id |  |  |
| 6 | xxl-job | 定时任务xxl-job定时任务平台，搜集部分问题需要二开 | docker容器发布 | xxl-job-admin-new |  |  |


详情见: 

## 2.2. 业务服务

|  | Git项目名称 | 描述 | 负责 | Git地址 | 运维部署使用的服务名称 | 实例数 | JVM |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | yxt-third-platform | 接口中台,对接三方平台 |  | [https://yxtgit.hxyxt.com/the3platform/yxtthirdplatform](https://yxtgit.hxyxt.com/the3platform/yxtthirdplatform) | third-platform-order-mt third-platform-order-tm third-platform-logistics third-platform-order-elm third-platform-order-pdd third-platform-order-jddj third-platform-callback-mt third-platform-callback-tm third-platform-order-other third-platform-callback-elm third-platform-callback-pdd third-platform-callback-jddj third-platform-callback-other third-platform-callback-rider |  |  |
| 2 | yxt-third-platform-gateway | 接口中台,对接三方平台路由器 |  | [https://yxtgit.hxyxt.com/the3platform/yxt-third-platform-gateway](https://yxtgit.hxyxt.com/the3platform/yxt-third-platform-gateway) | third-platform-gateway |  |  |
| 3 | order-account-atom-service | 重构后-订单中台-下账原子服务 |  | [https://yxtgit.hxyxt.com/order/order-account-atom-service](https://yxtgit.hxyxt.com/order/order-account-atom-service) | order-account-atom-service |  |  |
| 4 | order-after-sale-atom-service | 重构后-订单中台-售后原子服务 |  | [https://yxtgit.hxyxt.com/order/order-after-sale-atom-service](https://yxtgit.hxyxt.com/order/order-after-sale-atom-service) | order-after-sale-atom-service |  |  |
| 5 | order-atom-service | 重构后-订单中台-原子服务 |  | [https://yxtgit.hxyxt.com/order/order-atom-service](https://yxtgit.hxyxt.com/order/order-atom-service) | order-atom-service |  |  |
| 6 | order-delivery-atom-service | 重构后-订单中台-发货原子服务 |  | [https://yxtgit.hxyxt.com/order/order-delivery-atom-service](https://yxtgit.hxyxt.com/order/order-delivery-atom-service) | order-delivery-atom-service |  |  |
| 7 | order-batch-processing | 重构后-订单中台-订单批处理任务 |  | [https://yxtgit.hxyxt.com/order/order-batch-processing](https://yxtgit.hxyxt.com/order/order-batch-processing) | order-batch-processing |  |  |
| 8 | order-service | 重构后-订单中台-核心服务 |  | [https://yxtgit.hxyxt.com/order/order-service](https://yxtgit.hxyxt.com/order/order-service) | order-service |  |  |
| 9 | order-sync | 重构后-订单中台-异步服务 |  | [https://yxtgit.hxyxt.com/order/order-sync](https://yxtgit.hxyxt.com/order/order-sync) | order-sync |  |  |
| 10 | order-consistent-center | 订单中台-订单监控服务 |  | [https://yxtgit.hxyxt.com/order/order-consistent-center](https://yxtgit.hxyxt.com/order/order-consistent-center) | order-consistent-center |  |  |
| 11 | order-config-center | 订单中台-配置中心 |  | [https://yxtgit.hxyxt.com/order/order-config-center](https://yxtgit.hxyxt.com/order/order-config-center) | order-config-center |  |  |
| 12 | order-reconciliation | 订单中台-公域对账 |  | [https://yxtgit.hxyxt.com/order/order-reconciliation](https://yxtgit.hxyxt.com/order/order-reconciliation) | order-reconciliation |  |  |
| 13 | order-split-server | 订单中台-订单拆合单服务 |  | [https://yxtgit.hxyxt.com/order/order-split-server](https://yxtgit.hxyxt.com/order/order-split-server) | order-split-server |  |  |
| 14 | purchase-order-center | 采购中台 |  | [https://yxtgit.hxyxt.com/order/purchase-order-center](https://yxtgit.hxyxt.com/order/purchase-order-center) | purchase-order-center |  |  |
| 15 | yxt-trade-center | 交易中台 |  | [https://yxtgit.hxyxt.com/order/trade-service](https://yxtgit.hxyxt.com/order/trade-service) | yxt-trade-center |  |  |
| 16 | yxt-payment | 支付中台 |  | [https://yxtgit.hxyxt.com/order/yxt-payment](https://yxtgit.hxyxt.com/order/yxt-payment) | yxt-payment |  |  |
| 17 | logistics-center | 物流中台 |  | [https://yxtgit.hxyxt.com/order/logistics-center](https://yxtgit.hxyxt.com/order/logistics-center) | logistics-center |  |  |
| 18 | customer-service-center | 客服中台 |  | [https://yxtgit.hxyxt.com/order/customer-service-center](https://yxtgit.hxyxt.com/order/customer-service-center) | customer-service-center |  |  |
| 19 | evaluation-center | 评价中台 |  | [https://yxtgit.hxyxt.com/order/evaluation-center](https://yxtgit.hxyxt.com/order/evaluation-center) | evaluation-center |  |  |
| 20 | hydee-third-inside | 物流中台-调用三方物流平台接口 |  | [https://yxtgit.hxyxt.com/order/hydee-third-inside](https://yxtgit.hxyxt.com/order/hydee-third-inside) | hydee-third-inside |  |  |
| 21 | hydeeprintserver | 打印播报中台 .NET打印助手 |  | [https://yxtgit.hxyxt.com/order/hydeeprintserver](https://yxtgit.hxyxt.com/order/hydeeprintserver) |  |  |  |


## 2.3. 研发脚手架

|  | Git项目名称 | 描述 | Git地址 |
| --- | --- | --- | --- |
| 1 | order-framework | 接口中台,对接三方平台 | [https://yxtgit.hxyxt.com/order/order-framework](https://yxtgit.hxyxt.com/order/order-framework) |
| 2 | hydee-oms-tool | 研发脚手架 1.调用三方物流平台. 中通/圆通/邮政/极兔 2.JAVA调用.net的sdk | [https://yxtgit.hxyxt.com/order/hydee-oms-tool](https://yxtgit.hxyxt.com/order/hydee-oms-tool) |
| 3 | assignment-engine | 异步任务工具包 | [https://yxtgit.hxyxt.com/order/assignment-engine](https://yxtgit.hxyxt.com/order/assignment-engine) |


# 3. MySQL

**备注：交易生产·业务项目使用SK-Pro-核心业务数据库-004**，**Apollo配置文件对MySQL密码进行加密处理。**

|  | Git项目名称 | 描述 | DB名称 | 账号名称 |
| --- | --- | --- | --- | --- |
| 1 | yxt-third-platform | 接口中台,对接三方平台 |  |  |
| 2 | yxt-third-platform-gateway | 接口中台,对接三方平台路由器 | dscloud_offline |  |
| 3 | order-account-atom-service | 重构后-订单中台-下账原子服务 | order_account |  |
| 4 | order-after-sale-atom-service | 重构后-订单中台-售后原子服务 | order_after_sale |  |
| 5 | order-atom-service | 重构后-订单中台-原子服务 | order_delivery |  |
| 6 | order-delivery-atom-service | 重构后-订单中台-发货原子服务 | order_config_center |  |
| 7 | order-batch-processing | 重构后-订单中台-订单批处理任务 | yxt_trade_center |  |
| 8 | order-service | 重构后-订单中台-核心服务 | logistics_center |  |
| 9 | order-sync | 重构后-订单中台-异步服务 |  |  |
| 10 | order-consistent-center | 订单中台-订单监控服务 |  |  |
| 11 | order-config-center | 订单中台-配置中心 |  |  |
| 12 | order-reconciliation | 订单中台-公域对账 |  |  |
| 13 | order-split-server | 订单中台-订单拆合单服务 |  |  |
| 14 | purchase-order-center | 采购中台 |  |  |
| 15 | yxt-trade-center | 交易中台 |  |  |
| 16 | yxt-payment | 支付中台 |  |  |
| 17 | logistics-center | 物流中台 |  |  |
| 18 | customer-service-center | 客服中台 |  |  |
| 19 | evaluation-center | 评价中台 |  |  |
| 20 | hydee-third-inside | 物流中台-调用三方物流平台接口 |  |  |
| 21 | hydeeprintserver | 打印播报中台 .NET打印助手 |  |  |


# 4. ES索引

**备注：业务集群仅一个**

|  | 项目 | Es索引名称 | Es索引描述 | 说明 | 预发环境索引 |
| --- | --- | --- | --- | --- | --- |
| 1 | hydee-business-order-web | oms_b2c_platform_order_index_pro | B2C平台订单 |  |  |
| 2 | hydee-business-order | oms_orderinfo_basedata | O2O业务 | OMS-O2O列表查询 |  |
| 3 | order-atom-service | pro_es_new_b2c_refund_account_info | B2C退单下账单 |  | pre_es_new_b2c_refund_account_info |
| 4 | pro_es_new_b2c_account_info_s1 | B2C下账单 |  | pre_es_new_b2c_account_info无对应的pre_es_new_b2c_account_info_s1 |
| 5 | pro_es_order_chronic_disease | 慢病 | 线上线下的正单退单在一起 | pre_es_order_chronic_disease |
| 6 | pro_es_member_order | 会员消费记录-正单 | 含线上线下订单 按照会员分片,与会员相关的可以使用该索引 | pre_es_member_order |
| 7 | pro_es_member_refund_order | 会员消费记录-退单 | pre_es_member_refund_order |
| 8 | pro_es_offline_order_manage | 线下订单管理后台-正单 | 只有线下订单 | pre_es_offline_order_manage |
| 9 | pro_es_offline_refund_order_manage | 线下订单管理后台-退单 | pre_es_offline_refund_order_manage |
| 10 | pro_es_oms_order_info | B2C查询索引 | B2C查询 | pre_es_oms_order_info |
| 11 | pro_es_order_shard_by_org | 一心助手门店查询-正单 | 一心助手使用 按照门店分片,与门店相关的可以使用该索引 | pre_es_order_shard_by_org |
| 12 | pro_es_refund_shard_by_org | 一心助手门店查询-退单 | pre_es_refund_shard_by_org |
| 13 | pro_es_order_world_order | 订单新模型-正单 | 目前只有B2B在使用 | pre_es_order_world_order |
| 14 | pro_es_order_world_refund_order | 订单新模型-退款单 | pre_es_order_world_refund_order |
| 15 | pro_es_order_world_biz_log | 订单新模型-业务日志 | pr_es_order_world_biz_log |
| 16 | order-after-sale-atom-service | pro_es_order_world_after_sale_order | 订单新模型-售后单 | pre_es_order_world_after_sale_order |
| 17 | order-delivery-atom-service | pro_es_order_world_delivery_order | 订单新模型-发货单 | pre_es_order_world_delivery_order |
| 18 | pro_es_order_world_return_order | 订单新模型-退货单 | pre_es_order_world_return_order |


****

es_new_b2c_account_info
5. Redis

**备注：交易生产项目现阶段主要使用Redis02。**

|  | Git项目名称 | 描述 | Redis分片 | Redis生产资源·现状 |
| --- | --- | --- | --- | --- |
| 1 | yxt-third-platform | 接口中台,对接三方平台 | Redis02 |  |
| 2 | yxt-third-platform-gateway | 接口中台,对接三方平台路由器 | Redis02 |  |
| 3 | order-account-atom-service | 重构后-订单中台-下账原子服务 | Redis02 |  |
| 4 | order-after-sale-atom-service | 重构后-订单中台-售后原子服务 | Redis02 |  |
| 5 | order-atom-service | 重构后-订单中台-原子服务 | Redis02 |  |
| 6 | order-delivery-atom-service | 重构后-订单中台-发货原子服务 | Redis02 |  |
| 7 | order-batch-processing | 重构后-订单中台-订单批处理任务 | Redis02 |  |
| 8 | order-service | 重构后-订单中台-核心服务 | Redis02 |  |
| 9 | order-sync | 重构后-订单中台-异步服务 | Redis02 |  |
| 10 | order-consistent-center | 订单中台-订单监控服务 | Redis02 |  |
| 11 | order-config-center | 订单中台-配置中心 | Redis02 |  |
| 12 | order-reconciliation | 订单中台-公域对账 | Redis02 |  |
| 13 | order-split-server | 订单中台-订单拆合单服务 | Redis02 |  |
| 14 | purchase-order-center | 采购中台 | Redis02 |  |
| 15 | yxt-trade-center | 交易中台 | Redis02 |  |
| 16 | yxt-payment | 支付中台 | Redis02 |  |
| 17 | logistics-center | 物流中台 | Redis02 |  |
| 18 | customer-service-center | 客服中台 | Redis02 |  |
| 19 | evaluation-center | 评价中台 | Redis02 |  |
| 20 | hydee-third-inside | 物流中台-调用三方物流平台接口 | Redis02 |  |
| 21 | hydeeprintserver | 打印播报中台 .NET打印助手 | Redis02 |  |


# 6. RocketMQ

## 6.1 业务服务

### yxt-third-platform(接口中台)

|  | Topic 名称 | Topic描述 | 消费组名称 | 消费组使用场景概述 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
|  |  |


### xxxx(xxx)

|  | Topic 名称 | Topic描述 | 消费组名称 | 消费组使用场景概述 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
|  |  |
|  |  |
| 2 |  |  |  |  |
|  |  |
| 3 |  |  |  |  |
|  |  |


# 7. Kafka

|  | Topic 名称 | Topic描述 | 消费组名称 | 消费组使用场景概述 |
| --- | --- | --- | --- | --- |
| 1 | 示例: TP_ASSIST_FORUM_BINLOG_EVENT | canal监听binlog同步kafka，topic | TP_ASSIST_FORUM_BINLOG_EVENT_SYNC_ES | binlog同步ES |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |


## 8. 文件存储（华为OBS）

备注：一心助手App项目，后端如下Git项目统一使用Bucket（pro-assist-sk），暂不做更详细的区分。

|  | 服务类型 | Git项目名称 | 描述 | Bucket 使用场景概述 | 磁盘占用现状（2024.12月） | 磁盘占用现状（2025.1月） |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | 业务服务 | business-order | 订单中台 | 处方单 |  |  |
| 2 | 业务服务 |  |  |  |  |  |
| 3 | 业务服务 |  |  |  |  |  |
| 4 | 业务服务 |  |  |  |  |  |
| 5 | 业务服务 |  |  |  |  |  |
| 6 | 业务服务 |  |  |  |  |  |


# 9. XXL-JOB

备注：端口统一用9999。

|  | 服务类型 | 执行器名称 | 执行器描述 | 任务管理（任务调度中心） |
| --- | --- | --- | --- | --- |
| 1 | **业务服务** | business-order | 订单中台 |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |