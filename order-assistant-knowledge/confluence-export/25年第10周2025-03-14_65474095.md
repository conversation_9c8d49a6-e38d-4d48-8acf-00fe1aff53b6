# 25年第10周2025-03-14

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | business-gateway timeout [url:/dscloud/2.0/ds/order/sync](http://url/dscloud/2.0/ds/order/sync) |  |  |  |
| 2 | business-gateway timeout [url:/dscloud/1.0/ds/zhuishuma/needRecord](http://url/dscloud/1.0/ds/zhuishuma/needRecord) |  |  | ```  ``` |
| 3 | business-gateway timeout [url:/dscloud/1.0/ds/order/detail/batch/stock](http://url/dscloud/1.0/ds/order/detail/batch/stock) |  |  |  |
| 4 | business-gateway timeout [url:/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001](http://url/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001) |  |  |  |
| 5 | business-gateway timeout [url:/dscloud/1.0/duty-cash](http://url/dscloud/1.0/duty-cash) |  |  |  |
| 6 | business-gateway timeout [url:/dscloud/1.0/ds/refund/RefundLedgerList](http://url/dscloud/1.0/ds/refund/RefundLedgerList) |  |  |  |
| 7 | business-gateway timeout [url:/dscloud/1.0/ds/order/upOrderBatchNo](http://url/dscloud/1.0/ds/order/upOrderBatchNo) |  |  |  |
| 8 | [url:/dscloud/1.0/ds/baseinfo/getAuthedDeliveryName/500001](http://url/dscloud/1.0/ds/baseinfo/getAuthedDeliveryName/500001) |  |  |  |
| 9 | business-gateway timeout [url:/b2c/1.0/order/setting/getPrescription](http://url/b2c/1.0/order/setting/getPrescription) |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 | B2B加盟商城 | **交易中台**:  技术方案评审：已完成。  一心助手对接：已完成  代码开发进度：已提测   **支付中台**:  技术方案评审：已完成。  一心助手对齐：已完成与一心助手对于D-ERP支付流程的对齐工作。  代码开发进度：100%。 下游对接进度：100%。 已提测   **订单中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，对接ing。  ERP/POS对接: 接口已经和开发核对了一次。现在进入开发阶段  order-service开发进度：90%。 order-atom开发进度：90% 联调进度： |  |  |
| 2 | 客服中台搭建 | 客服中台 挂起 |  |  |
| 3 | 订单监控-(一致性保障) | 订单一致性保障方案 3月7号上线 |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发**B2C拉单重构**技术建设** |  |
| 2 |  | **本周总工时：5d**1. 退单数据迁移,完成 1d 2. 异常数据处理 4d   1. 科传老版本创建时间非交易时间导致迁移重复   2. 订单交易时间刷数脚本开发，上线   3. 迁移重复数据清理脚步开发、上线   4. 无法处理的订单收集脚本开发，人工核对。处理中 3. 科传老版本创建时间非交易时间导致迁移重复 4. 订单交易时间刷数脚本开发，上线 5. 迁移重复数据清理脚步开发、上线 6. 无法处理的订单收集脚本开发，人工核对。处理中 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：5d**1. 拼多多bug修改 1d 2. 配合测试+bug修复 3. 发货单详情 周五处理 | **遗留问题**所有和erp交互接口都需要有主动拉取服务。这里看时间可能在2期处理**风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. B2B适配订单新模型的修改点 2. 配合测试+bug修复 | **遗留问题**1. 权限问题，之前的方案需要给所有加盟店增加仓库权限 **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 智能客服订单查询线下单 开发 联调 2. OSS迁移代码开发 文档编写 3. 0元单风控告警优化 4. 慢接口问题排查 5. 线上问题排查支持 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 6 |  | **本周总工时：5d****1.拼多多bug修改****2.配合海典H2医保订单的邮费、包装费下账验证** | **遗留问题**1.拼多多开发**风险问题** | **需求研发**1.拼多多开发**技术建设** |  |
| 7 |  | **本周总工时：5d**1. 交易中台提测支持  2.xxl-job新文档编写 + 推动所有团队xxljob 升级- 解决优雅发布问题3. 微商城购物车代码梳理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 平台对账技术方案编写与评审 2. log4j2替换logback（70%） 3. B2C列表查询Bug修复 4. B2C异常单处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 添加退款日志. 2. 统一优化配置文件 3. 添加主从查询 4. 余额退款添加类型强校验 5. 添加认领结果字段 6. 优化分页查询排序规则和索引 7. 退款单分页查询合单退款时只展示子单 8. 操作日志添加禁用功能 9. 支付中回调支持 10. 添加退款状态补偿，支付单过期关闭补偿，支付回调三方添加参数 11. 回调添加操作日志 12. 添加全流程测试用例 13. 重构extends扩展表 14. 增强批量保存操作功能 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |