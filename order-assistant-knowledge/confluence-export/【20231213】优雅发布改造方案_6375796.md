# 【20231213】优雅发布改造方案

## 一、 背景

### 1.1 业务背景

 k8s管理pod和nacos管理服务的心跳机制没有同步，导致在滚动发布时候流量打在了老的pod上。影响到了上层业务的正常运行。

### 1.2 痛点分析

### 1.3 系统现状

## 二、 需求分析

### 2.1 业务流程

### 2.2 需求功能点

****

## 三、 目标

### 3.1 本期目标

#### 3.1.1 业务目标

#### 3.1.2 技术目标

### 3.2 中长期目标

## 四、整体设计

### 4.1统一语言定义

业务、技术名词解释等

### 4.2 滚动发布流程图

**true构建流程falseautotoptrue11651**

### 4.3 滚动发布时序图

### 4.5 核心技术问题

## 五、 详细设计

### 1、 模块详细设计

本次需求基于原有流程上进行改造

| 项目 | 分支 | 合并请求 |
| --- | --- | --- |
| ydjia-merchant-customer | feature-grey-start |  |


### 2、 改造思路设计

1、修改grey-spring-boot-lib的stop方法，在pod执行shutdown之前通知nacos注销pod

2、在dev环境测试business-order服务是否有效（已完成通过）

3、在线上环境测试ydjia-merchant-customer服务。因为王庭发现ydjia-merchant-customer在滚动发布时候出现连接500的错。

4、如果ydjia-merchant-customer在线上测试通过，并且能稳定运行一段时间，再将hydee-spring-boot-starter更换grey-spring-boot-lib 包，让所有服务都替换成功。

### 3、 代码改动

**1.grey-spring-boot-lib改动点**

 pod在shutdown之前会调用SmoothService的stop方法，所以需要在stop里通知nacos下线指定的pod。

代码修改点： 

修改grey-spring-boot-lib的版本号，避免影响到其他服务。先拿ydjia-merchant-customer在线上稳定运行一段时间后再逐步配置到所有服务。

**2.ydjia-merchant-customer**的改动点

 修改pom文件，引入grey-spring-boot-lib 3.0.0-SNAPSHOT

#### 

#### 4、 安全设计

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

项目工时、分工等

| 模块 | 功能点 | 开发 | 工时 | 开始时间 | 结束时间 | 完成度 |
| --- | --- | --- | --- | --- | --- | --- |
| 订单数据迁移 | 出设计文档 | 崔建波 | 8 |  |  |  |
| 开发程序同步接口 | 崔建波 | 2 |  |  |  |
| 测试自调 | 崔建波 | 2 |  |  |  |


## 九、 上线方案