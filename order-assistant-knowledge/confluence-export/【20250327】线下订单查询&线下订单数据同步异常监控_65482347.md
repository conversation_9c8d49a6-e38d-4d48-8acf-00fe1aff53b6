# 【20250327】线下订单查询&线下订单数据同步异常监控

Green已上线

### 接口地址| 接口 | 接口地址 |
| --- | --- |
| 线下单正单列表 | [http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/offlineOrderListUsingPOST](http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/offlineOrderListUsingPOST) |
| 线下单退单列表 | [http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/offlineRefundOrderListUsingPOST](http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/offlineRefundOrderListUsingPOST) |
| 线下单正单详情 | [http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/detailUsingPOST](http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/detailUsingPOST) |
| 线下单退单详情 | [http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/refundDetailUsingPOST](http://order-service.svc.k8s.test.hxyxt.com/doc.html#/default/offline-order-controller/refundDetailUsingPOST) |
| 线下单正单明细导出 | [http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineOrderListDetailExportUsingPOST](http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineOrderListDetailExportUsingPOST) |
| 线下单正单导出 | [http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineOrderListExportUsingPOST](http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineOrderListExportUsingPOST) |
| 线下单退单明细导出 | [http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineRefundOrderListDetailExportUsingPOST](http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineRefundOrderListDetailExportUsingPOST) |
| 线下单退单导出 | [http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineRefundOrderListExportUsingPOST](http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/export-controller/offlineRefundOrderListExportUsingPOST) |
| 导出任务列表 | [http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/task-controller/getTaskListUsingPOST](http://order-batch-service.svc.k8s.test.hxyxt.com/doc.html#/default/task-controller/getTaskListUsingPOST) |
正单详情和退单详情字段解释见: 

### ES索引

正单ES: {环境}_es_offline_order_manage

退单ES:  {环境}_es_offline_refund_order_manage

### 需求:

V2.24 线下订单查询&线下订单数据同步异常监控

### JIRA:

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3684

### 项目:

order-service

order-atom-service

order-sync-service 主要是维护新的营销枚举类型、处理科传订单公司编码

order-batch-processing

- incubation-yxt-import-export 在order-batch-processing开发一心堂导入和导出通用组件


order-framework

- order-types
- order-permission


### 分支:

feature-offline-order-query

### SDK版本:

offlineOrderQuery-SNAPSHOT

### SDK:

# SDK
<dependency>
   <groupId>com.yxt.order.atom.sdk</groupId>
   <artifactId>order-atom-sdk</artifactId>
   <version>offlineOrderQuery-SNAPSHOT</version>
</dependency>

<dependency>
    <groupId>com.yxt.order.batch.sdk</groupId>
    <artifactId>order-batch-sdk</artifactId>
    <version>offlineOrderQuery-SNAPSHOT</version>
</dependency>

<dependency>
    <groupId>com.yxt.order.types</groupId>
    <artifactId>order-types</artifactId>
    <version>offlineOrderQuery-SNAPSHOT</version>
</dependency>

<dependency>
  <groupId>com.yxt.order.open.sdk</groupId>
  <artifactId>order-open-sdk</artifactId>
  <version>offlineOrderQuery-SNAPSHOT</version>
</dependency>

<dependency>
  <groupId>com.yxt.permission</groupId>
  <artifactId>order-permission</artifactId>
  <version>offlineOrderQuery-SNAPSHOT</version>
</dependency>

    <dependency>
      <groupId>com.yxt.order.open.message</groupId>
      <artifactId>order-open-message</artifactId>
      <version>offlineOrderQuery-SNAPSHOT</version>
    </dependency>

### 交互

truebatch-to-atomfalseautotoptrue4211

### 线下单监控方案设计

true线下订单监控告警falseautotoptrue7215

### Apollo配置:

order-atom-service

canal:
  offline-order-management: offline_order_management_topic_pro

hydee-businesses-gateway

        # 订单新模型 这个已经有了，做检查即可
        - id: order-service
          uri: lb://order-service
          predicates:
            - Path=/order-world/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess
        # 订单批处理服务 【新增的】
        - id: order-batch-service
          uri: lb://order-batch-service
          predicates:
            - Path=/order-batch/**
          filters:
            - StripPrefix=1
            - YxtLoginAccess

order-batch-porcessing

yml# oss
aliyun:
  oss:
    endpoint: oss-cn-chengdu.aliyuncs.com
    #多个bucket用逗号隔开，有多少个它就是注册多少ossFileService到spring 容器中去，
    #bean名字是bucket 名字的驼峰方式+"OssFileService"，例如oms-2018-dev=oms2018DevOssFileService"。
    bucket-name: sk-test-centermerchant
    grant-expire-time: 1800
    #限制上传文件大小，单位为M
    grant-max-content-length: 100
    access-key-id: LTAI5tQRo6iGTnyLZuYt3au7
    access-key-secret: ******************************
    #client有默认配置.
    client:
      max-connections: 1024
      socket-timeout: 50000
      connection-timeout: 50000
      connection-request-timeout: 3000
      idle-connection-time: 600000
      max-error-retry: 3
      support-cname: true
      sld-enabled: false
      protocol: https
      user-agent: aliyun-sdk-java

keChuanOfflineOrderCountMonitorTaskAlert: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5ed0c16b-5670-4ea9-b7b5-ea387a8b6a78

### DB表:

trueCREATE TABLE `import_export_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `task_no` varchar(50) DEFAULT NULL COMMENT '任务编码',
  `mer_code` varchar(50) NOT NULL DEFAULT '500001' COMMENT '商户编码',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `data_mapping_clazz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'TaskType对应的数据处理映射类',
  `param_json` text COMMENT '参数',
  `param_mapping_clazz` varchar(255) DEFAULT NULL COMMENT '参数对应的类',
  `state` varchar(20) NOT NULL COMMENT '任务状态 WAIT,ING,DONE,ERROR',
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '错误原因',
  `download_url` varchar(4096) DEFAULT NULL COMMENT '下载Url',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `version` bigint DEFAULT '0' COMMENT '版本号',
  `file_name` varchar(512) DEFAULT NULL COMMENT '文件名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_no` (`task_no`) USING BTREE,
  KEY `idx_created_time` (`created_time`),
  KEY `idx_state` (`task_type`,`state`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='导入导出任务'; 

# 添加索引
ALTER TABLE `dscloud`.`inner_store_dictionary` 
ADD INDEX `idx_pos_mode`(`pos_mode`) USING BTREE;

### canal配置:

直接把会员消费记录的copy一下改一下topic. 

会员消费记录instance名称 order_memberTransactionRecord_offline

下面配置是测试环境的

true#instance名: offline_order_management

#################################################
## mysql serverId , v1.0.26+ will autoGen
# canal.instance.mysql.slaveId=0

# enable gtid use true/false
canal.instance.gtidon=false

# position info
canal.instance.master.address=**********:3306
canal.instance.master.journal.name=
canal.instance.master.position=
canal.instance.master.timestamp=
canal.instance.master.gtid=

# rds oss binlog
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# table meta tsdb info
canal.instance.tsdb.enable=true
#canal.instance.tsdb.url=**************************************
#canal.instance.tsdb.dbUsername=canal
#canal.instance.tsdb.dbPassword=canal

#canal.instance.standby.address =
#canal.instance.standby.journal.name =
#canal.instance.standby.position =
#canal.instance.standby.timestamp =
#canal.instance.standby.gtid=

# username/password
canal.instance.dbUsername=canal
canal.instance.dbPassword=canal123
canal.instance.connectionCharset = UTF-8
# enable druid Decrypt database password
canal.instance.enableDruid=false
#canal.instance.pwdPublicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALK4BUxdDltRRE5/zXpVEVPUgunvscYFtEip3pmLlhrWpacX7y7GCMo2/JM6LeHmiiNdH1FWgGCpUfircSwlWKUCAwEAAQ==

# table regex https://github.com/alibaba/canal/wiki/AdminGuide
canal.instance.filter.regex=dscloud_offline\\.offline_order.*,dscloud_offline\\.offline_refund_order.*
# table black regex
canal.instance.filter.black.regex=
# table field filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.field=test1.t_product:id/subject/keywords,test2.t_company:id/name/contact/ch
# table field black filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.black.field=test1.t_product:subject/product_image,test2.t_company:id/name/contact/ch

# mq config
canal.mq.topic=offline_order_management_topic_test
# dynamic topic route by schema or table regex
#canal.mq.dynamicTopic=mytest1.user,mytest2\\..*,.*\\..*
#canal.mq.partition=0
# hash partition config
# canal.mq.partitionsNum=32
#canal.mq.partitionHash=test.table:id^name,.*\\..*
canal.mq.partitionHash=dscloud_offline\\.offline_order.*:order_no,dscloud_offline\\.offline_refund_order.*:refund_no
#################################################

### XXLJob配置

order-batch-processing 【订单】批处理服务

keChuanOfflineOrderCountMonitorTask

### 权限

### TODO:

  20 complete 开发  21 complete 接口定义   22 complete ES索引建立   26 complete 接口实现   38 complete 导出任务   27 complete 单量监控   28 complete 联调     50 complete 测试环境部署  51 complete order-types test-SNAPSHOT   52 complete order-service test   53 complete order-atom-service test   54 complete order-batch-service test     9 complete 同步master代码  10 complete order-service   11 complete order-atom-service     34 complete Apollo配置  35 complete pre   36 complete prod     12 complete 发布sdk  13 complete order-atom-sdk     14 complete 合并分支代码到master  15 complete order-service   16 complete order-atom-service     42 incomplete 创建生产索引  43 incomplete createOfflineOrderManageIndex   44 incomplete createOfflineRefundOrderManageIndex     30 incomplete 准备刷数脚本