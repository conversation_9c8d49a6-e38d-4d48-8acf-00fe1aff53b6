# 2024-07-18 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 商家部分退款 | ``` hydee-business-order ``` | 1 | ``` feature/ORDER-1540/part-refund ``` | 焦钰斌 | 徐国华 | 徐凯 |  |  |  |  |
| 部分退款接口 | ds-service-mtds-service-jddj | 2 | feature/ORDER-1540-orderapi | 杨俊峰 | 徐国华 | 徐凯 |  |  |  |  |
|  | cloud-ui |  | master | 谢元罡 |  |  |  |  |  |  |
| 拆零后下账金额小于等于0和组合拆零后金额为负数 | hydee-business-order-webhydee-business-order-b2c-third | 3 | ``` bugfix/order-2125 ``` | 蒋一良 | 徐国华 | 徐凯 |  |  |  |  |
| 毛利预警 | hydee-business-order-webhydee-business-order | 4 | ``` feature/order-1540/forewarning_set ``` | 蒋一良 | 徐国华 | 徐凯 |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| ``` CREATE TABLE `dscloud`.`ori_third_order_detail` (                                                     `id` bigint NOT NULL AUTO_INCREMENT,                                                     `num_iid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台商品编码',                                                     `outer_iid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'erpCode',                                                     `upc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '条形码',                                                     `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',                                                     `num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原订单数量',                                                     `oid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子订单编号-京东到家需要',                                                     `delivery_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '京东健康商家自配送用 配送方式有：1：商家配送，2：门店自提，3：订单支持商家自配，不自动呼叫运力',                                                     `third_detail_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方详情ID',                                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',                                                     `modify_time` datetime DEFAULT NULL COMMENT '更新时间',                                                     `order_no` bigint DEFAULT NULL COMMENT '订单号',                                                     `refund_count` int DEFAULT NULL COMMENT '退单数量',                                                     PRIMARY KEY (`id`),                                                     KEY `idx_order_no` (`order_no`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; ALTER TABLE `dscloud`.`refund_detail`     ADD COLUMN `platform_refund_count` int(0) DEFAULT NULL  COMMENT '平台退款数量' AFTER `detail_discount`; `````` ENGINE = InnoDB AUTO_INCREMENT = 1 `````` ENGINE = InnoDB AUTO_INCREMENT = 1  ``` |
| --- |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-business-order-web | application.yml | ``` wms:   white:     platformList: 27     storeList: A003,H812 ``` |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  | OcelotConfigs | RouteApiConfig | {  "ModuleId": "27",  "MethodName": "hems.cloud.order.get.detail",  "ApiPath": "api/Order/GetOrderDetail"  },  {  "ModuleId": "11",  "MethodName": "hems.cloud.order.get.detail",  "ApiPath": "api/Order/GetOrderDetail"  },  {  "ModuleId": "24",  "MethodName": "hems.cloud.order.get.detail",  "ApiPath": "api/Order/GetOrderDetail"  },  {  "ModuleId": "11",  "MethodName": "hems.cloud.order.applypartrefund",  "ApiPath": "api/Refund/OrderApplyPartRefund"  }, |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 complete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |