# 4.4.1 需求研发流程

# 目的

围绕“需求研发流程标准化执行”的愿景，进一步提升研发RD的需求研发流程规范化意识，控制因需求评审、技术方案设计、代码研发、自测、CodeReview、提测、上线等过程中的缺失行为给团队协作或需求平稳上线带来的风险问题，特制定本规定。本规定旨在通过对需求研发全生命周期的管理，避免因研发流程不规范导致地团队协作问题、线上生产事故等，提升团队业务需求成功交付率。

# 适用范围

“本规定”适用于应用研发组研发RD（其他团队可参考），团队内需求及技术专项均须参考规定流程推进迭代过程，部分节点不做强制约束（例：小于2pd需求不强制做技术方案设计）。

# 研发规范

（1）研发规范清单表格罗列了团队现阶段需要严格执行的规范，其他规范可参考文档目录：[后端研发部]研发规范、[应用研发组]技术规约、[应用研发组]技术沉淀等。

**（2）特别强调：**

- 需求评审+技术方案设计完成后，新增/变更需求、上线前临时变更需求，都需要通知 @丁鹏。
- 不允许个人私自接收、或直接研发未经评审需求。


**（2）代码自我审查三遍的逻辑：**

- 第一遍：检查工程结构、目录结构、代码结构；
- 第二遍：检查代码规范；
- 第三遍：检查代码逻辑（代码实现与产品设计是否相符），核心问题、风险问题处理策略等。


| 需求阶段 | 规范分类 | 规范 | 备注 |
| --- | --- | --- | --- |
| 研发阶段 | 方案类 | 技术方案设计参考： | 1、技术方案首先需要研发侧自评，评审通过后再组织产研测相关同学评审。2、技术方案评审完成后，需要发送会议纪要，参考如下：java技术方案评审纪要-范例truetrue【领导版-框架方案评审纪要】 技术方案：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38834977 产品PRD：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32328662 评审时间：8月1日 14：30-15：10  核心关注点： 1.主要业务功能：领导版应用框架配置，应用切换，常用工具管理，红点角标，个人中心消息，广告等。 2.影响功能：工具搜索，应用版本切换，应用获取，常用工具管理，应用组织校验。  待完善问题： 1.领导版常用工具是否需要按组织划分。    结论：需要按照组织划分，不同组织授权的工具可能不一样 @丁鹏 @张蕊  2.确认现在通用版提供的常用工具管理和工具/论坛搜索功能是否可复用，数据表是否需要调整。    结论：目前看暂无需要调整字段。 @李飞 3.领导版的消息在个人中心，需要展示数量，是否需要纳入角标，如果纳入角标需要消息服务支持 @徐赛虎 @张蕊 @李治来 @陈令  4.集团版没有心云对应的组织层级。如果将集团的layer配置设置为0是否影响现有业务。    结论：目前吴铭和陈令反馈无影响，研发过程中再次关注确认。 @陈令 @吴铭   本次迭代功能对应影响范围、业务复杂度、风险等均在可控范围内，将不组织更大范围的产研评审，PM/QA/前端同学可自行查看技术文档，如有问题再单独沟通。 |
| 结构类 |  |  |
| 研发类 | [应用研发组]DDD实战 |  |
|  |  |
|  |  |
| Swagger接口文档规约 |  |
|  |  |
|  |  |
| DAO层写法 |  |
|  |  |
| 编码规范 | 代码格式化规范（[配置IDEA代码风格注释模板](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6360482)）、sonar检查规范 |
| CodeReview | 参考如下规范： |
| 配置类 |  |  |
|  |  |
| 自测阶段 | 单测类 |  |  |
| 一心助手项目单测规约 |  |
| 测试阶段 | 流程类 | 提测清单模板 参考： **备注：在研发阶段需要建好提测清单CF文档，实时记录研发过程的配置及相关风险问题。** | 提测需要发邮件，参考： |
| 上线阶段 | 流程类 | 上线清单模板 | 上线前1-2天，组织上线评审会议 |
| 上线流程规范 **备注：上线前需要发送上线公告，参考右侧。** | java上线公告-范例truetrue【上线公告】一心助手在2024年8月8日发布迭代。迭代核心内容如下:  【新增功能】  1.资源权限配置  2.资源权限应用  3.一心助手App安装包能力建设（静默更新、灰度发版）  4.一心助手应用登录，应用版本选择，应用切换，组织切换，应用组织权限校验  5.通用版上线  6.资源增强校验   【优化功能】  1.优化冷链导出记录查询不存在问题  2.优化用户登录设备绑定增加防重校验，避免设备重复绑定   【技术专项】  1.APP版本治理，API统一规范  2.工资查询优化，限制查询时间，只能在12号之前查询上月工资，之后查询本月工资  3.角标3.0优化，接入资源权限，统一权限控制  4.任务迁移，将任务服务中台化，迁移前端接口到业务服务中  5.服务xframe升级 |
| 系统运维 | 运维类 | **备注：生产事故需要在产研群发周知，参考右侧。** | java生产环境问题周知-范例truetrue2024.02.29 10:05 一心助手App-价格市调，出现待办任务与市调任务状态不一致的现象，现分析如下：  问题背景： 研发于2024.02.29 10:05，收到产品反馈，计划3月1号生效的市调任务，在待办任务中显示为进行中的状态，用户操作触发“市调任务当前状态不是进行中，不可操作！”的告警。  产生原因： 1.用户操作海南公司的市调活动推送市调任务 2.市调任务生成后推送给任务中台生成待办任务时，待办任务立即开始了 3.市调任务状态为待生效，待办任务状态为进行中，消息已推送 4.从待办任务或消息中就可进入待生效的市调任务详情页面 5.从详情操作待提交的市调商品，提交商品价格时触发告警，数据无法提交  影响范围： 1.共生成484家门店（海南公司）的市调任务， 2.待办任务1条，涉及484家门店， 3.共推送3208条消息，即3208名用户（海南公司员工）收到消息 4.触发告警4条，操作用户3人，价格市调未产生异常数据。 目前价格市调功能处于公测阶段，影响范围较小。  解决方案： 1.止损 （1）将待办任务紧急结束 （2）加待办任务的开始时间置为与市调任务相同的时间 （3）消息中台根据待办任务删除该批次消息 2.修复 （1）任务中台修正代码，HOT-FIX。（发布时增加开始时间判断） （2）C端市调任务详情添加未生效的任务，详情列表中的商品卡片不可点击的逻辑 3.后期避免 （1）测试CASE覆盖到位 （2）研发自测走查，需涵盖尽可能多的情况 |
| 企微报警问题解决方案，首先在研发群响应问题、再分析具体原因、最后将分析结果发送研发群。参考如右侧。 | 参考截图：java企微报警问题响应-范例truetrue环境：pro 服务：assist-synthesis 实例：10.100.33.41 TraceId：4d6d710f78a94f81b7ecdd7763ef513b.2510.17235123017693439 [2024-08-13 09:25:02.180]-租户用户:[500001:4086467602258048602]-[com.yxt.lang.util.ExLogger67]-ERROR [http-nio-8080-exec-89] error#/c/expiryInspect/w/1.0/createSnapshot#http://10.100.33.41:8080/c/expiryInspect/w/1.0/createSnapshot#409#$$商品名称不能为空 com.yxt.lang.exception.YxtRuntimeException: 商品名称不能为空         at com.yxt.lang.util.Conditions.checkNotBlank(Conditions.java:256)         at com.yxt.lang.util.Conditions.checkNotBlank(Conditions.java:204)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.domain.model.SnapshotDetailValueObject.createSnapshotDetailValueObject(SnapshotDetailValueObject.java:144)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.service.impl.SnapshotApplicationServiceImpl.lambda$null$15(SnapshotApplicationServiceImpl.java:710)         omit...         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.service.impl.SnapshotApplicationServiceImpl.lambda$getSnapshotDetailValueObjectList$16(SnapshotApplicationServiceImpl.java:713)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.service.impl.SnapshotApplicationServiceImpl.getSnapshotDetailValueObjectList(SnapshotApplicationServiceImpl.java:714)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.service.impl.SnapshotApplicationServiceImpl.submitSnapshot(SnapshotApplicationServiceImpl.java:195)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.entrance.api.ConsoleSnapshotOperateApiImpl.sw$original$createSnapshot$0ah0812(ConsoleSnapshotOperateApiImpl.java:45)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.entrance.api.ConsoleSnapshotOperateApiImpl.sw$original$createSnapshot$0ah0812$accessor$sw$g2afqd1(ConsoleSnapshotOperateApiImpl.java)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.entrance.api.ConsoleSnapshotOperateApiImpl$sw$auxiliary$0tcjgg2.call(Unknown Source)         at com.yxt.assist.synthesis.server.expiryinspect.snapshot.entrance.api.ConsoleS  问题原因：袋鼠云平台把效期上游的其中一张表的依赖搞掉了，本来商品基本信息可能平时 2点钟跑完 但是因为依赖掉了,0点就跑完了数据，导致部分数据缺失 影响范围：用户提交效期快照失败，页面显示效期快照为未提交状态，影响用户数44 解决方案：当前大数据侧已重新跑数，效期数据已恢复，用户重新提交即可 |


# 需求研发流程

## 需求研发正向流程

备注：如下图3-1需求研发工作流程仅标注了正向研发流程，逆向流程请参考团队内其他规范。

true研发流程false1200autotoptrue14411

# 附录

## 技术方案设计

### 技术方案模板

### 技术方案评审说明

【旧】会议痛点：技术方案/上线清单评审需要更深层次、更细粒度的剖析技术设计原理、技术实施细节、容错降级方案等，但大部分非技术研发同学并不关心这些；导致技术方案评审时比较尴尬，技术同学不知道应该讲解到什么程度，比较担心浪费非技术同学时间。

【新】会议策略：研发同学会在内部小范围评审技术方案/上线清单（评审后发会议纪要，细小的待确认项找相关同学线下沟通）。若在内部评审会议中发现需求存在风险较大、需要更多的同学参与决策/讨论，再单独拉更大范围的评审会议。

核心目的：减少大家开非必要会议的时间。中小型需求研发侧自己内部做技术方案/上线清单评审，大型需求（高风险需求）再单独拉更大范围的评审会议。

## Git分支管理流程图

trueGIT分支管理流程图falseautotoptrue11621