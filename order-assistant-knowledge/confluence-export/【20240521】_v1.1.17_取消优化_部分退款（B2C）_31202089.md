# 【20240521】 v1.1.17 取消优化/部分退款（B2C）

## 一、 背景

### 1.1 需求修改功能点

****

| 功能 | 功能描述 |
| --- | --- |
| 部分退款流程优化 | 优化B2C部分退款流程， 抛弃原有部分退款只修改商品明细流程，改为部分退款后废弃原有订单，重新生成新的订单进行B2C流程作业 |
| 下账优化 | 抛弃原下账流程，新增下账单概念，下账操作从原使用订单相关信息计算的方式进行下账改为生成下账单，下账操作以下账单为主 |
| 店铺管理 | B2C店铺新增OMS作业的支付方式和WMS作业时的店铺类型和编码；增加配置，自动打印发货单，自动获取面单，自动发货，增加自动默认批号； 将原有apollo配置及硬编码方式的配置迁移至数据库进行灵活配置 |
| B2C订单详情 | 优化订单详情 |
| 多快递单号 | 增加多快递单号管理 |
| BUG修复 | 云仓订单权限问题，门店账号非云仓权限，目前可看到云仓订单 |
| 补单操作 | 补单失败 |
| 订单路由 | 邮费最低，增加与原单配送费对比，转单邮费更高，则不做转单； |


疑问： 1. B2C门店仓下账如果组织机构按照仓库下账，店铺仓库设置为多仓库怎么处理？ 或者限定只能配置一个仓库？

 多仓库默认按仓库优先级取第一个

 2. 科传下账通过拉取，拉取下账列表和推送下账成功有时间差，这段内时间内出现仅退款，怎么判断？

 按照下账状态判断会问题，通过生成正单下账单判断， 只要正单已发货，就生成正单的下账单，如果正下账单未下账发生退款，不管是部分退款还是全额退款，都生成对应的退款下账单去下账

 3. 部分退款后，原系统单状态改为已取消？

 要看原系统单是否已发货，未发货修改为已取消，并生成新的正单；已发货生成下账单下账，部分退款再生成部分退款下账单去冲抵

### 1.2 痛点分析

1. B2C订单退款流程操作繁琐，不利于店员操作
2. 店铺管理配置信息不足，需要新增一些对于不同门店可适配的配置
3. 随着业务的迭代，B2C页面显示的数据需要有相应的调整，来提高店员的体验感


## 二、 需求分析

### 2.1 业务流程

[部分退款流程 (axshare.com)](https://3a66mj.axshare.com/?id=mk6dty&p=%E9%83%A8%E5%88%86%E9%80%80%E6%AC%BE%E6%B5%81%E7%A8%8B&g=1)

## 三、 目标

### 3.1 本期目标

实现业务相关功能，保证系统稳定运行

## 四、整体设计

### 4.1流程图

B2C退款业务现有流程图：

trueB2C退款流程falseautotoptrue9218

B2C退款业务调整后流程图：

trueB2c退款下账流程falseautotoptrue18414

退款时序图

退款时序图INLINE```plantuml
@startuml
平台--> O2OToB2COrderTemplateService:平台传递报文
O2OToB2COrderTemplateService--> O2OToB2COrderTemplateService: doRefundOrderParse()进入各个平台子类转换退款msg报文为refundOrderDTO
O2OToB2COrderTemplateService--> doRefundOrderParse: 美团根据mq的ServiceType字段来确认仅退款和退货退款
doRefundOrderParse--> doRefundOrderParse: 美团：mq的ServiceType=2 ? 1退货退款 : 0仅退款；邮费取邮费的order_pay_info表的BuyerActualAmount
doRefundOrderParse--> doRefundOrderParse: 饿百：退款类型判断正单是否已发货和已完成，是为退货退款，否则为仅退款；邮费取mq的freightamount字段
doRefundOrderParse--> doRefundOrderParse: 京东到家：mq的ExtraInfo的applyDeal = 40 ? 退货退款 : 仅退款；邮费取mq的freightamount字段
doRefundOrderParse--> doRefundOrderParse: 微商城：同理饿百，除了运费在mq中多了一个isreturnfreight是否退款运费字段，是才算运费
O2OToB2COrderTemplateService--> platformRefundOrderService: filterAndSaveOrUpdateByRefundOrderList()保存平台退款单信息以及相关扩展信息并记录ES日志
O2OToB2COrderTemplateService--> b2CRefundHandleChain: createAfterSaleChain()处理退款单核心业务逻辑
b2CRefundHandleChain--> b2CRefundHandleChain: dealPlatformRefundToSysRefund()平台退款单转换为系统退款单
b2CRefundHandleChain--> b2CRefundHandleChain: 判断是否有platformOrderInfo和omsOrderInfo，没有就直接返回
b2CRefundHandleChain--> b2CRefundHandleChain: 京东取消订单无明细，手工组退单明细
b2CRefundHandleChain--> getIncludePostFeeRefundDetails: 获取正常退款单，邮费退款单(邮费单value为空)Map
b2CRefundHandleChain--> b2CRefundHandleChain: 校验商户时候开启了自动处理售后
b2CRefundHandleChain--> b2CRefundHandleChain: for循环创建售后单和退单相关信息
b2CRefundHandleChain--> getAfterSaleOrderContext: 正式创建售后单和退单信息
getAfterSaleOrderContext--> omsRefundProcessor: 创建退款单，存入到上下文
omsRefundProcessor--> omsRefundProcessor: 退款类型为取消，则自动同意退款
getAfterSaleOrderContext--> afterSaleProcessor: 创建售后单，存入到上下文
afterSaleProcessor--> afterSaleProcessor: 校验商户时候开启了自动处理售后
afterSaleProcessor--> afterSaleProcessor: 系统退款单类型不为空，则售后单退款类型设置为退款单的退款类型
afterSaleProcessor--> afterSaleProcessor: 仅退款或者取消订单：退款mq消息为退款成功且正单已发货且已下账时创建
afterSaleProcessor--> afterSaleProcessor: 退货退款：退款mq消息为退款成功或者待卖家确认收货且正单已下账时创建
afterSaleProcessor--> afterSaleOrderStrategy: 真实退单明细是空的，则创建邮费售后单，否则创建商品售后单
afterSaleOrderStrategy--> onlyRefundMoneyConcreteStrategy: 取消单和仅退款单走仅退款策略
onlyRefundMoneyConcreteStrategy--> onlyRefundMoneyConcreteStrategy: 创建仅退款售后单并生成下账信息（有售后单就进行更新）
afterSaleOrderStrategy--> refundGoodsAndMoneyConcreteStrategy: 退货退款单走退货退款策略
refundGoodsAndMoneyConcreteStrategy--> refundGoodsAndMoneyConcreteStrategy: 创建退货退款退款售后单与退货单并生成下账信息（有售后单就进行更新，邮费单售后类型为仅退款)
getAfterSaleOrderContext--> omsRefundDetailProcessor: 创建退单明细
omsRefundDetailProcessor--> omsRefundDetailProcessor: 邮费单则跳过，商品单则创建（组合商品会拆分）
getAfterSaleOrderContext--> OmsOrderProcessor: 创建oms_order_es异常信息
@enduml
```

店员操作退款数据流转过程

INLINE```plantuml
@startuml
操作者--> mq:用户发起退货退款
mq--> mq: 1：等待处理中 --> 10：等待商家同意
mq--> b2cRefundHandleChain: 走售后链路(不创建售后单)
操作者--> AbstractRefundConfirmTemplateService: 店员拒绝退货退款 confirmStatus：0
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为30卖家拒绝退款
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（不创建售后单）
mq--> mq: 3：拒绝退款申请 --> 30：卖家拒绝退款
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（不创建售后单）
操作者--> AbstractRefundConfirmTemplateService: 店员同意退货退款 confirmStatus：1
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为13等待买家退货
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路
操作者--> mq: 用户填写快递，推送发货信息
mq--> mq: 7：待退货（售后单）--> 16：待定卖家确认收货
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（正单已下账且退款单为待卖家确认收货或者已完成创建售后单与退货单）
操作者--> AbstractRefundConfirmTemplateService: 店员拒绝收货 confirmStatus：2
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为30卖家拒绝退款
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（更新售后单）
操作者--> AbstractRefundConfirmTemplateService:确认收货 confirmStatus：3
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为20已完成
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（更新售后单）

操作者--> mq:用户发起仅退款
mq--> mq: 1：等待处理中 --> 10：等待商家同意
mq--> b2cRefundHandleChain: 走售后链路(不创建售后单)
操作者--> AbstractRefundConfirmTemplateService: 店员拒绝退款 confirmStatus：0
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路
mq--> mq: 3：拒绝退款申请 --> 30：卖家拒绝退款
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为30卖家拒绝退款
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路
操作者--> AbstractRefundConfirmTemplateService: 店员同意退款 confirmStatus：1
AbstractRefundConfirmTemplateService--> AbstractRefundConfirmTemplateService: 平台退款单更新status为20已完成
AbstractRefundConfirmTemplateService--> b2cRefundHandleChain: 重新走一次售后链路（正单已发货或者已完成且已下账，退款单也已完成才创建售后单）
@enduml
```

门店仓退款流程图

WMS发货退款流程图

## 五、 详细设计

### 1、 模块详细设计

分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比

a.部分仅退款且未发货时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 同意仅退款
buinessOrderWeb--> 三方平台: 更新退款完成状态
buinessOrderWeb--->buinessOrderWeb: 判断是否全部退款
buinessOrderWeb--->buinessOrderWeb: 全部退款更新销售单为已取消
buinessOrderWeb-> 店员:已全部退款完成
buinessOrderWeb--->buinessOrderWeb: 未全部退款，原订单置为无效,重新生成待审核订单
buinessOrderWeb-> 店员: 部分退款完成
店员-> buinessOrderWeb: 审核新订单
buinessOrderWeb--->buinessOrderWeb:审核通过
buinessOrderWeb-> 店员:审核已通过
店员-> buinessOrderWeb:打印面单
buinessOrderWeb-> 店员:打印面单完成
店员-> buinessOrderWeb:拣货复核/发货
buinessOrderWeb--->buinessOrderWeb:生成销售下账单
buinessOrderWeb-->pos: 推送销售下账单下账
pos-->buinessOrderWeb:返回下账结果
buinessOrderWeb--->buinessOrderWeb:更新下账结果
@enduml
```

b.部分仅退款且已发货时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 同意仅退款
buinessOrderWeb--> 三方平台: 更新退款完成状态
buinessOrderWeb--->buinessOrderWeb: 生成退款下账单
buinessOrderWeb-> 店员:退款完成
buinessOrderWeb-->pos: 推送退款下账单下账
pos-->buinessOrderWeb:返回下账结果
buinessOrderWeb--->buinessOrderWeb:更新下账结果
@enduml
```

c.全额退款且已发货时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 同意仅退款
buinessOrderWeb--> 三方平台: 更新退款完成状态
buinessOrderWeb--->buinessOrderWeb: 生成退款下账单
buinessOrderWeb-> 店员:退款完成
buinessOrderWeb-->pos: 推送退款下账单下账
pos-->buinessOrderWeb:返回下账结果
buinessOrderWeb--->buinessOrderWeb:更新下账结果
@enduml
```

c.全额退款且未发货时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 同意仅退款
buinessOrderWeb--> 三方平台: 更新退款完成状态
buinessOrderWeb-->buinessOrderWeb:更新销售单已取消
buinessOrderWeb-> 店员:退款完成
@enduml
```

d.退货退款时许图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 同意退货退款
buinessOrderWeb--> 三方平台: 更新为已同意退款
店员-> buinessOrderWeb: 同意收货
buinessOrderWeb-->buinessOrderWeb:生成收货单
三方平台--->buinessOrderWeb:推送收货物流信息
buinessOrderWeb-->buinessOrderWeb:退货完成
buinessOrderWeb-->buinessOrderWeb:生成退货退款售后单
buinessOrderWeb-->buinessOrderWeb:生成退款退款下账单
buinessOrderWeb-> 店员:退款完成
店员-> buinessOrderWeb: 拒绝收货
buinessOrderWeb-->buinessOrderWeb:生成仅退售后单
buinessOrderWeb-->buinessOrderWeb:生成仅退款下账单
buinessOrderWeb-> 店员:退款完成
buinessOrderWeb-->pos: 推送退款下账单下账
pos-->buinessOrderWeb:返回下账结果
buinessOrderWeb--->buinessOrderWeb:更新下账结果
@enduml
```

e.B2C科传销售单下账时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 发货
buinessOrderWeb-> 店员: 发货成功
buinessOrderWeb--> buinessOrderWeb:生成下账单
科传POS---> buinessOrder: 拉取待下账列表
buinessOrder--> 科传POS: 返回待下账列表
科传POS---> buinessOrder: 拉取订单下账明细
buinessOrder--> 科传POS: 返回订单下账明细
科传POS---> buinessOrder: 推送下账结果
buinessOrder --> buinessOrder: 修改下账状态
@enduml
```

f.B2C科传退款单下账时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 退款审核
buinessOrderWeb-> 店员: 审核通过
buinessOrderWeb--> buinessOrderWeb:销售单已发货生成退款下账单
科传POS---> buinessOrder: 拉取待下账列表
buinessOrder--> 科传POS: 返回待下账列表
科传POS---> buinessOrder: 拉取订单下账明细
buinessOrder--> 科传POS: 返回订单下账明细
科传POS---> buinessOrder: 推送下账结果
buinessOrder --> buinessOrder: 修改下账状态
@endu

g.海典B2C销售单下账时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 发货
buinessOrderWeb-> 店员: 发货成功
buinessOrderWeb--> buinessOrderWeb:生成下账单
buinessOrderWeb---> 海典POS: 推送下帐单
海典POS --> buinessOrderWeb: 返回下账结果
buinessOrderWeb --> buinessOrderWeb: 修改下账结果
@enduml
```

i.拣货复核默认批号时序图

INLINE```plantuml 
@startuml
店员-> buinessOrderWeb: 拣货复核
buinessOrderWeb--> buinessOrderWeb:判断是否开启默认批号
buinessOrderWeb--> 商品中台: 批量查询商品批号
buinessOrderWeb--> 店员: 返回默认批号
店员-> buinessOrderWeb: 完成拣货
buinessOrderWeb--> buinessOrderWeb: 拣货信息落库
@enduml
```

### 2、 存储数据库设计

| 表名 | sql |
| --- | --- |
| 销售单下账表 | sqlCREATE TABLE `account_order`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `third_order_no` varchar(100) NOT NULL COMMENT '三方平台订单号',   `order_no` BIGINT(20) NOT NUll COMEMENT '系统订单号',   `service_mode` VARCHAR(10) NOT NUll DEFAULT 'O2O' COMEMENT 'O2O / B2C',   `pos_mode` VARCHAR(10) NOT NULL DEFAUTL 'HD_H1' COMMENT 'HD_H1-海典H1  HD_H2-海典H2  KC-科传',   `pick_type` VARCHAR(10) NOT NULL DEFAULT 'OMS' COMENT 'OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS',   `third_plat_code` VARCHAR(10) NOT NULL COMMENT'三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康',   `organization_code` varchar(20) NOT NULL COMMENT '下账机构编码',   `org_parent_path` varchar(128) NOT NULL COMMENT '组织机构父路径id链路 1000-1100-1110-',   `buyer_actual_amount` DECIMAL(16,4) COMMENT '买家实付金额',   `merchantActualReceive` DECIMAL(16,4) COMMENT '商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠+平台收取佣金', 	`goods_total_amount` DECIMAL(16,4) COMMENT '商品总额 = 商品明细的商品金额汇总', 	`delivery_fee` DECIMAL(16,4) COMMENT '商家配送费，金额大于等于0', 	`package_fee` DECIMAL(16,4) COMMENT '订单总额 = 商品明细的商品金额汇总+商家配送+平台配送费+商家包装费+平台包装费', 	`merchant_discount` DECIMAL(16,4) COMMENT '商家优惠金额', 	`state` VARCHAR(10) DEFAULT 'WAIT' COMMENT '下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败', 	`account_time` datetime(0) NULL COMMENT '下账时间', 	`sale_no` varchar(255) COMMENT 'erp零售流水号 下账成功返回', 	`account_err_msg` varchar(1024) COMMENT '下账失败原因 下账失败返回', 	`create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间', `deleted`  bigint  NOT NULL DEFAULT 1  COMMENT '是否删除 0-未删除 时间戳-已删除',    `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',    PRIMARY KEY (`id`) USING BTREE,   INDEX `third_order_no_plat_index`(`third_order_no`, `third_plat_code`) USING BTREE COMMENT '三方单号+平台类型',   INDEX `order_no_index`(`order_no`) USING UNIQUE COMMENT '系统订单号唯一', ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售单下账表' ROW_FORMAT = Dynamic; |
| 销售单下账明细表 | sqlCREATE TABLE `account_order_detail`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT, 	`order_no` BIGINT(20) NOT NULL COMMENT '系统订单号', 	`erp_code` VARCHAR(20) COMMENT '商品编码', 	`goods_name` VARCHAR(255) COMMENT '商品名称', 	`goods_type` VARCHAR(10) COMMENT '商品类型, 普通商品- NORMAL、赠品-GIFT', 	`batch_no` VARCHAR(125) COMMENT '生产批号', 	`goods_count` INTEGER COMMENT '商品数量', 	`price` DECIMAL(16,4) COMMENT '商品单价', 	`goods_amount` DECIMAL(16,4) COMMENT '商品金额', 	`share_amount` DECIMAL(16,4) COMMENT '分摊金额', 	`bill_price` DECIMAL(16,4) COMMENT '下账单价',  	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   	`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `deleted`  bigint  NOT NULL DEFAULT 1  COMMENT '是否删除 0-未删除 时间戳-已删除',    `version `  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',     PRIMARY KEY (`id`) USING BTREE,   INDEX `account_order_id_index`(`account_order_id`) USING BTREE COMMENT '系统订单号', ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '销售单下账明细表' ROW_FORMAT = Dynamic; |
| 退库单下账单 | sqlCREATE TABLE `account_refund`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,    `order_no` BIGINT(20) NOT NUll COMEMENT '系统订单号',    `refund_no` BIGINT(20) NOT NULL COMMENT '系统退款单号',    `third_refund_no` VARCHAR(100) NOT NULL COMMENT '系统退款单号', `third_plat_code` VARCHAR(10) NOT NULL COMMENT'三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康',     `service_mode` VARCHAR(10) NOT NUll DEFAULT 'O2O' COMEMENT 'O2O / B2C',    `pos_mode` VARCHAR(10) NOT NULL DEFAUTL 'HD_H1' COMMENT 'HD_H1-海典H1  HD_H2-海典H2  KC-科传',   `pick_type` VARCHAR(10) NOT NULL DEFAULT 'OMS' COMENT 'OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS',    `organization_code` varchar(20) NOT NULL COMMENT '下账机构编码',    `org_parent_path` varchar(128) NOT NULL COMMENT '组织机构父路径id链路 1000-1100-1110-',   `refund_type` VARCHAR(10) NOT NULL COMMENT '仅退款-ONLY_REFUND 退货退款- ALL_REFUND', 	`refund_amount` DECIMAL(16,4) NOT NULL COMMENT '退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额', 	`refund_post_fee` DECIMAL(16,4) COMMENT '商家配送费，金额大于等于0', 	`package_fee` DECIMAL(16,4) COMMENT '订单总额 = 商品明细的商品金额汇总+商家配送+平台配送费+商家包装费+平台包装费', 	`state` VARCHAR(10) DEFAULT 'WAIT' COMMENT '下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败', 	`account_time` datetime(0) NULL COMMENT '下账时间', 	`account_err_msg` varchar(1024) COMMENT '下账失败原因 下账失败返回', 	`create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',  `deleted`  bigint  NOT NULL DEFAULT 1  COMMENT '是否删除 0-未删除 时间戳-已删除',        `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',    PRIMARY KEY (`id`) USING BTREE,   INDEX `order_no_index`(`order_no`) USING BTREE COMMENT '销售单订单号',   INDEX `refund_no_index`(`refund_no`) USING UNIQUE COMMENT '退款单订单号唯一', ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款单下账表' ROW_FORMAT = Dynamic; |
| 销售单明细 | sqlCREATE TABLE `account_refund_detail`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT, 	`refund_no` BIGINT(20) NOT NULL COMMENT '系统退款单号', 	`erp_code` VARCHAR(20) COMMENT '商品编码', 	`goods_name` VARCHAR(255) COMMENT '商品名称', 	`goods_type` VARCHAR(10) COMMENT '商品类型, 普通商品- NORMAL、赠品-GIFT', 	`batch_no` VARCHAR(125) COMMENT '生产批号', 	`goods_count` INTEGER COMMENT '商品数量', 	`refund_price` DECIMAL(16,4) COMMENT '退款单价', 	`refund_goods_amount` DECIMAL(16,4) COMMENT '退款金额 (退款数量*单价)', 	`share_amount` DECIMAL(16,4) COMMENT '分摊金额', 	`goods_order_price` DECIMAL(16,4) COMMENT '退款商品原价', 	`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   	`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `deleted`  bigint  NOT NULL DEFAULT 1  COMMENT '是否删除 0-未删除 时间戳-已删除',    `version `  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `account_refund_id_index`(`account_order_id`) USING BTREE COMMENT '系统退款单号', ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款单下账明细表' ROW_FORMAT = Dynamic; |
|  | sqlalter table oms_order_info add column deleted bigint default 0 comment '逻辑删除字段 默认0-未删除'; alter table order_info add column deleted bigint default 0 comment '逻辑删除字段 默认0-未删除';     alter table order_info drop index third_platform_code; alter table order_info add UNIQUE index third_order_plat_code(third_order_no, third_platform_code,deleted); |


### 3、 接口设计

新增、修改的接口定义；流量预估，接口性能设计；

### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

项目工时、分工等，贴jira连接

## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等