# 2024-05-20 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  | 1``` hydee-oms-logistic ``` | ``` feature/ORDER-1044/logistic_order_optimize ``` |  |  |  | 杨飞 |  | 2024-05-20 11:30 | 2023-10-17 10:00 |
|  | 2``` hydee-business-order-web ``` | ``` feature/ORDER-1044/logistic_order_optimize ``` |  |  |  | 杨飞 |  | 2024-05-20 11:30 |  |
|  | 3hydee-business-order |  |  |  |  |  |  | 2024-05-20 11:30 |  |
|  | 4 |  |  |  |  |  |  |  |  |
|  |  | 5 |  |  |  |  |  |  |  |  |
|  |  | 6 |  |  |  |  |  |  |  |  |
|  |  | 7 |  |  |  |  |  |  |  |  |
|  |  | 8 |  |  |  |  |  |  |  |  |
|  |  | 9 |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

sqlINSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'apiAccount', '客户标识', '请输入客户标识', 'input', 8, 1, 1, null, null, '648427321165877273', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'customerCode', '客户编码', '请输入客户编码', 'input', 6, 1, 1, null, null, 'J0086474299', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'customerPwd', '客户密码', '请输入客户密码', 'input', 7, 1, 1, null, null, 'H5CD3zE6', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'detail', '详细地址', '请输入详细地址', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'expressType', '产品类别', '请输入产品类别', 'select', 3, 1, 1, null, null, null, 2, 'JT_EXPRESS_TYPE');
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'privateKey', '私钥', '请输入私钥', 'input', 9, 1, 1, null, null, '1b4791cb17fb495eae69e0331597f219', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'provinceCityDistrict', '寄件人地址', '请选择寄件人地址', 'cascader', 4, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLJTSD', 'signConst', '签名常量', '请输入签名常量', 'input', 10, 1, 1, null, null, 'jadada236t2', 1, null);


INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'authorization', '授权码', '请输入授权码', 'input', 7, 1, 1, null, null, 'SRiUSv0HgLACgWKZ', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'bizProductNo', '产品类别', '请输入产品类别', 'select', 3, 1, 1, null, null, null, 2, 'YZ_PRODUCT_NO_TYPE');
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'detail', '详细地址', '请输入详细地址', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'key', '密钥', '请输入密钥', 'input', 8, 1, 1, null, null, 'bkR5ZnV6WjYydjRYWHk0eQ==', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'provinceCityDistrict', '寄件人地址', '请输入寄件人地址', 'cascader', 4, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'ZLYZXB', 'senderNo', '客户代码', '请输入客户代码', 'input', 6, 1, 1, null, null, '1100134918907', 1, null);

--平台关联快递
INSERT INTO dscloud.express_mapping (id, platform_code, value, name, code, oms_express, remark, tempKey) VALUES (619, 'ZL', 'JTSD', '极兔速递', 'JTSD', 131, null, null);

--配置常量
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (745, 'JT_EXPRESS_TYPE', 'EZ', '标准快递');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (746, 'JT_EXPRESS_TYPE', 'TYD', '兔优达');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (739, 'YZ_PRODUCT_NO_TYPE', '1', '特快专递');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (743, 'YZ_PRODUCT_NO_TYPE', '10', '电商标快');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (744, 'YZ_PRODUCT_NO_TYPE', '11', '国内标快');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (740, 'YZ_PRODUCT_NO_TYPE', '2', '快递包裹');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (741, 'YZ_PRODUCT_NO_TYPE', '3', '特快到付');
INSERT INTO dscloud.code_value (id, type, code, value_desc) VALUES (742, 'YZ_PRODUCT_NO_TYPE', '9', '国内即日');




CREATE TABLE dscloud.`express_merchant`  (
                                     `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `express_account_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递账号名称',
                                     `status` tinyint(0) NULL DEFAULT 1 COMMENT '快递状态，0禁用，1启用',
                                     `dict_express_merchant_id` int(0) NULL DEFAULT NULL COMMENT '快递码表id',
                                     `extend` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '扩展字段',
                                     `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',
                                     `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',
                                     `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                     `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                     `mer_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户code',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递商户' ROW_FORMAT = Dynamic;



ALTER TABLE `dscloud`.`ds_platform`
    ADD COLUMN `logo_path` varchar(255) NULL COMMENT 'logo地址';


ALTER TABLE `dscloud`.`dict_express_merchant`
    ADD COLUMN `logo_path` varchar(255) NULL COMMENT '图标url地址';

-- 当前可使用的快递做标记
ALTER TABLE `dscloud`.`dict_express_merchant`
    ADD COLUMN `express_merchant_used` tinyint(0) NULL DEFAULT 0 COMMENT 'express_merchant表使用状态：0 未被使用 ，1被使用';





-- 当前可使用的快递做标记
UPDATE `dscloud`.`dict_express_merchant` SET `express_merchant_used` = 1 WHERE id IN (69,
                                                                                      70,
                                                                                      71,
                                                                                      72,
                                                                                      73,
                                                                                      1004,
                                                                                      79,
                                                                                      131);




INSERT INTO `express_merchant` VALUES (8, '圆通快递', 1, 71, '{}', '1', '1', '2024-05-13 10:46:37', '2024-05-13 10:46:37', '500001');
INSERT INTO `express_merchant` VALUES (9, '百世快递', 1, 79, '{}', '1', '1', '2024-05-14 14:05:55', '2024-05-14 14:05:55', '500001');
INSERT INTO `express_merchant` VALUES (10, '中通速递', 1, 72, '{}', '1', '1', '2024-05-14 14:06:46', '2024-05-14 14:06:46', '500001');
INSERT INTO `express_merchant` VALUES (11, '申通快递', 1, 70, '{}', '1', '1', '2024-05-14 14:06:59', '2024-05-14 14:06:59', '500001');
INSERT INTO `express_merchant` VALUES (12, '顺丰速运', 0, 69, '{}', '1', '1', '2024-05-14 14:07:11', '2024-05-14 17:39:26', '500001');
INSERT INTO `express_merchant` VALUES (13, '极兔速递', 1, 131, '{}', '1', '1', '2024-05-14 14:08:26', '2024-05-14 14:08:26', '500001');
INSERT INTO `express_merchant` VALUES (14, '韵达快递', 0, 73, '{}', '1', '1', '2024-05-14 14:09:23', '2024-05-14 17:39:30', '500001');
INSERT INTO `express_merchant` VALUES (15, '邮政小包快递', 1, 1004, '{}', '1', '1', '2024-05-14 14:10:58', '2024-05-14 14:10:58', '500001');

#### 2.2 appolo配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-oms-logistic | **application.yaml** | kd.yz.baseurl | [https://api.ems.com.cn/amp-prod-api/f/amp/api/open](https://api.ems.com.cn/amp-prod-api/f/amp/api/open) |
| kd.yz.addOrder | [https://uat-openapi.jtexpress.com.cn/webopenplatformapi/api/order/addOrder](https://uat-openapi.jtexpress.com.cn/webopenplatformapi/api/order/addOrder) |
|  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |
|  |  |
|  |  |


#### 2.4 xxl-job配置变更

| 任务 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |


#### 2.5 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
| # V1.1.15 B2C（邮政对接/极兔对接/默认快递） | 新增极兔和邮政快递 | 通过界面配置不使用邮政和极兔即可 |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 complete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 complete |  |
| 上线周知产研 | 30 complete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |