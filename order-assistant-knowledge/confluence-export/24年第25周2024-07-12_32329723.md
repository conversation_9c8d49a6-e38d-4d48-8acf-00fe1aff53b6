# 24年第25周2024-07-12

~!

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月17号可提测   - 预计8月初上线 14. 6月27号进入开发阶段 15. 7月17号可提测 16. 预计8月初上线 17. 微商城：   - 预计7月初进入开发阶段 18. 预计7月初进入开发阶段 | 1. 京东到家：   - 消息回调-90%   - 接口对接-80%   - 订单中台接口替换-60% 2. 消息回调-90% 3. 接口对接-80% 4. 订单中台接口替换-60% 5. 微商城：   1. 消息回调 -60%   2. 接口对接-80%   3. 接口中台修改--重用之前的 6. 消息回调 -60% 7. 接口对接-80% 8. 接口中台修改--重用之前的 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0%    3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 20% - 订单同步服务重构, 35% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫   todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕30% 版本升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 7 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 8 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.订单效率优化 |  | 已上线 |  |
| 2.京东/拼多多金额修改 |  | 已上线 |  |
| 3.B2C作业二期 |  | 部分已上线 |  |
| 4.内购需求二期 |  | 已上线 |  |
| 5.美团接口升级 |  | 已上线 |  |
| 6.B2C退款流程优化 |  | 测试中 |  |
| 7.对接客服系统 |  | 接口文档（80%） |  |
| 8.线下单迁移 |  | 已提测（明杰） |  |
| 9.线上单对接 |  |  |
| 10.医保问题修复 |  | 暂时忽略 |  |
| 11.订单效率优化其他项1.主动发起部分退款2.复杂换货 废弃3.评价拉回无法实现  确认4.毛利预警 |  | 明杰下周一开始商家部分退款测试 |  |
| 12.美团电子围栏兼容距离限制 |  | 待开发 |  |
| 13. B2C作业-对接derp |  | 开发中 10% |  |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 | hydee-business-order（广播方式，发送语音播报消息，一个已经修改）待验证 |  |
| B2C下账失败历史单修复 | 计划下周二完成 |  |
|  |  |  |
|  |  |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d 1.B2C订单作业优化V1 编码 BUG 修改 上线 2. B2C订单作业优化V2 D-ERP请货技术方案编写 评审 3.线上BUG修改** | **遗留问题****1.B2C订单作业优化V2 D-ERP请货****风险问题** | **需求研发** **技术建设****** |  |
| 2 | 杨润康 | **本周总工时：5d**- 订单同步服务重构, 35% - 其他   - yxt-common-lang基础包升级调整   - 网关相关: YUNNAN_DS key上线 - yxt-common-lang基础包升级调整 - 网关相关: YUNNAN_DS key上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** 5day 1.微商城对接 （预计下周三提测） 3 day2.优惠金额为负数问题 1 day3.支付宝问题排查 0.5day 预计下周2上线 | **遗留问题** **风险问题** | **需求研发****技术建设****** |  |
| 4 |  | **本周总工时：5day**1. net迁移-京东到家模块：   - 消息回调-90%   - 接口对接-80%   - 订单中台接口替换-60% 2. 消息回调-90% 3. 接口对接-80% 4. 订单中台接口替换-60% 5. 线上-饿了么接口调用情况观察 6. 线上问题排查 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5day**1. 员工推广二期BUG修复 2. 生成请货单 3. B2C处方图片拉取 4. 线上问题排查及修复 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：**1.分摊优化bug a.售后单重复生成 b.京东、拼多多组合商品拆分失败 c.普通商品退款数量修正，修正首单优惠2.分摊优化上线 | **遗留问题****风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. 满减订单剔除医保通道 已上线 2. xxl-job任务迁移 迁移30% ，代码迁移，任务迁移。 3. xxl-job版本升级  a. 二次开发对接ldap账户体系 已完成 b. 增加任务异常企微报警 已完成 c. 版本升级流程文档 文档待评审 4. 日常运维处理 a. 京东到家b2c部分订单未下账 b. 告警群错误日志 | **遗留问题** a. xxl-job 版本升级， 任务表同步方式**风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：**1.科传POS切换处理2. B2C退款优化问题处理 a. O2O默认拣货批号 b. bug修复3. 线上问题处理 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 京东/拼多多金额优化 2. 协助分摊优化上线 3. 梳理智能客服接口 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |