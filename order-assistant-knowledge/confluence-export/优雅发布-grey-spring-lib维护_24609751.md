# 优雅发布-grey-spring-lib维护

[https://yxtgit.hxyxt.com/java-global/grey-spring-boot-lib](https://yxtgit.hxyxt.com/java-global/grey-spring-boot-lib)

| version | parent |
| --- | --- |
| ``` grey-spring-lib的project: ```<version>3.0.0-SNAPSHOT</version>使用3.0.0-SNAPSHOT的项目,不能使用3.1.0及3.1.0.x相关的版本 | 2.0.0-SNAPSHOT<parent> 	<artifactId>grey-spring-boot-starter</artifactId> 	<groupId>cn.hydee.starter</groupId> 	<version>2.0.0-SNAPSHOT</version> </parent> |
| 3.2.0-SNAPSHOT | rocketmq``` 对应的版本 grey-spring-boot-web-starter  3.1.0-SNAPSHOT hydee-spring-boot-starter  3.1.0-SNAPSHOT ``` |
| 3.3.0-SNAPSHOT | 独立优雅下线的开关,原有grey的开关失效,彻底分开配置文件新增配置:elegant-release:   enable: true |
| 3.4.0-RELEASE | 支持rokcetmq自动持有 & 支持kafka停止消费  新的项目包，如果扫描不到优雅发布组件会报空指针相关异常，可以手动添加包扫描: **cn.hydee.starter.grey.springboot.lib** |
| 3.5.0-RELEASE | elegant-release 默认开启 |
| 3.5.1-RELEASE | 优化: 如果未配置包扫描,则跳过Green最新版本 |
| 3.6.0-SNAPSHOT | 添加动态代理（无需上线，没有RELEASE版本） |


grey-spring-boot-web-starter [2.0.0.pa](http://2.0.0.pa)-SNAPSHOT grey-spring-lib 3.1.0-SNAPSHOT
grey-spring-boot-web-starter [2.0.0.pa](http://2.0.0.pa)-SNAPSHOT grey-spring-lib 3.1.0.X-SNAPSHOT


grey-spring-boot-web-starter 3.0.0-SNAPSHOT grey-spring-lib 3.0.0-SNAPSHOT
grey-spring-boot-web-starter 3.1.0-SNAPSHOT grey-spring-lib 3.2.0-SNAPSHOT
grey-spring-boot-web-starter 3.1.0-SNAPSHOT grey-spring-lib 3.3.0-SNAPSHOT 
grey-spring-boot-web-starter 3.1.0-SNAPSHOT grey-spring-lib 3.4.0-SNAPSHOT