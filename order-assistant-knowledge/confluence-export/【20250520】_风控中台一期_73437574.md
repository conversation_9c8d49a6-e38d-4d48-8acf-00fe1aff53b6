# 【20250520】 风控中台一期

# 一、业务背景

## 1.1 业务背景

目前有一个毛利预警，此预警是一个写死的预警规则，比较固定，与当前业务提供的按单一条件预警不再符合，需优化当前的预警配置；

预警只是一手段，最终的是要到自动化决策，如：最简单的自动化下架，自动化的跟价，自动化的价格调整，自动化的限购调整；

## 1.2 目标

a. 完成预警提醒策略，可按照不同的门店，不同渠道不同店铺进行预警；

b. 完成指定商品跳转电商商品页面，再商品管理页上可进行手动的上下架；

## 1.3 名词解释

商品维度提醒：按照商品维度进行统计预警，优势是能监控每一个品毛利情况，用于调整单品活动；

订单维度提醒：按照订单维度进行统计预警

# 二、需求分析

## 2.1 业务流程

**V2.44 订单预警**

# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

true预警处理falseautotoptrue13213

# 五、详细设计

## 5.1 项目结构

**true风控服务falseautotoptrue9112**

## 5.2 涉及数据库

### 5.2.1表设计

1.毛利预警配置：通过配置中心管理 无需添加新表

2.预警记录：

| 表名 | sql |
| --- | --- |
| ``` dscloud_offline. `````` gross_profit_forewarn_record ``` | ``` CREATE TABLE `gross_profit_forewarn_record` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `forewarn_dimension` varchar(15) COLLATE utf8mb4_general_ci NOT NULL COMMENT '预警维度：订单维度ORDER、 商品COMMODITY',   `order_model` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单模式：B2C、O2O、B2B、OFFLINE(线下)',   `sub_company_code` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '分公司编码',   `third_platform_code` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方平台编码：美团27 饿百24等',   `organization_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',   `organization_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店名称',   `online_store_code` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺编码',   `online_store_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺名称',   `order_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单号',   `erp_code` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',   `commodity_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',   `commodity_count` int DEFAULT NULL COMMENT '商品数量',   `is_tax_cost` tinyint DEFAULT NULL COMMENT '是否含税成本价：1：含税 0：不含税',   `cos_price` decimal(16,6) DEFAULT NULL COMMENT '成本单价',   `bill_amount` decimal(16,8) DEFAULT NULL COMMENT '下账金额',   `commodity_profit` decimal(16,2) DEFAULT NULL COMMENT '商品毛利率(%)',   `commodity_profit_quote` decimal(16,6) DEFAULT NULL COMMENT '商品毛利额',   `forewarn_profit` decimal(16,2) DEFAULT NULL COMMENT '预警毛利率(%)',   `forewarn_profit_quote` decimal(16,6) DEFAULT NULL COMMENT '预警毛利额',   `order_time` datetime DEFAULT NULL COMMENT '下单时间',   `forewarn_time` datetime DEFAULT NULL COMMENT '预警时间',   `forewarn_state` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '预警状态:WAIT-待预警 SUCCESS-预警成功 FAIL-预警失败',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='毛利预警记录' ``` |


## 5.3 接口设计

| 所属模块 | 接口URL | 描述 | 入参 | 出参 |
| --- | --- | --- | --- | --- |
| 配置中台 |  | 查询毛利预警规则列表 |  |  |
|  | 查询毛利预警规则明细 |  |  |
|  | 新增毛利预警规则 |  |  |
|  | 更新毛利预警规则 |  |  |
| 风控中台 |  | 选择适用门店 |  |  |
|  | 选择组织架构 |  |  |
|  | 选择门店标签 |  |  |
|  | 查询预警记录列表 |  |  |