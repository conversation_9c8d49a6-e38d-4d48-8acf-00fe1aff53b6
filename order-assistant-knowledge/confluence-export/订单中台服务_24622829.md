# 订单中台服务

> 2.27 DDD项目搭建 3.06 原子服务搭建、DDD第一次组内分享 3.08 订单中台重构落地方案编写 3.11 订单中台OMS重构一期 接口梳理 3.14 重构YXT订单中台(产品&研发)对齐会议 3.19 重放方案文档调整 3.24 订单中台重构正向流程事件风暴会议 4.11 订单中台重构逆向流程事件风暴会议 4.16 订单服务具体到对象(部分涉及到字段)讨论会议 4.19 讨论完【创单服务】请求入参 4.23 订单中台DDD项目结构敲定,拆分仓库,移除job module,并入sync module 4.24 讨论完【配送信息更新】、【申请售后】请求入参 4.25 讨论完【售后服务】、【换货】请求入参

```

```

true订单中台服务划分falseautotoptrue15074

|  |  |  |  | 返回 |
| --- | --- | --- | --- | --- |
| 订单域 | [创单服务](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=31203448) | 1. 主信息 orderInfo   1. 平台信息     1. 平台编码     2. 三方网点   2. 平台编码   3. 三方网点   4. 下单门店   5. 履约门店   6. 整单金额     1. 整单优惠     2. 平台服务费     3. 等等   7. 整单优惠   8. 平台服务费   9. 等等   10. 创建时间   11. 完成时间   12. 支付时间   13. 配送起止时间   1. 平台编码   2. 三方网点   1. 整单优惠   2. 平台服务费   3. 等等 2. 平台信息   1. 平台编码   2. 三方网点 3. 平台编码 4. 三方网点 5. 下单门店 6. 履约门店 7. 整单金额   1. 整单优惠   2. 平台服务费   3. 等等 8. 整单优惠 9. 平台服务费 10. 等等 11. 创建时间 12. 完成时间 13. 支付时间 14. 配送起止时间 15. 明细 16. 购买人（用户信息） 17. 履约信息   1. 收货人信息 18. 收货人信息 19. 处方单信息   1. 是否处方   2. 用药人信息 20. 是否处方 21. 用药人信息 22. 开票信息 23. assistInfo(辅助字段,不需要落库) | sql1.OrderInfo主信息:   `order_state`     '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',   `third_platform_code`          '平台编码',   `third_order_no`          '第三方平台订单号',   `mer_code`          '商户编码',   `client_code`          '网店编码',   `source_online_store_code`          '（接单）线上门店编码',   `source_online_store_name`          '（接单）线上门店名称',   `source_organization_code`          '（（接单）线下门店编码',   `source_organization_name`          '（接单）线下门店名称',   `online_store_code`          '（履约）线上门店编码',   `online_store_name`          '（履约）线上门店名称',   `organization_code`          '（履约）线下门店编码',   `organization_name`          '（履约）线下门店名称',   `delivery_time_type`     '送达方式，即时或预约',   `delivery_time_desc`          '期望送达时间描述',   `seller_remark`          '卖家备注',   `created`   '订单实际创建时间',   `day_num`          '每日号（流水号）',   `self_verify_code`          '骑手取货码&自提码',   `member_no`          '会员编号',   `new_customer_flag`            '新客标识，0：非新客 1：新客',   `integral_flag`            '积分订单标识，0：非积分订单，1：积分订单',   `service_mode`          '服务模式',   `pay_time`   '支付时间',   `order_type`     '订单类型:0普通订单,1预约订单,2处方订单,3积分订单',   `medical_insurance`     '0-非医保订单,1-医保订单',   `extend_info`   '扩展信息', 1.1 整单金额   `buyer_actual_amount`   '客户实付',    `merchant_actual_amount`   '商家实收',    `total_amount`   '商品总金额',   `total_discount`   '订单总优惠',   `merchant_total_discount_sum`   '商家订单总优惠(包含所有优惠)',   `merchant_total_discount_sum_not_delivery_fee`   '商家商品总优惠',   `merchant_discount_sum`   '商家整单优惠',   `discount_fee_dtl`   '商品明细折扣汇总',   `platform_discount`   '平台优惠',   `post_fee_discount`   '运费优惠',   `merchant_delivery_fee`   '商家配送费',   `merchant_delivery_fee_discount`   '商家配送费优惠金额',   `merchant_pack_fee`   '商家打包费',   `platform_delivery_fee`   '平台配送费',   `platform_delivery_fee_discount`   '平台配送费优惠金额',   `platform_pack_fee`   '平台打包费',   `buyer_cod_service_fee`   '买家到付服务费',   `seller_cod_service_fee`   '卖家到付服务费',   `brokerage_amount`   '交易佣金',   `buyer_cod_amount`   '（买家）到付金额',   `platform_fee_collection`   '代收平台费',   `manual_fix_amount`   '手工调整金额',   `detail_discount_collect`   '商品明细优惠汇总',   `different_amount`   '差异金额',    // 当存在多支付方式时，整单维度怎么去赋值?   `pay_code`          'ERP支付方式编码',     `pay_channel`          '支付渠道，如微信支付',   `health_num` '健康贝数量',   `health_value`     '健康贝换算金额',   `delivery_fee`   '配送费',   `pack_fee`   '包装费',   `apportion_type`   '分摊类型：0-OMS分摊规则，1-平台分摊规则',   `medicare_amount`   '医保金额',   `medicare_order_id`        '医保结算id',,    operator_id    operator_Name 1.2 支付信息(List)   `pay_code`          '支付编码,-对应ERP支付编码',   `pay_name`          '支付类型', // 类似微信支付   `pay_price`   '支付类型对应的金额',    2.明细   `platform_sku_id`          '商品三方平台编码',   `third_detail_id`        '第三方详情ID',   `first_type_name`          '一级分类名称',   `second_type_name`          '二级分类名称',   `type_name`          '三级分类名称',   `erp_code`          '商品erp编码',   `bar_code`          '商品条形编码',   `commodity_name`          '商品名称',   `manufacture`          '生产商',   `main_pic`          '商品图片',   `commodity_spec`          '商品规格',   `weight`          '商品重量',   `goods_type` '商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品',//? 需要看代码确认   `drug_type`   '药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)',   `is_medicare_item`   '是否时医保商品 0-否',   `commodity_count` '商品数量',   `original_price`   '商品标价',   `price`   '商品售价',   `average_price`   '商品加权成本价',   `discount_amount`   '商家+平台 促销优惠金额(已乘数量)',//? 需要看代码确认   `actual_amount`   '实付金额(已乘数量)',//? 需要看代码确认    `payment`   '商品实付金额(汇总)(已乘数量)',//? 需要看代码确认   `discount_share`   '单个商品的商家承担优惠', //? 需要看代码确认   `different_share`   '差异分摊', // ? 需要看代码确认   `status`     '明细状态，0正常显示商品，1已退款，2被换货的商品 ... todo 补充',     `platform_discount_fee`   '平台优惠',   `merchant_discount_fee`   '商家优惠',   `brokerage_amount`   '交易佣金',   `vip_different_amt`   '会员优惠金额',   `health_value`     '健康贝换算金额',   `detail_discount`   '平台分摊优惠明细',   `chailing`   '拆零商品标示 1不拆零 2拆零',   `chai_ling_original_erp_code`          '拆零商品原始商品编码',   `chai_ling_original_num` '拆零商品原始数量',   `chai_ling_num` '拆零商品的拆零系数',   `is_gift`       '是否是赠品（0不是赠品，1是赠品）',   `erp_gift` '如果该商品是赠品，该值才有作用；赠品是否下账，0不下账，1下账（默认）',   `is_joint`     '是否是组合商品，0不是组合商品，1是组合商品',   `original_erp_code`          '组合商品erpcode',   `original_erp_code_num` '组合商品原始数量',   `relation_code`          '组合商品关系编码', //? 需要看代码确认   `direct_delivery_type`   '供应商代发标识，0-非供应商代发，1-供应商代发',   `extend`   '明细扩展信息',   `storage_type`     '储存条件：0-常温，1-冷藏，2-冷冻，3-阴凉', 3.购买人   `buyer_message`          '买家留言'   `buyer_name`          '三方平台买家昵称', 4.处方单信息   `is_prescription`     '是否是处方单',   `prescription_flag`     '是否需要审方（1：需要审方 0：不需要审方）',   `prescription_status`       '处方状态 0-待审  1-通过  2-不通过  3-取消',   `is_push_check`     '是否推送审方平台',   用药人信息: 姓名、年龄、性别、`id_card_type` 证件类型、`id_card_no`  5.履约信息   `delivery_time_type`     '送达方式，即时或预约',   `delivery_time_desc`          '期望送达时间描述',   `appointment`     '0-正常订单，1-预约订单',   `appointment_business_flag`   '预约单业务处理状态 0-待处理 1-已处理',   `appointment_business_type`   '预约单业务处理类型 0-修改门店 1-确认到货',   `request_deliver_goods_result`   '申请代发服务商处理结果,1-拒单，2-发货', //B2c   `deliver_goods_refuse_reason`          '代发拒单理由',    5.1 收货人      `receiver_lat`          '收货地址纬度',      `receiver_lng`          '收货地址经度', 6. 开票信息   `invoice_name`         // 开票人   `invoice_content`          '发票内容',   `invoice_title`          '发票抬头',   `invoice_type`          '发票类型 专票 普票',   `need_invoice`          '是否开票，1.开发票；2.不开发票',   `taxer_id`          '纳税人识别码',   `vat_taxpayer_number`          '增值税纳税人识别号', | 订单号 |
| 拣货 | 1. 订单号 2. 拣货时间 3. 拣货员 4. List<商品→List<批号>> => pick_info(拣货信息) 5. version  结论:1. 编辑(含外部) 全部加分布式锁 ( 只要影响本系统或者下游系统的都要加分布式锁) 2. 订单详情 true订单详情布局falseautotoptrue6601 | sql  `order_no` '订单号，雪花算法',   `third_order_no`          '第三方平台订单号',   `picker_id`          '拣货操作员id',   `picker_name`          '拣货员名',   `pick_operator_id`          '拣货实施者',   `pick_operator_name`          '拣货操作者名',   `pick_time`   '拣货时间', // 小前台传    `order_pick_type`     '订单实际拣货类型 1-人工拣货 2-机器自动拣货',   拣货明细List:      `order_detail_id` '订单详情id',      `erp_code`          '商品erp编码',      `commodity_batch_no`          '商品批号',      `count`   '批号对应的数量',      `purchase_price`     '商品进价', // 可能和批号绑定,暂时没有值 |  |
| 配送信息更新 | -- 触发1. 主动发起 2. 平台回调 -- 触发后更新1. 收货人信息 2. 物流(顺丰 达达 美团)/快递(极兔 邮政等)/外卖(平台)/路由信息 3. 配送信息 4. 配送号(配送信息中) | todo 配送日志 order_delivery_log 加经纬度  =====配送信息 - 收货人信息   `order_no` '订单号',   `receiver_name`          '收货人',   `receiver_telephone`          '收货人电话',   `receiver_mobile`          '收货人手机',   `province`          '省份',   `city`          '城市',   `district`          '区域',   `town`          '城镇',   `address`          '详细地址',   `zip_code`          '邮编',   `full_address`          '完整详细地址',   `platform_receiver_mobile_desen`          '收货手机号密文',   `platform_oaid`          '收货人密文', =====配送信息 - 物流/快递/外卖/路由信息   `order_no` '系统订单号',   `state`     '状态，包括1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常',   `delivery_plat_name`          '详细配送方式名称',   `rider_name`       '配送员id',   `rider_phone`          '配送员手机',   `description`          '描述',   `latitude`          '纬度',   `longitude`          '经度',   uploadTIme // 采集经纬度的时间   packageId  =====配送信息 - 配送信息 配送号 orderNO addressId deveryId 关联配送路由信息 List<Pacakge>   packageId:     orderDetailIdList: |  |
| 申请售后 | 1. 订单号 2. List<商品(含数量)> 3. 仅退款、退货退款、仅退货 含强审逻辑,例如强审通过 | sql  `order_no` '系统订单号',   `after_sale_no` '售后订单号',   `after_sale_type`          '售后类型,0仅退款、1退货退款、2仅退货',   `after_refund_type`          '售后范围类型,0部分，1全部',   `after_sale_status`     '售后状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消',   `after_sale_source` '售后来源,1、系统生成，2、手工录入',   `remarks`          '系统备注(运营使用)',   `create_user_name`          '创建人',   `operator_id`  '操作人ID'   `operator_Name` '操作人姓名'   `mer_code`          '商户编码',   `client_code`          '网店编码',   `source_online_store_code`          '（接单）线上门店编码',   `source_online_store_name`          '（接单）线上门店名称',   `source_organization_code`          '（（接单）线下门店编码',   `source_organization_name`          '（接单）线下门店名称',   `online_store_code`          '（履约）线上门店编码',   `online_store_name`          '（履约）线上门店名称',   `organization_code`          '（履约）线下门店编码',   `organization_name`          '（履约）线下门店名称',   `third_order_no`          '第三方平台订单号',   `third_platform_code`          '三方平台编码',   `third_after_sale_no`          '平台售后订单号',   `third_status`          '第三方售后单状态',   `third_after_sale_time`   '平台售后申请时间',   `cancel_time`   '取消时间',   `top_hold`          'TOP拦截标识，0不拦截，1拦截 null=平台无此标识',    -- 根据after_sale_type    `reason`      '售后原因(客户下拉选择)',   `desc` varchar(2000)         '售后描述(客户写)',   `re_calculate_origin_order_flag`   '未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算',// 和产品沟通   `extend_info`      '拓展信息json', // 存入 `ebai_commission_flag`   '饿百获取佣金标识  1-已获取，0-未获取',     售后整单金额Class:       `total_food_amount`     '退款商品总金额',       `total_amount`   '退款商品退用户金额',       `consumer_refund`   '退买家总金额',       `shop_refund`   '商家退款总金额',       `fee_refund`   '退还佣金',       `platform_discount_refund`   '退平台优惠',       `shop_discount_refund`   '退还商家优惠',       `platform_refund_delivery_fee`     '退平台配送费',       `merchant_refund_post_fee`     '退商家配送费',       `platform_refund_pack_fee`     '退平台包装费',       `merchant_refund_pack_fee`     '退商家包装费',       `detail_discount_amount`     '退商品明细优惠',       `postage_amount`   '邮费退款金额',       `user_postage`     '退用户配送费金额(单位元)',       `platform_postage`   '商家退还给平台补贴的金额(单位元)',       `adjust_amount`   '手工调整金额',       `health_num` '心币数量',       `health_rate` '心币汇率',       `health_value`     '心币换算金额',   售后-明细:       `refund_count` '退货数量', | 售后单号 |
| 售后服务 | 1. 同意、拒绝 2. type: 仅退款、退货退款、仅退货、售后收货 3. 售后单号   售后申请同意后生成相应的退货\退款\退货退款单 | sql  `canceller_id`          '取消者id',   `canceller_name`          '取消操作者名',   `cancel_reason`          '取消原因',      同意、拒绝   `checker_id`          '审核员id',   // 是否和支付相关   `complete_time`   '退款完成时间',   `after_sale_goods_time_start`   '退货售后单上门取件，预计送达时间开始',   `after_sale_goods_time_end`   '退货售后单上门取件，预计送达时间结束',   `express_return`          '退回快递公司',   `express_no`          '退回快递单号',   `warehouse_return`          '退回仓库',   `warehouse_code`          '仓库编码',   `after_sale_no` '售后单号',   `third_order_no`          '第三方平台订单号',   `third_refund_no`          '第三方平台退款单号',   `return_goods_no` '退货单号(仅退货有)',   `after_sale_type`          '售后类型,0仅退款、1退货退款、2仅退货',   `after_refund_type`          '售后范围类型,0部分，1全部',   `remarks`          '系统备注(运营使用)',   `after_sale_status` '售后状态 0.待处理、1.已完成、2.已关闭、3.已取消',   todo: 具体操作的时候需要记录操作人、审核人。记录到日志表 |  |
| 换货 | 1. 订单号 2. 商品A→商品B（换货信息） 3. version | sql  {    单独生成一条数据,原始信息查原始订单表    有原单商品分摊到换货商品的逻辑    补充换货日志   }   `order_no` 订单号   `third_platform_code`          '平台编码',   `third_order_no`          '第三方平台订单号',   换货-明细:     原商品Id->List<目标商品ErpCode、数量>     原商品上加是否复杂换货标识 |  |
| 订单干预 | 运维接口，相对简单 | sql  `ex_operator_id`          '异常操作者id',   `ex_operator_name`          '异常操作者名',   `ex_operator_time`   '异常操作时间',   `remark`          '系统备注', // "系统补单" |  |
| 支撑域 | 打印 | 现有场景:1. O2O小票   1. 拣货单（没批号）   2. 配送单（有批号） 2. 拣货单（没批号） 3. 配送单（有批号） 4. B2C 电子面单  参数:1. 订单号(履约门店) 2. 打印type |  |  |
| 语音 | 定时监控统计 参数:1. 门店 2. 语音type (现在是根据type发送指定语音) 3. (订单号,这里忽略，因为目前是可以不通过订单来发送语音) |  |  |
| 门店设置/仓库设置 | 门店设置场景：1. 运营在页面设置 2. 平台拉取  现状:平台App界别，例如在美团平台有公司的3个app,分别为:1. 一心堂(新)   1. A002   2. A003 2. A002 3. A003 4. 上海   1. A002   2. A003 5. A002 6. A003 7. 一心堂(旧)  需要做的事情:1. 录入各平台授权信息 2. App网点拉取下来  现在的组织架构true目前店铺组织falseautotoptrue5111 门店配置内容:1. APP级别CURD 2. 网店CURD 3. 门店CURD 4. 组织CURD  同理，仓库设置、物流服务、平台设置如上 |  |  |
| 订单设置 | 自配送、下账、基础设置等 |  |  |
| 组织机构设置(包含Pos) |  |  |  |
| 日志服务(本地日志) | 1. 人+时间+动作+结果+state+订单号/平台单号 2. 记录before、now信息(小的json串) |  |  |
| 仓储配送域 | 呼叫骑手/呼叫物流 | 订单号运力type/物流type |  |  |
| 物流服务(物流商顺丰、达达等) | 同门店设置 |  |  |
| 电子面单 | (呼叫物流) |  |  |
| 发起配送(取消配送) | 订单号配送单号 |  |  |
| 三方配送回调API |  |  |  |


O2O订单场景

B2C订单场景

todo: 后面与产品讨论，是否可以模糊边界

sql=== 下账服务
`erp_sale_no`          '零售流水',
`erp_state`   '下账状态: 20 待锁库存 30 待下帐  100 已下账  110 已取消',
`bill_time`   '下账时间',
`bill_operator`          '下账操作人',
`cancel_bill_times` '已下账后取消下账次数',

=== 下账服务-退单  
  `erp_state`   '退款单erp状态，20-待下账，100-已下账，102-已取消', // todo 退款单需要
  `erp_refund_no`          '退款单零售流水',
  `bill_time`   '下账时间',


==待定=
 `lock_flag`     '锁定标志: 0未锁定,10取消锁定,20退款锁定,31库存不足,32部分商定不存在,33金额异常,34配送异常',
 `lock_msg`          '异常锁信息',
 `transfer_delivery`   '转仓发货: 0-未转仓, 1-已转仓',
 `complete_time`   '完成时间', // delete
 `source_channel_type`   '来源渠道类型 1-京东渠道',
 `freight_order_no` '运费单号', // 内部系统产生的一个单号
  待定-明细:
    `origin_type`   '预约单供应商来源 1-云货架，2-DC仓',
    `st_code`          '预约单组织机构编码（云货架商家编码或DC仓编码）',

 todo 中台接口添加通用字段-接口调用方、商家号、渠道; 查询时按需加载

平台相关命名使用third_前缀

### 工时评估

写服务

|  | 原-代码位置 |
| --- | --- |
| 创单服务 | cn.hydee.middle.business.order.service.rabbit.OrderMessageConsumer#process |
| 拣货 |  |
| 换货 |  |
| 配送信息更新 |  |
| 申请售后 |  |
| 售后服务(同意、拒绝退款、售后收货) |  |
| 订单干预(订单运维工具) |  |
|  |  |
| 打印 |  |
| 语音 |  |
| 门店设置/仓库设置 |  |
| 语音 |  |
| 订单设置 |  |
| 组织结构设置(含Pos) |  |
| 日志服务(存本地) |  |
|  |  |
| 呼叫骑手 |  |
| 物流服务 (物流商顺丰、达达) |  |
| 电子面单服务 |  |
| 发起配送、取消配送 |  |
| 三方配送回调API |  |


读服务

|  |  |
| --- | --- |
| 订单查询 |  |
| 下账单服务 |  |
| 退款单查询 |  |
| 校验有效期 |  |
| 校验自提码 |  |
|  |  |
| 门店设置/仓库设置 |  |
| 语音 |  |
| 订单设置 |  |
| 打印 |  |
| 日志服务(本地日志) |  |
|  |  |
| 骑手状态、轨迹 |  |
| 电子面单 |  |
|  |  |