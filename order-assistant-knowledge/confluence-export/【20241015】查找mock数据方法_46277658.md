# 【20241015】查找mock数据方法

## 前言

在使用虚拟服务时肯定会有一个问题，mock数据不知道如何进行配置，下面会根据不同的流程给出查找mock数据的办法。

## 通用信息

|  | 测试地址 |
| --- | --- |
| mongodb-test | [*********************************************************************************************************************************************** |
| rocketmq-test | 10.4.3.242:9876;10.4.3.243:9876 |
| mock-test 数据库 | jdbc:[mysql://10.4.3.195:3306/mock](mysql://10.4.3.195:3306/mock) agent/ WrHNOhOGHR8yzMEgKvao |


## 平台信息

| 平台名称 | 平台号 | 备注 |
| --- | --- | --- |
| ``` 其他渠道 ``` | 0 |  |
| ``` 京东到家 ``` | 11 |  |
| ``` 美团 ``` | 27 |  |
| ``` 饿百 ``` | 24 |  |
| ``` 微商城 ``` | ``` 43 ``` |  |
| ``` 平安中心仓 ``` | 44 |  |
| ``` 平安O2O ``` | 45 |  |
| ``` 平安城市仓 ``` | 46 |  |
| ``` 阿里健康 ``` | 48 |  |
| ``` 京东健康 ``` | 41 |  |
| ``` 平安健康B2C ``` | 3001 |  |
| ``` 天猫B2C ``` | 3002 | ``` 菜鸟云栈 ``` |
| ``` 拼多多B2C ``` | 3003 |  |
| ``` 京东B2C ``` | 3004 | ``` 京东无界 ``` |
| ``` 药房网B2C ``` | 3006 |  |
| ``` 平安非药B2C ``` | 9001 |  |


## MQ消息

|  | topic | tag |  |
| --- | --- | --- | --- |
| 创单消息，确认订单消息 | ``` TP_ORDER_THIRD-PLATFORM_ORDER ``` | ``` TAG_ORDER ``` |  |
| 全额退款消息 | ``` TP_ORDER_THIRD-PLATFORM_ORDER-REFUND ``` | ``` TAG_ORDER_REFUND ``` |  |


## 创单-O2O转B2C订单

以美团订单为例（3801293743774278113）

1、【mongodb-test】中【order_callback_27】 搜索 {"businessId":"3801293743774278113"} 得到下面两条数据，代表美团发了两次消息。 一个是创单消息，一个是确认订单消息。mqRecordList是发送的消息内容

2、删除之前的订单数据

| ``` ### 删除订单 POST https://test-api.hxyxt.com/zeus/mock/devops/delete/order content-type: application/json {   "thirdOrderNo":"3801293743774278113" } ``` |
| --- |


3、模拟推送消息

| ``` ### mq b2c创单 POST https://test-api.hxyxt.com/zeus/mock/devops/sendRocketMq content-type: application/json route: {"http://*":"yxt-mock-server","third-platform-gateway":"http://10.4.1.142:8090"} mockKey:test {  "namesrvAddr":"10.4.3.242:9876;10.4.3.243:9876",  "messages": [{"topic": "TP_ORDER_THIRD-PLATFORM_ORDER", "tag": "TAG_ORDER", "message": "{\"adjust_fee\":\"0\",\"buyer_cod_fee\":\"0\",\"buyer_flag\":0,\"buyer_memo\":\"\",\"buyer_message\":\"\",\"clientid\":\"f5e6222bc7ed4f5583b25d822dc75636\",\"cod_payment\":0,\"commission_fee\":\"0.75\",\"confirm_time\":\"2024-10-15 10:22:40\",\"created\":\"2024-10-15 10:22:18\",\"customerpayment\":\"15.0\",\"daynum\":\"1\",\"delivery_time_type\":0,\"delivery_type\":\"0000\",\"discount_fee\":\"0\",\"discount_fee_dtl\":\"0\",\"discount_fee_eccode\":0.0,\"discount_fee_sum\":\"7.5\",\"ectype\":\"27\",\"extrainfo\":\"{\\\"canselfdelivery\\\":false,\\\"details_discount\\\":\\\"[{\\\\\\\"count\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"merchant_discount\\\\\\\":\\\\\\\"0.0\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"[辅仁]感冒灵颗粒10g*9袋/盒\\\\\\\",\\\\\\\"outskuid\\\\\\\":\\\\\\\"849731\\\\\\\",\\\\\\\"platform_discount\\\\\\\":\\\\\\\"0.0\\\\\\\",\\\\\\\"skuid\\\\\\\":\\\\\\\"849731\\\\\\\",\\\\\\\"upc\\\\\\\":\\\\\\\"6938237700445\\\\\\\"},{\\\\\\\"count\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"merchant_discount\\\\\\\":\\\\\\\"7.5\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"[仁和可立克]风寒感冒颗粒8g*9袋/盒\\\\\\\",\\\\\\\"outskuid\\\\\\\":\\\\\\\"181351\\\\\\\",\\\\\\\"platform_discount\\\\\\\":\\\\\\\"0.0\\\\\\\",\\\\\\\"skuid\\\\\\\":\\\\\\\"181351\\\\\\\",\\\\\\\"upc\\\\\\\":\\\\\\\"6935803462349\\\\\\\"}]\\\",\\\"invoiceTitle\\\":\\\"\\\",\\\"needInvoice\\\":\\\"2\\\",\\\"taxerId\\\":\\\"\\\"}\",\"groupid\":\"500001\",\"has_post_fee\":false,\"isadjust\":0,\"medicareAmount\":0,\"merchantActualAmount\":14.25,\"modified\":\"2024-10-15 10:22:40\",\"o2o_shop_id\":\"5737_2705588\",\"olorderno\":\"3801293743774278113\",\"olstatus\":\"27_4\",\"omsdeliverytype\":\"3\",\"omsstatus\":\"2\",\"orderdetaillist\":[{\"adjust_fee\":\"0\",\"clientid\":\"f5e6222bc7ed4f5583b25d822dc75636\",\"discount_fee\":\"0\",\"isdelete\":0,\"item_meal_id\":\"0\",\"merchantDiscount\":7.500000,\"modified\":1728958962659,\"num\":\"1\",\"num_iid\":\"181351\",\"oid\":\"1\",\"orderdetailid\":\"7837089363595\",\"originPrice\":15.0,\"outer_iid\":\"181351\",\"payment\":\"15.0\",\"platformDiscount\":0,\"price\":\"15.0\",\"sku_id\":\"6935803462349\",\"title\":\"[仁和可立克]风寒感冒颗粒8g*9袋/盒\",\"total_fee\":\"15.0\"},{\"adjust_fee\":\"0\",\"clientid\":\"f5e6222bc7ed4f5583b25d822dc75636\",\"discount_fee\":\"0\",\"isdelete\":0,\"item_meal_id\":\"0\",\"merchantDiscount\":0,\"modified\":1728958962659,\"num\":\"1\",\"num_iid\":\"849731\",\"oid\":\"2\",\"orderdetailid\":\"7837089363659\",\"originPrice\":5.5,\"outer_iid\":\"849731\",\"payment\":\"5.5\",\"platformDiscount\":0,\"price\":\"5.5\",\"sku_id\":\"6938237700445\",\"title\":\"[辅仁]感冒灵颗粒10g*9袋/盒\",\"total_fee\":\"5.5\"}],\"ordergiftlist\":[],\"orderid\":\"3801293743774278113\",\"ordertype\":\"N\",\"package_fee\":\"0\",\"pay_time\":\"2024-10-15 10:22:18\",\"payment\":\"15.0\",\"paytype\":\"1\",\"platform_delivery_fee\":0,\"post_fee\":\"2.0\",\"postfee_dis\":\"0\",\"postfee_nodis\":\"2.0\",\"receiver_address\":\"提力克 (101)@#新疆维吾尔自治区和田地区于田县奥依托格拉克乡奥依托格拉克乡\",\"receiver_address_encrypt\":\"#MAvnZrWQzj+sJMGhhLvRatEuHnbTucKGPb9da/FuJM3K1qRQ/Lo/2xEMn5gl4qK4C7TF54i3t6iIyr69+bwirkYLZiZAXTEWxwWF3VZqqTGQScwiOFnkIfcpw7EKXW9cu7LGAEBiTdj7iQWRVlfA0M+gfnCCOkbtdiq3h30OObyfeSIPxyVMBQ==#1#\",\"receiver_address_privacy\":\"#MAvnZrWQzj+sJMGhhLvRatEuHnbTucKGPb9da/FuJM3K1qRQ/Lo/2xEMn5gl4qK4C7TF54i3t6iIyr69+bwirkYLZiZAXTEWxwWF3VZqqTGQScwiOFnkIfcpw7EKXW9cu7LGAEBiTdj7iQWRVlfA0M+gfnCCOkbtdiq3h30OObyfeSIPxyVMBQ==#1#\",\"receiver_city\":\"和田地区\",\"receiver_city_privacy\":\"#kQDaSEyQ0qH0QZ3FE8PG/7f3qxqZRmsvFSgHkhl9iaz6rmqEf7AJmOpxdZ0=#1#\",\"receiver_detail_address\":\"提力克 (101)\",\"receiver_detail_address_privacy\":\"#U4c+QPfhvB4zXFKpvstWR51yPJImYcFbLxFHT2KSAnfvZQ3/UZeEEc9O+HDtq9k=#1#\",\"receiver_district\":\"于田县\",\"receiver_district_privacy\":\"#wWLmz6IEzRKOe4FzpCj8+BYnDfGYapix31poxAZUzmhdXIUMefuR5kk=#1#\",\"receiver_lat\":\"36.824381\",\"receiver_lng\":\"81.946891\",\"receiver_mobile\":\"18190800161\",\"receiver_mobile_encrypt\":\"#VorsueRj4SZqQLAuobLLbevmg3cx/1T7M7vCsScBJOaq40/MlRyVdFjZUQ==#1#\",\"receiver_mobile_privacy\":\"#VorsueRj4SZqQLAuobLLbevmg3cx/1T7M7vCsScBJOaq40/MlRyVdFjZUQ==#1#\",\"receiver_name\":\"杨测试(**)\",\"receiver_name_encrypt\":\"#1SELOiUrJPB1ztI5d6XgjJKkjm2SuxPjPy2VwxA2jS8IxeBzIZleVR1MMveE#1#\",\"receiver_name_privacy\":\"#1SELOiUrJPB1ztI5d6XgjJKkjm2SuxPjPy2VwxA2jS8IxeBzIZleVR1MMveE#1#\",\"receiver_phone\":\"18190800161\",\"receiver_phone_encrypt\":\"#VorsueRj4SZqQLAuobLLbevmg3cx/1T7M7vCsScBJOaq40/MlRyVdFjZUQ==#1#\",\"receiver_phone_privacy\":\"#VorsueRj4SZqQLAuobLLbevmg3cx/1T7M7vCsScBJOaq40/MlRyVdFjZUQ==#1#\",\"receiver_state\":\"新疆维吾尔自治区\",\"receiver_state_privacy\":\"#TZ66LKpgk94wi9Jhk59ozGKlTFf6+1nXRd5YlQ4d8n2wj9wLbjvTF11c4lP92Lbr8eWwsDtoBfo=#1#\",\"receiver_town\":\"奥依托格拉克乡\",\"receiver_town_privacy\":\"#Xxzs/GWBfSVftEs6WgeVLeFRg7U8ly2mrnyaCOHYMmj9nAaAfufbKVdGfD+ZNM3IeulYRbM=#1#\",\"rx_audit_status\":\"0\",\"selfverifycode\":\"1\",\"seller_cod_fee\":\"0\",\"seller_flag\":0,\"settlement_amount\":\"0\",\"shop_delivery_fee\":0,\"status\":\"4\",\"sumdiscount\":\"7.5\",\"total_fee\":\"20.5\",\"trade_from\":\"meituan健康网\",\"type\":\"27_2\",\"yfx_fee\":\"0\"}", "result": "{\"messageId\":\"0A2A02BD000A3304362E4A53244B00CA\",\"success\":true}"}] } ``` |
| --- |


namesrvAddr 是MQ测试环境的。

messages 是推送的数据，可以直接使用 mqRecordList里的数据

## 退款消息

以美团订单为例（3801293743774278113）

消息位置和创单一样 

1、【mongodb-test】中【order_callback_27】 搜索 {"businessId":"3801293743774278113"} "topic": "TP_ORDER_THIRD-PLATFORM_ORDER-REFUND"就是退单消息。mqRecordList是发送的消息内容。

2、模拟推送消息

| ``` ### mq b2c创单 POST https://test-api.hxyxt.com/zeus/mock/devops/sendRocketMq content-type: application/json route: {"http://*":"yxt-mock-server","third-platform-gateway":"yxt-mock-server"} mockKey:test {  "namesrvAddr":"10.4.3.242:9876;10.4.3.243:9876",  "messages": [{"stamp": "2024-10-16 09:50:16", "topic": "TP_ORDER_THIRD-PLATFORM_ORDER-REFUND", "tag": "TAG_ORDER_REFUND", "message": "{\"clientid\":\"f5e6222bc7ed4f5583b25d822dc75636\",\"ectype\":\"27\",\"extrainfo\":\"{\\\"olStatus\\\":\\\"1\\\",\\\"service_type\\\":\\\"1\\\"}\",\"groupid\":\"500001\",\"olorderno\":\"3801293743774278113\",\"olstatus\":\"1\",\"operationSource\":\"1\",\"pushtime\":\"2024-10-16 09:50:16\",\"reason\":\"我不想要了\",\"refundid\":\"***********\",\"status\":\"1\",\"type\":\"1\"}", "result": "{\"messageId\":\"0A2A02BD000A3304362E4F5BCD250145\",\"success\":true}"}] } ``` |
| --- |


## B2C获取面单

暂不支持mock获取面单接口

## 退款审核

以美团订单为例（3801293743774278113）

退款流程会发起对三方进行调用，但是不清楚调用哪些接口，调用成功后的数据是什么，下面例子来解决这个问题

1.调用三方哪些接口如何查看

先配置动态路由： http://* 代表所有http方式的请求都转给虚拟服务(yxt-mock-server), 所有给third-platform-gateway(对接三方平台的服务)的请求，都转给虚拟服务(yxt-mock-server)

| route ：{"http://*":"yxt-mock-server","third-platform-gateway":"yxt-mock-server"} |
| --- |


配置后发起退款会提示系统错误，因为请求数据都给了虚拟服务，但是虚拟服务没有数据返回，所以报错，这时在虚拟服务中会记录哪些接口调用了虚拟服务，从下图可知 request_host是请求的服务，request_path是请求的地址

2. 获取退款审核时的数据

取消 route后，让接口正常路由，发起退款审核，返回成功。

【mongodb-test】中【order_27】 搜索 {"businessId":"3801293743774278113"}，会看到/third-platform/order/27/refund/audit 有一条数据，数据字段为 response

3. 配置mock数据

调用接口后数据就存储成功了， 后面可以调用删除订单接口，重走创单和退款，在发起退款审核时 mockKey填写为 jyb-order-refund 即可。

| ``` ### 添加mockData POST https://test-api.hxyxt.com/zeus/mock/mock/data/addData content-type: application/json {   "data":"",   "path": "/third-platform/order/27/refund/audit",   "mockKey": "jyb-order-refund" } ``` |
| --- |