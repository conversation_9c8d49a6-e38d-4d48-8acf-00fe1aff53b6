# 【20240913】智能客服IM对接

# 1 业务背景

1. 对接智能客服


## 1.2 痛点分析

1. 没写过，没接触过
2. websocket保持连接数存活


## 1.3 系统现状

1. 需要对接


# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 心云中台客服对话窗口列表展示;
2. 展示消息未读数量以及所有未读数量总和;
3. 展示聊天入口-只限单门店账号显示;
4. 聊天消息每30天一清除;
5. 旁白信息-订单号跳转订单详情;
6. 实现即时通讯


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

1. 智能客服下发消息流程图true智能客服推送消息流程falseautotoptrue10425
2. 智能客服回复消息流程true智能客服回复消息流程falseautotoptrue9822
3. 获取会话列表以及详情true获取会话列表以及详情falseautotoptrue3412


## 4.3 时序图

****

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.3.1 前端交互接口

### 5.3.2 纯后端接口

## 5.3 涉及数据库

**CREATE TABLE `ics_message` (**
**`id` bigint NOT NULL AUTO_INCREMENT,**
**`from_id` bigint NOT NULL COMMENT '发送方',**
**`receive_id` bigint NOT NULL COMMENT '接收方',**
**`session_id` bigint NOT NULL COMMENT '会话id',**
**`msg_type` tinyint NOT NULL COMMENT '消息类型(1：文字， 2：图片，3：语音，注 意b2c不支持语音，4： 商品卡片，发送商品卡 片类型则不关注 msg_content，5：订单 卡片类型商家只能接收 消息，不支持给用户发 送消息，只支持单聊 11：群文字，12：群图 片，13：群语音，注意 b2c不支持语音，14： 群商品卡片 其中商品卡 片单次最多传7个商品 )',**
**`msg_context` text COLLATE utf8mb4_general_ci COMMENT '消息内容',**
**`msg_status` tinyint NOT NULL DEFAULT '0' COMMENT '消息状态(0-未读,1-已读)',**
**`created_by` varchar(64) COLLATE NOT NULL COMMENT '创建人',**
**`updated_by` varchar(64) COLLATE NOT NULL DEFAULT COMMENT '更新人',**
**`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',**
**`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '更新时间' ,**
**`isvalid` bigint NOT NULL DEFAULT 1 COMMENT '是否删除 1-未删除，!=1已删除',**
**`version` bigint NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',**

**PRIMARY KEY (`id`),**
**KEY `index_session_id` (`session_id`,`created_time`****) USING BTREE**
**) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;**

**CREATE TABLE `ics_store_session` (**
**`id` bigint NOT NULL AUTO_INCREMENT,**

**`type` tinyint NOT NULL COMMENT '类型(0-单聊,1-多客户群聊)',**

'plat_from_code'
**`` varchar(64)COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店编码',**

'三方门店ID'
**`third_dest_id` bigint NOT NULL COMMENT '三方会话发起人id//todo',**
**`session_id` bigint NOT NULL COMMENT '会话id'(内部生成),**
**`last_message_id` bigint NOT NULL COMMENT '会话中的最后一次消息id',**

**`extent_info` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '扩展信息',**

'updated_day' 更新日 ?
**PRIMARY KEY (`id`)**
**) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;**

redis缓存设计:

1. 未读消息设计:
 数据结构：string
 key： im:platform_code:store_code:_:**third_dest_id**:{sessionId}
 value： 最后一个已读id_count
 过期时间：和会话时间保持一致


走es

1. 用户连接后会在redis中记录下用户连接的服务器，该记录主要用于当用户连接的通道在其他服务器时，服务器向集群中其他服务器转发消息时使用用户连接的所有服务器
数据结构：set
key： im:store:address:{storeId} 
value： [ip:port] 服务器地址集合 
过期时间：1天
2. 用户连接后会在redis中记录下用户和当前服务器的一个心跳关联，客户端定期发送心跳维护关联，如果转发消息时发现用户和该服务器的心跳不存在，则不会转发，同时删除无用的记录用户心跳刷新的记录
数据结构：string
key im:user:address:{storeId}-{address}
value ""
过期时间：30s 心跳会一直续期


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

1. 客户端和服务端每次建立连接时候，会创建有状态的会话Session，服务器得保存维持连接的Session。客户端每次只能和集群服务器其中的一个服务器连接，后续也是和该服务器进行数据传输。因此集群的问题，应该考虑Session的问题，客户端成功连接服务器之后，其他服务器也知道客户端连接成功。[https://www.51cto.com/article/780941.html](https://www.51cto.com/article/780941.html)
2. 通过网关进行路由,对nginx进行广播,调研时间3天
3. 3.1 同一个用户不同门店urseropenid是否相同
  1. 同一个用户相同门店不同单/不同时间
4. 同一个用户相同门店不同单/不同时间


# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年09月19日;**

**研发时间：**

**测试时间：**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等