# 【20250311】基于Binlog迁移数据-废弃

Red废弃

高斯库支持不了很高的并发,导致同步频繁中断。决定换种方式来实现迁移

分支: migration-step-1-archive-binlog

Apollo配置

# 迁移正单配置ORDER,迁移退单配置REFUND. 不在依赖于DB
migrateSort: ORDER

# 配置这个,主要是筛有效数据,因为是基于游标的
migrateStartTime: '2024-07-31 00:00:00'
migrateEndTime: '2025-01-16 00:00:00'

canal:
  # hana数据迁移
  migration-hana: order_migration_hana_to_offline

MySQL:

# 归档库添加version字段 
ALTER TABLE `xf_transsalestotal_${targetSchema}` ADD COLUMN `version` bigint COMMENT '数据版本，每次update+1',ALGORITHM=INPLACE, LOCK=NONE; 

触发Binlog

| 字段 |  |  |
| --- | --- | --- |
| migration | 0 | 未迁移/未成功迁移的 |
| 1 | 迁移成功 |
| 2 | 校验已存在,不迁移 |
| 3 | 线上订单,不迁移 |
| 4 | 明细不存在 |
| version | 默认为空 | 通过该字段触发binlog |


# 正单
migration = 0 and XF_SELLINGAMOUNT >= 0
# 退单
migration = 0 and XF_SELLINGAMOUNT < 0

canal配置

#instance名: migration_hana_to_offline
#ok

#################################################
## mysql serverId , v1.0.26+ will autoGen
# canal.instance.mysql.slaveId=0

# enable gtid use true/false
canal.instance.gtidon=false

# position info
canal.instance.master.address=**********:3306
canal.instance.master.journal.name=
canal.instance.master.position=
canal.instance.master.timestamp=
canal.instance.master.gtid=

# rds oss binlog
canal.instance.rds.accesskey=
canal.instance.rds.secretkey=
canal.instance.rds.instanceId=

# table meta tsdb info
canal.instance.tsdb.enable=true
#canal.instance.tsdb.url=**************************************
#canal.instance.tsdb.dbUsername=canal
#canal.instance.tsdb.dbPassword=canal

#canal.instance.standby.address =
#canal.instance.standby.journal.name =
#canal.instance.standby.position =
#canal.instance.standby.timestamp =
#canal.instance.standby.gtid=

# username/password
canal.instance.dbUsername=canal
canal.instance.dbPassword=canal123
canal.instance.connectionCharset = UTF-8
# enable druid Decrypt database password
canal.instance.enableDruid=false
#canal.instance.pwdPublicKey=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALK4BUxdDltRRE5/zXpVEVPUgunvscYFtEip3pmLlhrWpacX7y7GCMo2/JM6LeHmiiNdH1FWgGCpUfircSwlWKUCAwEAAQ==

# table regex https://github.com/alibaba/canal/wiki/AdminGuide
canal.instance.filter.regex=dscloud_offline_archive\\.xf_transsalestotal.*
# table black regex
canal.instance.filter.black.regex=
# table field filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.field=test1.t_product:id/subject/keywords,test2.t_company:id/name/contact/ch
# table field black filter(format: schema1.tableName1:field1/field2,schema2.tableName2:field1/field2)
#canal.instance.filter.black.field=test1.t_product:subject/product_image,test2.t_company:id/name/contact/ch


# mq config
canal.mq.topic=order_migration_hana_to_offline
# dynamic topic route by schema or table regex
#canal.mq.dynamicTopic=mytest1.user,mytest2\\..*,.*\\..*
#canal.mq.partition=0
# hash partition config
#canal.mq.partitionsNum=32
#canal.mq.partitionHash=test.table:id^name,.*\\..*
canal.mq.partitionHash=dscloud_offline_archive\\.xf_transsalestotal.*:XF_DOCNO

#################################################