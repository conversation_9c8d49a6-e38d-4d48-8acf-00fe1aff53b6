# 【20250328】迁移订单平台规则判断

### 特征确认

### 和产品确认

### 数据核对:

根据数据情况,使用XF_TXSERIAL更完善

### DB:

CREATE TABLE `offline_order_platform_error` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_type` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '迁移订单类型',
  `business_no` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单号',
  `store_code` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺编码',
  `third_platform_code` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台编码',
  `third_business_no` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台单号',
  `created` datetime DEFAULT NULL COMMENT '平台订单创建时间',
  `sharding_no` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分表位置',
  `note` text COLLATE utf8mb4_general_ci COMMENT '备注',
  `need_deleted` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'false' COMMENT '默认false',
  `delete_success` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否删除成功',
  `target_schema` varchar(96) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `hand_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_orderType_businessNo` (`order_type`,`business_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='线下订单平台错误表';

### TODO

  3 complete 收集数据脚本开发   4 complete 迁移脚本   6 complete 生产建表   10 complete 合并分支到master代码   11 complete 上线   12 complete curl   

重复数据收集

已经处理完成

这是根据XF_TXSERIAL长度来判断是否删除的

---其他

这是根据inner_store_dictionary来判断的，切到海典了，就算海典。理论上无影响。

已经咨询产品,暂不处理

  14 complete 代码还没有上线