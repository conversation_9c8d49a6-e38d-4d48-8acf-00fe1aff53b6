# 24年第46周2024-12-20

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 2 |  | **本周总工时：5d**1. 会员消费记录接口自测；迁移脚本开发,自测中 3d 2. 线下单记录售货员需求，已提测 1d 3. 协助营销产品排查促销和券信息\日常事务处理 1d | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：4.5**1.小票增加 批准文号 上线 1.5d2.打印程序灰度发布 周五提测 2.5d3.线上支持 0.5 d | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5**1. 一心助手-订单查询-线上单部分-80% 2. 线上问题支持 | **遗留问题****风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 一件代发联调 提测修复bug 2. 线上支持 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 6 |  | **本周总工时：4.5d**1.配送门店定时拉取2.配合产品处理电子围栏数据3.线上慢接口处理4.其他：拼多多下账单优化调整金额 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：4d** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 测试工具二期概要设计【已评审】   1. 调研录制mock数据技术方案   2. 数据回放技术实现调研 2. 调研录制mock数据技术方案 3. 数据回放技术实现调研 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 一心助手事项 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- | --- |
| 1 | 系统资源 | [生产环境资源](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6376487) | 需要 |  |
| [上下游依赖关系](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=1780751) | 不需要 |  |
| 2 | 稳定性建设 | [[一心助手] 核心链路稳定性建设](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoOBzdIWrdRWqVzHzAd2?scode=AOsAFQcYAAch6OMSDhAeoAxgY9ABo&tab=BB08J2) | 需要 |  |
| 3 | 风险预警 | 系统运行风险（参考：[一心助手项目·简报](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoS0D78UmYRkicHXKbaF?scode=AOsAFQcYAAcQ37SkBxAeoAxgY9ABo&tab=BB08J2)） | 暂定 |  |
| [项目风险预警](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6361502) | 需要 |  |
| [功能配置风险](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24613378) | 不需要 |  |
| 4 | 风险治理 | [[生产环境] 告警问题](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoA34W5hxAQfGNVlNmyi?scode=AOsAFQcYAAc5kkNq9rAdYAFgZCAMo&tab=koftou) | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [[一心助手] 慢接口](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABo19tYe1xnQOW4nKBwN9?scode=AOsAFQcYAAcFYhTsavAeoAxgY9ABo&tab=BB08J2)（参考：[ELK慢接口统计](http://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） | [https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) |  |
| [[MySQL] 慢查询治理（参考：](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2)[MySQL慢查询大盘](http://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))[）](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2) | [https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU) |  |
| [[Redis] 慢查询/大Key治理](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoOXgkVImYRL0kxEKW9i?scode=AOsAFQcYAAcyNcZKgEAeoAxgY9ABo&tab=BB08J2)[（参考：](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2)[Redis大Key监控](http://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!((%27$state%27:(store:appState),meta:(alias:!n,disabled:!f,index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,key:instance,negate:!f,params:!(sk_pro_redis004_cluster,sk_pro_redis007_cluster),type:phrases),query:(bool:(minimum_should_match:1,should:!((match_phrase:(instance:sk_pro_redis004_cluster)),(match_phrase:(instance:sk_pro_redis007_cluster))))))),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc))))[）](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2) | [https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) |  |
| [[ES] 慢查询治理](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABosg5k2fpzQTOGKf837R?scode=AOsAFQcYAAcqliRm10AeoAxgY9ABo&tab=BB08J2) | [https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy | 生产事故复盘 |  |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等

# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |