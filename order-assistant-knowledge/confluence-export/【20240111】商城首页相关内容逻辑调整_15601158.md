# 【20240111】商城首页相关内容逻辑调整

# 一、背景

## 1.1 业务背景

## 1.2 痛点分析

## 1.3 系统现状

# 二、需求分析

## 2.1 业务流程

[1.1.2 商城首页相关内容逻辑调整 - 产品部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6376186)

# 三、目标

**3.1 本期目标**

# 四、整体设计

**4.1 统一语言定义**

| **名称** | **说明** |
| --- | --- |
|  |  |


**4.2 流程图**

**4.2.1****系统流程全景图**

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | ydj_merchant_manager.ydj_cloud_delivery_set | 第三方商家快递配置表 | -- auto-generated definition create table ydj_cloud_delivery_set (     id                     varchar(50)                              not null         primary key,     mer_code               varchar(20)                              not null comment '商家编码',     sp_code                varchar(20)                              not null comment '服务商编码',     ykg                    decimal(16, 2)                           null comment '首重',     freight                decimal(16, 2) default 0.00              null comment '运费',     continue_weight        decimal(16, 2)                           null comment '续重',     renewal_cost           decimal(16, 2) default 0.00              null comment '续费',     postage_free_threshold decimal(16, 2) default 0.00              null comment '包邮门槛',     sort_number            int            default 0                 null,     isvalid                int            default 1                 null comment '是否有效',     create_time            datetime       default CURRENT_TIMESTAMP null comment '创建时间',     create_name            varchar(50)                              null,     modify_name            varchar(50)                              null,     modify_time            datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '末次修改时间' )     comment '第三方商家快递配置表';  create index idx_merCode_spCode     on ydj_cloud_delivery_set (mer_code, sp_code); |
| 2 | ``` ydj_merchant_manager.ydj_cloud_delivery_charge_type ``` | 第三方商家快递计费方式表 | create table ydj_cloud_delivery_charge_type (     id          varchar(50)                        not null         primary key,     mer_code    varchar(20)                        not null comment '商家编码',     sp_code     varchar(20)                        not null comment '服务商编码',     charge_type int      default 1                 not null comment '计费方式，1 默认无邮费 2 按区域/重量计费',     isvalid     int      default 1                 null comment '是否有效',     create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',     create_name varchar(50)                        null,     modify_name varchar(50)                        null,     modify_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '末次修改时间',     constraint idx_merCode_spCode         unique (mer_code, sp_code) )     comment '第三方商家快递计费方式表';  create index idx_spCode     on ydj_cloud_delivery_charge_type (sp_code); |


## 5.3 接口设计

### 5.3.1 前端交互接口

#### 1 API-批量保存(新增或修改)快递配置

1. url**:**/1.0/cloud-delivery-set
2. 请求体：
  1. header新增：spCode，类型：String
  2. 请求body示例：{
    "list": [
        {
            "isvalid": 1,
            "createName": "500001_admin",
            "createTime": "2024-01-12 14:19:30",
            "modifyName": null,
            "modifyTime": "2024-01-12 14:19:30",
            "id": "da68ab5b879a4c23bb4a5b556fcf41c5",
            "merCode": "500001",
            "ykg": 1,
            "freight": 6,
            "continueWeight": 1,
            "renewalCost": 1,
            "postageFreeThreshold": 69,
            "billingPlan": 0,
            "sortNumber": 2,
            "rangeResDTOList": null,
            "rangeId": [
                "510000"
            ]
        }
    ],
    "chargeType": 2
}
3. header新增：spCode，类型：String
4. 请求body示例：{
    "list": [
        {
            "isvalid": 1,
            "createName": "500001_admin",
            "createTime": "2024-01-12 14:19:30",
            "modifyName": null,
            "modifyTime": "2024-01-12 14:19:30",
            "id": "da68ab5b879a4c23bb4a5b556fcf41c5",
            "merCode": "500001",
            "ykg": 1,
            "freight": 6,
            "continueWeight": 1,
            "renewalCost": 1,
            "postageFreeThreshold": 69,
            "billingPlan": 0,
            "sortNumber": 2,
            "rangeResDTOList": null,
            "rangeId": [
                "510000"
            ]
        }
    ],
    "chargeType": 2
}
5. 响应体：


#### 2 API-查询快递配置

1. URL:/1.0/cloud-delivery-set/_search/{mercode}
2. 请求体：
  1. header新增：spCode，类型：String
3. header新增：spCode，类型：String
4. 响应体：
  1. 示例：{
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "deliverySetList": [
            {
                "id": "4ca208f9bfd14ded8fb9c627c4449c72",
                "merCode": "500001",
                "spCode": "551822",
                "ykg": 1.00,
                "freight": 6.00,
                "continueWeight": 1.00,
                "renewalCost": 1.00,
                "postageFreeThreshold": 69.00,
                "sortNumber": 2,
                "rangeResDTOList": [
                    {
                        "rangeId": "510000",
                        "name": "四川省",
                        "configId": "4ca208f9bfd14ded8fb9c627c4449c72"
                    }
                ]
            }
        ],
        "chargeType": 2
    },
    "timestamp": "1705127202256"
}
5. 示例：{
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "deliverySetList": [
            {
                "id": "4ca208f9bfd14ded8fb9c627c4449c72",
                "merCode": "500001",
                "spCode": "551822",
                "ykg": 1.00,
                "freight": 6.00,
                "continueWeight": 1.00,
                "renewalCost": 1.00,
                "postageFreeThreshold": 69.00,
                "sortNumber": 2,
                "rangeResDTOList": [
                    {
                        "rangeId": "510000",
                        "name": "四川省",
                        "configId": "4ca208f9bfd14ded8fb9c627c4449c72"
                    }
                ]
            }
        ],
        "chargeType": 2
    },
    "timestamp": "1705127202256"
}


#### 3 API-查询省份信息

1. URL：1.0/area/_search
2. 请求体：
  1. 示例：{"areaType":2,"parentId":1,"merCode":"500001"}
3. 示例：{"areaType":2,"parentId":1,"merCode":"500001"}
4. 响应体：
  1. 示例：{
    "code": "10000",
    "msg": "操作成功",
    "data": [
        {
            "id": "110000",
            "name": "北京"
        },
        {
            "id": "120000",
            "name": "天津"
        }
    ],
    "timestamp": "1705124773237"
}
5. 示例：{
    "code": "10000",
    "msg": "操作成功",
    "data": [
        {
            "id": "110000",
            "name": "北京"
        },
        {
            "id": "120000",
            "name": "天津"
        }
    ],
    "timestamp": "1705124773237"
}


#### 4 API-查询购物车数量

1. URL:1.0/cart/_getCount
2. 返回体新增字段：
  1. 快递配送数量：Integer expressCartCount;
  2. 自配送数量：Integer selfDeliveryCount;
3. 快递配送数量：Integer expressCartCount;
4. 自配送数量：Integer selfDeliveryCount;


## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 美团店铺授权 | 新建记录表 | business-order |  |  |  |  | done |
| excel上传 |  |  | done |
| xxx-job处理解绑、授权 |  |  |  |
| 授权记录列表（分页、条件查询） |  |  |  |
| 三方接口对接 | 美团店铺授权、解绑、三方门店id更换 | the3platform |  |  |  |  | 90%-待自测 |
| 美团配送方式查询接口：[https://open-shangou.meituan.com/home/<USER>/9 https://open-shangou.meituan.com/home/<USER>/9](https://open-shangou.meituan.com/home/<USER>/9https://open-shangou.meituan.com/home/<USER>/9) |  |  |  |  | 90%-待自测 |
| 京东到家配送方式查询：[https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=aa5a3f35a1c84d1e821b5690ba2828cd](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=aa5a3f35a1c84d1e821b5690ba2828cd) |  |  |  | 90%-待自测 |
| 美团拉单接口 |  |  |  | 90%-待自测 |
| 饿了么拉单接口 |  |  |  | 90%-待自测 |
| 京东到家拉单接口 |  |  |  | 90%-待自测 |
| 店铺自动配置 | 新建流程表 | business-order |  |  |  |  | done |
| 网店获取、去重（通过excel、分平台拉取） |  |  |  |  |  |
| 机构绑定 |  |  |  |  |  |
| 店铺配置 |  |  | 90%-待自测 |
| 订单处理设置 |  |  | 90%-待自测 |
| 自配送设置 |  |  | 90%-待自测 |
| 提示音设置 |  |  | 90%-待自测 |
| 下账设置 |  |  | 80%-待自测 |
| 通知商品中台 |  |  | 找 长江 提供接口 |
| 补单 |  |  |  |  |  |
| 流程回显、筛选 |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等