# 【20250514】线下订单管理后台Canal消息处理

### Green已完成

### 背景

发现线上有极少数的ES数据没有明细和公司code信息

出现概率:

1-2亿的数据，会有几百条

### 开发

分支: fix-offline-manage-es-handler

服务:

order-atom-service

order-framework/order-types

SDK版本

  <groupId>com.yxt.order.types</groupId>
  <artifactId>order-types</artifactId>
  <version>offlineManageEsHandler-SNAPSHOT</version>

Canal配置

# 监听表追加
,dscloud_offline\\.offline_order_detail.*,dscloud_offline\\.offline_refund_order_detail.*,dscloud_offline\\.offline_order_organization.*,dscloud_offline\\.offline_refund_order_organization.*

jira:

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5485

自测:

  5 complete OfflineOrderOrganization    6 complete OfflineRefundOrderOrganization   7 complete CanalOfflineRefundOrderDetail   8 complete CanalOfflineOrderDetail  

CheckList

  12 complete sdk发布   13 complete 合并至master   14 complete 配置生产canal   16 complete 上线  

找出问题数据

POST /pro_es_offline_order_manage/_search
{
  "from": 0,  // 分页起始位置
  "size": 800, // 每页数量
  "query": {
    "bool": {
      "must_not": [
        {
          "nested": {
            "path": "offlineOrderManageDetailList",
            "query": {
              "match_all": {}
            }
          }
        }
      ]
    }
  }
}   
POST /pro_es_offline_refund_order_manage/_search
{
  "from": 0,  // 分页起始位置
  "size": 800, // 每页数量
  "query": {
    "bool": {
      "must_not": [
        {
          "nested": {
            "path": "esOfflineRefundOrderManageDetailList",
            "query": {
              "match_all": {}
            }
          }
        }
      ]
    }
  }
}  