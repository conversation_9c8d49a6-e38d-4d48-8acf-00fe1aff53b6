# 【20250516】已经迁移的数据物理删除,重新迁移

经验: 做任何变动都要记录,方便溯源

|  |  | 状态 |
| --- | --- | --- |
| 删除第二批次已经迁移的数据 | /stage2/remigrate/order-data-relation-hana-old-delete 接口已经触发。redis key:clean_migrated_data根据order_data_relation_hana_stage_2表中的数据清理，order_data_relation_hana_stage_2这个表是第二阶段迁移时的辅助表,所以可用校验SQLselect (max(id) - 89182) as '已经删除的已经迁移的订单数' from deleted_data_7 ;  // 结果28098685,和order_data_relation_hana_stage_2 差了52条记录,经过排查在线下订单库中找不到。故已经全部清理完成已经清理完成了，hana_migration_error_stage_2\hana_migration_stage_2\order_data_relation_hana_stage_2已经无实际用途 | 已完成 |
| 归档库符合条件的表 | 恢复归档表中的migration字段和extend_json字段 | 已完成 |
| 从归档库出发,查询所有已经迁移的订单，收集海典修复平台编码和三方单号 | 这个是前置工作,需要做  84 complete fix_hd_wrong_platform_code_third_order_no 是建立在**归档库**的  fix_hd_wrong_platform_code_third_order_no  89 complete 已经自测通过     90 complete modify_platform_exists添加business_no索引。这是因为平台编码错误被删除的,如果在hana_to_order_failed校验后发现是被误删的，可以基于 以上2个表来重新迁入。结论: 20250518 挑选了几条数据,已经确认无问题。  ALTER TABLE `dscloud_offline`.`modify_platform_exists`  ADD INDEX `idx_businessNo_scene`(`business_no`, `scene`) USING BTREE,ALGORITHM=INPLACE, LOCK=NONE; | 已完成 |
| 迁移脚本开发,需要适配线下订单库和归档库,这个需要提测,让测试介入,不能影响现有业务 | 分支: migration-step-2-archive-refactor -→ 20250519已经合并到migration-step-2-archive服务:order-atom-service 基于migration-step-2-archive分支构建- 95 complete migration-step-2-archive-refactor合并到migration-step-2-archive   96 complete release order-types   97 complete 合并到master分支 order-framework 从master分支拉取- 98 complete 发布release分支，合并到master分支  SDK:  <groupId>com.yxt.order.types</groupId>   <artifactId>order-types</artifactId>   <version>step-2-archive-SNAPSHOT</version>  分成2个库之后orderNo和refundNo的唯一性就不太好处理,这里采用的方案是**增加迁移的标志位**来实现。方案: 修改地方:  69 complete 检查hana_migration_error_stage_2\hana_migration_stage_2\order_data_relation_hana_stage_2这三个表有无都添加_remigrate后缀,标识第二次迁移重来   52 complete 迁移时,海典单号获取XF_TXSERIAL   53 incomplete 要保持主库与归档库同时自动创建表。保持一致。否则会因为确实报表或者字段不存在的问题。所以【标记1】就不能删除,已确定,维持空表,即使没有数据   54 complete 保存时强制路由到归档库，需要改造一下接口  55 complete com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi#offlineOrderExistsInfo  70 complete 自测     71 complete com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi#offlineRefundOrderExistsInfo   72 complete 自测     73 complete com.yxt.order.atom.order.OfflineOrderController#saveOfflineOrder  74 complete 自测     75 complete com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomApi#saveOfflineRefundOrder  76 complete 自测     77 complete com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomQueryApi#refundDetail  78 complete 自测     79 complete com.yxt.order.atom.sdk.offline_order.OfflineOrderAtomQueryApi#detail  80 complete 自测     82 complete 迁移完成后，在触发一下，观察migration。结论:" 可以了,migration 都是2     56 incomplete 【标记1】之前建在主库中的年月归档表可以删除了(先采用重命名的方式,后面确定没有影响后再根据重名名的表明删除。这样可以减少风险)。又可以删的原因: 增加了迁移的标志位。最后操作: 不删除，两边保持一致,用少量的空间换取结构的一致性   100 complete 归档库表结构已经创建   86 complete 把归档库过滤ES逻辑放开。因为如果要修复迁移订单的ES需要放开，让其可以新增 | 已上线 |
| 清理个别订单删除失败的历史订单 |  | 已完成 |
| 数据收集完成后,编写脚本处理收集数据 |  | 已完成 |
| 上线前需要在归档库添加现在线上订单库表结构 |  | 处理中 |


--------------分割线,一下忽略---------

### 20250515重新迁移

分支: migration-step-2-archive

迁移过程中发现MySQL快达到存储上限,需要往归档库中迁移,需要做以下动作:

  10 complete stage_2 结尾的表修改为 stage_2_remigrate 结尾  30 complete stage_2_remigrate 不迁移到归档库,依然在主库。因为检查了逻辑,不用处理     33 incomplete 实际迁移逻辑,需要强制全部走到归档库   11 incomplete 在归档库建立线下订单库表结构   26 complete 各个环境的apollo配置需要修改回来,修改为2406   12 incomplete 因为分库了,所以单号也要独立校验是否重复,封装成一个接口method。只需要在迁移逻辑里面调用,正常逻辑不需要调用   13 incomplete 详情接口,走feign,SDK开一个新的接口   14 incomplete 重复校验接口,走feign,SDK开一个新的接口   22 complete com.yxt.order.atom.sdk.migration.MigrationApi 里面的接口   34 incomplete 正单号、退单号生产的位置,需要校验单号是否重复,调用MigrationApi接口   23 incomplete 里面4个接口已经自测   28 incomplete 
校验逻辑: 迁移时先调用MigrationApi是否存在接口来确认生产的单号是否与主库重复,如果重复就报错。如果与主库没有重复,有mysql表uk约束。所以只需要单向保证。正常业务则正常生成单号,不用考虑线下订单
     15 incomplete 因为需要重新迁移,归档库中的迁移状态需要变更为0 (这可能导致一个问题,extend_json中的数据可能是无效的)  36 incomplete 重新配置起始Id     16 complete 清理order_data_relation_hana_stage_2表中对应的订单数据,物理删除  17 complete order_data_relation_hana_stage_2 表使用OrderDataRelationHanaOldDO来操作   18 complete order_data_relation_hana_stage_2 添加一个字段,用来标识是否删除原单标识 – 没有添加,有deleted_data_7这个表记录(原本有89182条数据)   24 complete >>> 接口地址: /stage2/remigrate/order-data-relation-hana-old-delete 待生产触发