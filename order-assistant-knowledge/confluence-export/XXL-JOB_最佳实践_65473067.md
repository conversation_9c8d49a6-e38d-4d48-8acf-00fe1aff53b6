# XXL-JOB 最佳实践

---

## 一、概述

#### 1. 官方文档

****XXL-JOB 是一个分布式任务调度平台，具有简单易用、功能强大、扩展性强等特点。它广泛应用于各种需要定时任务调度的场景，如数据同步、报表生成、消息推送等。 详情移至 <[xxl-job 官网](https://www.xuxueli.com/index.html)>

#### 2. yxt-xxl-job-admin

 yxt-xxl-job-admin 在 xxl-job-admin 基础上增加了以下功能:

| 功能 | 说明 | 详情 |
| --- | --- | --- |
| 账号体系打通ldap | 替换xxl-job-admin 原有账号体系，集成ldap使用心云账号登陆 |  |
| 异常告警打通企微 | 出现任务调度异常会发送异常信息到企微 |  |


#### 3. yxt-xxljob-spring-boot-starter

****yxt-xxljob-spring-boot-starter 在 xxl-job-core 基础之上增加了以下功能：

| 功能 | 说明 | 详情 |
| --- | --- | --- |
| 简化配置 | 简化原生xxl-job配置最简配置：1.properties 格式：xxl.job.admin.addresses=[http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) 2.yml格式：``` xxl:   job:     admin:       addresses: http://xxl-job-admin:8080/xxl-job-admin/ ``` | `### 调度中心部署根地址 [必填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；``xxl.job.admin.addresses=http://127.0.0.1:8080/xxl-job-admin``### 调度中心通讯TOKEN [选填]：非空时使用默认TOKEN值 sk_token；``xxl.job.admin.accessToken=default_token``### 调度中心通讯超时时间[选填]，单位秒；默认3s；``xxl.job.admin.timeout=3``### 执行器AppName [选填]：执行器心跳注册分组依据；为空则使用`${spring.application.name}`xxl.job.executor.appname=xxl-job-executor-sample``### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。``xxl.job.executor.address=``### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯使用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；``xxl.job.executor.ip=``### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；``xxl.job.executor.port=9999``### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；``xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler``### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；``xxl.job.executor.logretentiondays=30` |
| 注册时机 | 原生xxl-job 注册时机为 单例Bean的预实例化阶段结束时注册任务，这样会导致容器未完全启动完成，任务已经注册，调度中心执行任务调度时会请求到该容器节点，导致调度异常。项目部署过程中企微会大量发送异常消息。yxt-xxljob-spring-boot-starter 修改为程序启动后注册任务。· | 1.原生 xxl-job 执行器实现 SmartInitializingSingleton 接口的 afterSingletonsInstantiated() 进行任务注册。 ```  ```2. yxt-xxljob-spring-boot-starter 自定义执行器 XxlYxtCustomExecutor，实现 ApplicationListener<ApplicationReadyEvent>接口 |
| 注销时机 | 原生xxl-job 注销时机 为Bean销毁时执行，容器下线过程中，执行节点并未从调度中心注销，任然有任务调度到该节点，导致调度异常向企微中发送大量异常消息。 yxt-xxljob-spring-boot-starter 注销时机使用优雅关机过程中调用注销。 | 1.原生 xxl-job 实现接口 DisposableBean 接口的destroy进行销毁2. yxt-xxljob-spring-boot-starter 使用优雅关机进行处理 |


#### 4. 环境地址

| 环境 | 地址 |
| --- | --- |
| 开发 | [https://dev-xxl-job-new.hxyxt.com/xxl-job-admin/](https://dev-xxl-job-new.hxyxt.com/) |
| 测试 | [https://test-xxl-job-new.hxyxt.com/xxl-job-admin/](https://pro-xxl-job-new.hxyxt.com/xxl-job-admin/) |
| 生产 | [https://pro-xxl-job-new.hxyxt.com/xxl-job-admin/](https://pro-xxl-job-new.hxyxt.com/xxl-job-admin/) |


---

## 二、快速开始

| 序号 | 步骤 | 说明 |
| --- | --- | --- |
| 1 | 项目添加依赖 | java<dependency> <groupId>com.yxt</groupId> <artifactId>yxt-xxljob-spring-boot-starter</artifactId> <version>最新版本</version> </dependency> |
| 2 | apollo修改配置文件 | | yml文件 | properties文件 | | --- | --- | | xxl: job: admin: addresses: [http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) | xxl.job.admin.addresses=[http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) | | 注：若是本地启动，请使用不同环境的外网地址 （上文环境地址） | | yml文件 | properties文件 | xxl: job: admin: addresses: [http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) | xxl.job.admin.addresses=[http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) | 注：若是本地启动，请使用不同环境的外网地址 （上文环境地址） |
| yml文件 | properties文件 |
| xxl: job: admin: addresses: [http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) | xxl.job.admin.addresses=[http://xxl-job-admin:8080/xxl-job-admin/](http://xxl-job-admin:8080/xxl-job-admin/) |
| 注：若是本地启动，请使用不同环境的外网地址 （上文环境地址） |
| 3 | 项目中开发Job方法 |  |
| 4 | 调度中心添加执行器（执行器已存在可略过） |  |
| 5 | 调度中心新建调度任务 |  |


---

## 三、特性详解

### 1.任务分片

 XXL-JOB 的任务分片功能允许将一个任务拆分为多个子任务并行执行，每个子任务处理一部分数据。这种机制非常适合处理大数据量的场景，例如批量数据处理、分布式计算等。

##### 1.1 分片原理

 分片参数：XXL-JOB 提供了两个关键的分片参数：

 shardIndex：当前任务实例的分片序号（从 0 开始）。

 shardTotal：总分片数（即任务实例的总数）。

 任务分配：每个任务实例根据 shardIndex 和 shardTotal 处理对应的数据片段。

##### 1.2 使用场景

 大数据量处理：例如处理百万级别的数据，可以将数据按分片拆分为多个子任务并行处理。

 分布式计算：例如分布式统计、分布式排序等场景。

 任务负载均衡：通过分片将任务均匀分配到多个执行器上，避免单点压力过大。

##### 1.3 代码示例

java
@XxlJob("shardingJobHandler")
public void shardingJobHandler() throws Exception {
    // 获取分片参数
    int shardIndex = XxlJobHelper.getShardIndex(); // 当前分片序号
    int shardTotal = XxlJobHelper.getShardTotal(); // 总分片数
 
    // 模拟数据总量
    int totalDataCount = 10000;
 
    // 计算每个分片处理的数据范围
    int dataPerShard = totalDataCount / shardTotal;
    int startIndex = shardIndex * dataPerShard;
    int endIndex = (shardIndex == shardTotal - 1) ? totalDataCount : (shardIndex + 1) * dataPerShard;
 
    // 处理当前分片的数据
    for (int i = startIndex; i < endIndex; i++) {
        // 处理数据逻辑
        processData(i);
    }
 
    XxlJobHelper.log("分片任务执行完成：分片序号={}, 总分片数={}, 处理数据范围={}-{}", shardIndex, shardTotal, startIndex, endIndex);
}
 
private void processData(int dataIndex) {
    // 具体的数据处理逻辑
    XxlJobHelper.log("处理数据：{}", dataIndex);
}





##### 1.4 分片任务配置

 调度中心创建任务

 启动任务后XXL-JOB 会自动将任务分配到多个执行器上，每个执行器根据分片参数处理对应的数据片段。

##### 1.5 分片任务优化

 数据均匀分配：确保每个分片处理的数据量尽量均匀，避免数据倾斜。

 分片数设置：根据执行器的数量和任务复杂度合理设置分片数，避免分片过多导致资源浪费。

 任务幂等性：确保每个分片任务具备幂等性，避免重复处理数据。

---

### 2. 任务依赖

 XXL-JOB 支持任务依赖，即一个任务的执行依赖于另一个任务的完成。可以通过设置任务的子任务 ID 来实现任务依赖。

##### 2.1 创建子任务

 在调度中心的任务管理页面，点击“新增”按钮。

 填写子任务的基本信息：

 任务描述：子任务示例

 JobHandler：childJobHandler

 Cron 表达式：无需设置（由父任务触发）

 路由策略：选择默认策略

 保存任务并记录任务的 ID。

##### 2.2 创建父任务

 在调度中心的任务管理页面，点击“新增”按钮。
 填写子任务的基本信息：

 任务描述：子任务示例

 JobHandler：parentJobHandler

 Cron 表达式：按需求设计

 路由策略：选择默认策略

 在“子任务 ID”字段中填写父任务的 ID（例如 119）。

 保存任务。

##### 2.3 任务依赖关系

 父任务完成后，调度中心会自动触发子任务的执行。
 如果父任务执行失败，子任务不会被触发。

##### 2.4 代码示例

java@XxlJob("parentJobHandler")
public void parentJobHandler() throws Exception {
    XxlJobHelper.log("父任务开始执行");
    // 父任务逻辑
    XxlJobHelper.log("父任务执行完成");
}
 
@XxlJob("childJobHandler")
public void childJobHandler() throws Exception {
    XxlJobHelper.log("子任务开始执行");
    // 子任务逻辑
    XxlJobHelper.log("子任务执行完成");
}

##### 2.5 任务依赖链

 如果需要配置多级任务依赖（例如任务 A → 任务 B → 任务 C），可以按照以下步骤操作：
 创建任务 A，并记录其 ID（例如 jobAId）。

 创建任务 B，设置其子任务 ID 为 jobAId，并记录其 ID（例如 jobBId）。

 创建任务 C，设置其子任务 ID 为 jobBId。

 这样，任务 C 完成后会触发任务 B，任务 B 完成后会触发任务 A。

##### 2.6 任务依赖的注意事项

 任务状态：父任务必须成功完成，子任务才会被触发。如果父任务失败，子任务不会执行。
 任务超时：如果父任务超时或异常终止，子任务不会被触发。

 任务幂等性：确保父子任务具备幂等性，避免重复执行导致数据不一致。

 任务日志：通过 XXL-JOB 的日志功能查看父子任务的执行情况，便于排查问题。

##### 2.7 任务依赖的监控

 日志监控：在调度中心查看父子任务的执行日志，确保任务按预期执行。
 任务状态：通过调度中心的任务管理页面查看任务的状态，确保依赖关系正确。

##### 2.8 任务依赖的优化

 依赖链长度：避免依赖链过长，导致任务执行延迟。
 任务拆分：如果某个任务过于复杂，可以拆分为多个子任务，通过依赖关系按顺序执行。

 任务并行：对于没有依赖关系的任务，可以配置为并行执行，提高任务执行效率

### 3. 任务超时控制

 XXL-JOB 支持任务超时控制，可以通过设置任务的超时时间来控制任务的执行时间。如果任务执行时间超过设定的超时时间，任务将被强制终止。

##### 3.1 使用场景

**防止任务长时间运行**：例如某些任务可能因为数据量过大或逻辑复杂导致执行时间过长，通过超时控制可以避免任务占用资源过久。

**资源保护**：防止任务因异常情况（如死循环）导致系统资源耗尽。

**任务稳定性**：确保任务在规定时间内完成，避免影响其他任务的执行

##### 3.2 代码示例

java@XxlJob("timeoutJobHandler")
public void timeoutJobHandler() throws Exception {
    // 设置任务超时时间为 5 秒
    XxlJobHelper.handleTimeout(5000);

    XxlJobHelper.log("任务开始执行");

    try {
        // 模拟任务执行逻辑
        for (int i = 0; i < 10; i++) {
            XxlJobHelper.log("处理数据：{}", i);
            Thread.sleep(1000); // 模拟耗时操作
        }
    } catch (InterruptedException e) {
        XxlJobHelper.log("任务被强制终止");
        throw e;
    }

    XxlJobHelper.log("任务执行完成");
}

##### 3.4 超时控制的注意事项

 超时时间设置：根据任务的实际执行时间合理设置超时时间，避免设置过短导致任务频繁失败。

 任务幂等性：确保任务具备幂等性，避免任务被强制终止后重试导致数据不一致。

 任务日志：通过 XXL-JOB 的日志功能查看任务的执行情况，便于排查超时问题。

##### 3.5 超时控制的优化

 任务拆分：如果任务执行时间较长，可以将任务拆分为多个子任务，通过分片或依赖关系并行执行。

 任务优化：优化任务逻辑，减少任务执行时间，避免触发超时控制。

 动态超时时间：根据任务的实际执行情况动态调整超时时间，提高任务的执行效率。

### 4. 任务日志

 XXL-JOB 提供了任务日志功能，可以记录任务的执行日志。通过 `XxlJobHelper.log` 方法可以记录日志信息。

##### 4.1 任务代码中记录日志

 使用 XxlJobHelper.log 方法记录日志信息。

 `XxlJobHelper.log` 方法支持格式化字符串，类似于 `String.format。` 

java@XxlJob("demoJobHandler")
public void demoJobHandler() throws Exception {
    XxlJobHelper.log("任务开始执行");

    try {
        // 模拟任务逻辑
        for (int i = 0; i < 5; i++) {
            XxlJobHelper.log("处理数据：%d", i); // 记录处理进度
            Thread.sleep(1000); // 模拟耗时操作
        }
        XxlJobHelper.log("任务执行成功");
    } catch (Exception e) {
        XxlJobHelper.log("任务执行失败：{}", e.getMessage()); // 记录异常信息
        throw e; // 抛出异常，标记任务为失败
    }
}

##### 4.2 查看任务日志

##### 4.3 日志清理

 a. 自动清理 

 XXL-JOB 提供了日志自动清理功能，可以定期清理过期的日志数据。

 b. 手动清理

##### 4.4 日志记录优化

 清晰简洁：日志内容应清晰简洁，便于快速定位问题。

 关键信息：记录任务的关键步骤、参数和结果。

 异常信息：捕获并记录异常信息，便于排查问题。

 避免过度记录：避免在循环中记录大量日志，影响任务性能。

 异步记录：如果需要记录大量日志，可以考虑异步记录方式。

### 5. 任务失败重试

 XXL-JOB 支持任务失败重试，可以通过设置任务的重试次数来控制任务失败后的重试行为。

##### 5.1 配置任务失败重试

 a. 调度中心页面配置任务重试次数

 b. 任务代码中处理重试逻辑

java@XxlJob("retryJobHandler")
public void retryJobHandler() throws Exception {
    int retryCount = XxlJobHelper.getRetryCount(); // 获取当前重试次数
    XxlJobHelper.log("第 {} 次重试", retryCount + 1);

    try {
        // 模拟任务逻辑
        if (Math.random() < 0.5) {
            throw new RuntimeException("模拟任务失败");
        }
        XxlJobHelper.log("任务执行成功");
    } catch (Exception e) {
        XxlJobHelper.log("任务执行失败：{}", e.getMessage());
        throw e; // 重新抛出异常，触发重试
    }
}

##### 5.2 任务失败重试优化建议

 a. 合理设置重试次数：根据任务的重要性和失败概率，合理设置重试次数。

 b. 重试间隔: 如果需要自定义重试间隔，可以在任务代码中实现

java@XxlJob("retryJobHandler")
public void retryJobHandler() throws Exception {
    int retryCount = XxlJobHelper.getRetryCount();
    if (retryCount > 0) {
        Thread.sleep(5000); // 每次重试间隔 5 秒
    }

    // 任务逻辑
}

 c. 幂等性设计: 确保任务具备幂等性，即多次执行同一任务不会产生副作用。

## 四、使用建议

#### 1. 任务设计

 1.1 任务粒度

 建议：任务粒度应尽量小，避免一个任务处理过多的逻辑。

 原因： 

 易于维护：小粒度的任务逻辑简单，便于理解和维护。

 易于扩展：小粒度的任务可以灵活组合，满足不同的业务需求。

 故障隔离：如果某个任务失败，不会影响其他任务的执行。

 1.2 任务幂等性

 建议：任务应具备幂等性，即多次执行同一任务不会产生副作用。

 原因： 

 重试安全： 在任务失败重试时，幂等性可以避免重复处理数据。

 数据一致性： 确保任务执行结果的一致性，避免数据错误。

#### 2. 任务监控

 2.1 日志监控

 建议：通过任务日志监控任务的执行情况，及时发现和解决问题。

 实现： 

 在任务代码中使用 XxlJobHelper.log 记录关键步骤和结果。

 在调度中心查看任务日志，分析任务的执行状态。

 2.2 报警机制

 建议：设置任务失败报警，及时通知相关人员处理。

 实现：（已经实现企微告警）

 在调度中心配置任务失败报警，支持邮件、短信、Webhook 等方式。

#### 3. 任务优化

 3.1 任务分片

 建议：对于大数据量处理任务，使用任务分片提高处理效率。

 实现：（可参考特性详解中 任务分片）

 在任务代码中获取分片参数（shardIndex 和 shardTotal）。

 根据分片参数将任务拆分为多个子任务并行执行。

 3.2 任务依赖

 建议： 合理设置任务依赖，避免任务之间的资源竞争。

 实现： （可参考特性详解中 任务依赖）

 在调度中心配置任务的子任务 ID，实现任务依赖。

 对于多级依赖，可以形成任务依赖链。