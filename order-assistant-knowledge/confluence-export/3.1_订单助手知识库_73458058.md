# 3.1 订单助手知识库

#### 维护地址

[https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-knowledge](https://yxtgit.hxyxt.com/order/order-assistant/-/tree/feature-order-assistant/order-assistant-knowledge) 

#### 开发阶段

目前维护在 feature-order-assistant 分支上

#### 暂定的目录结构

true知识库根目录
├── 01_产品与服务 (Product & Service)
│   ├── 01_产品概览 (Product Overview)
│   │   ├── 核心产品介绍
│   │   ├── 产品特性矩阵
│   │   └── 竞品对比分析
│   ├── 02_功能模块 (Feature Modules)
│   │   ├── 模块A详解
│   │   ├── 模块B详解
│   │   └── 集成关系图
│   ├── 03_用户场景 (User Scenarios)
│   │   ├── 典型用例集
│   │   ├── 用户画像
│   │   └── 场景流程图
│   └── 04_产品演进 (Product Evolution)
│       ├── 版本历史
│       ├── 功能路线图
│       └── 技术债务
├── 02_技术架构 (Technical Architecture)
│   ├── 01_系统设计 (System Design)
│   │   ├── 整体架构图
│   │   ├── 服务拆分
│   │   └── 数据流设计
│   ├── 02_技术栈 (Technology Stack)
│   │   ├── 前端技术
│   │   ├── 后端技术
│   │   └── 基础设施
│   ├── 03_开发规范 (Development Standards)
│   │   ├── 编码规范
│   │   ├── 测试规范
│   │   └── 代码审查
│   └── 04_部署运维 (DevOps)
│       ├── 部署流程
│       ├── 监控体系
│       └── 应急响应
├── 03_业务流程 (Business Process)
│   ├── 01_核心流程 (Core Processes)
│   ├── 02_支撑流程 (Support Processes)
│   ├── 03_管理流程 (Management Processes)
│   └── 04_流程优化 (Process Optimization)
├── 04_问题解决 (Problem-Solution)
│   ├── 01_常见问题 (FAQ)
│   ├── 02_故障诊断 (Troubleshooting)
│   ├── 03_解决方案 (Solutions)
│   └── 04_最佳实践 (Best Practices)
├── 05_培训入门 (Training & Onboarding)
│   ├── 01_新员工指南 (New Employee Guide)
│   ├── 02_技能培训 (Skill Training)
│   ├── 03_认证考核 (Certification)
│   └── 04_学习路径 (Learning Paths)
└── 06_治理规范 (Governance & Standards)
    ├── 01_组织架构 (Organization)
    ├── 02_政策制度 (Policies)
    ├── 03_安全规范 (Security Standards)
    └── 04_合规要求 (Compliance)

#### 参考

[搭建AI知识库踩了37个坑，血泪总结这套避雷手册](https://www.53ai.com/news/zhishiguanli/2025062018623.html)