# 【20231208】海典POS对接

# 一、背景

## 1.1 业务背景

目前心云主要使用的是科传POS，后续需要改为海典POS

## 1.2 痛点分析

科传POS过于老化，需要迭代更换

## 1.3 系统现状

心云使用的是科传POS

# 二、需求分析

## 2.1 业务流程

**1.2.3 海典pos对接 - 产品部 - Confluence (hxyxt.com)**

**订单拉单 (axshare.com)**

# 三、目标

## 3.1 本期目标

- 新增海典POS：心云对接海典POS下账
- POS适配：门店根据配置判断走科传POS还是海典POS进行下账
- 下账代码重构：原海典正向逆向单下账逻辑过于冗余，需要进行重构
- 新增退单下账列表：查看逆向单下账状态
- 添加补偿任务：
- B2C下账推单任务过滤，根据绑定的机构是门店还是仓库去做不同的处理
- B2C第一次审核过后页面无法直观判断是直发门店还是仓库
- 验证→ 凯哥


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

### 4.2.1 系统流程全景图

**1、正逆向单创建下账任务流程**

true下账定时任务创建时间falseautotoptrue4811

**2、正向单执行下账任务流程**

**true正向单下账定时任务流程falseautotoptrue6413**

**3、逆向单执行下账任务流程**

**true逆向单下账定时任务流程falseautotoptrue6214**

# 五、详细设计

## 5.1 存储数据库设计

sqlEmacsinner_store_dictionary表调整ALTER TABLE dscloud.inner_store_dictionary ADD COLUMN pos_mode tinyint(1) default 3 not null COMMENT 'pos机模式（1：海典H1，2：海典H2，3：科传）';

sqlEmacsorder_log订单日志表调整ALTER TABLE dscloud.order_log MODIFY COLUMN operate_desc varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作描述';

## 5.2 接口设计

1、异步任务正向单下账接口

2、异步任务逆向单下账接口

3、海典正向单对接接口

4、海典逆向单对接接口

5、下账规则重构（是否需要新建接口）

6、前端退款单下账列表查询接口

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 入口---- 异步任务插入点梳理 配置XXL | 插入正单定时任务（拣货或者配送出库时） | business-order |  | 0.5pd |  |  |  |
| 支持根据门店配置产生正向单 |  | 0.5pd |  |  |  |
| 插入逆单定时任务（逆单审批通过/平台已取消状态） 包含梳理代码的时间 |  | 1pd |  |  |  |
| 自测 |  | 0.5pd |  |  |  |
| 配置xxljob | xx-job |  | 0.5pd |  |  |  |
| 底层实现 海典POS对接 | 1.正单下账接口对接 | business-order |  | 1pd |  |  |  |
| 2.退单下账接口对接 |  | 1pd |  |  |  |
| 自测 |  | 0.5pd |  |  |  |
| 前置 正向单下账金额重算逻辑。 | 仅正向单下账 |  | 0.5pd |  |  |  |
| 正向+部分退下账 |  | 1pd |  |  |  |
| 正向+全退 |  | 0.5pd |  |  |  |
| 自测 |  | 1pd |  |  |  |
| 前置 逆向单下账金额重算逻辑 | 正未下+部分退-》不下账 |  | 0.5pd |  |  |  |
| 正未下+最后一次部分退-》不下账 |  | 0.5pd |  |  |  |
| 正未下+全退-》不下账 |  | 0.5pd |  |  |  |
| 正已下+部分退-》1.退下账 2.更新明细3.重算金额 |  | 1pd |  |  |  |
| 正已下+全退-》1.使用正单明细组装下账 2.更新正单状态 3.更新退单状态 |  | 1pd |  |  |  |
| 自测 |  | 1pd |  |  |  |
| 其他 | B2C下账推单任务过滤 B2C管理平台---下账过滤新POS单 |  | 0.5pd |  |  |  |
| 科传下账模式 下账过滤新POS单 |  | 0.5pd |  |  |  |
| 优化机器人报警服务 做成总览模式，根据消息中心推送给门店语音报警、OMS消息中心、企微触达门店人员。 |  | 1pd |  |  |  |
| OMS页面下账列表增加-退单下账失败列表 增加后端字段枚举 |  | 0.5pd |  |  |  |
| 添加补偿任务-- |  |  |  |  |  |
| 订单优化处理 | 订单拉取时店铺编码中有空格和大小写或空格需自动识别 |  | 0.5pd |  |  |  |
| 订单补单逻辑调整正向单补单成功后生成待拣货订单/异常订单 |  | 0.5pd |  |  |  |
| 三方平台下发新订单逻辑调整下发正向单且心云无此订单则生成带拣货订单/异常订单 |  | 0.5pd |  |  |  |
| 店铺管理–-订单状态修改时新增待拣货订单不能更新为已完成订单校验非待拣货订单修改为已完成订单时需要生成对应的下账单订单修改为已取消–-订单未下账则移除待下账列表订单修改为已取消--订单已下账则生成退货退款类型的负单在待下账列表退款单修改为已完成--正向单已下账需生成退款单的带下账订单 |  | 1pd |  |  |  |
| 接口联调测试 | 联调测试 |  |  | 2pd |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等