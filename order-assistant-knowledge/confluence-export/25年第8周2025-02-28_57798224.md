# 25年第8周2025-02-28

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | B2C拉单重构 |  |  | 待处理 |
| 2 | 减少告警数 |  |  | 已处理 |
| 3 | oms:o2o:dscloud:ordercount:888888:0001 |  |  | 已处理 |
| 4 | hydee:oms:b2c:order:strategy:express:merCode500001 |  |  | ``` 订单 快递策略匹配 ``` |
| 5 | business-gateway timeout [url:/dscloud/1.0/ds/order/page/exception](http://url/dscloud/1.0/ds/order/page/exception) |  |  | 已处理 |
| 6 | business-gateway timeout [url:/dscloud/1.0/ds/order/page/normal](http://url/dscloud/1.0/ds/order/page/normal) |  |  | 暂不处理 |
| 7 | business-gateway timeout [url:/b2c/1.0/order/state/count/500001/-99](http://url/b2c/1.0/order/state/count/500001/-99) |  |  | 待处理，现在查表优化空间不大，得改数据源 |
| 8 | business-gateway timeout [url:/b2c/1.0/order/list](http://url/b2c/1.0/order/list) |  |  | 已处理 |
| 9 | business-gateway timeout [url:/dscloud/1.0/ds/refund/RefundLedgerList](http://url/dscloud/1.0/ds/refund/RefundLedgerList) |  |  | 已处理 |
| 10 | business-gateway timeout [url:/b2c/1.0/order/page/getGoodsCounts](http://url/b2c/1.0/order/page/getGoodsCounts) |  |  | 待处理 |
| 11 | business-gateway timeout [url:/b2c/1.0/scanShip/expressSheet/generate/batch](http://url/b2c/1.0/scanShip/expressSheet/generate/batch) |  |  | 暂不处理。由于批量生成面单导致的 |
| 12 | select id from cloud_print_content where create_time < '2025-02-08 00:00:00.0' order by id desc limit 1; |  |  | 已处理 |
| 13 | select id from `cloud_sound_content` where `create_time` < '2025-02-03 00:00:00.0' order by id desc limit 1; |  |  | 已处理 |
| 14 | middle-order提交订单回滚问题(处方单-订单无法取消) |  |  | 改动范围大，暂不处理 |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 | B2B加盟商城 | **交易中台**:  技术方案评审：已完成。  一心助手对接：  代码开发进度：完成80%代码开发。   **支付中台**:  技术方案评审：已完成。  一心助手对齐：已完成与一心助手对于D-ERP支付流程的对齐工作。  代码开发进度：70%。 下游对接进度：0%。   **订单中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，对接ing。  ERP/POS对接: 接口已经和开发核对了一次。现在进入开发阶段  order-service开发进度：90%。 order-atom开发进度：90% 联调进度： |  |  |
| 2 | 客服中台搭建 | 客服中台 待联调 |  |  |
| 3 | 订单监控-(一致性保障) | 订单一致性保障方案 已上pre |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** 1.订单一致性自测上线（pre）2.定时任务释放库存bug处理 上线 历史数据清除 | **遗留问题** **风险问题** | **需求研发**订单一致性上线 跟踪B2C拉单重构**技术建设** |  |
| 2 |  | **本周总工时：5d**1. 15家公司hana增量数据迁移，脚本开发、上线、数据核对 **2.5d** 2. 迁移脚本重构,降低运维和数据核对成本;上线物料准备(迁移任务SQL(140个任务)\校验SQL\容量评估) **1.5d** 3. 迁移脚本自测、配合测试。新增无明细场景单处理，打标 1d 4. 海典\科传数据错误，修复脚本, 已上线 5. 线上\线下订单MQ消息添加五级分类编码,已上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：5d**订单B2B开发1. 接口联调 2. 业务细节处理 | **遗留问题**erp请求信息保存，以及异常后的手动重试逻辑**风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d****订单B2B开发**1. **权限校验** 2. **订单、售后单操作日志** 3. **库存占用释放** 4. **海典POS接口对接** 5. **联调** | **遗留问题**- 平台原始订单信息保存 - 售后单的库存释放问题 - 商品中台的库存占用释放接口待联调 **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 客服中台前端联调 2. 一件代发批量发货优化 3. 请货单优化 4. 转正常单优化 5. 微商城列表查询优化 6. 智能客服订单查询接口增加线下单查询 | **遗留问题**1. 智能客服方还未确认联调时间以及联调周期 2. 智能客服订单查询接口增加支付方式，需要徐凯调研 **风险问题**1. 微商城列表查询优化middle_order.order_info加字段刷数据 | **需求研发****技术建设** |  |
| 6 |  | **本周总工时：5d**1.海典H2医保订单下账对接（已上线）2.海典回调处方信息查询（待联调）2.拼多多开发（30%-40%） | **遗留问题**1.拼多多开发**风险问题** | **需求研发**1.拼多多开发**技术建设** |  |
| 7 |  | **本周总工时：4d******1. B2B交易中台支持 a. 购物车、结算、提单流程已和一心助手联调完成 b. 退款流程等待订单中台、支付中台联调 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 上海/天津Pos切换新增开关与sql准备 2. 原子服务一致性检查问题修复 3. B2C备注订单/会员慢病数据线下单数据支持 4. 慢sql问题修复 5. logback转log4j2：business_order已部署test 6. 值班文档处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. B2B支付中台支持   1. 支付域 100%   2. 支付账单 100%   3. 退款域 100%   4. 退款账单 100%   5. 支付异常处理 100%   6. 安全校验100%   7. 分库表 0% 2. 支付域 100% 3. 支付账单 100% 4. 退款域 100% 5. 退款账单 100% 6. 支付异常处理 100% 7. 安全校验100% 8. 分库表 0% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 | B2C拉单重构 |  |  | 待处理 |
| 2 | business-gateway timeout [url:/b2c/1.0/order/page/getGoodsCounts](http://url/b2c/1.0/order/page/getGoodsCounts) |  |  | 待处理 |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |