# 24年第22周2024-06-21

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度60%）   - 预计7月初上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度60%） 10. 预计7月初上线 11. 支付宝：   - 联调阶段 预计20号提测 12. 联调阶段 预计20号提测 | 饿了么 17日提测了 支付宝20日提测了 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参  6月1日进入开发阶段 |  | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 7 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 8 | [https://jira.hxyxt.com/issues/?filter=10715](https://jira.hxyxt.com/issues/?filter=10715) 订单中台-进行中筛选器 |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.B2C退款流程优化 |  | 测试中-龙敏 |  |
| 2.O2O电子围栏同步 |  | 开发中 5% |  |
| 3.订单效率优化 |  | 测试中-明杰 杨飞 |  |
| 4. 虚拟商品订单 |  | 测试中-宇航 |  |
| 5.紧急需求B2C作业优化一期 |  | 俊峰-开发中 80%国枫-开发中 80%杨花-联调中 |  |
| 线下单迁移 |  | 代码开发完成,待自测。迁移脚本非订单数据部分已迁移至dev环境 |  |
| 线上单对接 |  | 开发完成,下游系统对接中,配合解决相关问题 |  |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 |  |  |
| 对外门店信息查询 | 完成 |  |
| 对外历史B2C订单查询 | 完成 |  |
| Git分支管理 | 完成 |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：4.5d**1.虚拟商品订单提测 BUG修改 2.B2C订单汇总/自动拣货 3.售后流程图 | **遗留问题 1.虚拟商品订单BUG** **风险问题**   **** | **需求研发** **技术建设** **** |  |
| 2 | 杨润康 | **本周总工时：5d**- 线下单   - 迁移代码开发完成,待自测   - 线下单新增医保信息开发进度95%,剩余部分需要昆明研发确认   - hana非订单信息已迁移至开发库   - 线下单配合测试 - 迁移代码开发完成,待自测 - 线下单新增医保信息开发进度95%,剩余部分需要昆明研发确认 - hana非订单信息已迁移至开发库 - 线下单配合测试 - 线上单   - 线上单正单和退单消息开发完成 - 线上单正单和退单消息开发完成 - 其他   - 配合大数据同学核对订单字段、数据   - 日常问题处理(优雅发布、middle-id等问题) - 配合大数据同学核对订单字段、数据 - 日常问题处理(优雅发布、middle-id等问题) | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** 4 day1.支付宝联调 2day 2.电子围栏 商家主动退款 接口支持 1day 3. 打印优化 1day | **遗留问题**电子围栏 还没有接收美团骑手的变更通知，需要再额外增加一个获取第三方平台 配送费 等信息接口**风险问题** **** | **需求研发****技术建设****** |  |
| 4 |  | **本周总工时：5day**.net重构：- 饿了么自测+test环境测试支持 贵州美团O2O转B2C订单处理 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5 day**1. 虚拟商品前端联调 2. 虚拟商品修复bug 3. B2C优化 4. 订单重构售后服务活动图 5. 线上问题处理 | **遗留问题** **风险问题** **** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5day**1.B2C分摊优化提测及BUG修改2.异常换货提测3.全局预警设置提测 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. **b2c 退款流程优化 提测** 2. **美团/饿了么电子围栏需求 需求打回** 3. **b2c 退款下账金额错误问题** 4. **测试环境BUG修复** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 海典调拨单上线 2. refund_order冗余来源下单门店信息 3. B2C退款优化开发 4. 京东到家配置变更需求重启 5. 线上问题处理 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 商家部分退款，获取订单方式开发 2. 商家部分退款，平台退单回调模式开发 3. 项目启动工具开发完成,支持order-web和business-order快速启动。 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |