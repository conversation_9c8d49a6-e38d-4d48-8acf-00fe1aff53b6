# 【2025-04-24】采购中台一期

# 一业务背景

## 1.1 需求背景

a.加盟店首次铺货，目前是由D-ERP发起。直接铺货，但铺货的订单是加盟商无法进行支付的，所以需要走铺货后生成订单，进而进行支付；

b.智能请货，基于当前销售和销售预判从而对商品进行请货；

c.办公用品的请货（低消耗商品），它不需要POS进行收货。为了快速上线，此类订单，不推POS，直接推D-erp，ERP下推WMS进行作业。（为什么要走这个业务，1是低值消耗品不管控库存，2是POS不做这个库存管理和验收）

d.指定商品铺货，某些紧俏型商品，这类商品不是门店自主请货，而是由运营分配指定门店指定xx数量，通过这种方式进行铺货’

e.兼容POS端请货；

这些请货来有不同的场景，源于不同渠道，针对于这些来源不同的渠道，我们构建一个采购中台，承接不同平台的采购单，满足不同场景的请货；

## 1.2 痛点分析

1. 没有统一的采购管理。数据管理麻烦


## 1.3 系统现状

1. 采购目前只能在pos 系统中操作。


# 二、需求分析

## 2.1 业务流程

[V2.37 采购中台1.0建设 - 产品部 - 一心数科数字化产研中心-wiki](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65473007)

# 三、目标

**3.1 本期目标**

1. 建设采购中台，完成对上游各采购场景的兼容


# 四、整体设计

## 4.1 统一语言定义

****采购单：Purchase order

 采购单状态 ：待确认:WAIT_CONFIRM 已确认:CONFIRMED 已完成 FINISHED 已取消 CANCELED

采购单标签： 新店铺货: NEW_STORE_STOCK 消耗品:CONSUMABLES 多门店铺货: MULTIPLE_STORE

## 4.2 流程图

**4.2.1 采购单流程**

**？ 订单拆单 后采购单状态控制**

**true采购流程falseautotoptrue5232**

**4.2.2 心云OMS 添加采购单流程**

trueoms 导入falseautotoptrue6334

创建定时任务，删除7天以前的无效临时数据。

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.2.1 前端交互接口

### 5.2.2 对外接口

## 5.3 涉及数据库

5.3.1 采购单主表

sqlDJangotruecreate table purchase_order
(
    id                 bigint auto_increment primary key,
    mer_code                  varchar(20) collate utf8mb4_unicode_ci null comment '商户编码',
    purchase_order_no   varchar(64)                          not  null comment '采购单号 PO+yymmdd000001',
    state               varchar(32)                            null comment '采购单状态  待确认:WAIT_CONFIRM  已确认:CONFIRMED  已完成 FINISHED  已取消 CANCELED',
    related_order_no         varchar(64)                            null comment '关联订单号，采购转订单后的单号',
    parent_purchase_order_no varchar(64)                          not  null comment '父采购单号 关联一次请求或者一次导入',
    Auto_payment             tinyint     default 0 not null comment '是否超时自动支付 0:不支付  1:超时自动支付',
    overdue_payment_time     integer     default 0  null comment  '设置超期自动支付时间 天为单位，0 表示超期不会自动支付',
    settlement_status         varchar(20)                            null comment 'SETTLEMENT 提单成功 SETTLEMENT_FAILED:提单失败 WAIT_SETTLEMENT:等待提单',
    msg         varchar(500) charset utf8               null comment '采购单处理异常信息',
    company_code              varchar(20) collate utf8mb4_unicode_ci null comment '分公司编码',
    organization_code         varchar(20) collate utf8mb4_unicode_ci null comment '所属机构编码',
    organization_name         varchar(50) collate utf8mb4_unicode_ci null comment '所属机构名称',
    purchase_order_label       varchar(20)                         not null comment  '采购单标签 新店铺货: NEW_STORE_STOCK 消耗品:CONSUMABLES 多门店铺货: MULTIPLE_STORE ',
    confirm_by                varchar(64)                             null comment '确认人',
    confirm_time              datetime null                                   comment '确认时间',
    created_by                varchar(64)                            not null comment '创建人 申请人',
    updated_by                varchar(64)                            not null comment '更新人',
    created_time              datetime default CURRENT_TIMESTAMP     not null comment '创建时间',
    updated_time              datetime default CURRENT_TIMESTAMP     not null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                   tinyint default 0 not null   comment '是否删除 0：没有删除 1: 删除',
    version                   bigint   default 1                     not null comment '数据版本，每次update+1'
)
    comment '采购单主表';
  create index purchase_order_no_index
    on purchase_order (purchase_order_no); 

5.3.2 采购单商品信息表

sqlDJangotrueCREATE TABLE purchase_order_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '明细ID',
    purchase_order_no VARCHAR(32) NOT NULL COMMENT '采购订单编号',
    erp_code VARCHAR(32) NOT NULL COMMENT '商品编码',
    erp_name VARCHAR(128)  NULL COMMENT '商品名称',
    commodity_spec VARCHAR(128) COMMENT '商品规格',
    manufacture     varchar(255)  null comment '生产商',
    commodity_count decimal(16, 6)  null comment '商品数量',
    status   tinyint not null  comment '状态：-1 异常 0 正常 ',
    ex_msg varchar(255)  null comment '错误信息，如果status 为-1 这个字段必定有值',
     remark VARCHAR(200) COMMENT '备注',
    sys_create_time    datetime default CURRENT_TIMESTAMP null comment '系统创建时间',
    sys_update_time     datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '系统更新时间',
    KEY idx_purchase_order_no (purchase_order_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购商品明细表';

5.3.3 导入数据临时存储表 -废弃

sqlDJangotrueCREATE TABLE temp_purchase_order_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '临时明细ID',
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID 后端生成临时数据生成后返回给前端用于后面临时数据修改查询',
    erp_code VARCHAR(32) NOT NULL COMMENT '商品编码',
    erp_name VARCHAR(128) NOT NULL COMMENT '商品名称',
    commodity_spec VARCHAR(128) COMMENT '商品规格',
    manufacture     varchar(255)  null comment '生产商',
    commodity_count decimal(16, 6)  null comment '商品数量',
    price       decimal(16, 6)   null comment '商品售价',
    purchase_order_label       varchar(20)                         not null comment  '采购单标签 新店铺货: NEW_STORE_STOCK 消耗品:CONSUMABLES 多门店铺货: MULTIPLE_STORE ', 
    online_store_code         varchar(20)                            null comment '线上店铺编码',
    online_store_name         varchar(50) charset utf8               null comment '线上店铺名称',
    company_code              varchar(20) collate utf8mb4_unicode_ci null comment '分公司编码',
    company_name              varchar(20) collate utf8mb4_unicode_ci null comment '分公司名称',
    organization_code         varchar(20) collate utf8mb4_unicode_ci null comment '所属机构编码',
    organization_name         varchar(50) collate utf8mb4_unicode_ci null comment '所属机构名称',
    KEY idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='临时采购商品明细表';