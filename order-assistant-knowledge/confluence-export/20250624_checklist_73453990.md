# 20250624 checklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| B2C订单接入合规追溯码 | hydee-business-order-webhydee-business-order-b2c-third前端服务：cloud-ui |  | feature/ORDER-5684/trace-code |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备 注 |
| --- | --- | --- |
| dscloud | sqlMidnighttrueALTER TABLE `dscloud`.`account_order` MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`;   ALTER TABLE `dscloud`.`account_refund` MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`;   ALTER TABLE `dscloud`.`account_refund_detail` MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`, ADD COLUMN `trace_codes` varchar(1000) NULL COMMENT '追溯码，多个使用,号分隔' AFTER `batch_no`, ADD COLUMN `trace_code_type` varchar(30) NULL COMMENT '追溯码类型' AFTER `trace_codes`;        ALTER TABLE `dscloud`.`account_order_detail` ADD COLUMN `trace_codes` varchar(1000) NULL COMMENT '追溯码，多个使用,号分隔' AFTER `batch_no`, ADD COLUMN `trace_code_type` varchar(30) NULL COMMENT '追溯码类型' AFTER `trace_codes`;    CREATE TABLE `offline_order_detail_pick`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '主键',   `order_no` bigint(0) NULL DEFAULT NULL COMMENT '内部订单号',   `order_detail_no` bigint(0) NULL DEFAULT NULL COMMENT '内部明细编号',   `order_detail_pick_no` bigint(0) NULL DEFAULT NULL COMMENT '订单明细拣货批号唯一号',   `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',   `make_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品批号',   `count` int(0) NULL DEFAULT NULL COMMENT '数量',   `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '系统创建时间',   `sys_update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '系统更新时间',   `version` bigint(0) NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_order_detail_no`(`order_detail_no`) USING BTREE,   INDEX `idx_erp_code`(`erp_code`) USING BTREE,   INDEX `idx_sys_create_time`(`sys_create_time`) USING BTREE,   INDEX `idx_order_no`(`order_no`) USING BTREE ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '心云订单明细拣货信息-待迁移到发货单' ROW_FORMAT = Dynamic;   CREATE TABLE `offline_order_detail_trace`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `order_no` bigint(0) NULL DEFAULT NULL COMMENT '内部订单号,自己生成',   `order_detail_no` bigint(0) NULL DEFAULT NULL COMMENT '内部明细编号,自己生成',   `order_detail_pick_no` bigint(0) NULL DEFAULT NULL COMMENT '订单明细拣货批号唯一号',   `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',   `make_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品批号',   `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追溯码',   `trace_code_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追溯码类型',   `nhsa_report_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保上报标识',   `dra_report_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药监上报标识',   `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '系统创建时间',   `sys_update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '系统更新时间',   `version` bigint(0) NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_order_detail_no`(`order_detail_no`) USING BTREE,   INDEX `idx_erp_code`(`erp_code`) USING BTREE,   INDEX `idx_sys_create_time`(`sys_create_time`) USING BTREE,   INDEX `idx_order_no`(`order_no`) USING BTREE ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '心云订单明细追溯码信息-待迁移到发货单' ROW_FORMAT = Dynamic;   CREATE TABLE `offline_refund_detail_pick`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `order_no` bigint(0) NOT NULL COMMENT '订单号',   `refund_no` bigint(0) NOT NULL COMMENT '退款单号',   `refund_detail_no` bigint(0) NOT NULL COMMENT '售后单明细ID，关联refund_detail表id字段',   `refund_detail_pick_no` bigint(0) NOT NULL COMMENT '退单下账单明细拣货唯一号',   `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',   `make_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品批号',   `count` decimal(16, 6) NULL DEFAULT NULL COMMENT '数量',   `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '系统创建时间',   `sys_update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '系统更新时间',   `version` bigint(0) NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_refund_detail_no`(`refund_detail_no`) USING BTREE,   INDEX `idx_erp_code`(`erp_code`) USING BTREE,   INDEX `idx_sys_create_time`(`sys_create_time`) USING BTREE,   INDEX `idx_order_no`(`order_no`) USING BTREE ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '心云退单订单明细拣货信息' ROW_FORMAT = Dynamic;   CREATE TABLE `offline_refund_detail_trace`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `order_no` bigint(0) NOT NULL COMMENT '订单号',   `refund_no` bigint(0) NOT NULL COMMENT '退款单号',   `refund_detail_no` bigint(0) NOT NULL COMMENT '售后单明细ID refund_detail表id字段',   `refund_detail_pick_no` bigint(0) NOT NULL COMMENT '退单下账单明细拣货唯一号，内部生成 关联offline_refund_detail_pick表refund_detail_pick_no字段',   `erp_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',   `make_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品批号',   `trace_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追溯码',   `trace_code_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追溯码类型',   `refund_flag` tinyint(1) NULL DEFAULT 0 COMMENT '退款标识 0：正常 1：已退',   `nhsa_report_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保上报标识',   `dra_report_flag` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '药监上报标识',   `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',   `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',   `sys_create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '系统创建时间',   `sys_update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '系统更新时间',   `version` bigint(0) NULL DEFAULT NULL COMMENT '数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_refund_detail_no`(`refund_detail_no`) USING BTREE,   INDEX `idx_erp_code`(`erp_code`) USING BTREE,   INDEX `idx_sys_create_time`(`sys_create_time`) USING BTREE,   INDEX `idx_order_no`(`order_no`) USING BTREE ) ENGINE = InnoDB  CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '心云退单订单明细追溯码信息' ROW_FORMAT = Dynamic; |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


#### 2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 complete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 complete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 complete |  |
| 预发验证 | 24 complete |  |
| 依赖check | 6 complete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |