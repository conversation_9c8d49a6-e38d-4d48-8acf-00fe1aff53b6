<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.yxt.order</groupId>
    <artifactId>order-assistant</artifactId>
    <version>1.0.0</version>
  </parent>

  <groupId>io.github.imfangs</groupId>
  <artifactId>dify-java-client</artifactId>
  <version>1.1.3</version>
  <name>dify-java-client</name>
  <description>Java Client for Dify</description>
  <packaging>jar</packaging>

  <!--克隆开源项目的-->
  <!--    <url>https://github.com/imfangs/dify-java-client</url>-->

  <!--    <developers>-->
  <!--        <developer>-->
  <!--            <name>imfangs</name>-->
  <!--            <email><EMAIL></email>-->
  <!--            <organization>GitHub</organization>-->
  <!--        </developer>-->
  <!--    </developers>-->

  <!--    <scm>-->
  <!--        <connection>scm:git:**************:imfangs/dify-java-client.git</connection>-->
  <!--        <developerConnection>scm:git:**************:imfangs/dify-java-client.git</developerConnection>-->
  <!--        <url>https://github.com/imfangs/dify-java-client</url>-->
  <!--        <tag>HEAD</tag>-->
  <!--    </scm>-->

  <!--    <licenses>-->
  <!--        <license>-->
  <!--            <name>The Apache License, Version 2.0</name>-->
  <!--            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>-->
  <!--            <distribution>repo</distribution>-->
  <!--        </license>-->
  <!--    </licenses>-->

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <okhttp.version>4.12.0</okhttp.version>
    <jackson.version>2.18.3</jackson.version>
    <slf4j.version>2.0.17</slf4j.version>
  </properties>

  <dependencies>

    <!-- SLF4J -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>${slf4j.version}</version>
    </dependency>

    <!-- OkHttp -->
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>${okhttp.version}</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp-sse</artifactId>
      <version>${okhttp.version}</version>
    </dependency>

    <!-- Jackson -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.version}</version>
    </dependency>

    <!-- Lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.11.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>${slf4j.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.5.0</version>
        <configuration>
          <doclint>none</doclint>
        </configuration>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.central</groupId>
        <artifactId>central-publishing-maven-plugin</artifactId>
        <version>0.7.0</version>
        <extensions>true</extensions>
        <configuration>
          <publishingServerId>central</publishingServerId>
          <autoPublish>true</autoPublish>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-gpg-plugin</artifactId>
        <version>3.1.0</version>
        <executions>
          <execution>
            <id>sign-artifacts</id>
            <phase>verify</phase>
            <goals>
              <goal>sign</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
