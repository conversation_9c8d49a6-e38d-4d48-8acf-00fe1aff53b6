# 24年第30周2024-08-16

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 7月4-已上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 7月4-已上线 11. 支付宝：   - 7月4号 暂停对接 12. 7月4号 暂停对接 13. 京东到家：   - 6月27号进入开发阶段   - 7月18号已提测   - 8月13上线 14. 6月27号进入开发阶段 15. 7月18号已提测 16. 8月13上线 17. 微商城：   - 预计7月初进入开发阶段 18. 预计7月初进入开发阶段 19. 美团：   - 7月29号开始开发   - 预计8月16号提测   - 预计9月中旬上线 20. 7月29号开始开发 21. 预计8月16号提测 22. 预计9月中旬上线 23. 配送：   1. 7月29号开始开发   2. 预计8月19号提测 24. 7月29号开始开发 25. 预计8月19号提测 | 1. 微商城：   1. 消息回调 -100%   2. 接口对接-100%   3. 接口中台修改–重用之前的   4. 周四提测 2. 消息回调 -100% 3. 接口对接-100% 4. 接口中台修改–重用之前的 5. 周四提测 6. 配送：   1. 消息回调-70%   2. 接口对接-70%   3. 接口中台改造-100% 7. 消息回调-70% 8. 接口对接-70% 9. 接口中台改造-100% 10. 美团：   - 消息回调-100%   - 接口对接-100%   - 订单中台接口替换-100%   - 预计8-16号提测 11. 消息回调-100% 12. 接口对接-100% 13. 订单中台接口替换-100% 14. 预计8-16号提测 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段 - 拣货-拣货开发中 20% - 拣货-下账 0% - 拣货-呼叫骑手 0%    3.配送信息更新:  4.申请售后/售后服务: | - 拣货-拣货开发中 50% - 订单同步服务重构, 55% | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫   todo:发送事件与主逻辑非事务执行讨论 枚举名字规范化 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移7月5日 营销中台已上线完毕30% 版本升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 7 | [订单中台-进行中](https://jira.hxyxt.com/issues/?filter=10715) |  |  |  |  |
| 8 | [订单缺陷-进行中](https://jira.hxyxt.com/issues/?filter=10814) |  |  |  |  |
| 9 | [订单故障-进行中](https://jira.hxyxt.com/issues/?filter=10815) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.对接客服系统 |  | 开发完成，自测中 |  |
| 2.线下单对接-海典 |  | 预计下周上线 |  |
| 3.线下单对接-科传 |  | 测试中 |  |
| 4.线上单对接 |  | 测试中 |  |
| 5.慢病接口支持-按门店_sku es支持搜索 |  | 开发30% |  |
| 7.医保对接历史原价对接改为促销 |  | 8月19号之前进入开发 |  |
| 8.B2C中转平台迁移部署 |  | 9月9号之前完成上线 已经dev部署，下周预发环境验证 |  |
| 9.B2C作业V2.1 |  | 已上线 |  |
| 10.O2O作业V2.1 |  | 已上线 |  |
| 11.处方单履约流程V2.1 |  | 开发中10% |  |
| 12.店铺标签 |  | 联调中 下周二提测 |  |
| 13.O2O正单负单下账金额优化 |  | 测试中 |  |
| 14.B2C库存占用 |  | 开发中，下周二提测 |  |
| 15.购物车小优化 |  | 8月19号计划上线 |  |
| 吉客云 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 | hydee-business-order（和O2O正单负单下账金额优化一起上） |  |
| XXL-JOB迁移升级 | [https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e](https://doc.weixin.qq.com/sheet/e3_AaQAyAaZANIXd47tDf2TB6KrpdQPC?scode=AOsAFQcYAAcmpChsLEAboAOAYLADg&tab=6x120e) |  |
| Q3绩效 | [https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg7TQQGu5hTT0z1PGiJ1?scode=AOsAFQcYAAcRyn775gAboAOAYLADg&tab=9cjz4i) | 拉齐评级标准 |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d****1.线上事故 2.B2C库存占用****3.D-ERP 请货导出和回调时间添加 4.数字类型超长前端丢失精度专项** | **遗留问题****1.B2C库存占用下周提测 2.数字类型超长前端丢失精度专项** | **需求研发** **技术建设****** |  |
| 2 |  | **本周总工时：5d**1. 调整内容科传联调,已完成 2. 线下单调整   1. 新增订单是否参加促销标识【涉及海典、科传、迁移订单】   2. 新增订单拦截暂存开发【涉及海典、科传、迁移订单】   3. 新增海典渠道新增订单级别券开发【涉及海典】   4. 新增退单用户逻辑开发【涉及海典、科传、迁移订单】   5. 新增订单拦截后补偿逻辑开发,开发中,今天预计开发自测完成【涉及海典、科传、迁移订单】 3. 新增订单是否参加促销标识【涉及海典、科传、迁移订单】 4. 新增订单拦截暂存开发【涉及海典、科传、迁移订单】 5. 新增海典渠道新增订单级别券开发【涉及海典】 6. 新增退单用户逻辑开发【涉及海典、科传、迁移订单】 7. 新增订单拦截后补偿逻辑开发,开发中,今天预计开发自测完成【涉及海典、科传、迁移订单】 8. 线下单配合测试   1. 处理父子单传入顺序问题导致父子单关联关系确认问题 9. 处理父子单传入顺序问题导致父子单关联关系确认问题 10. 慢病接口支持-按门店_sku es支持搜索，30%，本周无进度 | **遗留问题**  **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：** **5d**1.NET 配送模块迁移 2 day，还需要2天左右时间2.美团活动接口对接以及配合联调 1 day3.京东无界隐私问题调查 1day4.其他线上问题处理。 1day 处方图片重试 下周2上线 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 4 |  | **本周总工时：5d**- .net迁移-美团模块： 1. 订单中台的接口替换 2. dev-test环境部署 - .net迁移-京东到家模块： 1. 8-13上线 2. 线上订单数据观测+bug修复 - 日常值班支持 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：4d**1.O2O订单导出 2.批量处理 3.回调物流平台重试 4.B2C新下账单批量处理 | **遗留问题**包装费下账毛利预警更新**风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：4.5d**1. 下账单同步ES，权限增加全部选项 已上线 2. 京东B2C 邮费生成邮费单，下账信息邮费为0 已解决 3. 拼多多退款取消单推拉后，导致正单明细退款数量未更新 问题已找到，解决方案待确定。 4. B2C管理平台运维。 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 店铺标签（联调中） 2. 处方订单流程优化（开发10%） 3. B2C海典H1全额仅退款下账缺少0.01赠品金额 已上线 4. 线上问题处理 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 动态路由网关转发dev,test已完成 2. 智能客服需求变更接口，自测中 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |