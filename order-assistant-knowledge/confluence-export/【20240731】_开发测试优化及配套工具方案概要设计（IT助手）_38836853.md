# 【20240731】 开发测试优化及配套工具方案概要设计（IT助手）

## 一、背景

### 1.1 业务背景

 测试环境验不出，线上事故频繁出，多次重复发布打补丁，严重影响生产环境的正常使用，浪费很多时间去修复错误单数据。

### 1.2 痛点分析

1. 部分场景在测试环境没有条件测试
  1. 部分平台没有测试店铺和全种类型商品
2. 部分平台没有测试店铺和全种类型商品
3. 场景太多，在测试时间内无法全部覆盖，覆盖测试自测和测试成本极高
  1. **没有做自动化测试**
  2. 数据依赖于三方平台，无法做自动化测试用例
4. **没有做自动化测试**
5. 数据依赖于三方平台，无法做自动化测试用例
6. 部分场景下复现事故流程复杂
  1. 订单不可重复使用
  2. 订单流程不可逆转
7. 订单不可重复使用
8. 订单流程不可逆转


#### 1.2.2 O2O订单复杂度

订单里的数据表很多，关联数据很多，在业务场景不熟悉时，较难通过手动修改数据库数据方式回滚到期望的业务状态。

商品种类： 普通，拆零，组合，换货，赠品

订单商品：一个品，多个品，一品多个，多品多个

订单类型：处方单，退款单，导入单，手工单，拆分单，补发单

订单状态：待处理，待接单，待拣货，待配送，配送中，已完成，已取消，已关闭

商品明细状态：正常， 缺货，商品不存在， 已换货，已退货

下账状态：待锁定，待下账，下账失败，已下账，已取消，已退款

平台：微商城，京东到家，饿百，美团，抖音

下账平台：科传，海典

核心数据：

### 1.3 系统现状

 我们的数据流都可以抽象如图所示，测试的数据来源主要分为4种方式，【基础配置数据】、【外部接口回调】、【MQ触发】、【调用外部服务】获取数据。

true2falseautotoptrue54119

部分流程触发高度依赖于外部服务，且操作不可逆，数据不可逆，无法重复业务操作。

这造成了很难进行自动化测试。

true4falseautotoptrue6012

## 二、目标

### 2.1 本期目标

解决外部数据不可控，内部部分业务不可重复执行问题。

true8falseautotoptrue4913

## 三、整体设计

 对上面的问题的核心是因为外部数据不可控，内部数据不可回滚。只要解决这两个问题再引入自动化测试，提及的问题都可以得到解决。

为此引入了【**模拟服务**】和【**数据快照**】能力，解决外部数据不可控，内部数据不可回滚。为了动态接入模拟服务，引入了【**动态路由**】功能。

【**模拟服务**】提供外部数据支撑；【**数据快照**】提供基于业务的数据回滚能力；【**动态路由**】允许发起端控制链路走向到指定服务。

整个链路我们都可以控制后，开发，联调，测试中的很多东西就变动很容易了。

true5falseautotoptrue4915

### 3.1 业务架构图

true3falseautotoptrue7416

### 3.2 业务能力解析

#### 3.2.1 测试数据积累（测试角度）

随着测试和开发的持续使用，里面会积累大量的用例数据场景，异常场景形成测试的数据资产。

#### 3.2.2 模拟数据可重复使用（开发角度）

解决三方数据难造的情况。

例如：测试在虚拟服务中配置了一个订单全流程数据，开发在自测时也可以使用

开发自测阶段，基于模拟服务中的数据可以完成全种类订单的覆盖测试，也可以选择某个订单类型数据进行单元测试，不再需要去下单或者抓取测试环境数据模拟MQ消息了。

#### 3.2.3 重复执行不可逆操作（开发角度）

开发自测阶段，执行单元测试后，会有多张表数据被修改，人为修改这些数据很麻烦，可以通过数据快照将数据快速回滚到执行单元测试之前的状态。

#### 3.2.4 全场景测试（测试角度）

使用模拟服务中积累的测试数据，通过自动化测试用例做全覆盖回滚测试。

#### 3.2.5 动态联调（开发角度）

开发联调阶段，通常都是发到开发环境进行联调，如果前端发现接口有bug。

true6falseautotoptrue12794

#### 3.2.6 操作界面本地自测（开发角度）

开发/自测/联调/阶段，后端同学指定动态路由到本地，通过网页直接访问本地服务，完成自测工作。

#### 3.2.7 简化自动化测试（测试角度）

自动化测试在我们系统中存在两座大山，外部服务不可控和【重复性原则】。外部服务不可控可以通过【**模拟服务**】变成可控服务，【**数据快照**】可以让测试用例不用考虑可重复性原则，简化自动化测试用例工作量。

可重复性原则：软件测试应设计为可以重复执行。测试用例应该独立于任何特定环境或时间，以便可以在任何时间、任何地点进行重复测试。

#### 3.2.8 场景重放（开发角度）

有时候，一些bug是经过各步骤操作和回调造成的，这时想要本地复现问题是一件麻烦的事情。

例子：测试环境中特殊多次复杂换货后(A换2个B，1个B换1个C....），进行部分退款，有bug，发现换货数据被清空了。

测试告知订单等数据，开发回滚数据到部分退款前，抓取部分退款数据(从模拟服务中获取退款数据)，发起部分退款并路由到本地执行复现场景。

#### 3.2.9 操作数据分析（开发角度）

在一些偶现场景中，经常会发出感慨，在所有操作和数据都一样的情况下，为什么有的时候这个数据是对的有的时候是错的。

在记录数据快照时，一并记录，请求ip，执行线程，栈，修改数据值等信息，可以帮助分析定位问题。

## 四、组件概要设计

### 4.1 动态路由

true9falseautotoptrue2011

#### 4.1.2 实现原理

true7falseautotoptrue6716

#### 4.1.3 零侵入性设计方案

服务间的数据请求都是通过Gateway, FeignClient, ThirdHttpClient 来进行请求了，对针对他们做请求拦截并根据请求header中的路由信息进行数据转发就可以实现动态路由的能力。

Gateway: 通过全局Filter来实现动态路由

Feign: 通过动态路由接口实现。兼容Grey功能，在没有配置动态路由时走Grey逻辑。在基础框架包实现动态路由功能的注入和注解动态路由url能力。

ThirdClient: 

对于客户端组件是okHttp，Apache HttpClient的，如果注入到了spring中，自动注入路由拦截器。其他的

为每一个三方客户端获取url的getUrl()添加注解，动态修改url返回。每个服务单独使用注解实现动态路由功能。

通过javaassist对已知客户端进行路由代码植入。

#### 4.1.3 路由数据传递

Gateway: 放到请求头中向下传递

Feign: 放到请求头中向下传递

ThridClient: 路由时能修改请求头的就放到请求头，不能的就放到url里 ，下游对url进行解析后放到线程上下文中。

#### 4.1.4 应用内部路由数据传递

零侵入方式实现异步线程上下文传递，通过动态代理植入包装Runnable和Callable实现异步线程上下文传递(限定我们自己创建的由spring管理的线程才会传递上下文)

true14falseautotoptrue4111

| ``` @Around("execution(* java.util.concurrent.ThreadPoolExecutor.execute(..))") public void threadPoolExecutorExecute(ProceedingJoinPoint joinPoint) throws Throwable {     joinPoint.proceed(wrap(joinPoint.getArgs())); } private Object[] wrap(Object[] args) throws SQLException {     if(args[0]==null || args.length==0){         return args;     }     if(args[0] instanceof Callable){         Callable arg = (Callable) args[0];         MDCCallable callable = new MDCCallable(arg);         args[0] = callable;     }else if(args[0] instanceof Runnable) {         Runnable arg = (Runnable) args[0];         MDCRunable callable = new MDCRunable(arg);         args[0] = callable;     }     return args; } ``` |
| --- |


| ``` public class MDCRunable implements Runnable {     private Map<String, String> copyOfContextMap;     private Runnable runnable;     public MDCRunable(Runnable runnable) {         this.copyOfContextMap = MDC.getCopyOfContextMap();         this.runnable = runnable;     }     @Override     public void run() {         if (copyOfContextMap!=null && !copyOfContextMap.isEmpty()) {             MDC.setContextMap(copyOfContextMap);         }         try {             runnable.run();         } finally {             if (copyOfContextMap!=null && !copyOfContextMap.isEmpty()) {                 MDC.clear();             }         }     } } ``` |
| --- |


#### 4.1.3 深度路由

在Gateway和Feign调用三方服务时，将header中的路由信息向下传递。

#### 4.1.4 指定ip/url路由

拦截请求的ip/url后直接替换成配置的地址信息

#### 4.1.5 应用名称路由

通过配置的应用名称从nacos上获取ip，替换原ip实现路由

#### 4.1.6 兼容grey

拦截在grey处理之后执行，如果配置了动态路由就覆盖grey的配置。

### 4.2 数据快照

#### 4.2.1 业务流程

##### ******* 更新操作

true11falseautotoptrue5615

##### ******* 回滚操作

true15falseautotoptrue5942

#### 4.2.2 技术原理

为了实现指定业务数据回滚，我们需要在提交前就需要将更新前的数据记录下来。通过代理方式实现对sql执行过程的拦截，并对数据进行记录。

借鉴seata AT模式中的undolog记录和回滚方式，添加额外的traceId,执行时间，执行栈，执行线程名称，操作人id等信息，以实现指定维度的数据回滚。

通过**DataSourceProxy**(DataSource datasource) 实现对sql执行的监听，并记录相关参数到【**image_log**】中。

| 业务接口 | 代理实现 |
| --- | --- |
| DataSource | ProxyDataSource |
| Connection | ProxyConnection |
| Statement | ProxyStatement |
| PrepareStatement | ProxyPrepareStatement |


true16falseautotoptrue5421

### 4.3 模拟服务

#### 4.3.1 功能图

true13falseautotoptrue9117

#### 4.3.2 获取虚拟数据接口执行流程

true17falseautotoptrue8125

### 4.4 其他

true1falseautotoptrue62110