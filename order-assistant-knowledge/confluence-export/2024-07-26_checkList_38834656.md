# 2024-07-26 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| B2C订单汇总/自动拣货 | ``` hydee-business-order-web ``` | 1 | ``` feature/ORDER-1540/part-refund ``` | 杨国枫、杨花 | 徐国华 | 徐凯 |  |  | 20240726 | 20240726 |
| B2C订单汇总/自动拣货 | ``` hydee-business-order-b2c-third ``` | 2 | feature/order-1984/auto request goods | 杨国枫、杨花 | 徐国华 | 徐凯 |  |  | 20240726 | 20240726 |


### 二、配置变更

#### 2.1 数据库变更

| ALTER TABLE `dscloud`.`oms_order_info`  ADD COLUMN `is_procurement_erp` tinyint(1) NULL COMMENT 'D-ERP清货状态：0-未清货 1已清货' AFTER `deleted`, ALGORITHM=INPLACE,LOCK=NONE ;ALTER TABLE `dscloud`.`oms_order_info`  ADD COLUMN `procurement_no` bigint NULL COMMENT '请货单关联字段' AFTER `is_procurement_erp`, ALGORITHM=INPLACE,LOCK=NONE ; ALTER TABLE `dscloud`.`oms_order_info`  ADD INDEX `idx_procurement_no`(`procurement_no`) USING BTREE, ALGORITHM=INPLACE;  CREATE TABLE `order_procurement` (  `id` bigint NOT NULL AUTO_INCREMENT,  `procurement_status` tinyint NOT NULL COMMENT '请货单状态：0.待请货 1.请货中 2.门店签收 3.异常',  `procurement_no` bigint NOT NULL COMMENT '请货单系统生成',  `erp_shipping_order` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'D-ERP铺货单号 D-ERP-回传',  `shipping_order` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出货单号（多个,隔开）D-ERP-回传',  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注（可能用于显示报错）',  `isvalid` smallint NOT NULL DEFAULT '1' COMMENT 'isvalid 是否删除 1-未删除， !=1已删除''',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `version` int NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  UNIQUE KEY `idx_procurement_no` (`procurement_no`),  KEY `idx_erp_shipping_order` (`erp_shipping_order`) USING BTREE,  KEY `idx_created_time` (`created_time`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C订单请货单';  CREATE TABLE `order_procurement_goods` (  `id` bigint NOT NULL AUTO_INCREMENT,  `erp_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编码',  `procurement_no` bigint NOT NULL COMMENT '请货单系统生成',  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称(厂商+规格)',  `actual_request_count` int NOT NULL COMMENT '实际请货数量',  `request_count` int NOT NULL COMMENT '请货数量',  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注（可能用于显示报错）',  `isvalid` smallint NOT NULL DEFAULT '1' COMMENT 'isvalid 是否删除 1-未删除， !=1已删除''',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `version` int NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_procurement_no` (`procurement_no`) USING BTREE,  KEY `idx_created_time` (`created_time`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1816749739128537092 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C订单请货单商品明细'; |
| --- |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-business-order-web | application.yml | ``` storeCodeMap: '{   "J034": "D107",   "C503": "D102",   "K019": "D108",   "K018": "D108",   "K023": "D108",   "G793": "D115",   "GZ16": "D112",   "H873": "D106",   "M544": "D109",   "F338": "D105",   "B999": "D101",   "N015": "D110",   "NY69": "D118" }'         ``` |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  | }, |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 complete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 complete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |