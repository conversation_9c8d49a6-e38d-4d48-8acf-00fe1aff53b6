# 2025-01-16 一件代发cheklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 一件代发 | ydjia-merchant-customer |  | feature/ORDER-3448/self-support |  |  |  |  |  |  |  |
| hydee-middle-order |  |  |  |  |  |  |
| order-split-server |  |  |  |  |  |
| middle-datasync-message |  |  |  |  |  |
| hydee-business-order |  |  |  |  |  |
| hydee-business-order-web |  |  |  |  |  |
| hydee-business-order-b2c-third |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| middle-order | ``` ALTER TABLE order_info ADD main_order_id BIGINT NULL COMMENT '关联主订单表编号', ALGORITHM = INPLACE, LOCK = NONE; ``` |  |
| ``` ALTER TABLE order_info ADD is_split TINYINT DEFAULT 0 NULL COMMENT '是否拆过单 0:否 1:是', ALGORITHM = INPLACE, LOCK = NONE; ``` |  |
| ``` ALTER TABLE order_detail ADD sp_code VARCHAR ( 20 ) NULL COMMENT '供应商编码', ALGORITHM = INPLACE, LOCK = NONE; ``` |  |
| dscloud | ``` INSERT INTO dscloud.dict_order_export_column (service_type, column_code, column_name, seq, status, create_time,                                               modify_time, `require`, need_merge, value_mapping, default_choose) VALUES (6, 'batchNo', '商品批号', 81, 1, DEFAULT, DEFAULT, 0, 0, null, 1) ``` |  |
| ``` INSERT INTO dscloud.code_value (type, code, value_desc) VALUES ('ORDER_TAG', '7', '代发单'); ``` |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| order-split-server | server:  port: 8080 spring:  application:  name: order-split-server  main:  allow-bean-definition-overriding: true  allow-circular-references: true  cloud:  nacos:  discovery:  server-addr: [http://sk-prod-nacos.nacos.cse.com:8848](http://sk-prod-nacos.nacos.cse.com:8848);  namespace: d04f590f-6926-4e84-becd-a62f369686a2  metadata:  department: NR  register-enabled: true  datasource:  dynamic:  primary: order_master  strict: false  datasource:  order_master:  url: jdbc:[mysql://10.100.5.198:3306/middle_order?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true&connectTimeout=2000&socketTimeout=60000](mysql://10.100.5.198:3306/middle_order?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true&connectTimeout=2000&socketTimeout=60000)  username: order_agent  password: ${myEncrypt.des(9eaa3fa45a7b7c1da0ee07b0206bdcc5d809e27dbd7f883e)}  driver-class-name: com.mysql.cj.jdbc.Driver  druid:  name: order_master  max-active: 100  initial-size: 40  min-idle: 40  max-wait: 10000  time-between-eviction-runs-millis: 60000  min-evictable-idle-time-millis: 300000  order_slave:  url:jdbc:[mysql://10.100.5.151:3306/middle_order?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true&connectTimeout=2000&socketTimeout=60000](mysql://10.100.5.151:3306/middle_order?allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false&rewriteBatchedStatements=true&connectTimeout=2000&socketTimeout=60000)  username: order_agent  password: ${myEncrypt.des(9eaa3fa45a7b7c1da0ee07b0206bdcc5d809e27dbd7f883e)}  driver-class-name: com.mysql.cj.jdbc.Driver  druid:  name: order_slave  max-active: 100  initial-size: 40  min-idle: 40  max-wait: 10000  time-between-eviction-runs-millis: 60000  min-evictable-idle-time-millis: 300000  druid:  wall:  multi-statement-allow: true  max-evictable-idle-time-millis: 588000  min-evictable-idle-time-millis: 488000  druid:  web-stat-filter:  enabled: true  url-pattern: /${api.version}/*  stat-view-servlet:  enabled: true  url-pattern: /druid/*  jackson:  time-zone: Asia/Shanghai  date-format: yyyy-MM-dd HH:mm:ss  servlet:  multipart:  max-file-size: 10MB  max-request-size: 100MB mq:  topic:  # 消费消息  consumer:  # 生产消息  producer:  order-status: TOPIC_MIDDLE_ORDER_DATA_PRO  event: message-notify:  order-update-topic: GROUP_ORDER_UPDATE_MESSAGE_PRO rocketmq:  producer:  group: PGROUP_${[spring.application.name](http://spring.application.name)}_${spring.profiles.active}  name-server: ************:8100;************:8100management:  endpoint:  mappings:  enabled: true  httptrace:  enabled: true  endpoints:  web:  exposure:  include: [ "*" ]  health:  mongo:  enabled: false  metrics:  distribution:  percentiles-histogram[http.server.requests]: true  maximum-expected-value[http.server.requests]: 10000 #预期最大值  minimum-expected-value[http.server.requests]: 1 #预期最小值swagger:  enable: trueweb-log-filter:  excluded-ant-pattern-uris:  - /file/_upload* feign:  hystrix:  enabled: false  okhttp:  enabled: true  client:  config:  default:  connectTimeout: 60000  readTimeout: 60000  loggerLevel: full  customer-config:  connectTimeout: 6000  readTimeout: 6000  loggerLevel: full ribbon:  ConnectTimeout: 60000  ReadTimeout: 60000  MaxAutoRetries: 0  MaxAutoRetriesNextServer: 0 hystrix:  command:  default:  execution:  isolation:  thread:  strategy: SEMAPHORE  timeoutInMilliseconds: 60000weixin:  robot-url: [https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb](https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb) # 企业微信机器人地址alarm:  robot:  # 是否开启机器人告警，默认开启；非必填  enable: true  # 值班人手机号，英文逗号分隔；非必填  oncallMobile: 17710036783,17302856015# 线程池配置 threadpool:  asyncCoreSize: 8  asyncMaxSize: 48  asyncKeepAliveTime: 0  asyncCapacity: 128mybatis-plus:  configuration:  cache-enabled: false  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  global-config:  db-config:  update-strategy: not_null |  |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |
|  |  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
|  | ```  ``` |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |