# 999(废弃)4.4 快速运维

****

# 一、服务监控

**详见：**[服务性能监控](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/zinw7FOIz/kubernetes-compute-resources-workload-ying-yong-wei-du?orgId=1&refresh=10s&var-datasource=Prometheus&var-cluster=&var-namespace=prod&var-type=deployment&var-workload=middle-yxt-assist-task-server-deployment&from=now-3h&to=now)

# 二、JVM监控

**详见：**[JVM监控](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/Q-eBJP4Sz/jvm-micrometer-jian-kong-da-pan?orgId=1&refresh=30s&var-namespace=prod&var-application=middle-yxt-assist-task-server-service&var-instance=10.100.32.197:8080&var-jvm_memory_pool_heap=All&var-jvm_memory_pool_nonheap=All&var-jvm_buffer_pool=All&from=now-1h&to=now)

# 三、接口监控

**详见：**[接口监控](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/EG1KNOVIk/xin-ling-shou-wei-fu-wu-jie-kou-jian-kong?orgId=1&var-namespace=prod&var-application=middle-yxt-assist-task-server-service&var-instance=All&var-uri=All&from=now-30m&to=now)

# 四、数据库实例监控

**详见：**[数据库监控](https://auth.huaweicloud.com/authui/login.html?id=hw76304849#/login)（lts-logs / %To5Ymflh1）

# 五、慢查询监控

**详见：**[慢查询](https://auth.huaweicloud.com/authui/login.html?id=hw76304849#/login)（lts-logs / %To5Ymflh1）

# 六、RocketMQ监控

## 6.1 grafana监控

**详见：**[RocketMQ消息队列监控](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/WQS4SOFMz/rocketmqxiao-xi-dui-lie-jian-kong?orgId=1)

## 6.2 华为云监控

**详见：**[RocketMQ监控](https://auth.huaweicloud.com/authui/login.html?id=hw76304849#/login)（lts-logs / %To5Ymflh1）