# 25年第16周2025-04-25

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
|  | 微商城-交易链路切换 | 交易中台    支付中台 | 暂停 |  |
|  | 微商城-小前台能力替换 |  | 开发ing |  |
|  | 新老模型,数据双向同步 | 开发ing1. 同步框架-90% 2. 老模型 → 新模型   1. 正单-5%   2. 逆单-0% 3. 正单-5% 4. 逆单-0% | 1. 老模型 → 新模型   1. 正单-10%   2. 逆单-0% 2. 正单-10% 3. 逆单-0% |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** 1.微商城-小前台能力替换技术方案设计 具体接口 流程2. 推广记录导出问题3.山西请货问题4.配合商品调整库存占用接口 | **遗留问题** **风险问题** | **需求研发**迁移整合微商城B2C正向下单流程到 hydee-business-order 不在转发MQ到hydee-business-order-web创单**技术建设** |  |
| 2 |  | **本周总工时：5d**1. 线下单管理后台提测，Bug修复。已上线 2. 会员消费记录接口添加追溯码、批号、拣货信息。已提测 3. 科传线下订单处理公司code,实时获取。已上线。科传历史数据处理在数据迁移之后统一处理。 4. 优雅发布三期配合会员、商品组接入，问题解决。 5. 海典线下订单对账功能开发 10% 。开发中 | **遗留问题** **风险问题** **海典线下订单对账功能**周一产品调低优先级，处理其他需求。排期顺延到下周 | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：4d**1.医保标识 2d 已上线2.采购中台 1.5d 调研以及文档，目前刚开始搭建项目3.其他问题处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. 订单模型同步开发   1. 同步框架-90%   2. 老模型 → 新模型     1. 正单-5%   3. 正单-5%   1. 正单-5% 2. 同步框架-90% 3. 老模型 → 新模型   1. 正单-5% 4. 正单-5% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 一心到家电子小票开发+测试 2. 评价中台联调 3. 评价中台小程序码支持下载功能 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 6 |  | **本周总工时：5d**1.异业联盟功能开发 80%2.用药福利bug修复 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1. 订单配置中心项目开发 70% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 对账服务联调 2. 京东到家下账金额小于0.01元订单下账失败Bug修复 3. B2C 2024-09 ~ 2025-03 财务数据支持 4. 抖店处方单接入（50%） 5. sonar扫描接入（Business-order已完成） | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：4d**1. ai相关技术调研 2. JDK新特性 3. ai 对话聊天前端代码编写 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) | 无 |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 | [ORDER-4893](https://jira.hxyxt.com/browse/ORDER-4893) |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |