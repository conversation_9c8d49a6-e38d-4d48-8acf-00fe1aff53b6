# 【20241017】付费会员

# 背景

## 业务背景

- 随着药店密集度不断提升，竞争越来越大，客流有下滑趋势。为激活会员存量，提升老用户的复购率；公司开始推出付费会员模式，通过付费会员卡付费办卡来满足特定会员的需求，强化顾客对品牌的感知和认同，促进会员购买行为向高频转变。
- 心云当前具有付费会员开通/续费的线上流程，能够满足会员通过“一心到家”进行付费会员的开通及续费但是不能
- 当前流程是通过支付订单来完成开通/续费，不支持下账。且退款时，需要运营在支付订单中手动输入单号进行退款，且退款后系统并为联动即不支持取消身份标识、作废权益
- 为兼容付费会员购买、优惠券购买、体检服务购买等虚拟商品得购买流程，订单需兼容此类虚拟商品的特殊订单流程业务。


## 系统现状

业务现有流程图、架构等


# 需求分析

## 业务流程

## 需求功能

|  | 功能点 | 功能说明 | 优先级 |
| --- | --- | --- | --- |
| 1 | 新增《付费会员》下单流程 | - 新增《付费会员》订单下单流程 -新增-邀请码 - 支付成功通知会员-MQ |  |
| 2 | 新增《付费会员》退款流程 | - 新增《付费会员》退款流程 - 退款结果通知会员-MQ |  |
| 3 | 原微商城订单下单流程改造 | 原订单下单流程新增—心钻会员价优惠原订单下单流程新增—心钻折上折优惠原订单下单流程新增—推广折扣优惠``` 会员查询订单信息接口改造/order-query/queryOrderPush ``` |  |
| 4 | 原结算流程改造 | 新增界面展示心砖标识价格展示 |  |
| 5 | 020订单新增逻辑调整 | 优惠透传 |  |
| 6 | 020订单查询逻辑调整 | 详情界面查询新增优惠字段 |  |
| 7 | 020订单导出新增字段 | 导出新增优惠字段 |  |


# 目标

## 本期目标

### 业务目标

### 技术目标

一般是技术指标（SLA）、沉淀基础服务、节约开发成本等

## 中长期目标

 a. 建设完全

# 整体设计

整体业务流程图

项目结构

## 统一语言定义

*业务、技术名词解释等*

# 详细设计

## 模块详细设计

*分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比。1*

创建付费会员正向订单流程

true付费会员开通流程falseautotoptrue270113

创建付费会员逆向订单流程

true会员中台申请付费会员退款falseautotoptrue11363

付费订单退款审核流程

true审核同意退款通知会员falseautotoptrue13217

原微商城订单下单逻辑调整

true原订单流程改造falseautotoptrue15917

原购物车核心流程图

true原购物车查询详情falseautotoptrue15315

原订单结算流程

true原订单结算流程falseautotoptrue15213

原订单下单流程

true原下单流程falseautotoptrue17613

*新增、修改的字段DDL；索引设计；数据量预估，分库分表设计；有时需要存储选型分析方案：缓存、es等。*

| 表名 | sql |
| --- | --- |
| `middle_order`.`order_detail` | ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `vip_discount` decimal(16, 4) NULL COMMENT '心钻会员价优惠' AFTER `expiring`, ADD COLUMN `vip_discount_plus` decimal(16, 4) NULL COMMENT '心钻折上折优惠' AFTER `vip_discount`, ALGORITHM = INPLACE, LOCK = NONE; |
| `middle_order`.`order_info` | ALTER TABLE `middle_order`.`order_info`  ADD COLUMN `invitation_code` varchar(40) NULL COMMENT '付费会员邀请码' AFTER `seller_remark`, ALGORITHM = INPLACE, LOCK = NONE; |


## 接口设计

*新增、修改的接口定义；流量预估，接口性能设计。*

1.新增付费单-正向

前置校验VIP订单

[http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/isExistVipOrderUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/isExistVipOrderUsingPOST)

保存VIP订单信息-提交订单

[http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/addVipOrderUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/addVipOrderUsingPOST)

2.新增付费单-逆向

3.原下单流程改造

4.原结算流程改造

## 安全设计

*时刻警惕资损问题；数据一致性、接口防刷、幂等设计等。*

## 监控报警

*需要思考上线后如何监控，及时响应止损、回滚、降级等方案。*

## 质量效率

*本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。*

项目开发规范、项目结构按照订单重构DDD方式进行

参考：

# 里程碑

备注：日期统一使用日期组件进行填写；如无特殊情况，里程碑日期、里程碑起止时间、工时等都需要填写。

|  | **里程碑** | 里程碑日期（填写完成日期） | 里程碑起止时间 | 工时(pd) | 备注 |
| --- | --- | --- | --- | --- | --- |
| 开始日期 | 结束日期 |
| 1 | PRD评审 |  |  |  |  |  |
| 2 | 技术方案-设计 |  |  |  |  |  |
| 3 | 技术方案-评审 |  |  |  |  |  |
| 4 | 研发 |  |  |  |  |  |
| 5 | 自测 |  |  |  |  |  |
| 6 | 联调 |  |  |  |  |  |
| 7 | 提测 |  |  |  |  |  |
| 8 | 测试 |  |  |  |  |  |
| 9 | 上线 |  |  |  |  |  |


# 项目排期

| 1 | 新增付费单-正向 |  |  |  |  |  |  |
| 2 | 新增付费单-逆向 |  |  |  |  |  |  |
| 3 | 原下单流程改造 |  |  |  |  |  |  |
| 4 | 原结算流程改造 |  |  |  |  |  |  |
| 5 | O2O订单新增逻辑调整 | 优惠透传 |  |  |  |  |  |
| 6 | O2O订单查询逻辑调整 | 详情界面查询新增优惠字段 |  |  |  |  |  |
| 7 | 订单导出新增字段 | 导出新增优惠字段 |  |  |  |  |  |
| 8 | 功能自测 |  |  |  |  |  |  |
| 9 | 联调 | 1.内部联调 |  |  |  |  |  |
|  | 功能模块 | 功能项 | 优先级 | 工时(PD) | 主R | 计划完成日期 | 研发进展 |


# 上线方案

备注：技术方案设计期间，需要提前建立好需求对应的提测清单（必须）、上线清单（可选）文档。

|  | 清单项 | 清单地址 | 说明 |
| --- | --- | --- | --- |
| 1 | 提测清单 |  | 1. 提测清单建立为技术方案的子文档，命名为：xxx(通常为需求名称)-提测清单。 2. 提测前RD需要及时维护最新信息，并识别不同环境之间的差异。 |
| 2 | 上线清单 |  |  |