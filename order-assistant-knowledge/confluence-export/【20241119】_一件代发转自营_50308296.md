# 【20241119】 一件代发转自营

# 一、业务背景

## 1.1 业务背景

一心到家是商家模式，所以只能销售一心堂的商品；但由于云仓每个月有1500万的销售，所以云仓业务必须要开展；

那么销售的模式则需要变更成一心堂自营。这些商品走先销后采的逻辑；但由于宣称自营，则商品需要信息流需要走一心堂走，所以需要走一个采购入库和出库的流程；

如果全是一心堂入库出库那么效率会很低，所以这些云仓服务还是需要走云仓后台进行发货；

## 1.2 痛点分析

1. 一心到家平台无法申请电商平台资质，所以服务商的商品无法单独拆开来卖。
2. 信息流未传至下游。


## 1.3 系统现状

目前系统的云仓商品都由单独的云仓店来管理，并且不将信息传至下游;

# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 代发商品需要打上对应到标记,同时标明商品的服务商编码,并且需要在旗舰店上架并展示于小程序页面上;
2. 当自营商品与代发商品一同被下单时,需要实现根据不同服务商进行订单的拆分;
3. 拆分后的订单需要生成对应的自营订单和一件代发订并且完成履约和发货;
4. 订单拆单后，增加仓库显示；
5. 退货退款订单增加退款地址；


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

1. 订单正向流程true一心到家正向下单流程falseautotoptrue19212
2. 订单中台发货同步一心到家流程true订单中台发货同步一心到家流程falseautotoptrue13212
3. 一心到家订单申请仅退款流程true一心到家退款流程falseautotoptrue11113
4. 一心到家订单申请退货退款流程true一心到家O2O退货退款流程falseautotoptrue11112


# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.3.1 前端交互接口

1. **一心到家获取订单详情:****http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/getEncryptSubOrderInfoUsingPOST**
2. **获取服务商简单信息: http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/order-info-controller/getMerchantInfoUsingGET**
3. **服务商后台获取订单详情: https://test-sp.hxyxt.com/businesses-gateway/b2c/1.0/order/detail/1816387809219451143?_t=0.5740383201568877**
4. **服务商后台发货管理：https://test-sp.hxyxt.com/businesses-gateway/b2c/1.0/batchShip/singleShip/expressNos**


### 5.3.2 纯后端接口

1. **查询服务商详细信息:http://hydee-middle-baseinfo.svc.k8s.pro.hxyxt.com/doc.html#/default/merchant-controller/queryMerchantByCodeUsingPOST**


## 5.3 涉及数据库

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等