# 【20240704】B2C订单优化（对接D-ERP自动请货）

# 一、背景

## 1.1 业务背景

目前请货，依旧需要很长时间，需要增加自动D-ERP请货的流程；

## 1.2 痛点分析

1. D-ERP回传消息为延迟回传,并且不为一次性回传,自动请货流程被拉长;
2. 请货回来后,发生了退款,需要手动退还商品


## 1.3 系统现状

需要工作人员手动生成请货单,根据请货单人工请货,流程复杂且效率慢;

# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 手动查询需要请货的订单,确认生成请货单自动到D-ERP请货;
  1. 请货单的权限按照仓库查询;
  2. 订单做只允许请货一次的限制;
2. 请货单的权限按照仓库查询;
3. 订单做只允许请货一次的限制;
4. 接受D-ERP回传的请货单状态和对应的批号;
5. OMS缺货或是商品不存在直接清除异常,转到待发货


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

**1.B2C订单自动请货流程图**

true自动请货流程图falseautotoptrue10574

生成请货单逻辑修改

true未命名绘图falseautotoptrue4311

确认请货

| 方案 | 描述 | 优缺 |
| --- | --- | --- |
| 1 | 确认请货：根据procurement_no读取redis获取生成的拣货汇总明细调用DERP请货 | 优点：编码简单 缺点：不知道放入redis的数据的过期时间 |
| 2（采用） | 确认请货：读取文档中需要请货的数据调用DERP请货 | 优点：实时读取 缺点：编码时间 |


# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.3.1 前端交互接口

**1. 请货单日志**

**https://merchants.hxyxt.com/businesses-gateway/b2c/1.0/exportRecord/search_requestOrder**

**procurementNo**

**2.确认请货 procurementNo**

### 5.3.1 对接DERP

**1. 确认请货**

{
 "procurementNo": 0,
 "organizationCode": "",
 "erpCodeList": []
}

返回铺货单号erpShippingOrder D-ERP铺货单号 D-ERP-回传

**1. 状态回传**

{
 "procurementNo": 0,
 "procurementStatus": ""

 "shippingOrder": ""
}

3.清除异常

****

## 5.3 涉及数据库

| 序号 | 库名 | DDL语句 |
| --- | --- | --- |
| 1 | dscloud | ALTER TABLE `dscloud`.`oms_order_info`  ADD COLUMN `is_procurement_erp` tinyint(255) NULL COMMENT 'D-ERP清货状态：0-未清货 1已清货' AFTER `deleted`;ALTER TABLE `dscloud`.`oms_order_info`  ADD COLUMN `procurement_no` bigint NULL COMMENT '请货单关联字段' AFTER `is_procurement_erp`; |
|  | dscloud | CREATE TABLE `order_procurement` (  `id` bigint NOT NULL AUTO_INCREMENT,  `procurement_status` tinyint NOT NULL COMMENT '请货单状态：0.待请货 1.请货中 2.门店签收 3.异常',  `procurement_no` bigint NOT NULL COMMENT '请货单系统生成',  `erp_shipping_order` bigint DEFAULT NULL COMMENT 'D-ERP铺货单号 D-ERP-回传',  `shipping_order` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出货单号（多个,隔开）D-ERP-回传',  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注（可能用于显示报错）',  `isvalid` smallint NOT NULL DEFAULT '1' COMMENT 'isvalid 是否删除 1-未删除， !=1已删除''',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `version` int NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  UNIQUE KEY `idx_procurement_no` (`procurement_no`),  KEY `idx_erp_shipping_order` (`erp_shipping_order`) USING BTREE,  KEY `idx_created_time` (`created_time`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C订单请货单'; |
|  |  | CREATE TABLE `order_procurement_goods` (  `id` bigint NOT NULL AUTO_INCREMENT,  `erp_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编码',  `procurement_no` bigint NOT NULL COMMENT '请货单系统生成',  `commodity_name` varchar(0) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称(厂商+规格)',  `actual_request_count` int NOT NULL COMMENT '实际请货数量',  `request_count` int NOT NULL COMMENT '请货数量',  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注（可能用于显示报错）',  `isvalid` smallint NOT NULL DEFAULT '1' COMMENT 'isvalid 是否删除 1-未删除， !=1已删除''',  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `version` int NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_procurement_no` (`procurement_no`) USING BTREE,  KEY `idx_created_time` (`created_time`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C订单请货单商品明细'; |


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年4月3日;**

**研发时间：2024年4月8日-2024年4月12日；**

**联调时间：2024年4月15日-2024年4月18日(含自测)；**

**测试时间：2024年4月19日(提测)；**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等