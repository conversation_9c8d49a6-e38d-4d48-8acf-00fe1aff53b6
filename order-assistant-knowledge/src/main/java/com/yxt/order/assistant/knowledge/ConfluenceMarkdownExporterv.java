package com.yxt.order.assistant.knowledge;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;

@Component
public class ConfluenceMarkdownExporter {

    public static void main(String[] args) {
        new ConfluenceMarkdownExporter("https://yxtcf.hxyxt.com","NDUxMjY5MDIyMDcwOigCdFHosMopCmWokCv3Cz+4jQOV")
            .exportPageToMarkdown("73452478", "output.md");
    }

    private final OkHttpClient client = new OkHttpClient();
    private final String confluenceUrl;
    private final String personalToken;

    public ConfluenceMarkdownExporter(@Value("${confluence.url}") String confluenceUrl,
                                      @Value("${confluence.token}") String personalToken) {
        this.confluenceUrl = confluenceUrl;
        this.personalToken = personalToken;
    }

    public void exportPageToMarkdown(String pageId, String outputFile) {
        String url = confluenceUrl + "/rest/api/content/" + pageId + "?expand=body.storage";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + personalToken)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.err.println("❌ 请求失败: " + response);
                return;
            }
            String body = response.body().string();
            String html = body.split("\\\"value\\\":\\\"")[1].split("\\\",\\\"representation")[0]
                    .replaceAll("\\\\n", "")
                    .replaceAll("\\\\\"", "\"");

            Document doc = Jsoup.parse(html);
            StringBuilder markdown = new StringBuilder();

            for (Element element : doc.body().children()) {
                markdown.append(convertElementToMarkdown(element)).append("\n\n");
            }

            try (FileWriter writer = new FileWriter(outputFile, StandardCharsets.UTF_8)) {
                writer.write(markdown.toString());
                System.out.println("✅ Markdown 导出完成: " + outputFile);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String convertElementToMarkdown(Element element) {
        switch (element.tagName()) {
            case "h1": return "# " + element.text();
            case "h2": return "## " + element.text();
            case "h3": return "### " + element.text();
            case "p": return element.text();
            case "ul":
                StringBuilder sb = new StringBuilder();
                for (Element li : element.select("li")) {
                    sb.append("- ").append(li.text()).append("\n");
                }
                return sb.toString();
            case "ol":
                sb = new StringBuilder();
                int index = 1;
                for (Element li : element.select("li")) {
                    sb.append(index++).append(". ").append(li.text()).append("\n");
                }
                return sb.toString();
            case "a": return "[" + element.text() + "](" + element.attr("href") + ")";
            case "img": return "![](" + element.attr("src") + ")";
            default: return element.text();
        }
    }
}
