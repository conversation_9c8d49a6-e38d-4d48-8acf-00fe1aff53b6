# 测试环境服务部署

| 项目 | 261 complete 测试环境部署并检查   262 complete 否启动成功 | 备注 |
| --- | --- | --- |
| hydee-middle-order | 110 complete     242 complete |  |
| hydee-business-order | 170 complete     243 complete |  |
| hydee-business-order-lua | jenkins无此项目 |  |
| hydee-business-order-ext | 176 complete     244 complete |  |
| hydee-business-order-web | 179 complete     245 complete |  |
| hydee-business-order-b2c-third | 182 complete     246 complete 实际读取的配置是hydee-business-order-web中的,需要将hydee-business-order-b2c-third的配置临时配置到hydee-business-order-web的项目中 |  |
| hydee-third-inside | jenkins无此项目 |  |
| middle-datasync-message | 188 complete     247 complete |  |
| hydee-middle-sdp | 191 complete     248 complete |  |
| hydee-print | jenkins无此项目 |  |
| the3platform-message | jenkins无此项目名,部署的是ydjia-** |  |
| the3platform-adapter | jenkins无此项目名,部署的是ydjia-** |  |
| hecms_aurora | 无需部署 | .net项目 |
| hydee-middle-payment | 206 complete     252 complete |  |
| h3-pay-core | 这是jar包,未操作部署 |  |
| h3-pay-finance | 这是jar包,未操作部署 |  |
| businesses-gateway | 215 complete     255 complete |  |
| hydee-xxl-job | 218 complete     256 complete |  |
| middle-id | 221 complete     257 complete |  |
| hydee-api-gateway | 224 complete     258 complete |  |