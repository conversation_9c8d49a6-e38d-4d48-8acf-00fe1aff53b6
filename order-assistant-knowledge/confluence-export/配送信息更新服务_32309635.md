# 配送信息更新服务

INLINE@startuml
'https://plantuml.com/activity-diagram-beta
title 现有配送信息更新服务活动图

skinparam ConditionEndStyle hline
'垂直模式
!pragma useVerticalIf on

start
:监听骑手状态通知消息;

if (平台类型是否为空) then (不为空)
  :根据deliveryId查询关联的OMS系统订单号;
else (为空)
  end
endif

if (关联关系是否为空) then (不为空)
  :根据orderNo查询deliveryId;
   note
   查询时间最近的一条
   end note
   if (判断查询出的deliveryId是\n否与下发的deliveryId是否相同) then (相同)
     : 根据orderNo查询orderInfo;
   else (不相同)
     end
   endif
else (为空)
   if(平台类型为骑手平台) then (true)
    : 根据deliveryId查询orderInfo;
    else
    : 根据ecType和三方orderId查询orderInfo;
    endif
endif
: 获取最终orderInfo;
if (判断orderInfo是否为空) is (为空) then
  end
else (不为空)
    if(系统订单号加锁) then (失败)
      :加入延迟队列;
      end
    else (成功)
     :复用上述方法查询orderInfo;
     if (orderInfo为空?) is (为空) then
       end
     else (不为空)
       : 查询门店配置信息;
       if(查询订单配送记录) is (订单记录不存在) then
       end
       else if(配送方式为员工自配送) is (员工配送不接受骑手消息) then
       end
       else if(判断配送平台) is (非平台呼叫骑手，但骑手消息由平台发送，忽略) then
       end
     endif
endif
if (是否为京东到家发起的平台配送转自配送消息?) is (是) then
  : 配送方式由平台配送改为自配送;
  : 如果订单状态是待拣货,则不设置配送平台;
  : 订单更新异步消费;
  : 记录订单配送日志;
  : 生成运费单;
  kill
else (否)
  if (判断下发消息的状态码与订单中台的状态?) is (不等于 已取消 已过期) then
  end
  endif
  if (配送方式为商家自配) then
    if (平台为美团/饿百) then
      : 查询门店配置信息;
      : 调用.net同步骑手状态到平台;
    else  (平台为抖店)
         : 查询门店配置信息;
         : 调用the3同步骑手状态到平台;
    end if
  endif
  ->//合并步骤//;
  switch (下发消息的配送状态)
  case (配送中)
    if (心云订单状态大于等于配送中) is (true) then
    :京东到家的自配送订
    单需要更新
    配送记录为配送中;
    note
     平台主动回调,心云状态有可能为待配送
     end note
    kill
    else (false)
        if (获取orderPayInfo) is (为空) then
         end
        else (不为空)
            if(平台是美团 京东到家 抖店 饿了么并且是平台配送) is (不是) then
                :调用.net订单发货接口;
            endif
            if(订单状态大于待拣货) is (是) then
                : 修改订单状态为配送中;
                : 记录订单配送日志;
                : 关联有运费单特殊逻辑处理;
                kill
            endif
        endif
    endif
  :京东健康商家自配送第三方骑手配送时，配送中回传平台;
  if (是否需要自动下账) is (需要) then
    if (平台为京东到家并且交易佣金为0) then
      end
    endif
  else (no)
    if(需要下账但是为未拣货) then
        : 发送MQ声音通知广播;
        note
            销售单待下账
        end note
    endif
  endif
  case (已完成)
    if (订单状态大于已完成) then
      end
    endif
  if(京东健康商家自配) then
    : 查询门店配置信息;
    : 调用.net订单发货接口;
  else if(抖店 饿了么不要调用.net通知第三方) then
    end
  else
    : 调用.net订单妥投接口;
  endif
  if (订单状态大于待拣货) then
    : 订单状态修改为已完成;
    : 订单完成修改成为催促;
    : 记录订单日志;
    : 海典自动下账;
  endif
  : 推送订单完成消息,同步ERP对账结果表订单状态;
  case (待取货)
  : 更新接单时间;
  case (已取消)
  : 使用MQ发送广播通知;
  note
    骑手取消订单
  end note
  if(平台为饿百) then
    : 进入延迟队列转自配送处理;
  endif
  case (异常)
  : 使用MQ发送广播通知;
  note
    骑手订单异常
  end note
  if(平台为饿百) then
      : 进入延迟队列转自配送处理;
    endif
  case (待接单)
    if(平台为京东到家/美团/抖店) then
        : 进入延迟队列转自配送处理;
    endif
  endswitch
  : 订单更新异步消费;
  : 更新订单配送记录;
  : 记录订单配送日志;
: 删除Redis锁;
stop
@enduml