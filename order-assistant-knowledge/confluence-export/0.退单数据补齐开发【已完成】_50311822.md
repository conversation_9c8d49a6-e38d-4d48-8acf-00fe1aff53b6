# 0.退单数据补齐开发【已完成】

**服务**

| 仓库 | 基于如下分支来取 feature-offline-order-20241120上线后, feature-complete-refund-data同步master代码 | 当前需求分支 | 上线前同步master分支 | pr |
| --- | --- | --- | --- | --- |
| order-service | feature-offline-order-20241120 | 4 complete | feature-complete-refund-data | 27 complete | 34 complete |
| order-atom-service | feature-offline-order-20241120 | 5 complete | feature-complete-refund-data | 30 complete | 35 complete |
| order-sync-service | feature-offline-order-20241120 | 6 complete | feature-complete-refund-data | 28 complete | 36 complete |


> feature-offline-order-20241120会在20241128上线

**SDK分支前缀**: completeRefundData

#### SDK版本

  15 complete snapshot   16 complete release  

<dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>completeRefundData-SNAPSHOT</version>
</dependency>

#### Apollo配置

  17 complete dev   18 complete test   19 complete prod  

        offline_refund_order_cashier_desk:
          actual-data-nodes: order-offline.offline_refund_order_cashier_desk_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
        offline_refund_order_organization:
          actual-data-nodes: order-offline.offline_refund_order_organization_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm

# 配置每批次
# 配置年月分表

#### 建表语句

  20 complete dev   21 complete test   22 complete prod  

CREATE TABLE `offline_refund_order_cashier_desk_${seq}` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部退单号,自己生成',
  `pos_cashier_desk_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'pos收银台编码',
  `cashier` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收银员编码',
  `cashier_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收银员姓名',
  `picker` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货员编码',
  `picker_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货员姓名',
  `shift_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '班次',
  `shift_date` datetime DEFAULT NULL COMMENT '班次日期  \r\n',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_refund_no` (`refund_no`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3991 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下退单收银员';
 
CREATE TABLE `offline_refund_order_organization_${seq}` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内部退单号,自己生成',
  `store_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店编码',
  `store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店名称',
  `company_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司Code ',
  `company_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分公司名称',
  `store_direct_join_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店直营加盟类型 DIRECT_SALES-直营 JOIN - 加盟',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_refund_no` (`refund_no`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下退单归属组织';

XXL-JOB配置

  38 complete prod  

completeRefundDataHandler