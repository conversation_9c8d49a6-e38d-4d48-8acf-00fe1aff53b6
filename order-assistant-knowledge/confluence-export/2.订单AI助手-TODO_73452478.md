# 2.订单AI助手-TODO

## 背景

当前，我们的运营、客服、销售等角色在日常订单处理中面临着四大核心痛点：

- **信息查询孤立且低效**: 查询一个订单的完整状态，往往需要客服或运营人员登录并跳转于**订单中台**、**物流中台**、**支付中台**，甚至**售后中台**等多个系统，过程繁琐，耗时良久。
- **跨系统操作流程复杂**: 处理一个复杂的客诉（如“部分商品缺货申请退款，其余正常发货”）需要人工在多个中台系统中按顺序执行一系列操作，极易出错，严重依赖资深员工的“肌肉记忆”。
- **异常处理响应延迟**: 订单异常（如风控预警、物流停滞、库存不足）依赖**订单监控模块**报警后人工介入，响应链路长，处理不及时可能导致客户投诉和业务损失。
- **知识断层与培训成本高**: 新员工需要花费大量时间学习各个系统的操作手册和公司的售后政策、活动规则等。知识更新（如新的促销活动）无法及时同步给所有人。


## 目标

**“订单AI助手”的核心价值主张**在于，通过构建一个基于大型语言模型的智能体（Agent），将我们强大的中台能力整合为一个统一的、可通过自然语言交互的智能接口。

**预期效率提升与ROI:**

- **降低平均处理时长 (AHT)**: 通过自然语言直接完成查询和操作，预计将客服和运营人员处理单个订单的平均时长**降低40%-60%**。
- **提升首次解决率 (FCR)**: AI助手能够一次性获取并整合所有相关信息，提供完整解决方案，预计将首次咨询的解决率**提升30%以上**。
- **降低错误率**: 自动化执行跨系统工作流，可将因人工操作失误导致的业务损失**降低95%**。
- **缩短员工培训周期**: 新员工通过与AI助手互动即可快速上手，培训周期可从数周**缩短至数天**。
- **释放人力，创造更高价值**: 将员工从重复、繁琐的查询和操作中解放出来，专注于更复杂的客户沟通、销售转化和流程优化等高价值工作。


## 功能架构设计

trueFunctional Architecturefalseautotoptrue5511

- A. 自然语言理解 (NLU) 模块: 负责解析用户输入的意图（Intent）和实体（Entities）。例如，从“查一下张三的订单”中识别出意图`query_order`和实体`{user_name: "张三"}`。
- B. 对话管理与记忆核心: 维持对话上下文，理解多轮对话，管理短期记忆（当前对话）和长期记忆（用户偏好）。
- C. 任务规划与执行引擎 (Agent Core): AI助手的“大脑”。接收NLU的意图后，进行思考和规划，决定是直接回答、从知识库检索、还是调用一个或多个工具来完成任务。对于复杂任务，它会编排一个工作流（Workflow）。
- D. 知识库与检索模块 (RAG): 基于检索增强生成（Retrieval-Augmented Generation）技术。当遇到知识性问题（如“7天无理由退货政策是什么？”），它会从向量化的内部知识库中检索最相关的信息，供模型生成准确答案。
- E. 多系统工具调用接口: 定义了一套标准的API schema，让LLM能够“理解”并调用我们所有的中台能力。
- F. 中台系统API工具集: 对现有的订单、物流、支付等中台接口进行封装，使其成为可被AI调用的“工具”。
- G. 向量数据库: 存储所有知识文档（FAQ、产品手册、政策文件）的向量表示，实现毫秒级语义检索。
- H. 内部知识库文档: 公司内部非结构化数据资产。


## 技术架构设计

trueTechnical Architecturefalseautotoptrue6562

- A. 编排服务 : 这是整个系统的中枢神经。它负责管理对话流程、构建包含上下文和可用工具的 Prompt，并解析 Chat Model 的响应。
- B. Chat Model : 它接收编排服务构建的复杂Prompt，并输出思考过程、任务规划（Workflow）以及对一个或多个工具的调用（Tool Calling）请求。
- C. Embedding Model (嵌入模型): 负责将我们的内部文档（如售后政策PDF、商品知识库）转换成高维向量，存入向量存储中。
- D. Vector Store (向量存储): 当用户问题需要背景知识时，编排服务会先将问题向量化，然后在向量数据库中进行相似度搜索，找出最相关的文档片段。这个过程就是RAG。检索到的内容会作为上下文补充到给Chat Model的Prompt中，极大提升了回答的准确性，有效避免了模型幻觉。
- E. Tool Calling (工具调用) 与智能体 (Agent): 这是AI具备行动能力的关键。
  - 定义: 我们会为每个中台的核心能力（如`query_order_status`, `initiate_refund`）定义一个标准的OpenAPI schema。
  - 执行: 当模型决定需要调用工具时，它会生成一段包含函数名和参数的JSON。编排服务会捕获这段JSON，调用相应的工具适配器，执行操作，并将结果返回给模型，让其决定下一步行动或生成最终回复。
  - Agent与Workflow: 一个Agent本质上就是LLM + Tools的组合。我们可以构建多个专业Agent：
    - 查询智能体: 拥有所有`query_*`和`check_*`类的只读工具。
    - 操作智能体: 拥有`initiate_*`和`create_*`等写入权限的工具，并有更严格的权限控制和人类确认环节。
    - 一个复杂请求，如“查一下四川仓库所有未发货的订单，并给这些用户发一条延迟通知”，会被分解成一个工作流 (Workflow)：1. 调用`query_order_status`工具（带多个筛选条件）。 2. 对返回的订单列表进行处理。 3. 循环调用`send_notification`工具。
  - 查询智能体: 拥有所有`query_*`和`check_*`类的只读工具。
  - 操作智能体: 拥有`initiate_*`和`create_*`等写入权限的工具，并有更严格的权限控制和人类确认环节。
  - 一个复杂请求，如“查一下四川仓库所有未发货的订单，并给这些用户发一条延迟通知”，会被分解成一个工作流 (Workflow)：1. 调用`query_order_status`工具（带多个筛选条件）。 2. 对返回的订单列表进行处理。 3. 循环调用`send_notification`工具。
  - 查询智能体: 拥有所有`query_*`和`check_*`类的只读工具。
  - 操作智能体: 拥有`initiate_*`和`create_*`等写入权限的工具，并有更严格的权限控制和人类确认环节。
  - 一个复杂请求，如“查一下四川仓库所有未发货的订单，并给这些用户发一条延迟通知”，会被分解成一个工作流 (Workflow)：1. 调用`query_order_status`工具（带多个筛选条件）。 2. 对返回的订单列表进行处理。 3. 循环调用`send_notification`工具。
- 定义: 我们会为每个中台的核心能力（如`query_order_status`, `initiate_refund`）定义一个标准的OpenAPI schema。
- 执行: 当模型决定需要调用工具时，它会生成一段包含函数名和参数的JSON。编排服务会捕获这段JSON，调用相应的工具适配器，执行操作，并将结果返回给模型，让其决定下一步行动或生成最终回复。
- Agent与Workflow: 一个Agent本质上就是LLM + Tools的组合。我们可以构建多个专业Agent：
  - 查询智能体: 拥有所有`query_*`和`check_*`类的只读工具。
  - 操作智能体: 拥有`initiate_*`和`create_*`等写入权限的工具，并有更严格的权限控制和人类确认环节。
  - 一个复杂请求，如“查一下四川仓库所有未发货的订单，并给这些用户发一条延迟通知”，会被分解成一个工作流 (Workflow)：1. 调用`query_order_status`工具（带多个筛选条件）。 2. 对返回的订单列表进行处理。 3. 循环调用`send_notification`工具。
- 查询智能体: 拥有所有`query_*`和`check_*`类的只读工具。
- 操作智能体: 拥有`initiate_*`和`create_*`等写入权限的工具，并有更严格的权限控制和人类确认环节。
- 一个复杂请求，如“查一下四川仓库所有未发货的订单，并给这些用户发一条延迟通知”，会被分解成一个工作流 (Workflow)：1. 调用`query_order_status`工具（带多个筛选条件）。 2. 对返回的订单列表进行处理。 3. 循环调用`send_notification`工具。


## 核心流程示例

**用户输入:** “帮我查一下昨天张三下的那笔订单为什么还没发货，如果是缺货了，就帮我申请售后并退款。”

**AI处理全流程解析:**

1. **NLU & 意图识别**:
  - AI助手识别出这是一个**复合意图**。
  - **实体提取**: `{date: "昨天", user_name: "张三", task_1: "查询发货状态", condition: "缺货", task_2: "申请售后退款"}`。
2. AI助手识别出这是一个**复合意图**。
3. **实体提取**: `{date: "昨天", user_name: "张三", task_1: "查询发货状态", condition: "缺货", task_2: "申请售后退款"}`。
4. **任务规划 (Workflow Generation)**:
  - **编排服务** 将该请求和对话历史构建成Prompt，发送给AI。
  - **ChatModel**进行思考:
    - *“我需要先找到这个订单。‘张三’可能不是唯一标识，我需要更多信息。但我可以先尝试用姓名和日期查询。”*
    - *“如果找到订单，我需要检查它的物流状态。”*
    - *“如果未发货，我需要检查它的库存分配状态，看是否缺货。”*
    - *“如果确认缺货，我需要调用售后工具创建退款单。”*
    - *“每一步都需要将结果告知用户。”*
  - *“我需要先找到这个订单。‘张三’可能不是唯一标识，我需要更多信息。但我可以先尝试用姓名和日期查询。”*
  - *“如果找到订单，我需要检查它的物流状态。”*
  - *“如果未发货，我需要检查它的库存分配状态，看是否缺货。”*
  - *“如果确认缺货，我需要调用售后工具创建退款单。”*
  - *“每一步都需要将结果告知用户。”*
  - **AI**输出一个执行计划（Workflow），第一步是调用工具。
  - *“我需要先找到这个订单。‘张三’可能不是唯一标识，我需要更多信息。但我可以先尝试用姓名和日期查询。”*
  - *“如果找到订单，我需要检查它的物流状态。”*
  - *“如果未发货，我需要检查它的库存分配状态，看是否缺货。”*
  - *“如果确认缺货，我需要调用售后工具创建退款单。”*
  - *“每一步都需要将结果告知用户。”*
5. **编排服务** 将该请求和对话历史构建成Prompt，发送给AI。
6. **ChatModel**进行思考:
  - *“我需要先找到这个订单。‘张三’可能不是唯一标识，我需要更多信息。但我可以先尝试用姓名和日期查询。”*
  - *“如果找到订单，我需要检查它的物流状态。”*
  - *“如果未发货，我需要检查它的库存分配状态，看是否缺货。”*
  - *“如果确认缺货，我需要调用售后工具创建退款单。”*
  - *“每一步都需要将结果告知用户。”*
7. *“我需要先找到这个订单。‘张三’可能不是唯一标识，我需要更多信息。但我可以先尝试用姓名和日期查询。”*
8. *“如果找到订单，我需要检查它的物流状态。”*
9. *“如果未发货，我需要检查它的库存分配状态，看是否缺货。”*
10. *“如果确认缺货，我需要调用售后工具创建退款单。”*
11. *“每一步都需要将结果告知用户。”*
12. **AI**输出一个执行计划（Workflow），第一步是调用工具。
13. **工具调用 & 信息收集**:
  - **Tool Call 1**: `query_order({user_name: "张三", date_range: "yesterday"})`。
  - **系统返回**: 假设返回了两个“张三”的订单。
  - **与用户澄清**: AI生成回复：“我找到了张三昨天的两笔订单，一笔是购买了iPhone 15，另一笔是购买了AirPods Pro。请问您想查询哪一笔？”
  - **用户回复**: “iPhone 15那笔。”
  - **Tool Call 2**: `check_logistics({order_id: "ORD12345"})`。
  - **系统返回**: `{"status": "未发货"}`。
  - **Tool Call 3**: `check_stock({order_id: "ORD12345"})`。
  - **系统返回**: `{"item": "iPhone 15", "stock_status": "缺货", "eta": "3天"}`。
14. **Tool Call 1**: `query_order({user_name: "张三", date_range: "yesterday"})`。
15. **系统返回**: 假设返回了两个“张三”的订单。
16. **与用户澄清**: AI生成回复：“我找到了张三昨天的两笔订单，一笔是购买了iPhone 15，另一笔是购买了AirPods Pro。请问您想查询哪一笔？”
17. **用户回复**: “iPhone 15那笔。”
18. **Tool Call 2**: `check_logistics({order_id: "ORD12345"})`。
19. **系统返回**: `{"status": "未发货"}`。
20. **Tool Call 3**: `check_stock({order_id: "ORD12345"})`。
21. **系统返回**: `{"item": "iPhone 15", "stock_status": "缺货", "eta": "3天"}`。
22. **知识检索 (RAG)**:
  - AI在规划下一步时，可能会思考：“用户要求退款，我应该了解一下公司的缺货退款政策。”
  - **RAG流程触发**: 将“缺货退款政策”向量化，在**VectorDB**中检索。
  - **检索结果**: "针对缺货订单，我们承诺全额退款，并可能为用户提供一张10元优惠券作为补偿..."。
23. AI在规划下一步时，可能会思考：“用户要求退款，我应该了解一下公司的缺货退款政策。”
24. **RAG流程触发**: 将“缺货退款政策”向量化，在**VectorDB**中检索。
25. **检索结果**: "针对缺货订单，我们承诺全额退款，并可能为用户提供一张10元优惠券作为补偿..."。
26. **执行最终任务 & 生成回复**:
  - **Tool Call 4**: `create_after_sale_ticket({order_id: "ORD12345", type: "refund", reason: "缺货"})`。
  - **系统返回**: `{"ticket_id": "AS67890", "status": "退款处理中"}`。
  - **最终回复生成**: AI整合所有信息，生成一段清晰、人性化的回复：“您好，已为您查询到订单ORD12345（iPhone 15）目前因商品暂时缺货未能发出。根据您的指示，并遵循公司的退款政策，我已经为您成功提交了全额退款申请，售后单号为AS67890，款项预计在1-3个工作日内原路退回。对于此次不便，我们深表歉意。”
27. **Tool Call 4**: `create_after_sale_ticket({order_id: "ORD12345", type: "refund", reason: "缺货"})`。
28. **系统返回**: `{"ticket_id": "AS67890", "status": "退款处理中"}`。
29. **最终回复生成**: AI整合所有信息，生成一段清晰、人性化的回复：“您好，已为您查询到订单ORD12345（iPhone 15）目前因商品暂时缺货未能发出。根据您的指示，并遵循公司的退款政策，我已经为您成功提交了全额退款申请，售后单号为AS67890，款项预计在1-3个工作日内原路退回。对于此次不便，我们深表歉意。”


## 订单助手ChatFlow

结论: 基于Dify做Agent

trueinvoke-chainfalseautotoptrue7212

### 一期

按职能分离、分阶段演进。对于项目启动和MVP（最小可行产品）阶段，建议创建两个核心智能体

trueorder-ai-agentsfalseautotoptrue3113

- 查询类能回答订单状态、物流、支付等问题
- 操作类能执行退款、创建售后单等，带人工确认环节


### 订单助手项目搭建

  2 incomplete todo  

### 二期

 按专业领域细分创建**专家智能体**

trueorder-ai-agents-2falseautotoptrue5811

## 需求

### 功能性需求

| 模块 | 功能点 | 描述 | 优先级 |
| --- | --- | --- | --- |
| **对话交互** | 自然语言理解 (NLU) | 精准识别用户关于订单的各类意图，如查询状态、修改信息、申请售后等。 | **高** |
| 多轮对话管理 | 能够联系上下文，完成需要多步信息确认的复杂任务。 | **高** |
| 主动澄清与引导 | 当用户意图模糊时，能主动提问以澄清需求。 | **中** |
| 多模态输入 | 支持文本、截图（识别订单号）等多种输入方式。 | **二期** |
| **订单查询** | 全景订单视图 | 一句话查询订单的完整信息，整合来自各中台的数据。 | **高** |
| 复杂条件筛选 | 支持多维度、模糊条件的组合查询，如“查询最近一周A002店铺所有待发货的订单”。 | **高** |
| **订单操作** | 信息修改 | 修改订单收货地址、联系方式、备注等。 | **高** |
| 状态变更 | 触发订单审核、取消、发货、标记异常等操作。 | **高** |
| 售后发起 | 辅助用户快速创建退款、换货申请。 | **中** |
| **流程自动化** | 批量操作 | 根据特定规则，批量处理订单，如“将所有超时的订单标记为优先发货”。 | **中** |
| 智能预警 | 基于风控、物流等数据，主动识别并提醒潜在问题订单。 | **二期** |
| **知识问答** | 平台规则查询 | 解答关于运费政策、退换货流程、发票制度等问题。 | **中** |


## 功能示例

| 中台/模块 | 典型自然语言指令 | 对应Agent/工具调用 |
| --- | --- | --- |
| **订单中台** | "查一下订单123456789的物流到哪了？" / "把这50个待发货订单的快递换成顺丰" | `OrderAgent` -> `query_order_detail`, `batch_update_orders` |
| **支付中台** | "用户反馈订单888支付失败，帮我查下原因并创建一个新的支付单" | `PaymentAgent` -> `get_payment_failure_log`, `create_new_payment` |
| **物流中台** | "预测一下上海仓今天发出的这批订单大概什么时候能送到？" | `LogisticsAgent` -> `predict_delivery_time`, `optimize_shipping_route` |
| **客服中台** | "客户在问这个商品的用法，根据知识库生成一个回复" / "用户情绪激动，立即转人工" | `CustomerServiceAgent` -> `rag_query`, `transfer_to_human` |
| **风控中台** | "立刻扫描过去1小时内所有新用户首单金额超过1000的订单，评估风险" | `RiskControlAgent` -> `scan_high_value_orders`, `update_risk_rule` |
| **售后中台** | "给用户A的订单B办理仅退款，原因是商品破损" | `AfterSaleAgent` -> `process_refund_only` |
| **订单拆单** | "这个订单包含多个仓库的货，用成本最低的方式拆一下单" | `OrderAgent` -> `intelligent_split_order` |
| **订单对账** | "对比一下昨天支付宝渠道的交易流水和我们的订单记录，有差异吗？" | `PaymentAgent` -> `reconcile_payment_channel` |


## 二期功能与迭代方向

一期项目成功上线后，我们将立即启动二期规划，向更智能、更主动的方向演进：

- **主动型智能体 (Proactive Agent)**:
  - **异常监控与处理**: AI助手7x24小时监控**订单监控模块**，一旦发现异常（如“超过24小时未揽收的订单”），能主动通知相关运营人员，并提供解决方案建议，甚至在预设规则下自动创建处理任务。
  - **商机发现**: 通过分析订单数据，主动发现潜在的大客户或复购行为，并提醒销售团队跟进。
- **异常监控与处理**: AI助手7x24小时监控**订单监控模块**，一旦发现异常（如“超过24小时未揽收的订单”），能主动通知相关运营人员，并提供解决方案建议，甚至在预设规则下自动创建处理任务。
- **商机发现**: 通过分析订单数据，主动发现潜在的大客户或复购行为，并提醒销售团队跟进。
- **多模态交互升级**:
  - **语音交互**: 允许运营人员通过语音直接与AI助手对话，进一步提升效率。
  - **图片理解**: 全面支持用户上传的各类图片，如商品损坏照片、物流面单照片等，AI能自动识别并归类，触发相应售后流程。
- **语音交互**: 允许运营人员通过语音直接与AI助手对话，进一步提升效率。
- **图片理解**: 全面支持用户上传的各类图片，如商品损坏照片、物流面单照片等，AI能自动识别并归类，触发相应售后流程。
- **深度业务流程整合**:
  - **联动采购与风控**: 当AI发现某商品频繁缺货时，可自动向**采购中台**发送补货建议。当识别到可疑订单时，可自动提交至**风控中台**进行分析。
  - **联动评价中台**: AI自动分析每日新增的中差评，提取核心问题点，自动关联订单与客服沟通记录，生成摘要报告，并为运营人员草拟回复。
- **联动采购与风控**: 当AI发现某商品频繁缺货时，可自动向**采购中台**发送补货建议。当识别到可疑订单时，可自动提交至**风控中台**进行分析。
- **联动评价中台**: AI自动分析每日新增的中差评，提取核心问题点，自动关联订单与客服沟通记录，生成摘要报告，并为运营人员草拟回复。
- **个性化与情感化增强**:
  - 结合用户的历史订单和偏好，提供更个性化的服务和推荐。
  - 在与客户的沟通中（如通过客服中台），引入情感计算，识别用户情绪，并采用更具同理心的沟通策略。
- 结合用户的历史订单和偏好，提供更个性化的服务和推荐。
- 在与客户的沟通中（如通过客服中台），引入情感计算，识别用户情绪，并采用更具同理心的沟通策略。


---

## 已有结论

- 通过一心堂AI中台统一的智能体页面交互 , 不在企微内通过消息交互