# 【20240313】一心到家V1.1.6-支付配置

# 一、需求分析

## 1.1 业务流程

[1.1.6 会员登录调整兼容&其他商城功能优化 - 产品部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18624778)

# 二、目标

**2.1 本期目标**

- 完成需求内容


# 三、整体设计

## 3.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 3.2 流程图

### 3.2.1 支付配置

#### 3.2.1.1 新门店上线微商城支付配置校验与设置

**true新门店上线微商城falseautotoptrue11415**

### 3.2.1.2 子公司

# 四、详细设计

## 4.1 详细模块设计

## 4.2 存储数据库设计

### 4.2.1 支付相关表的关联关系

**true支付信息表关系falseautotoptrue12472**

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | t_organization_tree | 门店机构表 | CREATE TABLE `t_organization_tree` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',   `org_parent` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父节点code',   `org_code` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机构编码',   `org_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机构名称',   `org_type` int DEFAULT NULL COMMENT '机构类型（1：公司，2：分部,3:区域,4:门店）',   `open_status` tinyint DEFAULT '1' COMMENT '状态（0：停用，1：启用）',   `create_name` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_name` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',   `parent_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '编码全路径',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_or_code` (`org_code`) USING BTREE,   KEY `idx_parent_or_code` (`org_parent`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店机构表'; |
| 2 | t_pay_organization_config | 子公司支付通道以及支付配置表 | CREATE TABLE `t_pay_organization_config` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',   `pay_channel_id` bigint NOT NULL COMMENT '支付通道id',   `pay_channel_code` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支付通道编码',   `pay_channel_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支付通道名称',   `org_code` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '机构编码，t_organization_tree表or_code',   `pay_customer_id` bigint NOT NULL COMMENT '客户id',   `pay_customer_code` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户编码',   `pay_customer_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '客户名称',   `channel_business_no` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道商户号',   `pay_company_code` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业编码',   `pay_company_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业名称',   `pay_config_detail` json DEFAULT NULL COMMENT '配置明细',   `remark` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',   `create_user` bigint DEFAULT NULL,   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,   `modify_user` bigint DEFAULT NULL,   `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,   `is_delete` tinyint NOT NULL DEFAULT '2' COMMENT '是否删除 1：是  2：否',   PRIMARY KEY (`id`) USING BTREE,   KEY `idx_org_code` (`org_code`) USING BTREE,   KEY `idx_orgCode_payChannel` (`org_code`,`pay_channel_code`) ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='子公司支付通道以及支付配置表'; |
| 3 | t_pay_channel_customer |  | ALTER TABLE `h3_pay_core`.`t_pay_channel_customer`  ADD INDEX `idx_channel_business_no`(`channel_business_no`);ALTER TABLE `h3_pay_core`.`t_pay_channel_customer`  ADD COLUMN `config_type` tinyint(0) NULL DEFAULT 1 COMMENT '1 客户自配置 2 沿用子公司配置' AFTER `business_mode`; |


## 4.3 接口设计

### 4.3.1 前端交互接口（新增）

#### 1 API-子公司配置列表

1. url: finance/payInfoConfig/org/config/page
2. 请求类型：POST
3. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orgCode ``` | ``` String ``` | 是 | ``` 机构编码 ``` |
| ``` orgName ``` | ``` String ``` | 是 | ``` 机构名 ``` |
| ``` payChannelCode ``` | String | 否 | 支付渠道编码，暂时只有 WEIXIN |
  2. 示例{
    "currentPage": 1,
    "pageSize": 20,
    "payChannelCode": "WEIXIN"
}
4. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orgCode ``` | ``` String ``` | 是 | ``` 机构编码 ``` |
| ``` orgName ``` | ``` String ``` | 是 | ``` 机构名 ``` |
| ``` payChannelCode ``` | String | 否 | 支付渠道编码，暂时只有 WEIXIN |
5. 示例{
    "currentPage": 1,
    "pageSize": 20,
    "payChannelCode": "WEIXIN"
}
6. 响应体：
  1. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` payOrgConfigId ``` | String | ``` 配置id ``` | t_pay_organization_config >> id |
|  | ``` orgCode ``` | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
|  | ``` orgName ``` | String | ``` 机构名称 ``` | t_organization_tree >> org_name |
|  | ``` channelBusinessNo ``` | String | ``` 通道商户号 ``` | t_pay_organization_config >> channel_business_no |
|  | ``` modifyDateTime ``` | String | ``` 修改时间 ``` | t_pay_organization_config >> modify_time |
|  | ``` modifyUser ``` | String | ``` 修改人 ``` | t_pay_organization_config >> modify_user |
  2. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 18,
        "totalPage": 0,
        "data": [
            {
                "payOrgConfigId": 1,
                "orgCode": "1000",
                "orgName": "云南公司",
                "channelBusinessNo": "34343",
                "modifyDateTime": "2024-03-19 10:32:30",
                "modifyUser": 0
            },
            {
                "payOrgConfigId": null,
                "orgCode": "1001",
                "orgName": "广西公司",
                "channelBusinessNo": "",
                "modifyDateTime": "",
                "modifyUser": null
            }
        ]
    }
}
7. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` payOrgConfigId ``` | String | ``` 配置id ``` | t_pay_organization_config >> id |
|  | ``` orgCode ``` | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
|  | ``` orgName ``` | String | ``` 机构名称 ``` | t_organization_tree >> org_name |
|  | ``` channelBusinessNo ``` | String | ``` 通道商户号 ``` | t_pay_organization_config >> channel_business_no |
|  | ``` modifyDateTime ``` | String | ``` 修改时间 ``` | t_pay_organization_config >> modify_time |
|  | ``` modifyUser ``` | String | ``` 修改人 ``` | t_pay_organization_config >> modify_user |
8. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 18,
        "totalPage": 0,
        "data": [
            {
                "payOrgConfigId": 1,
                "orgCode": "1000",
                "orgName": "云南公司",
                "channelBusinessNo": "34343",
                "modifyDateTime": "2024-03-19 10:32:30",
                "modifyUser": 0
            },
            {
                "payOrgConfigId": null,
                "orgCode": "1001",
                "orgName": "广西公司",
                "channelBusinessNo": "",
                "modifyDateTime": "",
                "modifyUser": null
            }
        ]
    }
}


#### 2 API-配置详情

1. url: finance/payInfoConfig/org/config/info
2. 请求类型：POST
3. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` orgCode ``` | String | ``` 组织机构编码 ``` |
| ``` payChannelCode ``` | String | ``` 支付渠道编码，暂时只有 WEIXIN ``` |
  2. 请求示例：{
    "orgCode": 1000,
    "payChannelCode": "WEIXIN"
}
4. | 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` orgCode ``` | String | ``` 组织机构编码 ``` |
| ``` payChannelCode ``` | String | ``` 支付渠道编码，暂时只有 WEIXIN ``` |
5. 请求示例：{
    "orgCode": 1000,
    "payChannelCode": "WEIXIN"
}
6. 响应体：不同的支付通道会有不同的返回体，具体返回体与创建接口的请求体是一致的，唯一区别是会多返回一个字段：payOrgConfigId，用于编辑时使用
  1. 微信的返回体
    1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | ``` 配置id ``` | t_pay_organization_config >> id |
| ``` orgCode ``` |  | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
| ``` payChannelCode ``` |  | String | ``` 支付通道编码 ``` | t_organization_tree >> pay_config_detail |
| ``` appId ``` |  | String | ``` 机构名称 ``` |
| ``` appSecret ``` |  | String | ``` 公众号密钥 ``` |
| ``` publicAppId ``` |  | String | ``` 公众号APPID ``` |
| ``` privateKey ``` |  | String | ``` 商户私钥 ``` |
| ``` secretKey ``` |  | String | ``` APIv3密钥 ``` |
| ``` customerSerialNumber ``` |  | String | ``` 证书序列号 ``` |
| ``` cityId ``` |  | String | ``` 城市ID ``` |
| ``` medicarePayKey ``` |  | String | ``` 医保支付Key ``` |
| ``` wxChannelCode ``` |  | String | ``` 微信渠道号 ``` |
| ``` channelBusinessNo ``` |  | String | ``` 通道商户号 ``` |
| ``` modifyDateTime ``` |  | String | ``` 修改时间 ``` |
| ``` modifyUser ``` |  | String | ``` 修改人 ``` |
| ``` payCompanyCode ``` |  | String | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |
| ``` payCustomerCode ``` |  | String |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` | ``` 商户证书 ``` |
|  | ``` name ``` | String | 文件名 |
|  | ``` url ``` | String | 文件地址 |
    2. 响应体示例：
      1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
    3. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
    1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  2. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | ``` 配置id ``` | t_pay_organization_config >> id |
| ``` orgCode ``` |  | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
| ``` payChannelCode ``` |  | String | ``` 支付通道编码 ``` | t_organization_tree >> pay_config_detail |
| ``` appId ``` |  | String | ``` 机构名称 ``` |
| ``` appSecret ``` |  | String | ``` 公众号密钥 ``` |
| ``` publicAppId ``` |  | String | ``` 公众号APPID ``` |
| ``` privateKey ``` |  | String | ``` 商户私钥 ``` |
| ``` secretKey ``` |  | String | ``` APIv3密钥 ``` |
| ``` customerSerialNumber ``` |  | String | ``` 证书序列号 ``` |
| ``` cityId ``` |  | String | ``` 城市ID ``` |
| ``` medicarePayKey ``` |  | String | ``` 医保支付Key ``` |
| ``` wxChannelCode ``` |  | String | ``` 微信渠道号 ``` |
| ``` channelBusinessNo ``` |  | String | ``` 通道商户号 ``` |
| ``` modifyDateTime ``` |  | String | ``` 修改时间 ``` |
| ``` modifyUser ``` |  | String | ``` 修改人 ``` |
| ``` payCompanyCode ``` |  | String | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |
| ``` payCustomerCode ``` |  | String |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` | ``` 商户证书 ``` |
|  | ``` name ``` | String | 文件名 |
|  | ``` url ``` | String | 文件地址 |
  3. 响应体示例：
    1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  4. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | ``` 配置id ``` | t_pay_organization_config >> id |
| ``` orgCode ``` |  | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
| ``` payChannelCode ``` |  | String | ``` 支付通道编码 ``` | t_organization_tree >> pay_config_detail |
| ``` appId ``` |  | String | ``` 机构名称 ``` |
| ``` appSecret ``` |  | String | ``` 公众号密钥 ``` |
| ``` publicAppId ``` |  | String | ``` 公众号APPID ``` |
| ``` privateKey ``` |  | String | ``` 商户私钥 ``` |
| ``` secretKey ``` |  | String | ``` APIv3密钥 ``` |
| ``` customerSerialNumber ``` |  | String | ``` 证书序列号 ``` |
| ``` cityId ``` |  | String | ``` 城市ID ``` |
| ``` medicarePayKey ``` |  | String | ``` 医保支付Key ``` |
| ``` wxChannelCode ``` |  | String | ``` 微信渠道号 ``` |
| ``` channelBusinessNo ``` |  | String | ``` 通道商户号 ``` |
| ``` modifyDateTime ``` |  | String | ``` 修改时间 ``` |
| ``` modifyUser ``` |  | String | ``` 修改人 ``` |
| ``` payCompanyCode ``` |  | String | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |
| ``` payCustomerCode ``` |  | String |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` | ``` 商户证书 ``` |
|  | ``` name ``` | String | 文件名 |
|  | ``` url ``` | String | 文件地址 |
  2. 响应体示例：
    1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  3. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
7. 微信的返回体
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | ``` 配置id ``` | t_pay_organization_config >> id |
| ``` orgCode ``` |  | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
| ``` payChannelCode ``` |  | String | ``` 支付通道编码 ``` | t_organization_tree >> pay_config_detail |
| ``` appId ``` |  | String | ``` 机构名称 ``` |
| ``` appSecret ``` |  | String | ``` 公众号密钥 ``` |
| ``` publicAppId ``` |  | String | ``` 公众号APPID ``` |
| ``` privateKey ``` |  | String | ``` 商户私钥 ``` |
| ``` secretKey ``` |  | String | ``` APIv3密钥 ``` |
| ``` customerSerialNumber ``` |  | String | ``` 证书序列号 ``` |
| ``` cityId ``` |  | String | ``` 城市ID ``` |
| ``` medicarePayKey ``` |  | String | ``` 医保支付Key ``` |
| ``` wxChannelCode ``` |  | String | ``` 微信渠道号 ``` |
| ``` channelBusinessNo ``` |  | String | ``` 通道商户号 ``` |
| ``` modifyDateTime ``` |  | String | ``` 修改时间 ``` |
| ``` modifyUser ``` |  | String | ``` 修改人 ``` |
| ``` payCompanyCode ``` |  | String | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |
| ``` payCustomerCode ``` |  | String |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` | ``` 商户证书 ``` |
|  | ``` name ``` | String | 文件名 |
|  | ``` url ``` | String | 文件地址 |
  2. 响应体示例：
    1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  3. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
  1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
8. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | ``` 配置id ``` | t_pay_organization_config >> id |
| ``` orgCode ``` |  | String | ``` 机构编码 ``` | t_pay_organization_config >> org_code |
| ``` payChannelCode ``` |  | String | ``` 支付通道编码 ``` | t_organization_tree >> pay_config_detail |
| ``` appId ``` |  | String | ``` 机构名称 ``` |
| ``` appSecret ``` |  | String | ``` 公众号密钥 ``` |
| ``` publicAppId ``` |  | String | ``` 公众号APPID ``` |
| ``` privateKey ``` |  | String | ``` 商户私钥 ``` |
| ``` secretKey ``` |  | String | ``` APIv3密钥 ``` |
| ``` customerSerialNumber ``` |  | String | ``` 证书序列号 ``` |
| ``` cityId ``` |  | String | ``` 城市ID ``` |
| ``` medicarePayKey ``` |  | String | ``` 医保支付Key ``` |
| ``` wxChannelCode ``` |  | String | ``` 微信渠道号 ``` |
| ``` channelBusinessNo ``` |  | String | ``` 通道商户号 ``` |
| ``` modifyDateTime ``` |  | String | ``` 修改时间 ``` |
| ``` modifyUser ``` |  | String | ``` 修改人 ``` |
| ``` payCompanyCode ``` |  | String | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |
| ``` payCustomerCode ``` |  | String |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` | ``` 商户证书 ``` |
|  | ``` name ``` | String | 文件名 |
|  | ``` url ``` | String | 文件地址 |
9. 响应体示例：
  1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}
10. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710820132528,
    "exception": null,
    "data": {
        "payChannelCode": "WEIXIN",
        "payOrgConfigId": 1,
        "channelBusinessNo": "34343",
        "orgCode": "1000",
        "payCompanyCode": "500001",
        "payCompanyName": "500001",
        "payCustomerCode": "500001",
        "appId": "333",
        "appSecret": "33222",
        "publicAppId": "wx2b4af4bac659845e",
        "privateKey": "22",
        "secretKey": "22222",
        "customerSerialNumber": "222",
        "fileVOS": [
            {
                "name": "正常下单支付.png",
                "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
            }
        ],
        "cityId": "",
        "medicarePayKey": "",
        "wxChannelCode": ""
    }
}


#### 3 API-配置增加/修改

1. url: finance/payInfoConfig/org/config/save-update
2. 请求类型：POST
3. 请求体：对于不同的支付通道着不同的请求体，但orgCode、payChannelCode需要必填，其余部分跟门店的支付配置基本保持一致
  1. | 字段名 |  | 字段类型 | 是否必填 | 字段描述 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | 否 | 配置id，编辑时传 |
| ``` orgCode ``` |  | String | 是 | 机构编码 |
| ``` payChannelCode ``` |  | String | 是 | 支付通道编码，目前只有 WEIXIN |
| ``` appId ``` |  | String |  | 小程序appid |
| ``` appSecret ``` |  | String |  | 公众号密钥 |
| ``` publicAppId ``` |  | String |  | 公众号APPID |
| ``` privateKey ``` |  | String |  | 商户私钥 |
| ``` secretKey ``` |  | String |  | APIv3密钥 |
| ``` customerSerialNumber ``` |  | String |  | 证书序列号 |
| ``` cityId ``` |  | String |  | 城市ID |
| ``` medicarePayKey ``` |  | String |  | 医保支付Key |
| ``` wxChannelCode ``` |  | String |  | 微信渠道号 |
| ``` channelBusinessNo ``` |  | String |  | 通道商户号 |
| ``` modifyDateTime ``` |  | String |  | 修改时间 |
| ``` modifyUser ``` |  | String |  | 修改人 |
| ``` payCompanyCode ``` |  | String |  | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |  |
| ``` payCustomerCode ``` |  | String |  |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` |  | 商户证书 |
|  | ``` name ``` | String |  | 文件名 |
|  | ``` url ``` | String |  | 文件地址 |
  2. 请求体示例：
    1. {
    "payOrgConfigId": 1,
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "payCompanyName": "500001",
    "payChannelCode": "WEIXIN",
    "channelBusinessNo": "34343",
    "customerSerialNumber": "222",
    "privateKey": "22",
    "secretKey": "22222",
    "appId": "333",
    "publicAppId": "wx2b4af4bac659845e",
    "fileVOS":
    [
        {
            "id": "正常下单支付.png",
            "name": "正常下单支付.png",
            "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
        }
    ],
    "medicarePayKey": "",
    "cityId": "",
    "wxChannelCode": "",
    "orgCode":"1000"
}
  3. {
    "payOrgConfigId": 1,
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "payCompanyName": "500001",
    "payChannelCode": "WEIXIN",
    "channelBusinessNo": "34343",
    "customerSerialNumber": "222",
    "privateKey": "22",
    "secretKey": "22222",
    "appId": "333",
    "publicAppId": "wx2b4af4bac659845e",
    "fileVOS":
    [
        {
            "id": "正常下单支付.png",
            "name": "正常下单支付.png",
            "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
        }
    ],
    "medicarePayKey": "",
    "cityId": "",
    "wxChannelCode": "",
    "orgCode":"1000"
}
  1. {
    "payOrgConfigId": 1,
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "payCompanyName": "500001",
    "payChannelCode": "WEIXIN",
    "channelBusinessNo": "34343",
    "customerSerialNumber": "222",
    "privateKey": "22",
    "secretKey": "22222",
    "appId": "333",
    "publicAppId": "wx2b4af4bac659845e",
    "fileVOS":
    [
        {
            "id": "正常下单支付.png",
            "name": "正常下单支付.png",
            "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
        }
    ],
    "medicarePayKey": "",
    "cityId": "",
    "wxChannelCode": "",
    "orgCode":"1000"
}
4. | 字段名 |  | 字段类型 | 是否必填 | 字段描述 |
| --- | --- | --- | --- | --- |
| ``` payOrgConfigId ``` |  | String | 否 | 配置id，编辑时传 |
| ``` orgCode ``` |  | String | 是 | 机构编码 |
| ``` payChannelCode ``` |  | String | 是 | 支付通道编码，目前只有 WEIXIN |
| ``` appId ``` |  | String |  | 小程序appid |
| ``` appSecret ``` |  | String |  | 公众号密钥 |
| ``` publicAppId ``` |  | String |  | 公众号APPID |
| ``` privateKey ``` |  | String |  | 商户私钥 |
| ``` secretKey ``` |  | String |  | APIv3密钥 |
| ``` customerSerialNumber ``` |  | String |  | 证书序列号 |
| ``` cityId ``` |  | String |  | 城市ID |
| ``` medicarePayKey ``` |  | String |  | 医保支付Key |
| ``` wxChannelCode ``` |  | String |  | 微信渠道号 |
| ``` channelBusinessNo ``` |  | String |  | 通道商户号 |
| ``` modifyDateTime ``` |  | String |  | 修改时间 |
| ``` modifyUser ``` |  | String |  | 修改人 |
| ``` payCompanyCode ``` |  | String |  | 这三个目前固定是 500001 |
| ``` payCompanyName ``` |  | String |  |
| ``` payCustomerCode ``` |  | String |  |
| ``` fileVOS ``` |  | ``` List<FileAttachmentVO> ``` |  | 商户证书 |
|  | ``` name ``` | String |  | 文件名 |
|  | ``` url ``` | String |  | 文件地址 |
5. 请求体示例：
  1. {
    "payOrgConfigId": 1,
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "payCompanyName": "500001",
    "payChannelCode": "WEIXIN",
    "channelBusinessNo": "34343",
    "customerSerialNumber": "222",
    "privateKey": "22",
    "secretKey": "22222",
    "appId": "333",
    "publicAppId": "wx2b4af4bac659845e",
    "fileVOS":
    [
        {
            "id": "正常下单支付.png",
            "name": "正常下单支付.png",
            "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
        }
    ],
    "medicarePayKey": "",
    "cityId": "",
    "wxChannelCode": "",
    "orgCode":"1000"
}
6. {
    "payOrgConfigId": 1,
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "payCompanyName": "500001",
    "payChannelCode": "WEIXIN",
    "channelBusinessNo": "34343",
    "customerSerialNumber": "222",
    "privateKey": "22",
    "secretKey": "22222",
    "appId": "333",
    "publicAppId": "wx2b4af4bac659845e",
    "fileVOS":
    [
        {
            "id": "正常下单支付.png",
            "name": "正常下单支付.png",
            "url": "https://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20240318/34343_1710728318509.p12"
        }
    ],
    "medicarePayKey": "",
    "cityId": "",
    "wxChannelCode": "",
    "orgCode":"1000"
}
7. 响应体：
  1. 返回配置id
  2. 示例：
    1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": "100"
}
  3. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": "100"
}
  1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": "100"
}
8. 返回配置id
9. 示例：
  1. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": "100"
}
10. {
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": "100"
}


#### 4 API-子公司分页列表

1. url: finance/org/page
2. 请求类型：POST
3. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orgCode ``` | ``` String ``` | 是 | ``` 机构编码 ``` |
| ``` orgName ``` | ``` String ``` | 是 | ``` 机构名 ``` |
  2. 示例{
    "currentPage": 1,
    "pageSize": 20
}
4. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orgCode ``` | ``` String ``` | 是 | ``` 机构编码 ``` |
| ``` orgName ``` | ``` String ``` | 是 | ``` 机构名 ``` |
5. 示例{
    "currentPage": 1,
    "pageSize": 20
}
6. 响应体：
  1. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` orgCode ``` | String | ``` 机构编码 ``` | t_organization_tree >> org_code |
|  | ``` orgName ``` | String | ``` 机构名称 ``` | t_organization_tree >> org_name |
  2. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 18,
        "totalPage": 0,
        "data": [
            {
                "orgCode": "1000",
                "orgName": "云南公司"
            },
            {
                "orgCode": "1001",
                "orgName": "广西公司"
            }
        ]
    }
}
7. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` orgCode ``` | String | ``` 机构编码 ``` | t_organization_tree >> org_code |
|  | ``` orgName ``` | String | ``` 机构名称 ``` | t_organization_tree >> org_name |
8. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710830751622,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 18,
        "totalPage": 0,
        "data": [
            {
                "orgCode": "1000",
                "orgName": "云南公司"
            },
            {
                "orgCode": "1001",
                "orgName": "广西公司"
            }
        ]
    }
}


#### 5 API-微商城网店支付配置分页查询

1. url: /finance/payInfoConfig/third_find/page
2. 请求类型：POST
3. 请求体：颜色不一致的字段为此次新增，其余请求体与原有的支付配置查询接口（/finance/payInfoConfig/third_find）一致
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| currentPage | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| pageSize | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` payChannelCode ``` | ``` String ``` | 否 | ``` 支付通道 ``` |
| ``` payCustomerCode ``` | ``` String ``` | 否 | ``` 客户编码 ``` |
| ``` payCompanyCode ``` | ``` String ``` | 否 | ``` 企业编码 ``` |
| ``` infoType ``` | ``` Integer ``` | 否 | ``` 类型：0：企业 1：机构 ``` |
| ``` businessInfo ``` | ``` String ``` | 是 | ``` 门店编码或名称 ``` |
| ``` infoStatus ``` | ``` Integer ``` | 是 | ``` 状态 1：启用 2：禁用 ``` |
| channelBusinessNo | ``` String ``` | 是 | ``` 通道商户号 ``` |
| subCompanyCode | ``` String ``` | 是 | ``` 子公司编码 ``` |
  2. 示例{
    "infoType": 1,
    "payChannelCode": "WEIXIN",
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "businessInfo": "",
    "infoStatus": null,
    "currentPage": 1,
    "pageSize": 20,
    "subCompanyCode": "1000",
    "channelBusinessNo": "111"
}
4. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| currentPage | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| pageSize | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` payChannelCode ``` | ``` String ``` | 否 | ``` 支付通道 ``` |
| ``` payCustomerCode ``` | ``` String ``` | 否 | ``` 客户编码 ``` |
| ``` payCompanyCode ``` | ``` String ``` | 否 | ``` 企业编码 ``` |
| ``` infoType ``` | ``` Integer ``` | 否 | ``` 类型：0：企业 1：机构 ``` |
| ``` businessInfo ``` | ``` String ``` | 是 | ``` 门店编码或名称 ``` |
| ``` infoStatus ``` | ``` Integer ``` | 是 | ``` 状态 1：启用 2：禁用 ``` |
| channelBusinessNo | ``` String ``` | 是 | ``` 通道商户号 ``` |
| subCompanyCode | ``` String ``` | 是 | ``` 子公司编码 ``` |
5. 示例{
    "infoType": 1,
    "payChannelCode": "WEIXIN",
    "payCustomerCode": "500001",
    "payCompanyCode": "500001",
    "businessInfo": "",
    "infoStatus": null,
    "currentPage": 1,
    "pageSize": 20,
    "subCompanyCode": "1000",
    "channelBusinessNo": "111"
}
6. 响应体：颜色不一致的字段为此次新增，其余响应体与原有的支付配置查询接口（/finance/payInfoConfig/third_find）一致
  1. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` id ``` | String | ``` 主键 ``` | ``` t_pay_info_config_rela_channel >> id ``` |
|  | ``` infoId ``` | String | ``` 支付信息配置ID ``` | ``` t_pay_info_config_rela_channel >> info_id ``` |
|  | ``` payChannelCode ``` | String | ``` 支付通道编码 ``` | t_pay_info_config_rela_channel >> pay_channel_code |
|  | ``` payChannelName ``` | String | ``` 支付通道名称 ``` | t_pay_info_config_rela_channel >> pay_channel_name |
|  | ``` payChannelCustomerCode ``` | String | ``` 关联通道配置编码 ``` | t_pay_info_config_rela_channel >> pay_channel_customer_code |
|  | ``` infoType ``` | String | ``` 类型：0：企业 1：机构 ``` | ``` t_pay_info_config >> info_type ``` |
|  | ``` payCustomerCode ``` | String | ``` 客户编码 ``` | t_pay_info_config >> pay_customer_code |
|  | ``` payCompanyCode ``` | String | ``` 企业编码 ``` | t_pay_info_config >> pay_company_code |
|  | ``` payCompanyName ``` | String | ``` 企业名称 ``` | t_pay_info_config >> pay_company_name |
|  | ``` payBusinessCode ``` | String | ``` 业务机构编码 ``` | t_pay_info_config >> pay_business_code |
|  | ``` payBusinessName ``` | String | ``` 业务机构名称 ``` | t_pay_info_config >> pay_business_name |
|  | ``` platformBusinessNo ``` | String | ``` 平台商户号 ``` | t_pay_info_config >> platform_business_no |
|  | ``` platformBusinessName ``` | String | ``` 平台商户名称 ``` | t_pay_info_config >> platform_business_name |
|  | ``` infoStatus ``` | int | ``` 状态， 1：启用 2：禁用 ``` | t_pay_info_config_rela_channel >> info_status |
|  | subCompanyCode | String | ``` 微商城门店-子公司编码 ``` | t_organization_tree >> org_code |
|  | subCompanyName | String | ``` 微商城门店-子公司名称 ``` | t_organization_tree >> org_name |
|  | channelBusinessNo | String | ``` 通道商户号 ``` | ``` t_pay_channel_customer >> channel_business_no ``` |
  2. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710903810788,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 1,
        "totalPage": 0,
        "data": [
            {
                "id": 9789,
                "infoId": 9982,
                "payChannelCode": "WEIXIN",
                "payChannelName": "微信",
                "payChannelCustomerCode": "500001-WEIXIN-4WX8RX",
                "infoType": 1,
                "payCustomerCode": "500001",
                "payCompanyCode": "500001",
                "payCompanyName": "500001",
                "payBusinessCode": "A087",
                "payBusinessName": "一心堂昆明双龙桥连锁店",
                "platformBusinessNo": "12727552",
                "platformBusinessName": "一心堂昆明双龙桥连锁店",
                "infoStatus": 2,
                "createUser": 13273,
                "createTime": "2024-03-14T05:40:30.000Z",
                "modifyUser": 13273,
                "modifyTime": "2024-03-14T05:40:30.000Z",
                "isDelete": 2,
                "groupId": 500001,
                "companyId": 500001,
                "businessId": null,
                "channelTypeStatus": 0,
                "subCompanyCode": "1000",
                "subCompanyName": "云南公司",
                "channelBusinessNo": "111"
            }
        ]
    }
}
7. | 字段名 | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| currentPage |  | int | 当前页 |  |
| pageSize |  | int | 每页大小 |  |
| totalCount |  | int | 总数 |  |
| data |  | List<Object> |  |  |
|  | ``` id ``` | String | ``` 主键 ``` | ``` t_pay_info_config_rela_channel >> id ``` |
|  | ``` infoId ``` | String | ``` 支付信息配置ID ``` | ``` t_pay_info_config_rela_channel >> info_id ``` |
|  | ``` payChannelCode ``` | String | ``` 支付通道编码 ``` | t_pay_info_config_rela_channel >> pay_channel_code |
|  | ``` payChannelName ``` | String | ``` 支付通道名称 ``` | t_pay_info_config_rela_channel >> pay_channel_name |
|  | ``` payChannelCustomerCode ``` | String | ``` 关联通道配置编码 ``` | t_pay_info_config_rela_channel >> pay_channel_customer_code |
|  | ``` infoType ``` | String | ``` 类型：0：企业 1：机构 ``` | ``` t_pay_info_config >> info_type ``` |
|  | ``` payCustomerCode ``` | String | ``` 客户编码 ``` | t_pay_info_config >> pay_customer_code |
|  | ``` payCompanyCode ``` | String | ``` 企业编码 ``` | t_pay_info_config >> pay_company_code |
|  | ``` payCompanyName ``` | String | ``` 企业名称 ``` | t_pay_info_config >> pay_company_name |
|  | ``` payBusinessCode ``` | String | ``` 业务机构编码 ``` | t_pay_info_config >> pay_business_code |
|  | ``` payBusinessName ``` | String | ``` 业务机构名称 ``` | t_pay_info_config >> pay_business_name |
|  | ``` platformBusinessNo ``` | String | ``` 平台商户号 ``` | t_pay_info_config >> platform_business_no |
|  | ``` platformBusinessName ``` | String | ``` 平台商户名称 ``` | t_pay_info_config >> platform_business_name |
|  | ``` infoStatus ``` | int | ``` 状态， 1：启用 2：禁用 ``` | t_pay_info_config_rela_channel >> info_status |
|  | subCompanyCode | String | ``` 微商城门店-子公司编码 ``` | t_organization_tree >> org_code |
|  | subCompanyName | String | ``` 微商城门店-子公司名称 ``` | t_organization_tree >> org_name |
|  | channelBusinessNo | String | ``` 通道商户号 ``` | ``` t_pay_channel_customer >> channel_business_no ``` |
8. 示例值{
    "success": true,
    "bizSuccess": true,
    "msg": "查询成功！",
    "code": "00600",
    "traceId": "",
    "currentTimeMillis": 1710903810788,
    "exception": null,
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 1,
        "totalPage": 0,
        "data": [
            {
                "id": 9789,
                "infoId": 9982,
                "payChannelCode": "WEIXIN",
                "payChannelName": "微信",
                "payChannelCustomerCode": "500001-WEIXIN-4WX8RX",
                "infoType": 1,
                "payCustomerCode": "500001",
                "payCompanyCode": "500001",
                "payCompanyName": "500001",
                "payBusinessCode": "A087",
                "payBusinessName": "一心堂昆明双龙桥连锁店",
                "platformBusinessNo": "12727552",
                "platformBusinessName": "一心堂昆明双龙桥连锁店",
                "infoStatus": 2,
                "createUser": 13273,
                "createTime": "2024-03-14T05:40:30.000Z",
                "modifyUser": 13273,
                "modifyTime": "2024-03-14T05:40:30.000Z",
                "isDelete": 2,
                "groupId": 500001,
                "companyId": 500001,
                "businessId": null,
                "channelTypeStatus": 0,
                "subCompanyCode": "1000",
                "subCompanyName": "云南公司",
                "channelBusinessNo": "111"
            }
        ]
    }
}


### 4.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 4.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 五、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 六、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 七、项目排期

**设计文档输出： 设计评审：**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）**

**联调时间：2023年12月13日-2023年11月15日**

**测试时间：2023年11月18日-2023年11月20日**

**上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 开发时间 | 负责人 | 进度 |
| --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |


# 八、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等