package com.yxt.order.assistant.knowledge;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yxt.order.assistant.knowledge.model.ConfluenceChildrenResponse;
import com.yxt.order.assistant.knowledge.model.ConfluencePage;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class ConfluenceMarkdownExporter {

    private final OkHttpClient client;
    private final Gson gson;
    private final String confluenceUrl;
    private final String personalToken;
    private final String outputDir;
    private final String resourcesDir;

    public static void main(String[] args) {
        new ConfluenceMarkdownExporter("https://yxtcf.hxyxt.com","NDUxMjY5MDIyMDcwOigCdFHosMopCmWokCv3Cz+4jQOV")
            .exportPageAndChildrenToMarkdown("73450218");
    }

    public ConfluenceMarkdownExporter(@Value("${confluence.url}") String confluenceUrl,
                                      @Value("${confluence.token}") String personalToken) {
        this.confluenceUrl = confluenceUrl;
        this.personalToken = personalToken;
        this.outputDir = "confluence-export";

        // 配置OkHttp客户端
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();

        // 配置Gson
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .create();

        // 创建输出目录
        createOutputDirectory();
    }

    /**
     * 创建输出目录
     */
    private void createOutputDirectory() {
        File dir = new File(outputDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }

    /**
     * 导出页面及其所有子页面到Markdown
     * 使用广度优先遍历避免递归调用栈溢出
     */
    public void exportPageAndChildrenToMarkdown(String rootPageId) {
        Queue<String> pageQueue = new LinkedList<>();
        Set<String> processedPages = new HashSet<>();
        List<String> exportedFiles = new ArrayList<>();

        pageQueue.offer(rootPageId);

        System.out.println("🚀 开始导出Confluence页面，根页面ID: " + rootPageId);

        while (!pageQueue.isEmpty()) {
            String pageId = pageQueue.poll();

            if (processedPages.contains(pageId)) {
                continue;
            }

            processedPages.add(pageId);

            try {
                // 导出当前页面
                String exportedFile = exportPageToMarkdown(pageId);
                if (exportedFile != null) {
                    exportedFiles.add(exportedFile);
                }

                // 获取子页面并加入队列
                List<String> childPageIds = getChildPageIds(pageId);
                pageQueue.addAll(childPageIds);

            } catch (Exception e) {
                System.err.println("❌ 处理页面失败 " + pageId + ": " + e.getMessage());
                e.printStackTrace();
            }
        }

        System.out.println("✅ 导出完成！共导出 " + exportedFiles.size() + " 个页面");
        System.out.println("📁 输出目录: " + new File(outputDir).getAbsolutePath());

        // 生成索引文件
        generateIndexFile(exportedFiles);
    }

    /**
     * 获取子页面ID列表
     */
    private List<String> getChildPageIds(String pageId) {
        List<String> childPageIds = new ArrayList<>();
        String childrenUrl = confluenceUrl + "/rest/api/content/" + pageId + "/child/page?limit=100";

        Request request = new Request.Builder()
                .url(childrenUrl)
                .addHeader("Authorization", "Bearer " + personalToken)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.err.println("❌ 获取子页面失败 " + pageId + ": " + response.code() + " " + response.message());
                return childPageIds;
            }

            String json = response.body().string();
            ConfluenceChildrenResponse childrenResponse = gson.fromJson(json, ConfluenceChildrenResponse.class);

            if (childrenResponse.getResults() != null) {
                for (ConfluenceChildrenResponse.ConfluencePageSummary page : childrenResponse.getResults()) {
                    if ("page".equals(page.getType()) && "current".equals(page.getStatus())) {
                        childPageIds.add(page.getId());
                        System.out.println("📄 发现子页面: " + page.getTitle() + " (ID: " + page.getId() + ")");
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("❌ 解析子页面响应失败 " + pageId + ": " + e.getMessage());
            e.printStackTrace();
        }

        return childPageIds;
    }

    /**
     * 导出单个页面到Markdown
     */
    public String exportPageToMarkdown(String pageId) {
        String url = confluenceUrl + "/rest/api/content/" + pageId + "?expand=body.storage";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + personalToken)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                System.err.println("❌ 获取页面内容失败 " + pageId + ": " + response.code() + " " + response.message());
                return null;
            }

            String json = response.body().string();
            ConfluencePage page = gson.fromJson(json, ConfluencePage.class);

            if (page == null || page.getBody() == null || page.getBody().getStorage() == null) {
                System.err.println("❌ 页面内容为空 " + pageId);
                return null;
            }

            String html = page.getBody().getStorage().getValue();
            String title = page.getTitle();

            System.out.println("📝 正在处理页面: " + title + " (ID: " + pageId + ")");

            // 转换HTML到Markdown
            String markdown = convertHtmlToMarkdown(html, title);

            // 生成安全的文件名
            String fileName = sanitizeFileName(title) + "_" + pageId + ".md";
            String filePath = outputDir + File.separator + fileName;

            // 写入文件（使用UTF-8编码）
            try (OutputStreamWriter writer = new OutputStreamWriter(
                    new FileOutputStream(filePath), StandardCharsets.UTF_8)) {
                writer.write(markdown);
                System.out.println("✅ 导出完成: " + fileName);
                return fileName;
            }

        } catch (Exception e) {
            System.err.println("❌ 导出页面失败 " + pageId + ": " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 将HTML转换为Markdown
     */
    private String convertHtmlToMarkdown(String html, String title) {
        if (html == null || html.trim().isEmpty()) {
            return "# " + title + "\n\n*此页面内容为空*\n";
        }

        Document doc = Jsoup.parse(html);
        StringBuilder markdown = new StringBuilder();

        // 添加页面标题
        markdown.append("# ").append(title).append("\n\n");

        // 转换body内容
        if (doc.body() != null) {
            for (Element element : doc.body().children()) {
                String converted = convertElementToMarkdown(element, 0);
                if (!converted.trim().isEmpty()) {
                    markdown.append(converted).append("\n\n");
                }
            }
        }

        return markdown.toString().trim();
    }

    /**
     * 递归转换HTML元素到Markdown
     */
    private String convertElementToMarkdown(Element element, int depth) {
        if (element == null) {
            return "";
        }

        String tagName = element.tagName().toLowerCase();
        StringBuilder result = new StringBuilder();

        switch (tagName) {
            case "h1":
                return "# " + element.text();
            case "h2":
                return "## " + element.text();
            case "h3":
                return "### " + element.text();
            case "h4":
                return "#### " + element.text();
            case "h5":
                return "##### " + element.text();
            case "h6":
                return "###### " + element.text();

            case "p":
                return processInlineElements(element);

            case "br":
                return "\n";

            case "strong":
            case "b":
                return "**" + element.text() + "**";

            case "em":
            case "i":
                return "*" + element.text() + "*";

            case "code":
                return "`" + element.text() + "`";

            case "pre":
                Elements codeElements = element.select("code");
                if (!codeElements.isEmpty()) {
                    return "```\n" + codeElements.first().text() + "\n```";
                } else {
                    return "```\n" + element.text() + "\n```";
                }

            case "blockquote":
                String[] lines = element.text().split("\n");
                StringBuilder quote = new StringBuilder();
                for (String line : lines) {
                    quote.append("> ").append(line).append("\n");
                }
                return quote.toString().trim();

            case "ul":
                return convertList(element, false, depth);

            case "ol":
                return convertList(element, true, depth);

            case "li":
                // 这个会在convertList中处理
                return processInlineElements(element);

            case "a":
                String href = element.attr("href");
                String text = element.text();
                if (href.isEmpty()) {
                    return text;
                }
                // 处理相对链接
                if (href.startsWith("/")) {
                    href = confluenceUrl + href;
                }
                return "[" + text + "](" + href + ")";

            case "img":
                String src = element.attr("src");
                String alt = element.attr("alt");
                if (src.startsWith("/")) {
                    src = confluenceUrl + src;
                }
                return "![" + alt + "](" + src + ")";

            case "table":
                return convertTable(element);

            case "hr":
                return "---";

            default:
                // 对于未知标签，处理其子元素
                return processInlineElements(element);
        }
    }

    /**
     * 处理内联元素
     */
    private String processInlineElements(Element element) {
        StringBuilder result = new StringBuilder();

        for (Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                result.append(((TextNode) node).text());
            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                result.append(convertElementToMarkdown(childElement, 0));
            }
        }

        return result.toString();
    }

    /**
     * 转换列表
     */
    private String convertList(Element listElement, boolean ordered, int depth) {
        StringBuilder result = new StringBuilder();
        String indent = repeatString("  ", depth);

        Elements listItems = listElement.children().select("li");
        int counter = 1;

        for (Element li : listItems) {
            if (ordered) {
                result.append(indent).append(counter++).append(". ");
            } else {
                result.append(indent).append("- ");
            }

            // 处理嵌套列表
            Elements nestedLists = li.children().select("ul, ol");
            if (!nestedLists.isEmpty()) {
                // 先处理文本内容
                Element liCopy = li.clone();
                liCopy.select("ul, ol").remove();
                String text = processInlineElements(liCopy).trim();
                if (!text.isEmpty()) {
                    result.append(text).append("\n");
                }

                // 然后处理嵌套列表
                for (Element nestedList : nestedLists) {
                    boolean isOrdered = "ol".equals(nestedList.tagName());
                    result.append(convertList(nestedList, isOrdered, depth + 1));
                }
            } else {
                result.append(processInlineElements(li).trim()).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 转换表格
     */
    private String convertTable(Element table) {
        StringBuilder result = new StringBuilder();
        Elements rows = table.select("tr");

        if (rows.isEmpty()) {
            return "";
        }

        boolean hasHeader = false;

        for (int i = 0; i < rows.size(); i++) {
            Element row = rows.get(i);
            Elements cells = row.select("th, td");

            if (cells.isEmpty()) {
                continue;
            }

            // 检查是否是表头
            if (i == 0 && !row.select("th").isEmpty()) {
                hasHeader = true;
            }

            result.append("|");
            for (Element cell : cells) {
                String cellText = processInlineElements(cell).trim().replace("\n", " ");
                result.append(" ").append(cellText).append(" |");
            }
            result.append("\n");

            // 添加表头分隔符
            if (hasHeader && i == 0) {
                result.append("|");
                for (int j = 0; j < cells.size(); j++) {
                    result.append(" --- |");
                }
                result.append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 重复字符串（Java 8兼容）
     */
    private String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * 生成安全的文件名
     */
    private String sanitizeFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "untitled";
        }

        // 移除或替换不安全的字符
        return fileName.trim()
                .replaceAll("[\\\\/:*?\"<>|]", "_")
                .replaceAll("\\s+", "_")
                .replaceAll("_{2,}", "_")
                .replaceAll("^_|_$", "");
    }

    /**
     * 生成索引文件
     */
    private void generateIndexFile(List<String> exportedFiles) {
        if (exportedFiles.isEmpty()) {
            return;
        }

        try {
            String indexPath = outputDir + File.separator + "README.md";
            try (OutputStreamWriter writer = new OutputStreamWriter(
                    new FileOutputStream(indexPath), StandardCharsets.UTF_8)) {
                writer.write("# Confluence 导出索引\n\n");
                writer.write("导出时间: " + new Date() + "\n\n");
                writer.write("## 导出的页面\n\n");

                for (String fileName : exportedFiles) {
                    String displayName = fileName.replace(".md", "").replaceAll("_\\d+$", "");
                    writer.write("- [" + displayName + "](./" + fileName + ")\n");
                }

                System.out.println("📋 生成索引文件: README.md");
            }
        } catch (IOException e) {
            System.err.println("❌ 生成索引文件失败: " + e.getMessage());
        }
    }
}
