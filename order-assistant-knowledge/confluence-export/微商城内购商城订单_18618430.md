# 微商城内购商城订单

## 一、 背景

### 1.1 业务背景

### 1.2 痛点分析

### 1.3 系统现状

****

## 二、 需求分析

### 2.1 业务流程

### 2.2 需求功能点

#### 订单中台-需求功能：

| **系统** | **功能** | **描述** | **备注** |
| 订单中台 | 消息--新订单消息 | 接收抖店消息，并做判断生成对应的新订单 |  |
| 消息--申请售后消息 | 接收平台申请售后消息 0退货退款，1仅退款，2发货前退款，生成对应的O2O平台退款单，对应的退款类型为：退货退款，仅退款，取消订单 |  |
| 消息--退款成功 | 接收审核成功的售后单消息 |  |
| 消息--售后关闭 |  |  |
| 订单详情查询 | 查询抖店平台订单详情接口，查询订单详细，build为OMS订单格式，生成订单 |  |
| 批量解密接口 | 解密收货人信息 |  |
| 订单状态回传 | 拣货复核后上传订单状态到平台 |  |
| 手动补单 | 输入抖店订单编号补录订单 | 存在不补录，完成、已发货走新单流程，取消补录回来就是取消 |
| 售后审核 | 退款审核 |  |
| 调用三方请求返回放MD | 请求三方放MD |  |
|  | 平台配送呼叫运力 |  |  |
|  | 解密日志 |  |  |


#### 商品中台-需求功能：

| **系统** | **功能** | **描述** | **备注** |
| 商品中台 | 商品库存同步 | OMS 对应门店库存变更，同步对应的平台 | 同步失败/成功写日志的操作参考当前海典 |
| 商品价格同步 | OMS 对应门店价格变更，同步对应的平台 | 同步失败/成功写日志的操作参考当前海典 |
| 商品下架 | OMS 商品变更为禁售/对应第三方商品下架，调用平台下架接口，下架商品 | 同步失败/成功写日志的操作参考当前海典 |
| 商品上架 | OMS 商品变更为可售/对应第三方商品上架，调用平台上架接口，上架商品 | 同步失败/成功写日志的操作参考当前海典 |
| 拉取平台商品 | 从平台拉取门店对应的商品 | 若本地有商品，重新拉取后，则更新对应的库存、价格、状态每拉取一次，则需要形成商品的操作日志 （写日志的操作参考当前海典） |
| 商品自动上下架配置 | 商品自动上下架配置 |  |
| 商品管理-第三方商品-新增商品-下载平台商品 | 商品管理-第三方商品-新增商品-下载平台商品 |  |


## 三、 目标

### 3.1 本期目标

#### 3.1.1 业务目标

### 3.2 中长期目标

1、重构接口中台为JAVA项目

### 四、整体设计

### 4.1统一语言定义

### 4.2 用例图

## 五、 详细设计

### 1、 模块详细设计

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

## 九、 上线方案