# 订单新模型QA

### Q1: 订单模型状态字段如何建立?最佳实践是?

**背景:**

有售后系统，发货系统，支付系统。在订单主模型中是使用一个字段来统一表示这些状态，例如已发货，待审核,已退款等等。还是用多个字段分开表示?例如退款状态、审核状态等等

#### A:

从**数据建模**的角度来分析这个问题

电商订单状态流转图:

true电商订单状态流转图falseautotoptrue5121

建议采用**组合方案**，即主状态字段 + 独立状态字段的方式：

1. 主状态字段(main_status)


CREATE TABLE orders (
    id BIGINT PRIMARY KEY,
    main_status VARCHAR(32),  -- 订单主状态
    payment_status VARCHAR(32),  -- 支付状态
    shipping_status VARCHAR(32),  -- 物流状态
    refund_status VARCHAR(32),  -- 退款状态
    after_sale_status VARCHAR(32)  -- 售后状态
);

理由如下：

1. **业务清晰性**
  - 每个子系统（发货、售后、退款）都能独立维护自己的状态
  - 避免状态组合爆炸，如果用单一状态字段，会产生大量复杂的状态组合
2. 每个子系统（发货、售后、退款）都能独立维护自己的状态
3. 避免状态组合爆炸，如果用单一状态字段，会产生大量复杂的状态组合
4. **查询效率**
  - 可以直接按具体业务场景查询，如"查询所有退款中的订单"
  - 索引效率更高，不需要复杂的状态判断
5. 可以直接按具体业务场景查询，如"查询所有退款中的订单"
6. 索引效率更高，不需要复杂的状态判断
7. **并发处理**
  - 不同子系统可以并行处理订单状态
  - 减少状态更新冲突，如售后系统更新不会影响发货状态
8. 不同子系统可以并行处理订单状态
9. 减少状态更新冲突，如售后系统更新不会影响发货状态
10. **系统解耦**
  - 各个子系统只需关注自己的状态字段
  - 便于后续系统扩展，如增加新的业务流程
11. 各个子系统只需关注自己的状态字段
12. 便于后续系统扩展，如增加新的业务流程


具体实现建议：

1. **状态定义**：


-- 主状态
ENUM main_status (
    CREATED,    -- 已创建
    PAID,       -- 已支付
    SHIPPED,    -- 已发货
    DELIVERED,  -- 已送达
    COMPLETED,  -- 已完成
    CLOSED      -- 已关闭
)

-- 售后状态
ENUM after_sale_status (
    NONE,              -- 无售后
    RETURN_REQUESTED,  -- 申请退货
    RETURN_APPROVED,   -- 退货审核通过
    RETURN_REJECTED,   -- 退货被拒绝
    RETURN_SHIPPED,    -- 退货已寄出
    RETURN_RECEIVED    -- 退货已收到
)

-- 退款状态
ENUM refund_status (
    NONE,               -- 无退款
    REFUND_REQUESTED,   -- 申请退款
    REFUND_PROCESSING,  -- 退款处理中
    REFUND_COMPLETED,   -- 退款完成
    REFUND_FAILED      -- 退款失败
)

1. **状态约束**：


- 用代码层面的状态机来控制状态流转
- 确保状态更新的原子性
- 记录状态变更历史


1. **查询优化**：


- 对常用状态字段建立索引
- 针对高频查询场景建立组合索引


这样的设计既保证了业务的清晰性，又兼顾了系统的可扩展性和性能。