# 【20241212】开发、测试工具（IT助手）二期

# 

# 一、一期回顾

### 1.1 业务背景

 测试环境验不出，线上事故频繁出，多次重复发布打补丁，严重影响生产环境的正常使用，浪费很多时间去修复错误单数据。

### 1.2 痛点分析

1. 部分场景在测试环境没有条件测试
  1. 部分平台没有测试店铺和全种类型商品
2. 部分平台没有测试店铺和全种类型商品
3. 场景太多，在测试时间内无法全部覆盖，覆盖测试自测和测试成本极高
  1. **没有做自动化测试**
  2. 数据依赖于三方平台，无法做自动化测试用例
4. **没有做自动化测试**
5. 数据依赖于三方平台，无法做自动化测试用例
6. 部分场景下复现事故流程复杂
  1. 订单不可重复使用
  2. 订单流程不可逆转
7. 订单不可重复使用
8. 订单流程不可逆转


### 1.3 一期设计规划

 对上面的问题的核心是因为外部数据不可控。只要解决这个问题再引入【**自动化测试】**，提及的问题都可以得到解决。

为此引入了【**模拟服务**】、【**动态路由**】能力，解决外部数据不可控。【**模拟服务**】提供外部数据支撑；【**动态路由**】允许发起端控制链路走向到指定服务；【**数据快照**】提供基于业务的数据回滚能力。

整个链路我们都可以控制后，开发，联调，测试中的很多东西就变动很容易了。

#### 1.3.1 功能依赖关系

true一期依赖关系falseautotoptrue3114

### 1.4 目标达成情况

目标：解决【数据依赖于三方平台，无法做自动化测试用例】问题。达到可用水平。

#### 1.4.1 具体实现

完成动态路由和mock服务基础功能，实现对三方平台接口解耦。

true1falseautotoptrue5813

#### 1.4.2 动态路由达成情况

true3falseautotoptrue10113

#### 1.4.3 虚拟服务达成情况

true2falseautotoptrue9114

### 1.5 一期设计缺陷

1. mock数据导入过程太复杂
  1. ETL方案导入mock数据无可行性
  2. 需要mock的数据都需要服务独立记录，工作量大，可迁移性低。
2. ETL方案导入mock数据无可行性
3. 需要mock的数据都需要服务独立记录，工作量大，可迁移性低。
4. 不能清晰的知道整个链路情况
  1. 没有链路图，无法知道该在哪些阶段配置mock数据
  2. mock数据不知道配置到哪个接口上
5. 没有链路图，无法知道该在哪些阶段配置mock数据
6. mock数据不知道配置到哪个接口上


## 二、测试辅助工具整体规划调整

### 目标：

1. 更简便的完成回归测试。
2. 让开发对不可逆操作可以重复执行。


### 实现路径：

建立一套可视化测试平台，实现测试用例、测试数据统一管理；解决三方数据依赖问题。

true依赖关系图falseautotoptrue56111

## 二、功能图

### 2.1 动态路由

true动态路由falseautotoptrue10114

### 2.2 Mock-Server

true自动化测试falseautotoptrue9619

### 2.3 自动化测试

true测试流程编排falseautotoptrue10515

### 2.4 数据快照

true数据快照falseautotoptrue7913

## 三、Mock服务二期概要设计

目标：

 让mock服务变得好用

实现路径：

 1.录制mock数据

 2.可视化

true自动化测试falseautotoptrue9619

### 3.1 可视化配置原型图

、

规则设置

### 3.2 技术设计

#### 3.2.1 接口数据存储现状

true4falseautotoptrue5011

#### 3.2.2 配置流程图

true录制功能falseautotoptrue3724

#### 3.2.4 录制原理

mock服务做出前置代理，记录所有的请求和响应，转换mock对象模型。

true代理记录falseautotoptrue8212

#### 3.2.5 监控服务内部数据变化

可以做除了数据库之外的数据快照。

true5falseautotoptrue8211

### 3.3 采集Mock数据技术调研

#### 3.3.1 SpringBoot actuator

结论：不能满足需求

原因：

 目前通过Filter拦截了本应用的入口流量，无法拦截出口流量

#### 3.3.2 Sentinel

结论：不能满足需求

原因：

 通过AOP拦截了应用入口流量，通过Feign.Builder 抓去了部分出口流量，但是其他非spring管理的客户端流量没有抓取，由于通过是通过sdk方式接入，无法将代码植入到http客户端中，扩展开发任然不能满足需求

#### 3.3.3 skyWalking

结论：不能满足需求

原因：

 通过探针方式植入代码，目前功能只是透传了请求头，没有对请求报文和响应报文做拦截，需要自定义开发，各个客户端不同，通过植入http客户端方式去抓取响应，需要适配多种客户端，响应由于流数据只能取一次，也有可能会影响到对客户端的配置。

#### 3.3.4 开源抓包工具

结论：找不到适合部署到k8s中采集数据后保存到本地的工具

3.3.5 代理抓包记录

结论：可行。

原因：

 目前发现的只要是录制http请求功能的软件基本都是通过这个方案来实现抓包的。没有找到合适的代理抓包工具能集成到我们系统，需要自研开发。

## 四、自动化测试编排概要设计

### 4.1 功能图

true测试流程编排falseautotoptrue10515

### 4.2 自动化测试编排原型图

### 4.3 竞品分析

| 名称 | 接口录制、自动化测试录制 | 深度接口mock | 断言 | 流程控制、条件控制 | WEB UI自动化测试 | 多人共享、权限隔离 | 接口管理、自动化测试管理 | 接口重放 | 可扩展性 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Apifox |  |  |  |  |  | 收费 |  |  | 无法扩展 |
| PostMain | 使用麻烦 |  | 不好用 |  |  | 收费 |  |  | 无法扩展 |
| 自研 |  |  |  |  |  |  |  |  | 可根据需求自定义 |


使用人群定位为开发和测试；开发可以使用它来完成接口重放和自测。测试也可以使用它来实现回归测试编排，可解耦三方服务。虽然做完录制mock功能后其他工具也可以通过mock服务使用解耦三方服务的能力，但是由于需要添加特定的请求头，不方便进行维护，集成度太低，学习成本较高。所以才决定自研测试编排功能。

## 五、数据快照

true数据快照falseautotoptrue7913

### 5.1 技术设计

#### 5.1.1 应用架构图

true数据快照架构图falseautotoptrue4411

#### 5.1.2 快照记录流程

true数据快照流程图falseautotoptrue6112

#### 5.1.3 回滚流程

true数据快照回滚图falseautotoptrue6112

#### 5.1.4 SQL解析能力

站在巨人肩膀上干活。 复用seata做回滚镜像的能力。

UPDATE t_product SET count = 1 WHERE product code = '001'

#### 5.1.5 回滚风险

由于数据可能会有依赖性或同一个主键数据被操作多次的情况，由于现有技术情况，不是所有情况程序都能识别出回滚的风险，如果此时操作了回滚，就会造成脏数据的产生，从这点上来说，数据快照不是一个安全的程序。

但是换个思路去思考，我们没有数据快照的时候为了能够重复执行同一条测试数据，就会手动的去修改表数据，修改的范围是通过对业务的熟悉和阅读大量代码确定的，认为是可以修改的；数据快照只是帮助我们确定修改范围和提供快速回滚的能力，至于是否能进行回滚，交给回滚人员去确定。