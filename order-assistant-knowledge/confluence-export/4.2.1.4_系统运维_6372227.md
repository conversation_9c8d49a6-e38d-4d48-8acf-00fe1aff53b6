# 4.2.1.4 系统运维

1.常见问题排查列表

| 标题 | 问题描述 | 解决方案 | 备注 |
| --- | --- | --- | --- |
| 打印程序海典、心云版本切换问题 | 打印程序中途更名一次，从hydee.printserver 更名为 xinyun.printserver 这使得无法通过热更新来执行最新得程序。具体表现为 新老版本程序共存。 | 1.点击程序文件夹中的 old_unins.bat 卸载老应用。2.点击 【 启动打印服务.bat】 启动最新的应用。 3.删除 一下三个文件。Hydee.PrintServer.exe Hydee.PrintServer.exe.config Hydee.PrintCore.dll | 只有贵州部分门店需要此处理。 |
| 切店问题：心云老订单点击配送报错1038错误 | 切店问题：在切店的时候订单下发到雨诺然后在配送平台配送完成，心云再次点击配送时.net转发到配送平台配送平台报错并返回错误信息 | 在雨诺OMS先确认数据配送信息是已完成，是的话在心云中直接修改订单状态为已完成 |  |
| 待下账/下账失败问题: 拣货信息丢失 | 操作日志显示有拣货信息  但是数据库内没有拣货信息 | 1.1查询待下账拣货信息缺失 SELECT o.third_order_no,[od.id](http://od.id), od.order_no ,opi.order_detail_id from order_info o LEFT JOIN order_detail od on o.order_no=od.order_no LEFT JOIN order_pick_info opi on [od.id](http://od.id)=opi.order_detail_id where o.erp_state=30 and o.order_state=100 and o.created>='2023-11-29 00:00:00' and opi.order_detail_id is null order by o.third_order_no;  1.2查询下账失败拣货信息缺失  SELECT o.third_order_no,[od.id](http://od.id), od.order_no ,opi.order_detail_id from order_info o LEFT JOIN order_detail od on o.order_no=od.order_no LEFT JOIN order_pick_info opi on [od.id](http://od.id)=opi.order_detail_id where o.erp_state=99 and opi.order_detail_id is null order by o.third_order_no;   2.更新订单状态为20重新页面手动拣货. update order_info set order_state=20 , erp_state=99 where third_order_no in(); |  |
| 下账失败问题:  订单存在下账金额与商品合计金额不相等的错误! | 订单存在下账金额与商品合计金额不相等的错误! | 暂时交给国枫/世达/国华处理; SELECT * from order_info where erp_state=99 and online_store_code like 'C%' and extend_info like '%下账金额%' ; |  |
| 下账失败问题:  订单存在金额错误[ (Round((发货数量*商品单价,2)-销售单的分摊折扣)<>销售单的分摊金额 ] | 1.报错:订单存在金额错误[ (Round((发货数量*商品单价,2)-销售单的分摊折扣)<>销售单的分摊金额 ]  2.查看订单.是存在复杂换货  3.订单下账状态改为30.重新自动下账 从api-gateway抓取原报文  4.查明问题修复明细数据 | 1.问题产生,是订单发生了复杂换货.明细信息的原商品的原价换货后 未变化,导致下账时使用了原价 造成了公式不匹配. SELECT * from order_detail where order_no='1785143070946488577';  2.修复数据 update order_detail set original_price=price where order_no='1785143070946488577';  3.修改下账状态 改为30重新自动下账     订单原始价*数量 - iscount_amount=下账金额 |  |
| 下账失败：分摊金额存在负数 |  | 1.先查询范围 SELECT * from order_info where erp_state=99 and online_store_code like 'C%' and extend_info like '%分摊折扣为负数%' ; 2.修复数据 平台优惠计算有问题。目前先手动修复数据update dscloud.order_detail set original_price= , price= ,total_amount= ,actual_amount= ,discount_share= where id= ;按照上述类似方法来处理订单detail |  |
| 下账失败：库存不足 |  | 库存不足 需要店员核对是否有库存，有库存的话就直接在POS收到下账 |  |
| 订单处理完成，但是平台回调处理异常 | 心云平台状态为配送中 或者待配送。但 第三方平台实际已经完成。 | 直接修改 dscloud.order_info 的order_state=100 |  |