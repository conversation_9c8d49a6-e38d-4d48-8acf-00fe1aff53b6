# 产品/业务导出数据汇集

1.常见导数汇集

| 标题 | 问题描述 | 解决方案 | 备注 |
| --- | --- | --- | --- |
| 获取门店订单与拣货员关系 | 1.因为门店POS未更新版本.自动下账订单未关联具体拣货员.造成业绩统计遗漏. 业务会要求导出具体关联关系 | SELECT  o.created as '下单时间', o.online_store_code as '门店编码' , case o.third_platform_code when 11 THEN '京东到家' when 24 THEN '饿百' when 27 THEN '美团' when 43 THEN '微商城' else '未知' end as '平台', o.third_order_no as '平台订单号', case o.order_state when 5 THEN '待处理' when 10 THEN'待接单' when 20 THEN '待拣货' when 30 THEN '待配送'  when 40 THEN '配送中' when 100 THEN '已完成' when 102 THEN '已取消' when 101 THEN '已关闭'   else '未知' end as '订单状态', case o.erp_state when 20 then '待锁定' when 30 then '待下账' when 99 then '下账失败' when 100 then '已下账' when 110 then '已取消' when 120 then '已退款' else '未知' end as '下账状态', o.pick_operator_id as '拣货员id', o.pick_operator_name as '拣货员名字', e.bill_total_amount as '订单下账总金额', od.erp_code as '商品sku', od.commodity_name as '商品名字', od.commodity_count-od.refund_count as '实际购买数(购买数-退货数)', od.bill_price as '下账价格', od.actual_net_amount as '下账商品合计金额' from order_info o  LEFT JOIN erp_bill_info e on o.order_no=e.order_no LEFT JOIN order_detail od on o.order_no=od.order_no where  o.service_mode='O2O' and o.created>'2023-11-20 00:00:00' and od.status<10 ; |  |
| 平台订单金额字段解析错误,修补数据后,财务对账需要. | 1.由于之前金额解析不对,造成平台运费优惠/商家运费优惠 解析失败. 修复数据后重新导数给财务 | 1. 导出订单主数据 select oi.order_no as '系统订单号', oi.third_order_no as '平台订单号', oi.create_time as '订单时间',dos.platform_name as '第三方平台名称', oi.source_online_store_code as '平台店铺ID',  dos.online_store_name as '线上门店名称', oi.online_store_code as '下单门店ID', case oi.order_state when 5 THEN '待处理' when 10 THEN'待接单' when 20 THEN '待拣货' when 30 THEN '待配送' when 40 THEN '配送中' when 100 THEN '已完成' when 102 THEN '已取消' when 101 THEN '已关闭' else '未知' end as '订单状态', case odr.delivery_type when 1 then '平台配送' when 2 then '平台合作方配送' when 3 then '门店配送' when 4 then '门店自提' when 5 then '快递' else '未知' end as '配送方式', odr.delivery_plat_name as '配送方式小类', opi.buyer_actual_amount as '买家实付', opi.merchant_actual_amount as '商家实收', opi.total_amount as '商品总金额', opi.delivery_fee as '运费', opi.brokerage_amount as '交易佣金', odr.delivery_tip as '配送服务费', opi.merchant_total_discount_sum as '订单总优惠', opi.discount_fee_dtl as '平台商品优惠', opi.platform_delivery_fee_discount as '平台运费优惠', opi.merchant_total_discount_sum_not_delivery_fee as '商家商品优惠', opi.merchant_delivery_fee_discount as '商家运费优惠', opi.merchant_delivery_fee as '商家配送费', opi.platform_delivery_fee as '平台配送费', opi.platform_pack_fee as '平台包装费', opi.merchant_pack_fee as '商家包装费', ebi.bill_total_amount as '下账总金额', ebi.plat_brokerage_amount as '下账平台收取佣金', ebi.bill_commodity_amount as '下账商品金额' from order_info oi  left join ds_online_store dos on oi.online_store_code = dos.online_store_code and oi.third_platform_code = dos.platform_code  left join order_pay_info opi on oi.order_no = opi.order_no  left join erp_bill_info ebi on oi.order_no = ebi.order_no left join order_delivery_record odr on oi.order_no = odr.order_no  where oi.created >= '2023-12-01 00:00:00' and oi.created < '2023-12-21 00:00:00' and oi.online_store_code like 'C%';  2.导出订单明细数据  select oi.order_no as '系统订单号', oi.third_order_no as '平台订单号', od.commodity_count as '商品数量',od.discount_amount as '商品明细优惠', od.original_price as '商品原单价', od.bill_price as '下账价格', od.actual_net_amount as '下账金额' from order_detail od left join order_info oi on od.order_no = oi.order_no  where oi.created >= '2023-12-01 00:00:00' and oi.created < '2023-12-21 00:00:00' and oi.online_store_code like 'C%' ; |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |