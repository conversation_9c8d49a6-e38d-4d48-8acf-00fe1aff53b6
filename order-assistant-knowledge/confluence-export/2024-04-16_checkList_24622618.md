# 2024-04-16 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 诺和2期 | ``` hydee-business-order-web ``` | 1 |  |  |  | 汪骁 |  |  | 2024-04-16 |  |
| ``` ydjia-merchant-customer ``` | 2 | feature-order-381nuohe_mzl |  |  |  |  |
| 诺和2期 | hydee-middle-order | 3 | feature-order-381 |  |  | 汪骁 |  |  | 2024-04-16 |  |
|  | middle-id |  |  |  |  |  |  |  |  |  |
| [一心到家V1.1.6-支付配置](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18630185&src=breadcrumbs-parent) | hydee-pay-finance |  |  |  |  |  |  |  |  |  |
| 商品组件优化 | hydee-middle-goods | - | [feature/YXDJ-287](https://yxtgit.hxyxt.com/webapp/ydjia-merchant-manager/-/tree/feature/YXDJ-287) |  |  | @汪骁 |  |  | 2024-04-16 |  |
| 商品组件优化 | ydjia-merchant-manager | - | [feature/YXDJ-287](https://yxtgit.hxyxt.com/webapp/ydjia-merchant-manager/-/tree/feature/YXDJ-287) |  |  | @汪骁 |  |  | 2024-04-16 |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | t_organization_tree | 门店机构表 | | `CREATE TABLE `t_organization_tree` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'id'``,` ````org_parent` varchar(``36``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'父节点code'``,` ````org_code` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构编码'``,` ````org_name` varchar(``256``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构名称'``,` ````org_type```int` `DEFAULT NULL COMMENT``'机构类型（1：公司，2：分部,3:区域,4:门店）'``,` ````open_status` tinyint DEFAULT``'1'` `COMMENT``'状态（0：停用，1：启用）'``,` ````create_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'创建人'``,` ````create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT``'创建时间'``,` ````modify_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'修改人'``,` ````modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT``'末次修改时间'``,` ````parent_path` varchar(``255``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'编码全路径'``,` ```PRIMARY KEY (`id`),` ```UNIQUE KEY `idx_or_code` (`org_code`) USING BTREE,` ```KEY `idx_parent_or_code` (`org_parent`) USING BTREE` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT=``'门店机构表'``;` | | `CREATE TABLE `t_organization_tree` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'id'``,` ````org_parent` varchar(``36``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'父节点code'``,` ````org_code` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构编码'``,` ````org_name` varchar(``256``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构名称'``,` ````org_type```int` `DEFAULT NULL COMMENT``'机构类型（1：公司，2：分部,3:区域,4:门店）'``,` ````open_status` tinyint DEFAULT``'1'` `COMMENT``'状态（0：停用，1：启用）'``,` ````create_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'创建人'``,` ````create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT``'创建时间'``,` ````modify_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'修改人'``,` ````modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT``'末次修改时间'``,` ````parent_path` varchar(``255``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'编码全路径'``,` ```PRIMARY KEY (`id`),` ```UNIQUE KEY `idx_or_code` (`org_code`) USING BTREE,` ```KEY `idx_parent_or_code` (`org_parent`) USING BTREE` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT=``'门店机构表'``;` |
| `CREATE TABLE `t_organization_tree` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'id'``,` ````org_parent` varchar(``36``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'父节点code'``,` ````org_code` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构编码'``,` ````org_name` varchar(``256``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'机构名称'``,` ````org_type```int` `DEFAULT NULL COMMENT``'机构类型（1：公司，2：分部,3:区域,4:门店）'``,` ````open_status` tinyint DEFAULT``'1'` `COMMENT``'状态（0：停用，1：启用）'``,` ````create_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'创建人'``,` ````create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT``'创建时间'``,` ````modify_name` varchar(``40``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'修改人'``,` ````modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT``'末次修改时间'``,` ````parent_path` varchar(``255``) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT``'编码全路径'``,` ```PRIMARY KEY (`id`),` ```UNIQUE KEY `idx_or_code` (`org_code`) USING BTREE,` ```KEY `idx_parent_or_code` (`org_parent`) USING BTREE` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT=``'门店机构表'``;` |
| 2 | t_pay_organization_config | 子公司支付通道以及支付配置表 | | `CREATE TABLE `t_pay_organization_config` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'主键id'``,` ````pay_channel_id` bigint NOT NULL COMMENT``'支付通道id'``,` ````pay_channel_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道编码'``,` ````pay_channel_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道名称'``,` ````org_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'机构编码，t_organization_tree表or_code'``,` ````pay_customer_id` bigint NOT NULL COMMENT``'客户id'``,` ````pay_customer_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户编码'``,` ````pay_customer_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户名称'``,` ````channel_business_no` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'通道商户号'``,` ````pay_company_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业编码'``,` ````pay_company_name` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业名称'``,` ````pay_config_detail` json DEFAULT NULL COMMENT``'配置明细'``,` ````remark` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'备注'``,` ````create_user` bigint DEFAULT NULL,` ````create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,` ````modify_user` bigint DEFAULT NULL,` ````modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,` ````is_delete` tinyint NOT NULL DEFAULT``'2'` `COMMENT``'是否删除 1：是 2：否'``,` ```PRIMARY KEY (`id`) USING BTREE,` ```KEY `idx_org_code` (`org_code`) USING BTREE,` ```KEY `idx_orgCode_payChannel` (`org_code`,`pay_channel_code`)` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT=``'子公司支付通道以及支付配置表'``;` | | `CREATE TABLE `t_pay_organization_config` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'主键id'``,` ````pay_channel_id` bigint NOT NULL COMMENT``'支付通道id'``,` ````pay_channel_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道编码'``,` ````pay_channel_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道名称'``,` ````org_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'机构编码，t_organization_tree表or_code'``,` ````pay_customer_id` bigint NOT NULL COMMENT``'客户id'``,` ````pay_customer_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户编码'``,` ````pay_customer_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户名称'``,` ````channel_business_no` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'通道商户号'``,` ````pay_company_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业编码'``,` ````pay_company_name` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业名称'``,` ````pay_config_detail` json DEFAULT NULL COMMENT``'配置明细'``,` ````remark` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'备注'``,` ````create_user` bigint DEFAULT NULL,` ````create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,` ````modify_user` bigint DEFAULT NULL,` ````modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,` ````is_delete` tinyint NOT NULL DEFAULT``'2'` `COMMENT``'是否删除 1：是 2：否'``,` ```PRIMARY KEY (`id`) USING BTREE,` ```KEY `idx_org_code` (`org_code`) USING BTREE,` ```KEY `idx_orgCode_payChannel` (`org_code`,`pay_channel_code`)` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT=``'子公司支付通道以及支付配置表'``;` |
| `CREATE TABLE `t_pay_organization_config` (` ````id` bigint NOT NULL AUTO_INCREMENT COMMENT``'主键id'``,` ````pay_channel_id` bigint NOT NULL COMMENT``'支付通道id'``,` ````pay_channel_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道编码'``,` ````pay_channel_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'支付通道名称'``,` ````org_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'机构编码，t_organization_tree表or_code'``,` ````pay_customer_id` bigint NOT NULL COMMENT``'客户id'``,` ````pay_customer_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户编码'``,` ````pay_customer_name` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT``'客户名称'``,` ````channel_business_no` varchar(``150``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'通道商户号'``,` ````pay_company_code` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业编码'``,` ````pay_company_name` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT``'企业名称'``,` ````pay_config_detail` json DEFAULT NULL COMMENT``'配置明细'``,` ````remark` varchar(``120``) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT``'备注'``,` ````create_user` bigint DEFAULT NULL,` ````create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,` ````modify_user` bigint DEFAULT NULL,` ````modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,` ````is_delete` tinyint NOT NULL DEFAULT``'2'` `COMMENT``'是否删除 1：是 2：否'``,` ```PRIMARY KEY (`id`) USING BTREE,` ```KEY `idx_org_code` (`org_code`) USING BTREE,` ```KEY `idx_orgCode_payChannel` (`org_code`,`pay_channel_code`)` `) ENGINE=InnoDB AUTO_INCREMENT=``1` `DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT=``'子公司支付通道以及支付配置表'``;` |
| 3 | t_pay_channel_customer |  | ALTER TABLE `h3_pay_core`.`t_pay_channel_customer`  ADD INDEX `idx_channel_business_no`(`channel_business_no`);ALTER TABLE `h3_pay_core`.`t_pay_channel_customer`  ADD COLUMN `config_type` tinyint(0) NULL DEFAULT 1 COMMENT '1 客户自配置 2 沿用子公司配置' AFTER `business_mode`; |
| 4 | t_organization_tree初始化数据 |  |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-pay-finance | application.properties |  | ``` rocketmq.name-server=10.100.10.62:8100;10.100.10.59:8100 ```rocketmq.producer.group = PGROUP_${[spring.application.name](http://spring.application.name/)}_${spring.profiles.active} rocketmq.consumer.enable-msg-trace = true# topic message.topicList[0].topic = TOPIC_MIDDLE_DATA # 是否需要环境隔离，为true时，topic会加后缀：_{$spring.profiles.active} message.topicList[0].enabledIsolation = true # 最大线程数 message.topicList[0].consumeThreadMax = 4 # 最小线程数 message.topicList[0].consumeThreadMin = 1 # 最大重试次数 message.topicList[0].maxReconsumeTimes = 3# 是否开启子公司配置同步给门店 pay.config.org2store = true |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
| 海典h2下账切换 |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |