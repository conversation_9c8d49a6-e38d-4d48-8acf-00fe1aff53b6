# 【20250624】合规追溯码需求技术方案

****

**优秀技术方案参考：**

**本次需求PRD**： 

JIRA链接：一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5684

# 一、背景

## 1.1 业务背景

合规平台目前是公司统一的对外合规处理，其包含合规的基础属性，如：门店是否需要录入追溯码，如商品是否录入追溯码，追溯码上传监管平台；等等

## 1.2 痛点分析

*当前业务痛点分析，为什么要做*

## 1.3 系统现状

*业务现有流程图、架构等，为下述技术方案设计提供背景依据，尤其是已有功能的迭代改造。*

# 二、目标

## 2.1 本期目标

### 2.1.2 业务目标

完成合规平台的对接，包含追溯码扫码判断，追溯码合规判；

优化追溯码校验，接口调整为追溯码的准确性验证和合理性校验；

下账数据调整：解决弱管控，录入几个商品追溯码，则下账几个商品追溯码；

### 2.1.3 技术目标

*一般是技术指标（SLA）、沉淀基础服务、节约开发成本等。*

## 2.2 中长期目标

*愿景、长远规划。*

# 三、需求分析

## 3.1 场景分析

*需求分析环节，研发消化需求后从研发视角将需求讲清楚，项目团队产研测再次达成对业务和需求的一致理解。 一般采用用例图、需求功能表格等形式归纳涉及模块和功能点，帮助项目团队人员对齐、消化需求。*

*一般的需求功能点表格具有局限性，要准确表达一个需求点，可以参考用例表，但不如用例图方便、清晰，所以采用用例图加必要的功能归纳表格来分析需求。*

需求功能点：

1. 针对是否录入追溯码，根据店铺设置字段（MDM,统一接口），**是否录入追溯码判断！**
  1. **商品追溯码，新对接RPC接口**
  2. **店铺查询配置，待寻找？**
2. **商品追溯码，新对接RPC接口**
3. **店铺查询配置，待寻找？**
4. B2C项目：增加追溯码校验查询接口，返回批号信息
  1. 入参：追溯码、条形码、商品编码
5. 入参：追溯码、条形码、商品编码
6. B2C订单处理→待发货→拣货复核，录入追溯码
  1. **只录入批号，是否走合规平台校验?**
  2. 历史功能已经存在批号录入
  3. 无码录入：默认20个0
  4. cn.hydee.business.order.b2c.yxtadapter.infrastructure.domainserviceimpl.pick.PickInfoServiceImpl#upOrderBatchNo
  5. 增加新表和老表的切换，不做数据迁移
  6. 
7. **只录入批号，是否走合规平台校验?**
8. 历史功能已经存在批号录入
9. 无码录入：默认20个0
10. cn.hydee.business.order.b2c.yxtadapter.infrastructure.domainserviceimpl.pick.PickInfoServiceImpl#upOrderBatchNo
11. 增加新表和老表的切换，不做数据迁移
12. 
13. B2C订单处理→待发货→扫描发货
  1. 界面布局改造
    1. 增加商品图片返回
    2. 增加生产商
  2. 增加商品图片返回
  3. 增加生产商
  1. 增加商品图片返回
  2. 增加生产商
14. 界面布局改造
  1. 增加商品图片返回
  2. 增加生产商
15. 增加商品图片返回
16. 增加生产商
17. 退货审核
  1. 原型页面改造；改造接口：/businesses-gateway/b2c/1.0/refund/getReturnGoods/Detail
  2. 退货商品信息，增加是否需要录入追溯码的标识返回；；根据正向订单判断
  3. **增加根据追溯码、商品编码查询商品信息、追溯码、批号信息、部分退货的追溯码标识已退。**
  4. **提交退货审核**
    1. **businesses-gateway/b2c/1.0/refund/returnGoods/audit**
  5. **businesses-gateway/b2c/1.0/refund/returnGoods/audit**
  1. **businesses-gateway/b2c/1.0/refund/returnGoods/audit**
18. 原型页面改造；改造接口：/businesses-gateway/b2c/1.0/refund/getReturnGoods/Detail
19. 退货商品信息，增加是否需要录入追溯码的标识返回；；根据正向订单判断
20. **增加根据追溯码、商品编码查询商品信息、追溯码、批号信息、部分退货的追溯码标识已退。**
21. **提交退货审核**
  1. **businesses-gateway/b2c/1.0/refund/returnGoods/audit**
22. **businesses-gateway/b2c/1.0/refund/returnGoods/audit**
23. **下账单**
  1. **cn.hydee.business.order.b2c.service.account.event.CreateAccountEventListener**
  2. **正向、逆向单增加追溯码同步！ OrderDetailDto**
  3. **新老逻辑切换查询。**
  4. **疑问点，同步海典第三方，追溯码字段的对接？**
  5. ****
24. **cn.hydee.business.order.b2c.service.account.event.CreateAccountEventListener**
25. **正向、逆向单增加追溯码同步！ OrderDetailDto**
26. **新老逻辑切换查询。**
27. **疑问点，同步海典第三方，追溯码字段的对接？**
28. ****


## 3.2 业务流程



![未命名绘图](./resources/未命名绘图_2.png)



## 3.3 非功能性需求

*需求的流量、用户量、数据量、性能要求、核心技术挑战等非功能需求识别。*

# 四、整体设计

*概要设计/战略设计*

## 4.1 统一语言定义

*业务、技术名词解释等，为方案文档创造统一的上下文语境。*

## 4.2 架构设计

*架构五视图：逻辑架构、开发架构、物理架构、运行架构、数据架构，根据需要进行选择.后期主要是不断迭代演进的架构推演，改动的或新增的模块特殊颜色标识。*

## 4.3 模型设计

*不仅仅是一个图，最好有业务建模分析的过程；传统项目的ER实体关系图；DDD项目的领域模型图。*

### 4.3.1 ER图



![er](./resources/er.png)



### 4.3.2 DDD领域模型图

### 4.3.4 DDD领域包结构

| 领域设计 | 英文命名 | 备注 |
| --- | --- | --- |
|  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |  |
|  |  |  |


## 4.4 核心问题

*需要探讨的核心技术问题，扩展性、数据迁移、兼容等核心技术挑战和解决方案*

|  | 问题描述（Q） | 问题解答 / 解决方案（A） |
| --- | --- | --- |
| 1 | 退货单处的添加追溯码【扫描录入，选择录入】是否需要校验追溯码 是否属于此订单商品正向单录入的码 还是手动录入直接落库进行售后单下账 | 本期暂不校验与正向单一致 |


# 五、详细设计

## 5.1 模块详细设计

*分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比*

### xxx模块

改动的项目代码：

| 项目名称 | 分支 | 是否合并主干 |
| --- | --- | --- |
| hydee-business-order-web | feature/ORDER-5684/trace-code | 2 incomplete |
|  |  |  |


## 5.2 存储设计

*新增、修改的字段DDL；索引设计；数据量预估，分库分表设计；有时需要存储选型分析方案：缓存、es等*

上线前以测试环境为准

dscloud数据库

sqlMidnighttrueuse dscloud;  

ALTER TABLE `dscloud`.`account_order` 
MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`;

ALTER TABLE `dscloud`.`account_refund_detail` 
MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`;

ALTER TABLE `dscloud`.`account_refund` 
MODIFY COLUMN `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间' AFTER `create_time`;


ALTER TABLE `dscloud`.`account_refund_detail` 
ADD COLUMN `trace_code_type` varchar(30) NULL COMMENT '追溯码类型' AFTER `trace_codes`;

ALTER TABLE `dscloud`.`account_order_detail` 
ADD COLUMN `trace_code_type` varchar(30) NULL COMMENT '追溯码类型' AFTER `trace_codes`;

  ALTER TABLE `dscloud`.`account_order_detail` 
ADD COLUMN `trace_codes` varchar(1000) NULL COMMENT '追溯码，多个使用,号分隔' AFTER `batch_no`;

ALTER TABLE `dscloud`.`account_refund_detail` 
ADD COLUMN `trace_codes` varchar(1000) NULL COMMENT '追溯码，多个使用,号分隔' AFTER `batch_no`;


CREATE TABLE `offline_order_detail_pick`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(50)  DEFAULT NULL COMMENT '内部订单号,自己生成',
  `order_detail_no` varchar(50)  DEFAULT NULL COMMENT '内部明细编号,自己生成',
  `order_detail_pick_no` varchar(50)  DEFAULT NULL COMMENT '订单明细拣货批号唯一号',
  `erp_code` varchar(20)  DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(50)  DEFAULT NULL COMMENT '商品批号',
  `count` decimal(16, 6) DEFAULT NULL COMMENT '数量',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) 
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云订单明细拣货信息-待迁移到发货单' ;




CREATE TABLE `offline_order_detail_trace`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50)  DEFAULT NULL COMMENT '内部订单号,自己生成',
  `order_detail_no` varchar(50)  DEFAULT NULL COMMENT '内部明细编号,自己生成',
  `order_detail_pick_no` varchar(50)  DEFAULT NULL COMMENT '订单明细拣货批号唯一号',
  `erp_code` varchar(20)  DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(50)  DEFAULT NULL COMMENT '商品批号',
  `trace_code` varchar(50)  DEFAULT NULL COMMENT '追溯码',
  `nhsa_report_flag` varchar(20)  DEFAULT NULL COMMENT '医保上报标识',
  `dra_report_flag` varchar(20)  DEFAULT NULL COMMENT '药监上报标识',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) 
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云订单明细追溯码信息-待迁移到发货单' ;

  
  
  
  
  CREATE TABLE `offline_refund_detail_pick`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` bigint NOT NULL COMMENT '退款单号',
  `refund_detail_no` bigint  NOT NULL COMMENT '售后单明细ID，关联refund_detail表id字段',
  `refund_detail_pick_no` bigint  NOT NULL COMMENT '退单下账单明细拣货唯一号',
  `erp_code` varchar(20)  DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(50)  DEFAULT NULL COMMENT '商品批号',
  `count` decimal(16, 6) DEFAULT NULL COMMENT '数量',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) 
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云退单订单明细拣货信息' ;



CREATE TABLE `offline_refund_detail_trace`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` bigint NOT NULL COMMENT '退款单号',
  `refund_detail_no` bigint  NOT NULL COMMENT '售后单明细ID refund_detail表id字段',
  `refund_detail_pick_no` bigint  NOT NULL COMMENT '退单下账单明细拣货唯一号，内部生成 关联offline_refund_detail_pick表refund_detail_pick_no字段',
  `erp_code` varchar(20)  DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(50)  DEFAULT NULL COMMENT '商品批号',
  `trace_code` varchar(50)  DEFAULT NULL COMMENT '追溯码',
  `nhsa_report_flag` varchar(20)  DEFAULT NULL COMMENT '医保上报标识',
  `dra_report_flag` varchar(20)  DEFAULT NULL COMMENT '药监上报标识',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `created_by` varchar(50)  DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50)  DEFAULT NULL COMMENT '更新人',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) 
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云退单订单明细追溯码信息' ;



## 5.3 接口设计

*新增、修改的接口定义；流量预估，接口性能设计；*

### 5.3.1 后端接口定义

1、增加追溯码校验查询接口，返回批号信息

**入参：**

| 字段 | 描述 | 备注 |
| --- | --- | --- |
| ecode | 追溯码 |  |
| erpCode | 商品编码 |  |


出参：{"code":"10000","msg":"操作成功","data":{}}

| 字段 | 描述 | 备注 |
| --- | --- | --- |
| code | 10000 成功 xxx：追溯码不存在 |  |
| msg |  |  |
| data |  |  |
| >>>commodity_batch_no | 商品批号 |  |


### 5.3.1 新老接口定义前端对接

拣货复核：
[http://10.4.1.221:12601/doc.html#/default/B2C订单拣货相关接口/getOrderDetailAllUsingGET](http://10.4.1.221:12601/doc.html#/default/B2C订单拣货相关接口/getOrderDetailAllUsingGET)
新增字段：

扫描发货：
[http://10.4.1.221:12601/doc.html#/default/扫描发货接口/getOrderDetailInfoUsingPOST](http://10.4.1.221:12601/doc.html#/default/扫描发货接口/getOrderDetailInfoUsingPOST)
新增字段：

退货列表新接口：[http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/getReturnGoodsListUsingGET](http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/getReturnGoodsListUsingGET)
新增字段：refundNo

调用接口：
[http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/getReturnReviewDetailUsingGET](http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/getReturnReviewDetailUsingGET)
入参：refundNo （取值列表返回字段：refundNo）

退货单审核：确定

[http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/returnGoodsAuditUsingPOST](http://10.4.1.221:12601/doc.html#/default/退款退货售后接口/returnGoodsAuditUsingPOST)

**新接口：**
追溯码校验与批号查询-b2c订单
[http://10.4.1.221:12601/doc.html#/default/b2c订单接口/getCheckTraceCodeUsingPOST](http://10.4.1.221:12601/doc.html#/default/b2c订单接口/getCheckTraceCodeUsingPOST)

## 5.4 安全设计

*时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；*

## 5.5 监控报警

*需要思考上线后如何监控，及时响应止损、回滚、降级等方案。*

# 六、质量效率

*本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。*

# 七、里程碑

*一般可以为产品研流程的关键节点，也可根据项目实际情况梳理重要进展时间节点*

|  | **里程碑** | **日期** |
| --- | --- | --- |
| 1 | 需求评审 | 2025-05-26 |
| 2 | 技术方案评审 | 2025-05-28 |
| 3 | 提测 | 2025-06-09 |
| 4 | 上线 |  |


# 八、上线方案

*兼容、回滚方案等;上线流程、SOP等, 此处填写上线清单链接。*

| *PS：技术方案设计完成后，必须在「技术方案」下建立上线清单CF子文档，参考：* |
| **上线清单链接** | https://xxxx |


# 九、项目排期

**研发工时：**pd，单测：pd（要求：单测行覆盖率>70%，核心模块 100%；行业内对于单测工时是与研发工时 1:1），联调工时：pd；

**研发时间：**2025年04月xx日-2025年xx月xx日（含研发自测）；联调时间：2025年xx月xx日-2025年xx月xx日；测试时间：2025年xx月xx日-2025年xx月xx日；上线时间：2025年xx月xx日

| 1 | B2C订单 | 拣货复核 |  |  | 1 |  |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 扫描发货 | 页面优化，字段对接 |  | 1 |  |  |  |
| 2 | 增加拣货操作 |  | 1 |  |  |  |
| 3 | 退货审核 | 页面布局调整，字段对接 |  | 1 |  |  |  |
| 4 | 整单退货 |  | 1 |  |  |  |
| 5 | 部分退货 |  | 1 |  |  |  |
| 6 | 下账单处理 | 正向单 |  | 0.2 |  |  |  |
| 7 | 逆向单 |  | 0.2 |  |  |  |
| 8 | 批量操作过滤存在追溯码的订单 |  | 0.1 |  |  |  |
| 1 | 总工时 |  | 6.5 |  |
|  | 所属系统 | 功能模块 | 功能项 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |


# 十、待办

  4 incomplete 商品是否扫码 cn.hydee.business.order.b2c.service.baseinfo.order.service.impl.OmsOrderInfoServiceImpl#getCommodityControlLevel 商品中台接口？   7 incomplete Case评审：需要录入追溯码的店铺和商品，无码录入的时候，批号是否需要填写？  8 incomplete 拣货复核Case：没有手工输入/选择批号。扫描发货存在？？     10 incomplete 下账单，勾选需要下账的追溯码；新功能，确认历史逻辑。   12 incomplete 记录操作日志；扫描发货。