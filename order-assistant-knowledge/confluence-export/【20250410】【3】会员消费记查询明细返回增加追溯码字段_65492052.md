# 【20250410】【3】会员消费记查询明细返回增加追溯码字段

### Green已上线

### 需求文档:

### JIRA:

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5097

### 项目

order-service

order-atom-service

order-sync-service 添加 科传(科传无拣货信息)、海典退单的明细拣货信息

order-framework

- order-types 添加RefundOrderDetailPickNo


### 分支

feature-vipTraceCode-new

feature-vip-traceCode

### 版本前缀

vipTraceCode-SNAPSHOT

### SDK

<dependency>    
  <groupId>com.yxt.order.atom.sdk</groupId>    
  <artifactId>order-atom-sdk</artifactId>   
  <version>vipTraceCode-SNAPSHOT</version>
</dependency> 

<dependency>
  <groupId>com.yxt.order.open.sdk</groupId>
  <artifactId>order-open-sdk</artifactId>
  <version>vipTraceCode-SNAPSHOT</version>
</dependency>

<dependency>
  <groupId>com.yxt.order.types</groupId>
  <artifactId>order-types</artifactId>
  <version>vipTraceCode-SNAPSHOT</version>
</dependency>

### SQL

  2 complete 注意当前月的下一个月表也要配置哈  

sqlCREATE TABLE `offline_refund_order_detail_pick_${seq}` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pick_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下订单退单明细拣货明细编号,内部生成',
  `refund_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单号',
  `refund_detail_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单明细唯一号',
  `erp_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批号',
  `count` decimal(16,6) DEFAULT NULL COMMENT '数量',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no_detail_no` (`refund_no`,`refund_detail_no`) USING BTREE,
  KEY `idx_pick_no` (`pick_no`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='线下退单明细拣货信息';

### Apollo配置

refund_order_detail_pick分表配置

        offline_refund_order_detail_pick:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_pick_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm

### TODO

  8 complete 同步master代码  9 complete order-service   10 complete order-atom-service   11 complete order-sync-service   12 complete order-framework