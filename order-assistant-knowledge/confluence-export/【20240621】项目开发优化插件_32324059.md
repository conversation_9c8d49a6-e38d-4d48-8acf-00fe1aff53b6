# 【20240621】项目开发优化插件

## 项目地址

[https://yxtgit.hxyxt.com/order/start-up-optimization/-/tree/feature/optimization_order_project?ref_type=heads](https://yxtgit.hxyxt.com/order/start-up-optimization/-/tree/feature/optimization_order_project?ref_type=heads)

## 前言

 在开发测试阶段频繁项目启动或单元测试启动，business-order-web项目每次启动需要84s左右时间，十分影响开发和测试效率，希望解决启动慢的问题，并提供一个可重复使用的工具帮助有相同问题的项目快速解决本地开发中启动慢的问题。

## 问题分析

 项目启动并达到可用状态会加载和初始化很多功能，这些功能的初始化总时长 ≈ 项目启动时长，对某个功能的初始化加速很复杂比如ForkJoin方式并行计算并行IO阻塞方式加速，在原理是可行，但是修改难度较大，需要从springboot加载器或者功能内部源码入手，直接放弃。

true2falseautotoptrue5614

true3falseautotoptrue7812

 通过关闭非核心功能的方式达到快速启动的目的

 true1falseautotoptrue9816

## 技术方案

 使用JavaAgent插件化的方式，在项目启动前拦截class文件，对class文件和加载的class对象进行修改，去除**非核心功能**以达到优化启动速度的目的。

### 移除注解实现原理

 虽然JavaAgent可以修改载入到内存中class对象，但一些特殊的代码不会直接使用内存中的class对象去获取注解，而是去文件中进行查找，所以除了替换掉内存中的class对象外还会在启动类同路径下创建相同包名类命的文件。

### 取消spring对象的载入原理

 在开发调试过程中发现spring对象的注入不会受到修改注解影响，为了排除指定的spring对象，通过动态创建class的方式，写一个spring的加载拦截器，将配置的spring拦截不进行初始化操作。

## 已经实现功能

1. 修改类函数内容
2. 删除类函数
3. 覆盖父类函数
4. 删除类注解
5. 删除方法注解
6. 写入class本地文件
7. 移除spring注入对象


# 项目结构（一期）

true模块关系falseautotoptrue6714

## 代码结构

根据java对象的结构定义了对应操作的实体，对class的编辑方法各自内部管理。

JClass: 类操作器

JMethod：函数操作

JSpring：spring对象操作

JAnnotation：类枚举操作

JMethodAnnotation：方法枚举操作

示例：通过配置方式对类进行修改，以达到加速项目启动目的

## 好的设计分享

大多数场景下尽量使用组件/工具的方式提供扩展，而不是框架

高内聚，低耦合，面向接口开发，善于利用public 空 protected private限定操作权限。

## 启动时间优化前后对比

## 使用方法

- 进入start-up-optimization，执行mvn clean package


- 在target下得到agent的jar包，hydee-business-order-optimization-jar-with-dependencies.jar


- 在idea中设置


设置java启动参数

-javaagent:【jar包绝对路径】

eg：

```
-javaagent:D:\useful-code\start-up-optimization\hydee-business-order-optimization\target\hydee-business-order-optimization-jar-with-dependencies.jar
```

- 启动项目，看到加速功能启动代表成功设置