# 2025-01-14 V1.3.1 checklist

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 appoll配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.2appoll%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-10-25+checkList#id-20241025checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 内购O2O门店备货中状态 | ``` middle-datasync-message third-platform-order-other hydee-business-order hydee-middle-order ydjia-merchant-customer hydee-health-customer-ui-mp-tpl ``` | ```  ``` |  |  |  |  |  |  | 2025-1-14 |  |
| 新增门店提货提醒模板 | hydee-middle-third |  |  |  |  |  |  |  |  |  |


### 三、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
| middle_member | ``` INSERT INTO middle_member.wx_message_template (id, platform, model_flag, model_code, model_type, message_type,                                                model_name, model_time, model_url, model_head, model_note, kid_list,                                                pri_tmpl_id, is_valid, create_name, create_time, update_name,                                                update_time) VALUES (307, 1, 'store_self_pick_msg', '7772', 2, 'mini_program_subscribe', '门店提货通知', '门店自提通知', '订单详情页', null, null, '1,2,4,5,6',         'rLSBGwbyCCdB5JeVpX0XK-yelk8rtitkSJ4ZUuhAqkM', 1, 'admin', '2025-01-06 11:58:43', 'admin', '2025-01-06 11:58:49'); INSERT INTO middle_member.wx_message_merchant (id, mer_code, model_flag, model_code, model_type, message_type, model_id,                                                model_title, primary_industry, deputy_industry, model_head,                                                model_content, model_note, model_example, notice_state, is_valid,                                                create_name, create_time, update_name, update_time, type, scene_type,                                                message_variable, appid, reason) VALUES (1849631257224777731, '500001', 'store_self_pick_msg', '7772', '2', 'mini_program_subscribe',         'rLSBGwbyCCdB5JeVpX0XK-yelk8rtitkSJ4ZUuhAqkM', '门店提货通知', null, null, '门店提货通知', '门店名称:{{thing1.DATA}}                门店地址:{{thing2.DATA}}                商品名称:{{thing4.DATA}}                自提码:{{number5.DATA}}                备注:{{thing6.DATA}}', null, '门店名称:江西黄庆仁栈华氏大药房南昌市新溪桥药店                门店地址:南昌市井冈山大道243号                商品名称:消栓口服液                自提码:123456                备注:请您在自提时间48小时内提货，避免逾期', 0, 1, 'admin', '2025-01-06 12:06:19', 'admin', DEFAULT, 1, 1, null,         'wx3cfef1f7990a1f68', null) ``` |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 变更前 | 修改内容 | 备注 |
| --- | --- | --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | tag|grup | 备注 |
| --- | --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| checkList | 是否完成 | 备注 |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |