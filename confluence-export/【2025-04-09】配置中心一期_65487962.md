# 【2025-04-09】配置中心一期

 一、目标

 1. 以微商城交易链路迁移为核心，先将相关配置摘出，建立配置中心项目。

二、配置中心交互方案

 整体结构

 

![整体架构](./resources/整体架构.png)



1. 程序交互图




![配置中心交互](./resources/配置中心交互.png)



order-config-center (父模块)
├── order-config-center-interface (接口层)
├── order-config-center-common (公共层)
├── order-config-center-core (核心服务层)
│   ├── config-distribution (配置分发功能)
│   ├── config-adapter (配置适配器功能)
│   └── service-manager (服务注册管理功能)
├── order-config-center-notification (事件通知层)
├── order-config-center-log (操作日志处理层)
├── order-config-center-db (数据库操作层)
├── order-config-center-cache (缓存操作层)
├── order-config-center-sdk (SDK模块，父模块)
│   ├── order-config-center-sdk-api (SDK接口)
│   ├── order-config-center-sdk-feign (Feign客户端)
│   └── order-config-center-sdk-sync (配置同步器)
└── order-config-center-bootstrap (启动模块)

 程序结构

 

![未命名绘图](./resources/未命名绘图_3.png)



 2. 配置中心客户端启动初始化



![初始化流程](./resources/初始化流程.png)



 3. 配置变动主动通知



![变更通知](./resources/变更通知.png)



三、 公共基础配置SQL



![数据关系图](./resources/数据关系图.png)



sql-- 配置主表（按配置模块划分）
CREATE TABLE config_groups (
  id INT PRIMARY KEY AUTO_INCREMENT,
  group_name VARCHAR(64) NOT NULL UNIQUE COMMENT '配置组名(如B2C/O2O/other  自定义唯一)',
  description TEXT COMMENT '描述',
  created_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  updated_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  sys_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  sys_update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  version bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置主表 按配置模块划分';
 
-- 通用配置项明细表
CREATE TABLE common_config_items (
  id INT PRIMARY KEY AUTO_INCREMENT,
  item_no bigint NOT NULL comment '明细编号',
  group_name VARCHAR(64) NOT NULL COMMENT	'组名',
  config_key VARCHAR(128) NOT NULL COMMENT '配置键',
  data_type ENUM('STRING','INTERER','LONG','FLOAT','DOUBLE','BOOLEAN','JSON') NOT NULL DEFAULT 'STRING' COMMENT '数据类型',
  default_value TEXT COMMENT '默认值',
  min_value TEXT COMMENT '最小值/最短长度',
  max_value TEXT COMMENT '最大值/最大长度',
  regex_pattern VARCHAR(256) COMMENT '正则表达式',
  is_encrypted BOOLEAN DEFAULT 0 COMMENT '是否加密存储',
  description TEXT COMMENT '描述',
  created_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  updated_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  sys_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  sys_update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  version bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
  UNIQUE KEY(group_name, config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用配置项明细表';
 
-- 通用环境配置值表
CREATE TABLE common_config_env_values (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  item_no bigint NOT NULL comment '配置明细编号',
  environment ENUM('DEFAULT','DEV','TEST','PRE','PROD') NOT NULL DEFAULT 'DEFAULT' COMMENT '环境类型',
  config_value TEXT NOT NULL COMMENT '配置值',
  effective_time DATETIME NOT NULL COMMENT '生效时间',
  operator VARCHAR(64) NOT NULL COMMENT '操作人',
  created_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  updated_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  sys_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  sys_update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  version bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
  INDEX idx_env_item(environment, item_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用环境配置值表';
 
-- 配置操作记录表（支持版本回滚）
CREATE TABLE config_operation_history (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  item_no bigint NOT NULL COMMENT '关联配置项编号',
  environment ENUM('DEFAULT','DEV','TEST','PRE','PROD') NOT NULL COMMENT '环境类型',
  operation_type ENUM('INSERT','UPDATE','DELETE') NOT NULL COMMENT '操作类型',
  old_value JSON COMMENT '操作前值（包含生效时间和操作人）',
  new_value JSON COMMENT '操作后值（包含生效时间和操作人）',
  operated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  operated_by VARCHAR(64) NOT NULL COMMENT '操作人账号',
  is_rolled_back BOOLEAN DEFAULT 0 COMMENT '是否已回滚',
  created_day date DEFAULT NULL COMMENT '创建日',
  created_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  updated_by varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  sys_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  sys_update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  version bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
  INDEX idx_operation(item_no, environment, operated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置操作历史记录表';
 
-- 配置通知日志表
CREATE TABLE config_notification_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  item_no bigint NOT NULL COMMENT '关联配置项编号',
  environment ENUM('DEFAULT','DEV','TEST','PRE','PROD') NOT NULL COMMENT '环境类型',
  notification_type ENUM('MQ','API') NOT NULL COMMENT '通知类型',
  content TEXT NOT NULL COMMENT '原始通知内容',
  recipients JSON NOT NULL COMMENT '接收者列表',
  status ENUM('PENDING','SUCCESS','FAILED') NOT NULL DEFAULT 'PENDING',
  retry_count INT DEFAULT 0 COMMENT '重试次数',
  last_attempt DATETIME COMMENT '最后尝试时间',
  error_message TEXT COMMENT '失败原因',
  executed_at TIMESTAMP NULL COMMENT '实际执行时间',
  sys_create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  sys_update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  version bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',
  INDEX idx_notification(item_no,environment, status),
  INDEX idx_recipients(environment, notification_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配置变更通知日志表';

四、缓存设计

 1. 服务端缓存结构

| key | value | 备注 |
| --- | --- | --- |
| env:registration_service | {{"service_id": [{"ip":'*********', "port":"8080", "groupList": ["B2C_BASE","ORDER_TIME"]}]}} | 注册服务信息 |
| env:group_name | [{"item_id":1, "version":0},{"item_id":2, "version":1}] | 当前环境下获取出组名下所有的配置 |
| env:group_name:item_id | {"item_id":1,"config_key": "time_out", "config_value": "30","description":'订单超时时间，单位: 分钟"} | 当前环境下配置明细信息 |


2. 客户端本地缓存设计

| key | value | 备注 |
| --- | --- | --- |
| group_name:config_key | {"item_id":1,"config_key": "time_out", "config_value": "30","description":'订单超时时间，单位: 分钟", "version":0} | 业务系统通过自定义组名+自定义key获取配置信息 |


五、当前配置梳理

| 配置项 |  | 是否废弃 | 备注 |
| --- | --- | --- | --- |
| 订单配置 |  | 58 incomplete | 主要对微商城的订单 |
| 运费配置 | **** | ****    59 incomplete | 争对微商城订单 |
| B2C基础配置 |  | 60 incomplete |  |
| 快递不达策略 |  | 61 complete |  |
| 快递例外配置 |  | 62 complete |  |
| 快递默认策略 |  | 63 complete |  |
| 赠品规则设置 |  | 64 incomplete |  |
| 智能审单策略 |  | 65 incomplete |  |
| 区域仓库优先级 |  | 66 complete |  |
| 商品可发策略 |  | 67 complete |  |
| 拆单策略 |  | 68 complete |  |
| 合单策略 |  | 69 complete |  |
| 空单策略 |  | 70 complete |  |
| 模板管理 |  | 71 incomplete |  |
| 电子面单 |  | 72 complete |  |
| 店铺分类 |  | 73 incomplete |  |
| 行政区域 |  | 74 incomplete |  |
| 快递商管理 |  | 75 incomplete |  |
| 店铺设置快递商 |  | 76 incomplete |  |