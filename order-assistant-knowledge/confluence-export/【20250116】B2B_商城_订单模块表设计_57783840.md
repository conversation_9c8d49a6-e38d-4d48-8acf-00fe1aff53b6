# 【20250116】B2B 商城 订单模块表设计

1.出库单

RDark出库单表（outbound_order）trueCREATE TABLE outbound_order (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，唯一标识出库单',
    outbound_order  VARCHAR(50) NOT NULL COMMENT '出库单编号（唯一）',
    order_no bigint NOT NULL COMMENT '订单号，心云透传给ERP的平台编号',
    waybill_code  VARCHAR(50) NOT NULL COMMENT '物流单号',
    carrier_code  VARCHAR(50) NOT NULL COMMENT '物流公司编码',
    warehouse_code VARCHAR(50) NOT NULL COMMENT '仓库 ID',
    order_type VARCHAR(20) NOT NULL COMMENT '出库类型（0:如销售出库 1:退货出库）',
    status VARCHAR(20) NOT NULL COMMENT '出库单状态（0: 如待处理 1:已出库）',
    quantity INT NOT NULL DEFAULT 0 COMMENT '出库商品总数量',
    total_amount DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '出库商品总金额',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '出库单创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '出库单更新时间',
    operator_id VARCHAR(50) COMMENT '出库员id',
    operator_name VARCHAR(50) COMMENT '出库员姓名', 
    check_user_id VARCHAR(50) COMMENT '复核人员id',
    check_user_name VARCHAR(50) COMMENT '复核人员姓名',  
    remark VARCHAR(500) COMMENT '备注信息',
    UNIQUE KEY uk_order_number (order_no),
    KEY idx_outbound_order (outbound_order),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4_general_ci COMMENT='出库单主表';

2.出库单明细表

RDark出库单明细表（outbound_order_detail）trueCREATE TABLE outbound_order_detail (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，唯一标识出库单明细',
    outbound_order VARCHAR(50) NOT NULL COMMENT '出库单 ID，关联出库单主表',
    order_no bigint not null  COMMENT '订单号，心云透传给ERP的平台编号',
    erp_code VARCHAR(50) NOT NULL COMMENT '商品编码',
    commodity_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    specs VARCHAR(50) NOT NULL COMMENT '商品规格',
    manufacturer VARCHAR(100) NULL COMMENT '商品生产厂商名称',
    quantity INT NOT NULL DEFAULT 0 COMMENT '出库数量',
    batch_no VARCHAR(100) NULL COMMENT '批次号',
    price DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '商品单价',
    total_amount DECIMAL(15, 2) NOT NULL DEFAULT 0.00 COMMENT '商品总价（quantity * price）',
    expiration VARCHAR(20) COMMENT '商品有效期 2027-01-01',
    traceability_code VARCHAR(50) null COMMENT '追溯码',
    KEY idx_outbound_order (outbound_order),
    KEY idx_order_no (order_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4_general_ci COMMENT='出库单明细表';