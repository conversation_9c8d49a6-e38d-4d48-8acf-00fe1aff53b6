# SQL注意事项

# 建表DDL注意事项

1. 建表DDL语句需要指定字符集以及排序规则，字符集：utf8mb4，字符集排序规则：utf8mb4_general_ci，示例如下：
  1. CREATE TABLE `ydj_cloud_delivery_charge_type` (
  `id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `mer_code` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家编码',
  `sp_code` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务商编码',
  `charge_type` int NOT NULL DEFAULT '1' COMMENT '计费方式，1 默认无邮费 2 按区域/重量计费',
  `isvalid` int DEFAULT '1' COMMENT '是否有效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `modify_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merCode_spCode` (`mer_code`,`sp_code`),
  KEY `idx_spCode` (`sp_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方商家快递计费方式表';
2. CREATE TABLE `ydj_cloud_delivery_charge_type` (
  `id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `mer_code` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家编码',
  `sp_code` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务商编码',
  `charge_type` int NOT NULL DEFAULT '1' COMMENT '计费方式，1 默认无邮费 2 按区域/重量计费',
  `isvalid` int DEFAULT '1' COMMENT '是否有效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `modify_name` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_merCode_spCode` (`mer_code`,`sp_code`),
  KEY `idx_spCode` (`sp_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方商家快递计费方式表';
3. 若没有指定，mysql会默认设置排序规则为utf8mb4_0900_ai_ci，导致在关联表查询时报错：
  1. 
4. 


# 大表删除历史数据

## 场景描述：

现有一张记录声音播报的表：cloud_sound_content，该表目前一天的数据量在55W左右，该表只需要保留最近三天的数据，每天会定时删除3天前的无用数据

## 现在的删除逻辑：

根据创建时间删除，如下图：

## 现有逻辑带来的问题

### mysql 的delete都做了哪些操作？

1. 根据where条件对删除表进行索引/全表扫描，检查是否符合where条件，该阶段会对扫描中所有行进行加锁。该阶段是最大的资源消耗隐患，若表的数据量大且delete操作无法有效利用索引减少扫描数据量，该步骤对于数据库带来的锁竞争、cpu/io资源的消耗都是巨大的
2. 对不能够被where条件匹配的行施加的锁会在条件检查后予以释放。
3. 如何优化：
  1. delete … where … 中，where过滤条件尽量保证可有效利用索引减少数据扫描量，避免全表扫描
  2. 对于大批量数据删除且where条件无索引的情况，delete操作可额外增加自增长主键或者含索引的时间字段，进行分批删除操作，每次删除少量数据，分多批次执行。
4. delete … where … 中，where过滤条件尽量保证可有效利用索引减少数据扫描量，避免全表扫描
5. 对于大批量数据删除且where条件无索引的情况，delete操作可额外增加自增长主键或者含索引的时间字段，进行分批删除操作，每次删除少量数据，分多批次执行。


### 现有删除语句的问题：

1. 由于create_time未建立索引，只能全表扫描，**导致锁表**
2. 若一次性需要删除几十上百万的数据，会产生大量的日志，导致这个过程会有很长时间，**锁表锁很长时间**，期间这个表无法处理线上业务
3. 由于产生了大量 binlog 导致**主从同步压力变大**


## 改造方案

1. cloud_sound_content中有自增id，我们可以利用自增id进行删除操作
2. 如何根据无索引的create_time获取到最大的id呢？有两个方案：
  1. 第一种：
    1. 
  2. 
  3. 第二种：
    1. 
  4. 
  1. 
  1. 
3. 第一种：
  1. 
4. 
5. 第二种：
  1. 
6. 
7. 如何选择？
  1. 我们可以使用mysql的sql分析器来看看两者的区别（如何使用mysql的SQL分析器呢？只要在sql最前面加explain即可）
  2. 第一种方案的sql分析：
    1. 
  3. 
  4. 第二种方案的sql分析：
    1. 
  5. 
  6. 这样一看，是不是及其清晰，毫无悬念，选择**第二种**
  1. 
  1. 
8. 我们可以使用mysql的sql分析器来看看两者的区别（如何使用mysql的SQL分析器呢？只要在sql最前面加explain即可）
9. 第一种方案的sql分析：
  1. 
10. 
11. 第二种方案的sql分析：
  1. 
12. 
13. 这样一看，是不是及其清晰，毫无悬念，选择**第二种**
14. 取到最大id之后，那我们的删除sql就简单噜，如下：
  1. 
15. 
16. **但，这样写真的没有问题吗？**
  1. 上十万数据的删除，最好分批删除，添加limit语句进行限制
17. 上十万数据的删除，最好分批删除，添加limit语句进行限制
18. 改造效果：
  1. 改造前执行策略：
    1. 
  2. 
  3. 改造后的执行策略：
    1. 
  4. 
  1. 
  1. 
19. 改造前执行策略：
  1. 
20. 
21. 改造后的执行策略：
  1. 
22. 


# 数据插入注意事项

## 背景

线上报警群发现插入拣货信息表时发生了死锁：

## 问题分析

1. 先抓取死锁日志：SHOW ENGINE INNODB STATUS;
  1. true
=====================================
2024-04-03 17:28:34 139636791011072 INNODB MONITOR OUTPUT
=====================================
Per second averages calculated from the last 59 seconds
-----------------
BACKGROUND THREAD
-----------------
srv_master_thread loops: 15431920 srv_active, 0 srv_shutdown, 1324673 srv_idle
srv_master_thread log flush and writes: 0
----------
SEMAPHORES
----------
OS WAIT ARRAY INFO: reservation count 34107616
OS WAIT ARRAY INFO: signal count 17765278
RW-shared spins 0, rounds 0, OS waits 0
RW-excl spins 0, rounds 0, OS waits 0
RW-sx spins 0, rounds 0, OS waits 0
Spin rounds per wait: 0.00 RW-shared, 0.00 RW-excl, 0.00 RW-sx
------------------------
LATEST DETECTED DEADLOCK
------------------------
2024-04-03 17:24:59 139636894508800
*** (1) TRANSACTION:
TRANSACTION 1448604302, ACTIVE 0 sec inserting
mysql tables in use 1, locked 1
LOCK WAIT 9 lock struct(s), heap size 1136, 6 row lock(s), undo log entries 2
MySQL thread id 44203444, OS thread handle 139636474509056, query id 14021020221 10.100.35.248 dscloud_agent update
INSERT INTO order_pick_info  ( order_detail_id,
erp_code,
commodity_batch_no,
count )  VALUES  ( 24764010,
'134823',
'1002149825',
1 )

*** (1) HOLDS THE LOCK(S):
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604302 lock_mode X locks gap before rec
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (1) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604302 lock_mode X locks gap before rec insert intention waiting
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (2) TRANSACTION:
TRANSACTION 1448604303, ACTIVE 0 sec inserting
mysql tables in use 1, locked 1
LOCK WAIT 9 lock struct(s), heap size 1136, 6 row lock(s), undo log entries 2
MySQL thread id 45411991, OS thread handle 139636201125632, query id 14021020224 10.100.32.223 dscloud_agent update
INSERT INTO order_pick_info  ( order_detail_id,
erp_code,
commodity_batch_no,
count )  VALUES  ( 24764015,
'137280',
'230704',
2 )

*** (2) HOLDS THE LOCK(S):
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604303 lock_mode X locks gap before rec
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (2) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604303 lock_mode X locks gap before rec insert intention waiting
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;

*** WE ROLL BACK TRANSACTION (2)
------------
TRANSACTIONS
------------
Trx id counter 1448649350
Purge done for trx's n:o < 1448649214 undo n:o < 0 state: running but idle
History list length 59
LIST OF TRANSACTIONS FOR EACH SESSION:
---TRANSACTION 421135726562456, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726449672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726563304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726548888, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726542952, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726539560, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726524296, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726518360, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726516664, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726508184, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726500552, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726497160, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726452216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726435256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726475112, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726559064, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726551432, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726509032, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726496312, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726492072, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726422536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726433560, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726453064, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726502248, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726579416, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726576872, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726576024, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726566696, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726469176, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726570088, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726567544, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726436952, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726481896, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726444584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726538712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726462392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726534472, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726494616, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726474264, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726544648, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726541256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726477656, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726535320, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726514968, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726480200, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726531080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726490376, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726526840, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726509880, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726473416, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726472568, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726466632, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726460696, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726478504, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726450520, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726468328, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726498856, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726483592, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726533624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726532776, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726528536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726523448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726504792, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726479352, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726482744, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726426776, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726436104, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726434408, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726540408, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726536168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726481048, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726437800, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726519208, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726520904, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726557368, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726552280, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726463240, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726457304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726425928, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726489528, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726498008, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726471720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726456456, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726486136, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726470872, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726470024, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726467480, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726464088, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726421688, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726440344, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726427624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726419144, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726522600, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726513272, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726447128, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726443736, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726492920, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726549736, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726550584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726451368, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726430168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726464936, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726575176, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726476808, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726493768, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726581960, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726577720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726560760, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726569240, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726568392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726565000, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726564152, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726419992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726554824, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726547192, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726531928, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726514120, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726511576, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726499704, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726491224, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726488680, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726486984, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726418296, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726484440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726425080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726465784, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726461544, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726458152, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726445432, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726438648, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726548040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726556520, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726555672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726455608, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726429320, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726432712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726423384, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726503096, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726446280, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726515816, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726537864, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726537016, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726527688, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726510728, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726503944, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726454760, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726459000, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726561608, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726442040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726505640, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726431864, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726487832, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726431016, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726521752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726553976, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726545496, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726495464, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726459848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726442888, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726558216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726553128, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726543800, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726542104, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726517512, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726512424, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726506488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726612488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726590440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726581112, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726571784, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726578568, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726475960, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726417448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726447976, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726565848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726546344, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726501400, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726485288, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726441192, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726424232, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726530232, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726529384, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726525992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726525144, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726420840, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726428472, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726520056, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726559912, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726448824, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726416600, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726415752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 1448649216, ACTIVE 1 sec
6 lock struct(s), heap size 1136, 0 row lock(s), undo log entries 6
MySQL thread id 44659305, OS thread handle 139635542193920, query id 14021439684 10.100.35.89 dscloud_agent
Trx read view will not see trx with id >= 1448649214, sees < 1448649213
--------
FILE I/O
--------
I/O thread 0 state: waiting for completed aio requests (insert buffer thread)
I/O thread 1 state: waiting for completed aio requests (log thread)
I/O thread 2 state: waiting for completed aio requests (read thread)
I/O thread 3 state: waiting for completed aio requests (read thread)
I/O thread 4 state: waiting for completed aio requests (read thread)
I/O thread 5 state: waiting for completed aio requests (read thread)
I/O thread 6 state: waiting for completed aio requests (write thread)
I/O thread 7 state: waiting for completed aio requests (write thread)
I/O thread 8 state: waiting for completed aio requests (write thread)
I/O thread 9 state: waiting for completed aio requests (write thread)
Pending normal aio reads: [0, 0, 0, 0] , aio writes: [0, 0, 0, 0] ,
 ibuf aio reads:, log i/o's:, sync i/o's:
Pending flushes (fsync) log: 0; buffer pool: 264516
25246477 OS file reads, 2742854612 OS file writes, 1628235825 OS fsyncs
2.54 reads/s, 16384 avg bytes/read, 358.54 writes/s, 225.98 fsyncs/s
-------------------------------------
INSERT BUFFER AND ADAPTIVE HASH INDEX
-------------------------------------
Ibuf: size 1, free list len 662, seg size 664, 2495417 merges
merged operations:
 insert 1959171, delete mark 48244797, delete 897577
discarded operations:
 insert 0, delete mark 0, delete 0
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
0.00 hash searches/s, 158097.15 non-hash searches/s
---
LOG
---
Log sequence number          952041642638
Log buffer assigned up to    952041642638
Log buffer completed up to   952041642638
Log written up to            952041642453
Log flushed up to            952041642453
Added dirty pages up to      952041642638
Pages flushed up to          951625619823
Last checkpoint at           951625432410
2470192249 log i/o's done, 336.53 log i/o's/second
----------------------
BUFFER POOL AND MEMORY
----------------------
Total large memory allocated 21474836480
Dictionary memory allocated 7191571
Buffer pool size   1283680
Free buffers       16382
Database pages     1267298
Old database pages 467648
Modified db pages  33305
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 149363931, not young 925251128
0.00 youngs/s, 0.00 non-youngs/s
Pages read 25242447, created 14704141, written 161796674
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 1267298, unzip_LRU len: 0
I/O sum[4160]:cur[64], unzip sum[0]:cur[0]
----------------------
INDIVIDUAL BUFFER POOL INFO
----------------------
---BUFFER POOL 0
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  3950
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 19852733, not young 117579118
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3210758, created 2342093, written 21884546
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 1
Buffer pool size   160460
Free buffers       2047
Database pages     158413
Old database pages 58456
Modified db pages  3633
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18543729, not young 103147691
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3148244, created 3115487, written 19623891
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158413, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 2
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4074
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 19491706, not young 124779060
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3088757, created 2346098, written 19475784
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 3
Buffer pool size   160460
Free buffers       2047
Database pages     158413
Old database pages 58456
Modified db pages  4187
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 17393671, not young 107349153
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3114373, created 1380026, written 19634807
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158413, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 4
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  3841
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18827982, not young 113389681
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3259353, created 1384985, written 18983761
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 5
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4423
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18104190, not young 112094388
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3122973, created 1378013, written 17980552
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 6
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4693
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 16666809, not young 116597601
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3093465, created 1378303, written 21248162
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 7
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4504
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 20483111, not young 130314436
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3204524, created 1379136, written 22965171
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
--------------
ROW OPERATIONS
--------------
0 <USER> <GROUP> InnoDB, 0 queries in queue
3 read views open inside InnoDB
Process ID=2769465, Main thread ID=139636860937984 , state=sleeping
Number of rows inserted 633239986, updated 489695758, deleted 109565418, read 6483756618921
47.36 inserts/s, 77.52 updates/s, 0.59 deletes/s, 134906.54 reads/s
Number of system rows inserted 88850070, updated 1946201, deleted 88843709, read 629311354
9.93 inserts/s, 0.20 updates/s, 10.19 deletes/s, 64.78 reads/s
----------------------------
END OF INNODB MONITOR OUTPUT
============================
2. true
=====================================
2024-04-03 17:28:34 139636791011072 INNODB MONITOR OUTPUT
=====================================
Per second averages calculated from the last 59 seconds
-----------------
BACKGROUND THREAD
-----------------
srv_master_thread loops: 15431920 srv_active, 0 srv_shutdown, 1324673 srv_idle
srv_master_thread log flush and writes: 0
----------
SEMAPHORES
----------
OS WAIT ARRAY INFO: reservation count 34107616
OS WAIT ARRAY INFO: signal count 17765278
RW-shared spins 0, rounds 0, OS waits 0
RW-excl spins 0, rounds 0, OS waits 0
RW-sx spins 0, rounds 0, OS waits 0
Spin rounds per wait: 0.00 RW-shared, 0.00 RW-excl, 0.00 RW-sx
------------------------
LATEST DETECTED DEADLOCK
------------------------
2024-04-03 17:24:59 139636894508800
*** (1) TRANSACTION:
TRANSACTION 1448604302, ACTIVE 0 sec inserting
mysql tables in use 1, locked 1
LOCK WAIT 9 lock struct(s), heap size 1136, 6 row lock(s), undo log entries 2
MySQL thread id 44203444, OS thread handle 139636474509056, query id 14021020221 10.100.35.248 dscloud_agent update
INSERT INTO order_pick_info  ( order_detail_id,
erp_code,
commodity_batch_no,
count )  VALUES  ( 24764010,
'134823',
'1002149825',
1 )

*** (1) HOLDS THE LOCK(S):
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604302 lock_mode X locks gap before rec
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (1) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604302 lock_mode X locks gap before rec insert intention waiting
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (2) TRANSACTION:
TRANSACTION 1448604303, ACTIVE 0 sec inserting
mysql tables in use 1, locked 1
LOCK WAIT 9 lock struct(s), heap size 1136, 6 row lock(s), undo log entries 2
MySQL thread id 45411991, OS thread handle 139636201125632, query id 14021020224 10.100.32.223 dscloud_agent update
INSERT INTO order_pick_info  ( order_detail_id,
erp_code,
commodity_batch_no,
count )  VALUES  ( 24764015,
'137280',
'230704',
2 )

*** (2) HOLDS THE LOCK(S):
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604303 lock_mode X locks gap before rec
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;


*** (2) WAITING FOR THIS LOCK TO BE GRANTED:
RECORD LOCKS space id 839 page no 124147 n bits 568 index u_detail_id_batch of table `dscloud`.`order_pick_info` trx id 1448604303 lock_mode X locks gap before rec insert intention waiting
Record lock, heap no 7 PHYSICAL RECORD: n_fields 4; compact format; info bits 0
 0: len 8; hex 8000000003ae78c5; asc       x ;;
 1: len 7; hex 32323035303732; asc 2205072;;
 2: len 1; hex 81; asc  ;;
 3: len 8; hex 800000000169d7b8; asc      i  ;;

*** WE ROLL BACK TRANSACTION (2)
------------
TRANSACTIONS
------------
Trx id counter 1448649350
Purge done for trx's n:o < 1448649214 undo n:o < 0 state: running but idle
History list length 59
LIST OF TRANSACTIONS FOR EACH SESSION:
---TRANSACTION 421135726562456, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726449672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726563304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726548888, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726542952, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726539560, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726524296, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726518360, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726516664, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726508184, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726500552, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726497160, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726452216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726435256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726475112, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726559064, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726551432, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726509032, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726496312, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726492072, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726422536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726433560, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726453064, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726502248, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726579416, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726576872, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726576024, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726566696, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726469176, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726570088, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726567544, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726436952, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726481896, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726444584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726538712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726462392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726534472, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726494616, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726474264, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726544648, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726541256, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726477656, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726535320, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726514968, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726480200, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726531080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726490376, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726526840, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726509880, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726473416, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726472568, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726466632, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726460696, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726478504, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726450520, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726468328, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726498856, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726483592, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726533624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726532776, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726528536, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726523448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726504792, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726479352, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726482744, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726426776, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726436104, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726434408, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726540408, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726536168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726481048, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726437800, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726519208, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726520904, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726557368, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726552280, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726463240, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726457304, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726425928, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726489528, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726498008, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726471720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726456456, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726486136, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726470872, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726470024, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726467480, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726464088, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726421688, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726440344, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726427624, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726419144, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726522600, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726513272, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726447128, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726443736, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726492920, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726549736, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726550584, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726451368, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726430168, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726464936, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726575176, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726476808, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726493768, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726581960, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726577720, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726560760, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726569240, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726568392, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726565000, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726564152, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726419992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726554824, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726547192, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726531928, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726514120, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726511576, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726499704, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726491224, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726488680, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726486984, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726418296, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726484440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726425080, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726465784, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726461544, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726458152, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726445432, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726438648, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726548040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726556520, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726555672, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726455608, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726429320, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726432712, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726423384, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726503096, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726446280, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726515816, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726537864, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726537016, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726527688, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726510728, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726503944, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726454760, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726459000, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726561608, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726442040, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726505640, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726431864, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726487832, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726431016, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726521752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726553976, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726545496, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726495464, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726459848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726442888, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726558216, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726553128, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726543800, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726542104, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726517512, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726512424, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726506488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726612488, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726590440, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726581112, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726571784, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726578568, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726475960, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726417448, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726447976, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726565848, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726546344, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726501400, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726485288, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726441192, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726424232, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726530232, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726529384, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726525992, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726525144, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726420840, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726428472, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726520056, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726559912, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726448824, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726416600, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 421135726415752, not started
0 lock struct(s), heap size 1136, 0 row lock(s)
---TRANSACTION 1448649216, ACTIVE 1 sec
6 lock struct(s), heap size 1136, 0 row lock(s), undo log entries 6
MySQL thread id 44659305, OS thread handle 139635542193920, query id 14021439684 10.100.35.89 dscloud_agent
Trx read view will not see trx with id >= 1448649214, sees < 1448649213
--------
FILE I/O
--------
I/O thread 0 state: waiting for completed aio requests (insert buffer thread)
I/O thread 1 state: waiting for completed aio requests (log thread)
I/O thread 2 state: waiting for completed aio requests (read thread)
I/O thread 3 state: waiting for completed aio requests (read thread)
I/O thread 4 state: waiting for completed aio requests (read thread)
I/O thread 5 state: waiting for completed aio requests (read thread)
I/O thread 6 state: waiting for completed aio requests (write thread)
I/O thread 7 state: waiting for completed aio requests (write thread)
I/O thread 8 state: waiting for completed aio requests (write thread)
I/O thread 9 state: waiting for completed aio requests (write thread)
Pending normal aio reads: [0, 0, 0, 0] , aio writes: [0, 0, 0, 0] ,
 ibuf aio reads:, log i/o's:, sync i/o's:
Pending flushes (fsync) log: 0; buffer pool: 264516
25246477 OS file reads, 2742854612 OS file writes, 1628235825 OS fsyncs
2.54 reads/s, 16384 avg bytes/read, 358.54 writes/s, 225.98 fsyncs/s
-------------------------------------
INSERT BUFFER AND ADAPTIVE HASH INDEX
-------------------------------------
Ibuf: size 1, free list len 662, seg size 664, 2495417 merges
merged operations:
 insert 1959171, delete mark 48244797, delete 897577
discarded operations:
 insert 0, delete mark 0, delete 0
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
Hash table size 5312557, node heap has 0 buffer(s)
0.00 hash searches/s, 158097.15 non-hash searches/s
---
LOG
---
Log sequence number          952041642638
Log buffer assigned up to    952041642638
Log buffer completed up to   952041642638
Log written up to            952041642453
Log flushed up to            952041642453
Added dirty pages up to      952041642638
Pages flushed up to          951625619823
Last checkpoint at           951625432410
2470192249 log i/o's done, 336.53 log i/o's/second
----------------------
BUFFER POOL AND MEMORY
----------------------
Total large memory allocated 21474836480
Dictionary memory allocated 7191571
Buffer pool size   1283680
Free buffers       16382
Database pages     1267298
Old database pages 467648
Modified db pages  33305
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 149363931, not young 925251128
0.00 youngs/s, 0.00 non-youngs/s
Pages read 25242447, created 14704141, written 161796674
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 1267298, unzip_LRU len: 0
I/O sum[4160]:cur[64], unzip sum[0]:cur[0]
----------------------
INDIVIDUAL BUFFER POOL INFO
----------------------
---BUFFER POOL 0
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  3950
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 19852733, not young 117579118
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3210758, created 2342093, written 21884546
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 1
Buffer pool size   160460
Free buffers       2047
Database pages     158413
Old database pages 58456
Modified db pages  3633
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18543729, not young 103147691
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3148244, created 3115487, written 19623891
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158413, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 2
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4074
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 19491706, not young 124779060
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3088757, created 2346098, written 19475784
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 3
Buffer pool size   160460
Free buffers       2047
Database pages     158413
Old database pages 58456
Modified db pages  4187
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 17393671, not young 107349153
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3114373, created 1380026, written 19634807
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158413, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 4
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  3841
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18827982, not young 113389681
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3259353, created 1384985, written 18983761
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 5
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4423
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 18104190, not young 112094388
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3122973, created 1378013, written 17980552
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 6
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4693
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 16666809, not young 116597601
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3093465, created 1378303, written 21248162
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
---BUFFER POOL 7
Buffer pool size   160460
Free buffers       2048
Database pages     158412
Old database pages 58456
Modified db pages  4504
Pending reads      0
Pending writes: LRU 0, flush list 0, single page 0
Pages made young 20483111, not young 130314436
0.00 youngs/s, 0.00 non-youngs/s
Pages read 3204524, created 1379136, written 22965171
0.00 reads/s, 0.00 creates/s, 0.00 writes/s
Buffer pool hit rate 1000 / 1000, young-making rate 0 / 1000 not 0 / 1000
Pages read ahead 0.00/s, evicted without access 0.00/s, Random read ahead 0.00/s
LRU len: 158412, unzip_LRU len: 0
I/O sum[520]:cur[8], unzip sum[0]:cur[0]
--------------
ROW OPERATIONS
--------------
0 <USER> <GROUP> InnoDB, 0 queries in queue
3 read views open inside InnoDB
Process ID=2769465, Main thread ID=139636860937984 , state=sleeping
Number of rows inserted 633239986, updated 489695758, deleted 109565418, read 6483756618921
47.36 inserts/s, 77.52 updates/s, 0.59 deletes/s, 134906.54 reads/s
Number of system rows inserted 88850070, updated 1946201, deleted 88843709, read 629311354
9.93 inserts/s, 0.20 updates/s, 10.19 deletes/s, 64.78 reads/s
----------------------------
END OF INNODB MONITOR OUTPUT
============================
3. 看日志可以发现有两个事务同时往order_pick_info插入数据导致了死锁：
  1. 
4. 
5. 查看问题代码
  1. 
  2. 可以看出是for循环中插入数据
6. 
7. 可以看出是for循环中插入数据
8. 修改方式：
  1. 改为批量插入
9. 改为批量插入