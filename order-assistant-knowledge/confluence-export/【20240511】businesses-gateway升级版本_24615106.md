# 【20240511】businesses-gateway升级版本

upgrade-gateway-3

最新分支(20240511): refactor-upgrade-gateway-version

ServerRequest serverRequest = new DefaultServerRequest(exchange, HandlerStrategies.withDefaults().messageReaders());
调整为
ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());

import org.springframework.cloud.gateway.support.CachedBodyOutputMessage;
调整为
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;

升级中发现的问题:

1. 部分接口正常返回
2. 部分接口返回如下异常:


Caused by: io.netty.handler.codec.EncoderException: io.netty.util.IllegalReferenceCountException: refCnt: 0, decrement: 1
        at io.netty.handler.codec.MessageToMessageEncoder.write(MessageToMessageEncoder.java:104)
        Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
        |_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
        |_ checkpoint ⇢ org.springframework.boot.actuate.metrics.web.reactive.server.MetricsWebFilter [DefaultWebFilterChain]
        |_ checkpoint ⇢ HTTP POST "/dscloud/1.0/ds/order/page/search" [ExceptionHandlingWebHandler]
Stack trace:

之前在info级别的日志无法定位到具体问题，添加全局异常捕获也无法捕获。后面想到可以将日志调整到debug级别来定位，调整到后定位到ModifyBodyGatewayFilter过滤器有问题,经过调试后发现之前的2.x自定义了一个CacheBodyOutputMessage类:MyCachedBodyOutputMessage,这个类和3.x的版本差异较大,不能复用,需要调整为3.x版本的CacheBodyOutputMessage,调整后正常

到此，可以解释为什么部分接口正常部分接口异常了，因为正常的接口未走所有的过滤器。部分异常的，走到了异常的过滤器

本地apollo修改:

注释掉(根据错误提示来获取的):
allowCredentials: true

本机测试记录:

grey:
  #  local-mappings:
  "[(.*)]": $1.svc.k8s.dev.hxyxt.com

-----

配置参考: 

  15 incomplete 验证/actuator/prometheus是否符合 [http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus](http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/prometheus)  16 complete dev   37 complete test   38 complete prod     31 incomplete 验证/actuator/health返回数据是否符合 [http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/health](http://businesses-gateway.svc.k8s.dev.hxyxt.com/actuator/health)  32 complete dev   39 complete test   40 complete prod     17 incomplete 配置httpClient连接池  18 complete dev   19 complete test   20 complete prod     21 incomplete 配置服务发现健康监测  22 complete dev   23 complete test   24 complete prod     25 incomplete 配置lifo（需要配置构建）  26 complete dev   27 complete test   28 complete prod