# 【20241216】一心助手-线上线下订单查询-技术方案设计V2

# 项目背景

需求文档：[门店订单-线上线下订单查询 - 产品部 - 一心数科数字化产研中心-wiki](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50316555)

JIRA：一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3742

# 项目目标

1. 业务目标：
  1. 支持门店通过输入订单号和商品erpCode进行精准查询实时订单（3个月数据），商品名称查询？
  2. 订单列表页支持6个月的数据查询
  3. 支持门店查看线上线下订单详情信息，包括商品明细信息
2. 支持门店通过输入订单号和商品erpCode进行精准查询实时订单（3个月数据），商品名称查询？
3. 订单列表页支持6个月的数据查询
4. 支持门店查看线上线下订单详情信息，包括商品明细信息
5. 技术目标：
  1. 同步最近6个月的线上线下订单数据到es中，支持毫秒级查询
  2. 此次建立的ES索引可以作为后续订单查询服务的数据基石
6. 同步最近6个月的线上线下订单数据到es中，支持毫秒级查询
7. 此次建立的ES索引可以作为后续订单查询服务的数据基石


# 整体设计

## 数据量统计

统计最近一个月【2024.11.01 - 2024.12.01（不含）】的数据量

### 线下正单：

会员：34000 * 256 = 8704000

非会员：4319360

共 1302w+

### 线下逆单：

会员：300 * 256 = 79800

非会员：47708

共 12w+

### 线上单(O2O)

1. 正单：246W+
2. 退单：6w+


## 数据同步

### 历史数据

#### 流程图

true线下单历史数据同步falseautotoptrue11581

#### 耗时预估

需要最近6个月数据，启动6个job，每个job同步一个月数据

假定一个月数据量在1500W

一次job执行10批次，每批次10000条，一次job同步 10 * 10000 = 10W条数据

假定一批次处理时分10个线程，每个线程处理200条数据，那么处理1w条数据，需要 10000/(10 * 200) = 5次，若每次执行耗时 300ms，那么一批次共需要耗时 300ms * 5 = 1500ms = 1.5s

那么一次job运行需要耗时 1.5s * 10 = 15s

job设定为3分钟执行一次，那么 保守计算 4分钟 10w条数据，1500W/10W = 150次，150 * 4 = 600分钟 = 10小时

### 增量数据

1. 主要通过canal+Kafka进行增量同步
2. 流程图如下：
  1. true一心助手订单数据查询流转falseautotoptrue4811
3. true一心助手订单数据查询流转falseautotoptrue4811
4. 监听表
  1. 线下正单：（按需求进度来看，可直接使用润康的canal配置）
    1. 监听表：offline_order*
    2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-ORDER-SYNC
    3. 抓取字段：order_no
  2. 监听表：offline_order*
  3. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-ORDER-SYNC
  4. 抓取字段：order_no
  5. 线下逆单：（按需求进度来看，可直接使用润康的canal配置）
    1. 监听表：offline_refund_order*
    2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-REFUND-SYNC
    3. 抓取字段：refund_no
  6. 监听表：offline_refund_order*
  7. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-REFUND-SYNC
  8. 抓取字段：refund_no
  9. 线上单
    1. 前已有canal配置：dscloud_orderdeal，但目前只监听了order_info和refund_order，没有监听order_detail、erp_bill_info、refund_detail、erp_refund_info，是否需要新增一个canal？
  10. 前已有canal配置：dscloud_orderdeal，但目前只监听了order_info和refund_order，没有监听order_detail、erp_bill_info、refund_detail、erp_refund_info，是否需要新增一个canal？
  1. 监听表：offline_order*
  2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-ORDER-SYNC
  3. 抓取字段：order_no
  1. 监听表：offline_refund_order*
  2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-REFUND-SYNC
  3. 抓取字段：refund_no
  1. 前已有canal配置：dscloud_orderdeal，但目前只监听了order_info和refund_order，没有监听order_detail、erp_bill_info、refund_detail、erp_refund_info，是否需要新增一个canal？
5. 线下正单：（按需求进度来看，可直接使用润康的canal配置）
  1. 监听表：offline_order*
  2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-ORDER-SYNC
  3. 抓取字段：order_no
6. 监听表：offline_order*
7. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-ORDER-SYNC
8. 抓取字段：order_no
9. 线下逆单：（按需求进度来看，可直接使用润康的canal配置）
  1. 监听表：offline_refund_order*
  2. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-REFUND-SYNC
  3. 抓取字段：refund_no
10. 监听表：offline_refund_order*
11. Kafka topic：TP_ORDER_ORDER-ATOM_OFFLINE-REFUND-SYNC
12. 抓取字段：refund_no
13. 线上单
  1. 前已有canal配置：dscloud_orderdeal，但目前只监听了order_info和refund_order，没有监听order_detail、erp_bill_info、refund_detail、erp_refund_info，是否需要新增一个canal？
14. 前已有canal配置：dscloud_orderdeal，但目前只监听了order_info和refund_order，没有监听order_detail、erp_bill_info、refund_detail、erp_refund_info，是否需要新增一个canal？


## 时序图

### 订单查询时序图

true一心助手-订单查询时序图falseautotoptrue10811

# 详细设计

## ES索引设计

### 线上正单

索引名：**es_order_shard_by_org**

| 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| orderNo |  | keyword | 系统订单号 | order_info >> order_no |  |
| thirdOrderNo |  | keyword | 三方订单号 | order_info >> third_order_no |  |
| created |  | date | 下单时间 | order_info >> created |  |
| payTime |  | date | 支付时间 | order_info >> pay_time |  |
| createTime |  | date | 系统创建时间 | order_info >> create_time |  |
| storeCode |  | keyword | 线上门店编码 | order_info >> online_store_code |  |
| orgCode |  | keyword | 机构编码（线下实际发货门店） | order_info >> organization_code |  |
| sourceStoreCode |  | keyword | 线上下单门店编码 | order_info >> source_online_store_code |  |
| sourceOrgCode |  | keyword | 线下实际下单机构编码 | order_info >> source_organization_code |  |
| orderStatus |  | int | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 | order_info >> order_state |  |
| orderSource |  | keyword | 订单来源：ONLINE：线上 POS：线下 |  |  |
| platformCode |  | keyword | HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传 | order_info >> third_platform_code |  |
| orderFlags |  | text | 订单标记EXCEPTION：该订单为异常订单PRESCRIPTION：该订单为处方订单ROUTE：订单路由（转单）REFUNDING：订单退款中 | 异常：order_info >> lock_flag： 0未锁定,10取消锁定,20退款锁定,31库存不足,32部分商定不存在,33金额异常,34配送异常处方：order_info >> prescription_flag订单路由：route_allot >> oms_no退款中：refund_order >> order_no && state = 10|20 |  |
| userCardNo |  | keyword | 会员号 |  |  |
| userId |  | keyword | 会员id |  |  |
| billAmount |  | scaled_float | 下账金额 | erp_bill_info >> bill_total_amount |  |
| orderDetailList |  | nested | 商品列表 |  |  |
|  | orderDetailId | keyword | 详情id | order_detail >> id |  |
|  | erpCode | keyword | 商品编码 | order_detail >> erp_code |  |
|  | ItemName | text | 商品名称 | order_detail >> commodity_name |  |


### 线上逆单

索引名：**es_refund_shard_by_org**

| 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| thirdRefundNo |  | keyword | 三方退单号 | refund_order >> third_refund_no |  |
| thirdOrderNo |  | keyword | 三方正单号 | refund_order >> third_order_no |  |
| orderNo |  | keyword | 系统订单号 | refund_order >> order_no |  |
| refundNo |  | keyword | 系统退单号 | refund_order >> refund_no |  |
| created |  | date | 退单时间 | refund_order >> create_time |  |
| createTime |  | date | 创建时间 | refund_order >> create_time |  |
| order_created |  | date | 正单创建时间 | order_info >> created |  |
| storeCode |  | keyword | 门店编码 | refund_order >> online_store_code |  |
| orgCode |  | keyword | 机构编码（线下实际发货门店） | refund_order >> organization_code |  |
| sourceStoreCode |  | keyword | 下单门店编码 | refund_order >> source_online_store_code |  |
| sourceOrgCode |  | keyword | 下单机构编码 | refund_order >> source_organization_code |  |
| platformCode |  | keyword |  | refund_order >> third_platform_code |  |
| orderSource |  | keyword | 订单来源：ONLINE：线上 POS：线下 |  |  |
| refundStatus |  | int | 退单状态0, "待退款",20, "待退货",100, "已完成",102, "已拒绝",103, "已取消" | refund_order >> state |  |
| refundType |  | keyword | 退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款 | refund_order >> type |  |
| afterSaleType |  | keyword | 售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款 | refund_order >> after_sale_type |  |
| refundFlags |  | text | 退单标记NO_ORDER:无正单 |  |  |
| refundBillAmount |  | scaled_float | 退款下账金额 | erp_refund_info >> refund_merchant_total |  |
| userCardNo |  | keyword | 会员号 |  |  |
| userId |  | keyword | 会员id |  |  |
| refundDetailList |  | nested | 商品列表 |  |  |
|  | refundDetailId | keyword | 退单详情id | refund_detail >> id |  |
|  | erpCode | keyword | 商品编码 | refund_detail >> erp_code |  |
|  | ItemName | text | 商品名称 | refund_detail >> commodity_name |  |


## 接口设计

注：所有接口的请求方式均为 POST

### 枚举值梳理

| 字段描述 | 枚举值 | 备注 |
| --- | --- | --- |
| 订单状态 | 5, "待处理" 10, "待接单" 20, "待拣货" 30, "待配送" 40, "配送中" 100, "已完成" 102, "已取消" 101, "已关闭" | 线下单只有【已完成】状态 |
| 退款类型, | PART-部分退款，ALL-全额退款UNKNOWN-未知 | 线下单会出现 UNKNOWN类型 |
| 退款状态 | 10, "待退款"20, "待退货"100, "已完成"102, "已拒绝"103, "已取消" | 线下单只有【已完成】状态 |
| 订单来源 | ONLINE(线上)OFFLINE(线下) |  |
| 平台编码 | HAIDIAN-海典 KE_CHUAN-科传  JD_DAOJIA-京东到家 MEITUAN-美团 E_BAI-饿百 YD_JIA-微商城DOUDIAN-抖店  ZHIFUBAO-支付宝小程序  KUAI_SHOU-快手OTHER-其他渠道  POS_HD_H1-海典H1POS_HD_H2-海典H2 POS_KC-科传 |  |
| 订单标记 | EXCEPTION：该订单为异常订单PRESCRIPTION：该订单为处方订单ROUTE：订单路由（转单）REFUNDING：订单退款中 |  |
| 退单标记 | NO_ORDER:无正单 |  |


### 正单金额统计

1. url：/c/order/r/1.0/amount/static
2. 请求体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),ORDER_STATUS(订单状态)ORDER_FLAG(订单标记) |  | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
3. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),ORDER_STATUS(订单状态)ORDER_FLAG(订单标记) |  | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
4. 响应体
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- |
| orderAmount | BigDecimal | 销售金额 | es_offline_order >> sum(billAmount) |  |
5. | 字段名 | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- |
| orderAmount | BigDecimal | 销售金额 | es_offline_order >> sum(billAmount) |  |
6. 流程图


### 逆单金额统计

1. url：/c/refund/r/1.0/amount/static
2. 请求体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),REFUND_FLAG(退单标记)REFUND_STATUS(退单状态) |  | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
3. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),REFUND_FLAG(退单标记)REFUND_STATUS(退单状态) |  | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
4. 响应体
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- |
| refundAmount | String | 退款金额 | es_refund_shard_by_org >> sum(refundBillAmount) |  |
5. | 字段名 | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- |
| refundAmount | String | 退款金额 | es_refund_shard_by_org >> sum(refundBillAmount) |  |
6. 流程图


### 退单分页查询

1. url：/c/refund/r/1.0/page/query
2. 请求体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchAfter |  | String | 翻页参数 | 为空时查询第一页 |  |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),REFUND_NO(退单号),THIRD_REFUND_NO(三方退单号),REFUND_FLAG(退单标记) |  | 必须要附加searchType=ORG_CODE和searchType=ORDER_SOURCE 的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
3. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| searchAfter |  | String | 翻页参数 | 为空时查询第一页 |  |
| searchConditionList |  | List<Object> |  | 是 |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),REFUND_NO(退单号),THIRD_REFUND_NO(三方退单号),REFUND_FLAG(退单标记) |  | 必须要附加searchType=ORG_CODE和searchType=ORDER_SOURCE 的条件 |
|  | searchData | String | 条件值 |  | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：秒级时间戳，如果此时searchData为空，默认处理为 now() |
4. 响应体
  1. | 字段名 |  |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| totalCount |  |  | Long | 总条数 |  |  |
| searchAfter |  |  | String | 翻页参数 |  |  |
| data |  |  | List<Object> |  |  |  |
|  | orderNo |  | String | 订单号 | es_refund_shard_by_org >> orderNo |  |
|  | thirdOrderNo |  | String | 三方单号 | es_refund_shard_by_org >> thirdOrderNo |  |
|  | refundNo |  | String | 退单号 | es_refund_shard_by_org >> refundNo |  |
|  | thirdRefundNo |  | String | 三方退单号 | es_refund_shard_by_org >> thirdRefundNo |  |
|  | created |  | String | 创建时间格式：秒级时间戳 | es_refund_shard_by_org >> created |  |
|  | billAmount |  | BigDecimal | 下账金额 | es_refund_shard_by_org >> bill_amount |  |
|  | refundDetailList |  | List<Object> | 退单详情列表 |  |  |
|  |  | refundDetailId | String | 退单详情id | es_refund_shard_by_org >> refundDetailList >> refundDetailId |  |
|  |  | erpCode | String | 商品编码 | es_refund_shard_by_org >> refundDetailList >> erpCode |  |
|  |  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_refund_shard_by_org >> refundDetailList >> ItemCount |  |
|  |  | ItemName | String | 商品名称 | es_refund_shard_by_org >> refundDetailList >> ItemName |  |
|  |  | itemPicUrl | String | 商品图片 | hydee-middle-merchandise | /1.0/ds/Basic_Spec |
5. | 字段名 |  |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| totalCount |  |  | Long | 总条数 |  |  |
| searchAfter |  |  | String | 翻页参数 |  |  |
| data |  |  | List<Object> |  |  |  |
|  | orderNo |  | String | 订单号 | es_refund_shard_by_org >> orderNo |  |
|  | thirdOrderNo |  | String | 三方单号 | es_refund_shard_by_org >> thirdOrderNo |  |
|  | refundNo |  | String | 退单号 | es_refund_shard_by_org >> refundNo |  |
|  | thirdRefundNo |  | String | 三方退单号 | es_refund_shard_by_org >> thirdRefundNo |  |
|  | created |  | String | 创建时间格式：秒级时间戳 | es_refund_shard_by_org >> created |  |
|  | billAmount |  | BigDecimal | 下账金额 | es_refund_shard_by_org >> bill_amount |  |
|  | refundDetailList |  | List<Object> | 退单详情列表 |  |  |
|  |  | refundDetailId | String | 退单详情id | es_refund_shard_by_org >> refundDetailList >> refundDetailId |  |
|  |  | erpCode | String | 商品编码 | es_refund_shard_by_org >> refundDetailList >> erpCode |  |
|  |  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_refund_shard_by_org >> refundDetailList >> ItemCount |  |
|  |  | ItemName | String | 商品名称 | es_refund_shard_by_org >> refundDetailList >> ItemName |  |
|  |  | itemPicUrl | String | 商品图片 | hydee-middle-merchandise | /1.0/ds/Basic_Spec |
6. 流程图


### 退单详情

1. url：/c/refund/r/1.0/info
2. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| refundNo | String | 订单号 | 是 |  |
3. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| refundNo | String | 订单号 | 是 |  |
4. 响应体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| orderNo |  | String | 订单号 | es_refund_shard_by_org >> orderNo |  |
| thirdOrderNo |  | String | 三方单号 | es_refund_shard_by_org >> thirdOrderNo |  |
| refundNo |  | String | 退单号 | es_refund_shard_by_org >> refundNo |  |
| thirdRefundNo |  | String | 三方退单号 | es_refund_shard_by_org >> thirdRefundNo |  |
| refundCreated |  | String | 创建时间格式：秒级时间戳 | es_refund_shard_by_org >> created |  |
| refundDetailList |  | List<Object> | 退款详情列表 |  |  |
|  | refundDetailId | String | 退款明细id | es_refund_shard_by_org >> refundDetailList >> refundDetailId |  |
|  | gift | Boolean | 是否为赠品 |  |  |
|  | erpCode | String | 商品编码 | es_refund_shard_by_org >> refundDetailList >> erpCode |  |
|  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_refund_shard_by_org >> refundDetailList >> ItemCount |  |
|  | ItemName | String | 商品名称 | es_refund_shard_by_org >> refundDetailList >> ItemName |  |
|  | itemPicUrl | String | 商品图片 | hydee-middle-merchandise | /1.0/ds/Basic_Spec |
|  | itemPrice | BigDecimal | 实付金额 | offline_refund_order_detail >> price |  |
|  | itemOriginalPrice | BigDecimal | 划线价 | offline_refund_order_detail >> original_price |  |
| refundAmount |  | BigDecimal | 实际退款金额 | offline_refund_order_pay >> sum(refund_pay_amount) |  |
| billAmount |  | BigDecimal | 下账金额 | offline_refund_order_detail >> sum(bill_amount) |  |
| paymentList |  | List<Object> | 支付方式 | offline_refund_order_pay |  |
|  | payType | String | 支付方式 | offline_refund_order_pay >> pay_name |  |
|  | payAmount | BigDecimal | 支付金额 | offline_refund_order_pay >> pay_amount |  |
5. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| orderNo |  | String | 订单号 | es_refund_shard_by_org >> orderNo |  |
| thirdOrderNo |  | String | 三方单号 | es_refund_shard_by_org >> thirdOrderNo |  |
| refundNo |  | String | 退单号 | es_refund_shard_by_org >> refundNo |  |
| thirdRefundNo |  | String | 三方退单号 | es_refund_shard_by_org >> thirdRefundNo |  |
| refundCreated |  | String | 创建时间格式：秒级时间戳 | es_refund_shard_by_org >> created |  |
| refundDetailList |  | List<Object> | 退款详情列表 |  |  |
|  | refundDetailId | String | 退款明细id | es_refund_shard_by_org >> refundDetailList >> refundDetailId |  |
|  | gift | Boolean | 是否为赠品 |  |  |
|  | erpCode | String | 商品编码 | es_refund_shard_by_org >> refundDetailList >> erpCode |  |
|  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_refund_shard_by_org >> refundDetailList >> ItemCount |  |
|  | ItemName | String | 商品名称 | es_refund_shard_by_org >> refundDetailList >> ItemName |  |
|  | itemPicUrl | String | 商品图片 | hydee-middle-merchandise | /1.0/ds/Basic_Spec |
|  | itemPrice | BigDecimal | 实付金额 | offline_refund_order_detail >> price |  |
|  | itemOriginalPrice | BigDecimal | 划线价 | offline_refund_order_detail >> original_price |  |
| refundAmount |  | BigDecimal | 实际退款金额 | offline_refund_order_pay >> sum(refund_pay_amount) |  |
| billAmount |  | BigDecimal | 下账金额 | offline_refund_order_detail >> sum(bill_amount) |  |
| paymentList |  | List<Object> | 支付方式 | offline_refund_order_pay |  |
|  | payType | String | 支付方式 | offline_refund_order_pay >> pay_name |  |
|  | payAmount | BigDecimal | 支付金额 | offline_refund_order_pay >> pay_amount |  |
6. 流程图：


### 列表分页查询

1. url: /c/order/r/1.0/page/query
2. 请求体
  1. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| pageSize |  | Integer | 每页条数 | 否 | 不传默认20条 |
| searchAfter |  | String | 翻页参数 | 第一页可为空，否则不能为空 |  |
| searchConditionList |  | List<Object> |  |  |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),ORDER_NO(订单号),THIRD_ORDER_NO(三方订单号)ERP_CODE(商品编码),ERP_INFO(商品编码/商品名称)ORDER_STATUS(订单状态)，ORDER_FLAG(订单标记) | 否 | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 查询值 | 否 | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：yyyy-MM-dd，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：yyyy-MM-dd，如果此时searchData为空，默认处理为 now() |
3. | 字段名 |  | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- | --- |
| pageSize |  | Integer | 每页条数 | 否 | 不传默认20条 |
| searchAfter |  | String | 翻页参数 | 第一页可为空，否则不能为空 |  |
| searchConditionList |  | List<Object> |  |  |  |
|  | searchType | enum | 查询条件类型，可用值：ORG_CODE(门店编码),ORDER_SOURCE(订单来源),START_DATE(开始时间),END_DATE(结束时间),ORDER_NO(订单号),THIRD_ORDER_NO(三方订单号)ERP_CODE(商品编码),ERP_INFO(商品编码/商品名称)ORDER_STATUS(订单状态)，ORDER_FLAG(订单标记) | 否 | 必须要附加searchType=ORG_CODE和ORDER_SOURCE的条件 |
|  | searchData | String | 查询值 | 否 | 当searchType不为空时，该字段不能为空特殊场景：1 当searchType = START_DATE时，格式：yyyy-MM-dd，如果此时searchData为空，默认处理为 now() - 6mouth2 当searchType = END_DATE时，格式：yyyy-MM-dd，如果此时searchData为空，默认处理为 now() |
4. 响应体
  1. | 字段名 |  | 字段类型 | 字段描述 | 备注 | 数据源 |  |
| --- | --- | --- | --- | --- | --- | --- |
| totalCount |  |  | Long | 总条数 |  |  |
| searchAfter |  |  | String | 翻页参数 |  |  |
| data |  |  | List<Object> |  |  |  |
|  | orderNo |  | String | 订单号 | es_order_shard_by_org >> orderNo |  |
|  | thirdOrderNo |  | String | 三方单号 | es_order_shard_by_org >> thirdOrderNo |  |
|  | dayNum |  | String | 每日单号 | es_order_shard_by_org >> dayNum |  |
|  | orderStatus |  | Integer | 订单状态，线下单默认为已完成 | es_order_shard_by_org >> orderStatus |  |
|  | platfomCode |  | String | 平台编码 | es_order_shard_by_org >> platformCode |  |
|  | orderPrompts |  | List<Object> | 订单提示信息 | es_order_shard_by_org >> orderFlags |  |
|  |  | flag |  | EXCEPTION：该订单为异常订单REFUNDING:退款中ROUTE_IN：订单路由（转入）ROUTE_OUT：订单路由（转出） |  |  |
|  |  | title |  | 标题 |  |  |
|  |  | content |  | 内容 |  |  |
|  | prescriptionFlag |  | Boolean | 是否为处方单 | es_order_shard_by_org >> orderFlags 包含 ”PRESCRIPTION“ |  |
|  | orgCode |  | String | 实际发货门店编码 | es_order_shard_by_org >> orgCode |  |
|  | sourceOrgCode |  | String | 下单门店编码 | es_order_shard_by_org >> sourceOrgCode |  |
|  | orderCreated |  | String | 创建时间,秒级时间戳 | es_order_shard_by_org >> created |  |
|  | payTime |  | String | 支付时间,秒级时间戳 | es_order_shard_by_org >> payTime |  |
|  | deliveryType |  | Integer | 配送方式   ``` 1, 平台配送 2, 平台合作方配送 3, 门店配送 4, 到店自提 5, 快递 ``` | order_delivery_record >> delivery_type |  |
|  | deliveryTimeType |  | Integer | 送达方式：0即时 1预约 | order_info >> delivery_time_type |  |
|  | deliveryTimeDesc |  | String | 期望送达时间描述 | order_info >> delivery_time_desc |  |
|  | riderName |  | String | 配送员 | order_delivery_record >> rider_name |  |
|  | riderPhone |  | String | 配送员电话 | order_delivery_record >> rider_phone |  |
|  | orderDetailList |  | List<Object> | 订单详情列表 |  |  |
|  |  | orderDetailId | String |  | es_order_shard_by_org >> orderDetailList >> orderDetailId |  |
|  |  | erpCode | String | 商品编码 | es_order_shard_by_org >> orderDetailList >> erpCode |  |
|  |  | itemCount | String | 商品数量 | es_order_shard_by_org >> orderDetailList >> ItemCount |  |
|  |  | ItemName | String | 商品名称 | es_order_shard_by_org >> orderDetailList >> ItemName |  |
|  |  | itemPicUrl | String | 商品图片 | 商品组接口，待补充 |  |
|  | hasRefund |  | Boolean | 是否有退款单 |  |  |
|  | refundStatus |  | Integer | 退款状态 | es_online_refund >> refundStatus |  |
|  | refundType |  | String | 退款类型, PART-部分退款，ALL-全额退款 | es_online_refund >> refundType |  |
|  | orderAmount |  | BigDecimal | 订单金额 |  |  |
|  | salerList |  | List<Object> | 售货员列表 |  |  |
|  |  | salerCode | String | 售货员Code（线下单才会有） | offline_order_cashier_desk >> cashier |  |
|  |  | salerName | String | 售货员姓名（线下单才会有） | offline_order_cashier_desk >> cashier_name |  |
5. | 字段名 |  | 字段类型 | 字段描述 | 备注 | 数据源 |  |
| --- | --- | --- | --- | --- | --- | --- |
| totalCount |  |  | Long | 总条数 |  |  |
| searchAfter |  |  | String | 翻页参数 |  |  |
| data |  |  | List<Object> |  |  |  |
|  | orderNo |  | String | 订单号 | es_order_shard_by_org >> orderNo |  |
|  | thirdOrderNo |  | String | 三方单号 | es_order_shard_by_org >> thirdOrderNo |  |
|  | dayNum |  | String | 每日单号 | es_order_shard_by_org >> dayNum |  |
|  | orderStatus |  | Integer | 订单状态，线下单默认为已完成 | es_order_shard_by_org >> orderStatus |  |
|  | platfomCode |  | String | 平台编码 | es_order_shard_by_org >> platformCode |  |
|  | orderPrompts |  | List<Object> | 订单提示信息 | es_order_shard_by_org >> orderFlags |  |
|  |  | flag |  | EXCEPTION：该订单为异常订单REFUNDING:退款中ROUTE_IN：订单路由（转入）ROUTE_OUT：订单路由（转出） |  |  |
|  |  | title |  | 标题 |  |  |
|  |  | content |  | 内容 |  |  |
|  | prescriptionFlag |  | Boolean | 是否为处方单 | es_order_shard_by_org >> orderFlags 包含 ”PRESCRIPTION“ |  |
|  | orgCode |  | String | 实际发货门店编码 | es_order_shard_by_org >> orgCode |  |
|  | sourceOrgCode |  | String | 下单门店编码 | es_order_shard_by_org >> sourceOrgCode |  |
|  | orderCreated |  | String | 创建时间,秒级时间戳 | es_order_shard_by_org >> created |  |
|  | payTime |  | String | 支付时间,秒级时间戳 | es_order_shard_by_org >> payTime |  |
|  | deliveryType |  | Integer | 配送方式   ``` 1, 平台配送 2, 平台合作方配送 3, 门店配送 4, 到店自提 5, 快递 ``` | order_delivery_record >> delivery_type |  |
|  | deliveryTimeType |  | Integer | 送达方式：0即时 1预约 | order_info >> delivery_time_type |  |
|  | deliveryTimeDesc |  | String | 期望送达时间描述 | order_info >> delivery_time_desc |  |
|  | riderName |  | String | 配送员 | order_delivery_record >> rider_name |  |
|  | riderPhone |  | String | 配送员电话 | order_delivery_record >> rider_phone |  |
|  | orderDetailList |  | List<Object> | 订单详情列表 |  |  |
|  |  | orderDetailId | String |  | es_order_shard_by_org >> orderDetailList >> orderDetailId |  |
|  |  | erpCode | String | 商品编码 | es_order_shard_by_org >> orderDetailList >> erpCode |  |
|  |  | itemCount | String | 商品数量 | es_order_shard_by_org >> orderDetailList >> ItemCount |  |
|  |  | ItemName | String | 商品名称 | es_order_shard_by_org >> orderDetailList >> ItemName |  |
|  |  | itemPicUrl | String | 商品图片 | 商品组接口，待补充 |  |
|  | hasRefund |  | Boolean | 是否有退款单 |  |  |
|  | refundStatus |  | Integer | 退款状态 | es_online_refund >> refundStatus |  |
|  | refundType |  | String | 退款类型, PART-部分退款，ALL-全额退款 | es_online_refund >> refundType |  |
|  | orderAmount |  | BigDecimal | 订单金额 |  |  |
|  | salerList |  | List<Object> | 售货员列表 |  |  |
|  |  | salerCode | String | 售货员Code（线下单才会有） | offline_order_cashier_desk >> cashier |  |
|  |  | salerName | String | 售货员姓名（线下单才会有） | offline_order_cashier_desk >> cashier_name |  |
6. 流程图


### 订单详情

1. url: /c/order/r/1.0/info
2. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orderNo | String | 订单号 | 是 |  |
3. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orderNo | String | 订单号 | 是 |  |
4. 响应体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| orderNo |  | String | 订单号 | es_order_shard_by_org >> orderNo |  |
| thirdOrderNo |  | String | 三方单号 | es_order_shard_by_org >> thirdOrderNo |  |
| orderStatus |  | Integer | 订单状态 | es_order_shard_by_org >> orderStatus |  |
| buyerMessage |  | String | 买家备注 | order_info >> buyer_remark |  |
| exceptionList |  | List<String> | 异常信息 | order_info >> lock_msg |  |
| orderCreated |  | String | 下单时间，秒级时间戳 | es_offline_order >> created |  |
| orderPayTime |  | String | 订单支付时间，秒级时间戳 |  |  |
| receiverDetail |  | Object | 收件人信息 |  |  |
|  | receiverName | String | 收件人名 | order_delivery_address >> receiver_name |  |
|  | receiverPhone | String | 收件人电话 | order_delivery_address >> receiver_mobile |  |
|  | receiverAddress | String | 收件人地址 | order_delivery_address >> full_address |  |
| orderDetailList |  | List<Object> | 订单明细信息 |  |  |
|  | orderDetailId | String | 明细id | es_order_shard_by_org >> orderDetailList >> orderDetailId |  |
|  | erpCode | String | 商品编码 | es_order_shard_by_org >> orderDetailList >> erpCode |  |
|  | gift | boolean | 是否为赠品 | order_detail >> is_gift |  |
|  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_order_shard_by_org >> orderDetailList >> ItemCount |  |
|  | ItemName | String | 商品名称 | es_order_shard_by_org >> orderDetailList >> ItemName |  |
|  | itemPicUrl | String | 商品图片 | order_detail >> main_pic |  |
|  | itemPrice | BigDecimal | 实际单价 | order_detail >> price |  |
|  | itemOriginalPrice | BigDecimal | 划线价 | order_detail >> original_price |  |
|  | status | Integer | 0, "正常",1, "库存不足异常",2, "商品不存在",10, "已换货",11, "已退款" | order_detail >> status |  |
|  | swapDetailId | String | 替换该商品的orderDetailId（线上单会有） | order_detail >> swap_id |  |
|  | hasRefund | Boolean | 是否有退款 | refund_detail表进行匹配，现在退款明细表中的order_detail_id未维护，根据erpCode匹配？赠品？ |  |
|  | refundBillAmount | BigDecimal | 退款下账金额 | refund_detail >> bill_price* refund_count |  |
| orderPayInfo |  | Object |  |  |  |
|  | itemTotalAmount | BigDecimal | 商品总价 | order_pay_info >> total_amount |  |
|  | packageAmount | BigDecimal | 包装费 | order_pay_info >> pack_fee |  |
|  | deliveryAmount | BigDecimal | 运费 | order_pay_info >> delivery_fee |  |
|  | itemTotalDiscountAmount | BigDecimal | 商品优惠 | order_pay_info >> merchant_total_discount_sum_not_delivery_fee + platform_discount_sum_not_delivery_fee |  |
|  | deliveryDiscountAmount | BigDecimal | 运费优惠 | order_pay_info >> platform_delivery_fee_discount + merchant_delivery_fee_discount |  |
|  | payAmount | BigDecimal | 实付金额 | order_pay_info >> buyer_actual_amount |  |
|  | billAmount | BigDecimal | 下账金额 | erp_bill_info >> bill_total_amount |  |
| pickerId |  | String | 拣货员Code | order_info >> picker_id |  |
| pickerName |  | String | 拣货员姓名 | order_info >> picker_name |  |
| salerList |  |  |  |  |  |
|  | salerCode | String | 售货员Code |  |  |
|  | salerName | String | 售货员姓名 |  |  |
5. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| orderNo |  | String | 订单号 | es_order_shard_by_org >> orderNo |  |
| thirdOrderNo |  | String | 三方单号 | es_order_shard_by_org >> thirdOrderNo |  |
| orderStatus |  | Integer | 订单状态 | es_order_shard_by_org >> orderStatus |  |
| buyerMessage |  | String | 买家备注 | order_info >> buyer_remark |  |
| exceptionList |  | List<String> | 异常信息 | order_info >> lock_msg |  |
| orderCreated |  | String | 下单时间，秒级时间戳 | es_offline_order >> created |  |
| orderPayTime |  | String | 订单支付时间，秒级时间戳 |  |  |
| receiverDetail |  | Object | 收件人信息 |  |  |
|  | receiverName | String | 收件人名 | order_delivery_address >> receiver_name |  |
|  | receiverPhone | String | 收件人电话 | order_delivery_address >> receiver_mobile |  |
|  | receiverAddress | String | 收件人地址 | order_delivery_address >> full_address |  |
| orderDetailList |  | List<Object> | 订单明细信息 |  |  |
|  | orderDetailId | String | 明细id | es_order_shard_by_org >> orderDetailList >> orderDetailId |  |
|  | erpCode | String | 商品编码 | es_order_shard_by_org >> orderDetailList >> erpCode |  |
|  | gift | boolean | 是否为赠品 | order_detail >> is_gift |  |
|  | itemCount | String | 商品数量（有小数的情况，如称重中药） | es_order_shard_by_org >> orderDetailList >> ItemCount |  |
|  | ItemName | String | 商品名称 | es_order_shard_by_org >> orderDetailList >> ItemName |  |
|  | itemPicUrl | String | 商品图片 | order_detail >> main_pic |  |
|  | itemPrice | BigDecimal | 实际单价 | order_detail >> price |  |
|  | itemOriginalPrice | BigDecimal | 划线价 | order_detail >> original_price |  |
|  | status | Integer | 0, "正常",1, "库存不足异常",2, "商品不存在",10, "已换货",11, "已退款" | order_detail >> status |  |
|  | swapDetailId | String | 替换该商品的orderDetailId（线上单会有） | order_detail >> swap_id |  |
|  | hasRefund | Boolean | 是否有退款 | refund_detail表进行匹配，现在退款明细表中的order_detail_id未维护，根据erpCode匹配？赠品？ |  |
|  | refundBillAmount | BigDecimal | 退款下账金额 | refund_detail >> bill_price* refund_count |  |
| orderPayInfo |  | Object |  |  |  |
|  | itemTotalAmount | BigDecimal | 商品总价 | order_pay_info >> total_amount |  |
|  | packageAmount | BigDecimal | 包装费 | order_pay_info >> pack_fee |  |
|  | deliveryAmount | BigDecimal | 运费 | order_pay_info >> delivery_fee |  |
|  | itemTotalDiscountAmount | BigDecimal | 商品优惠 | order_pay_info >> merchant_total_discount_sum_not_delivery_fee + platform_discount_sum_not_delivery_fee |  |
|  | deliveryDiscountAmount | BigDecimal | 运费优惠 | order_pay_info >> platform_delivery_fee_discount + merchant_delivery_fee_discount |  |
|  | payAmount | BigDecimal | 实付金额 | order_pay_info >> buyer_actual_amount |  |
|  | billAmount | BigDecimal | 下账金额 | erp_bill_info >> bill_total_amount |  |
| pickerId |  | String | 拣货员Code | order_info >> picker_id |  |
| pickerName |  | String | 拣货员姓名 | order_info >> picker_name |  |
| salerList |  |  |  |  |  |
|  | salerCode | String | 售货员Code |  |  |
|  | salerName | String | 售货员姓名 |  |  |
6. 流程图：


### 线上单-查询订单三方平台明细

1. url：/c/order/r/1.0/third-detail
2. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orgCode | String | 门店编码 | 是 |  |
| orderNo | String | 订单号 | 是 |  |
3. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orgCode | String | 门店编码 | 是 |  |
| orderNo | String | 订单号 | 是 |  |
4. 响应体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| originItemList |  | List<Object> |  |  |  |
|  | thirdDetailId | String |  | ori_third_order_detail >> third_detail_id |  |
|  | erpCode | String | 商品编码 | ori_third_order_detail >> outer_iid |  |
|  | itemCount | String | 商品数量 | ori_third_order_detail >> num |  |
|  | ItemName | String | 商品名称 | ori_third_order_detail >> title |  |
|  | itemPicUrl | String | 商品图片 | 表中未存图片，查商品组？ |  |
5. | 字段名 |  | 字段类型 | 字段描述 | 数据源 | 备注 |
| --- | --- | --- | --- | --- | --- |
| originItemList |  | List<Object> |  |  |  |
|  | thirdDetailId | String |  | ori_third_order_detail >> third_detail_id |  |
|  | erpCode | String | 商品编码 | ori_third_order_detail >> outer_iid |  |
|  | itemCount | String | 商品数量 | ori_third_order_detail >> num |  |
|  | ItemName | String | 商品名称 | ori_third_order_detail >> title |  |
|  | itemPicUrl | String | 商品图片 | 表中未存图片，查商品组？ |  |
6. 流程图


### 用户隐私信息解密接口

1. url: order/online/receiver/decrypt
2. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orgCode | String | 门店编码 | 是 |  |
| orderNo | String | 订单号 | 是 |  |
3. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| orgCode | String | 门店编码 | 是 |  |
| orderNo | String | 订单号 | 是 |  |
4. 响应体：
  1. | 字段名 | 字段类型 | 字段描述 | 备注 | 数据源 |
| --- | --- | --- | --- | --- |
| receiverName | String | 收件人名 |  |  |
| receiverPhone | String | 收件人电话 |  |  |
| receiverAddress | String | 收件人地址 |  |  |
5. | 字段名 | 字段类型 | 字段描述 | 备注 | 数据源 |
| --- | --- | --- | --- | --- |
| receiverName | String | 收件人名 |  |  |
| receiverPhone | String | 收件人电话 |  |  |
| receiverAddress | String | 收件人地址 |  |  |
6. 异常：
  1. 达到查看次数限制
7. 达到查看次数限制


## job设计

### 订单历史数据刷ES job

1. job名：
2. 执行时间：
3. job参数：
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| processType | String | 处理类型offline_orderoffline_refundonline_orderonline_refund | 是 |  |
| processBatchSize | Integer | 每批次处理多少条 | 否 | 默认1000 |
| processBatch | Integer | 一次job运行，执行多少批次 | 否 | 默认100 |
| startTime | String | 开始时间，格式：yyyy-MM-dd HH:mmss | 是 |  |
| endTime | String | 结束时间，格式：yyyy-MM-dd HH:mmss | 是 |  |
4. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| processType | String | 处理类型offline_orderoffline_refundonline_orderonline_refund | 是 |  |
| processBatchSize | Integer | 每批次处理多少条 | 否 | 默认1000 |
| processBatch | Integer | 一次job运行，执行多少批次 | 否 | 默认100 |
| startTime | String | 开始时间，格式：yyyy-MM-dd HH:mmss | 是 |  |
| endTime | String | 结束时间，格式：yyyy-MM-dd HH:mmss | 是 |  |
5. 其他


### ES数据清理

1. job名：
2. 执行时间：凌晨
3. job参数
  1. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| keepDays | Integer | 保留天数 | 否 | 默认保存6个月，即6*30 = 180 |
| processType | String | 处理类型offline_orderoffline_refundonline_orderonline_refund | 是 |  |
| processBatchSize | Integer | 每批次删除多少条 | 否 | 默认10000 |
| processBatch | Integer | 一次job运行，执行多少批次 | 否 | 为null表示一直删除，直到删除完毕 |
4. | 字段名 | 字段类型 | 字段描述 | 是否必填 | 备注 |
| --- | --- | --- | --- | --- |
| keepDays | Integer | 保留天数 | 否 | 默认保存6个月，即6*30 = 180 |
| processType | String | 处理类型offline_orderoffline_refundonline_orderonline_refund | 是 |  |
| processBatchSize | Integer | 每批次删除多少条 | 否 | 默认10000 |
| processBatch | Integer | 一次job运行，执行多少批次 | 否 | 为null表示一直删除，直到删除完毕 |
5. 其他


### 搜索历史记录清理

依赖mongo的自动过期索引处理即可

# 开发排期

| 模块 | 功能 | 开发时间（d） | 备注 |
| --- | --- | --- | --- |
| 线下单 | 历史数据同步（索引构建、正单、退单） | 2 | 依赖润康的消费记录索引 |
| 增量数据同步（正单、退单） | 1 |  |
| 正/逆单金额统计 | 0.5 |  |
| 列表分页查询 | 1 |  |
| 订单详情 | 0.5 |  |
| 退款单分页列表 | 1 |  |
| 退款单详情 | 0.5 |  |
| 合计：6.5 |
| 线上单 | 历史数据同步（索引构建、正单、退单） | 2 | 依赖润康的消费记录索引 |
| 增量数据同步（正单、退单） | 1 |  |
| 正/逆单金额统计 | 0.5 |  |
| 列表分页查询 | 1 |  |
| 订单详情 | 0.5 |  |
| 三方平台明细查询 | 0.5 |  |
| 用户隐私信息查看 | 0.5 |  |
| 合计：6 |
| 搜索记录 | 保存 | 1 |  |
| 查询 |  |
| 删除 |  |
| 联调：2 |
| 合计：15.5 |