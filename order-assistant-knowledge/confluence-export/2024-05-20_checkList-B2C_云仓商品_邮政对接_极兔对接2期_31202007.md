# 2024-05-20 checkList-B2C/云仓商品 邮政对接/极兔对接2期

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  | 1``` hydee-oms-logistic ``` | ``` feature/ORDER-1044/logistic_order_optimize ``` |  |  |  | 杨飞 |  | 2024-05-20 11:30 | 2023-10-17 10:00 |
|  | 2``` hydee-business-order-web ``` | ``` feature/ORDER-1044/logistic_order_optimize ``` |  |  |  | 杨飞 |  | 2024-05-20 11:30 |  |
|  | k8s-hydee-im-server | feature/ORDER-1044/logistic_order_optimize |  |  |  |  |  | 2024-05-20 11:30 |  |
|  | printservice | dev |  |  |  |  |  |  |  |
|  |  | k8s-hydee-business-order | feature/ORDER-1044/logistic_order_optimize |  |  |  |  |  |  |  |
|  | 前端 |  |  |  |  |  |  |  |  |  |
|  |  | cloud-ui | realses |  |  |  |  |  |  |  |
|  |  | hydee-merchant-platform-ui | realses |  |  |  |  |  |  |  |
|  |  | 9 |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

sql-- 顺丰
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:SF', 'partnerId', 'PartnerID', '请输入PartnerID', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:SF', 'partnerKey', 'PartnerKey', '请输入PartnerKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 申通
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
    ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:STO', 'appKey', 'SecretKey', '请输入SecretKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
    ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:STO', 'appCode', 'AppCode', '请输入AppCode', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
   ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:STO', 'appSecret', 'AppSecret', '请输入AppSecret', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 中通
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:ZTO', 'appKey', 'AppKey', '请输入AppKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:ZTO', 'secretKey', 'SecretKey', '请输入SecretKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 韵达
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
   ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:YUNDA', 'partnerId', 'PartnerID', '请输入PartnerID', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
   ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:YUNDA', 'partnerKey', 'PartnerKey', '请输入PartnerKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 百世
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:HT', 'partnerId', 'PartnerID', '请输入PartnerID', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:HT', 'partnerKey', 'PartnerKey', '请输入PartnerKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 邮政
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:YZXB', 'authorization', '授权码', '请输入授权码', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:YZXB', 'key', '密钥', '请输入密钥', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);
-- 极兔
INSERT INTO `dscloud`.`oms_custom_attributes`(`create_by`, `create_time`, `update_by`, `update_time`, `key`, `column_code`, `column_name`, `placeholder`, `type`, `seq`, `require_flag`, `status`, `type_attrs`, `tooltip`, `default_value`, `value_type`, `value_extend`) VALUES
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:JTSD', 'apiAccount', 'ApiAccount', '请输入ApiAccount', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL),
 ( NULL, NULL, NULL, NULL, 'EXPRESS_MERCHANT:JTSD', 'privateKey', 'PrivateKey', '请输入PrivateKey', 'input', 1, 1, 1, NULL, NULL, NULL, 1, NULL);

-- 店铺配置物流公司
-- 直连配置
-- 顺丰
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_SF', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_SF', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_SF', 'provinceCityDistrict', '发货省-市-区', '请输入发货省-市-区', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_SF', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_SF', 'monthlyCard', '月结卡账号', '请输入月结卡账号', 'input', 5, 1, 1, null, null, null, 1, null);

-- 申通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'provinceCityDistrict', '发货省-市-区', '请输入发货省-市-区', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'customerName', '客户编码', '请输入客户编码', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'monthCustomerCode', '月结客户编码', '请输入月结客户编码', 'input', 6, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'sizeCode', '网点编码', '请输入网点编码', 'input', 7, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_STO', 'sitePwd', '电子面单密码', '请输入电子面单密码', 'input', 8, 1, 1, null, null, null, 1, null);
-- 中通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'provinceCityDistrict', '发货省-市-区', '请输入发货省-市-区', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'siteCode', '网点编码', '请输入网点Code', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'accountId', '电子面单账号', '请输入电子面单账号', 'input', 6, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_ZTO', 'accountPassword', '电子面单密码', '请输入电子面单密码', 'input', 7, 1, 1, null, null, null, 1, null);
-- 韵达
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'provinceCityDistrict', '发货省-市-区', '请输入省份', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'appId', '合作商ID', '请输入合作商ID', 'input', 5, 1, 1, null, null, '001143', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YUNDA', 'appSecret', '合作商密码', '请输入合作商密码', 'input', 6, 1, 1, null, null, '5cff10b4ac504f3ca356d6496d2be927', 1, null);
-- 百世
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'provinceCityDistrict', '发货省-市-区', '请输入发货省-市-区', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'username', '电子面单账户', '请输入电子面单账户', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_HT', 'pass', '电子面单密码', '请输入电子面单密码', 'input', 6, 1, 1, null, null, null, 1, null);
-- 邮政小包
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'provinceCityDistrict', '寄件人地址', '请输入寄件人地址', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'detail', '详细地址', '请输入详细地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'bizProductNo', '产品类别', '请输入产品类别', 'select', 5, 1, 1, null, null, null, 2, 'YZ_PRODUCT_NO_TYPE');
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YZXB', 'senderNo', '客户代码', '请输入客户代码', 'input', 6, 1, 1, null, null, '1100134918907', 1, null);
-- 极兔
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'provinceCityDistrict', '寄件人地址', '请选择寄件人地址', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'detail', '详细地址', '请输入详细地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'expressType', '产品类别', '请输入产品类别', 'select', 5, 1, 1, null, null, null, 2, 'JT_EXPRESS_TYPE');
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'customerCode', '客户编码', '请输入客户编码', 'input', 6, 1, 1, null, null, 'J00866428447', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'customerPwd', '客户密码', '请输入客户密码', 'input', 7, 1, 1, null, null, 'YXTyxt123', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JTSD', 'signConst', '签名常量', '请输入签名常量', 'input', 8, 1, 1, null, null, 'jadada236t2', 1, null);
-- 圆通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'provinceCityDistrict', '发货省-市-区', '请输入发货省-市-区', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, null, 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'clientId', '客户编码', '请输入客户编码', 'input', 5, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_YTO', 'appSecret', '商家密钥', '请输入商家密钥', 'input', 6, 1, 1, null, null, null, 1, null);
-- 京东快递
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'provinceCityDistrict', '发货省-市-区', '请输入发货地址', 'cascader', 3, 1, 1, '{"props": {"label": "codeDesc", "value": "codeDesc"}}', null, '', 4, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'detail', '发货地址', '请输入发货地址', 'input', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'customerCode', '青龙商家编码', '请输入青龙商家编码', 'input', 6, 1, 1, null, null, '', 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'province', '发货省份', '请输入省份', 'input', 7, 1, 0, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'city', '发货城市', '请输入发货城市', 'input', 8, 1, 0, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_ZL_JD', 'district', '发货区域', '请输入发货区域', 'input', 9, 1, 0, null, null, null, 1, null);


-- 菜鸟云栈配置
-- 顺丰
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_SF', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_SF', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_SF', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_SF', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 申通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_STO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_STO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_STO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_STO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 中通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_ZTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_ZTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_ZTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_ZTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 韵达
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YUNDA', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YUNDA', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YUNDA', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YUNDA', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 百世
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_HT', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_HT', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_HT', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_HT', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 邮政小包
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YZXB', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YZXB', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YZXB', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YZXB', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 极兔
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JTSD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JTSD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JTSD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JTSD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 圆通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_YTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 京东快递
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3002_JD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);

-- 拼多多配置
-- 顺丰
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_SF', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_SF', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_SF', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_SF', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_SF', 'productType', '选择时效', '请选择时效', 'select', 5, 1, 1, null, null, null, 2, 'PDD_JD_PRODUCT_TYPE');
-- 申通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_STO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_STO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_STO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_STO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 中通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_ZTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_ZTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_ZTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_ZTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 韵达
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YUNDA', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YUNDA', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YUNDA', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YUNDA', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 百世
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_HT', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_HT', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_HT', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_HT', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 邮政小包
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YZXB', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YZXB', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YZXB', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YZXB', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 极兔
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JTSD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JTSD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JTSD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JTSD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 圆通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_YTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 京东快递
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3003_JD', 'productType', '选择时效', '请选择时效', 'select', 1, 1, 1, null, null, null, 2, 'PDD_JD_PRODUCT_TYPE');

-- 京东无界配置
-- 顺丰
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_SF', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_SF', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_SF', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_SF', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 申通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_STO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_STO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_STO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_STO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 中通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_ZTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_ZTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_ZTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_ZTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 韵达
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YUNDA', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YUNDA', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YUNDA', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YUNDA', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 百世
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_HT', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_HT', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_HT', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_HT', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 邮政小包
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YZXB', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YZXB', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YZXB', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YZXB', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 极兔
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JTSD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JTSD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JTSD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JTSD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 圆通
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YTO', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YTO', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YTO', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_YTO', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);
-- 京东快递
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JD', 'contacter', '寄件人姓名', '请输入寄件人姓名', 'input', 1, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JD', 'mobile', '寄件人电话', '请输入寄件人电话', 'input', 2, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JD', 'logisticBranchId', '发货网点', '请选择发货网点', 'select', 3, 1, 1, null, null, null, 1, null);
INSERT INTO dscloud.oms_custom_attributes (create_by, create_time, update_by, update_time, `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value, value_type, value_extend) VALUES (null, null, null, null, 'STORE_EXPRESS_MERCHANT_3004_JD', 'logisticBranchAddressId', '网点发货地址', '请选择网点发货地址', 'select', 4, 1, 1, null, null, null, 1, null);




-- 快递logo
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/SF.png?Expires=4870032160&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=fTV1s%2FBA3G%2FT3qDjtyZcTJGuYQw%3D' where id = 69;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/STO.png?Expires=4870032181&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=JCatXeexWoG9AVeNOXek2l7D2tQ%3D' where id = 70;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/YTO.png?Expires=4870032206&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=HLc8QgpYm67I4UOtvE59tfSBtxo%3D' where id = 71;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/ZTO.png?Expires=4870032230&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=9o8v4M84deRu3CYM7eo4C%2FQ5yys%3D' where id = 72;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/YUNDA.png?Expires=4870032254&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=4IZqCzklrOmZn%2FlfIfoKhOOGEDI%3D' where id = 73;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/JD.png?Expires=4870032395&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=lkQHb9B0Cyacp5UP0vgLnRso6VI%3D' where id = 74;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/HT.png?Expires=4870031735&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=wWECl5Ltw5YmTR90SN4EM2qY2Fk%3D' where id = 79;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/JTSD.png?Expires=4870032413&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=4GHJ9ZYSlrttaQAXaKcTxIkEMDE%3D' where id = 131;
update dscloud.dict_express_merchant set logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/expressLogo/YZ.png?Expires=4870032430&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=7ZM6RX3Np%2Bq2gM%2FQ9uxg001URxM%3D' where id = 1004;

-- 平台logo 只展示目前已对接的平台（淘宝、拼多多、京东、美团、饿百、微商城）
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E6%B7%98%E5%AE%9D.png?Expires=4870033031&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=%2Fn5VjgrpzjLsk1fkMZsAHVnVKb0%3D' WHERE id = 2;
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E6%8B%BC%E5%A4%9A%E5%A4%9A.png?Expires=4870033057&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=%2Fv6tz7a3GCR%2BKKajIzvmWLiJfDg%3D' WHERE id = 3;
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E4%BA%AC%E4%B8%9C.png?Expires=4870033085&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=TO0LygLUNgTmtlA9qy4NlGEi7Wo%3D' WHERE id = 4;
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E7%BE%8E%E5%9B%A2.png?Expires=4870033207&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=e0oUyr8rS%2Fdtdu2kWfWfDiBTiLw%3D' WHERE id = 19;
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E9%A5%BF%E7%99%BE.png?Expires=4870033237&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=cIUHmnILvGP6fr%2Fnxi1J52xc%2BVE%3D' WHERE id = 20;
UPDATE dscloud.ds_platform SET logo_path = 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E5%BE%AE%E5%95%86%E5%9F%8E.png?Expires=4870033257&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zA9x3nBhMiKBv0Po3HjfVqT%2B7GE%3D' WHERE id = 21;



-- 新增店铺配置id,打印面单时使用
ALTER TABLE `dscloud`.`logistic_order`
    ADD COLUMN `ds_online_store_express_merchant_id` bigint COMMENT '店铺快递配置id';

-- 模板规格表新增模板来源和模板加密标识字段
ALTER TABLE `dscloud`.`logistic_stdtemplates`
    ADD COLUMN `source` tinyint default 1 NOT NULL COMMENT '模板来源 1三方模板 2系统生成';
ALTER TABLE `dscloud`.`logistic_stdtemplates`
    ADD COLUMN `encrypt_flag` tinyint default 0 NOT NULL COMMENT '加密标识 0不加密 1加密';


-- 通用面单模板 赋值id用于模板的数据插入 执行前校验id是否使用
INSERT INTO dscloud.logistic_stdtemplates (id, sync_by, sync_time, standard_template_id, standard_template_name, standard_waybill_type, standard_template_url, wp_code, print_code, custom_area_url, custom_area_id, expanded, view_url, source, encrypt_flag) VALUES (750,null, null, null, '通用-联单(76mm*130mm)', 1, 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 'ZL', 'ZL', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', null, null, 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/%E9%9D%A2%E5%8D%95%E9%A2%84%E8%A7%88%E6%A8%A1%E6%9D%BF_%E6%98%8E%E6%96%87.png?Expires=4870033510&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=vFgTGd2CQm3USMNhFxVAbE8lBRs%3D', 2, 0);
INSERT INTO dscloud.logistic_stdtemplates (id, sync_by, sync_time, standard_template_id, standard_template_name, standard_waybill_type, standard_template_url, wp_code, print_code, custom_area_url, custom_area_id, expanded, view_url, source, encrypt_flag) VALUES (751,null, null, null, '通用-隐私联单(76mm*130mm)', 1, 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 'ZL', 'ZL', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', null, null, 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/%E9%9D%A2%E5%8D%95%E9%A2%84%E8%A7%88%E6%A8%A1%E6%9D%BF_%E5%AF%86%E6%96%87.png?Expires=4870033565&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=qv27cOVITIao05osOy5ynunmSZw%3D', 2, 1);

-- 通用发货单模板 赋值id用于模板的数据插入 执行前校验id是否使用
INSERT INTO `dscloud`.`logistic_stdtemplates` (`id`, `standard_template_id`, `standard_template_name`, `standard_waybill_type`, `standard_template_url`, `print_code`, `custom_area_id`, `source`, `encrypt_flag`) VALUES ('2000', '3', '发货单基础模板', '98', '', 'ZL', '0', '2', '0');

-- 非直连面单数据迁移
-- select * from logistic_config_info where id = 566 中通
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3003', 'eee5219664f94019983cf3c2fdcb1e86', '10',72, '3003', 1, 96, 236, 595, 249, 1, '{"contacter":"徐呈芬","mobile":"15658049006","logisticBranchId":"332","logisticBranchAddressId":"345"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 568 邮政小包
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3003', 'f4a7b88bc216471fa09006e1617b5b73', '15',1004, '3003', 1, 119, 232, 595, 249, 1, '{"contacter":"邓刊","mobile":"18708556325","logisticBranchId":"334","logisticBranchAddressId":"348"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 581 圆通
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3003', '7deac791e40d4f7493044bf355528dd7', '8',71, '3003', 1, 100, 257, 595, 249, 1, '{"contacter":"马颖","mobile":"022-87950969","logisticBranchId":"335","logisticBranchAddressId":"349"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 582 中通
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3003', 'ed7087a1ea9f42c4b6bd8aeffa798570', '10',72, '3003', 1, 96, 255, 595, 249, 1, '{"contacter":"陈晓宇","mobile":"","logisticBranchId":"337","logisticBranchAddressId":"351"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 583 中通
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3004', '5707ab60babe410682623450bcd102b6', '10',72, '3004', 0, 4, 243, null, null, 1, '{"contacter":"徐呈芬","mobile":"15658049006","logisticBranchId":"345","logisticBranchAddressId":"359"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 584 中通
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('3003', 'c0822bdbbbbb4fee8150d3cdaffba8f3', '10',72, '3003', 1, 96, 255, 595, 249, 1, '{"contacter":"一心堂天津颐安","mobile":"022-82100427","logisticBranchId":"346","logisticBranchAddressId":"360"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- 直连面单数据迁移
-- select * from logistic_config_info where id = 570 中通
-- 新增模板 执行sql之前需要确认id有没有被使用
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (300, '美团山西B2C-电子面单', 1, 1, 'ZL', 'ZTO', 72, '中通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-05-24 14:00:00', '2024-05-24 14:00:00');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 1, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 2, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 4, 1, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (300, 10, 0, 0, 8, 6);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', '5a216d92736a49c0b1e69c73ffec8c39', '10',72, 'ZL', 1, 751, 300, 576, 251, 1, '{"contacter":"陈晓宇","mobile":"***********","province":"山西省","city":"太原市","district":"迎泽区","detail":"小店区一心堂店","siteCode":"35364","accountId":"KDGJ221000072027","accountPassword":"DOJ86H3J"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (301, '山西一心堂大药房(快递发全国)-电子面单', 1, 1, 'ZL', 'ZTO', 72, '中通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-05-24 14:00:00', '2024-05-24 14:00:00');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 1, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 2, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 4, 1, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (301, 10, 0, 0, 8, 6);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('24', '160a585d8af24bdfa0c47a056785a431', '10',72, 'ZL', 1, 751, 301, 580, 240, 1, '{"contacter":"陈晓宇","mobile":"***********","province":"山西省","city":"太原市","district":"迎泽区","detail":"小店区一心堂店","siteCode":"35364","accountId":"KDGJ221000072027","accountPassword":"DOJ86H3J"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 575 圆通
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (302, '美团天津B2C-电子面单', 1, 1, 'ZL', 'YTO', 71, '圆通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-04-08 14:36:27', '2024-04-08 14:36:34');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 1, 1, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 2, 1, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 4, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 10, 0, 0, 8, 6);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 11, 0, 0, 8, 7);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 40, 0, 0, 8, -1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 41, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 42, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 174, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (302, 179, 0, 1, 8, 0);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', 'aa2d6de522074d4280d09ea94f85e06f', '8',71, 'ZL', 1, 751, 302, 576, 251, 1, '{"contacter":"刘莉","mobile":"15822038862","province":"天津","city":"天津市","district":"和平区","detail":"劝业场街道和平路226号二层","clientId":"K220143046","appSecret":"oGvHaJ89"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 579 圆通
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (303, '四川一心堂大药房旗舰店（快递电商）-电子面单', 1, 1, 'ZL', 'YTO', 71, '圆通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-04-08 14:36:27', '2024-04-08 14:36:34');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 1, 1, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 2, 1, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 4, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 10, 0, 0, 8, 6);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 11, 0, 0, 8, 7);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 40, 0, 0, 8, -1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 41, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 42, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 174, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (303, 179, 0, 1, 8, 0);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', 'c5b3ff4929274602a72b07d665a13a06', '8',71, 'ZL', 1, 751, 303, 576, 251, 1, '{"contacter":"四川一心堂","mobile":"028-80201152","province":"四川省","city":"成都市","district":"双流区","detail":"西南航空港经济开发区空港一路二段266号","clientId":"K280271241","appSecret":"uWAtvBwf"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 585 圆通
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (304, '重庆一心堂大药房旗舰店（快递电商）-电子面单', 1, 1, 'ZL', 'YTO', 71, '圆通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-04-08 14:36:27', '2024-04-08 14:36:34');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 1, 1, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 2, 1, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 4, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 10, 0, 0, 8, 6);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 11, 0, 0, 8, 7);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 40, 0, 0, 8, -1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 41, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 42, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 174, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (304, 179, 0, 1, 8, 0);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', 'f7ce86cec17f4462adce92f8a7d3373c', '8',71,'ZL', 1, 751, 304, 576, 251, 1, '{"contacter":"李红灯","mobile":"18983941158","province":"重庆","city":"重庆市","district":"其它区","detail":"北部新区星光大道62号海王星科技大厦D区6楼18#","clientId":"K230180900","appSecret":"m38qV5Ki"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 586 中通
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (305, '一心堂广生堂大药房旗舰店（快递电商）MT-电子面单', 1, 1, 'ZL', 'ZTO', 72, '中通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-05-24 14:00:00', '2024-05-24 14:00:00');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 1, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 2, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 4, 1, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (305, 10, 0, 0, 8, 6);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', '3f7c518923e548e5ac3fc6e8f31fbae0', '10',72, 'ZL', 1, 751, 305, 576, 251, 1, '{"contacter":" 一心堂侯马佳园店","mobile":"***********","province":"山西省","city":"临汾市","district":"侯马市","detail":"合欢街59号世纪佳园8幢A1号，2号房产","siteCode":"35742","accountId":"KDGJ221000086287","accountPassword":"K3CJHBEB"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 588 中通 存在相同店铺都绑定中通 需要产品确认
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (306, '饿了么-山西B2C-电子面单', 1, 1, 'ZL', 'ZTO', 72, '中通速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-05-24 14:00:00', '2024-05-24 14:00:00');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 1, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 2, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 4, 1, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (306, 10, 0, 0, 8, 6);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('24', 'c42e8c17ab4d41a98ff9a1bf05a81a87', '10',72, 'ZL', 1, 751, 306, 580, 240, 1, '{"contacter":" 刘店长","mobile":"***********","province":"山西省","city":"太原市","district":"小店区","detail":"小店区一心堂店","siteCode":"35364","accountId":"KDGJ221000072027","accountPassword":"DOJ86H3J"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 591 邮政小包
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (307, '贵州一心堂大药房旗舰店（快递发全国-电子面单', 1, 1, 'ZL', 'YZXB', 1004, '邮政小包快递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-04-08 14:36:27', '2024-04-08 14:36:34');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 1, 1, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 2, 1, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 4, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 10, 0, 0, 8, 6);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 11, 0, 0, 8, 7);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 40, 0, 0, 8, -1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 41, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 42, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 174, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (307, 179, 0, 1, 8, 0);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', '83fe15fb0c0b43469b2276a13e212dc7', '15',1004, 'ZL', 1, 751, 307, 576, 251, 1, '{"contacter":"邓刊","mobile":"18708556325","province":"贵州省","city":"贵阳市","district":"其它区","detail":"经济开发区小孟街道办事处小孟工业园1号","bizProductNo":"11","senderNo":"1100134918907"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);

-- select * from logistic_config_info where id = 593 极兔速递
INSERT INTO dscloud.logistic_templates_manage (id, name, templates_category, templates_type, platform_code, express_code, oms_express, express_name, templates_id, templates_name, templates_url, status, mer_code, create_time, update_time) VALUES
    (308, '海南一心堂大药房旗舰店（快递电商）MT', 1, 1, 'ZL', 'JTSD', 131, '极兔速递', 751, '通用-隐私联单(76mm*130mm)', 'http://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/stdTemplates/logisticstemplate.html?Expires=**********&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=zzMsAsW7MG41YmvvfuVkYPxZZvg%3D', 1, '500001', '2024-04-08 14:36:27', '2024-04-08 14:36:34');
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 1, 1, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 2, 1, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 3, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 4, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 5, 0, 0, 8, 1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 6, 0, 0, 8, 2);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 7, 0, 0, 8, 3);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 8, 0, 0, 8, 4);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 9, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 10, 0, 0, 8, 6);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 11, 0, 0, 8, 7);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 40, 0, 0, 8, -1);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 41, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 42, 0, 0, 8, 0);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 174, 0, 0, 8, 5);
INSERT INTO dscloud.logistic_customtemp_config_map (templates_manage_id, customtemp_config_id, selected, bold, font_size, seq) VALUES (308, 179, 0, 1, 8, 0);
INSERT INTO dscloud.ds_online_store_express_merchant (platform_code, online_store_code, express_merchant_id,dict_express_merchant_id, link_type, status, logistic_stdtemplates_id, logistic_template_manage_id, send_order_stdtemplates_id, send_order_template_manage_id, priority, extend, created_by, updated_by, created_time, updated_time, version) VALUES
    ('27', '91b6709fab3e427cb9c7602df8ec3e52', '13',131, 'ZL', 1, 751, 308, 576, 251, 1, '{"contacter":" 一心堂金盘路分店","mobile":"13976978684","province":"海南省","city":"澄迈县","district":"老城镇","detail":"生态软件园一环路5.3公里南侧办公楼2楼","expressType":"EZ","customerCode":"J00866428447","customerPwd":"YXTyxt123","signConst":"jadada236t2"}', '4098459363848514267', '4098459363848514267', '2024-05-23 14:05:14', '2024-05-23 14:46:45', 1);


-- 模板管理表添加名称唯一索引
CREATE UNIQUE INDEX idx_name_unique ON logistic_templates_manage(name);




create table ds_online_store_express_merchant
(
    id                            bigint auto_increment comment '主键'
        primary key,
    platform_code                 varchar(64)                        not null comment '平台编码',
    online_store_code             varchar(64)                        not null comment '线上店铺code',
    express_merchant_id           varchar(50)                        not null comment '快递商户id',
    dict_express_merchant_id      bigint                             not null comment '快递公司id',
    link_type                     varchar(50)                        not null comment '链接模式 3002:菜鸟云栈 3003:拼多多 3004:京东无界 ZL:直连',
    status                        tinyint  default 1                 not null comment '0禁用，1启用',
    logistic_stdtemplates_id      bigint                             null comment '面单规格模板id',
    logistic_template_manage_id   bigint                             null comment '面单模板id',
    send_order_stdtemplates_id    bigint                             null comment '发货单规格模板id',
    send_order_template_manage_id bigint                             null comment '发货单模板id',
    priority                      tinyint  default 1                 not null comment '优先级',
    extend                        text collate utf8mb4_bin           not null comment '扩展字段（存放网点设置所有信息）',
    created_by                    varchar(64)                        not null comment '创建人',
    updated_by                    varchar(64)                        not null comment '更新人',
    created_time                  datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_time                  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    version                       bigint   default 1                 not null comment '数据版本，每次update+1',
    constraint idx_store_express_link_unique
        unique (online_store_code, dict_express_merchant_id, link_type)
)
    comment '店铺快递配置表';

create index idx_online_store_code
    on ds_online_store_express_merchant (online_store_code);


-- 只在正式环境执行,初始化快递商配置
-- 顺丰
UPDATE express_merchant SET extend='{"partnerId":"HDRJ3Y1w","partnerKey":"xrR86t633dHCxTNQPW5Typr7EZAeCMqZ"}' WHERE id = 12;
-- 中通
UPDATE express_merchant SET extend='{"appKey":"1a980a49b3b53517c9e67","secretKey":"91087c58946b08b0cea4d8667aae7dca"}' WHERE id = 10;
-- 邮政
UPDATE express_merchant SET extend='{"authorization":"SRiUSv0HgLACgWKZ","key":"bkR5ZnV6WjYydjRYWHk0eQ=="}' WHERE id = 15;
-- 极兔
UPDATE express_merchant SET extend='{"apiAccount":"648427321165877273","privateKey":"1b4791cb17fb495eae69e0331597f219"}' WHERE id = 13;

#### 2.2 appolo配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hydee-oms-logistic | **application.yaml** | oms.b2c.useLogisticConfig | false |
|  |  |
|  |  |
| hydee-business-order-web | **application.yaml** | ``` push-to-ws-topic ``` | TOPIC_BUSINESS_CLOUD_PUSH_TO_WS_TEST |
|  |  |  |  |
|  |  |  |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |
|  |  |
|  |  |


#### 2.4 xxl-job配置变更

| 任务 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |


#### 2.5 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
| # V1.1.15 B2C（邮政对接/极兔对接/默认快递） | 新增极兔和邮政快递 | 通过界面配置不使用邮政和极兔即可 |
| 获取面单接口变更 | 获取面单逻辑 | 回滚hydee-order-web服务即可 |
| 打印面单支持直连打印 | 打印面单 | 回滚前端服务cloud-ui |
|  |  |  |
|  |  |  |
|  |  |  |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 complete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |


## 五 、回滚预案

1.打印模块，回滚前端代码