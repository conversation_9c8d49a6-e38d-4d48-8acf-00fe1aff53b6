# 24年第17周2024-05-10

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25号讨论完O2O订单域核心服务入参 | 20240520开始定排期 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审4月26日进入开发阶段 | 抖店正单通过SDK请求的接口80%抖店回调接口40%系统补偿任务待定日志部分待定 | . |
| 3 | 优雅发布升级 |  | 4月12日完成待办列表,推动全部门升级 | 已完成 | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  | 本地升级启动成功,部署失败。 | 20240510 开发环境部署成功,访问暂时有问题,排查中 | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表规范文档待编写 | 暂停 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | 支付中台重构 |  |  | 暂停 |  |
| 8 | Rocketmq |  |  | 已经梳理出规范,待评审 |  |
| 9 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 10 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1.吉客云技术方案编写/评审2.生产BUG用户权限门店超过一千家、美团组包商品问题3.V1.6.1 订单运维工具BUG/V1.6.1 订单运维工具上线4.吉客云新增订单5.MQ规范文档 | **计划工作** **㊁实际完成**1.吉客云技术方案编写/评审2.生产BUG用户权限门店超过一千家、美团组包商品问题3.V1.6.1 订单运维工具BUG/V1.6.1 订单运维工具上线4.吉客云新增订单逻辑4.MQ规范文档 **㊂遗留问题**1.吉客云新增订单2.MQ规范文档**㊃风险问题** **㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关**1. 吉客云 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. MQ **** | 新逻辑6位小数下账单表-下账金额 String |
| 2 | 杨润康 | **本周总工时：5d**- api-gateway网关压测,配置上线(jvm级别的参数下周二上线) - 本周生产交易值班，问题收集 - 线下单&线上单联调(部分开发)   - 接口新增商品名称字段   - 项目部署(仓库调整后重新部署)   - 父级依赖调整为yxt-xframe,自测 - 接口新增商品名称字段 - 项目部署(仓库调整后重新部署) - 父级依赖调整为yxt-xframe,自测 - business-order redis域名解析超时问题解决，配置上线 - 其他   - 订单中台创单服务流程熟悉(50%)   - businesses-gateway压测,会复现api-gateway网关问题   - businesses-gateway升级,开发环境部署成功,不配置local-mapping访问则报错,排查中 - 订单中台创单服务流程熟悉(50%) - businesses-gateway压测,会复现api-gateway网关问题 - businesses-gateway升级,开发环境部署成功,不配置local-mapping访问则报错,排查中 | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 3 | 杨俊峰 | **本周总工时：5day**1.三个平台的商品库存变更通知 1.5day2.隐私字段处理 1.5day （美团接口无法使用原有的client 调用，排查问题重写 client 花费了较多时间）3. 面单打印 （35%） 1.5day 4.处理test环境 达达配送 和产线问题定位 (优惠金额已经上线 ) 0.5day **** | **㊀计划工作****㊁实际完成****㊂遗留问题****1.面单打印目前处理了设置 状态上传 以及部分 面单打印组装代码。还剩下推送面单打印 以及打印部分功能****2.发货单打印还没有处理 这个原有逻辑已经实现了 搬移即可。****㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 4 |  | **本周总工时：5**1. 拣货复核时合并批号拣货测试+上线 2. 微商城购物车加购bug修复 3. .net迁移-抖店订单部分开发 4. 支付配置慢sql排查 5. 其他线上问题支持 | **㊀计划工作****㊁实际完成****㊂遗留问题**1. 拣货复核时合并批号拣货前端遗留一个bug（已上线） 2. 支付配置慢sql下周二上线 3. 前端历史bug：拣货时，商品批号取错的问题（前端跟进中） **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 |  | **本周总工时：5pd**1. 订单运维工具测试+上线 2. 吉客云技术方案评审 3. B2C物流多包裹回传 4. B2C更新快递单号 5. 吉客云退款检测接口 | **㊀计划工作**1. 订单运维工具测试+上线 2. 吉客云技术方案评审 3. B2C物流多包裹回传 4. B2C更新快递单号 5. 吉客云退款检测接口 **㊁实际完成**1. 订单运维工具测试+上线 2. 吉客云技术方案评审 3. B2C物流多包裹回传 4. B2C更新快递单号 5. 吉客云退款检测接口 **㊂遗留问题**B2C多包裹更新快递单号**㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：5pd**1.支付宝接口流程2.V1.1.15 B2C（邮政对接/极兔对接/默认快递） a.快递公司店铺映射管理 b.新对接快递商(部分完成) c.初始化数据(部分完成) | **㊀计划工作**1.支付宝接口流程2.V1.1.15 B2C（邮政对接/极兔对接/默认快递）**㊁实际完成** a.快递公司店铺映射管理 b.新对接快递商**㊂遗留问题**1.新对接快递商2.初始化数据**㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **** |  |
| 7 |  | **本周总工时：**1. 现金券云仓零元单无法退款问题修复 已上线 2. 美团收货隐私信息需求 前后端联调 3. 科传下账订单号由平台单号转为系统单号需求 已提测 4. 订单路由bug修复 5. 医保测试单配合测试 剩三家门店，东软系统出问题，暂时停滞 6. 小程序优惠金额浮点数问题，已解决。（前端计算问题） 7. 其它线上问题处理 | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 8 |  | **本周总工时：**1. 支付宝正单接入自测通过 2. 订单路由bug修复 3. 京东到家下账改造 4. B2C/O2O下账修改批次号（联调中） 5. 科传部分退款修改退单明细（待测试） 6. 其他线上问题处理 | **㊀计划工作**1. 支付宝流程接入 2. 订单路由测试 3. B2C/O2O下账修改批次号 4. 科传部分退款修改退单明细 **㊁实际完成**1. 订单路由测试 2. 京东到家下账改造 3. B2C/O2O下账修改批次号 4. 科传部分退款修改退单明细 **㊂遗留问题**1. 支付宝流程接入 2. 下账列表ES刷新按钮 **㊃风险问题** **㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 9 |  | **本周总工时：5pd**1. 快递管理功能开发2pd 2. 创建面单执行流程修改1pd 3. 初始化快递管理动态表单数据0.5pd | **㊀计划工作**1. 快递管理 2. 初始化快递管理动态表单数据 3. 创建面单执行流程修改 **㊁实际完成****㊂遗留问题** | **㊀需求研发相关**1. 邮政对接/极兔对接/默认快递技术方案编写 |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |