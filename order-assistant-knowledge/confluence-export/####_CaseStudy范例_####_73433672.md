# #### CaseStudy范例 ####

# 一、事件描述

2023-12-25 19:00左右 ： 测试和前端发现，门店编码包含中文或特殊字符时首页无数据展示的情况。

正常情况下首页会根据门店编码（理论上是只包含字母数字）从心云或大数据获取对应门店的数据并加载，如果该门店无数据或查不到该门店的情况下无数据展示。

## 事件回顾（排查分析）

1）2023-12-25 19:00左右：前端发现门店编码包含中文或特殊字符的门店首页无数据展示

2）2023-12-25 19:11：由于之前出现过门店编码包含中文，从心云侧了解到这些包含中文或特殊字符的门店编码认为属于脏数据，需要修改，于是联系心云修改处理脏数据

3）2023-12-26 11:00左右：和心云讨论这些脏数据的来源、处理和预防：

- 来源：心云从OA同步组织机构数据，OA侧用户录入门店编码时可随意输入（只有输入字母数字类型时会校验是否重复，无其他输入规则），导致门店编码可能会录入包含中文或特殊字符类型的内容
- 处理：由于当前OA无人维护，心云联系相关负责人无响应，无法从源头修改，为保证心云同步的数据中没有脏数据，心云将门店信息对应的id作为包含中文或特殊字符的门店编码的值
- 预防：由于心云同步组织架构信息是每天全量同步一次，为保证修改后的数据不会被覆盖，心云侧修改错误门店编码后，暂时将这些门店加入黑名单，后续不同步，并过滤掉新出现的错误门店编码的门店


4）由于确认包含中文或特殊字符的门店编码为脏数据，担心心云侧未完全过滤，计划一心助手兜底过滤并对其告警

5）2023-12-26 14:19：和产品对齐过滤这些包含中文或特殊字符的门店的可行性，这些包含中文的门店编码的门店为加盟店，而一心助手涉及的门店都为直营门店，于是判断可过滤

6）2023-12-26 15:00左右：提交修改的代码

****

7）2023-12-26 16:00左右：上线发布评审会中前端提出门店编码包含中文或特殊字符问题，对其说明过滤方案后，前端质疑单纯过滤这些门店是否合理

8）2023-12-26 17:00左右：评审会后，思考过滤这些门店后对全局业务是否有影响时，发现一心助手只在门店列表处过滤，默认门店依旧能获取错误门店，和靖哥讨论后发现单纯过滤这些门店的合理性有待确认

9）于是和靖哥到心云侧和大数据侧确认门店编码来源发现心云组织架构门店来源于OA，大数据则来源于ERP，ERP的门店编码有做限制，而OA的门店编码无限制，导致使用OA的门店编码来匹配ERP门店编码时匹配不到门店

10）由于中间还涉及多方多层关系12-26当日无法确定门店编码问题的最终解决方案，于是决定先回退之前修改的代码只保留告警

# 二、事件影响

## 1.业务影响

只过滤门店列表中门店编码包含中文或特殊字符的门店后，在获取默认登录门店时（此种情况一般是所属门店），默认登录的门店编码包含中文或特殊字符，在校验门店权限时由于门店列表过滤了此类型门店，导致匹配不上，从而使用户无法登录一心助手APP。

# 三、原因分析

## 1、原因概要

1. 对于对接方的反馈未进一步思考即认定此为结论
2. 未深究问题出现的原因
3. 未完全充分的考虑问题出现对业务各方面的影响
4. 未思考全面修改操作后对业务的影响以及是否有修改遗漏


## 2、对于对接方的反馈未进一步思考即认定此为结论

对于心云侧门店编码包含中文或特殊字符的门店信息为脏数据的反馈，就直接当做结论进行后续修改依据，实际上只是这些门店编码是由OA（手动输入，无任何填写规则）同步过来的加盟店或店中店数据，心云侧把这些门店都标为了直营店

## 3、未深究问题出现的原因

和心云讨论门店编码处理方案时没有深究为什么会出现包含中文或特殊字符的门店编码的原因，以及大数据门店编码和心云的门店编码不是一套数据来源

## 4、未完全充分的考虑问题出现对业务各方面的影响

发现问题后没有及时、全面的排查门店编码包含中文或特殊字符时对一心助手业务的影响，只排查到了对首页业绩看板数据展示的影响，导致不能全面评估问题的影响范围

心云组织架构的门店数据来源和大数据的门店数据来源不一致，未考虑完全业务影响就决定修改方案

## 5、未思考全面修改操作后对业务的影响以及是否有修改遗漏

没有充分考虑过滤这些门店后造成的影响，可能会导致只有该门店权限的员工被过滤后无法登录一心助手APP；修改时也只修改了门店列表处的过滤，未考虑到获取组织所属门店时的过滤，如果考虑到所属门店的过滤就会发现是否需要登录此门店的问题，从而发现其他来源问题

# 四、反思

## 1、发现问题时需要深究其出现原因。

深究问题原因，可能会发现更多隐藏未暴露出来的问题，从而了解到问题的本质原因；而不是了解其表层原因后就对其进行方案解决，反而会改出更多的问题

## 2、定解决方案时需尽可能的充分考虑影响范围

讨论解决方案时需要全面排查问题的影响范围，并考虑根据方案修改后会引起哪些变动，变动的结果是否正常

## 3、修改代码时需充分考虑其应用场景

根据解决方案修改时，需要充分考虑其对应场景，哪些场景是有必要修改的，哪些是可以保持不改，并评估修改和不修改的影响

# 五、改进措施

1. 发现问题时需要深究其出现原因。
2. 定解决方案时需尽可能的充分考虑影响范围
3. 修改代码时需充分考虑其应用场景


# 六、后续规划

1. 从自己的角度要从根本上杜绝未完全了解问题的根本原因就草率定下解决方案并进行修改。不要在自己不了解原理的情况下去“试一试”，先了解清楚，再使用。
2. 谨慎对待上线的代码，不仅仅要关注功能、异常、规范等情况，还要关全面关注代码实现流程，避免低级错误。