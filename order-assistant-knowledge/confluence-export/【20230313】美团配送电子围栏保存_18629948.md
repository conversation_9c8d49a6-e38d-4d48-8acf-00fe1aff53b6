# 【20230313】美团配送电子围栏保存

1. 第三方配送表增加字段 scope 保存各个配送平台的配送范围。
  1. alter table dscloud.ds_delivery_store add column scope json default null comment '电子围栏数据，用于保存配送范围' ;
2. alter table dscloud.ds_delivery_store add column scope json default null comment '电子围栏数据，用于保存配送范围' ;
3. .NET 接口中台【hems.rider.shop.get】 增加返回字段 scope，返回配送平台的电子围栏 经纬度坐标数组。目前只支持美团配送。
  1. 相关接口文档 ： 美团 （[开发文档 - 美团配送技术服务合作中心 (meituan.com)](https://peisong.meituan.com/tscc/docNew#%E6%9F%A5%E8%AF%A2%E9%97%A8%E5%BA%97%E9%85%8D%E9%80%81%E8%8C%83%E5%9B%B4)）
4. 相关接口文档 ： 美团 （[开发文档 - 美团配送技术服务合作中心 (meituan.com)](https://peisong.meituan.com/tscc/docNew#%E6%9F%A5%E8%AF%A2%E9%97%A8%E5%BA%97%E9%85%8D%E9%80%81%E8%8C%83%E5%9B%B4)）
5. 第三方配送门店同步或者店铺同步时 获取配送门店信息一并返回电子围栏信息