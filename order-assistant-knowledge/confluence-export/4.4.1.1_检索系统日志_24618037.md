# 4.4.1.1> 检索系统日志

****

**参考：**

# 一、找到问题对应的TraceId

例如：研发接收到企微应用号“【线上】告警”，从报警信息里面找到TraceId。

javatrue环境：pro
服务：assist-home
实例：10.100.33.233
TraceId：b08178bf1e0347cfa5ffdaa2587ef7e2.75.17029704849970387
[2023-12-19 15:21:26.134]-租户用户:[500001:4086467633730663258]-[com.yxt.lang.util.ExLogger63]-ERROR [http-nio-8080-exec-10] error#/c/achievement/r/1.0/listSaleDataTrendCategoryAndDateType#http://10.100.33.233:8080/c/achievement/r/1.0/listSaleDataTrendCategoryAndDateType#1136#EACIA#$$当前操作人没有店长角色，不可操作！
com.yxt.lang.exception.YxtRuntimeException: 当前操作人没有店长角色，不可操作！
        at com.yxt.lang.util.Conditions.assertTrue(Conditions.java:264)
        at com.yxt.assist.home.anticorruption.role.adaptor.impl.RoleAdaptorImpl.checkStoreManagerRole(RoleAdaptorImpl.java:92)
        at com.yxt.assist.home.server.achievement.service.impl.AchievementApplicationServiceImpl.listSaleDataTrendCategoryAndDateType(AchievementApplicationServiceImpl.java:62)
        at com.yxt.assist.home.server.achievement.entrance.api.ConsoleAchievementQueryApiImpl.sw$original$listSaleDataTrendCategoryAndDateType$0os26k1(ConsoleAchievementQueryApiImpl.java:36)
        at com.yxt.assist.home.server.achievement.entrance.api.ConsoleAchievementQueryApiImpl.sw$original$listSaleDataTrendCategoryAndDateType$0os26k1$accessor$sw$sfgjku3(ConsoleAchievementQueryApiImpl.java)
        at com.yxt.assist.home.server.achievement.entrance.api.ConsoleAchievementQueryApiImpl$sw$auxiliary$k421940.call(Unknown Source)
        omit...
        at com.yxt.assist.home.server.achievement.entrance.api.ConsoleAchievementQueryApiImpl.listSaleDataTrendCategoryAndDateType(ConsoleAchievementQueryApiImpl.java)
        at com.yxt.starter.filter.WebLogFilter.doFilter(WebLogFilter.java:105)
        at com.yxt.starter.filter.TraceIdFilter.doFilter(TraceIdFilter.java:58)
        at cn.hydee.starter.grey.springboot.web.GreyWebInterceptor.doFilter(GreyWebInterceptor.java:62)
        at com.yxt.starter.filter.assist.OperateContextFilter.doFilter(OperateContextFilter.java:93)
        at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
        at org.apache.tomcat.util.thre

# 二、登录华为云.日志控制台（lts-logs / %To5Ymflh1）

## 2.1 找到“云日志服务LTS”

****

## 2.2 找到日志组“k8s-sk-prod-cluster-app-logs”

****

## 2.3 检索日志

**例如：**输入AppName 、traceID 检索日志。示例查询字符串：AppName : assist-synthesis AND traceID : "**dbf831d9b0304f3bab9c6fd1a3a48599.144.17030614110311405**"