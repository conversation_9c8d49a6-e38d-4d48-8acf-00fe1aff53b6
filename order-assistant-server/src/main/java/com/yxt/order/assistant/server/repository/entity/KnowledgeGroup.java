package com.yxt.order.assistant.server.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("knowledge_group")
public class KnowledgeGroup {


  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 知识库名
   */
  private String name;


  /**
   * 拓展信息，灵活使用，存储JSON
   */
  private String extendJson;

  /**
   * 映射到Dify的知识库配置
   */
  private String mappingDifyConfig;

  /**
   * 状态 NORMAL-正常 DELETED-删除
   */
  private String status;

  /**
   * 知识库更新状态：UPDATING-更新中 SUCCESS-更新成功 FAIL-更新失败
   */
  private String updateStatus;

}
