# 2024-08-20 checkList V2.3 B2C库存占用优化

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| V2.3 B2C库存占用优化 | ``` hydee-business-order-web ``` | 1 | Git项目:hydee-business-order-web分支: feature/order-2399/stockOccupy-V2 |  |  |  | @杨明杰 |  |  |  |
| O2O下账失败批量处理 | ``` hydee-business-order ``` | 2 | ``` feature/ORDER-2361/batch_pick ``` |  |  |  | @杨明杰 |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| ```  ``` LTER TABLE `dscloud`.`commodity_stock`  ADD COLUMN `store_id` bigint NULL COMMENT '线下门店id' AFTER `serial_number`, ALGORITHM=INPLACE ALTER TABLE `dscloud`.`commodity_stock`  MODIFY COLUMN `order_detail_id` bigint(0) NOT NULL COMMENT '商品详情ID' AFTER `online_store_code`; ALTER TABLE `dscloud`.`commodity_stock`  ADD INDEX `idx_create_time`(`create_time`) USING BTREE, ADD INDEX `idx_erp_code`(`erp_code`) USING BTREE; |
| --- |


#### 2.2 appoll配置变更

#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  | }, |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 complete |  |
| dev自测 | 17 complete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |