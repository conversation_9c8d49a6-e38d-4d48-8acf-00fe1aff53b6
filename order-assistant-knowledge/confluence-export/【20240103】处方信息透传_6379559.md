# 【20240103】处方信息透传

- [一、背景](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%B8%80%E3%80%81%E8%83%8C%E6%99%AF)
  - [1.1 业务背景](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.1%E4%B8%9A%E5%8A%A1%E8%83%8C%E6%99%AF)
  - [1.2 痛点分析](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.2%E7%97%9B%E7%82%B9%E5%88%86%E6%9E%90)
  - [1.3 系统现状](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.3%E7%B3%BB%E7%BB%9F%E7%8E%B0%E7%8A%B6)
- [1.1 业务背景](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.1%E4%B8%9A%E5%8A%A1%E8%83%8C%E6%99%AF)
- [1.2 痛点分析](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.2%E7%97%9B%E7%82%B9%E5%88%86%E6%9E%90)
- [1.3 系统现状](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-1.3%E7%B3%BB%E7%BB%9F%E7%8E%B0%E7%8A%B6)
- [二、需求分析](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%BA%8C%E3%80%81%E9%9C%80%E6%B1%82%E5%88%86%E6%9E%90)
  - [2.1 业务流程](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-2.1%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B)
- [2.1 业务流程](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-2.1%E4%B8%9A%E5%8A%A1%E6%B5%81%E7%A8%8B)
- [三、目标](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%B8%89%E3%80%81%E7%9B%AE%E6%A0%87)
- [四、整体设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E5%9B%9B%E3%80%81%E6%95%B4%E4%BD%93%E8%AE%BE%E8%AE%A1)
- [五、详细设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%BA%94%E3%80%81%E8%AF%A6%E7%BB%86%E8%AE%BE%E8%AE%A1)
  - [5.1 详细模块设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.1%E8%AF%A6%E7%BB%86%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1)
  - [5.2 存储数据库设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.2%E5%AD%98%E5%82%A8%E6%95%B0%E6%8D%AE%E5%BA%93%E8%AE%BE%E8%AE%A1)
  - [5.3 接口设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1)
    - [5.3.1 前端交互接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.1%E5%89%8D%E7%AB%AF%E4%BA%A4%E4%BA%92%E6%8E%A5%E5%8F%A3)
    - [5.3.2 后台服务接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.2%E5%90%8E%E5%8F%B0%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3)
  - [5.3.1 前端交互接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.1%E5%89%8D%E7%AB%AF%E4%BA%A4%E4%BA%92%E6%8E%A5%E5%8F%A3)
  - [5.3.2 后台服务接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.2%E5%90%8E%E5%8F%B0%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3)
  - [5.5 安全设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.5%E5%AE%89%E5%85%A8%E8%AE%BE%E8%AE%A1)
  - [5.6 监控报警](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.6%E7%9B%91%E6%8E%A7%E6%8A%A5%E8%AD%A6)
  - [5.3.1 前端交互接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.1%E5%89%8D%E7%AB%AF%E4%BA%A4%E4%BA%92%E6%8E%A5%E5%8F%A3)
  - [5.3.2 后台服务接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.2%E5%90%8E%E5%8F%B0%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3)
- [5.1 详细模块设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.1%E8%AF%A6%E7%BB%86%E6%A8%A1%E5%9D%97%E8%AE%BE%E8%AE%A1)
- [5.2 存储数据库设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.2%E5%AD%98%E5%82%A8%E6%95%B0%E6%8D%AE%E5%BA%93%E8%AE%BE%E8%AE%A1)
- [5.3 接口设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3%E6%8E%A5%E5%8F%A3%E8%AE%BE%E8%AE%A1)
  - [5.3.1 前端交互接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.1%E5%89%8D%E7%AB%AF%E4%BA%A4%E4%BA%92%E6%8E%A5%E5%8F%A3)
  - [5.3.2 后台服务接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.2%E5%90%8E%E5%8F%B0%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3)
- [5.3.1 前端交互接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.1%E5%89%8D%E7%AB%AF%E4%BA%A4%E4%BA%92%E6%8E%A5%E5%8F%A3)
- [5.3.2 后台服务接口](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.3.2%E5%90%8E%E5%8F%B0%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%8F%A3)
- [5.5 安全设计](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.5%E5%AE%89%E5%85%A8%E8%AE%BE%E8%AE%A1)
- [5.6 监控报警](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-5.6%E7%9B%91%E6%8E%A7%E6%8A%A5%E8%AD%A6)
- [六、质量效率](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E5%85%AD%E3%80%81%E8%B4%A8%E9%87%8F%E6%95%88%E7%8E%87)
- [七、里程碑](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%B8%83%E3%80%81%E9%87%8C%E7%A8%8B%E7%A2%91)
- [八、项目排期](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E5%85%AB%E3%80%81%E9%A1%B9%E7%9B%AE%E6%8E%92%E6%9C%9F)
- [九、上线方案](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6372595#id-%E3%80%9020231207%E3%80%91%E5%88%87%E5%BA%97%E8%87%AA%E5%8A%A8%E5%8C%96-%E4%B9%9D%E3%80%81%E4%B8%8A%E7%BA%BF%E6%96%B9%E6%A1%88)


# 一、背景

## 1.1 业务背景

门店许多药品为处方药,购买处方药时需要提供用药人的信息,同时需要根据用药人的信息以及疾病描述来开处方单,门店需要将所有处方单图片下载留存做记录

## 1.2 痛点分析

处方订单无法知道相关用药人的信息，只能预览处方图片得知用药人的信息,处方图片的命名与订单无关,门店下载处方图片时,需要自己重新命名图片,比较麻烦

## 1.3 系统现状

需要门店手动为处方图片重新命名。

# 二、需求分析

## 2.1 业务流程

[https://3a66mj.axshare.com/?id=9eck2u](https://3a66mj.axshare.com/?id=9eck2u)

# 三、目标

**3.1 本期目标**

- 用药人信息：在订单详情页展示处方信息包括用药人姓名、性别、年龄等
- 处方图片命名：处方单图片下载后的名称为：门店编号_姓名_渠道_流水号_yy-mm-dd_4位随机数.jpg/.png


# 四、整体设计

**4.1 统一语言定义**

| **名称** | **说明** |
| --- | --- |
|  |  |


**4.2 流程图**

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

## 5.3 接口设计

### 5.3.1 前端交互接口

### 5.3.2 后台服务接口

**1 查询订单的处方信息**

**接口文档地址: https://open-shangou.meituan.com/home/<USER>/811**

**请求地址:https://waimaiopen.meituan.com/api/v1/gw/order/hospital/rp/used/list**

**系统级参数:**

| API接口 | 接口描述 | 接口描述 |
| --- | --- | --- |
| timestamp | long | 调用接口时的时间戳，即当前时间戳（当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix - timestamp），注意传输时间戳与当前北京时间前后相差不能超过10分钟 |
| app_id | string | 美团分配给APP方的id |
| sig | string | 输入参数计算后的签名结果 |


**应用级参数:**

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| order_id | long | 是 | 27061900338318741 | 订单号（同订单展示ID），商家可根据订单号查询订单当前的处方信息。 |


**返回结果:**

| 参数名 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| code | int | 0 | 0:成功 |
| msg | string |  | 描述信息 |
| data | list<object> |  | 处方信息 |
| rpId | long |  | 处方id |
| medicalUserName | string |  | 用药人姓名 |
| gender | int |  | 用药人性别，1, "男"；2, "女" |
| birthday | string | 2017-11-07 | 用药人出生日期 |
| hospitalName | string |  | 开方医院名称 |
| doctorName | string |  | 医生姓名 |
| createTime | string | 2022-08-08 11:32:27 | 处方开具时间 |
| icdName | string | 心脏病,高血压 | 诊断 |
| rpPicUrl | string |  | 处方图片 |


**2 订单关联处方笺查询**

**接口文档地址:https://open-retail.ele.me/#/apidoc/me.ele.retail:drug.trade.prescriptiondetail.query-3?aopApiCategory=drug_all&type=api_menu**

**请求地址: me.ele.retail:drug.trade.prescriptiondetail.query-3**

**系统级参数:**

| 名称 | 类型 | 是否必须 | 描述 |
| --- | --- | --- | --- |
| cmd | String | 是 | 接口cmd |
| version | String | 是 | 版本,默认3 |
| timestamp | Long | 是 | 时间戳 |
| ticket | String | 是 | 请求流水号 |
| source | String | 是 | 填写APPID |
| sign | String | 是 | 签名,md5 |
| encrypt | String | 否 | 是否加密,如AES,默认为空 |
| access_token | String | 否 | 访问令牌,访问用户隐私数据时的唯一权限标识。 如果API不需要授权则可以不带入此参数 |


**应用级参数:**

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| body | message:drug.trade.prescriptiondetail.query.req | 是 | 请求消息体 | {} |
| orderId | Long | 是 | 订单id | 4063060106183635700 |


**返回结果:**

| 名称 | 类型 | 描述 | 示例值 |
| --- | --- | --- | --- |
| error | String | 返回错误信息 | sucess |
| errno | String | 返回错误码 | 0 |
| data | message:drug.trade.prescriptiondetail.query.apiResult | 返回信息 | {"prescriptionDTOList":[{"alscOrderId":4063060106183635700,"createdAt":"2023-04-03 16:42:25","doctorName":"郭为","eleUserId":**********,"hospitalName":"莲藕医院","icd":"眩晕","patientBirthday":"1986-12-03","patientGender":2,"patientName":"韩菲菲","prescriptionFileUrl":"https://bwm-newretail-durgrx-zbprod-zb1-oss-1.cn-zhangjiakou.oss.aliyuncs.com/prescription/202304/fa8f3b32-d253-4838-b32c-4e89d671b69799.pdf?Expires=**********&OSSAccessKeyId=LTAI04pMyCgH2Tt9&Signature=HFyHdYjFT4Gw9NDZLlSamdwp%2BAo%3D","prescriptionId":300000000076662658}]} |
| prescriptionDTOList | message:drug.trade.prescriptiondetail.query.PrescriptionDetailOpenApiQueryDTO[] | 处方明细 | {} |
| patientName | String | 患者姓名 | 韩菲菲 |
| patientGender | Integer | 患者性别 1=男，2=女 | 1 |
| patientBirthday | String | 患者生日 | 1996-12-03 |
| prescriptionFileUrl | String | 处方文件url | [https://bwm-newretail-durgrx-zbprod-zb1-oss-1.cn-zhangjiakou.oss.aliyuncs.com/prescription/202304/fa8f3b32-d253-4838-b32c-4e89d671b69799.pdf?Expires=**********&OSSAccessKeyId=LTAI04pMyCgH2Tt9&Signature=HFyHdYjFT4Gw9NDZLlSamdwp%2BAo%3D](https://bwm-newretail-durgrx-zbprod-zb1-oss-1.cn-zhangjiakou.oss.aliyuncs.com/prescription/202304/fa8f3b32-d253-4838-b32c-4e89d671b69799.pdf?Expires=**********&OSSAccessKeyId=LTAI04pMyCgH2Tt9&Signature=HFyHdYjFT4Gw9NDZLlSamdwp%2BAo%3D) |
| hospitalName | String | 医院名称 | 莲藕医院 |
| doctorName | String | 医生 | 郭为 |
| createdAt | String | 创建时间，时间格式：yyyy-mm-dd HH:mm:ss | 2023-04-03 16:42:25 |
| icd | String | 疾病词 | 眩晕 |
| prescriptionId | Long | 处方笺ID | 3000000000766626583 |


## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年1月3日**

**研发时间：2024年1月3日-2024年1月5日（含研发自测）；测试时间：2024年1月8日-2024年1月9日；上线时间：2024年1月9日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 处方用药人信息透传 | 美团查询处方信息接口 | .net |  |  |  |  |  |
| 饿了么订单关联处方笺查询接口 |  |  |  |
| 京东到处方订单信息查询接口 |  |  |  |
| 微商城处方信息封装 |  |  |  |
| 下载处方图片 | 重新命名处方图片名称 | business-order |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等