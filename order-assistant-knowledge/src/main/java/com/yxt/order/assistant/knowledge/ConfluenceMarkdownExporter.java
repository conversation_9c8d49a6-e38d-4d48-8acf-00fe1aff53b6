package com.yxt.order.assistant.knowledge;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yxt.order.assistant.knowledge.model.ConfluenceChildrenResponse;
import com.yxt.order.assistant.knowledge.model.ConfluencePage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class ConfluenceMarkdownExporter {

  private final String TITLE_EMPTY = "";
  private final String BODY_EMPTY = "";

  private final OkHttpClient client;
  private final Gson gson;
  private final String confluenceUrl;
  private final String personalToken;
  private final String outputDir;
  private final String resourcesDir;

  public static void main(String[] args) {
    new ConfluenceMarkdownExporter("https://yxtcf.hxyxt.com",
        "NDUxMjY5MDIyMDcwOigCdFHosMopCmWokCv3Cz+4jQOV")
//        .exportPageAndChildrenToMarkdown("73450218");  // 某一个目录
        .exportPageAndChildrenToMarkdown("6370644"); // 交易生产组
  }

  public ConfluenceMarkdownExporter(@Value("${confluence.url}") String confluenceUrl,
      @Value("${confluence.token}") String personalToken) {
    this.confluenceUrl = confluenceUrl;
    this.personalToken = personalToken;
    this.outputDir = "confluence-export";
    this.resourcesDir = outputDir + File.separator + "resources";

    // 配置OkHttp客户端
    this.client = new OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build();

    // 配置Gson
    this.gson = new GsonBuilder().setPrettyPrinting().create();

    // 创建输出目录
    createOutputDirectories();
  }

  /**
   * 创建输出目录
   */
  private void createOutputDirectories() {
    File dir = new File(outputDir);
    if (!dir.exists()) {
      dir.mkdirs();
    }

    File resourceDir = new File(resourcesDir);
    if (!resourceDir.exists()) {
      resourceDir.mkdirs();
    }
  }

  /**
   * 导出页面及其所有子页面到Markdown 使用广度优先遍历避免递归调用栈溢出
   */
  public void exportPageAndChildrenToMarkdown(String rootPageId) {
    Queue<String> pageQueue = new LinkedList<>();
    Set<String> processedPages = new HashSet<>();
    List<String> exportedFiles = new ArrayList<>();

    pageQueue.offer(rootPageId);

    System.out.println("🚀 开始导出Confluence页面，根页面ID: " + rootPageId);

    while (!pageQueue.isEmpty()) {
      String pageId = pageQueue.poll();

      if (processedPages.contains(pageId)) {
        continue;
      }

      processedPages.add(pageId);

      try {
        // 导出当前页面
        String exportedFile = exportPageToMarkdown(pageId);
        if (exportedFile != null) {
          exportedFiles.add(exportedFile);
        }

        // 获取子页面并加入队列
        List<String> childPageIds = getChildPageIds(pageId);
        pageQueue.addAll(childPageIds);

      } catch (Exception e) {
        System.err.println("❌ 处理页面失败 " + pageId + ": " + e.getMessage());
        e.printStackTrace();
      }
    }

    System.out.println("✅ 导出完成！共导出 " + exportedFiles.size() + " 个页面");
    System.out.println("📁 输出目录: " + new File(outputDir).getAbsolutePath());

    // 生成索引文件
    generateIndexFile(exportedFiles);
  }

  /**
   * 获取子页面ID列表
   */
  private List<String> getChildPageIds(String pageId) {
    List<String> childPageIds = new ArrayList<>();
    String childrenUrl = confluenceUrl + "/rest/api/content/" + pageId + "/child/page?limit=100";

    Request request = new Request.Builder().url(childrenUrl)
        .addHeader("Authorization", "Bearer " + personalToken)
        .addHeader("Content-Type", "application/json").build();

    try (Response response = client.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        System.err.println(
            "❌ 获取子页面失败 " + pageId + ": " + response.code() + " " + response.message());
        return childPageIds;
      }

      String json = response.body().string();
      ConfluenceChildrenResponse childrenResponse = gson.fromJson(json,
          ConfluenceChildrenResponse.class);

      if (childrenResponse.getResults() != null) {
        for (ConfluenceChildrenResponse.ConfluencePageSummary page : childrenResponse.getResults()) {
          if ("page".equals(page.getType()) && "current".equals(page.getStatus())) {
            String title = page.getTitle();
            if("归档文档回收".equals(title)
                || "4.6.2 团队周会".equals(title)
                || "4.4 研发全流程".equals(title)){
              continue;
            }


            childPageIds.add(page.getId());
            System.out.println("📄 发现子页面: " + title + " (ID: " + page.getId() + ")");
          }
        }
      }

    } catch (Exception e) {
      System.err.println("❌ 解析子页面响应失败 " + pageId + ": " + e.getMessage());
      e.printStackTrace();
    }

    return childPageIds;
  }

  /**
   * 导出单个页面到Markdown
   */
  public String exportPageToMarkdown(String pageId) {
    String url = confluenceUrl + "/rest/api/content/" + pageId + "?expand=body.storage";
    Request request = new Request.Builder().url(url)
        .addHeader("Authorization", "Bearer " + personalToken)
        .addHeader("Content-Type", "application/json").build();

    try (Response response = client.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        System.err.println(
            "❌ 获取页面内容失败 " + pageId + ": " + response.code() + " " + response.message());
        return null;
      }

      String json = response.body().string();
      ConfluencePage page = gson.fromJson(json, ConfluencePage.class);

      if (page == null || page.getBody() == null || page.getBody().getStorage() == null) {
        System.err.println("❌ 页面内容为空 " + pageId);
        return null;
      }

      String html = page.getBody().getStorage().getValue();
      String title = page.getTitle();

      System.out.println("📝 正在处理页面: " + title + " (ID: " + pageId + ")");

      // 设置当前页面ID供draw.io处理使用
      this.currentPageId = pageId;

      String lowerCaseTitle = title.toLowerCase();
      if (lowerCaseTitle.contains("checklist") || lowerCaseTitle.contains("上线清单模板")
          || lowerCaseTitle.contains("技术方案模板")

      ) {
        System.err.println("【人工干预】不处理");
        return null;
      }

      String markdown = convertHtmlToMarkdown(html, title);
      if (StringUtils.isEmpty(markdown)) {
        System.err.println("【人工干预】不处理");
        return null;
      }

      // 生成安全的文件名
      String fileName = sanitizeFileName(title) + "_" + pageId + ".md";
      String filePath = outputDir + File.separator + fileName;

      // 写入文件（使用UTF-8编码）
      try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(filePath),
          StandardCharsets.UTF_8)) {
        writer.write(markdown);
        System.out.println("✅ 导出完成: " + fileName);
        return fileName;
      }

    } catch (Exception e) {
      System.err.println("❌ 导出页面失败 " + pageId + ": " + e.getMessage());
      e.printStackTrace();
    }

    return null;
  }

  /**
   * 将HTML转换为Markdown
   */
  private String convertHtmlToMarkdown(String html, String title) {
    if (html == null || html.trim().isEmpty()) {
//      return "# " + title + "\n\n*此页面内容为空*\n";
      return TITLE_EMPTY;
    }

    Document doc = Jsoup.parse(html);
    StringBuilder markdown = new StringBuilder();

    // 添加页面标题
    markdown.append("# ").append(title).append("\n\n");

    // 转换body内容
    StringBuilder bodyContent = new StringBuilder();
    if (doc.body() != null) {
      for (Element element : doc.body().children()) {
        String converted = convertElementToMarkdown(element, 0);
        if (!converted.trim().isEmpty()) {
          bodyContent.append(converted).append("\n\n");
//          markdown.append(converted).append("\n\n");
        }
      }
    }
    if (StringUtils.isEmpty(bodyContent.toString())) {
      return BODY_EMPTY;
    }
    markdown.append(bodyContent);
    String result = markdown.toString().trim();

    // 处理draw.io图表标识符
    result = processDrawioMacros(result, title);

    return result;
  }

  /**
   * 递归转换HTML元素到Markdown
   */
  private String convertElementToMarkdown(Element element, int depth) {
    if (element == null) {
      return "";
    }

    String tagName = element.tagName().toLowerCase();
    StringBuilder result = new StringBuilder();

    switch (tagName) {
      case "h1":
        return "# " + element.text();
      case "h2":
        return "## " + element.text();
      case "h3":
        return "### " + element.text();
      case "h4":
        return "#### " + element.text();
      case "h5":
        return "##### " + element.text();
      case "h6":
        return "###### " + element.text();

      case "p":
        return processInlineElements(element);

      case "br":
        return "\n";

      case "strong":
      case "b":
        return "**" + element.text() + "**";

      case "em":
      case "i":
        return "*" + element.text() + "*";

      case "code":
        return "`" + element.text() + "`";

      case "pre":
        Elements codeElements = element.select("code");
        if (!codeElements.isEmpty()) {
          return "```\n" + codeElements.first().text() + "\n```";
        } else {
          return "```\n" + element.text() + "\n```";
        }

      case "blockquote":
        String[] lines = element.text().split("\n");
        StringBuilder quote = new StringBuilder();
        for (String line : lines) {
          quote.append("> ").append(line).append("\n");
        }
        return quote.toString().trim();

      case "ul":
        return convertList(element, false, depth);

      case "ol":
        return convertList(element, true, depth);

      case "li":
        // 这个会在convertList中处理
        return processInlineElements(element);

      case "a":
        String href = element.attr("href");
        String text = element.text();
        if (href.isEmpty()) {
          return text;
        }

        // 检查是否是附件链接
        if (isAttachmentLink(href)) {
          String localAttachmentPath = downloadResource(href, "attachment");
          if (localAttachmentPath != null) {
            return "[" + text + "](" + localAttachmentPath + ")";
          }
        }

        // 处理相对链接
        if (href.startsWith("/")) {
          href = confluenceUrl + href;
        }
        return "[" + text + "](" + href + ")";

      case "img":
        String src = element.attr("src");
        String alt = element.attr("alt");
        if (!src.isEmpty()) {
          String localImagePath = downloadResource(src, "image");
          if (localImagePath != null) {
            return "![" + alt + "](" + localImagePath + ")";
          }
        }
        // 如果下载失败，使用原始链接
        if (src.startsWith("/")) {
          src = confluenceUrl + src;
        }
        return "![" + alt + "](" + src + ")";

      case "table":
        return convertTable(element);

      case "hr":
        return "---";

      default:
        // 对于未知标签，处理其子元素
        return processInlineElements(element);
    }
  }

  /**
   * 处理内联元素
   */
  private String processInlineElements(Element element) {
    StringBuilder result = new StringBuilder();

    for (Node node : element.childNodes()) {
      if (node instanceof TextNode) {
        result.append(((TextNode) node).text());
      } else if (node instanceof Element) {
        Element childElement = (Element) node;
        result.append(convertElementToMarkdown(childElement, 0));
      }
    }

    return result.toString();
  }

  /**
   * 转换列表
   */
  private String convertList(Element listElement, boolean ordered, int depth) {
    StringBuilder result = new StringBuilder();
    String indent = repeatString("  ", depth);

    Elements listItems = listElement.children().select("li");
    int counter = 1;

    for (Element li : listItems) {
      if (ordered) {
        result.append(indent).append(counter++).append(". ");
      } else {
        result.append(indent).append("- ");
      }

      // 处理嵌套列表
      Elements nestedLists = li.children().select("ul, ol");
      if (!nestedLists.isEmpty()) {
        // 先处理文本内容
        Element liCopy = li.clone();
        liCopy.select("ul, ol").remove();
        String text = processInlineElements(liCopy).trim();
        if (!text.isEmpty()) {
          result.append(text).append("\n");
        }

        // 然后处理嵌套列表
        for (Element nestedList : nestedLists) {
          boolean isOrdered = "ol".equals(nestedList.tagName());
          result.append(convertList(nestedList, isOrdered, depth + 1));
        }
      } else {
        result.append(processInlineElements(li).trim()).append("\n");
      }
    }

    return result.toString();
  }

  /**
   * 转换表格
   */
  private String convertTable(Element table) {
    StringBuilder result = new StringBuilder();
    Elements rows = table.select("tr");

    if (rows.isEmpty()) {
      return "";
    }

    boolean hasHeader = false;

    for (int i = 0; i < rows.size(); i++) {
      Element row = rows.get(i);
      Elements cells = row.select("th, td");

      if (cells.isEmpty()) {
        continue;
      }

      // 检查是否是表头
      if (i == 0 && !row.select("th").isEmpty()) {
        hasHeader = true;
      }

      result.append("|");
      for (Element cell : cells) {
        String cellText = processInlineElements(cell).trim().replace("\n", " ");
        result.append(" ").append(cellText).append(" |");
      }
      result.append("\n");

      // 添加表头分隔符
      if (hasHeader && i == 0) {
        result.append("|");
        for (int j = 0; j < cells.size(); j++) {
          result.append(" --- |");
        }
        result.append("\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理draw.io图表宏标识符
   */
  private String processDrawioMacros(String content, String pageTitle) {
    // 匹配draw.io宏的模式：true[图表名]false[其他参数]true[数字ID]
    // 实际模式类似：truejmanus-statusfalseautotoptrue7811
    // 匹配draw.io宏的模式：true[图表名]false[参数]true[数字ID]
    Pattern drawioPattern = Pattern.compile("true(.*?)false.*?true(\\d+)");
    Matcher matcher = drawioPattern.matcher(content);

    StringBuffer result = new StringBuffer();
    int matchCount = 0;

    while (matcher.find()) {
      matchCount++;
      String diagramName = matcher.group(1);
      String diagramId = matcher.group(2);

      System.out.println("🎨 发现draw.io图表: " + diagramName + " (ID: " + diagramId + ")");

      // 尝试下载draw.io图表
      String localImagePath = downloadDrawioDiagram(diagramName, diagramId, pageTitle);
      String replacement;
      if (localImagePath != null) {
        replacement = "\n\n![" + diagramName + "](" + localImagePath + ")\n\n";
      } else {
        replacement = "\n\n*[Draw.io图表: " + diagramName + " - ID: " + diagramId + "]*\n\n";
      }

      matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
    }
    matcher.appendTail(result);

    if (matchCount > 0) {
      System.out.println("📊 总共处理了 " + matchCount + " 个draw.io图表");
    } else {
      System.out.println("❌ 没有找到draw.io图表模式");
    }

    return result.toString();
  }

  /**
   * 下载draw.io图表
   */
  private String downloadDrawioDiagram(String diagramName, String diagramId, String pageTitle) {
    try {
      System.out.println("📥 正在尝试下载draw.io图表: " + diagramName + " (ID: " + diagramId + ")");

      String pageId = getCurrentPageId();
      if (pageId == null) {
        System.err.println("❌ 无法获取页面ID");
        return null;
      }

      // 尝试多种可能的draw.io导出URL格式
      String[] urlPatterns = {
          // draw.io插件的标准导出API
          "/rest/drawio/1.0/diagram/crud/IMAGE/" + pageId + "/" + diagramId,
          "/rest/drawio/1.0/diagram/crud/PNG/" + pageId + "/" + diagramId,
          "/rest/drawio/1.0/diagram/crud/SVG/" + pageId + "/" + diagramId,

          // 作为附件的可能路径
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".png",
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".svg",
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".drawio.png",

          // 通过附件API查找
          "/rest/api/content/" + pageId + "/child/attachment/" + diagramId,};

      for (String urlPattern : urlPatterns) {
        String fullUrl = confluenceUrl + urlPattern;
        System.out.println("🔍 尝试URL: " + urlPattern);

        String localPath = downloadResource(fullUrl, "drawio");
        if (localPath != null) {
          System.out.println("✅ 成功下载draw.io图表: " + diagramName);
          return localPath;
        }
      }

      // 如果直接下载失败，尝试通过页面附件列表查找
      String attachmentPath = findDrawioInAttachments(pageId, diagramName, diagramId);
      if (attachmentPath != null) {
        return attachmentPath;
      }

      System.err.println("❌ 无法下载draw.io图表: " + diagramName);
      return null;

    } catch (Exception e) {
      System.err.println("❌ 下载draw.io图表异常: " + diagramName + " - " + e.getMessage());
      return null;
    }
  }

  /**
   * 在页面附件中查找draw.io图表
   */
  private String findDrawioInAttachments(String pageId, String diagramName, String diagramId) {
    try {
      String attachmentsUrl = confluenceUrl + "/rest/api/content/" + pageId + "/child/attachment";

      Request request = new Request.Builder().url(attachmentsUrl)
          .addHeader("Authorization", "Bearer " + personalToken)
          .addHeader("Content-Type", "application/json").build();

      try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          return null;
        }

        String json = response.body().string();
        // 这里可以解析附件列表，查找相关的draw.io文件
        // 由于结构复杂，暂时返回null，后续可以完善
        System.out.println("📋 获取到附件列表，但暂未实现解析逻辑");

        return null;
      }
    } catch (Exception e) {
      System.err.println("❌ 查找附件异常: " + e.getMessage());
      return null;
    }
  }

  // 临时存储当前处理的页面ID
  private String currentPageId;

  private String getCurrentPageId() {
    return currentPageId;
  }

  /**
   * 判断是否是附件链接
   */
  private boolean isAttachmentLink(String href) {
    if (href == null) {
      return false;
    }

    // Confluence附件链接通常包含这些路径
    return href.contains("/download/attachments/") || href.contains("/download/thumbnails/")
        || href.contains("/download/") ||
        // 检查常见的文件扩展名
        href.matches(
            ".*\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|tar|gz|txt|csv|json|xml)($|\\?.*)");
  }

  /**
   * 下载资源文件
   */
  private String downloadResource(String resourceUrl, String resourceType) {
    try {
      // 处理相对路径
      if (resourceUrl.startsWith("/")) {
        resourceUrl = confluenceUrl + resourceUrl;
      }

      // 如果不是完整URL，跳过
      if (!resourceUrl.startsWith("http")) {
        return null;
      }

      System.out.println("📥 正在下载资源: " + resourceUrl);

      Request request = new Request.Builder().url(resourceUrl)
          .addHeader("Authorization", "Bearer " + personalToken).build();

      try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          System.err.println("❌ 下载资源失败: " + resourceUrl + " - " + response.code());
          return null;
        }

        // 获取文件扩展名
        String fileName = extractFileNameFromUrl(resourceUrl);
        if (fileName == null) {
          // 根据Content-Type生成文件名
          String contentType = response.header("Content-Type");
          fileName = generateFileName(resourceType, contentType);
        }

        // 确保文件名唯一
        fileName = ensureUniqueFileName(fileName);

        String localPath = resourcesDir + File.separator + fileName;

        // 下载文件
        try (FileOutputStream fos = new FileOutputStream(
            localPath); InputStream is = response.body().byteStream()) {

          byte[] buffer = new byte[8192];
          int bytesRead;
          while ((bytesRead = is.read(buffer)) != -1) {
            fos.write(buffer, 0, bytesRead);
          }
        }

        System.out.println("✅ 资源下载完成: " + fileName);

        // 返回相对路径
        return "./resources/" + fileName;

      }
    } catch (Exception e) {
      System.err.println("❌ 下载资源异常: " + resourceUrl + " - " + e.getMessage());
      return null;
    }
  }

  /**
   * 从URL中提取文件名
   */
  private String extractFileNameFromUrl(String url) {
    try {
      // 移除查询参数
      int queryIndex = url.indexOf('?');
      if (queryIndex != -1) {
        url = url.substring(0, queryIndex);
      }

      // 获取最后一个/后的内容
      int lastSlash = url.lastIndexOf('/');
      if (lastSlash != -1 && lastSlash < url.length() - 1) {
        String fileName = url.substring(lastSlash + 1);
        // 检查是否有有效的文件扩展名
        if (fileName.contains(".") && fileName.lastIndexOf('.') < fileName.length() - 1) {
          return sanitizeFileName(fileName);
        }
      }
    } catch (Exception e) {
      // 忽略异常，返回null
    }
    return null;
  }

  /**
   * 根据资源类型和Content-Type生成文件名
   */
  private String generateFileName(String resourceType, String contentType) {
    String extension = ".bin"; // 默认扩展名

    if (contentType != null) {
      contentType = contentType.toLowerCase();
      if (contentType.contains("image/jpeg") || contentType.contains("image/jpg")) {
        extension = ".jpg";
      } else if (contentType.contains("image/png")) {
        extension = ".png";
      } else if (contentType.contains("image/gif")) {
        extension = ".gif";
      } else if (contentType.contains("image/svg")) {
        extension = ".svg";
      } else if (contentType.contains("image/webp")) {
        extension = ".webp";
      } else if (contentType.contains("application/pdf")) {
        extension = ".pdf";
      } else if (contentType.contains("text/plain")) {
        extension = ".txt";
      } else if (contentType.contains("application/json")) {
        extension = ".json";
      }
    }

    return resourceType + "_" + System.currentTimeMillis() + extension;
  }

  /**
   * 确保文件名唯一
   */
  private String ensureUniqueFileName(String fileName) {
    File file = new File(resourcesDir, fileName);
    if (!file.exists()) {
      return fileName;
    }

    // 如果文件已存在，添加数字后缀
    String nameWithoutExt = fileName;
    String extension = "";

    int lastDot = fileName.lastIndexOf('.');
    if (lastDot != -1) {
      nameWithoutExt = fileName.substring(0, lastDot);
      extension = fileName.substring(lastDot);
    }

    int counter = 1;
    while (file.exists()) {
      fileName = nameWithoutExt + "_" + counter + extension;
      file = new File(resourcesDir, fileName);
      counter++;
    }

    return fileName;
  }

  /**
   * 重复字符串（Java 8兼容）
   */
  private String repeatString(String str, int count) {
    if (count <= 0) {
      return "";
    }
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < count; i++) {
      sb.append(str);
    }
    return sb.toString();
  }

  /**
   * 生成安全的文件名
   */
  private String sanitizeFileName(String fileName) {
    if (fileName == null || fileName.trim().isEmpty()) {
      return "untitled";
    }

    // 移除或替换不安全的字符
    return fileName.trim().replaceAll("[\\\\/:*?\"<>|]", "_").replaceAll("\\s+", "_")
        .replaceAll("_{2,}", "_").replaceAll("^_|_$", "");
  }

  /**
   * 生成索引文件
   */
  private void generateIndexFile(List<String> exportedFiles) {
    if (exportedFiles.isEmpty()) {
      return;
    }

    try {
      String indexPath = outputDir + File.separator + "README.md";
      try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(indexPath),
          StandardCharsets.UTF_8)) {
        writer.write("# Confluence 导出索引\n\n");
        writer.write("导出时间: " + new Date() + "\n\n");
        writer.write("## 导出的页面\n\n");

        for (String fileName : exportedFiles) {
          String displayName = fileName.replace(".md", "").replaceAll("_\\d+$", "");
          writer.write("- [" + displayName + "](./" + fileName + ")\n");
        }

        System.out.println("📋 生成索引文件: README.md");
      }
    } catch (IOException e) {
      System.err.println("❌ 生成索引文件失败: " + e.getMessage());
    }
  }
}
