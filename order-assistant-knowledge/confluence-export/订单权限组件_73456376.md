# 订单权限组件

| 版本 | 说明 | 日期(倒序) |  |
| --- | --- | --- | --- |
| 1.31.0-RELEASE | 支持动态传入userid | 20250626 |  |
| 1.30.0-RELEASE | 支持门店和公司权限 | 20250424 |  |


### 场景入参:

- 门店
- 公司


### 目前权限组件支持的场景

|  | 无输入 | 仅输入公司 | 仅输入门店 | 同时输入公司和门店 |
| --- | --- | --- | --- | --- |
| 配置了公司权限 | 按配置公司查 | 按公司校验结果查 | 按门店校验结果查 | 按公司校验结果&门店校验结果查询 |
| 没有配置公司权限 | 按配置门店查（有大IN风险） | 按门店校验结果查询 |


测试Case:

### 依赖

    <dependency>
      <groupId>com.yxt.permission</groupId>
      <artifactId>order-permission</artifactId>
      <version>${latestVersion}</version>
    </dependency>

### 使用

@EnableFeignClients(basePackages = {"com.yxt.permission"})
@ComponentScan(basePackages = {"com.yxt.permission"})

### @YxtOrderPermission注解

package com.yxt.permission;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 订单权限校验注解 支持同时校验多种类型的权限
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface YxtOrderPermission {

  /**
   * 权限校验配置 可以配置多个校验规则
   */
  PermissionCheck[] value();

  /**
   * 用户ID字段名称,如果当前字段传值了， 不需要通过http header 获取 userId
   */
  String userIdField() default "";

  /**
   * 单个权限校验配置
   */
  @interface PermissionCheck {

    /**
     * 校验类型
     */
    ValidType validType();

    /**
     * SpEL表达式，用于获取和设置请求参数中的具体字段
     */
    String field();
  }

  /**
   * 校验类型枚举
   */
  enum ValidType {
    /**
     * 门店编码
     */
    STORE_CODE,
    /**
     * 公司编码，将调用queryUserAllCompany接口
     */
    COMPANY_CODE
  }
}

### 使用示例

  @Override   
@YxtOrderPermission(value = {
        @YxtOrderPermission.PermissionCheck(validType = ValidType.COMPANY_CODE, field = "#req.companyCodeList"),
        @YxtOrderPermission.PermissionCheck(validType = ValidType.STORE_CODE, field = "#req.storeCodeList")
    }, userIdField = "#req.currentUserId")  
 public ResponseBase<PageDTO<IOfflineOrderRes>> offlineOrderList(IOfflineOrderListReq req) {
    return generateSuccess(null);
  }