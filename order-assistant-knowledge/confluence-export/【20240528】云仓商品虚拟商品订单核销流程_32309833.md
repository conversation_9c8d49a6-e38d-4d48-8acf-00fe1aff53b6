# 【20240528】云仓商品虚拟商品订单核销流程

# 一、背景

## 1.1 业务背景

在积分商城中有部分商品是有合作商家提供服务或代金券的形式进行兑换，此类商品需要通过核销步骤进行控制，目前采用云仓商品发货的物流状态进行控制，低效且无法一次购买多份控制核销

## 1.2 痛点分析

当前雨诺积分商城预计2024年5月底停止运行，需要通过现有一心到家商城兑换商城替代，其中需要补充虚拟商品订单兑换的核心能力，由于当前积分兑换的虚拟商品需要通过云仓商品上架到微商城，保证虚拟商品兑换的通用性，需要在云仓商品中增加虚拟商品类型及一心到家商城的虚拟商品兑换订单流程。

## 1.3 系统现状

现有一心到家商城兑换商城暂无虚拟商品并核销虚拟商品的能力

# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 订单提交页面交互调整(纯虚拟商品提交订单后状态直接扭转为已发货);
2. 虚拟商品订单状态需要与现有B2C订单状态映射兼容;
3. 云仓订单、积分兑换订单、内购订单均支持虚拟商品类型订单；
4. 增加H5虚拟商品订单核销功能.


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

**1.小程序虚拟商品下单**

**true虚拟商品下单流程falseautotoptrue115714true虚拟商品下单支付回调falseautotoptrue8812**

**2.核销流程**

**true核销falseautotoptrue14615**

**3.撤销流程**

**true撤销核销流程falseautotoptrue7413**

**4.二维码生成**

**true二维码生成falseautotoptrue3714**

**5.申请退款**

**true未命名绘图falseautotoptrue3711**

# 五、详细设计

## 5.1 详细模块设计

## 5.3 接口设计

### 5.3.1 前后端交互

**1.**

**https://dev-merchants.hxyxt.com/businesses-gateway/mer-manager/1.0/order-info**

**2.B端商城订单新订单类型查询--虚拟商品订单**

**https://dev-merchants.hxyxt.com/businesses-gateway/mer-manager/1.0/order-info/initOrderQuery**

**3.C端确认订单页面 POST //businesses-gateway/customer/1.0/order/orderConfirmInit //businesses-gateway/customer/1.0/order/addOrder**

**4.C端订单详细 POST //businesses-gateway/customer/1.0/order/encrypt/get**

**5.核销记录**

****

**订单核销 http://hydee-business-order-web.svc.k8s.dev.hxyxt.com/doc.html#/default/%E8%AE%A2%E5%8D%95%E6%A0%B8%E9%94%80/queryVerificationRecordListUsingPOST**

分页查询：[https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0](https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0)/verifyRecord/pageList

订单导出：[https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0](https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0)/export/verify_record/action
订单核销：[https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0](https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0)/verify
撤销：[https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0](https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0)/repeal
撤销详情：[https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0](https://dev-sp.hxyxt.com/businesses-gateway/b2c/1.0)/repeal_detail

**6.C端H5订单核销**

**http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E8%AE%A2%E5%8D%95%E6%A0%B8%E9%94%80%E7%AE%A1%E7%90%86/queryRecordByCustomerUsingGET**

1. 获取二维码链接:/1.0/verify_order/query_customer
2. 获取二维码扫码结果:/1.0/verify_order/getCodeResult
3. 查询核销记录:/1.0/verify_order/query_customer
4. 订单核销:/1.0/verify_order/verify
5. 登录:/1.0/verify_order/_login


## 5.4 涉及数据库

| 库 | 表 | SQL |
| --- | --- | --- |
| middle_order | order_detail | ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `verification_quantity` int(0) NULL DEFAULT 0 COMMENT '虚拟商品待核销数量' AFTER `promotion_ratio`;  ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `is_virtual` tinyint(0) NULL DEFAULT 0 COMMENT '发货类型(0：实物发货，1：虚拟核销)' AFTER `verification_quantity`; |
| middle_order | order_info | ALTER TABLE `middle_order`.`order_info`  ADD COLUMN `is_virtual_goods_order` tinyint(255) NULL DEFAULT 0 COMMENT '是否虚拟订单，0：否，1：是' AFTER `emp_code`; |
| dscloud | verification_record | CREATE TABLE verification_record(  id BIGINT NOT NULL COMMENT 'ID' ,  order_id BIGINT NOT NULL COMMENT 'middler库order_info id order_id' ,  order_detail_id VARCHAR(32) NOT NULL COMMENT 'middler库order_detail_id' ,  commodity_code VARCHAR(32) NOT NULL COMMENT '商品编码' ,  commodity_name VARCHAR(128) NOT NULL COMMENT '商品名称' ,  status tinyint NOT NULL COMMENT '核销状态(1.已核销2.撤销)' ,  verification_time DATETIME NOT NULL COMMENT '核销时间' ,  verification_quantity INT NOT NULL COMMENT '核销/撤销数量' ,  verification_by VARCHAR(128) NOT NULL COMMENT '核销人' ,  residual_quantity INT NOT NULL COMMENT '剩余核销数量' ,  remark VARCHAR(64) NOT NULL COMMENT '核销备注' ,  sp_code VARCHAR(128) NOT NULL COMMENT '供应商编码' ,  isvalid tinyint NOT NULL DEFAULT 1 COMMENT 'isvalid' ,  created_by VARCHAR(64) NOT NULL COMMENT '创建人' ,  created_time DATETIME NOT NULL COMMENT '创建时间' ,  updated_by VARCHAR(64) COMMENT '更新人' ,  updated_time DATETIME COMMENT '更新时间' ,  version INT NOT NULL DEFAULT 1 COMMENT '数据版本，每次update+1' ,  PRIMARY KEY (id),  KEY `idx_order_id` (`order_id`) USING BTREE,  KEY `idx_status` (`status`) USING BTREE,  KEY `idx_commodity_code` (`commodity_code`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |


## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.7 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年5月30日;**

**研发时间：2024年6月3日-2024年5月22日；**

**联调时间：2024年5月22日-2024年5月24日(含自测)；**

**测试时间：2024年4月24日(提测)；**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等