# AAA 首选项!!--线上问题排查路径

#### 快捷导航

企微线上告警

[Skywalking](https://yxtcf.hxyxt.com/skywalking.hxyxt.com/General-Service/Services)

[华为云日志](https://console.huaweicloud.com/lts/?region=cn-southwest-2&locale=zh-cn#/cts/logEventsLeftMenu/events?groupId=f68c7c65-2699-4b31-a09b-e4afde9674fa&topicId=100967b3-9a32-47c4-9495-38d6b149514f&epsId=33757bf3-64ff-472e-84dc-4e28f300acff)

[华为云 rocketmq](https://console.huaweicloud.com/dms/?engine=reliability&agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/queue/instanceDetail/baseInfo?instanceId=583757bd-0936-4e67-b87b-f96595809e8c)

[华为云 kafka](https://console.huaweicloud.com/dms/?engine=kafka&agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/queue/newKafkaDetail/topicQuery?instanceId=82454f48-2deb-4496-944f-65e31bbde46d)

[华为云慢日志](https://console.huaweicloud.com/rds/?agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/rds/management/logsTab/ec8abc8da24f4556b33fd74920584aa7in01)

[Rancher 部署列表](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/apps.deployment)

[Rancher Pod](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/pod)

[Grafana监控 - 新零售微服务接口监控](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/EG1KNOVIk/xin-ling-shou-wei-fu-wu-jie-kou-jian-kong?orgId=1)

[Grafana监控 - 微服务网关](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/7voUGKrik/spring-cloud-gateway-wei-fu-wu-wang-guan?orgId=1&refresh=1m)

[Grafana监控 - RocketMQ](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/WQS4SOFMz/rocketmqxiao-xi-dui-lie-jian-kong?orgId=1)

[Grafana监控 - Kafka](https://grafana.hxyxt.com/k8s/clusters/c-m-k9st897j/api/v1/namespaces/cattle-monitoring-system/services/http:rancher-monitoring-grafana:80/proxy/d/jwPKIsniz/kafkaxiao-xi-dui-lie-jian-kong?orgId=1)

|  |  |
| --- | --- |
| 关注企微**线上**告警群,这里的告警比较及时 |  |
| Skywalking —— 查看链路和日志 发现Skywalking在chrome浏览器容易崩溃。在Microsoft Edge上不会崩溃,所以崩溃了,可以使用Edge浏览器 | 直接到这个界面: [http://skywalking.hxyxt.com/General-Service/Services](http://skywalking.hxyxt.com/General-Service/Services) |
| 看链路日志也可以直接在链路点击查看日志 |
| 链路日志只能看到一个请求链。如果要看项目的全部日志,例如想要依据某个info日志来判断某个逻辑是否执行，则可以到华为云日志 | 华为云日志: [https://console.huaweicloud.com/lts/?region=cn-southwest-2&locale=zh-cn#/cts/logEventsLeftMenu/events?groupId=f68c7c65-2699-4b31-a09b-e4afde9674fa&topicId=100967b3-9a32-47c4-9495-38d6b149514f&epsId=33757bf3-64ff-472e-84dc-4e28f300acff](https://console.huaweicloud.com/lts/?region=cn-southwest-2&locale=zh-cn#/cts/logEventsLeftMenu/events?groupId=f68c7c65-2699-4b31-a09b-e4afde9674fa&topicId=100967b3-9a32-47c4-9495-38d6b149514f&epsId=33757bf3-64ff-472e-84dc-4e28f300acff) 可以直接将想要搜索的内容直接输入,也可以加一些筛选条件。搜索框有提示 下面的示例就是索索hydee-api-gateway的ERROR日志,且ERROR日志中包含Memory字符串的日志 |
| 华为云日志几个比较实用的操作在idea中分析堆栈 |
| 在pod中查看日志 | 线上Rancher地址: [https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/apps.deployment](https://prod-cloud.hxyxt.com/dashboard/c/c-m-k9st897j/explorer/apps.deployment)#### 服务部署列表可以直接输入服务名直接检索#### 节点列表 常用shell命令:容器内命令:  cdlog 直接进入日志目录  ls -lt 查看列表,最新的排在前面  tail -f 日志文件  试试查看日志 tail -200f 日志文件  从前200行开始查看日志  结合管道 grep 可以实现过滤,例如: ls -lt | grep xxx tail -f 日志文件 | grep xxx  cat 日志名 | grep xxx | more 一般是加一个more命令,不然刷的太快了  grep 管道支持正则,有些场景需要正则 |
| 慢SQL - [华为云慢日志](https://console.huaweicloud.com/rds/?agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/rds/management/logsTab/ec8abc8da24f4556b33fd74920584aa7in01) | 在华为云上如果有慢SQL,可以使用explain命令解释计划产看是否有使用到索引。这个分场景的话,灵活处理即可。例如某些日志表,没有设置有效期存了很多数据,为了临时解决可以加索引来解决耗时问题，后面还是要和产品沟通下场景。 |
| [华为云 rocketmq](https://console.huaweicloud.com/dms/?engine=reliability&agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/queue/instanceDetail/baseInfo?instanceId=583757bd-0936-4e67-b87b-f96595809e8c) |  |
| [华为云 kafka](https://console.huaweicloud.com/dms/?engine=kafka&agencyId=acf8a996fd9641228f735aa99405bf2a&region=cn-southwest-2&locale=zh-cn#/queue/newKafkaDetail/topicQuery?instanceId=82454f48-2deb-4496-944f-65e31bbde46d) |  |