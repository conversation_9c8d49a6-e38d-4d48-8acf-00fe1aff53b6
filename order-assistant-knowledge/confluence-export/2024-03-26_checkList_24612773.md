# 2024-03-26 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1.云仓订单补推 | hydee-middle-order | 1 | feature-ORDER-297 |  |  | @汪骁 |  |  |  |  |
| hydee-business-order-web | 2 |  |  |  |  |  |  |  |
| hydee-business-order-b2c-third | 3 |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | third_purchase_order_repush | 三方订单推送失败记录表 | CREATE TABLE `third_purchase_order_repush` (   `id` bigint NOT NULL COMMENT '订单id，与order_info表id保持一致',   `third_purchase_order_no` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方云仓平台采购订单号',   `third_purchase_order_status` tinyint DEFAULT NULL COMMENT '第三方云仓平台采购订单状态 1 创建成功 2 创建失败 3已取消',   `sp_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商（服务商）编码',   `sp_type` tinyint DEFAULT NULL COMMENT '供应商（服务商）类型 0：其他；1、京东慧采；2、会订货',   `create_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',   `repush_count` int DEFAULT '0' COMMENT '重推次数',   `remark` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',   PRIMARY KEY (`id`),   KEY `idx_third_purchase_order_no` (`third_purchase_order_no`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='三方订单推送失败记录表'; |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |