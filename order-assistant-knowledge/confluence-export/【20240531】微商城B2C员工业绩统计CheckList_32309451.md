# 【20240531】微商城B2C员工业绩统计CheckList

- [一、上线内容](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-%E4%B8%80%E3%80%81%E4%B8%8A%E7%BA%BF%E5%86%85%E5%AE%B9)
- [二、配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-%E4%BA%8C%E3%80%81%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
  - [2.2 apollo配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.2apollo%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
  - [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.5MQ)
  - [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.6nacos)
- [2.1 数据库变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.1%E6%95%B0%E6%8D%AE%E5%BA%93%E5%8F%98%E6%9B%B4)
- [2.2 apollo配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.2apollo%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.3 网关配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.3%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.4 xxl-job配置变更](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.4xxl-job%E9%85%8D%E7%BD%AE%E5%8F%98%E6%9B%B4)
- [2.5 MQ](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.5MQ)
- [2.6 nacos](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-2.6nacos)
- [三、上线影响](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-%E4%B8%89%E3%80%81%E4%B8%8A%E7%BA%BF%E5%BD%B1%E5%93%8D)
- [四、上线SOP](https://yxtcf.hxyxt.com/display/brd/2024-01-03+checkList#id-20240103checkList-%E5%9B%9B%E3%80%81%E4%B8%8A%E7%BA%BFSOP)


### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 微商城B2C员工业绩统计 | ydjia-merchant-customer ydjia-merchant-manager hydee-middle-order | 1 | ydjia-merchant-customer ydjia-merchant-manager hydee-middle-order |  |  | @汪骁 |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 库名 | DDL语句 |
| --- | --- | --- |
| 1 | middle-order | CREATE TABLE `staff_promotion_record` (  `id` bigint NOT NULL,  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编码现在基本都是500001',  `order_id` bigint NOT NULL COMMENT 'order_info id',  `status` tinyint NOT NULL COMMENT '状态 1支付 2退款',  `commodity_id` bigint NOT NULL COMMENT '商品中台 商品id',  `commodity_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编码',  `commodity_type` tinyint NOT NULL COMMENT '商品类型 1自营 2云仓',  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',  `commodity_number` int NOT NULL COMMENT '商品数量',  `commodity_price` decimal(16,2) NOT NULL COMMENT '商品实付总金额 单位：元',  `promotion_ratio_id` bigint NOT NULL COMMENT '商品业绩比例id',  `promotion_ratio` double(5,4) unsigned zerofill NOT NULL COMMENT '商品业绩比例',  `m_pic` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品图片 （SKU主图）',  `emp_code` varchar(40) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工编号 推广员',  `member_id` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT '下单用户会员id 小程序查询用户信息展示用',  `org_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工所属机构',  `send_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '货源 （自营订单放门店编码，云仓服务商编码）',  `send_store_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '货源中文',  `performance` decimal(16,4) NOT NULL COMMENT '商品业绩金额（commodity_price*promotion_ratio）',  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `updated_by` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `isvalid` bigint NOT NULL DEFAULT '1' COMMENT '是否删除 1-未删除， !=1已删除''',  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_created_time` (`created_time`),  KEY `idx_status` (`status`) USING BTREE,  KEY `idx_order_no` (`order_id`) USING BTREE,  KEY `idx_emp_code` (`emp_code`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C推广信息记录表'; |
| 2 | CREATE TABLE `staff_income_statistics` (  `id` bigint NOT NULL,  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编码现在基本都是500001',  `emp_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工编号 推广员',  `org_code` varchar(40) COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工所属机构',  `order_number` int NOT NULL DEFAULT '0' COMMENT '月单量',  `refund_number` int NOT NULL DEFAULT '0' COMMENT '退款数量',  `promotion_amount` decimal(16,4) NOT NULL COMMENT '业绩金额单位：元',  `current_refund` decimal(16,4) NOT NULL COMMENT '本期退款单位：元',  `actual_income` decimal(16,4) NOT NULL COMMENT '实际收益单位：元',  `previous_refund` decimal(16,4) NOT NULL COMMENT '上期退款单位：元',  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `isvalid` bigint NOT NULL DEFAULT '1' COMMENT '是否删除 1-未删除， !=1已删除''',  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_created_time` (`created_time`),  KEY `idx_emp_code` (`emp_code`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='B2C推广信息统计表'; |
| 3 | ALTER TABLE `middle_order`.`order_info`  ADD COLUMN `emp_code` bigint NULL COMMENT '员工编号 推广员' AFTER `pay_sale_info`; |
| 4 | ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `promotion_ratio_id` bigint NULL COMMENT '商品业绩比例id' AFTER `final_payment_amount`, ADD COLUMN `promotion_ratio` double(5, 4) ZEROFILL NULL COMMENT '商品业绩比例' AFTER `promotion_ratio_id`; |


#### 2.2 apollo配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |