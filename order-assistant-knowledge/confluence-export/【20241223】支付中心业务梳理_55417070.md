# 【20241223】支付中心业务梳理

## 

## 一、支付现状

支付中台现状梳理

### 1.1 功能梳理

true3falseautotoptrue10415

### 1.1 支付流程

true支付流程falseautotoptrue7019

### 1.3 支付时序图

true4falseautotoptrue13213

1.2 微信支付配置关系

true配置依赖关系falseautotoptrue4311

资金流向关系

true资金管理方式falseautotoptrue6816

### 1.2 服务关系

true1falseautotoptrue13516

### 1.4 表情况

| 表明 | 类型 |
| --- | --- |
| assignment_engine_assignment | 其他 |
| t_divide_account_log | 支付记录 |
| t_divide_account_order | 支付记录 |
| t_divide_account_receiver | 支付记录 |
| t_organization_tree | 配置 |
| t_pay_bill_job_config | 配置 |
| t_pay_bill_log | 支付记录 |
| t_pay_bill_order | 支付记录 |
| t_pay_bill_order_detail | 支付记录 |
| t_pay_business_channel | 配置 |
| t_pay_channel | 配置 |
| t_pay_channel_customer | 配置 |
| t_pay_channel_type | 配置 |
| t_pay_commodity | 配置 |
| t_pay_contract_address | 配置 |
| t_pay_contract_bank | 配置 |
| t_pay_contract_base | 配置 |
| t_pay_contract_branch_bank | 配置 |
| t_pay_contract_country | 配置 |
| t_pay_contract_mcc | 配置 |
| t_pay_core_log | 支付记录 |
| t_pay_core_order | 支付记录 |
| t_pay_core_refund_order | 支付记录 |
| t_pay_core_refund_request_order | 支付记录 |
| t_pay_core_request_order | 支付记录 |
| t_pay_customer | 配置 |
| t_pay_customer_rate | 配置 |
| t_pay_divide_account_main | 支付记录 |
| t_pay_exception_log | 支付记录 |
| t_pay_exception_order | 支付记录 |
| t_pay_info_config | 配置 |
| t_pay_info_config_rela_channel | 配置 |
| t_pay_organization_config | 配置 |
| t_pay_platform_business | 配置 |
| t_pay_platform_business_channel_type | 配置 |
| t_pay_platform_channel_code | 配置 |
| t_pay_type | 配置 |
| t_pay_way | 配置 |
| t_pay_way_detail | 配置 |


空表

| 表明 |  |
| --- | --- |
| bak_pay_channel_customer |  |
| nr_pay_info |  |
| t_business_pay_order |  |
| t_medicine_insurance_bill_detail |  |
| t_medicine_insurance_bill_gather |  |
| t_pay_bill_record |  |
| t_pay_bill_record_detail |  |
| t_pay_bill_record_total |  |
| t_pay_bill_report |  |
| t_pay_contract_address_copy |  |
| t_pay_contract_change_detail |  |
| t_pay_contract_change_record |  |
| t_pay_contract_img |  |
| t_pay_contract_incoming_record |  |
| t_pay_contract_merchant_store |  |
| t_pay_contract_open_business |  |
| t_pay_contract_personal |  |
| t_pay_contract_support_bank |  |
| t_pay_contract_third_channel |  |
| t_pay_core_parent_order |  |
| t_pay_platform_business_channel_type_bak |  |
| t_pay_report_operation |  |
| t_pay_risk_complaint_order |  |
| t_pay_settle_info |  |
| t_pay_task |  |
| t_pay_tonglian_device_info |  |
| t_pay_tonglian_device_main |  |
| t_test |  |


### 1,.5 接口情况

| payment |  |
| --- | --- |
| POST:/${api.version}/unifiedPay/refund |  |
| POST:/${api.version}/unifiedPay/refundNotify/{refundNo} |  |
| POST:/${api.version}/payAfter/qryPaymentDetails |  |
| POST:/${api.version}/payAfter/getPaymentLogs |  |
| POST:/${api.version}/config/medical-insurance-payment/storeList |  |
| POST:/${api.version}/payAfter/queryMessage |  |
| POST:/${api.version}/unifiedPay/pay |  |
| POST:/${api.version}/config/store/getPayChannel |  |
| POST:/${api.version}/unifiedPay/getPaidFee |  |
| POST:/${api.version}/unifiedPay/payCancel |  |
| POST:/${api.version}/payAfter/getLastPaymentLog |  |
| POST:/${api.version}/unifiedPay/payNotify/{orderNo} |  |
| POST:/${api.version}/unifiedPay/orderPayStatusQuery |  |
| POST:/${api.version}/config/queryPayChannelOpenStatus |  |


| pay-core |  |
| --- | --- |
| POST:/open-api/secondPhase/refund/v1.1 |  |
| POST:/open-api/secondPhase/refund/query/v1.1 |  |
| POST:/pay-inner/refund/query/v1.1 |  |
| POST:/pay-inner/close/v1.1 |  |
| POST:/pay-inner/query/v1.1 |  |
| POST:/public/secondPhase/refund/callback/{orderNo}/v1.1 |  |
| POST:/pay-inner/refund/v1.1 |  |
| POST:/pay-inner/pay/v1.1 |  |
| POST:/public/secondPhase/pay/callback/{orderNo}/v1.1 |  |


| pay-finance-center |  |
| --- | --- |
| POST:/public/manual-refund/refund |  |
| POST:/public/finance/payInfoConfig/org/config/page |  |
| POST:/public/finance/payBindChannel/third_find_business |  |
| POST:/public/manual-refund/query_payment_record |  |
| POST:/public/payBill/get_third_bill_detail |  |
| POST:/public/finance/payPlatformBusiness/fixedQrCode |  |
| POST:/public/finance/payBusinessChannel/find |  |
| POST:/public/finance/payBusinessChannel/hasRecord |  |
| POST:/public/finance/payInfoConfig/org/config/init/store |  |
| POST:/public/finance/payBindChannel/find |  |
| POST:/public/finance/payInfoConfig/org/config/info |  |
| POST:/public/finance/payBindChannel/third_update_status |  |
| POST:/public/finance/payInfoConfig/org/config/save-update |  |
| POST:/public/finance/payBindChannel/third_cert_upload |  |
| POST:/public/finance/payBindChannel/thirdFindAppid |  |
| POST:/public/finance/payInfoConfig/third_find |  |
| POST:/public/finance/org/page |  |
| POST:/public/finance/payInfoConfig/third_find/page |  |
| POST:/private/payWay/get_pay_way |  |
| POST:/private/payWay/get_business_is_medical |  |