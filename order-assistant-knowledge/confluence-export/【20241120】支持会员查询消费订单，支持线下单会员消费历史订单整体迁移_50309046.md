# 【20241120】支持会员查询消费订单，支持线下单会员消费历史订单整体迁移

**JIRA: 一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3520**

### 线下单模型和迁移文档

1. ****
2. ****


## 一、 背景

### 1.1 业务背景

会员中心/微商城/POS/一心助手/慢病中心均需要查询到会员的消费记录；

同时12月，会员中心将原来CRM查询会员记录替换成心云会员中心查询，需要增加历史订单的迁移

### 1.2 痛点分析

- 无法通过会员号查到该会员的线上和线下消费记录
- 无历史消费记录


### 1.3 系统现状

业务现有流程图

true当前消费记录查询falseautotoptrue6122

**外部依赖现状**

1. 目前会员消费记录强依赖SAP HANA视图,经常存在超时不可用情况
2. 未来(25年1月左右)CRM的SAP HANA视图会下线


**订单数据存储现状**

1. 订单数据分布在不同的业务表中,线上订单、线下订单。【数据库】
2. 线下订单按照用户Id数据分散在不同的分表中，如果无用户入参查询，跨表查询性能较差,响应时间难以保证【数据表】


## 二、 需求分析

### 2.1 场景分析

### 2.2 业务流程

产品优化后的业务流程

true优化后falseautotoptrue8015

## 三、 目标

### 3.1 本期目标

#### 3.1.1 业务目标

1. 订单中台支持会员中心/微商城/POS/一心助手/慢病中心查询会员消费记录；
2. 线下单历史数据迁移完成


#### 3.1.2 技术目标

- SLA指标提升
  - 查询响应时间从原有的10s~20s优化至300ms以内,解决响应超时问题
  - 支持亿级数据秒级查询
  - 服务可用性达到99.99%
- 查询响应时间从原有的10s~20s优化至300ms以内,解决响应超时问题
- 支持亿级数据秒级查询
- 服务可用性达到99.99%
- 研发效能提升
  - 减少80%跨表查询开发工作
  - 简化数据访问层代码
  - 降低维护成本
- 减少80%跨表查询开发工作
- 简化数据访问层代码
- 降低维护成本


### 3.2 中长期目标

- 构建统一的订单查询服务
- 支持灵活的查询条件组合
- 提供标准化的查询接口


## 四、整体设计

**消费记录数据查询**

true数据架构falseautotoptrue10907

**历史数据迁移迁移**

true数据迁移falseautotoptrue11666

**分库算法详细流程:**

****

**归档数据查询方案,单个订单详情**

本次需求只涉及到会员数据查询,这部分数据暂不入归档库,故不用调整

**true归档数据查询方案(单订单)falseautotoptrue3516**

## 五、 详细设计

### 1、 模块详细设计

请求时序图

**true订单数据查询falseautotoptrue6411**

### 2、 ES条件索引设计

**正单条件索引**

| 字段 | 字段类型 | 注释 |  |
| --- | --- | --- | --- |
| userId | String | 会员ID (心云) |  |
| created | Date | 下单时间 |  |
| createTime | Date | 创建时间 |  |
| storeCode | String | 门店 |  |
| userCardNo | String | 会员编码(唯一值) |  |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  |
| platformCode | String | HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传 |  |
| orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 |  |
| thirdOrderNo | String | 三方订单号 |  |
| orderNo | String | 系统订单号 |  |
| companyCode | String | 分公司Code |  |
| storeType | String | 门店类型 DIRECT_SALES-直营 JOIN-加盟 |  |
| refundType? | String | 退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款MT、ElE refund_order.typeJDDJ 可以判断 order_info.order_state=101,102微商城 refund_order.last_apply_flag = 1 | 会员慢病记录加 |


**退单条件索引**

| 字段 | 字段类型 | 注释 |
| --- | --- | --- |
| userId | String | 会员ID |
| created | Date | 退单时间 |
| createTime | Date | 创建时间 |
| storeCode | String | 门店 |
| userCardNo | String | 会员编码 |
| orderSource | Integer | 订单类型 ONLINE-线上订单 POS-线下订单 |
| platformCode | String | HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传 |
| refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 |
| refundType | String | 退款类型, PART_REFUND-部分退款，REFUND_AMOUNT-全额退款 |
| afterSaleType | String | 售后单类型 AFTER_SALE_AMOUNT-退款 、AFTER_SALE_GOODS-退货 、AFTER_SALE_AMOUNT_GOODS-退货退款 |
| thirdRefundNo | String | 三方退单号 |
| thirdOrderNo | String | 三方正单号 |
| orderNo | String | 系统订单号 |
| refundNo | String | 系统退单号 |
| storeType | String | 门店类型 DIRECT_SALES-直营 JOIN - 加盟 |


### 3、 接口设计

**正单接口**

| 序号 |  | 入参 | 正单返参 |
| --- | --- | --- | --- |
| 1 | 单个会员的历史消费记录 com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberOrderTransaction | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | userId | String | 会员ID | | createdStart | Date | 下单开始时间 | | createdEnd | Date | 下单结束时间 | | 字段 | 字段类型 | 注释 | userId | String | 会员ID | createdStart | Date | 下单开始时间 | createdEnd | Date | 下单结束时间 | OrderInfoDto| 字段 | 字段类型 | 注释 | 字段取值 | | --- | --- | --- | --- | | orderNo | String | 系统单号 |  | | thirdOrderNo | String | 平台订单号 |  | | orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 |  | | created | Date | 下单时间 |  | | payTime | Date | 支付时间 |  | | buyerActualAmount | BigDecimal | 客户实付金额 | order_pay_info.buyer_actual_amountO2O\B2C 满足 | | deliveryFeeAmount | BigDecimal | 运费金额 | order_pay_info.delivery_fee-order_pay_info.merchant_delivery_fee_discount-order_pay_info.platform_delivery_fee_discountO2O\B2C 满足 | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  | | storeCode | String | （下单）门店编码 |  | | storeName | String | （下单）门店名称 |  | | orderDetailDtoList | List<OrderDetailDto> | 订单明细 |  | | hasRefundOrder | Boolean | 是否含退单 |  | | billAmount | BigDecimal | 下账金额 | O2O: erp_bill_info.bill_total_amount B2C(oms_order_no):先取account_order.bill_commodity_amount+account_order.delivery_fee取不到再取 erp_bill_info.bill_total_amount | OrderDetailDto| 字段 | 字段类型 | 注释 |  | 字段取值 | | --- | --- | --- | --- | --- | | erpCode | String | 商品编码 |  |  | | erpName | String | 商品名称 |  |  | | commoditySpec | String | 规格 |  |  | | manufacture | String | 生产厂商 |  |  | | commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  |  | | actualAmount | BigDecimal | 商品实付金额,目前只有一心助手使用 |  | order_detail.actual_amount O2O\B2C 满足 | | billAmount | BigDecimal | 下账金额 |  | order_detail.actual_net_amountB2C(oms_order_no):account_order_detail.bill_price*account_order_detail.goods_count | | fiveClass | String | 商品五级分类编码 | ********会员慢病要求加 |  | | fiveClassName | String | 商品五级分类Name | ********会员慢病要求加 |  | | beReplacedOrderDetail | OrderDetailDto | 被替换的原商品 | ********添加 |  | | 字段 | 字段类型 | 注释 | 字段取值 | orderNo | String | 系统单号 |  | thirdOrderNo | String | 平台订单号 |  | orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 |  | created | Date | 下单时间 |  | payTime | Date | 支付时间 |  | buyerActualAmount | BigDecimal | 客户实付金额 | order_pay_info.buyer_actual_amountO2O\B2C 满足 | deliveryFeeAmount | BigDecimal | 运费金额 | order_pay_info.delivery_fee-order_pay_info.merchant_delivery_fee_discount-order_pay_info.platform_delivery_fee_discountO2O\B2C 满足 | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  | storeCode | String | （下单）门店编码 |  | storeName | String | （下单）门店名称 |  | orderDetailDtoList | List<OrderDetailDto> | 订单明细 |  | hasRefundOrder | Boolean | 是否含退单 |  | billAmount | BigDecimal | 下账金额 | O2O: erp_bill_info.bill_total_amount B2C(oms_order_no):先取account_order.bill_commodity_amount+account_order.delivery_fee取不到再取 erp_bill_info.bill_total_amount | 字段 | 字段类型 | 注释 |  | 字段取值 | erpCode | String | 商品编码 |  |  | erpName | String | 商品名称 |  |  | commoditySpec | String | 规格 |  |  | manufacture | String | 生产厂商 |  |  | commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  |  | actualAmount | BigDecimal | 商品实付金额,目前只有一心助手使用 |  | order_detail.actual_amount O2O\B2C 满足 | billAmount | BigDecimal | 下账金额 |  | order_detail.actual_net_amountB2C(oms_order_no):account_order_detail.bill_price*account_order_detail.goods_count | fiveClass | String | 商品五级分类编码 | ********会员慢病要求加 |  | fiveClassName | String | 商品五级分类Name | ********会员慢病要求加 |  | beReplacedOrderDetail | OrderDetailDto | 被替换的原商品 | ********添加 |  |
| 字段 | 字段类型 | 注释 |
| userId | String | 会员ID |
| createdStart | Date | 下单开始时间 |
| createdEnd | Date | 下单结束时间 |
| 字段 | 字段类型 | 注释 | 字段取值 |
| orderNo | String | 系统单号 |  |
| thirdOrderNo | String | 平台订单号 |  |
| orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 |  |
| created | Date | 下单时间 |  |
| payTime | Date | 支付时间 |  |
| buyerActualAmount | BigDecimal | 客户实付金额 | order_pay_info.buyer_actual_amountO2O\B2C 满足 |
| deliveryFeeAmount | BigDecimal | 运费金额 | order_pay_info.delivery_fee-order_pay_info.merchant_delivery_fee_discount-order_pay_info.platform_delivery_fee_discountO2O\B2C 满足 |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  |
| storeCode | String | （下单）门店编码 |  |
| storeName | String | （下单）门店名称 |  |
| orderDetailDtoList | List<OrderDetailDto> | 订单明细 |  |
| hasRefundOrder | Boolean | 是否含退单 |  |
| billAmount | BigDecimal | 下账金额 | O2O: erp_bill_info.bill_total_amount B2C(oms_order_no):先取account_order.bill_commodity_amount+account_order.delivery_fee取不到再取 erp_bill_info.bill_total_amount |
| 字段 | 字段类型 | 注释 |  | 字段取值 |
| erpCode | String | 商品编码 |  |  |
| erpName | String | 商品名称 |  |  |
| commoditySpec | String | 规格 |  |  |
| manufacture | String | 生产厂商 |  |  |
| commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  |  |
| actualAmount | BigDecimal | 商品实付金额,目前只有一心助手使用 |  | order_detail.actual_amount O2O\B2C 满足 |
| billAmount | BigDecimal | 下账金额 |  | order_detail.actual_net_amountB2C(oms_order_no):account_order_detail.bill_price*account_order_detail.goods_count |
| fiveClass | String | 商品五级分类编码 | ********会员慢病要求加 |  |
| fiveClassName | String | 商品五级分类Name | ********会员慢病要求加 |  |
| beReplacedOrderDetail | OrderDetailDto | 被替换的原商品 | ********添加 |  |
| 2 | 自定义范围的消费记录com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberOrderTransactionSimple | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | userId | String | 会员ID | | createdStart | Date | 下单开始时间 | | createdEnd | Date | 下单结束时间 | | storeCode | String | 门店 | | userCardNo | String | 会员号 | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 | | orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 | | thirdOrderNo | String | 三方订单号 | | orderNo | String | 系统订单号 | | 字段 | 字段类型 | 注释 | userId | String | 会员ID | createdStart | Date | 下单开始时间 | createdEnd | Date | 下单结束时间 | storeCode | String | 门店 | userCardNo | String | 会员号 | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 | orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 | thirdOrderNo | String | 三方订单号 | orderNo | String | 系统订单号 | 返回结构同OrderInfoDto,无orderDetailDtoList明细数据 |
| 字段 | 字段类型 | 注释 |
| userId | String | 会员ID |
| createdStart | Date | 下单开始时间 |
| createdEnd | Date | 下单结束时间 |
| storeCode | String | 门店 |
| userCardNo | String | 会员号 |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |
| orderStatus | Integer | 订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 |
| thirdOrderNo | String | 三方订单号 |
| orderNo | String | 系统订单号 |
| 3 | 单个订单明细com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberOrderTransactionDetail | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | orderNo | String | 系统订单号 | | userId | String | 会员ID | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 | | 字段 | 字段类型 | 注释 | orderNo | String | 系统订单号 | userId | String | 会员ID | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 | 同接口1返参结构 |
| 字段 | 字段类型 | 注释 |
| orderNo | String | 系统订单号 |
| userId | String | 会员ID |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 |


**逆向订单接口**

| 序号 |  | 入参 | 逆单返参 |
| --- | --- | --- | --- |
| 1 | 单个会员的历史消费记录com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberRefundOrderTransaction | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | userId | String | 会员ID | | createdStart | Date | 退单开始时间 | | createdEnd | Date | 退单结束时间 | | 字段 | 字段类型 | 注释 | userId | String | 会员ID | createdStart | Date | 退单开始时间 | createdEnd | Date | 退单结束时间 | RefundOrderDto| 字段 | 字段类型 | 注释 | 字段取值 | | --- | --- | --- | --- | | refundNo | String | 系统退单号 |  | | orderNo | String | 系统单号 |  | | thirdOrderNo | String | 正单三方单号 |  | | thirdRefundNo | String | 退单三方单号 |  | | refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 |  | | created | Date | 退单时间 |  | | refundAmount | BigDecimal | 退款金额 | refund_order.consumer_refund | | refundDeliveryFeeAmount | BigDecimal | 退款运费金额 | refund_order.user_postage | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  | | reason | String | 退款原因 |  | | refundDetailDtoList | List<RefundOrderDetailDto> | 订单退款明细 |  | | billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count 累加B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取order_detail.bill_price*refund_detail.refund_count 累加 O2O: erp_refund_info.refund_merchant_totalwhere refund_no 只会有一条  B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取erp_refund_info.refund_merchant_total(需要排除掉 after_sale_order 手工的记录。如果还有多条，就是脏数据)（B2C的erp_refund_info 使用refund_no+after_sale_no 两个字段关联查询的哈，不然有可能会取不到唯一 ，因为线上一个退款单可能创建了多个售后单） | | storeCode | String | 门店编码 |  | RefundOrderDetailDto| 字段 | 字段类型 | 注释 | 字段取值 | | --- | --- | --- | --- | | erpCode | String | 商品编码 |  | | erpName | String | 商品名称 |  | | commoditySpec | String | 规格 |  | | manufacture | String | 生产厂商 |  | | commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  | | actualRefundAmount | BigDecimal | 商品实退金额,目前只有一心助手使用 | refund_detail.buyer_amount O2O/B2C | | billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取order_detail.bill_price*refund_detail.refund_count O2O:refund_detail.actual_net_amount B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取refund_detail.bill_price*refund_detail.refund_count refund_detail.actual_net_amount | | 字段 | 字段类型 | 注释 | 字段取值 | refundNo | String | 系统退单号 |  | orderNo | String | 系统单号 |  | thirdOrderNo | String | 正单三方单号 |  | thirdRefundNo | String | 退单三方单号 |  | refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 |  | created | Date | 退单时间 |  | refundAmount | BigDecimal | 退款金额 | refund_order.consumer_refund | refundDeliveryFeeAmount | BigDecimal | 退款运费金额 | refund_order.user_postage | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  | reason | String | 退款原因 |  | refundDetailDtoList | List<RefundOrderDetailDto> | 订单退款明细 |  | billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count 累加B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取order_detail.bill_price*refund_detail.refund_count 累加 O2O: erp_refund_info.refund_merchant_totalwhere refund_no 只会有一条  B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取erp_refund_info.refund_merchant_total(需要排除掉 after_sale_order 手工的记录。如果还有多条，就是脏数据)（B2C的erp_refund_info 使用refund_no+after_sale_no 两个字段关联查询的哈，不然有可能会取不到唯一 ，因为线上一个退款单可能创建了多个售后单） | storeCode | String | 门店编码 |  | 字段 | 字段类型 | 注释 | 字段取值 | erpCode | String | 商品编码 |  | erpName | String | 商品名称 |  | commoditySpec | String | 规格 |  | manufacture | String | 生产厂商 |  | commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  | actualRefundAmount | BigDecimal | 商品实退金额,目前只有一心助手使用 | refund_detail.buyer_amount O2O/B2C | billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取order_detail.bill_price*refund_detail.refund_count O2O:refund_detail.actual_net_amount B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取refund_detail.bill_price*refund_detail.refund_count refund_detail.actual_net_amount |
| 字段 | 字段类型 | 注释 |
| userId | String | 会员ID |
| createdStart | Date | 退单开始时间 |
| createdEnd | Date | 退单结束时间 |
| 字段 | 字段类型 | 注释 | 字段取值 |
| refundNo | String | 系统退单号 |  |
| orderNo | String | 系统单号 |  |
| thirdOrderNo | String | 正单三方单号 |  |
| thirdRefundNo | String | 退单三方单号 |  |
| refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 |  |
| created | Date | 退单时间 |  |
| refundAmount | BigDecimal | 退款金额 | refund_order.consumer_refund |
| refundDeliveryFeeAmount | BigDecimal | 退款运费金额 | refund_order.user_postage |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |  |
| reason | String | 退款原因 |  |
| refundDetailDtoList | List<RefundOrderDetailDto> | 订单退款明细 |  |
| billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count 累加B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取order_detail.bill_price*refund_detail.refund_count 累加 O2O: erp_refund_info.refund_merchant_totalwhere refund_no 只会有一条  B2C(oms_order_no):先取account_refund.refund_goods_total+account_refund.refund_post_fee 再取erp_refund_info.refund_merchant_total(需要排除掉 after_sale_order 手工的记录。如果还有多条，就是脏数据)（B2C的erp_refund_info 使用refund_no+after_sale_no 两个字段关联查询的哈，不然有可能会取不到唯一 ，因为线上一个退款单可能创建了多个售后单） |
| storeCode | String | 门店编码 |  |
| 字段 | 字段类型 | 注释 | 字段取值 |
| erpCode | String | 商品编码 |  |
| erpName | String | 商品名称 |  |
| commoditySpec | String | 规格 |  |
| manufacture | String | 生产厂商 |  |
| commodityCount | BigDecimal | 商品数量(线下单中药场景会出现小数) |  |
| actualRefundAmount | BigDecimal | 商品实退金额,目前只有一心助手使用 | refund_detail.buyer_amount O2O/B2C |
| billAmount | BigDecimal | 下账金额 | O2O: order_detail.bill_price*refund_detail.refund_count B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取order_detail.bill_price*refund_detail.refund_count O2O:refund_detail.actual_net_amount B2C(oms_order_no):先取account_refund_detail.refund_goods_amount 累加再取refund_detail.bill_price*refund_detail.refund_count refund_detail.actual_net_amount |
| 2 | 自定义范围的消费记录com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberRefundOrderTransactionSimple | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | userId | String | 会员ID | | createdStart | Date | 退单开始时间 | | createdEnd | Date | 退单结束时间 | | storeCode | String | 门店 | | userCardNo | String | 会员号 | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 | | refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 | | thirdOrderNo | String | 正单三方单号 | | orderNo | String | 系统订单号 | | refundNo | String | 系统退单号 | | thirdRefundNo | String | 退单三方单号 | | 字段 | 字段类型 | 注释 | userId | String | 会员ID | createdStart | Date | 退单开始时间 | createdEnd | Date | 退单结束时间 | storeCode | String | 门店 | userCardNo | String | 会员号 | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 | refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 | thirdOrderNo | String | 正单三方单号 | orderNo | String | 系统订单号 | refundNo | String | 系统退单号 | thirdRefundNo | String | 退单三方单号 | 返回结构同RefundOrderDto,无refundDetailDtoList明细数据 |
| 字段 | 字段类型 | 注释 |
| userId | String | 会员ID |
| createdStart | Date | 退单开始时间 |
| createdEnd | Date | 退单结束时间 |
| storeCode | String | 门店 |
| userCardNo | String | 会员号 |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单 |
| refundStatus | Integer | 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消 |
| thirdOrderNo | String | 正单三方单号 |
| orderNo | String | 系统订单号 |
| refundNo | String | 系统退单号 |
| thirdRefundNo | String | 退单三方单号 |
| 3 | 单个退订明细com.yxt.order.open.sdk.offline_order.OrderTransactionQueryApi#memberRefundOrderTransactionDetail | | 字段 | 字段类型 | 注释 | | --- | --- | --- | | refundNo | String | 系统退单号 | | userId | String | 会员ID | | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 | | 字段 | 字段类型 | 注释 | refundNo | String | 系统退单号 | userId | String | 会员ID | orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 | 同接口1返参结构 |
| 字段 | 字段类型 | 注释 |
| refundNo | String | 系统退单号 |
| userId | String | 会员ID |
| orderSource | String | 订单来源 ONLINE-线上订单 POS-线下订单  > 用于快速区分数据源 |


### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

监控数据量是否一致

true数据量检查falseautotoptrue4512

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 | 2024.11.21 |
| 技术方案评审 | 2024.11.27 |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

| 一级功能 | 二级功能 | 优先级 | 工时 |
| --- | --- | --- | --- |
| 构建条件索引 | 线上、线下条件索引构建+正单、退单按照userId分片 | P1 | 3d |
| 刷数脚本开发 | 线上、线下刷数脚本开发+正单、退单 | P2 | 2d |
| 数据量监控 | 监控脚本开发 | P4 | 1d |
| 正单、退单查询接口 (一共6个接口) | 单个会员的历史消费记录 | P1 | 2d |
| 自定义范围的消费记录 | 0.5d |
| 单个订单明细 | 0.5d |
| 退单数据记录(单独分支,先上线) | 退单记录用户、归属、收银台信息 | P0 | 1d |
| 退单数据补齐 | 1d |
| 数据迁移 | 迁移脚本调整:1. 移除拦截逻辑 2. 表结构调整,需要重新测试促销明细和券明细记录 3. migration表示支持多场景(除了TRUE、FALSE,支持EXISTS等) | P3 | 2d |
| 归档库数据,整体迁移至GaussDB1. 基础表年月表建立 2. GaussDB数据源处理 3. 自测 |
| 联调: 2d |
| 合计: 15d |


## 九、 上线方案

1、兼容、回滚方案等

- 全新接口,无需兼容
- 如服务启动异常,回滚至上一个版本即可



2、上线流程、SOP等

  5 incomplete 同步master代码   6 incomplete 发布release sdk   7 incomplete 发布order-service、order-atom-service   8 incomplete 执行刷数脚本,刷完之后,开放接口