# 【20240329】V1.6.2 订单运维工具

# 一、背景

## 1.1 业务背景

针对订单系统当前存在的一些问题，已经不合理的点进行优化以及改进

1.异常订单处理流程优化（针对于商品异常以及 无门店异常订单处理）

2.手工订单 手工创建正向订单和逆向订单

3.订单重推

4.无门店异常订单处理

5.新增配送单的优化

## 1.2 痛点分析

1.异常订单处理流程不合理 主要是商品异常处理 以及无门店异常订单的处理

2.无法创建手工单

3.下账订单卡丢单无法在心云手动重推

4.商家配送的物流订单 物流单号不合理

5.导入第三方配送，无法自动去绑定对应的店铺；

## 1.3 系统现状

无门店异常订单直接单纯去匹配不能手动选择门店

# 二、需求分析

## 2.1 业务流程

[https://3a66mj.axshare.com/?id=nyf3x3&p=%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95&g=1](https://3a66mj.axshare.com/?id=nyf3x3&p=%E5%8A%9F%E8%83%BD%E6%B8%85%E5%8D%95&g=1)

# 三、目标

**3.1 本期目标**

1.异常订单处理流程优化（针对于商品异常 和 无门店异常订单处理）

2.手工订单 手工创建正向订单和逆向订单

3.已经下账的订单的重推（区分科传 海典）

4.商家配送的物流订单的新增逻辑的调整

5.导入第三方配送，自动去绑定对应的店铺；

# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

**1.订单处理-异常订单流程图**

true异常订单强审falseautotoptrue6432

**2.手工单-销售平库存**

****

**true销售平库存falseautotoptrue6514**

**3.手工单-赔付单**

****

**true赔付单falseautotoptrue5865**

**4.手工单-纯手工单**

****

true手工单流程falseautotoptrue7212

**4.手工单-无门店异常处理**

**true门店异常falseautotoptrue5861**

# 五、详细设计

## 5.1 详细模块设计

## 5.3 接口设计

### 5.3.1 前端交互接口

**1. 无门店订单处理**

****

原接口 [https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/matching/stores](https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/matching/stores)

现接口API

1. api-url：/ds/order/matching/storesByCode
2. 请求体:
  1. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
  2. 应用级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 系统订单号 |
| onlineStoreCode | String | 否 | 下单线上门店code |
  3. 请求体示例:
    1. `{`
```"orderNo"``:``0``,`
```"onlineStoreCode"``: ""`
`}`
  4. `{`
```"orderNo"``:``0``,`
```"onlineStoreCode"``: ""`
`}`
  1. `{`
```"orderNo"``:``0``,`
```"onlineStoreCode"``: ""`
`}`
3. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
4. 应用级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 系统订单号 |
| onlineStoreCode | String | 否 | 下单线上门店code |
5. 请求体示例:
  1. `{`
```"orderNo"``:``0``,`
```"onlineStoreCode"``: ""`
`}`
6. `{`
```"orderNo"``:``0``,`
```"onlineStoreCode"``: ""`
`}`


**2.订单重推-API**

 api-url：[/inner/devOps/push/accountAgain](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/inner/devOps/push/accountAgain)

1.   1. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
  2. 应用级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 订单号 |
| pushOrderType | Integer | 否 | 推送订单类型 |
  3. 请求体示例:
    1. `{`
```"orderNo"``:``0``,`
```"pushOrderType"``: 1`
`}`
  4. `{`
```"orderNo"``:``0``,`
```"pushOrderType"``: 1`
`}`
  1. `{`
```"orderNo"``:``0``,`
```"pushOrderType"``: 1`
`}`
2. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
3. 应用级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 订单号 |
| pushOrderType | Integer | 否 | 推送订单类型 |
4. 请求体示例:
  1. `{`
```"orderNo"``:``0``,`
```"pushOrderType"``: 1`
`}`
5. `{`
```"orderNo"``:``0``,`
```"pushOrderType"``: 1`
`}`


**3.创建手工订单-API**

1. api-url：/inner/devOps/createOrder/accounting
2. 请求体:


1.   1. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
  2. **OrderToolDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderCommon | Object | 否 | 手工订单基本信息 必传 |
| orderDetailList | Array | 否 | 订单商品明细 必传 |
| orderPayInfo | Object | 是 | 订单运费信息 |
| orderErpBillInfo | Object | 是 | 订单下账信息 |
| commission | BigDecimal | 是 | 佣金 |
  3. **orderCommonDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| thirdPlatformCode | String | 否 | 平台编码 |
| thirdOrderNo | String | 是 | 第三方平台订单号 |
| sourceOrganizationCode | String | 否 | 下单门店编码 |
| organizationCode | String | 否 | 发货门店编码 |
| repairText | String | 否 | 修复文本记录 |
| repairOrderType | RepairOrderType | 否 | 修补类型 1.repairInventory 2.repairCompensate 3.handmade |
| deliveryType | String | 否 | 配送方式 |
  4. **orderDetailDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| erpCode | String | 否 | 商品erp编码 |
| commodityCount | Integer | 否 | 商品数量 |
| commodityBatchNo | String | 否 | 商品批号 |
| price | BigDecimal | 否 | 商品售价 |
| totalPrice | BigDecimal | 否 | 商品总金额 |
| merchantDiscount | BigDecimal | 是 | 商家承担优惠 |
  5. **orderPayInfoDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| deliveryFee | BigDecimal | 否 | 应收运费 |
| merchantDeliveryFeeDiscount | BigDecimal | 否 | 商家运费优惠 |
| platformDeliveryFeeDiscount | BigDecimal | 否 | 平台运费优惠 |
  6. **orderErpBillInfoDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| billTotalAmount | BigDecimal | 否 | 下账总金额 |
| billCommodityAmount | BigDecimal | 否 | 下账商品金额 |
| merchantDeliveryFee | BigDecimal | 否 | 下账运费金额 |
  7. 请求体示例:{
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}
2. 系统级参数| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| merCode | String | 否 | 商户编码 |
| userId | String | 否 | 处理人ID |
3. **OrderToolDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderCommon | Object | 否 | 手工订单基本信息 必传 |
| orderDetailList | Array | 否 | 订单商品明细 必传 |
| orderPayInfo | Object | 是 | 订单运费信息 |
| orderErpBillInfo | Object | 是 | 订单下账信息 |
| commission | BigDecimal | 是 | 佣金 |
4. **orderCommonDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| thirdPlatformCode | String | 否 | 平台编码 |
| thirdOrderNo | String | 是 | 第三方平台订单号 |
| sourceOrganizationCode | String | 否 | 下单门店编码 |
| organizationCode | String | 否 | 发货门店编码 |
| repairText | String | 否 | 修复文本记录 |
| repairOrderType | RepairOrderType | 否 | 修补类型 1.repairInventory 2.repairCompensate 3.handmade |
| deliveryType | String | 否 | 配送方式 |
5. **orderDetailDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| erpCode | String | 否 | 商品erp编码 |
| commodityCount | Integer | 否 | 商品数量 |
| commodityBatchNo | String | 否 | 商品批号 |
| price | BigDecimal | 否 | 商品售价 |
| totalPrice | BigDecimal | 否 | 商品总金额 |
| merchantDiscount | BigDecimal | 是 | 商家承担优惠 |
6. **orderPayInfoDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| deliveryFee | BigDecimal | 否 | 应收运费 |
| merchantDeliveryFeeDiscount | BigDecimal | 否 | 商家运费优惠 |
| platformDeliveryFeeDiscount | BigDecimal | 否 | 平台运费优惠 |
7. **orderErpBillInfoDto:**| 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| billTotalAmount | BigDecimal | 否 | 下账总金额 |
| billCommodityAmount | BigDecimal | 否 | 下账商品金额 |
| merchantDeliveryFee | BigDecimal | 否 | 下账运费金额 |
8. 请求体示例:{
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}


**4.手工订单查询订单详情-API**

1. api-url：/inner/devOps/handmadeOrder/queryOrderInfo
2. 请求体:
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 系统订单号 |
3. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderNo | Long | 否 | 系统订单号 |
4. 响应体:
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderCommon | Object | 否 | 手工订单基本信息 必传 |
| orderDetailList | Array | 否 | 订单商品明细 必传 |
| orderPayInfo | Object | 是 | 订单运费信息 |
| orderErpBillInfo | Object | 是 | 订单下账信息 |
| commission | BigDecimal | 是 | 佣金 |
  2. 响应体示例:
    1. {
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}
  3. {
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}
  1. {
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}
5. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| orderCommon | Object | 否 | 手工订单基本信息 必传 |
| orderDetailList | Array | 否 | 订单商品明细 必传 |
| orderPayInfo | Object | 是 | 订单运费信息 |
| orderErpBillInfo | Object | 是 | 订单下账信息 |
| commission | BigDecimal | 是 | 佣金 |
6. 响应体示例:
  1. {
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}
7. {
 "orderCommon":{
 "thirdPlatformCode":"27",
 "thirdOrderNo":null,
 "sourceOrganizationCode":"AY31",
 "organizationCode":"AY31",
 "repairType":"repairInventory",
 "repairText":"参苓健脾胃颗粒拆零库存修补"
 },
 "orderDetailList":[
 {"erpCode":"107589",
 "commodityCount":27,
 "commodityBatchNo":"20240110",
 "price":"0.01",
 "totalPrice":"0.27",
 "merchantDiscount":"0.00"
 }
 ],
 "orderPayInfo":{
 "deliveryFee":"0.00",
 "merchantDeliveryFeeDiscount":"0.00",
 "platformDeliveryFeeDiscount":"0.00"
 },
 "orderErpBillInfo":{
 "billTotalAmount":"0.00",
 "billCommodityAmount":"0.00",
 "merchantDeliveryFee":"0.00"
 },
 "commison":"0.00"
}


### 5.3.2 纯后端接口

1. **异常订单--强审核**


1.   1. /merchant/ex/force-audit
  2. /merchant/ex/force-audit-status 异常订单直接强审核
2. /merchant/ex/force-audit
3. /merchant/ex/force-audit-status 异常订单直接强审核
4. **第三方配送导入**
  1. ****
  2. /ds/batch/upload/AsyncStoreDelivery 导入同步配送门店 发送事件
5. ****
6. /ds/batch/upload/AsyncStoreDelivery 导入同步配送门店 发送事件
7. **配送单请求优化**
  1. cn.hydee.middle.business.order.v2.manager.OrderPickManager#callRider
8. cn.hydee.middle.business.order.v2.manager.OrderPickManager#callRider


## 5.4 涉及数据库

无

## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.7 问题

1. 当前无门店异常订单里存在数据如何处理?
2. 手工订单拣货复核完成后订单状态变成已完成如何处理?
3. 创建手工单是否需要走呼叫骑手?
4. 无门店异常订单匹配门店后是否占用库存?


# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年4月3日;**

**研发时间：2024年4月8日-2024年4月12日；**

**联调时间：2024年4月15日-2024年4月18日(含自测)；**

**测试时间：2024年4月19日(提测)；**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等