# 【20240511】SpringGateway接受外部请求

### Spring处理Request请求

Webflux模式替换了旧的Servlet线程模型。用少量的线程处理request和response io操作，这些线程称为Loop线程，而业务交给响应式编程框架处理，响应式编程是非常灵活的，用户可以将业务中阻塞的操作提交到响应式框架的work线程中执行，而不阻塞的操作依然可以在Loop线程中进行处理，大大提高了Loop线程的利用率。官方结构图：

Webflux虽然可以兼容多个底层的通信框架，但是一般情况下，底层使用的还是Netty，毕竟，Netty是目前业界认可的最高性能的通信框架。而Webflux的Loop线程，正好就是著名的Reactor 模式IO处理模型的Reactor线程，如果使用的是高性能的通信框架Netty，这就是Netty的EventLoop线程。

reactor.netty.resources.DefaultLoopResources

--> reactor.netty.resources.DefaultLoopResources#cacheNioServerLoops

[https://www.cnblogs.com/kuangtf/articles/16353220.html](https://www.cnblogs.com/kuangtf/articles/16353220.html)

Spring webflux 线程模型: [https://piotrminkowski.wordpress.com/2020/03/30/a-deep-dive-into-spring-webflux-threading-model/](https://piotrminkowski.wordpress.com/2020/03/30/a-deep-dive-into-spring-webflux-threading-model/)

netty

[https://github.com/doocs/source-code-hunter/blob/main/docs/Netty/基于Netty开发服务端及客户端/基于Netty的服务端开发.md](https://github.com/doocs/source-code-hunter/blob/main/docs/Netty/基于Netty开发服务端及客户端/基于Netty的服务端开发.md)