# 25年第13周2025-04-03

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
|  | 微商城-交易链路切换 | 交易中台    支付中台 |  |  |
|  | 微商城-小前台能力替换 |  |  |  |
|  | 新老模型,数据双向同步 |  |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** | **遗留问题****风险问题** | **需求研发** **技术建设** |  |
| 2 |  | **本周总工时：4d**1. 线上运维   1. 调整会员消费记录分区数   2. 迁移deleted_data至归档库 2. 调整会员消费记录分区数 3. 迁移deleted_data至归档库 4. 会员消费记录支持海典POS上线，刷ES 5. 线下订单管理开发,50% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：4d**1.B2B 相关调整2.数据库升级 （手动升级大表目前还剩下60%，每隔一点时间升级一张表非常耗时）3.处方单打印和提醒延迟（提测） | **遗留问题**  **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d** | **遗留问题****风险问题** | **需求研发****技术建设** |  |
| 5 |  | **本周总工时：4d**1. 评价中台一期上线 2. 评价中台技术方案编写 3. 门店中台支持 4. 线上空单问题排查支持 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 6 |  | **本周总工时：3d** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1. B2B 单笔退款支持无正单退款修改。 2. 广西门店切换海典，B999门店下账异常问题处理。 3. B2C 海典下账同一线程重复推送问题排查。 4. 微商城购物车接口梳理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：3d**1. 广西POS切换 2. 平台对账开发 3. B2C重复下账问题修复 4. B2CWMS作业与OMS作业处方单status状态不一致问题修复 5. B2C订单刷数 6. 慢sql处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 测试用例框架编写 2. 动态路由 3. 支付中台总体设计文档 | **遗留问题****风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) | 无 |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |