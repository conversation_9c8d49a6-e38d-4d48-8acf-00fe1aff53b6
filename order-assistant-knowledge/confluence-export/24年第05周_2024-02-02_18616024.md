# 24年第05周 2024-02-02

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5day**1. 抖店O2O测试+上线 2. 拼多多B2C 3. 和花一起优化订单导出查ES的分页方式 4. 部分线上问题支持 | **㊀计划工作**1. 抖店O2O上线 2. 拼多多B2C订单流程打通 **㊁实际完成**1. 抖店O2O上线 2. 拼多多B2C待测试，年后上线 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 线上问题支持 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. 技术文档沉淀 |  |
| 2 | @王世达 | **本周总工时：5 day**1. B2C门店仓 测试 + 上线 2. 海典H1 增加补偿机制 医保信息修复 3. 海典下账代码优化 持续中 4. 线上订单修复 | **㊀计划工作****** 1. B2C门店仓需求 已上线 2. 海典H1 增加补偿机制 已完成 3. 海典下账代码优化 持续中**㊁实际完成** 1. B2C门店仓需求 已上线 2. 海典H1 增加补偿机制 已完成**㊂遗留问题****** 海典pos下账代码优化，由测试H2过程中逐步进行**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 3 | @杨国枫 | **本周总工时：5 day****1.抖店020提测/****BUG修改并且上线****2.拣货复核查询平台接口（待自测）****3.多次释放库存问题（修改中）** | **㊀计划工作****1.抖店020提测****2.抖店O2OBUG修改并且上线****3.线上BUG****㊁实际完成****1.抖店020提测/****BUG修改并且上线****2.拣货复核查询平台接口（待自测）****3.多次释放库存问题（修改中）****㊂遗留问题****1.多次释放库存问题（修改中）****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 4 |  | **本周总工时：5day**1.修复多还库存订单2.解决历史遗留待下账订单3.和小明一起优化订单导出查ES的分页方式4.查看线上问题 | **㊀计划工作**1.修复多还库存订单2.查看线上问题**㊁实际完成**1.修复多还库存订单2.解决历史遗留待下账订单3.和小明一起优化订单导出查ES的分页方式4.查看线上问题**㊂遗留问题** **㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 5 | @杨俊峰 | **本周总工时：5 day**1.处理打印问题。3day2.商品团队库存同步协查和代码修改。1day3.其他线上问题支援。 1day | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 6 | @李洋 | **本周总工时：5 day**1. 解决B2C走O2O下账的剩余Bug 2. 完成需求营业状态设置需求调整沟通，确认过滤微商城与抖店 3. 处理遗留雨诺未下账逆单，统计各个地区未下账正单并做好通知 4. 接入海典H2测试 | **㊀计划工作**1. 解决B2C走O2O下账的剩余Bug **㊁实际完成**1. 解决B2C走O2O下账的剩余Bug 2. 完成需求营业状态设置需求调整沟通，确认过滤微商城与抖店 3. 处理遗留雨诺未下账逆单，统计各个地区未下账正单并做好通知 4. 接入海典H2测试 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 7 | @杨润康 | **本周总工时：5 day**1. 优雅发布验证 2. 网关内存泄漏排查 3. 测试环境部署 4. 经营分析刷数 | **㊀计划工作********** **㊁实际完成**************  **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 切换值班

| 时间 | 值班人员 |  |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |