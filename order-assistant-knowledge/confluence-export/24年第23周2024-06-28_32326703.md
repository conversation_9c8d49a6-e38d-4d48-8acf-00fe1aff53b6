# 24年第23周2024-06-28

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=32320592#id-24%E5%B9%B4%E7%AC%AC21%E5%91%A820240614-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 6月17号提测   - 6月18-现在 测试中（进度90%）   - 预计7月初上线 7. 5月28号进入开发阶段 8. 6月17号提测 9. 6月18-现在 测试中（进度90%） 10. 预计7月初上线 11. 支付宝：   - 联调阶段 预计20号提测 12. 联调阶段 预计20号提测 13. 京东到家：   - 6月27进入开发阶段 14. 6月27进入开发阶段 15. 微商城：   - 预计7月初进入开发阶段 16. 预计7月初进入开发阶段 | 饿了么预计7-4上线支付宝 还需要时间上线时间还没定。还在和商品对接口，心云bug 还没有改完 |  |
| 2 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参6月1日进入开发阶段  1.创单服务:  2.拣货/换货:- 6月28号进入开发阶段(已完成老流程整理)    3.配送信息更新:  4.申请售后/售后服务: |  | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 |
| 3 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档6月18日开始 营销中台迁移 | 营销中台开始迁移 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 4 | 支付中台重构 |  |  | 暂停 |  |
| 5 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 7 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 8 | [https://jira.hxyxt.com/issues/?filter=10715](https://jira.hxyxt.com/issues/?filter=10715) 订单中台-进行中筛选器 |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 1.B2C退款流程优化 |  | 测试中 （龙敏） |  |
| 2.O2O电子围栏同步 |  | 已上线 |  |
| 3.订单效率优化 |  | 提测下账商家优惠金额部分需下周二提测 |  |
| 4.京东金额修改 |  | 开发（50%）下周二提测 |  |
| 5. 虚拟商品订单 |  | 测试中（宇航） |  |
| 6.紧急需求B2C作业优化一期 |  | 已上线 |  |
| 7.内购需求二期 |  | 待联调 |  |
| 8.线下单迁移 |  | 已提测（明杰） |  |
| 9.线上单对接 |  |  |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| 广播模式mq迁移 |  |  |
| B2C历史订单修复 | 已完成 |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：** | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 2 | 杨润康 | **本周总工时：5d**- Hana数据迁移自测，已提测 - 优雅发布开关支持单独配置，已完成 - 线下单医保信息联调，已提测 - 创单服务梳理,处理中 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** 5day1.B2C 订单作业优化 1.5day2.电子围栏 1.5day3.pre 环境发布。 1 day4.给门店处理打印问题 0.5 day5. 打印方案替换 0.5 day | **遗留问题** 打印方案替换 还需要1天左右**风险问题** | **需求研发****技术建设****** |  |
| 4 |  | **本周总工时：4day**1. 贵州美团B2C单处理 2. 贵州拼多多漏单处理 3. .net重构：   1. 饿了么-电子围栏迁移   2. 京东到家开始介入开发 4. 饿了么-电子围栏迁移 5. 京东到家开始介入开发 6. 日常值班与问题收集处理 | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：4day**1. 修复虚拟商品BUG 2. 生成请货单添加待审核状态 3. 线上BUG处理:   1. 线上环境商城订单根据手机号查询不到   2. 线上环境缓存NPE问题   3. 生成请货单失败 4. 线上环境商城订单根据手机号查询不到 5. 线上环境缓存NPE问题 6. 生成请货单失败 7. 员工推广二期 8. 生成请货单添加订单汇总 | **遗留问题** **风险问题** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：**1.B2C正单组合商品拆分修改2.B2C逆单组合商品生成退款明细3.美团B2C部分退款后全额退款获取数据修复4.B2C退款金额和平台平台金额没有对齐 | **遗留问题**1.B2C退款金额和平台平台金额没有对齐**风险问题** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d**1. 平台电子围栏同步 已上线 2. B2C退款优化bug修复 3. 保山医保正式环境切换问题处理 4. 保山医保字段调整 代码处理完成，下周二上线 5. 微商城退款问题处理 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：**1.B2C退款流程优化 a. 售后单退货单流程调整 b. 退款优化流程bug修复2. 线上问题处理 | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 9 |  | **本周总工时：5d**1. 商家部分退款bug修复 2. 京东拼多多取值优化   1. 熟悉代码业务梳理   2. 开发 3. 熟悉代码业务梳理 4. 开发 | **遗留问题**1. 商家部分退款bug修复 2. 京东拼多多取值优化 **风险问题** | **需求研发****技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |