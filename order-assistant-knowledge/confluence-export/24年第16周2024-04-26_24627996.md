# 24年第16周2024-04-26

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25号讨论完O2O订单域核心服务入参 |  | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审4月26日进入开发阶段 |  | . |
| 3 | 优雅发布升级 |  | 4月12日完成待办列表,推动全部门升级 |  | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  | 本地升级启动成功,部署失败。 |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表规范文档待编写 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | 支付中台重构 |  |  |  |  |
| 8 | Rocketmq |  |  |  |  |
| 9 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 10 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d**1. 订单运维工具配合测试 2. 商品分摊优化 3. 慢SQL 4. DDD字段讨论 5. 订单线上运维 | **计划工作**1. 订单运维工具配合测试 2. 商品分摊优化 3. 慢SQL **㊁实际完成**1. 订单运维工具配合测试 2. 商品分摊优化 3. 慢SQL **㊂遗留问题**1. 吉客云 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx **** | **㊀需求研发相关**1. 吉客云 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. MQ **** |  |
| 2 | 杨润康 | **本周总工时：5pd**1. 线下单对账接口开发、自测 2. 线上单对账接口开发、自测 3. 订单重构DDD   1. 核心服务入参字段讨论,文档整理   2. DDD项目结构调整&仓库拆分&SDK调整(拆分、移除feign依赖等) 4. 核心服务入参字段讨论,文档整理 5. DDD项目结构调整&仓库拆分&SDK调整(拆分、移除feign依赖等) 6. 其他   1. 参加《DDD项目实施案例串讲》会议   2. 参加《测试部门API Case》会议   3. 参加《[第17周] DDD(领域驱动设计)实现分享》&& 会后讨论 7. 参加《DDD项目实施案例串讲》会议 8. 参加《测试部门API Case》会议 9. 参加《[第17周] DDD(领域驱动设计)实现分享》&& 会后讨论 | **㊀计划工作****㊁实际完成** **㊂遗留问题**新的DDD项目结构构建需等最终确认后找运维构建**㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 3 | 杨俊峰 | **本周总工时：5 pd**1. 修复bug（顺丰同城） 2. 京东到家推送 上线以及性能调整 3. 诺和0元单调整，协助排查问题。 4. api中台优惠分摊 5. mongo db 数据库日志处理 6. 药师云审批处方 7. 微商城接口回调参数空缺问题排查。 **** | **㊀计划工作****㊁实际完成****㊂遗留问题******微商城回调可以不传client id 和 group id 问题排查**㊃风险问题****㊄关于团队/项目建设的建议（想法）**1. xxx  **** | **** |  |
| 4 |  | **本周总工时：5pd**1. 接口中台重构   1. 技术二次评审   2. 项目进入开发阶段 2. 技术二次评审 3. 项目进入开发阶段 4. 拣货明细合并需求开发 5. 传统项目-maven模板项目搭建 **** | **㊀计划工作****㊁实际完成**1. 接口中台重构（开发中） 2. 拣货明细合并（今天下班前提测，五一后上线） 3. 传统项目-maven模板项目搭建（第一版已上传到git，但还需要调整） **㊂遗留问题**1. xxx **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx **** | **** |  |
| 5 |  | **本周总工时：5pd**1. 订单运维工具测试 2. 商品金额分摊优化测试 3. 订单线上运维   1. 云仓订单无地址选择优惠券报错(未发版)   2. 微商城拼团订单优惠分摊错误(五一过后 方案忘了)   3. 退款单下账失败     1. 有赠品时多了0.01     2. 退货数量*退货商品单价 != 退货商品金额   4. 有赠品时多了0.01   5. 退货数量*退货商品单价 != 退货商品金额   1. 有赠品时多了0.01   2. 退货数量*退货商品单价 != 退货商品金额 4. 云仓订单无地址选择优惠券报错(未发版) 5. 微商城拼团订单优惠分摊错误(五一过后 方案忘了) 6. 退款单下账失败   1. 有赠品时多了0.01   2. 退货数量*退货商品单价 != 退货商品金额 7. 有赠品时多了0.01 8. 退货数量*退货商品单价 != 退货商品金额 9. DDD字段讨论 **** | **㊀计划工作**1. 订单运维工具测试 2. 商品金额分摊优化测试 3. 订单线上运维 4. 微商城三方自营商品(吉客云订单)技术方案 **㊁实际完成**1. 商品金额分摊优化测试 2. 订单线上运维 **㊂遗留问题**1. 订单运维工具测试 2. 订单线上运维 3. 微商城三方自营商品(吉客云订单)技术方案 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx **** | **** |  |
| 6 |  | **本周总工时：5pd**1.路由策略bug修复 a.更新获取可接单门店功能实现和距离校验 b.聚合校验优化2.内购商城bug修复 a.购物车云仓商品购买失败 b.普通、兑换、内购商城之间的限购规则互不影响 c.总部仓自提实现优化3.线上b2c订单释放库存4.支付宝订单接入开发 | **㊀计划工作**1. 路由bug修复 2. 内购商城bug修复 3. 线上b2c订单释放库存 4. 支付宝订单接入开发 **㊁实际完成**1. 路由bug修复 2. 内购商城bug修复 3. 线上b2c订单释放库存 **㊂遗留问题**1. 支付宝订单接入开发 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx **** | **** |  |
| 7 |  | **本周总工时：5pd**1. 支付券需求开发上线 2. 美团多物流信息上传平台开发上线 3. B2C订单组合商品展示影响拣货复核问题修复 4. 线上问题处理  a. B2C门店单下账缺少POS类型修复代码及修复改该问题导致下账失败得订单 b.组合商品订单无法拣货操作修复 c. 微商城退款单问题处理 | **㊀计划工作**1. 支付券需求开发 **㊁实际完成**1. 支付券需求开发 已上线 2. 美团多物流信息上传平台开发 已上线 3. B2C订单组合商品展示影响拣货复核问题修复 已上线 **㊂遗留问题**1. B2C物流回传，目前只支持单个物流回传，已和产品沟通过，后期出需求 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 8 |  | **本周总工时：5pd**1. 线上运维 2. 订单路由测试优化 3. 科传部分退款下账调整（已发版，测试中） | **㊀计划工作**1. 订单路由测试优化 2. 支付宝订单对接 **㊁实际完成**1. 订单路由测试优化 **㊂遗留问题**1. 支付宝订单对接 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 9 |  | **本周总工时：5pd**1. 熟悉代码 2. 邮政对接/极兔对接/默认快递技术方案编写 | **㊀计划工作****㊁实际完成****㊂遗留问题**1. 开发环境搭建 2. 邮政对接/极兔对接/默认快递技术方案编写 | **㊀需求研发相关**1. 邮政对接/极兔对接/默认快递技术方案编写 |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |