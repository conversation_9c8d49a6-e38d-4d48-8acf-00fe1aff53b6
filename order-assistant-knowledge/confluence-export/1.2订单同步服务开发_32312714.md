# 1.2订单同步服务开发

订单同步服务原始逻辑梳理: 

订单中台重构服务梳理: 

### 订单同步聚合根

主要将订单同步消息划分清晰,便于定义聚合根的动作和事件

|  | 字段 | 类型 | 动作及事件 | 备注 |
| --- | --- | --- | --- | --- |
| 订单主信息 | olorderno  三方平台订单号 orderid 三方平台订单号 消息通知时使用 ectype 电商平台 status 交易状态 omsstatus oms状态 对应该枚举OrderThirdStateEnum trade_from 交易内部来源 modified 交易修改时间->订单修改时间 confirm_time 确认时间 created 交易创建时间 end_time 交易结束时间 daynum 每日号 pay_time 付款时间 ordertype 订单类型 isfirstorder 是否是当前用户首单 extrainfo 额外的附加信息 omni_package B2C 天猫同城购 orderIsNew 订单新老订单标识    membercard 会员卡号   // 订单操作相关字段 applyCancel 申请取消 o2o_auto_confirm o2o自动确认 isadjust 是否调整订单 1 是，0否 top_hold TOP拦截标识，0不拦截，1拦截 null=平台无此标识    // 订单支付信息 type 交付类型列表-->订单付款方式 paytype 支付方式    // 订单金额 discount_fee 系统优惠金额 total_fee 商品金额 adjust_fee 卖家手工调整金额 post_fee 邮费 seller_cod_fee 卖家货到付款服务费 buyer_cod_fee = "0.0"; 买家到付服务费 payment = "0.0"; 商家实收 discount_fee_dtl = "0.0"; 商家明细优惠合计 yfx_fee = "0.0"; 订单运费险 discount_fee_eccode = BigDecimal.ZERO; 平台优惠 discount_fee_sum = "0.0"; 商家整单优惠 cod_payment = BigDecimal.ZERO; 到付金额 settlement_amount = "0.0"; 结算金额 commission_fee = "0.0"; 交易佣金 package_fee = "0.0"; 打包费 sumdiscount = "0.0"; 商品优惠  没使用 postfee_dis; 运费优惠 customerpayment = "0.0"; 客户实付 （商品小计+运费+包装费-商品优惠-运费优惠） postfee_nodis = "0.0" 京东到家原始配送费 platform_delivery_fee 平台配送费优惠金额 shop_delivery_fee 商家配送费优惠金额 merchantActualAmount 商家实收金额，目前只有饿了么和美团是通过接口直接返回，京东到家需要计算 | 聚合 |  |  |
| 订单医保 | medicareAmount 订单医保金额，目前由第三方平台直接返回 medicareOrderId 订单医保结算id，目前由第三方平台直接返回 | 值对象 |  |  |
| 订单处方 | 订单维度处方相关字段rx_audit_status 处方审核状态 havecfy 是否处方  1，是 ；0，否 cfycheck 处方药检验**处方具体明细 List<AddOrderPrescriptionReqDto>**orderno 三方订单号 clientid 网店编码 groupid 企业编码 usedrugname 用药人 sex 性别 age 年龄 identitynumber 身份证号 birthday 出生日期 phonenumber 手机号码 picurl 处方原始图片地址 cfpicurl 处方图片地址 ectype  平台编码 extrainfo Json格式，额外的附加信息 | 值对象 |  |  |
| 订单的归属企业、网点等信息 | groupid  企业编码  商户编码 clientid 网店编码  notnull o2o_shop_id 导购员门店 线上门店 b2cClientId b2cClient的id | 值对象 |  |  |
| 订单收货人信息 | receiver_name 收货人姓名 receiver_name_privacy 收货人姓名隐私 receiver_state 收货人所在省份 receiver_city 收货人所在城市 receiver_district 收货人所在地区 receiver_town 收货人所在镇 receiver_address 收货人详细地址 receiver_address_privacy 收货人隐私详细地址 receiver_state_privacy 收货人所在省份隐私 receiver_city_privacy  收货人所在城市隐私  receiver_district_privacy 收货人所在地区隐私 receiver_town_privacy 收货人所在镇隐私 receiver_detail_address_privacy 收货人隐私详细地址 receiver_zip 收货人邮编 receiver_mobile 收货人手机号码 receiver_phone 收货人电话号码 receiver_phone_privacy 收货人隐私手机号 receiver_lat 收货地址维度 receiver_lng 收货地址经度   buyer_email 买家邮件地址 buyer_memo 买家备注 buyer_flag 买家备注标志 buyer_nick 买家昵称 buyer_message 买家留言 seller_memo 卖家备注 seller_flag 卖家备注标志 | 值对象 |  |  |
| 订单发票 | vatTaxpayerNumber 增值税纳税人识别号 invoice_contect 发票内容 invoice_name 发票抬头 invoice_type 发票类型 | 值对象 |  |  |
| 配送相关 | expcmpname 快递名称 omsdeliverytype 配送类型 delivery_time_type 配送时间类型 --> 送达方式 delivery_time 配送时间描述(送达时间描述) selfverifycode 骑手取货码  delivery_type 京东健康商家自配送用 配送方式 | 值对象 |  |  |
| 订单明细 List | List<AddOrderDetailReqDto>olorderno 三方平台订单号 clientid 网店编码  notnull oid 子订单编号 num_iid  商品数字id 三方平台商品编码 num 购买数量 item_meal_id 套餐id modified 修改时间 title 商品标题 sku_properties_name sku的值 sku_id 商品最小库存单位sku的id upc outer_iid 商家外部编码  erp编码 upc upc-->商品条形编码 isdelete 是否删除 orderdetailid 订单详情ID isgift  是否赠品 true false isMedicareItem 是否是医保商品 extrainfo 拓展信息  originalErpCode  组合商品erpCode，只有为组合商品拆单的订单详情才有值 originalErpCodeNum 组合商品原始数量，只有为组合商品拆单的订单详情才有值 chaiLingOriginalErpCode 拆零商品erpCode，只有为拆零商品的订单详情才有值  //微商城明细相关的字段 order_store_source 微商城OB预约单 供应商来源 1-云货架，2-DC仓 store_code  微商城OB预约单 组织机构编码 云货架商家编码或DC仓编码 expectdeliverytime 微商城OB预约单 预计送达时间   // 商品价格相关 price 商品原单价(客户在页面上看到的商品价格) total_fee  商品应付金额 payment 商品实付金额 discount_fee 子订单级订单优惠金额 adjust_fee 手工调整金额 detailDiscount 平台分摊明细优惠 originPrice 商品实际单价(客户实际支付的该商品的单价) merchantDiscount 商家承担优惠  // 组合商品 ordersuitlist 组合商品明细，为空则不是组合商品 List<AddOrderDetailSuitReqDto> {     olorderno 三方平台订单号     clientid 网店编码  notnull     price 商品价格      num_iid 商品数字id 三方平台商品编码     num 购买数量      total_fee 商品应付金额     title 商品标题     outer_iid 商家外部编码  erp编码     groupid 企业编码     ectype 平台编码      suit_outer_iid 组合商品编码  } | 值对象 |  |  |
| 订单赠品 List | gift_outer_iid 赠品外部商品编码（赠品的ERP商品编码） main_outer_iid 主商品外部商品编码（赠品关联的主商品的ERP商品编码） gift_num 赠品数量 gift_name gift_name main_sku_id main_sku_id gift_sku_id 赠品 skuid | 值对象 |  |  |
|  |  |  |  |  |
|  |  |  |  |  |