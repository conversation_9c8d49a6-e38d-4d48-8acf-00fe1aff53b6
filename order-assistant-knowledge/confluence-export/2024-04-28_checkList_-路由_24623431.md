# 2024-04-28 checkList -路由

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  | ``` hdyee-business-order ``` |  | ``` feature/order-345/order-route ``` |  |  |  |  |  | 2024-05-14 |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 |  | sql |
| --- | --- | --- |
|  | ddl | sql -- ---------------------------- -- Table structure for route_allot -- ---------------------------- CREATE TABLE `route_allot`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `out_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调出门店编码',   `out_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调出门店名称',   `in_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调入门店编码',   `in_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调入门店名称',   `source_org_id_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下单子公司全路径',   `org_id_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货子公司全路径',   `third_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方订单号',   `oms_no` bigint(0) NOT NULL COMMENT '系统订单号 区分销售单和退款单',   `order_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ORDER' COMMENT '关联订单类型 ORDER-销售单 REFUND-退款单',   `oms_allot_no` bigint(0) NOT NULL COMMENT '心云拨单号',   `pos_allot_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'pos拨单号',   `allot_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WAIT' COMMENT '调拨单状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功',   `allot_time` datetime(0) NULL DEFAULT NULL COMMENT '调拨成功时间',   `fail_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败类型 BATCH_NO_ERROR -批次号错误',   `fail_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',   `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   `third_refund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三方退款单号',   PRIMARY KEY (`id`) USING BTREE,   INDEX `oms_no_order_type_idx`(`oms_no`, `order_type`) USING BTREE COMMENT '订单号唯一',   INDEX `idx_source_org_id_path`(`source_org_id_path`) USING BTREE,   INDEX `idx_org_id_path`(`org_id_path`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '转单调拨表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_allot_detail -- ---------------------------- CREATE TABLE `route_allot_detail`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `allot_id` bigint(0) NOT NULL COMMENT '调拨单主表ID',   `order_detail_id` bigint(0) NOT NULL COMMENT '商品明细id',   `erp_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品erp编码',   `good_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',   `commodity_batch_no` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品批号',   `count` int(0) NOT NULL DEFAULT 0 COMMENT '批号对应的数量',   `allot_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'WAIT' COMMENT '状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功',   `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注(包含失败原因等)',   `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '转单调拨表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_divide_order_log -- ---------------------------- CREATE TABLE `route_divide_order_log`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `strategy_id` bigint(0) NOT NULL COMMENT '策略主表ID',   `rule_id` bigint(0) NULL DEFAULT NULL COMMENT '规则ID',   `third_order_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台订单号',   `order_no` bigint(0) NOT NULL COMMENT '系统订单号',   `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '三方平台编码',   `source_org_id_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下单子公司全路径',   `org_id_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货子公司全路径',   `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线下门店编码',   `organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '线下门店名称',   `source_online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源平台店铺编码',   `source_online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源平台店铺名称',   `source_organization_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源线上门店编码',   `source_organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源线上门店名称',   `service_scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务场景',   `strategy_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '策略名称',   `increase_amount` decimal(18, 6) NULL DEFAULT NULL COMMENT '增收金额（订单金额）',   `origin_delivery_fee` decimal(18, 6) NULL DEFAULT NULL COMMENT '原配送费',   `transfer_delivery_fee` decimal(18, 6) NULL DEFAULT NULL COMMENT '转单后配送费',   `cost_savings` decimal(18, 6) NULL DEFAULT NULL COMMENT '成本节约',   `divide_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'SUCCESS' COMMENT '分单状态 SUCCESS：成功，FAIL：失败',   `divide_fail_reason` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分单失败原因',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_company_code`(`source_org_id_path`) USING BTREE,   INDEX `idx_org_id_path`(`org_id_path`) USING BTREE,   INDEX `idx_source_org_id_path`(`source_org_id_path`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单路由分单日志表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_good_set -- ---------------------------- CREATE TABLE `route_good_set`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_rule_id` bigint(0) NOT NULL COMMENT '策略规则表 id',   `re_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接单门店编码',   `re_store_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接单门店名称',   `erp_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品编码',   `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由指定商品配置表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_receive_store_set -- ---------------------------- CREATE TABLE `route_receive_store_set`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_rule_id` bigint(0) NOT NULL COMMENT '策略规则表 id',   `re_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接单门店编码',   `re_store_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接单门店名称',   `distance` double(6, 2) NOT NULL COMMENT '接单门店与分单门店距离（单位：米）',   `priority` int(0) NOT NULL DEFAULT -1 COMMENT '门店优先级',   `pick_num` int(0) NULL DEFAULT 0 COMMENT '待拣货订单量(订单堆积量超过多少不接单)',   `acquiesce` tinyint(1) NULL DEFAULT 0 COMMENT '是否兜底 ture-是 false-否',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   UNIQUE INDEX `uniq_strategy_rule_re_store_code`(`strategy_rule_id`, `re_store_code`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由接单门店配置表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_rule -- ---------------------------- CREATE TABLE `route_rule`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `rule_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则名称',   `rule_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规则类型 分单-HAND_OUT 接单-RECEIVE',   `handle_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理类型 代码逻辑使用，唯一值',   `required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否所有场景均必选 0-否 1-是',   `meaning` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '意义',   `seq` int(0) NOT NULL DEFAULT -1 COMMENT '规则校验顺序',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由规则表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_scene -- ---------------------------- CREATE TABLE `route_scene`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `scene_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景名称',   `scene_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景类型 大店分单-BIG_STORE_HAND_OUT 闭店转单-CLOSE_STORE_TRANSFER 夜店转单-NIGHT_STORE_TRANSGER',   `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '场景说明',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `created_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人名称',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_created_time`(`created_time`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由场景表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_scene_rule_re -- ---------------------------- CREATE TABLE `route_scene_rule_re`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `scent_id` bigint(0) NOT NULL COMMENT '场景模板ID',   `rule_id` bigint(0) NOT NULL COMMENT '规则模板ID',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由场景与规则关联表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_strategy -- ---------------------------- CREATE TABLE `route_strategy`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '策略名称',   `scene_id` bigint(0) NOT NULL COMMENT '所选场景ID',   `scene_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '场景名称',   `company_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '子公司名称',   `company_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '子公司编码',   `store_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店名称',   `store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门店编码',   `online_store_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '店铺id集合,使用;分割',   `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '策略状态 开启-true 关闭-false',   `sms_notifiy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否短信通知 开启-true 关闭-false',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `created_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人名称',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_created_time`(`created_time`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由策略主表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_strategy_modify_log -- ---------------------------- CREATE TABLE `route_strategy_modify_log`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_id` bigint(0) NOT NULL COMMENT '策略id',   `before_content` blob NULL COMMENT '修改前的内容',   `after_content` blob NULL COMMENT '修改后的内容',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '修改人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT '版本号',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '路由策略修改日志表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_strategy_rule -- ---------------------------- CREATE TABLE `route_strategy_rule`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_id` bigint(0) NOT NULL COMMENT '策略主表ID',   `rule_id` bigint(0) NOT NULL COMMENT 'route_rule 规则表ID',   `rule_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规则属性值，不同规则处理不同值，由规则选项决定值，代码中自行处理(允许分单距离单位：米)',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   UNIQUE INDEX `uniq_strategy_id_rule_id`(`strategy_id`, `rule_id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由策略规则明细表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for route_time_period_set -- ---------------------------- CREATE TABLE `route_time_period_set`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_rule_id` bigint(0) NOT NULL COMMENT '策略规则表 id',   `start_time` time(0) NOT NULL COMMENT '开始时间',   `end_time` time(0) NOT NULL COMMENT '结束时间',   `ext_info` varchar(125) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展信息',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由规则时间段配置表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for delivery_fee_rule -- ---------------------------- CREATE TABLE `delivery_fee_rule`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `delivery_rule_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送规则名称',   `delivery_platform_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送商编码',   `trans_priority` tinyint(0) NULL DEFAULT NULL COMMENT '运力优先级',   `start_price` decimal(16, 2) NULL DEFAULT NULL COMMENT '起步价',   `start_distance` decimal(16, 2) NULL DEFAULT NULL COMMENT '起步距离',   `start_weight` decimal(16, 2) NULL DEFAULT NULL COMMENT '起步重量',   `city_grade` tinyint(0) NULL DEFAULT NULL COMMENT '城市等级',   `rule_explain` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规则说明',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配送费规则表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for delivery_fee_rule_addprice -- ---------------------------- CREATE TABLE `delivery_fee_rule_addprice`  (   `id` bigint(0) NOT NULL AUTO_INCREMENT,   `delivery_rule_id` bigint(0) NOT NULL COMMENT '配送策略ID',   `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码',   `city_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市名称',   `add_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '加价类型 DISTANCE_ADD_PRICE：距离加价，WEIGHT_ADD_PRICE：重量加加，TIME_ADD_PRICE：时间加价',   `start_range` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '起始范围(距离m,重量kg,时间时分)',   `end_range` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '截至范围',   `add_price` decimal(16, 2) NULL DEFAULT NULL COMMENT '加价价格',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) USING BTREE,   INDEX `idx_delivery_rule_id`(`delivery_rule_id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配送费加价表' ROW_FORMAT = Dynamic;  -- ---------------------------- -- Table structure for delivery_fee_rule_log -- ---------------------------- CREATE TABLE `delivery_fee_rule_log`  (   `id` bigint(0) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',   `delivery_rule_id` bigint(0) NOT NULL COMMENT '配送策略id delivery_fee_rule',   `before_content` json NULL COMMENT '修改前的内容',   `after_content` json NULL COMMENT '修改后的内容',   `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建人',   `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '修改人',   `created_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',   `updated_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '修改时间',   `version` bigint(0) NOT NULL DEFAULT 1 COMMENT '版本号',   PRIMARY KEY (`id`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '配送费规则修改日志表' ROW_FORMAT = Dynamic; |
|  | ddl | -- ---------------------------- -- Records of route_rule -- ---------------------------- INSERT INTO `route_rule` VALUES (6, '允许分单的订单类型', 'HAND_OUT', 'OUT_ORDER_TYPE_HANDLE', 1, '设置是全部订单或者即时订单或者是预约订单', 5, 'system', 'system', '2024-03-21 06:32:34', '2024-05-13 17:11:25', 1); INSERT INTO `route_rule` VALUES (7, '允许分单时段', 'HAND_OUT', 'OUT_TIME_FRAME_HANDLE', 0, '设置哪些时段可以允许分单', 10, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (8, '允许分单距离', 'HAND_OUT', 'OUT_DISTANCE_HANDLE', 0, '设置超过xx距离的才允许分单', 15, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (9, '是否校验库存', 'HAND_OUT', 'OUT_STOCK_HANDLE', 0, '分单时，是否校验接单门店库存是否充足', 20, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (10, '接单门店只有美团配送时', 'HAND_OUT', 'OUT_MT_FENCE_HANDLE', 0, '设置接单门店只有美团配送时，是否校验在接单门店电子围栏中', 25, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (11, '指定商品指定门店', 'RECEIVE', 'RECEIVE_APPOINT_GOOD_HANDLE', 0, '设置指定商品指定门店发出', 30, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (12, '按订单时段指定门店', 'RECEIVE', 'RECEIVE_TIME_FRAME_HANDLE', 0, '按订单时段设置指定门店', 35, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:32:34', 1); INSERT INTO `route_rule` VALUES (13, '多门店符合分单时', 'RECEIVE', 'RECEIVE_MORE_SHOP_ACCORD_HANDLE', 0, '多门店符合分单时，设置按距离最优/配送费最优\n订单/堆积量最优', 45, 'system', 'system', '2024-03-21 06:32:34', '2024-04-17 09:52:05', 1); INSERT INTO `route_rule` VALUES (14, '接单门店订单堆积量', 'RECEIVE', 'RECEIVE_ORDER_NUM_HANDLE', 0, '接单门店订单堆积量多余xx时，不分单', 40, 'system', 'system', '2024-03-21 06:32:34', '2024-04-16 09:52:03', 1); INSERT INTO `route_rule` VALUES (15, '无门店符合分单时', 'RECEIVE', 'RECEIVE_FORCE_SHOP_HANDLE', 1, '无门店符合分单时，设置是否需要强制分单', 50, 'system', 'system', '2024-03-21 06:32:34', '2024-03-29 23:19:55', 1); INSERT INTO `route_rule` VALUES (16, '接单门店列表', 'RECEIVE', 'RECEIVE_SHOP_LIST_HANDLE', 1, '设置可接单的门店', -1, 'system', 'system', '2024-03-21 06:32:34', '2024-03-21 06:33:30', 1); INSERT INTO `route_rule` VALUES (17, '接单门店能否拒绝', 'RECEIVE', 'RECEIVE_SHOP_ACCEPT_HANDLE', 1, '设置接单门店能否拒绝', 99, 'system', 'system', '2024-03-28 00:23:50', '2024-03-28 04:50:10', 1); INSERT INTO `route_rule` VALUES (19, '分单门店订单堆积量', 'HAND_OUT', 'OUT_ORDER_NUM_HANDLE', 0, '设置门店待拣货订单超过xx，即允许被分单', 18, 'system', 'system', '2024-03-28 03:42:50', '2024-03-28 03:42:50', 1); -- ---------------------------- -- Records of `es_log_config` -- ----------------------------`INSERT` `INTO` `middle_es.es_log_config` `(id, `type`, business_group, is_valid, create_name, create_time, modify_name, modify_time, extend)` `VALUES``(11,``'hydee-route-allot-log'``,``'电商云业务组'``, 1,``'lingxiao'``,``'2021-08-06 00:49:09.000'``,``'lingxiao'``,``'2021-08-06 03:08:01.000'``,``NULL``);` |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| hdyee-business-order | application.yml | message-notify. strategy-change-topic | 新增策略变更通知topic |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |
|  |  |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
| ROCEKETMQ | TOPIC_STRATEGY_CHANGE | 策略变更通知mq |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
| **<dependency> <groupId>com.yxt.middle</groupId> <artifactId>yxt-middle-baseinfo-sdk</artifactId> <version>1.0.2-RELEASE</version> </dependency>** | 修改baseinfo 正式环境依赖版本 |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |