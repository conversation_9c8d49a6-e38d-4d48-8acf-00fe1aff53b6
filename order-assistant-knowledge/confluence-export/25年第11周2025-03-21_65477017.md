# 25年第11周2025-03-21

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | business-gateway timeout [url:/dscloud/2.0/ds/order/sync](http://url/dscloud/2.0/ds/order/sync) |  |  | 不改动 根据redis 查询再同步给es 逻辑深改不动 |
| 2 | business-gateway timeout [url:/dscloud/1.0/ds/zhuishuma/needRecord](http://url/dscloud/1.0/ds/zhuishuma/needRecord) |  |  | ``` 不改动  调用商品校验门店下 某些品是否需要追溯码 ``` |
| 3 | business-gateway timeout [url:/dscloud/1.0/ds/order/detail/batch/stock](http://url/dscloud/1.0/ds/order/detail/batch/stock) |  |  | 不改动 调用商品获取批号信息 |
| 4 | business-gateway timeout [url:/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001](http://url/dscloud/1.0/ds/baseinfo/getPlatformByMerCode/500001) |  |  | 查询用户有权限的平台信息，目前已经不出现了。慢主要是 会员的接口。已经和会员组说过了。 |
| 5 | business-gateway timeout [url:/dscloud/1.0/duty-cash](http://url/dscloud/1.0/duty-cash) |  |  | 大表 删除数据 |
| 6 | business-gateway timeout [url:/dscloud/1.0/ds/refund/RefundLedgerList](http://url/dscloud/1.0/ds/refund/RefundLedgerList) |  |  | 暂不处理，大in |
| 7 | business-gateway timeout [url:/dscloud/1.0/ds/order/upOrderBatchNo](http://url/dscloud/1.0/ds/order/upOrderBatchNo) |  |  | 暂不处理，拣货接口，校验追溯码 |
| 8 | [url:/dscloud/1.0/ds/baseinfo/getAuthedDeliveryName/500001](http://url/dscloud/1.0/ds/baseinfo/getAuthedDeliveryName/500001) |  |  | 今天下班之前清理 |
| 9 | business-gateway timeout [url:/b2c/1.0/order/setting/getPrescription](http://url/b2c/1.0/order/setting/getPrescription) |  |  | 今天下班之前清理 |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** 1.微商城积分订单下单库存问题2.配合商品处理B2C库存占用问题3.调整O2O\B2C 大于30订单新增问题4.定位运维费缺失问题-待修改5.售后中台技术方案-30%6.组合商品占用拆单问题定位 | **遗留问题**售后中台技术方案**风险问题** | **需求研发**售后中台技术方案**技术建设** |  |
| 2 |  | **本周总工时：5d**1. hana归档库数据迁移至线下单库。问题解决:   1. 排查科传无法处理的订单，脚本上线。 929单已处理完成   2. 【历史问题】科传金额问题、海典拆零问题、海典将线上单当做线下单传入，数据已经修复   3. 会员和消费记录ES刷数(脚本优化上线) 2. 排查科传无法处理的订单，脚本上线。 929单已处理完成 3. 【历史问题】科传金额问题、海典拆零问题、海典将线上单当做线下单传入，数据已经修复 4. 会员和消费记录ES刷数(脚本优化上线) 5. 海典POS会员消费记录开发, 50% | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：5d**1. Bug修改 2. PDD上线 | **遗留问题**  3 incomplete **PDD复盘、切店流程**   4 incomplete **接新平台流程文档**   **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. 加盟B2B支持   1. 增加订单明细级别的冷链标识   2. 订单日志优化   3. bug修复   4. 增加launch_user_id   5. 其他bug修复 2. 增加订单明细级别的冷链标识 3. 订单日志优化 4. bug修复 5. 增加launch_user_id 6. 其他bug修复 | **遗留问题**1. launch_user_id适配 **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d** | **遗留问题** **风险问题** | **需求研发****技术建设** |  |
| 6 |  | **本周总工时：5d**1.拼多多BUG修复2.拼多多上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1. 交易中台测试支持  a. 购物车及下单区分冷链商品修改 b. 增加售后单号透传修改 c. 其它排查修改 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. logback转log4j2 2. 平台对账开发 30% 3. B2C订单查询金额Bug修复 下周上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：3.5d**1. 支付中台自测问题修复   1. 超时支付单关闭状态查询补偿   2. 补偿三方状态查询异常处理：三方失败时使用本地状态   3. 修复日志打印空值对象显示null问题   4. 修复退款定时补偿状态，退款单没有生成问题   5. 重写退款逻辑，三方同步失败任然记录退款信息，有支付中台进行后续补偿   6. 修复单号过长精度丢失问题   7. 调整回调线程池，任务策略改为丢弃 2. 超时支付单关闭状态查询补偿 3. 补偿三方状态查询异常处理：三方失败时使用本地状态 4. 修复日志打印空值对象显示null问题 5. 修复退款定时补偿状态，退款单没有生成问题 6. 重写退款逻辑，三方同步失败任然记录退款信息，有支付中台进行后续补偿 7. 修复单号过长精度丢失问题 8. 调整回调线程池，任务策略改为丢弃 9. 添加售后单号 10. 支付中台总体设计文档补充 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- |
| 1 | 系统资源 | 需要 |  |
| 2 | 稳定性建设 | 需要 |  |
| 3 | 风险预警 | 暂定 |  |
| 需要 |  |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |  |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) | 无 |
| 5 | CaseStudy |  |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 | trueSELECT  id,order_no,order_state,erp_state,third_platform_code,third_order_no, third_order_id,third_order_state,off_state,freight_order_no,mer_code,client_code, online_store_code,online_store_name,organization_code,organization_name, delivery_time_type,delivery_time_desc,buyer_remark,buyer_message,seller_remark, lock_flag,lock_msg,locker_id,remind_flag,buyer_name,receiver_lat,receiver_lng, acceptor_id,acceptor_name,accept_time,picker_id,picker_name,pick_operator_id ,pick_operator_name,pick_time,canceller_id,canceller_name,cancel_reason, cancel_time,ex_operator_id,ex_operator_name,ex_operator_time,complete_time, created,day_num,modified,erp_adjust_no,erp_sale_no,self_verify_code, prescription_flag,bill_time,create_time,modify_time,call_erp_flag, member_no,transfer_delivery,client_conf_id,bill_operator,appointment, appointment_business_flag,appointment_business_type,request_deliver_goods_result, deliver_goods_refuse_reason,is_prescription,prescription_status, is_push_check,new_customer_flag,integral_flag,need_invoice,invoice_title, invoice_type,invoice_content,taxer_id,source_online_store_code, source_online_store_name,source_organization_code,source_organization_name, complex_modify_flag,medical_insurance,service_mode,cancel_bill_times, wsc_ext_json,top_hold,order_type,order_is_new,order_pick_type, source_channel_type,migration_order_no,extend_info,data_version, remark,deleted,pay_time  FROM order_info   WHERE  deleted=0 AND erp_state IN (30,99)  AND order_state IN (30,40,100) AND create_time >= '2025-03-01 00:00:00.0'   AND service_mode = 'O2O' LIMIT 0,100;   SELECT COUNT(1) FROM order_info WHERE deleted = 0 AND erp_state IN (30, 99)  AND order_state IN (30, 40, 100) AND create_time >= '2025-03-01 00:00:00.0' AND service_mode = 'O2O'; |  |  |  |
| 2 | trueselect 1 status,count(1) as num  from  oms_order_info a  where  1=1   and a.mer_code='500001'   and a.order_status in (5,10,30,40)   and a.deleted=0    and a.is_post_fee_order = 0   and a.ex_status = 1  and a.created >= ADDDATE( ADDDATE( curdate(), INTERVAL - 1 MONTH ), INTERVAL 1 DAY ) and a.created < ADDDATE(CURDATE(),INTERVAL 1 day)and a.warehouse_id in   ( 'WSC1111'    ) and a.online_store_code in   ( 'WSC1111'   , |  |  |  |
| 3 |  |  |  |  |
| 4 |  |  |  |  |
| 5 |  |  |  |  |
| 6 |  |  |  |  |
| 7 |  |  |  |  |
| 8 |  |  |  |  |
| 9 |  |  |  |  |
| 10 |  |  |  |  |
| 11 |  |  |  |  |
| 12 |  |  |  |  |
| 13 |  |  |  |  |
| 14 |  |  |  |  |