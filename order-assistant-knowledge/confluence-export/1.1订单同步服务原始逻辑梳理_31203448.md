# 1.1订单同步服务原始逻辑梳理

创单服务重构开发 

### 创单服务整体逻辑

创单服务整体逻辑INLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.service.rabbit.OrderMessageConsumer#process

start
package #SeaShell 1. 参数处理及校验 {
  :前置校验;
  :原始消息加工,信息补充;
  note #Plum: 关于明细判断,原始逻辑是没有提前的,\n这是为什么?是有什么历史原因吗?


  split
    :日志打印;
  split again
    :组合商品拆开,\n统一为明细;
  '  note: 这里的操作其实类似是\n基于订单模型来做,但是对象没有拆开,\n只是做修改
  split again
    :处方图片处理;
  split again
    :坐标转换;
  end split

}

package #SeaShell 2. 核心业务处理 {
  :统一封装责任链参数;
  note left #aqua: 不使用Object类,\n使用明确类型的对象,\n移除强转逻辑
  note right: 1. 原始生单Message \n 2. 线上门店信息(包含线下)【通用】 \n 3.门店下账配置

  -> 开始执行责任链,从左到右依次执行;
  fork
  :订单保存-存在性;

  fork again
  :订单保存-订单状态、保存;

  fork again
  :订单保存-订单后置处理;

  fork again
  :订单保存-订单路由转单处理;

  end fork
}
package #SeaShell 3. 异步业务 {
  :生产订单更新的异步消息;
}
stop
@enduml



### 订单责任链-1-SaveOrderExistsCheckHandler

订单责任链-1-SaveOrderExistsCheckHandlerINLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.v2.handlers.SaveOrderExistsCheckHandler#handlerRequest


:订单保存-存在性;

start


if(订单是否存在) then (不存在)
   #Aquamarine :下一个责任链;
  end
else (存在)
  :订单状态映射(status->omsStatus);
  :重建订单支付信息;
  note right: 异步任务的形式处理
  if(是否是京东到家订单) then (是)
    #pink :重新创建京东到家订单;
     :基础请求参数校验;
     package 修改逻辑(内存) {
      if(线下门店入参为空) then(是)
       :订单信息设置为门店异常、待接单;
      endif
     }
     package 订单非门店异常 {
      :校验商品;
      note: 根据商品信息,\n设置内存上下文数据\n,给下游逻辑使用;
     }
     package re创建订单 {
      :先释放原有库存;
      note: CommodityStock表,先保存,\n再调用接口,\n然后根据接口来更新本地表;
      :删除原订单中的相关表;
      :保存新的订单信息;
      note #red: 里面还包含req,深度这么深了,还包含...
      :关联旧的epr商品拣货货位调整单编号;
       if(lockFlag) then(有)
        :发送异常订单声音;
       endif
     }
     :重新锁定库存;
     if(是否需要核验处方药) then(是)
          :如需要,则发送审方单标识;
          :发送京东到家货物调整订单声音消息;
     endif
     :如需要,则自动打印小票;
     :如需要,则发送新订单消息,呼叫骑手;
     :发送京东调整单消息;

      if(是否配置了接单是否锁库存) then(否)
       :note:停止后续流程;
      endif

      if(是否为待接单且开启自动接单) then(是)
       :调用自动接单接口;
       :更新订单为待拣货;
      endif

  else (不是)
    #pink :更新订单;
     :校验更新时间;
     package 更新订单信息{
      :平台下发订单状态为完成或者配送中时，\n判断订单是否拣货 没拣货不修改订单状态;

      if(平台订单已完成或者配送中) then(是)
        if(订单是否拣货) then(否)
          :订单未拣货,不修改订单状态;
        endif
      endif

      if(订单是否已转仓) then(是)
        :封装数据,推送到HEMS;
        note: 主要是封装数据;
      endif
      :;

      if(是否满足修改条件) then(否)
        :return;
      endif

      if (是否是微商城订单?) is (否) then
        :更新支付信息的交易佣金;
      endif

      package 订单信息赋值及更新 {
        :常规字段设置;

        switch (更新状态)
          case (配送中)
            :订单状态设置为配送中;
            if (拣货时间为空?) is (是) then
              :添加发货提醒;
            endif
            :移除所有提醒;
          case (订单取消)
            if (订单已取消或者已退款?) is (否) then
              package undoErpForCancel方法 {
                if (是否调用ERP接口标识?) is (yes) then
                  if(订单ERP状态) then(已下账)
                    :生成退款单;
                    note: 创建部分或者全部退款;\n 这个是专门取消逻辑的退款单创建\n\n*逻辑简单,基本上是赋值操作及一些金额的计算逻辑
                  elseif(订单ERP状态) then(待下账)
                    :释放库存;
                    :更新订单状态;
                  elseif(订单ERP状态) then(待锁定)
                    :释放库存;
                    :更新订单状态;
                  endif
                else (no)
                  :return null;
                endif
              }
            endif
            :订单字段设置;
            :订单取消更新配送记录;
            note:1.如果有呼叫骑手则取消;
            :移除所有提醒;
            :如果是同城购平台,播放取消订单声音;
          case (已完成)
            :订单字段设置;
            :移除所有提醒;
            :播放确认搜达声音;
          case (订单退款)
            if(订单状态) then (<=已完成)
              :订单字段设置;
              :订单取消更新配送记录;
            endif
            package undoErpForThirdPlatform{
              :基础前置校验;
              :更新订单下账状态为已取消;
              :释放库存;
            }
            :移除所有提醒;
          case (取消订单待审核)
            if(是否是京东健康) then(是)
              :锁定标志设置为取消锁定;
            endif
        endswitch
        :同城购特殊逻辑处理;
        note:特殊逻辑:根据一些字段标识来判断同城购是否需要呼叫运力
        :订单更新消息入库;
        if(更新成功且订单状态字段发生变更) then(是)
          :记录关键日志;
          if(运费单号) then(有值)
            :处理运费单(存在order_info表中,所以适用之前的orderInfo逻辑);
            note: 1.订单状态已取消、已关闭且下账状态是已下账,无退款单时,则创建退款单\n2.订单状态未配送中或者已完成,下账状态为未下账时,则更新订单下账状态为待下账
'            note:cn.hydee.middle.business.order.v2.manager.ErpBillManager#undoErpForCancel(cn.hydee.middle.business.order.entity.OrderInfo, java.lang.Long);
            :日志记录;
          endif
        elseif(更新成功且订单状态字段发生未发生变更) then(是)
          if(京东健康且是取消订单待审核) then(yes)
            :播放取消订单声音 ;
          endif
        endif

        '处方信息处理
        :处理处方信息;
        :服务商订单状态更新推送SRM;
      }
     }
     ' todo
     :更新后置处理;
     package 更新后置处理 {
      :按需更新订单信息;
      if(订单状态已取消或者已关闭时) then(是)
        ' cn.hydee.middle.business.order.service.impl.ob.cloud.shelf.CloudShelfServiceImpl#cancelDeliverGoods
        :调用OMS取消供货商发货接口;
      endif
      :更新订单收货信息;
      if(饿白订单且是自配送) then(是)
        'cn.hydee.middle.business.order.service.impl.OrderChangeSelfDeliveryServiceImpl#callRiderAfterChangeSelf
        :饿百订单转自配送骑手呼叫;
      endif
      :更新后自动下账逻辑处理;
      note: 其实就是冗余了下账逻辑;\n这一块可以异步处理;
     }
  endif
  :发送订单更新异步消息;
endif

stop
@enduml



### 订单责任链-2-SaveOrderStatusGetHandler

订单责任链-2-SaveOrderStatusGetHandlerINLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.v2.handlers.SaveOrderStatusGetHandler#handlerRequest

start


:订单保存-订单状态、保存;

if(平台0元单和状态判断) then(true)
  :return null;
endif

:订单状态映射(status->omsStatus);
note #red: 重复逻辑,上一个过滤器也有

:订单初始状态设置;

if(非门店异常) then(是)
  :校验商品;
  note: 这个是当前过滤器自己写一个校验逻辑;
endif

:状态等字段给到下一个过滤器使用;

stop
@enduml

### 订单责任链-3-SaveOrderHandler

订单责任链-3-SaveOrderHandlerINLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.v2.handlers.SaveOrderHandler#handlerRequest

start

:订单保存-订单保存处理;

:保存订单;

if(门店异常) then(是)
  :播放异常单消息;
  :return null;
endif

:redis 锁住订单;

if(上一个过滤器的flag值) then(true)
  if(京东到家且三方状态为取消订单待审核) then(是)
    :播放取消订单声音;
  endif
  :return null;
endif

:创建收货地址解密任务;

stop
@enduml

### 订单责任链-4-RouteOrderHandler

订单责任链-4-RouteOrderHandlerINLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.v2.handlers.RouteOrderHandler#handlerRequest

start

:订单保存-订单路由转单处理;

:跑转到规则;
:规则输出结果设置到订单;
:更新订单信息;
:责任链上下文信息更新;
:记录转单日志;

stop
@enduml

### 订单责任链-5-SaveOrderBehindHandler

订单责任链-5-SaveOrderBehindHandlerINLINE@startuml
'https://plantuml.com/activity-diagram-beta
'https://www.w3schools.com/colors/colors_names.asp 颜色

'代码位置 cn.hydee.middle.business.order.v2.handlers.SaveOrderBehindHandler#handlerRequest

start

:订单保存-保存订单后置处理;

if(京东到家订单) then(是)
  :直接更新订单处方状态为通过;
else(否)
  :处方信息给三方审方平台审,需要调用接口;
  :根据三方响应结果更新订单信息;
endif

:按照配置进行处理 如果配置了创建新订单自动锁库存 则进行扣减库存操作①; 
note:预约单不锁库存; 转仓到 HEMS的不锁库存;
:按照配置进行判断是否自动打印小票;
:呼叫骑手;
:client新订单声音通知;

if(omsStatus为等待接单且自动接单关闭) then(是)
  :更新订单:接单时间置为null;
endif

:拣货提醒;

if(omsStatus为等待接单且自动接单打开) then(是)
  if(处方单且配置了当前平台) then(否)
    :调用订单确认接口;
    :更新订单为带拣货;
    :记录日志;
  endif
endif

if(微商城未切店且开启了自动接单)
  :记录日志;
endif

:自动拣货逻辑;
note: 根据配置,发起自动拣货延时消息,到时间后触发;

'note: 这个依赖本过滤器的前置商品中台接口返回数据来判断,\n如果前置接口数据有问题,则通知商品中台
if(①返回商品有异常) then(是)
  :通知商品中台 商品存在问题;
  note #LightPink:逻辑很深;\n 涉及到CommodityStock表的操作 \n1.订单不存在\n\t1.order_detail表\n\t2.CommodityExceptionOrder\n\t3.更新订单主表\n\t4.记录日志\n2.订单商品异常\n\t1.order_detail表\n\t2.CommodityExceptionOrder\n\t3.更新订单主表\n\t4.商品分析消息发送\n\t5.再次更新订单主表lockMsg\n\t6.记录日志\n3.如果触发熔断,则发送延时消息重试
endif

:供应商自动转单;

stop
@enduml