# 【20241030】B2C列表查询优化【已上线】

### 项目及分支:

**hydee-business-order-web**

release-b2c-query-optimize-20241101

分支主要变动:sql优化、添加索引、并行


**order-atom-service**

release-b2c-query-optimize-20241104-esIndex

### 添加索引

ALTER TABLE platform_refund_order ADD INDEX idx_group_status_type_ectype_client
(groupid, status, refund_type, ectype, clientid);

ALTER TABLE oms_order_info ADD INDEX idx_mer_order_status_comp (
    mer_code,
    order_status,
    order_owner_type,
    deleted,
    is_post_fee_order,
    ex_status,
    created
);

---

### 重建B2C索引

| 字段 |  | 是否自测 |
| --- | --- | --- |
| id | oms_order_info 表主键ID。不使用id,使用oms_order_no作为主键 |  |
| oms_order_no |  |  |
| deleted |  |  |
| mer_code |  |  |
| order_owner_type |  |  |
| supplier_code |  |  |
| order_status |  |  |
| logisticOrderOfLogisticConfigId | and exists ( SELECT 1 FROM logistic_order lo             left JOIN logistic_config_info lci on lci.id = lo.logistic_config_id             where lo.oms_order_no = a.oms_order_no and lo.status = 1             <if test="param.templateId != '' and param.templateId != null">                 and lci.standard_template_id = #{param.templateId}             </if>             <if test="param.printPlatformCode != '' and param.printPlatformCode != null">                 and lo.platform_code = #{param.printPlatformCode}             </if>             )理论上是1对1，但是数据库有脏数据select oms_order_no,status,count(1) c from logistic_order where `status` = 1 group by oms_order_no,status having c > 1;-- 30多条脏数，  33 incomplete 俊峰会在上线前处理  SELECT lo.oms_order_no,lo.status,count(1) c FROM logistic_order lo  left JOIN logistic_config_info lci on [lci.id](http://lci.id) = lo.logistic_config_id where lo.status = 1  GROUP BY lo.oms_order_no,status having c > 1  66 incomplete 有多物流单需求,主要是处理补发单和漏单的场景。需要改成List |  |
| logisticOrderOfPlatformCode |
| logisticOrderOfStatus |
| logisticConfigInfoOfStandardTemplateId |
| created |  |  |
| pay_time |  |  |
| audit_time |  |  |
| ship_time |  |  |
| cancel_time |  |  |
| spread_store_code |  |  |
| is_post_fee_order |  |  |
| ex_status |  |  |
| create_time |  |  |
| express_id |  |  |
| warehouse_id |  |  |
| is_prescription |  |  |
| third_platform_code |  |  |
| third_order_no |  |  |
| oms_ship_no |  |  |
| express_number |  |  |
| buyer_name |  |  |
| buyer_message |  |  |
| seller_remark |  |  |
| order_type |  |  |
| split_status |  |  |
| ship_status |  |  |
| sheet_status |  |  |
| remark |  |  |
| online_store_code |  |  |
| online_store_type | 对应classifyId字段的查询这个店铺所属的平台classifyId,所以ok	<sql id="existStoreType">         <if test="param.classifyId != null and param.classifyId != ''">             AND EXISTS (             SELECT             1             FROM             ds_online_store dos             INNER JOIN ds_online_store_config dosc ON dos.id = dosc.online_store_id             AND FIND_IN_SET(#{param.classifyId}, dosc.store_type )             WHERE dos.mer_code = a.mer_code             AND dos.online_store_code = a.online_store_code             )         </if>     </sql> |  |
| goods_qty |  |  |
| goods_category_qty |  |  |
| tagEmptyOrderStatus | tag字段里面存储的是json<if test="param.tag.emptyOrder != null or param.tag.mergeOrder != null or param.tag.preSellOrder != null or param.tag.modifyAddressTag != null">                 and (                 JSON_EXTRACT(a.tag, '$.emptyOrder.status') = #{param.tag.emptyOrder.status}                 or                 JSON_EXTRACT(a.tag, '$.mergeOrder.status') = #{param.tag.mergeOrder.status}                 or                 JSON_EXTRACT(a.tag, '$.preSellOrder.status') = #{param.tag.preSellOrder.status}                 or                 JSON_EXTRACT(a.tag, '$.modifyAddressTag.status') = #{param.tag.modifyAddressTag.status}                 )             </if> |  |
| tagMergeOrderStatus |
| tagPreSellOrderStatus |
| tagModifyAddressTagStatus |
| stock_state |  |  |
| sendorder_print_num |  |  |
| refund_count | (SELECT         COUNT(ro.id)         FROM         refund_order ro         WHERE         <![CDATA[ ro.state != 102 AND ro.state != 103 ]]> and         ro.oms_order_no = a.oms_order_no         ) |  |
| erp_audit_status |  |  |
| erp_code_list |  |  |
| platform_order_info 平铺与oms_order_info一对一UNIQUE KEY `u_idx_olono_ectype` (`ectype`,`olorderno`) USING BTREE |  |
| platform_order_info.havecfy | where poi.olorderno = a.third_order_no and poi.ectype=a.third_platform_code |  |
| platform_order_info.rx_audit_status |  |  |
| order_pay_info 平铺与oms_order_info一对一select oms_order_no,count(1) c from order_pay_info where oms_order_no != 0 group by oms_order_no having c > 1; |  |
| order_pay_info.pay_type |  |  |
| order_pay_info.buyer_actual_amount |  |  |
| order_delivery_address 平铺与oms_order_info一对一select oms_order_no,count(1) c from order_delivery_address where oms_order_no != 0 group by oms_order_no having c > 1; |  |
| order_delivery_address.receiver_mobile |  |  |
| order_delivery_address.receiver_name |  |  |
| order_delivery_address.full_address |  |  |
| 异常明细oms_order_ex 1对多select * from oms_order_ex where oms_order_no = '1793206636368538374' and operate_status = 0，不同的type |  |
| List<OMS订单异常明细 oms_order_ex> | 脏数据 select oms_order_no,operate_status,ex_type, count(1) c from oms_order_ex  where operate_status = 0  group by oms_order_no,operate_status,ex_type having c > 1    select * from oms_order_ex where operate_status = 0 and oms_order_no in (  '1809084463939123209',  '1809138118630182659'  )  42 incomplete 待处理 |  |
| 订单明细 |  |
| List<OMS订单明细 order_detail> | 订单明细 |  |


### OMS订单明细 order_detail

|  |  |
| --- | --- |
| status |  |
| oms_order_no | 关联oms_order_info表。在构造条件索引时使用 |
| erp_code |  |
| commodity_name |  |


### OMS订单银行明细 oms_order_ex

|  |  |
| --- | --- |
| ex_type |  |
| operate_status |  |


#### 其他待处理接口

  13 incomplete B2C订单处理-退款处理 /refundProcessing/list 主表是platform_refund_order 需要重建索引 → 暂时不考虑   14 complete B2C订单处理-异常/order/page/exception    15 complete B2C订单处理-其他tab   16 complete 订单查询   17 incomplete B2C平台订单，**暂时无需处理,单表查询,直接添加DB索引。在release-b2c-query-optimize-20241101 分支优化**  18 incomplete 查询列,如有时间可以优化     19 incomplete B2C平台退款单，**暂时无需处理,单表查询,直接添加DB索引。在release-b2c-query-optimize-20241101 分支优化**  20 incomplete 查询列,如有时间可以优化     21 incomplete B2C售后单，主表是after_sale_order。**数据量较小,暂无需接入ES。在release-b2c-query-optimize-20241101 分支优化**   22 incomplete B2C退货单，主表是return_goods_order。**数据量较小,暂无需接入ES。在release-b2c-query-optimize-20241101 分支优化**   23 complete B2C下账列表，未来废弃，已经有新的列表   24 complete B2C下账列表新,已走ES  

**请求URL梳理**

  34 complete /order/page/normal OrderPageReqDto  35 complete db主要查询oms_order_no和client_code,然后再回表查其他信息   36 incomplete 回表查其他信息的sql可以优化    50 complete order-web部分调用     37 complete /order/erp/audit/count OrderPageReqDto  48 complete order-web部分调用     38 incomplete /refundProcessing/list RefundOrderDto → 暂时不考虑重建索引   39 complete /order/page/exception OrderPageOtherReqDto  45 complete order-web部分调用     40 complete /order/list OrderListQueryDto  46 complete order-web部分调用    

#### 需要监听的表

  58 complete logistic_order   59 complete refund_order   60 complete platform_order_info   61 complete order_pay_info   62 complete order_delivery_address   63 complete oms_order_ex   64 complete order_detail  

### 上线checkList

**SDK**

xml<dependency>
    <groupId>com.yxt.order.atom.sdk</groupId>
    <artifactId>order-atom-sdk</artifactId>
    <version>1.5.0-SNAPSHOT</version>
</dependency>

<dependency>
	  <groupId>com.yxt.order.common</groupId>
      <artifactId>order-common</artifactId>
      <version>1.5.0-SNAPSHOT</version>
</dependency>

<dependency>
    <groupId>com.yxt.order.types</groupId>
    <artifactId>order-types</artifactId>
    <version>1.4.0-SNAPSHOT</version>
</dependency>

### Apollo配置

order-atom-service

canal:
  # b2c-订单
  b2c-order: b2c_order_info_canal_to_kafka_topic_pro

### canal admin配置

canal.instance.filter.regex=dscloud.oms_order_info,dscloud.logistic_order,dscloud.platform_order_info,dscloud.order_pay_info,dscloud.order_delivery_address,dscloud.oms_order_ex,dscloud.order_detail,dscloud.refund_order

### 清理脏数据

-- 改status即可,不物理删除
select oms_order_no,ex_type,count(1) c from oms_order_ex where operate_status = 0  group by  oms_order_no,ex_type having c >1
select oms_order_no,status,count(1) c from logistic_order where `status` = 1 group by oms_order_no,status having c > 1;

#### 疑问

SELECT
 dosc.store_type
 FROM
 ds_online_store dos
 INNER JOIN ds_online_store_config dosc ON [dos.id](http://dos.id) = dosc.online_store_id
 WHERE dos.mer_code = '500001'
 AND dos.online_store_code = 'WSC1111' and dosc.store_type is not null

测试环境有2条数据，53和null, 生产环境无。问一句具体的逻辑。还有null在构建条件索引是已经删除了