# 24年第20周2024-06-07

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分4月25日讨论完O2O订单域核心服务入参  5月24日进入开发阶段 | 创单服务 -润康拣货/换货 (含正向单下账) 国华配送信息更新 - 杨花申请售后 /售后服务 (含逆向单下账) 国枫 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审4月24日技术方案二次评审1. 抖店部分：   - 4月26日进入开发阶段   - 5月17日抖店开发完成，待部署测试   - 5月22日下班前提测，测试中   - 6月4号上线 2. 4月26日进入开发阶段 3. 5月17日抖店开发完成，待部署测试 4. 5月22日下班前提测，测试中 5. 6月4号上线 6. 饿了么：   - 5月28号进入开发阶段   - 开发阶段预计6月底上线 7. 5月28号进入开发阶段 8. 开发阶段预计6月底上线 9. 支付宝：   - 联调阶段 预计20号提测 10. 联调阶段 预计20号提测 |  |  |
| 3 | middle-id升级 |  |  | 暂停 | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 4 | xxl-job执行器优化专项 |  | 4月12日完成待办列表5月21日前完成规范文档 | 暂停 | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 5 | 支付中台重构 |  |  | 暂停 |  |
| 6 | [需求池](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgFQpYWtHhRbWSdBxb5u?scode=AOsAFQcYAAcFpJng4uAboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 7 | [每周上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |
| 8 | [每周值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2) |  |  |  |  |
| 9 | [2024Q2-交易生产组](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgA9Okm682QqKMEwhssg?scode=AOsAFQcYAAc3lcl7J9AboAOAYLADg&tab=9cjz4i) |  |  |  |  |


### 二、本周工作情况

**1.总览 本周产研目标**

| 事项 | 目标 | 当前进度 | 跟进人 |
| --- | --- | --- | --- |
| 对接极兔/邮政 | 周四上线 | 周四上线 | 钰斌 |
| B2C员工业绩返利 | 周四上线 | 周四上线 | 国枫 |
| B2C退款流程优化 | 周一技术方案评审 | 开发中30% 6月19日提测 |  |
| 订单优化2效率提升 | 周二PRD评审 | 下周三技术方案评审 |  |
| 虚拟商品需求 | 周二PRD评审 | 开发中0% 6月19日提测 |  |
| B2C手工单 功能修复 | 周内上线 | 周二上线 | 国华 |
| 吉客云 | 暂停 | 暂停 |  |
| 支付宝对接 | 暂停 | 暂停 |  |



本周其他目标

| 事项 | 当前进度 | 跟进人 |
| --- | --- | --- |
| B2C手工单修复 | 已处理 | 国华 |
| 组织结构冗余 | 已处理.历史数据已修复, ；/正逆单全路径冗余 |  |
| es/mongo/db 数据量文档填写 | 已处理 | 国华 润康 |
| 订单推送消息改造指定 userid | 已处理 | 国华 |
| 云仓订单物流信息回传重写逻辑 | 开发中20% | 国华 |
| 广播模式mq迁移 | 进度0% |  |


**2、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d** | **遗留问题** **风险问题**   **** | **需求研发** **技术建设** **** |  |
| 2 | 杨润康 | **本周总工时：5d**1. 创单服务开发   1. 聚合根定义,完成消息到聚合根的开发、参数转换-适配服务模式逻辑   2. 拆分出order-framework,包含order-common、order-types 2. 聚合根定义,完成消息到聚合根的开发、参数转换-适配服务模式逻辑 3. 拆分出order-framework,包含order-common、order-types 4. 线下单   1. 海典线下单需求测试(环境配置、问题排查、按MQ命名规范调整命名) 进行中   2. 线下单迁移字段映射整理 已完成   3. 迁移历史数据(使用datax) 已完成   4. 从归档数据迁移至线下单表,开发中(20%) 5. 海典线下单需求测试(环境配置、问题排查、按MQ命名规范调整命名) 进行中 6. 线下单迁移字段映射整理 已完成 7. 迁移历史数据(使用datax) 已完成 8. 从归档数据迁移至线下单表,开发中(20%) | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 | 杨俊峰 | **本周总工时：** 5d1.B2C 问题处理 2day2.支付宝迁移 2day3.订单优化提供接口 0.5 day | **遗留问题**1.打印模糊，天津K019发货单从中间开始打印跨纸张。（尽快替换打印方案，放弃图片转印直接使用原生打印）2。支付宝处于联调阶段，预计月中提测**风险问题** **** | **需求研发**1.支付宝提测出去2.打印替换技术方案**技术建设****** |  |
| 4 |  | **本周总工时：5day** | **遗留问题** **风险问题****** | **需求研发** **技术建设****** |  |
| 5 |  | **本周总工时：** | **遗留问题** **风险问题** **** | **需求研发** **技术建设****** |  |
| 6 |  | **本周总工时：5day**1.订单作业优化技术文档2.订单作业优化开发a.异常处理换货 70%b.评价拉回 30%c.B2C组合商品分摊优化d.全局预警设置 50%3.线上运维 面单加密、动态配置优化 | **遗留问题**a.异常处理换货 b.评价拉回 c.B2C组合商品分摊优化d.全局预警设置 **风险问题** **** | **需求研发** **技术建设****** |  |
| 7 |  | **本周总工时：5d** 1. B2C退款/取消优化 60% 2. 海典B2C下账增加成本中心 代码开发完成 等待海典与derp联调完成。 2. 日常订单运维 a.微商城退款单 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：5d**1. 海典调拨单对接  a. 海典推送调拨单接口对接完成 b. 提供海典回调接口（暂未对接） 2. B2C订单取消/退款优化 | **遗留问题** **风险问题** | **需求研发**1. 海典调拨单对接 2. B2C订单取消/退款优化 **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 商家主动部分退款，B2C订单金额分摊，复杂换货，订单预警技术方案及PRD评审 【完成】 2. 商家主动部分退款开发【40%】   1. 商品拆分   2. 价格均摊计算   3. 重算下账金额   4. 重算财务信息 3. 商品拆分 4. 价格均摊计算 5. 重算下账金额 6. 重算财务信息 7. 解决圆通获取面单异常 | **遗留问题** **风险问题** | **需求研发**1. 商家主动部分退款，B2C订单金额分摊，复杂换货，订单预警 **技术建设** |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |


### 五、本周异常告警&线上问题

[交易生产值班问题](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcKt4f2ek)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |