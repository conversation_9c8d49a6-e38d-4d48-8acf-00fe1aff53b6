# 线下单上预发新增需求上线CheckList

新增Job

refundOrderFindOrder404Handler 退单订单查不到对应的正单补偿任务处理
user404Handler 通过用户卡号查不到用户补偿任务

每小时执行一次: 0 0 */1 * * ?

新增配置

# 新增分表
        offline_refund_order_user:
          actual-data-nodes: order-offline.offline_refund_order_user_$->{0..255}
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm
# 新增mq
mq:
  topic:
    producer:
      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD
      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC

# 新增限流 order-service、order-atom-service:
spring:
  cloud:
    sentinel:
      filter:
        enabled: true
      web-context-unify: false # 关闭context整合 避免链路失效 默认会由context为根链路
      eager: true #启动立即加载规则，而不是懒加载
      transport:
        dashboard: dev-sentinel.hxyxt.com
        port: 8719 #dashboard通信端口，如果冲突则会自动+1 寻找可用端口
        heartbeat-interval-ms: 5000 #心跳秒数
      datasource:
        flow-rule:
          nacos:
            #流控规则的nacos配置文件 dashbard上报推送的是这个格式
            data-id: ${spring.application.name}-flow-rules.json
            #流控规则格式 XML OR JSON dashbard配置的json
            data-type: json
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            group-id: SENTINEL_GROUP
            server-addr: 10.4.3.210:8848
            #规则类型控制规则更新的策略
            rule-type: flow
        param-flow-rule:
          nacos:
            data-id: ${spring.application.name}-param-rules.json
            data-type: json
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            group-id: SENTINEL_GROUP
            server-addr: 10.4.3.210:8848
            rule-type: param-flow
        authority-rule:
          nacos:
            data-id: ${spring.application.name}-authority-rules.json
            data-type: json
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            group-id: SENTINEL_GROUP
            server-addr: 10.4.3.210:8848
            rule-type: authority
        system-rule:
          nacos:
            data-id: ${spring.application.name}-system-rules.json
            data-type: json
            namespace: 13949598-55d3-46ac-b0e8-42a9c30a1601
            group-id: SENTINEL_GROUP
            server-addr: 10.4.3.210:8848
            rule-type: system

表结构变更

【20240813】

退单是否参加促销-未上线.sql

【20240814】