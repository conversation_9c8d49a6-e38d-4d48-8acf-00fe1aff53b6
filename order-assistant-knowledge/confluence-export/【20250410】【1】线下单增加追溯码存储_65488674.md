# 【20250410】【1】线下单增加追溯码存储

### Green已上线

### 需求文档:

### JIRA:

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5093

### 服务:

order-sync-service

order-atom-service

order-framework

- order-types


### SDK:

    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>traceCode-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.yxt.order.types</groupId>
      <artifactId>order-types</artifactId>
      <version>traceCode-SNAPSHOT</version>
    </dependency>




### 分支:

feature-trace-code

### 版本前缀:

traceCode-SNAPSHOT

### DB

正单明细添加索引

> 生产已经添加 20250418

ALTER TABLE `dscloud_offline`.`offline_order_detail_trace_${seq}` 
ADD INDEX `idx_order_no`(`order_no`) USING BTREE,
ADD INDEX `idx_sys_update_time`(`sys_update_time`) USING BTREE;

退款单明细追溯码表

sqlCREATE TABLE `offline_refund_order_detail_trace_${seq}` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单号',
  `refund_detail_no` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单明细唯一号',
  `erp_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品编码',
  `make_no` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品批号',
  `trace_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '追溯码',
  `nhsa_report_flag` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '医保上报标识',
  `dra_report_flag` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '药监上报标识',
  `created` datetime DEFAULT NULL COMMENT '平台创建时间',
  `updated` datetime DEFAULT NULL COMMENT '平台更新时间',
  `created_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',
  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',
  `version` bigint DEFAULT NULL COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_refund_no_detail_no` (`refund_no`,`refund_detail_no`) USING BTREE,
  KEY `idx_created` (`created`) USING BTREE,
  KEY `idx_sys_create_time` (`sys_create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='心云退单明细追溯码信息';

### Apollo

order-atom-service

# offline_order_detail_trace分表配置 ######dev环境已经配置，生产的环境上线是校验
        offline_order_detail_trace:
          actual-data-nodes: order-offline-$->{0..1}.offline_order_detail_trace_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm

# 退单明细追溯码表配置(New)
        offline_refund_order_detail_trace:
          actual-data-nodes: order-offline-$->{0..1}.offline_refund_order_detail_trace_$->{0..255}
          database-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderDatabaseShardingHintAlgorithm
          table-strategy:
            hint:
              algorithm-class-name: com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm  

### 跟着需求的优化项

  2 complete 平台创建时间调整【上线时间 2025-04-15 14:00:00】  

### TODO

  13 complete 检查是否有落后master提交。结论:无   14 complete 发布release包 1.26.0-RELEASE  15 complete order-types   16 complete order-atom-sdk     17 complete 配置pre apollo   18 complete 配置prod apollo   4 complete 检查offline_order_detail_trace表在生产apollo上是否配置分表规则。dev我看是有的，prod需要人工确认下  20 complete prod offline_order_detail_trace所有分表字段都有,且一致   24 complete prod offline_order_detail_trace 添加索引   25 complete offline_refund_order_detail_trace已经创建     6 complete 校验下全表字段: 与本次需求相关的  26 complete 明细几个价格字段。结论: 发现117所有分表都缺失