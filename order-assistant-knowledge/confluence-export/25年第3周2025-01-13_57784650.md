# 25年第3周2025-01-13

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | Q4总结 |  |  | 已完成 |
| 2 | 25年Q1 OKR |  |  | 已完成,但需要科普OKR |
| 3 | 转单场景下单门店查询?发货门店查询 |  |  | 已沟通清楚.体现在新模型解决 |
| 4 | 收货地址脱敏jira卡片 |  |  | 已完成[修改收货地址后脱敏处理](https://jira.hxyxt.com/browse/ORDER-4126) |
| 5 | 京东快递bug修复 |  |  | 年后处理，先做交易中台-购物车 |
| 6 | redis大key：oms:o2o:dscloud:ordercount:500001:AY91 |  |  |  |
| 7 | redis大key：pay_center:service:pay_service:channel_convert_platform:WEIXIN_refundpay_center:service:pay_service:channel_convert_platform:WEIXIN_pay |  |  |  |
| 8 | 慢sql处理 |  |  | 已处理，索引已添加上线 |
| 9 | 值班问题 |  |  | 已完成 |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) |  |  |  |


## 2、重点项目/专项进展与风险

|  | 重点项目/专项 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- |
| 1 | B2B加盟商城 | **交易中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，等待部分细节字段确定。  代码开发进度：完成20%代码开发。   **支付中台**:  技术方案评审：已完成。  一心助手对齐：已完成与一心助手对于D-ERP支付流程的对齐工作。  代码开发进度：预计下周进入开发。   **订单中台**:  技术方案评审：已完成。  一心助手对接：文档已提供给一心助手，等待双方对齐确认。  ERP/POS对接：提供了对接文档的初版，等待与相关方对齐确认。  代码开发进度：预计下周进入开发。 |  |  |
| 2 | 移动OMS一期 | 已上线代码,待刷数,待启用 |  |  |
|  | 会员消费记录 | 已上线 |  |  |


## 3、成员工作情况

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5d 1.订单一致性服务搭建 10%****2.线上订单退款数据刷数****3.一件代发上线配合****4.微商城生产新增收获地址BUG****5.B2C生产请货问题调整转正常单逻辑** | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 2 |  | **本周总工时：5d**1. 会员消费记录   1. 配合测试，上线，验证   2. 排查DB与ES数据量不等问题 2. 配合测试，上线，验证 3. 排查DB与ES数据量不等问题 4. 消费记录迁移：处理非7位会员号订单 5. 线下单修复脚本开发,20% 6. 其他   1. 订单通用能力文档编写   2. 订单分库分表文档编写   3. 支付中台技术方案讨论   4. 刷数通用逻辑重构,处理中 7. 订单通用能力文档编写 8. 订单分库分表文档编写 9. 支付中台技术方案讨论 10. 刷数通用逻辑重构,处理中 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 3 |  | **本周总工时：5d**1.订单完成态透传。 已上线 功能暂时未打开 2d2.B2B 商城文档 和业务梳理 3d | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 4 |  | **本周总工时：5d**1. 移动OMS测试、上线 2. B2B接口输出文档撰写 3. 新模型技术文档撰写 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 5 |  | **本周总工时：5d**1. 一件代发修复Bug 上线 2. 切换旗舰店 3. 客服中台技术方案编写 4. 线上问题排查 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 6 |  | **本周总工时：4.5d**1.内购商城备货中标识上线2.线上问题处理（海典下账单弥补的定时任务，已上线）3.值班问题处理（慢sql，已添加索引）4.交易中台-购物车开发（字段定义） | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 7 |  | **本周总工时：5d**1.交易中台开发 a. 接口定义完成 b. 下单流程代码框架搭建完成 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 8 |  | **本周总工时：4d**1. O2O下账补偿定时任务优化 2. B2C下账单列表偶发查询ES数量为0 3. 微商城订单订阅快递100失败未更新为已发货 4. 新增B2C新下账单列表ES同步任务 5. B2C列表优化ES Bug修复与上线 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |
| 9 |  | **本周总工时：5d**1. 余额支付技术设计 2. 一心助手接口对齐 3. 支付相关技术学习 | **遗留问题** **风险问题** | **需求研发** **技术建设** |  |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 一心助手事项 | 交易生产事项 | 本周实际情况概述 |
| --- | --- | --- | --- | --- |
| 1 | 系统资源 | [生产环境资源](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6376487) | 需要 |  |
| 2 | 稳定性建设 | [[一心助手] 核心链路稳定性建设](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoOBzdIWrdRWqVzHzAd2?scode=AOsAFQcYAAch6OMSDhAeoAxgY9ABo&tab=BB08J2) | 需要 |  |
| 3 | 风险预警 | 系统运行风险（参考：[一心助手项目·简报](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoS0D78UmYRkicHXKbaF?scode=AOsAFQcYAAcQ37SkBxAeoAxgY9ABo&tab=BB08J2)） | 暂定 |  |
| [项目风险预警](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6361502) | 需要 |  |
| 4 | 风险治理 | [[生产环境] 告警问题](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoA34W5hxAQfGNVlNmyi?scode=AOsAFQcYAAc5kkNq9rAdYAFgZCAMo&tab=koftou) | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |  |
| [[一心助手] 慢接口](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABo19tYe1xnQOW4nKBwN9?scode=AOsAFQcYAAcFYhTsavAeoAxgY9ABo&tab=BB08J2)（参考：[ELK慢接口统计](http://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） | [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |  |
| [[MySQL] 慢查询治理（参考：](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2)[MySQL慢查询大盘](http://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))[）](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2) | [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |  |
| [[Redis] 慢查询/大Key治理](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABoOXgkVImYRL0kxEKW9i?scode=AOsAFQcYAAcyNcZKgEAeoAxgY9ABo&tab=BB08J2)[（参考：](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2)[Redis大Key监控](http://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!((%27$state%27:(store:appState),meta:(alias:!n,disabled:!f,index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,key:instance,negate:!f,params:!(sk_pro_redis004_cluster,sk_pro_redis007_cluster),type:phrases),query:(bool:(minimum_should_match:1,should:!((match_phrase:(instance:sk_pro_redis004_cluster)),(match_phrase:(instance:sk_pro_redis007_cluster))))))),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc))))[）](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABorbjNFf6AQlWodM0FQV?scode=AOsAFQcYAAcp947MNBAeoAxgY9ABo&tab=BB08J2) | [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) (订单组redis cluster) |  |
| [[ES] 慢查询治理](https://doc.weixin.qq.com/sheet/e3_AeoAxgY9ABosg5k2fpzQTOGKf837R?scode=AOsAFQcYAAcqliRm10AeoAxgY9ABo&tab=BB08J2) | [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |  |
| 5 | CaseStudy | 生产事故复盘 |  |  |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等

1.OKR填写指南科普

2.请假的基本原则



# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | 郭志明 徐国华 | 订单新模型 |  |
| 2 |  |  |  |
| 3 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 | redis大key：oms:o2o:dscloud:ordercount:500001:AY91oms:o2o:dscloud:ordercount:500001:AJG5 | 暂不处理 |  |
| 2 | redis大key：pay_center:service:pay_service:channel_convert_platform:WEIXIN_refundpay_center:service:pay_service:channel_convert_platform:WEIXIN_pay | 暂不处理，随着支付中台上线，此问题就解决了 |  |
| 3 | middle-order提交订单回滚问题(处方单-订单无法取消) |  |  |
| 4 | oms:o2o:dscloud:ordercount:999999:10101oms:o2o:dscloud:ordercount:888888:0909oms:o2o:dscloud:ordercount:888888:0001无效删除 |  |  |
| 5 |  |  |  |
| 6 |  |  |  |
| 7 |  |  |  |
| 8 |  |  |  |