# 25年第22周2025-05-30

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 预计完成时间 | 负责人 | 备注 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

| 重点项目/专项 | 本周进展 | 下周计划 |
| --- | --- | --- |
| 微商城-交易链路切换 | 交易中台 一、购物车模块链路迁移 （区分 O2O、B2C 【内购、积分 融合至一套流程】）50%  1. 购物车基础操作（获取、加购、减购、清空、删除） 60%  2. 营销优惠券计算 （营销接口调用、优惠计算赋值） 80%  3. 营销活动计算 80%  4. 会员优惠价格计算 30%  5. 换购商品处理 0%  6. 门店处理 （已确定， 按照最近单门店方式）100%  7. OBC 模式 （待定）0%  8. 处方单处理 （是否优化原有流程待定）0%  9. 用户购物车信息迁移 0%二、结算流程迁移 0%  1. 营销活动计算   2. 会员优惠价格计算 三、下单流程 0%  1. 资源扣减  2. 创订单接口  3. 创支付单接口  4. 资源回滚  5. 再来一单兼容 四、customer 对接 交易中台 | 1. 购物车链路模块迁移 90% |
| 微商城-交易链路切换 | 支付中台 (暂停)1. 完成产品设计评审 |  |
| 微商城-小前台能力替换 | 能力迁移:  开发ing1.重写微商城新增订单流程-O2O hydee-business-order服务 90% 2.微商城新增订单流程O2O/B2C融合到 hydee-business-order服务 0% 3.剔除微商接口中台转发 0%  3.1剔除middle-datasync-message  3.2剔除 yxt-xframe 4.整合微商城新增订单 给2整合一起 0% 5.复写middle-order能力 0% 6.剔除middle-order 0% |  |
| 新老模型,数据双向同步 | 数据同步:  开发ing，正单数据单向同步（历史数据+增量数据）可提测1. 同步框架-100% 2. 老模型 → 新模型   1. O2O正单-90%(含自测)   2. O2O逆单-30%   3. B2C正单-90%(含自测)   4. B2C逆单-0%   5. 物流相关 3. O2O正单-90%(含自测) 4. O2O逆单-30% 5. B2C正单-90%(含自测) 6. B2C逆单-0% 7. 物流相关 | 1. 老模型 → 新模型   1. O2O逆单-90%（含自测） 2. O2O逆单-90%（含自测） |


## 3、成员工作情况

| 成员 | 本周 | 下周 |
| --- | --- | --- |
|  | **本周工作进展:** - **业务需求** 重写微商城新增订单流程-O2O hydee-business-order服务 90%适老化商品补贴活动（云闪付小程序支付对接）- **技术专项**  - **其他****慢SQL优化上线**   **风险遗留项:** | **下周重点工作:**适老化商品补贴活动（云闪付小程序支付对接） |
|  | **本周工作进展****业务需求 3d**1. 线下订单第二批次数据迁移。已完成 2. 退单无法关联正单暂存时间调整。已上线  **技术专项** **其他**  **风险遗留项:** | **下周重点工作:**businesses-gateway网关优化 |
|  | **本周工作进展:** - **业务需求** 1.处方单信息增强 上线2.采购单 修改bug 上线3.集团- **技术专项******  - **其他**    **风险遗留项:** | **下周重点工作:** |
|  | **本周工作进展:** - **业务需求** - **技术专项**   - O2O逆单-30%   - B2C正单-90%(含自测) - O2O逆单-30% - B2C正单-90%(含自测) - **其他**  **风险遗留项:** | **下周重点工作:**1. 老模型 → 新模型   1. O2O逆单-90%（含自测） 2. O2O逆单-90%（含自测） |
|  | **本周工作进展:** - **业务需求**  - **技术专项**  - **其他**  **风险遗留项:** | **下周重点工作:** |
|  | **本周工作进展:** - **业务需求**  - **技术专项******  - **其他** ****- **风险遗留项:** | **下周重点工作:** |
|  | **本周工作进展:** - **业务需求**  1. 采购中台-交易支持 bug修复 已上线- **技术专项**  1. 微商城交易链路切换-购物车模块迁移切换 50%- **其他**   **风险遗留项:** | **下周重点工作:**1. 微商城链路切换-购物车模块迁移 |
|  | **本周工作进展:** - **业务需求**  1、平台对账重评 a、bug修复 b、公式对比调整 c、需求重评 2、唯品会--WMS打印面单接口已完成- **技术专项******  - **其他**  1、大数据业务与数据支持 2、线上Bug修复   **风险遗留项:** | **下周重点工作:**1、唯品会开发 |
|  | **本周工作进展:** - **业务需求** 国补云闪付需求开发(15%)- **技术专项******  - **其他**    **风险遗留项:** | **下周重点工作:**国补云闪付开发 |
|  | **本周工作进展:**- **业务需求** 1. B2C订单接入追溯码，涉及功能点：拣货复核、扫描发货、退货审核、下账单同步追溯码   1. 开发进度：10%   2. 目前在接口定义环节，与商品中台对接接口 2. 开发进度：10% 3. 目前在接口定义环节，与商品中台对接接口 - **技术专项****1. 输出订单、售后表的ER关系****2. 带着追溯码需求进行代码熟悉、流程整理** - **其他** 无   **风险遗留项:****** | **下周重点工作：**1. B2C订单接入追溯码需求开发-80% **** |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 |
| --- | --- | --- |
| 1 | 系统资源 | 需要 |
| 2 | 稳定性建设 | 需要 |
| 3 | 风险预警 | 暂定 |
| 需要 |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |
| 5 | CaseStudy |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 | 环境：prod 服务：hydee-business-order-web 实例：10.100.33.218 3d9406d0bbc841ff8aa4575f43b2af8b.90.17477093953612739 [05-20 10:49:55.452]-[http-nio-12601-exec-43]-[cn.hydee.starter.util.ExLogger:63] error#/1.0/orderProcurement/plat/exportOrderProcurementGoods#[http://10.100.33.218/1.0/orde...java.util.LinkedHashMap](http://10.100.33.218/1.0/orde...java.util.LinkedHashMap)] with preset Content-Type 'application/[vnd.ms](http://vnd.ms)-excel;charset=UTF-8' org.springframework.http.converter.HttpMessageNotWritableException: No converter for [class java.util.LinkedHashMap] with preset Content-Type 'application/[vnd.ms](http://vnd.ms)-excel;charset=UTF-8' at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:312) at org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor.handleReturnValue(HttpEntityMethodProcessor.java:219) at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78) at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:124) | 已完成，端午后上线 |  |  |
| 2 | 环境：prod 服务：yxt-payment 实例：10.100.42.77 f368fc31dde64f97874eb1622786fc55.110.17478783582635495 [05-22 09:46:00.290]-[http-nio-8080-exec-2]-[com.yxt.payment.server.payment.domain.listener.LogRefundLocalEventListener.failedRefundOrderEvent:44] 退款失败 需要人工介入 ASSIST 1009 null XSTHSQ-1009-250513-00012 null 查看详情 .. 忽略1h 忽略8h 忽略12h |  |  |  |
| 3 | 环境：prod 服务：hydee-business-order-b2c-third 实例：10.100.38.199 1f1b0c29b670416dba38f905cc16e33f.144056.17478780000510001 [05-22 09:40:01.213]-[SimpleAsyncTaskExecutor-113394]-[cn.hydee.starter.util.ExLogger:63] B2COrderHandleService#500001#315340429657#$$Failed to doOrderChain! org.springframework.dao.DataIntegrityViolationException: Error updating database. Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'invoice_name' at row 1 The error may exist in cn/hydee/business/order/common/mapper/OrderInfoMapper.java (best guess) The error may involve cn.hydee.business.order.common.mapper.OrderInfoMapper.insert-Inline The error occurred while setting parameters Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column 'invoice_name' at row 1 at cn.hydee.business.order.b2c.service.baseinfo.order.service.impl.OrderInfoServiceImpl$$EnhancerBySpringCGLIB$$43f4c625.save(<generated>) at cn.hydee.business.order.b2c.service.baseinfo.order.service.impl.BatchHandleOrderServiceImpl.saveOrderInfo(BatchHandleOrderServiceImpl.java:131) at cn.hydee.business.order.b2c.service.baseinfo.order.wmshandle.WmsB2COrderHandleChain.doOrderChain(WmsB2COrderHandleChain.java:395) |  |  |  |
| 4 | 环境：prod 服务：hydee-business-order 实例：10.100.35.129 3d9406d0bbc841ff8aa4575f43b2af8b.119.17478727601973969 [05-22 08:12:40.224]-[long-async-executor-2]-[cn.hydee.middle.business.order.v2.service.impl.OrderSyncServiceImpl.orderSync:40] order sync get data error,req: {"index":4,"merCode":"500001","organizationCode":"B119","userId":"c7741b7a27d141f2824b0d767fb4d02d"}, message: java.lang.NumberFormatException: For input string: "null" at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65) at java.lang.Integer.parseInt(Integer.java:580) at java.lang.Integer.parseInt(Integer.java:615) at cn.hydee.middle.business.order.v2.manager.base.OrderInfoRedisManager.getIndexValue(OrderInfoRedisManager.java:499) at cn.hydee.middle.business.order.v2.service.impl.OrderSyncServiceImpl.orderSync(OrderSyncServiceImpl.java:37) | O2O订单处理count与列表查询出的total不等时触发数据同步（海典原代码），不用处理 |  |  |
| 5 | 环境：prod 服务：hydee-business-order-web ip：************ 时间：2025-05-23 10:32:23.485 traceId：8083b24b80ca41f0b9562d95971880d2.125.17479674830957925 方法：cn.hydee.starter.util.ExLogger:63msg：error#/1.0/order/state/count/500001/-99#[http://************/1.0/order/state/count/500001/-99#60381#V21z6#$$org.springframework.dao.RecoverableDataAccessException](http://************/1.0/order/state/count/500001/-99#60381#V21z6#$$org.springframework.dao.RecoverableDataAccessException): ### Error querying database. Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure The last packet successfully received from the server was 60,141 milliseconds ago. The last packet sent successfully to the server was 60,141 milliseconds ago. ### The error may exist in URL [jar:[file:/data/app/dscloud/hydee-business-order-web.jar!/BOOT-INF/lib/hydee-business-order-common-1.0.0.jar!/mapper/OrderInfoMapper.xml](http://file/data/app/dscloud/hydee-business-order-web.jar!/BOOT-INF/lib/hydee-business-order-common-1.0.0.jar!/mapper/OrderInfoMapper.xml)] ### The error may involve defaultParameterMap ### The error occurred while setting parameters ### SQL: select 1 status,count(1) as num from oms_order_info a where 1=1 and a.mer_code=? and a.order_status in (5,10,30,40) and a.deleted=0 and a.is_post_fee_order = 0 and a.ex_status = 1 and a.created >= ADDDATE( ADDDATE( curdate(), INTERVAL - 1 MONTH ), INTERVAL 1 DAY ) and a.created < ADDDATE(CURDATE(),INTERVAL 1 day) and a.warehouse_id in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) and a.third_platform_code in ( ? , ? , ? , ? , ? , ? , ? , ? ) and a.online_store_code in ( ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? , ? ) ### Cause: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure The last packet successfully received from the server was 60,141 milliseconds ago. The last packet sent successfully to the server was 60,141 milliseconds ago. ; Communications link failure The last packet successfully received from the server was 60,141 milliseconds ago. The last packet sent successfully to the server was 60,141 milliseconds ago.; nested exception is com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure The last packet successfully received from the server was 60,141 milliseconds ago. The last packet sent successfully to the server was 60,141 milliseconds ago. | B2C订单处理数量统计，暂不处理 |  |  |
| 6 | SELECT id,create_by,create_time,update_by,update_time,oms_order_no,waybill_code,document,platform_code,wp_code,logistic_config_id,ds_online_store_express_merchant_id,ext_oms_order_no,print_num,status FROM logistic_order WHERE status=1 AND waybill_code IN ('YT8763095988417') AND status = 1; |  |  |  |