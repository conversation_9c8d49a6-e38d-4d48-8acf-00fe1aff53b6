# 线下单上预发CheckList

### Jenkins服务:

  7 complete order-service   8 complete order-sync-service   9 complete order-atom-service   26 incomplete hydee-middle-order  

### 数据库表结构初始化

添加三方父单号.txt

添加内部父单号.txt

idx_parent_order_no索引.txt

mq_messagetrueCREATE TABLE `mq_message` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `msg_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息ID',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息内容',
  `mq_msg_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息状态 UN_HANDLE-未处理;HANDLED已处理',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `msg_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '消息类型 OFFLINE_ORDER_SYNC_MSG 线下单同步消息',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_msg_id_status_type` (`msg_id`,`mq_msg_status`,`msg_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6739 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='mq消息表';

### Apollo配置

  10 complete order-atom-sevice   11 complete order-service   12 complete order-sync-service  

order-atom-service配置

# 非会员分表起始时间
nonVipShardingStartDate: 2024-06-18 23:00:01

### Apollo网关路由配置

只配置对**科传的**网关

  16 incomplete order-atom-sevice feign调用不用   17 incomplete order-service 需要给科传使用  20 incomplete 网关需要配置APPKEY,根据不同的path来,不要影响到原有的服务     18 incomplete order-sync-service feign调用不用  

### job配置

#### order-atom-service服务

自动创建按照年月归档表任务

autoCreateOfflineOrderTableHandler

每天凌晨2点执行
0 0 2 * * ?


任务入参:  ["offline_order_202406","offline_order_cashier_desk_202406","offline_order_detail_202406","offline_order_detail_coupon_202406","offline_order_detail_pick_202406","offline_order_detail_promotion_202406","offline_order_med_ins_settle_202406","offline_order_organization_202406","offline_order_pay_202406","offline_order_prescription_202406","offline_order_user_202406","offline_refund_order_202406","offline_refund_order_detail_202406","offline_refund_order_med_ins_settle_202406","offline_refund_order_pay_202406"]

退单数据补偿正单号任务

compensationOrderNoForRefundOrderHandler 
已废弃

每5分钟执行一次
0 */5 * * * ?


任务入参:  
10

#### order-sync-sevice服务

mq_message兜底操作

mqMsgHandler

每5分钟执行一次
0 */5 * * * ?

任务入参
{"typeList":["OFFLINE_ORDER_SYNC_MSG_HD"]}

删除mq_message已经处理的消息

mqMsgDeleteHandler

每5分钟执行一次
0 */5 * * * ?

任务入参
{"mqMsgStatus":"HANDLED","days":"-10"}

### hydee-middle-order

  28 incomplete 线上单   

开发分支: order-detail-add-fields 

目前所以有问题的,改为从hydee-business-order发送消息: 

### 新建rocketmq队列:

| TOPIC**("TP_%s_%s_%s", "领域名",""系统名","业务名")** | TAG新**（"TAG_%s","业务动作或类别"**） | 描述 | 生产者 | 消费者 | 备注 |
| --- | --- | --- | --- | --- | --- |
| TP_ORDER_OFFLINE_SYNC-HD |  | 海典同步线下单topic | 海典 | order-sync |  |
| TP_ORDER_OFFLINE_EXCEPTION-HANDLE |  | 异常消息处理topic | order-sync | order-sync |  |
| TP_ORDER_OFFLINE_ORDER-DATA | TAG_CREATED | 线下单正单topic | order-sync | 会员、营销 |  |
| TP_ORDER_OFFLINE_REFUND-ORDER-DATA | TAG_CREATED | 线下单退单topic | order-sync | 会员、营销 |  |
| TP_ORDER_OFFLINE_ORDER-RECONCILIATION | 正单:TAG_NORMAL退单: TAG_REFUND | 线下单正单和退单对账topic | order-service | 会员 | 会员消费,调用详情接口 |
| TP_ORDER_OFFLINE_SYNC-KC | 正单: TAG_ORDER退单: TAG_REFUND_ORDER | 科传线下单数据同步topic | order-service | order-sync | 20240516添加 |


### api-gateway-hades配置

科传使用

        # order-service同步线下单
        - id: order-service-offlineOrder
          uri: lb://order-service
          predicates:
            - Path=/order-aggregate/offlineOrder/**
          filters:
            - AppKey
            - StripPrefix=1

### 依赖接口

  22 complete com.yxt.middle.member.api.MemberInfoApi#getMemberInfoByCardNo 会员上线  

### 从Hana迁移线下单迁移MQ

TP_ORDER_ATOM_MIGRATION-HANA
TP_ORDER_ATOM_MIGRATION-RE-HANDLE-HANA

触发接口

# 触发迁移脚本
curl --location --request POST 'http://order-atom-service.svc.k8s.环境.hxyxt.com/migration-hana'

# 重试失败的任务
curl --location --request POST 'http://order-atom-service.svc.k8s.环境.hxyxt.com/migration-updateAllFailed'



异常处理任务

migrationHanaDataReHandler

每5分钟执行一次
0 */5 * * * ?


migrationHanaDataRefreshProcessHandler
0/3 * * * * ?  每3s刷一次

数据库

  31 complete 建库   32 complete 建表