# B2C订单管理系统（中转平台）

项目地址： [https://yxtgit.hxyxt.com/order/order-transfer-system.git](https://yxtgit.hxyxt.com/order/order-transfer-system.git)

一、 环境地址

原环境地址

| 环境 | 地址 | 账号 | 密码 |
| --- | --- | --- | --- |
| test | [https://thirdorderstest.yxtmart.cn](https://thirdorderstest.yxtmart.cn) | admin | yxt@002727 |
| pro | [https://thirdorders.yxtmart.cn](http://thirdorders.yxtmart.cn) | admin |  |


迁移环境地址

| 环境 | 地址 | 账号 | 密码 |  |
| --- | --- | --- | --- | --- |
| dev | [http://order-transfer-system.svc.k8s.dev.hxyxt.com/login](http://order-transfer-system.svc.k8s.dev.hxyxt.com/login) | admin | yxt@002727 |  |
|  |  |  |  |  |


二、 运维事项

| 问题 | 描述 | 解决方案 |
| --- | --- | --- |
| 美团订单收货信息已经未明文状态未待解密 |  | 直接执行sql:UPDATE yxt_order  SET order_status = 'WAITTING_SYNC'  WHERE  order_status = 'WAITTING_DECRYPT'  注：问题原因是美团下发的是明文，代码解密异常，导致解密状态未修改，后续提需求解决 |
| 同步异常: 数据存储失败保存 请填写“商品明细”第1行:“商品编码”。 保存 请填写“商品明细”第1行:“仓库”。 提示:上述异常由“当前账套(self)”数垢: 源抛出。若需要更详细的日志分析报错，请在连接配置“ 的系统中获取更多日志。 |  | 找运营处理 |