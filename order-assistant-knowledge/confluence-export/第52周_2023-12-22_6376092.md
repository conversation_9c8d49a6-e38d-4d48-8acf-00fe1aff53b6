# 第52周 2023-12-22

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | 聚石塔、云顶、多多云 需要跟进真实B2C海典转发流程 |  |  |  |
| 2 | nacos热更新 | 郭志明 |  |  |
| 3 | .NET 迁移k8s | 杨俊峰 |  | 1月份开始迁移核心模块 |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 | 李洋 | **本周总工时：xxpd**1. 订单优化处理相关问题调整 2. 海典pos对接 | **㊀计划工作**1. 完成海典POS对接 **㊁实际完成**1. 完成海典POS正向单对接 **㊂遗留问题**1. 用测试数据进行测试 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. 海典POS对接 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 | 王世达 | **本周总工时：xxpd**1. 保山医保支付流程重构 2day 2. pos对接 调用海典接口联调 2day 3. B2C订单下账单价计算精度问题 + 手动推送失败订单 1day | **㊀计划工作**1. pos对接 2. 保山医保支付流程重构 3. B2C订单下账单价计算精度问题 **㊁实际完成**1. pos对接 处于退款下账联调阶段 2. 保山医保支付流程重构 已上线 3. B2C订单下账单价计算精度问题 测试环境验证通过，等待处理 b2c-third 模块重启问题后上线 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 | 崔建波 | **本周总工时：xxpd**1. 优雅发布 2. 经营分析数据同步 | **㊀计划工作**1. 优雅发布到线上。（先部署到ydjia-merchant-customer试用一段时间，稳定后部署到其他服务） 2. 经营分析开发 **㊁实际完成**1. 优雅发布， 总体进展100% 2. 经营分析数据同步，完成了开发和自测，下周一提测。 **㊂遗留问题**1. xxx **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 4 | 郭志明 | **本周工时统计：**1. 切店自动化测试、上线 2. 山西门店退款单迁移 3. 线上问题支持 **** | **㊀计划工作**1. 切店自动化上线 2. 山西门店退款单迁移 **㊁实际完成**1. 切店自动化上线 2. 山西门店退款单迁移 **㊂遗留问题**1. 暂无 **㊃风险问题**1. 暂无 **㊄关于团队/项目建设的建议（想法）**1. 暂无 **** | **㊀需求研发相关**1. 购物车优化项目 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 5 | 杨国枫 | **本周工时统计：**1. 释放占用库存问题 2. 山西门店正向订单迁移 3. 线上订单支持 4. 抖店O2O新订单流程20% **** | **㊀计划工作**1. 释放占用库存问题 2. 山西门店正向订单迁移 3. 线上订单支持 4. 抖店O2O新订单流程20% **㊁实际完成**1. 释放占用库存问题 2. 山西门店正向订单迁移 **㊂遗留问题**1. 抖店O2O对接 **㊃风险问题**1. 暂无 **㊄关于团队/项目建设的建议（想法）**1. 暂无 **** | **㊀需求研发相关**1. 购物车优化项目 2. 抖店O2O对接 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 6 |  | **本周工时统计：**1. 切店自动化测试、上线 2. 解决线上BUG 3. 线上问题支持 | **㊀计划工作**1. 切店自动化上线 2. 解决生产运维 **㊁实际完成**1. 切店自动化上线 2. 解决生产运维 **㊂遗留问题**1. 暂无 **㊃风险问题**1. 暂无 **㊄关于团队/项目建设的建议（想法）**1. 暂无 **** | **㊀需求研发相关**1. 批量导出下载订单 2. 处方图片名称修改 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 7 |  | **本周总工时：**1. 门店自动化切换 1day 2. .NET服务性能问题追踪 2day 3. 新店切换问题处理 1day 4. 顺丰配送bug 修复 1day **** | **计划工作**1. 切店自动化遗留问题处理 2. .NET 现有业务文档梳理 [.NET 工程维护 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6370373) **** | **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 元旦值班

| 时间 | 值班人员 | 请假人员 |
| --- | --- | --- |
| 12月29号 | 郭志明 | 徐国华 |
| 12月30号 | 李洋 |  |
| 12月31号 | 崔建波 |  |
| 1月1号 | 徐国华 |  |
| 1月2号 | 徐国华 |  |