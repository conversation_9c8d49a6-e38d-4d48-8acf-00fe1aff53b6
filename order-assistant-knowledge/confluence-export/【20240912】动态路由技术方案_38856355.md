# 【20240912】动态路由技术方案

## 

## 一、 什么是动态路由

可以动态选择调用服务的能力称为动态路由。

举例：如下图， 服务A 到 服务D的调用链中，可以指定本该让服务C执行的逻辑转给了服务C1.

true1falseautotoptrue8434

## 二、应用场景

### 1.2.1 提升联、开发调效率

在开发阶段，可以直接把前端界面的请求指定路由到本地，快速复现问题，修复bug后快速验证修复情况。

true4falseautotoptrue12791

### 1.2.2 测试版本隔离支撑

具体见 《测试版本隔离方案》（待完成）

### 1.2.3 虚拟服务支撑

具体见 《虚拟服务方案》（待完成）

## 三、技术原理

### 3.1 技术现状

目前内部服务和服务之间，内部服务和外部服务的调用都是通过OpenFeign 或 HttpClient(ApacheClient,JdkHttpClient,OkHttpClient等）发起http协议调用。

### 3.1 技术原理

用户发起请求时通过请求头添加路由配置，动态路由器根据路由配置进行服务转发

true2falseautotoptrue9512

### 3.1 深度路由原理

通过请求头在整改调用链中持续向下传递，来实现指定节点的路由

true3falseautotoptrue5311

### 3.2 httpClient动态路由实现原理

目前支持OkHttpClient的动态路由，其他HttpClient根据需求情况后续慢慢支持。

#### 3.2.1 OkHttpClient动态路由实现原理

我们在使用okHttpClient时，需要先构建client对象，并可以对其进行拦截器设置，有的是通过springboot方式进行配置，有的则是直接通过static method，自定义类构造函数方式进行初始化。所以client对象不能全通过springboot相关接口拦截下client对象并对齐进行拦截器配置。

所以引入了 javaAgetn 和javaassist，在字节码层面对齐进行拦截注入动态路由拦截器。

#### 3.2.2 其他httpClient

如果不紧急可以@焦钰斌进行路由实现；着急需求，可以通过注解方式来接入动态路由功能。请求头透传需要自行根据client特点进行实现。

通过aop对获取baseUrl方法进行拦截，根据路由规则动态修改配置的url。

修改前

java@Value("${xxx.xxx.xxx}")
public String url =  http://10.0.100.1:9080/base/api;

public Object execute(Object params){
	httpClient.execute(url+"/someApi",params);
}



修改后

java@Value("${xxx.xxx.xxx}")
public String url =  http://10.0.100.1:9080/base/api;

@DynamicRoute
public String getUrl(){
	return this.url;
}

public Object execute(Object params){
	httpClient.execute(getUrl()+"/someApi",params);
}

设置路由请求头

java        String allRoute = DynamicRouteContext.getAllRoute();
        String mockKey = DynamicRouteContext.getMockKey();
        if(StringUtils.isNotBlank(allRoute)){
            request.setHeader(DynamicRouteContext.ROUTE_KEY,allRoute);
        }
        if(StringUtils.isNotBlank(mockKey)){
        request.setHeader(DynamicRouteContext.MOCK_KEY,mockKey);
        }

### 3.3 支持异步线程实现原理

true5falseautotoptrue4111

请求到了服务内部，动态路由为了做到零侵入性和开箱即用，路由规则都是通过threadLocal进行隐式的数据传递，而threadLocal中的数据无法跨线程访问，InheritableThreadLocal虽然能父子线程传递，但是在线程池的情况下也无法使用。

所以考虑到了动态代理技术，通过修改线程池内部实现，拦截线程池的执行任务函数，将请求线程中的路由规则传递到异步线程中。因为绝大多数场景的异步场景都是通过@Async注解完成的，并且为了限制增强对象的泛滥，动态代理实现方式选用spring方式进行的，所以切面的线程池必须是由spring进行管理的。目前支持的线程池支持下面几类。

@Around("execution(public * org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor.execute(..))") 

@Around("execution(public * org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor.submit(..))") 

@Around("execution(* java.util.concurrent.AbstractExecutorService+.submit(..))") 

@Around("execution(* java.util.concurrent.ThreadPoolExecutor+.execute(..))")

为了适配性更强，目前是通过 javaAgent和javaassist方式对线程池进行class层面注入代码实现上下文的传递。目前支持下面的线程池。目前来看已经覆盖了99%，如有特殊线程池需求联系@焦钰斌

```
java.util.concurrent.ThreadPoolExecutor
```

```
java.util.concurrent.AbstractExecutorService
```

```
org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
```

```
java.util.concurrent.ForkJoinPool
```

```

```

## 三、接入和使用

如果只是接路由网关下发的请求时不需要配置任何依赖包和配置。

### 3.1 修改pom.xml 依赖

考虑到本功能不应该到生产,且能快速发布新功能,目前使用SNAPSHOT方式进行引用.

`grey-spring-boot-web-starter` 替换成下面配置

xml<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>grey-spring-boot-web-starter</artifactId>
    <version>3.2.0-SNAPSHOT</version>
</dependency>

```
grey-spring-boot-gateway-starter
```

xml<dependency>
    <groupId>cn.hydee.starter</groupId>
    <artifactId>yxt-grey-pa-spring-boot-web-starter</artifactId>
    <version>3.2.0.pa-SNAPSHOT</version> 
</dependency>

如果有单独引入 `grey-spring-lib的，需要删除该引用`

### 3.2 添加application.yml 配置

yml## 动态代理开关，默认false，修改后需重启
dynamic:
  enable: true

### 3.3 使用

开发阶段联调，动态路由使用方法：