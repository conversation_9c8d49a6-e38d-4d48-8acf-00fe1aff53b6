# 【20240315】v1.6 需求 订单路由

# 一、 背景

**1.1 业务背景**

****在商家自配送为前提下，目前公司的门店在经营时间上区分了24小时门店及非24小时门店，当非24小时门店处于关店状态时可能会有用户存在下单需求，会导致该门店非营业时间的订单量减少，通过转单功能可实现在非营业时间正常接单后转单到附近正常营业且可发货门店进行处理，从 而增加收入；用户下单后，根据客户和门店的位置进行转单处理，通过对比待选择门店配送费的多少，选择出配送费少的门店进行转单，用于减少配送费的支出。

**1.2 痛点分析**

 1. 非24小时门店闭店时间段中，可能会有用户下单，此时就会缺失该部分收入。

 2. 当前区域存在多家门店，用户下单至最远门店，若是商家自配，配送费用增高。

 3. 当前区域存在多家门店，其中某一门店单量过多，门店无法及时处理。

**1.3 系统现状**

O2O订单落库流程

true原订单入库流程falseautotoptrue3072

# 二、 需求分析

### 业务流程

订单落库流程

true订单路由流程falseautotoptrue9058

正向单转单履约流程

true转单拣货创建调拨单falseautotoptrue98110

逆向单流程 正向调拨单未成功？ 

true转单拣货创建调拨单falseautotoptrue98110

### 2.2 需求功能点

| **模块** | **功能点** | **功能说明** | **备注** | **优先级** |
| --- | --- | --- | --- | --- |
| **场景设置** | **新增场景** | **增加转单场景，配置部分规则** |  | p0 |
| **修改场景** | **修改转单场景，添加或减少规则** | **需要校验已使用的场景不可修改** | p0 |
| **删除场景** | **删除当前未使用或配置错误场景** | **需要校验已使用的场景不可删除** | p0 |
| **查询场景** | **根据特定条件查询场景列表** |  | p0 |
| **策略设置********** | **查询场景** | **查询出已启用的场景供用户选择** |  | p0 |
| **新增策略** | **根据场景配置规则，设置具体门店、店铺及规则具体值** | **校验门店下店铺是否和其它店铺重叠** | p0 |
| **编辑策略** | **修改指定策略，只能修改策略规则，不能修改配置的门店信息** |  | p0 |
| **查询策略** | **根据特定查询策略列表** |  | p0 |
| **开启/关闭策略** | **开启或关闭策略** | **校验同一门店下店铺是否重叠** | p0 |
| **复制策略** | **将指定策略信息带入新增页面，其余信息不变** |  | p1 |
| **范围门店查询** | **根据距离查询符合店铺集合** |  | p0 |
| **通知商品中台** | **策略的变更通知商品中台，用于库存的同步** |  | p1 |
| **指定商品导入** | **接单规则策略指定商品支持excel导入** |  | p1 |
| **配送费配置** | **新增** | **增加配送费配置** |  | p0 |
| **修改** | **修改配送费配置** |  | p1 |
| **删除** | **删除指定配置** |  | p1 |
| **查询** | **根据特定条件查询配置列表** |  | p1 |
| **订单流程处理********** | **发货门店拒绝转单** | **店员操作不接受转单，发货门店转为下单门店** |  | P1 |
| **订单打印数据处理** | **小票打印数据应该为发货门店的信息** |  | p1 |
| **订单操作权限处理** | **下单门店只有转单的查看权限，无操作权限，发货门店具有操作&写权限** |  | p1 |
| **转单语音推送处理** | **转单语音推送按照门店推送，推送配置为下单店铺配置** |  | p1 |
| **订单处理增加转单标识** | **订单查询页面标识出该转单信息** |  | p1 |
| **转单日志增加** | **操作日志中增加转单操作记录** |  | p1 |
| **订单路由规则校验** | **下单后若开启订单路由，匹配配置的策略规则，判定是否转单** | **核心流程** | p0 |
| **转单记录入库** | **规则匹配成功后，将记录转单详情** |  | p1 |
| **转单记录查询** | **根据条件查询转单记录** |  | p1 |
| **调拨单** ******** | **对接海典接口** | **调拨单的交互** |  | p0 |
| **正单创建调拨单** | **创建待推送的正向调拨单** |  | p1 |
| **逆单创建调拨单** | **创建待推送的逆向调拨单** |  | p1 |
| **调拨单修改** | **主要为状态修改及批号修改** |  | p1 |
| **查询调拨单** | **根据条件查询调拨单列表 （包含详情）** |  | p1 |
| **调拨单导出** | **根据条件导出调拨单记录** |  | p1 |
| **消息预警** | **转单异常告警，企业微信通知** |  | p2 |
| **三方对接** | **短信中台对接** | **转单给用户发送短信** | **和短信中台沟通建立单独短信类型** | p1 |
| **对接腾讯地图服务** | **计算配送费和配送距离** | @徐凯 腾讯地图开放平台账号还未给到 | p1 |
| **美团配送** | **用发单前预览，获取配送距离和配送费** | 配送费计算方案二和腾讯地图选一个 | p1 |
| **蜂鸟配送** | **调用预下单接口：获取配送距离和配送费** | p1 |
| **顺丰同城配** | **调用预建订单接口，获取配送费和配送距离** | p1 |
| **达达配送** | **调用查询运费接口** | p1 |


# 三、目标

### 本期目标

### 业务目标

完成订单路由模型构建及主要功能实现：

 a. 订单场景配置实现

 b. 订单策略配置实现

 c. 订单路由转单实现

### 技术目标

a. 尽量减少对正常流程的影响，保证订单路由功能不影响原下单流程。 

b.主要实现规则灵活配置，后期增加规则，无需动用已有路由代码流程，减少后期开发成本。

### 中长期目标

# 四、整体设计

**4.1统一语言定义**

业务、技术名词解释等

| 名词 | 解释 |
| --- | --- |
| 场景 | 策略配置的模板，限制和规定策略配置规则 |
| 规则 | 用于订单转单校验所使用的条件判断 |
| 策略 | 规则的集合体，门店具体执行的配置单元 |
| 发货门店 | 哪个门店将货发出，则为发货门店，订单下账的门店是发货门店 |
| 下单门店 | 用户原始下单的门店；这个门店是平台结算的门店 |


**4.2 用例图**

**true场景设置falseautotoptrue6611**

true分单策略falseautotoptrue12411

true分单结果falseautotoptrue7111

**4.3 时序图**

true时序图falseautotoptrue9215

### ER图

**** 场景策略相关表表关系

true策略配置相关表er图falseautotoptrue11864

配送费计算规则相关表表关系

true费用相关表设计ER图falseautotoptrue2613

策略更新日志表关系

true策略更新日志表关系falseautotoptrue6111

### 架构图

架构五视图：逻辑架构、开发架构、物理架构、运行架构、数据架构，跟进需要进行选择.后期主要是不断迭代演进的架构推演，改动的或新增的模块特殊颜色标识。

### 核心技术问题

| 问题 | 描述 |
| --- | --- |
| **发货门店与下单门店区分** | 沿用order_info表原有设计，系统现状已区分下单门店与发货门店。 |
| **订单路由对于履约流程的影响** | 模拟订单路由后的履约流程： 1. 下单后数据库直接修改发货门店 ,将E016发货门店修改为E017，此时只有E017的店员才能看到此订单。   2. 拣货复核操作后，此操作无影响 3. 配送出库，(下帐门店传值为E017为发货门店,该门店配置的H2下帐）   4. 配送完成 5. 退款操作 （退款信息表 refund_order 中未冗余 organization_code, 通过order_info做关联处理，退款操作仍然在发货门店权限中）  结论： a. 模拟订单路由成功后的履约操作，对于发货门店整体流程无影响 （不包含 小票打印，该场景无法模拟）。 b. 下单门店无法查看转单信息，该处需要修改原权限代码逻辑。 |
| **规则校验灵活配置** | a. 目前规则数量和类型是固定的，规则表的数据是开发提前写入库的。b. 规则表中增加优先级的字段用于规则校验执行的顺序。c. 每条规则策略对应唯一处理类，后续若有新的规则增加，只需插入规则数据，开发对应规则的处理类即可，对已有逻辑影响几乎没有。 |
| **订单路由具体入口** | true未命名绘图false600autotoptrue17213订单路由入口，使用原逻辑流程，继承 AbstractHandler ，单独开发处理类，捕获所有异常不抛出，执行顺序防止于SaveOrderSaveHandler后，减少对原流程的影响 |


# 五、 详细设计

### 模块详细设计

业务全流程

true业务全流程falseautotoptrue7911

订单路由匹配流程

true转单流程falseautotoptrue3192

正单履约下账详细流程

true转单正单下账流程falseautotoptrue8021

逆向单履约下账流程

true退款调拨单falseautotoptrue8891

### 存储数据库设计

| 表名 | dml |
| --- | --- |
| ****     **订单路由场景表** | sqlEmacsCREATE TABLE `route_scene` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `scene_name` varchar(64) not null comment '场景名称',   `scene_type` varchar(32) not null comment '场景类型 大店分单-BIG_STORE_HAND_OUT 闭店转单-CLOSE_STORE_TRANSFER 夜店转单-NIGHT_STORE_TRANSGER',   `mark` varchar(255) default null comment '场景说明',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL  COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`),   KEY `idx_created_time` (`created_time`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由场景表'; |
| **订单路由规则表** | sqlEmacsCREATE TABLE `route_rule` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `rule_name` varchar(64) not null comment '规则名称',   `rule_type` varchar(16) not null comment '规则类型 分单-HAND_OUT 接单-RECEIVE',   `handle_type` varchar(255) default null comment '处理类型 代码逻辑使用，唯一值',   `required` tinyint(1) not null default 0 comment '是否所有场景均必选 0-否 1-是',   `meaning` varchar(255) default null comment '意义',   `seq` int(3) not null default -1 comment '规则校验顺序',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL   COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,   `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由规则表'; |
| **订单路由场景与规则关联表** | sqlEmacsCREATE TABLE `route_scene_rule_re` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `scent_id` bigint(20) not null comment '场景模板ID',   `rule_id` bigint(20) not null comment '规则模板ID',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL  COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,   `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由场景与规则关联表'; |
| **订单路由策略主表** | sqlEmacsCREATE TABLE `route_strategy` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_name` varchar(64) not null comment '策略名称',   `scene_id` bigint(20) not null comment '所选场景ID',   `company_name` varchar(256) not null comment '子公司名称',   `company_code` varchar(40) not null comment '子公司编码',   `store_name` varchar(256) not null comment '门店名称',   `store_code` varchar(40) not null comment '门店编码',   `online_store_ids` varchar(255) not null comment '店铺id集合,使用;分割',   `state` tinyint(1) not null default 0 comment '策略状态 开启-true 关闭-false',   `sms_notifiy` tinyint(1) not null DEFAULT 0 comment '是否短信通知 开启-1关闭-0',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL  COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`),   KEY `idx_created_time` (`created_time`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由策略主表'; |
| **订单路由策略规则明细表** | sqlEmacsCREATE TABLE `route_strategy_rule` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_id` bigint(20) not null comment '策略主表ID',   `rule_id` bigint(20) not null comment 'route_rule 规则表ID',   `rule_value` varchar(255) default null comment '规则属性值，不同规则处理不同值，由规则选项决定值，代码中自行处理',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL   COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `uniq_strategy_id_rule_id` (`strategy_id`, `rule_id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由策略规则明细表'; |
| **订单路由接单门店配置表** | sqlEmacsCREATE TABLE `route_receive_store_set` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',  `strategy_rule_id` bigint(20) not null comment '策略规则表 id',   `re_store_code` varchar(40) not null comment '接单门店编码',   `re_store_name` varchar(256) not null comment '接单门店名称',   `distance` double(4,2) not null comment '接单门店与分单门店距离',  `priority`  int  default -1  not null comment '门店优先级',  `pick_num`  int comment '待拣货订单量(订单堆积量超过多少不接单)',  `acquiesce` tinyint(1) default 0 comment '是否兜底 ture-是 false-否',    #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL  COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,    `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`),   UNIQUE KEY `uniq_strategy_rule_re_store_code` (`strategy_rule_id`, `re_store_code`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由接单门店配置表'; |
| **订单路由指定商品配置表** | sqlEmacsCREATE TABLE `route_good_set` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_rule_id` bigint(20) not null comment '策略规则表 id',   `re_store_code` varchar(40) not null comment '接单门店编码',   `re_store_name` varchar(256) not null comment '接单门店名称',   `erp_code` varchar(60) not null comment '商品编码',   `commodity_name` varchar(255) not null comment '商品名称',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL  COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,   `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由指定商品配置表'; |
| **订单路由规则时间段配置表** | sqlEmacsCREATE TABLE `route_time_period_set` (   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',   `strategy_rule_id` bigint(20) not null comment '策略规则表 id',   `start_time` time not null comment '开始时间',   `end_time` time not null comment '结束时间',   `ext_info` varchar(125) default null comment '扩展信息',   #必须字段   `created_by` varchar(64) NOT NULL COMMENT '创建人',   `updated_by` varchar(64) NOT NULL COMMENT  '更新人',   `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,   `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单路由规则时间段配置表'; |
| **订单路由配送费规则表** | sqlEmacsCREATE TABLE delivery_fee_rule (         `id` bigint NOT NULL AUTO_INCREMENT,         `delivery_rule_name` varchar(64) NOT NULL COMMENT '配送规则名称',         `delivery_platform_code` varchar(10) NULL COMMENT '配送商编码',         `trans_priority` tinyint NULL COMMENT '运力优先级',         `start_price` decimal(16,2) NULL COMMENT '起步价',         `start_distance` decimal(16,2) NULL COMMENT '起步距离',         `start_weight` decimal(16,2) NULL COMMENT '起步重量', 		`city_grade` tinyint NULL COMMENT '城市等级', 		`rule_explain` varchar(128) NULL COMMENT '规则说明', 		`created_by` varchar(64) NOT NULL COMMENT '创建人',         `updated_by` varchar(64) NOT NULL COMMENT  '更新人',         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',         `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,         `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',         PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配送费规则表'; |
| **订单路由配送费加价表** | sqlEmacsCREATE TABLE delivery_fee_rule_addprice (         `id` bigint NOT NULL AUTO_INCREMENT,         `delivery_rule_id` varchar(20) NOT NULL COMMENT '配送策略ID',         `city_code` varchar(10) NULL COMMENT '城市编码',         `city_name` varchar(20) NULL COMMENT '城市名称',         `add_type` tinyint NOT NULL COMMENT '加价类型 1：距离，2：重量，3：时间',         `start_range` varchar(10) NULL COMMENT '起始范围',         `end_range` varchar(10) NULL COMMENT '截至范围',         `add_price` decimal(16,2) NULL COMMENT '加价价格',         `created_by` varchar(64) NOT NULL COMMENT '创建人',         `updated_by` varchar(64) NOT NULL COMMENT  '更新人',         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',         `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,         `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',         PRIMARY KEY (`id`),         KEY `idx_delivery_rule_id` (`delivery_rule_id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='配送费加价表'; |
| **订单路由分单日志表** | sqlEmacs CREATE TABLE route_divide_order_log (         `id` bigint NOT NULL AUTO_INCREMENT,         `strategy_id` bigint(20) not null comment '策略主表ID',         `rule_id` bigint(20) null comment '规则ID',         `third_order_no` varchar(20) NOT NULL COMMENT '平台订单号',         `order_no` bigint(20) NOT NULL COMMENT '系统订单号',         `third_platform_code` varchar(20) NULL COMMENT '三方平台编码',         `organization_code` varchar(40) NOT NULL COMMENT '线下门店编码',         `organization_name` varchar(128) NOT NULL COMMENT '线下门店名称',         `online_store_code` varchar(60) NOT NULL COMMENT '下单线上门店编码',         `online_store_name` varchar(128) NOT    NULL COMMENT '下单线上门店名称',         `source_online_store_code` varchar(60) NOT NULL COMMENT '来源线上门店编码',         `source_online_store_name` varchar(128) NOT NULL COMMENT '来源线上门店名称',         `service_scene` varchar(32) NULL COMMENT '业务场景',         `strategy_name` varchar(64) NULL COMMENT '策略名称',         `increase_amount` decimal(16,2) NULL COMMENT '增收金额（订单金额）',         `origin_delivery_fee` decimal(16,2) NULL COMMENT '原配送费',         `transfer_delivery_fee` decimal(16,2) NULL COMMENT '转单后配送费',         `cost_savings` decimal(16,2) NULL COMMENT '成本节约',         `divide_status` varchar(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '分单状态 SUCCESS：成功，FAIL：失败',         `divide_fail_reason` varchar(128) NULL COMMENT '分单失败原因',         `created_by` varchar(64) NOT NULL COMMENT '创建人',         `updated_by` varchar(64) NOT NULL COMMENT  '更新人',         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',         `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP   COMMENT '更新时间' ,         `version`  bigint  NOT NULL DEFAULT 1  COMMENT ' 数据版本，每次update+1',         PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单路由分单日志表'; |
| **转单调拨表** | sqlEmacsCREATE TABLE route_allot (         `id` bigint NOT NULL AUTO_INCREMENT,         `out_store_code` varchar(40) NOT NULL COMMENT '调出门店编码',         `out_store_name` varchar(128) NOT NULL COMMENT '调出门店名称', 				`in_store_code` varchar(40) NOT NULL COMMENT '调入门店编码', 				`in_store_name` varchar(128) NOT NULL COMMENT '调入门店名称', 				`third_no` varchar(100) NOT NULL COMMENT '三方订单号 区分销售单和退款单', 			  `oms_no` bigint(20) NOT NULL COMMENT '系统订单号 区分销售单和退款单', 				`order_type` varchar(20) NOT NULL DEFAULT 'ORDER' COMMENT '关联订单类型 ORDER-销售单 REFUND-退款单', 				`oms_allot_no` BIGINT(20) NOT NULL COMMENT '心云拨单号', 			  `pos_allot_no` varchar(100) NOT NULL COMMENT 'pos拨单号', 				`allot_status` varchar(20) NOT NULL DEFAULT 'WAIT'  COMMENT '调拨单状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功', 				`allot_time` datetime DEFAULT NULL COMMENT '调拨成功时间', 				`fail_type` varchar(20) DEFAULT NULL COMMENT '失败类型 BATCH_NO_ERROR -批次号错误', 				`fail_message` varchar(255) DEFAULT NULL COMMENT '失败原因',         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',         `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',         `version` bigint NOT NULL DEFAULT 1 COMMENT ' 数据版本，每次update+1',         PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='转单调拨表'; |
| **转单调拨明细表** | sqlEmacsCREATE TABLE `route_allot_detail` (   `id` bigint NOT NULL AUTO_INCREMENT,   `allot_id` bigint NOT NULL COMMENT '调拨单主表ID',   `order_detail_id` bigint NOT NULL COMMENT '商品明细id',   `erp_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品erp编码',   `good_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',   `commodity_batch_no` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品批号',   `count` int NOT NULL DEFAULT '0' COMMENT '批号对应的数量',   `allot_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WAIT' COMMENT '调拨单状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功',   `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注(包含失败原因等)',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',   `version` bigint NOT NULL DEFAULT '1' COMMENT ' 数据版本，每次update+1',   PRIMARY KEY (`id`) ) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='转单调拨明细表 |
| 路由策略修改日志表 | sqlEmacscreate table route_strategy_modify_log (     id             bigint unsigned auto_increment comment '主键'         primary key,     strategy_id    bigint                             not null comment '策略id',     before_content varchar(2048)                       null comment '修改前的内容',     after_content  varchar(2048)                       null comment '修改后的内容',     created_by     varchar(64)                        not null comment '创建人',     updated_by     varchar(64)                        not null comment '修改人',     created_time   datetime default CURRENT_TIMESTAMP not null comment '创建时间',     updated_time   datetime default CURRENT_TIMESTAMP not null comment '修改时间',     version        bigint   default 1                 not null comment '版本号' )     comment '路由策略修改日志表' collate = utf8mb4_unicode_ci; |


sqlConfluence初始化sqlINSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '允许分单店铺', 'HAND_OUT', 'OUT_SHOP_HANDLE', 1, '可以设置某个店铺的分单设置', 1, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '允许分单的订单类型', 'HAND_OUT', 'OUT_ORDER_TYPE_HANDLE', 1, '设置是全部订单或者及时订单或者是预约订单', 5, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '允许分单时段', 'HAND_OUT', 'OUT_TIME_FRAME_HANDLE', 0, '设置哪些时段可以允许分单', 10, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '允许分单距离', 'HAND_OUT', 'OUT_DISTANCE_HANDLE', 0, '设置超过xx距离的才允许分单', 15, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`)
VALUES ( '分单门店订单堆积量', 'HAND_OUT', 'OUT_ORDER_NUM_HANDLE', 0, '设置门店待拣货订单超过xx，即允许被分单', 18, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '是否校验库存', 'HAND_OUT', 'OUT_STOCK_HANDLE', 0, '分单时，是否校验接单门店库存是否充足', 20, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '接单门店只有美团配送时', 'HAND_OUT', 'OUT_MT_FENCE_HANDLE', 0, '设置接单门店只有美团配送时，是否校验在接单门店电子围栏中', 25, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '指定商品指定门店', 'RECEIVE', 'RECEIVE_APPOINT_GOOD_HANDLE', 0, '设置指定商品指定门店发出', 30, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '按订单时段指定门店', 'RECEIVE', 'RECEIVE_TIME_FRAME_HANDLE', 0, '按订单时段设置指定门店', 35, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '多门店符合分单时', 'RECEIVE', 'RECEIVE_MORE_SHOP_ACCORD_HANDLE', 0, '多门店符合分单时，设置按距离优先，按配送费优先，按订单堆积量优先', 40, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '接单门店订单堆积量', 'RECEIVE', 'RECEIVE_ORDER_NUM_HANDLE', 0, '接单门店订单堆积量多余xx时，不分单', 45, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '无门店符合分单时', 'RECEIVE', 'REVEIVE_FORCE_SHOP_HANDLE', 1, '无门店符合分单时，设置是否需要强制分单', 50, 'system', 'system');
INSERT INTO `dscloud`.`route_rule`( `rule_name`, `rule_type`, `handle_type`, `required`, `meaning`, `seq`, `created_by`, `updated_by`) 
VALUES ( '接单门店列表', 'RECEIVE', 'RECEIVE_SHOP_LIST_HANDLE', 1, '设置可接单的门店', -1, 'system', 'system');  
INSERT INTO dscloud.route_rule(rule_name, rule_type, handle_type, required, meaning, seq, created_by, updated_by)
VALUES('接单门店能否拒绝', 'RECEIVE', 'RECEIVE_SHOP_ACCEPT_HANDLE', 1, '设置接单门店能否拒绝', 99, 'system', 'system');


INSERT INTO middle_es.es_log_config
(id, `type`, business_group, is_valid, create_name, create_time, modify_name, modify_time, extend)
VALUES(11, 'hydee-route-allot-log', '电商云业务组', 1, 'lingxiao', '2021-08-06 00:49:09.000', 'lingxiao', '2021-08-06 03:08:01.000', NULL);

### 接口设计

新增、修改的接口定义；流量预估，接口性能设计；

### 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 | 2024-03-15 |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、 项目排期

项目工时、分工等，贴jira连接

# 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等