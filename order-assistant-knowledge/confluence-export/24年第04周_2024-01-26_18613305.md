# 24年第04周 2024-01-26

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：4day**1. 第四批切店支持 2. 购物车优化二期上线 3. 问题排查与支持 4. 抖店O2O | **㊀计划工作**1. 第四批切店支持 2. 购物车优化二期上线 3. 问题排查与支持 4. 抖店O2O **㊁实际完成**1. 购物车优化2期上线（已上线） 2. 抖店O2O开发（今天提测） **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 抖店o2o测试上线 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. 建表DDL语句需要指定字符集以及排序规则，字符集：utf8mb4 字符集排序规则：utf8mb4_general_ci，Navicat自己生成的DDL是可以的，如图： 2. 但是DataGrip中，表没建索引的时候正常有字符集和排序规则，但是有了索引之后，他的DDL中是没有的，如下图： 3. 没有指定的话，mysql默认的排序规则会是utf8mb4_0900_ai_ci，导致在关联表查询时报错： |  |
| 2 | 王世达 | **本周总工时：4.0day**1. 海典H1下账问题修复（部分订单，下账代码修复） 2. B2C拣货复核bug修改及门店仓下账释放库存逻辑增加 3. 海典H2 增加B2C下账逻辑 | **㊀计划工作**1. 海典H1下账问题修复（部分订单，下账代码修修复） 2. B2C拣货复核需求 **㊁实际完成**1. 海典H1下账问题修复 （正单多情况下账已修复） 2. B2C拣货复核门店仓下账释放库存（已完成，测试中） 3. 海典H2增加B2C下账逻辑（正单冒烟测试完成） **㊂遗留问题** 1. 海典部分退款，平台下发退款完成消息会更新退款单下账状态为带下帐 （暂时不动，可能有相关场景） 2. 补充海典下账补偿逻辑，防止特殊情况未自动下帐**㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：4day**1. 第四批切店支持 2. 问题排查与支持 3. 抖店O2O | **㊀计划工作**1. 第四批切店支持 2. 问题排查与支持 3. 抖店O2O **㊁实际完成**1. 第四批切店支持 2. 问题排查与支持 3. 抖店O2O **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | 1.抖店O2O上线2.库存问题修改**** |  |
| 4 |  | **本周总工时：5day**1.平台转自配送自测2.解决门店问题3.解决线上bug | **㊀计划工作**1.平台转自配送自测2.解决门店问题3.解决线上bug**㊁实际完成**1.平台转自配送自测2.解决门店问题3.解决线上bug**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 5 | 杨俊峰 | **本周总工时：5 day**1. 京东到家自配送流转。1day 2. 切店问题处理 2.5day 3. 打印异常 | **㊀计划工作****㊁实际完成** **㊃风险问题**打印异常，目前排查到语音播报会调起系统api后无响应。还没有解决全部问题。**** | **** |  |
| 6 | 李洋 | **本周总工时：5day**1. 跟踪饿了么生成多次退款单问题 0.5day 2. 上海H1订单未自动下账问题 1day 3. 上海切店运维 1 day 4. B2C走O2O下账BUG修改（共19个，剩余7个） 2day | **㊀计划工作**1. 上海H1上线 2. B2C走O2O下账测试 **㊁实际完成**1. 上海H1上线 **㊂遗留问题**1. B2C走O2O下账BUG剩余7个 | **㊀需求研发相关**1. 上海H1上线 2. B2C走O2O下账测试 |  |
| 7 | 杨润康 | **本周总工时：5d**- doris表唯一键异常处理。主要通过分页和批量操作处理 - 循环依赖解决 - order_pick_info死锁问题解决。 - 订单统计数据刷ES定时任务开发 - ES部分订单数据丢失处理 - 告警群耗时长的接口处理,都是job触发的,线程池异步 - 切店问题处理。[输出线上问题排除路径文档](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18612613) - 线上服务日常监控 - 优雅发布仓库已建立 - 经营分析数据问题排查 | **㊀计划工作** **㊁实际完成** **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. [输出线上问题排除路径文档](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18612613) 2.  3. |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 九、 切换值班

| 时间 | 值班人员 |  |
| --- | --- | --- |
|  |  |  |
|  |  |  |
|  |  |  |