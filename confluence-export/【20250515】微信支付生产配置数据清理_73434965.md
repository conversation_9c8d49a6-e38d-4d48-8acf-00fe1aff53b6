# 【20250515】微信支付生产配置数据清理

## 

## 一、业务背景

### 1.1 业务背景

1. 因为生产环境支付配置混乱且多,导致查账困难,对财务造成了困扰,本应该通过修改功能和数据清洗来处理,考虑到支付中台马上会被重构,功能修改搁置, 优先通过数据清洗解决当前问题,后续重构时将该问题考虑进去一并解决.


## 二、需求分析

### 2.1 产品需求

1. 找到直营店已经上线的微商城店铺；
2. 看是否有对应的商户号设置； 若没有设置，则设置，设置的规则是门店属于什么子公司商户号就是对应的子公司；
3. 历史的数据全量排查；若门店对应的商户号不是子公司对应的商户号，全部修改成子公司对应的商户号；启用状态全部为启用；


### 2.2 需求拆解

1. 对于直营店铺,如果没有对应的微信配置就新增一个微信配置,未上线的门店也新增
2. 对于已经存在的直营店铺微信配置,如果是自配置类型,需要调整成为根据子公司配置类型
3. 对于已经存在的直营店铺微信配置,保证和子公司一直
4. 对于已经存在的直营店铺微信配置全部设置为启动状态,未上线的门店也启用


## 三、目标

1. 完成数据清洗,达到产品要求


## 四、整体设计

### 4.1 处理方式

通过离线+在线的方式来处理数据

在线处理方式是通过原有接口方式进行批量处理, 保证业务流程约束一致性



![未命名绘图](./resources/未命名绘图_5.png)



优势: 

- 便于测试,和生产检查,分步骤监控需要处理的数据及其处理情况
- 如果有遗漏或后期数据错乱,可以快速重复执行


## 五、风险

- 需要保证生产子公司配置的正确
- 需要确保清洗过程中运营不会去修改数据
- 需要提前备份本次修改影响到的表数据
- 对于新增的支付配置,用户是通过默认云南通道支付的,刷数后如果无法正常退款的(部分单子可以退款,但是状态扭转需要审核两次,这个需要告知一下运营). 需要运营介入微信后台退款
- 在预生产上做好支付测试
- 执行清洗时,需要有能直接恢复备份表的执行权限,保证在出事故后快速回滚


## 六、需要备份的表

6.1 需要备份的表

t_pay_channel_customer 行数: 12205

t_pay_platform_business 行数:12062
t_pay_platform_business_channel_type 行数: 74408
t_pay_business_channel 行数:12222
t_pay_info_config 行数:12059
t_pay_info_config_rela_channel 行数:12094

### 6.2 备份sql

CREATE TABLE t_pay_channel_customer_05_bak AS SELECT * FROM t_pay_channel_customer;
CREATE TABLE t_pay_platform_business_05_bak AS SELECT * FROM t_pay_platform_business;
CREATE TABLE t_pay_platform_business_channel_type_05_bak AS SELECT * FROM t_pay_platform_business_channel_type;
CREATE TABLE t_pay_business_channel_05_bak AS SELECT * FROM t_pay_business_channel;
CREATE TABLE t_pay_info_config_05_bak AS SELECT * FROM t_pay_info_config;
CREATE TABLE t_pay_info_config_rela_channel_05_bak AS SELECT * FROM t_pay_info_config_rela_channel;

CREATE TABLE t_organization_tree_05_bak AS SELECT * FROM t_organization_tree;

6.3 回滚脚本

START TRANSACTION;

DROP TABLE IF EXISTS t_pay_channel_customer;
DROP TABLE IF EXISTS t_pay_platform_business;
DROP TABLE IF EXISTS t_pay_platform_business_channel_type;
DROP TABLE IF EXISTS t_pay_business_channel;
DROP TABLE IF EXISTS t_pay_info_config;
DROP TABLE IF EXISTS t_pay_info_config_rela_channel;
DROP TABLE IF EXISTS t_organization_tree;


RENAME TABLE 
 t_pay_channel_customer_05_bak TO t_pay_channel_customer,
 t_pay_platform_business_05_bak TO t_pay_platform_business,
 t_pay_platform_business_channel_type_05_bak TO t_pay_platform_business_channel_type,
 t_pay_business_channel_05_bak TO t_pay_business_channel,
 t_pay_info_config_05_bak TO t_pay_info_config,
 t_pay_info_config_rela_channel_05_bak TO t_pay_info_config_rela_channel;
 t_organization_tree_05_bak TO t_organization_tree;

COMMIT;

```

```

## 七、里程碑

| 里程碑 | 时间 |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况梳理重要进展时间节点

## 八、上线方案

1、直接部署 pay-core-finacy即可