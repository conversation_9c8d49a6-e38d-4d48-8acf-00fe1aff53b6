# 【20241224】美团客服对接

# 一、业务背景

1. 由于智能客服系统无代码无人力，为解决问题，现需要由客服中台承接美团下发的客服消息，店员可直接回复客服消息，当店员超过一定时间时，需要由智能客服人员进行回复。


## 1.2 痛点分析

1. 需要判断是否推送智能客服；
2. 需要进行历史会话存储；
3. 接口需要限流；
4. 语音和图片文件的存储以及删除；


## 1.3 系统现状

1. 承接上游的客服系统推送的新消息，然后做展示；
2. 消息回复后，回推上游；
3. 只要有一个门店的权限时，显示客服按钮；
4. 消息超过24小时未回复，则直接删除会话，后续不允许回复；
5. 语音和图片文件留存至智能客服系统中，由智能客服生成链接下发至心云，并允许跨域；


# 二、需求分析

## 2.1 业务流程

[v2.4 智能客服对接](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38835048)

# 三、目标

**3.1 本期目标**

1. 心云只保留已读未读的状态，已读/未读的状态为是否打开聊天界面，打开即已读，即使客服已回复，依旧不变更是否已读；
2. 店员是否在指定时间内回复，指定时间回复则不推送消息到智能客服，若指定时间未回复或未收到商家在平台回复，则将消息推送给智能客服，标记这个聊天为已推送；后续心云回复需要同步到智能客服；
3. （开关）是否统一已读
4. 聊天包含三方，客户，店员（心云），客服（智能客服）；
5. 店员回复主体：心云，店员名称，工号，
6. 客服回复主体：客服，客服名称，客服工号，
7. 对平台：心云/客服，统一为商家回复；
8. 语音文件/图片文件：均为心云存储，给到智能客服时为URL；
9. 历史会话查询：增加指定会话的历史记录查询，接口需要限流；


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

1. 消息下发流程图true消息上下流程图falseautotoptrue9113
2. 回复消息流程图true店员回复消息流程falseautotoptrue9822


## 4.3 时序图

INLINE@startuml
'https://plantuml.com/sequence-diagram

autonumber
hide footbox
skinparam sequenceMessageAlign center
title 客服中台系统交互时序图
==用户发送消息==
actor 用户


用户 -> 三方平台: 发送消息
activate 用户
activate 三方平台
三方平台 --> 用户: 回显
deactivate 用户

三方平台 -> 接口中台: 回调接口
activate 接口中台
接口中台 --> 三方平台: 响应成功
deactivate 三方平台
queue MQ
接口中台 -> MQ: 发送消息到队列
activate MQ
接口中台 -> MQ: 获取消息

database mongodb
接口中台 -> mongodb: 存储原始消息
activate mongodb
接口中台 -> 接口中台: 组装消息体
接口中台 -> mongodb: 存储组装消息
deactivate mongodb
接口中台 -> MQ: 发送新消息到队列
deactivate 接口中台

客服中台 -> MQ: 获取消息
activate 客服中台
deactivate MQ


database dscloud
客服中台 -> 客服中台: 解析消息体
客服中台 -> OBS: 上传文件
activate OBS
note right: 语音、图片
OBS --> 客服中台: 文件链接
deactivate OBS

'activate dscloud
客服中台 -> dscloud: 存储会话
'deactivate dscloud

alt 店员在规定时间内回复消息
actor 心云店员
客服中台 -> 心云店员: 推送会话消息
note right: 多店员群聊
activate 心云店员
else 店员超时未回复
deactivate 心云店员
客服中台 -> 接口中台: sdk调用推送消息接口
deactivate 客服中台

activate 接口中台
接口中台 -> 智能客服: 调外部接口
activate 智能客服
智能客服 --> 接口中台: 响应成功
deactivate 智能客服
deactivate 接口中台
end

==店员回复消息==
心云店员 --> 客服中台: 建立长连接
activate 心云店员
activate 客服中台
心云店员 --> 客服中台: 获取消息列表
activate dscloud
客服中台 -> dscloud: 查询消息集合
note left: 根据店员所属门店查询
客服中台 -> 心云店员: 返回消息列表
心云店员 --> 客服中台: 获取消息详情
note left: 根据会话ID所属门店查询
客服中台 -> dscloud: 查询消息详情
alt 存在未读消息
客服中台 -> dscloud: 更新未读状态
dscloud --> 客服中台: 更新成功
else 不存在未读消息
客服中台 -> 心云店员: 返回消息详情
note right: 分页返回

end


心云店员 --> 客服中台: 回复消息
客服中台 -> dscloud: 存储消息内容

客服中台 -> 心云店员: 回显消息内容
deactivate dscloud
note right: 所有店员
deactivate 心云店员

alt 不需要发送智能客服

客服中台 --> 接口中台: sdk调用回复消息接口
activate 接口中台
||20||
else 需要发送智能客服

客服中台 --[#red]>> 接口中台: sdk调用推送消息接口
note right: 异步调用
deactivate 客服中台
接口中台 -[#red]> 智能客服: 调外部接口
activate 智能客服
智能客服 --[#red]> 接口中台: 响应成功
||20||
deactivate 智能客服
end
接口中台 --> 三方平台: 调用回复消息接口
activate 三方平台
三方平台 -> 接口中台: 响应回复结果
三方平台 --> 用户: 回显
deactivate 三方平台

接口中台 -> mongodb: 存储消息
activate mongodb
deactivate mongodb

接口中台 -> 客服中台: 返回回复结果
deactivate 接口中台
activate 客服中台
activate dscloud
客服中台 -> dscloud: 更新消息的发送结果
note over dscloud: 如果存在推送\n则更新推送结果
deactivate 客服中台
deactivate dscloud


==集团客服回复消息==
智能客服 --> 接口中台: 回复消息
note left 智能客服: 多客服人员
activate 智能客服
activate 接口中台
接口中台 -> 智能客服: 响应成功
deactivate 智能客服

接口中台 --> 三方平台: 调用回复消息接口
activate 三方平台
三方平台 -> 接口中台: 响应回复结果
三方平台 --> 用户: 回显
deactivate 三方平台
deactivate 三方平台
接口中台 -> MQ: 发送消息到队列
activate MQ
接口中台 -> MQ: 获取消息
接口中台 -> mongodb: 存储原始消息
activate mongodb
接口中台 -> 接口中台: 组装消息体
接口中台 -> mongodb: 存储组装消息
deactivate mongodb
接口中台 -> MQ: 发送新消息到队列
deactivate 接口中台
客服中台 -> MQ: 获取消息
deactivate MQ

activate 客服中台
客服中台 -> 客服中台: 解析消息体
客服中台 -> dscloud: 存储会话
客服中台 -> 心云店员: 推送会话消息
deactivate 客服中台
deactivate 心云店员

@enduml

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.2.1 前端交互接口

#### 

### 5.2.2 对外接口

**https://doc.weixin.qq.com/doc/w3_AZcARQYcAOwhK638y3nTrKGFJbK18?scode=AOsAFQcYAAcaPTh3C1AZcARQYcAOw**

## 5.3 涉及数据库

### 5.3.1 客服消息会话表

sqlcreate table customer_store_session
(       
    id                  bigint auto_increment
        primary key,
    type                tinyint                                                      not null comment '类型(0-单聊,1-多客户群聊)',
    third_platform_code varchar(20)                                                  not null comment '平台编码',
    online_store_code   varchar(20)                                                  not null comment '线上门店编码',
    store_code          varchar(20)                                                  not null comment '门店编码',
    third_origin_id     bigint                                                       not null comment '三方会话发起人id',
    last_message_no     bigint                                                       null comment '会话中的最后一次消息id',
    extend_json         json                                                         null default null comment '拓展字段',
    created             datetime                                                     null default null comment '平台创建时间',
    updated             datetime                                                     null default null comment '平台更新时间',
    created_by          varchar(50) character set utf8mb4 collate utf8mb4_general_ci null default null comment '创建人',
    updated_by          varchar(50) character set utf8mb4 collate utf8mb4_general_ci null default null comment '更新人',
    sys_create_time     datetime                                                          default current_timestamp comment '系统创建时间',
    sys_update_time     datetime                                                          default current_timestamp on update current_timestamp comment '系统更新时间',
    version             bigint                                                       null default null comment '数据版本，每次update+1',
    constraint uk_platform_store_id
        unique (third_platform_code, online_store_code, store_code, third_origin_id)
)
    comment '客服消息会话表';

create index idx_time_store
    on customer_store_session (updated, store_code);

### 5.3.2 客服聊天消息表

sqlcreate table customer_message
(
    id              bigint auto_increment
        primary key,
    message_no      bigint                                                       not null comment '消息id',
    msg_source      int                                                          not null comment '消息来源：1-商家 2-用户 3-员工 4-客服',
    sender_id       varchar(20)                                                  not null comment '发送方id',
    receive_id      varchar(20)                                                  not null comment '接收方id（群聊时可为空）',
    session_no      bigint                                                       not null comment '会话id',
    msg_type        int                                                          null comment '消息类型',
    msg_context     text                                                         null comment '消息内容',
    msg_status      int                                                          null comment '消息状态(0-未读,1-已读)',
    push_status     int                                                          null comment '推送状态(0-未推送,1-已推送)',
    send_status     int                                                               default 1 null comment '发送状态 0-失败,1-成功',
    push_time       datetime                                                          default current_timestamp not null comment '推送时间',
    extend_json     json                                                              default null comment '拓展字段',
    created         datetime                                                     null default null comment '平台创建时间',
    updated         datetime                                                     null default null comment '平台更新时间',
    created_by      varchar(50) character set utf8mb4 collate utf8mb4_general_ci null default null comment '创建人',
    updated_by      varchar(50) character set utf8mb4 collate utf8mb4_general_ci null default null comment '更新人',
    sys_create_time datetime                                                          default current_timestamp comment '系统创建时间',
    sys_update_time datetime                                                          default current_timestamp on update current_timestamp comment '系统更新时间',
    version         bigint                                                       null default null comment '数据版本，每次update+1'
)
    comment '客服聊天消息表';

create index idx_session_time
    on customer_message (session_no, created);

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2025年0月19日;**

**研发时间：**

**测试时间：**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等