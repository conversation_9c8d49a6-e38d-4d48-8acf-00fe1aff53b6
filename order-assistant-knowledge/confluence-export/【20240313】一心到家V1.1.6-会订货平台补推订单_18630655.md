# 【20240313】一心到家V1.1.6-会订货平台补推订单

# 一、需求分析

## 1.1 业务流程

[1.1.6 会员登录调整兼容&其他商城功能优化 - 产品部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=18624778)

# 二、目标

**2.1 本期目标**

- 完成需求内容


# 三、整体设计

## 3.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 3.2 流程图

# 四、详细设计

## 4.1 详细模块设计

## 4.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
|  | third_purchase_order_repush | 三方订单推送失败记录表 | CREATE TABLE `third_purchase_order_repush` (   `id` bigint NOT NULL COMMENT '订单id，与order_info表id保持一致',   `third_purchase_order_no` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方云仓平台采购订单号',   `third_purchase_order_status` tinyint DEFAULT NULL COMMENT '第三方云仓平台采购订单状态 1 创建成功 2 创建失败',   `sp_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商（服务商）编码',   `sp_type` tinyint DEFAULT NULL COMMENT '供应商（服务商）类型 0：其他；1、京东慧采；2、会订货',   `create_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',   `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',   `repush_count` int DEFAULT '0' COMMENT '重推次数',   `remark` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',   PRIMARY KEY (`id`),   KEY `idx_third_purchase_order_no` (`third_purchase_order_no`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='三方订单推送失败记录表'; |


## 4.3 接口设计

### 4.3.1 前端交互接口（新增）

#### 1 API-订单补推分页列表

1. url:/1.0/third-purchase-order/page
2. 请求类型：POST
3. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orderNo ``` | Long | 是 | 订单号 |
| ``` thirdPurchaseOrderNo ``` | String | 是 | 三方订单号 |
| ``` thirdPurchaseType ``` | enum | 是 | 云仓服务商类型可用值：``` OTHER：其他 JDHC：京东慧采 HDH：会订货 ``` |
| ``` thirdPurchaseOrderStatus ``` | enum | 是 | 创建状态：``` SUCCESS：创建成功 `````` FAIL：创建失败 CANCEL：订单已取消 ``` |
| ``` startTime ``` | String | 是 | 开始时间，示例值：2024-01-01 00:00:00 |
| ``` endTime ``` | String | 是 | 结束时间，示例值：2024-01-01 00:00:00 |
  2. 示例{
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 1,
        "totalPage": 1,
        "data": [
            {
                "orderNo": 200,
                "thirdPurchaseOrderNo": "2001",
                "thirdPurchaseType": "OTHER",
                "thirdPurchaseOrderStatus": "SUCCESS",
                "createTime": "2024-03-15 11:05:33"
            }
        ]
    },
    "timestamp": 1710473648586
}
4. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` currentPage ``` | ``` int ``` | 是 | ``` 当前页，从第1页开始，不传默认为1 ``` |
| ``` pageSize ``` | ``` int ``` | 是 | ``` 每页显示条数，不传默认20 ``` |
| ``` orderNo ``` | Long | 是 | 订单号 |
| ``` thirdPurchaseOrderNo ``` | String | 是 | 三方订单号 |
| ``` thirdPurchaseType ``` | enum | 是 | 云仓服务商类型可用值：``` OTHER：其他 JDHC：京东慧采 HDH：会订货 ``` |
| ``` thirdPurchaseOrderStatus ``` | enum | 是 | 创建状态：``` SUCCESS：创建成功 `````` FAIL：创建失败 CANCEL：订单已取消 ``` |
| ``` startTime ``` | String | 是 | 开始时间，示例值：2024-01-01 00:00:00 |
| ``` endTime ``` | String | 是 | 结束时间，示例值：2024-01-01 00:00:00 |
5. 示例{
    "code": "10000",
    "msg": "操作成功",
    "data": {
        "currentPage": 1,
        "pageSize": 20,
        "totalCount": 1,
        "totalPage": 1,
        "data": [
            {
                "orderNo": 200,
                "thirdPurchaseOrderNo": "2001",
                "thirdPurchaseType": "OTHER",
                "thirdPurchaseOrderStatus": "SUCCESS",
                "createTime": "2024-03-15 11:05:33"
            }
        ]
    },
    "timestamp": 1710473648586
}
6. 响应体：
  1. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` totalCount ``` |  | int | ``` 总数 ``` |  |
| ``` totalPage ``` |  | int | 总页数 |  |
| ``` data ``` |  | List<Object> | 数据 |  |
|  | ``` orderNo ``` |  | 订单号 | third_purchase_order_repush |
|  | ``` thirdPurchaseOrderNo ``` |  | 三方订单号 |
|  | ``` thirdPurchaseType ``` |  | 云仓服务商类型可用值：``` OTHER：其他 JDHC：京东慧采 HDH：会订货 ``` |
|  | ``` thirdPurchaseOrderStatus ``` |  | 创建状态：``` SUCCESS：创建成功 `````` FAIL：创建失败 CANCEL：订单已取消 ``` |
|  | ``` createTime ``` |  | 订单创建时间 |
  2. 示例值{
  "currentPage": 0,
  "data": [
    {
      "createTime": "string",
      "orderNo": 0,
      "thirdPurchaseOrderNo": "string",
      "thirdPurchaseOrderStatus": 0,
      "thirdPurchaseType": 0
    }
  ],
  "pageSize": 0,
  "totalCount": 0,
  "totalPage": 0
}
7. | 字段名 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| ``` totalCount ``` |  | int | ``` 总数 ``` |  |
| ``` totalPage ``` |  | int | 总页数 |  |
| ``` data ``` |  | List<Object> | 数据 |  |
|  | ``` orderNo ``` |  | 订单号 | third_purchase_order_repush |
|  | ``` thirdPurchaseOrderNo ``` |  | 三方订单号 |
|  | ``` thirdPurchaseType ``` |  | 云仓服务商类型可用值：``` OTHER：其他 JDHC：京东慧采 HDH：会订货 ``` |
|  | ``` thirdPurchaseOrderStatus ``` |  | 创建状态：``` SUCCESS：创建成功 `````` FAIL：创建失败 CANCEL：订单已取消 ``` |
|  | ``` createTime ``` |  | 订单创建时间 |
8. 示例值{
  "currentPage": 0,
  "data": [
    {
      "createTime": "string",
      "orderNo": 0,
      "thirdPurchaseOrderNo": "string",
      "thirdPurchaseOrderStatus": 0,
      "thirdPurchaseType": 0
    }
  ],
  "pageSize": 0,
  "totalCount": 0,
  "totalPage": 0
}


#### 2 API-订单取消

1. url: /1.0/third-purchase-order/cancel?orderNo=XXX
2. 请求类型：PUT
3. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |  |
  2. 请求示例：/1.0/third-purchase-order/cancel?orderNo=1
4. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |  |
5. 请求示例：/1.0/third-purchase-order/cancel?orderNo=1
6. 响应体：
  1. {
    "code": 31019,
    "msg": "订单不存在",
    "data": null,
    "timestamp": 1710469006024
}
7. {
    "code": 31019,
    "msg": "订单不存在",
    "data": null,
    "timestamp": 1710469006024
}


#### 3 API-订单补推

1. url: /1.0/third-purchase-order/repush?orderNo=XXX
2. 请求类型：PUT
3. 请求体：
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |  |
  2. 请求示例：/1.0/third-purchase-order/repush?orderNo=1
4. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |  |
5. 请求示例：/1.0/third-purchase-order/repush?orderNo=1
6. 响应体：
  1. | 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |
| ``` thirdPurchaseOrderNo ``` | String | 三方单号 |
| ``` result ``` | Boolean | 是否创建成功 |
| ``` errorMsg ``` | String | 错误消息 |
  2. 异常：{
  "code": "string",
  "data": {
    "errorMsg": "string",
    "orderNo": "string",
    "result": true,
    "thirdPurchaseOrderNo": "string"
  },
  "msg": "string",
  "timestamp": 0
}
7. | 字段名 | 字段类型 | 字段描述 |
| --- | --- | --- |
| ``` orderNo ``` | Long | ``` 订单号 ``` |
| ``` thirdPurchaseOrderNo ``` | String | 三方单号 |
| ``` result ``` | Boolean | 是否创建成功 |
| ``` errorMsg ``` | String | 错误消息 |
8. 异常：{
  "code": "string",
  "data": {
    "errorMsg": "string",
    "orderNo": "string",
    "result": true,
    "thirdPurchaseOrderNo": "string"
  },
  "msg": "string",
  "timestamp": 0
}


## 4.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 4.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 五、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 六、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 七、项目排期

**设计文档输出： 设计评审：**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）**

**联调时间：2023年12月13日-2023年11月15日**

**测试时间：2023年11月18日-2023年11月20日**

**上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 开发时间 | 负责人 | 进度 |
| --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |


# 八、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等