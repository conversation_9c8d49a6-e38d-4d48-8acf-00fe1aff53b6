# 重构YXT订单中台V1.0

> 头脑风暴1.0

### 订单同步、数据回传

true拉单、数据回传falseautotoptrue8617

b2c: api方式

线下单: 

会员: 看线上单和线上单,需要聚合

todo:

  8 incomplete 查看线下单的数据结构   

### 当前痛点和优化需要注意的点

#### 现在的痛点

- 目前部分平台订单推送是由.net项目承接 , 订单关键节点数据的回传也是由.net项目做一层中转,而非java项目,和后端业务系统研发主流技术栈差异较大,痛点较多:
  - 因不同的技术栈,系统维护和迭代难度较大,无法很好的发挥团队效能
  - 入口和出口有多处(.net、java), 扩展能力弱。不能很好的做一些抽象，例如订单同步、发货、确认收货等节点的抽象
  - 无法做统一的动作,例如接入公共组件、统一系统调优、动态扩缩容等等
  - 处理链路较长
- 因不同的技术栈,系统维护和迭代难度较大,无法很好的发挥团队效能
- 入口和出口有多处(.net、java), 扩展能力弱。不能很好的做一些抽象，例如订单同步、发货、确认收货等节点的抽象
- 无法做统一的动作,例如接入公共组件、统一系统调优、动态扩缩容等等
- 处理链路较长
- 因为历史原因,订单OMS项目代码臃肿,逐渐不可控,定位和排查问题较为困难:
  - Java对象定义不清晰,很多Java对象跨层使用。
  - Bean的跨层使用。新项目中应严格控制Bean的职责及范围,可以很大程度避免事务失效、死锁、循环依赖等问题
  - 大方法。方法行数多、大而全、嵌套严重。
  - 订单表的一些设计因为项目迭代及历史等各种原因渐渐的被忽略了，例如版本号。如果做统一的入口管控就能很大程度的这类问题的发生，即使如果后面发现一些设计渐渐的被忽视了，因为做了统一的收口,也很容易做统一的调整。
- Java对象定义不清晰,很多Java对象跨层使用。
- Bean的跨层使用。新项目中应严格控制Bean的职责及范围,可以很大程度避免事务失效、死锁、循环依赖等问题
- 大方法。方法行数多、大而全、嵌套严重。
- 订单表的一些设计因为项目迭代及历史等各种原因渐渐的被忽略了，例如版本号。如果做统一的入口管控就能很大程度的这类问题的发生，即使如果后面发现一些设计渐渐的被忽视了，因为做了统一的收口,也很容易做统一的调整。


#### 优化注意点

- 良好的代码分层
- 清晰(职责分离)的Java对象定义,禁止跨层使用
- 清晰的方法定义及命名,不追求大而全的方法,追求粒度小,职责清晰的方法。如果某一个业务方法逻辑较为复杂,应做好设计，做好功能拆分,当这些工作完成,可以使用一个方法将这些方法聚合
- 禁止依赖倒置
- 将一些相对固定、长期不变的业务下沉到原子服务


### 原子服务和聚合服务

通过聚合服务达到业务逻辑的实现，原子服务则是对不变的业务进行原子化，同时也确定业务的职责、边界。

#### 构建订单OMS原子服务,期望可以实现以下目标:

- 订单OMS有多个服务,这些服务或多或少都有DB、Redis等操作,因此存在重复代码。构建原子服务后,只需要在一个地方做一次代码维护
- 分库分表。如果不做原子服务,每个涉及的项目都需要做分库分表,成本较大。构建原子服务之后,只需要在原子服务做改动,依赖原子服务的项目代码无需变动
- 运维层面运维粒度更小
- SQL优化
- 订单快照实现
- 可以反推聚合服务的优化。例如一次性查大量数据操作、非批量化的操作,如果接入原子服务就会检查到这些问题,并做出优化措施
- 可以更好的基于mock做单元测试


#### 构建原子服务的弊端:

- 对上线顺序有要求。如果需求涉及原子服务的改动需要评估好上线顺序
- 多了一次网络请求
- 存在中心化问题，类似的DB也存在中心化问题,DB挂应用挂,无解。我们能做的只需在有限的条件下寻找最优解


### 核心业务和非核心业务

使用MQ将核心业务和非核心业务目的是:

- 使主线业务更加清晰
- 避免非核心业务异常阻断主线业务
- 提升订单OMS系统核心业务的处理能力
- 使用MQ来构建非核心业务可以拓展处很多功能,例如故障自愈、运维工具等。同时使用MQ来解耦非核心业务相当于是对这部分业务做了一个收口,便于问题的排查


### 所有使用到MQ的地方需要做统一的兜底+幂等操作

trueMQ兜底方案falseautotoptrue5381

基于MQ的解耦方案一般可以实现以下功能:

1. 故障自愈。通过 异常+异常处理策略 可以实现故障自愈
2. 业务补偿。
3. 运维工具服务
4. 关键节点溯源


### 配送平台

true配送平台falseautotoptrue7002

#### 目前痛点:

下配送单、配送平台回传都在.net项目,会有以下问题:

- 因不同的技术栈,系统维护和迭代难度较大,无法很好的发挥团队效能
- 处理链路较长
- 无法做统一的动作,例如接入公共组件、统一系统调优、动态扩缩容等等


#### 优化注意点,除了[订单同步、数据回传]提到的一些注意点,还需要注意:

- 在设计上,按照职责尽量分开,例如策略、配送方等数据,在交互和编码方面尽量通过配送模型对象来交互,与具体的DB映射对象隔离开,不做强绑定
- 因为对接三方平台,部分场景需要做好重试策略,减少类似网络超时之类的异常而造成的业务流程中断问题
- 完善日志记录,便于问题排查.敏感信息做好脱敏


**期望优化可以实现一下目标:**

- 将订单整个配送相关的业务独立为一个单独的Java服务,进行迭代与维护。
- 重新梳理配送相关业务,抽象公共逻辑,提升拓展性,在承接后续配送相关业务时尽可能缩小成本
- 公共组件更方便的接入
- 统一接入订单OMS的原子服务


### 头脑风暴1.0其余优化点

1. 统一状态
  1. 新增字段使用字符串明确的表示状态的含义,不使用数字
  2. 现有代码新增状态枚举,逐渐替代代码中的 **DsConstants.INTEGER_ZERO**这类含义不清的字段
2. 新增字段使用字符串明确的表示状态的含义,不使用数字
3. 现有代码新增状态枚举,逐渐替代代码中的 **DsConstants.INTEGER_ZERO**这类含义不清的字段
4. 字段冗余,目前订单部分表没有做适当的字段冗余,导致问题排查效率低和连表过多慢SQL等问题
5. 新项目使用DDD来构建
6. 订单金额计算收口,期望可以基于金额模型来与订单表主数据隔离开。添加拓展接口来实现可扩展


#### DDD相关资料

[https://cloud.tencent.com/developer/article/2313016?areaId=106001](https://cloud.tencent.com/developer/article/2313016?areaId=106001) 对传统架构和DDD架构做了很详细的阐述
[https://insights.thoughtworks.cn/backend-development-ddd/](https://insights.thoughtworks.cn/backend-development-ddd/) 在代码层面对DDD做了直观的展现,便于一开始的上手
[https://cloud.tencent.com/developer/article/1078532](https://cloud.tencent.com/developer/article/1078532)

参考代码: [https://github.com/e-commerce-sample/ecommerce-order-service](https://github.com/e-commerce-sample/ecommerce-order-service) 

todo:

  9 incomplete 如何落地?  10 incomplete 梳理心云系统接口文档   11 incomplete 订单统一模型，需要找到每个平台的订单结构     12 incomplete 项目  13 incomplete new-api，页面查询，新云系统交互   14 complete 原子服务     16 complete DDD