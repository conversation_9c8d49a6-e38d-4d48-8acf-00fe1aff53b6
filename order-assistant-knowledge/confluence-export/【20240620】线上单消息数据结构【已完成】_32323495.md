# 【20240620】线上单消息数据结构【已完成】

### 正单消息

### Table for OrderModel Class

| 属性名 | 注释 | 字段类型 |
| --- | --- | --- |
| merCode | 商家编码 | String |
| thirdPlatformCode | 三方平台编码11-京东到家 27-美团 24-饿百 43-微商城 44-平安中心仓 45-平安O2O 46-平安城市仓 48-阿里健康 1001-京东健康 9998-电商第三方标准平台 3002-淘宝 3003-拼多多B2C 3004-京东B2C 9002-抖店 3008-快手B2C0-其他渠道 | String |
| orderNo | 订单号 | Long |
| omsOrderNo | B2C系统订单号 | Long |
| thirdOrderNo | 三方平台订单号 | String |
| orderStatus | 订单状态 OrderStateEnum 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭 | Integer |
| orderType | 订单类型 OrderTypeEnum 0普通订单,1预约订单,2处方订单,3积分订单 5运费单 30机器自动拣货订单 | Integer |
| onlineStoreCode | （履约）下单线上门店code | String |
| onlineStoreName | （履约）线上门店名称 | String |
| organizationCode | （履约）线下门店编码 | String |
| organizationName | （履约）线下门店名称 | String |
| sourceOnlineStoreCode | （接单）下单线上门店code-来源 | String |
| sourceOnlineStoreName | （接单） 线上门店名称-来源 | String |
| sourceOrganizationCode | （接单）线下门店编码-来源 | String |
| sourceOrganizationName | （接单）线下门店名称-来源 | String |
| payType | 支付类型 1是在线支付,2是货到付款 3-医保 4-优惠券 | String |
| payTime | 支付时间 | Date |
| payInfoDataList | 列表对象[  {  "payType": "1", // 1是在线支付,2是货到付款 3-医保 4-优惠券  "payAmount": 0.00 // 支付金额  } ] | List |
| deliveryType | 配送方式 1-平台配送 2-平台合作方配送 3-自配送 4-到店自取5-快递 | String |
| totalActualOrderAmount | 订单总金额 buyer_actual_amount 即实付金额(客户付的的钱) | BigDecimal |
| actualFreightAmount | 实际运费金额 delivery_fee | BigDecimal |
| memberCard | 会员卡号 | String |
| memberId | 会员ID | Long |
| created | 订单在第三方的创建时间 | Date |
| createTime | 创建时间 落库时间 | Date |
| updateTime | 更新时间 |  |
| acceptTime | 接单时间 | Date |
| completeTime | 完成时间 | Date |
| serviceMode | 服务模式 O2O B2C | String |
| detailList | 订单明细(排除了换货的明细) | List<OrderDetailModel> |
| originalDetailList | 原始订单明细 | List<OrderDetailModel> |
| erpState | 下账状态: 20 待锁库存 30 待下帐 100 已下账 110 已取消 | Integer |


### Table for OrderDetailModel Class

| 属性名 | 注释 | 字段类型 |
| --- | --- | --- |
| orderNo | 订单号 | Long |
| commodityCode | 商品编码 | String |
| commodityName | 商品名称 | String |
| commodityCount | 商品个数 | Integer |
| totalActualAmount | 成交总额=小计金额-优惠实际下账的金额 20240730 | BigDecimal |
| billPrice | 下账单价 | BigDecimal |
| goodsType | 商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品 | Integer |
| isJoint | 是否是组合商品 0否1是 | Integer |
| mainPic | sku主图 | String |
| isOriginal | 是否是原始明细 0-是 1-否 | Integer |
| isGift | 是否是赠品（0，不是赠品,1是赠品） | Integer |
| status | 订单明细状态serviceMode=O2O：0-正常 1-库存不足异常 2-商品不存在 10-已换货 11-已退款serviceMode=B2C：0-正常 1-已退款 2-被换货的商品 3-组合商品 11-库存不足异常 12-商品不存在 | Integer |
| createTime | 创建时间 | Date |
| updateTime |  |  |
| averagePrice | 不含税成本价 | BigDecimal |
| taxPrice | 含税成本价 | BigDecimal |
| taxRate | 税率 | String |


### 退单消息

### Table for RefundOrderModel

| Property Name | Comment | Type |
| --- | --- | --- |
| merCode | 商家编码 | String |
| thirdPlatformCode | 三方平台编码11-京东到家 27-美团 24-饿百 43-微商城 44-平安中心仓 45-平安O2O 46-平安城市仓 48-阿里健康 1001-京东健康 9998-电商第三方标准平台 3002-淘宝 3003-拼多多B2C 3004-京东B2C 9002-抖店 3008-快手B2C0-其他渠道 | String |
| refundNo | 退单号 | Long |
| thirdRefundNo | 三方平台退款ID | String |
| orderNo | 正向订单号 | Long |
| thirdOrderNo | 第三方平台订单号 | String |
| completeTime | 退款完成时间 | Date |
| refundStatus | 退款单状态 RefundStateEnum 退款单状态 10 待退款 ;20 待退货; 100 已完成; 102 已拒绝; 103 已取消 | Integer |
| memberCard | 会员卡号 | String |
| memberId | 会员Id | Long |
| organizationCode | 线下门店编码 | String |
| onlineStoreCode | 线上门店编码 | String |
| sourceOrganizationCode | 来源线下门店编码 | String |
| sourceOnlineStoreCode | 来源线上门店编码 | String |
| consumerRefund | 退款金额 consumer_refund 退买家的金额 | BigDecimal |
| platformRefundDeliveryFee | 退平台配送费 | BigDecimal |
| merchantRefundPostFee | 退商家配送费 | BigDecimal |
| createTime | 创建时间 | Date |
| detailList | 商品明细 | List<RefundDetailModel> |
| reCalculateOriginOrderFlag | 未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算 20240802 | Integer |
| erpState | 退款单下账状态: 20 待下帐 100 已下账 120 已取消 | Integer |
| updateTime | 更新时间 |  |


### Table for RefundDetailModel

| Property Name | Comment | Type |
| --- | --- | --- |
| refundNo | 退单号 | Long |
| commodityCode | 商品编码 | String |
| commodityName | 商品名称 | String |
| commodityCount | 商品数量 | Integer |
| billPrice | 下账单价 | BigDecimal |
| isGift | 是否是赠品（0，不是赠品,1是赠品） | Integer |
| createTime | 创建时间 | Date |
| updateTime |  |  |


RocketMQ topic信息

| topic | tag | 说明 |
| --- | --- | --- |
| ``` TP_ORDER_BUSINESS-ORDER_ORDER-MODEL ``` | TAG_ORDER_MODEL | 线上单正单topic |
| ``` TP_ORDER_BUSINESS-ORDER_REFUND-ORDER-MODEL ``` | TAG_REFUND_ORDER_MODEL | 线上单退单topic |


上线配置:

Apollo

truemessage-notify:
  order-model-topic: TP_ORDER_BUSINESS-ORDER_ORDER-MODEL
  refund-order-model-topic: TP_ORDER_BUSINESS-ORDER_REFUND-ORDER-MODEL

表

sqltrue
CREATE TABLE `order_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
    `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
		  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码',
    `order_no` bigint NOT NULL COMMENT '订单号，雪花算法',
`third_order_no` varchar(100) NOT NULL COMMENT '第三方平台订单号',
     `order_status` tinyint DEFAULT NULL COMMENT '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',
    `order_type` tinyint DEFAULT '0' COMMENT '订单类型:0普通订单,1预约订单,2处方订单,3积分订单,5运费订单,30机器自动拣货订单',
    `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店编码',
  `online_store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下单线上门店名称',
  `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线下门店名称',
    `source_online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
  `source_online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店名称',
  `source_organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店名称',
    `pay_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付方式,1是在线支付,2是货到付款吧',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `delivery_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配送方式',
    `total_actual_order_amount` decimal(16,2) DEFAULT '0.00' COMMENT '客户实付',
    `actual_freight_amount` decimal(16,2) DEFAULT '0.00' COMMENT '配送费',
    `member_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',
		 `member_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员唯一标示',
    `created` datetime DEFAULT NULL COMMENT '订单实际创建时间',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
    `accept_time` datetime DEFAULT NULL COMMENT '接单时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'O2O' COMMENT '服务模式',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_order_no` (`order_no`) USING BTREE,
  KEY `idx_create_time` (`create_time`),
  KEY `i_mer_online_code` (`mer_code`,`online_store_code`) USING BTREE,
  KEY `idx_code` (`mer_code`,`organization_code`,`id` DESC),
  KEY `idx_created` (`created`,`mer_code`) USING BTREE,
  KEY `idx_source_organization_code` (`source_organization_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单模型数据'; 

  
CREATE TABLE `order_detail_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` bigint NOT NULL COMMENT '订单号',
  `commodity_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
  `commodity_count` int DEFAULT NULL COMMENT '商品数量',
  `total_actual_amount` decimal(16, 2) DEFAULT '0.00' COMMENT '成交总额小计-优惠',
  `bill_price` decimal(16, 4) NOT NULL COMMENT '下账价格',
  `goods_type` tinyint(1) DEFAULT '1' COMMENT '商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品',
  `is_joint` tinyint DEFAULT '0' COMMENT '是否是组合商品，0不是组合商品，1是组合商品',
  `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',
  `is_gift` tinyint NOT NULL DEFAULT '0' COMMENT '是否是赠品（0不是赠品，1是赠品）',
  `status` tinyint DEFAULT '0' COMMENT '明细状态，0, "正常",1, "库存不足异常",2, "商品不存在",10, "已换货",11, "已退款"',
  `is_original` tinyint DEFAULT '0' COMMENT '是否是原始明细 0-是 1-否',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP comment '创建时间',
  `average_price` decimal(16, 6) DEFAULT NULL COMMENT '商品不含税加权成本价',
  `tax_price` decimal(16, 6) DEFAULT NULL COMMENT '商品含税加权成本价',
  `tax_rate` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '税率',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no` (`order_no`) USING BTREE,
  KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC COMMENT = '订单明细模型数据'    


 CREATE TABLE `refund_order_model` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
      `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
			  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码',
     `refund_no` bigint NOT NULL COMMENT '退款单号，雪花算法',
     `third_refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台退款ID',
     `order_no` bigint NOT NULL COMMENT '订单号',
     `third_order_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单号',
        `complete_time` datetime DEFAULT NULL COMMENT '退款完成时间',
    `refund_status` tinyint DEFAULT NULL COMMENT '退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消',
    `member_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',
		 `member_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员唯一标示',
    `organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线上门店编码',
  `source_organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
    `consumer_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退买家总金额',
      `platform_refund_delivery_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退平台配送费',
     `merchant_refund_post_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商家配送费',
      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `refund_no` (`refund_no`) USING BTREE,
  KEY `idx_order` (`order_no`),
  KEY `idx_mer_create_time` (`mer_code`,`create_time`),
  KEY `idx_store` (`mer_code`,`online_store_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款单模型表';

 
CREATE TABLE `refund_detail_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` bigint NOT NULL COMMENT '退款单号',
    `commodity_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
    `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
    `commodity_count` int NOT NULL COMMENT '退款数量',
		 `bill_price` decimal(16,4) NOT NULL COMMENT '下账价格',
    `is_gift` tinyint NOT NULL DEFAULT '0' COMMENT '是否是赠品（0不是赠品，1是赠品）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_no` (`refund_no`) USING BTREE COMMENT '退款款单索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款明细模型表';

-- 20240726
ALTER TABLE `order_model` 
DROP COLUMN `pay_type`,
ADD COLUMN `pay_info_json` json NULL COMMENT '支付信息json' AFTER `service_mode`;   

CREATE TABLE `order_commodity_detail_cost_price` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `service_mode` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务模式(O2O、B2C)',
  `order_no` bigint NOT NULL COMMENT '订单号(B2C是oms_order_no,O2O是order_no)',
  `erp_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品erp编码',
  `make_no` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品批号',
  `batch_no` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品批次',
  `cost_price` decimal(16, 6) DEFAULT NULL COMMENT '商品成本价',
  `average_price` decimal(16, 6) DEFAULT NULL COMMENT '商品不含税加权成本价',
  `tax_price` decimal(16, 6) DEFAULT NULL COMMENT '商品含税加权成本价',
  `tax_rate` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '税率',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `index_erp_code` (`erp_code`),
  KEY `index_order_no` (`order_no`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC COMMENT = '订单商品明细成本价'

### 依赖项:

  2 incomplete 依赖根据会员卡号获取会员ID接口,需确认会员侧是否上线   4 complete 世达business-order-web release-onlineOrderDataPush-20240617 补充completeTime```

```  

数据库存储对象
order_info



CREATE TABLE `order_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` bigint NOT NULL COMMENT '订单号，雪花算法',
  `order_state` tinyint DEFAULT NULL COMMENT '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',
  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码',
  `third_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单号',
  `third_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单id,消息通知时使用',
  `third_order_state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方平台订单状态',
  `off_state` int DEFAULT NULL COMMENT '接口中台的订单状态',
  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
  `client_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网店编码',
  `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店编码',
  `online_store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下单线上门店名称',
  `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线下门店名称',
  `delivery_time_type` tinyint DEFAULT NULL COMMENT '送达方式，即时或预约',
  `delivery_time_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '期望送达时间描述',
  `seller_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '卖家备注',
  `buyer_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '买家备注',
  `buyer_message` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '买家留言',
  `lock_flag` tinyint DEFAULT '0' COMMENT '锁定标志: 0未锁定,10取消锁定,20退款锁定,31库存不足,32部分商定不存在,33金额异常,34配送异常',
  `lock_msg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异常锁信息',
  `locker_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '锁定者id',
  `remind_flag` tinyint DEFAULT '0' COMMENT '催单标志,0未催，1已催促',
  `buyer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台买家昵称',
  `receiver_lat` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收货地址纬度',
  `receiver_lng` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收货地址经度',
  `acceptor_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接单操作员id',
  `acceptor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '接单名',
  `accept_time` datetime DEFAULT NULL COMMENT '接单时间',
  `picker_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货操作员id',
  `picker_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货员名',
  `pick_operator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货实施者',
  `pick_operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拣货操作者名',
  `pick_time` datetime DEFAULT NULL COMMENT '拣货时间',
  `canceller_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '取消者id',
  `canceller_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '取消操作者名',
  `cancel_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '取消原因',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `ex_operator_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异常操作者id',
  `ex_operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异常操作者名',
  `ex_operator_time` datetime DEFAULT NULL COMMENT '异常操作时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `created` datetime DEFAULT NULL COMMENT '订单实际创建时间',
  `day_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '每日号',
  `modified` datetime DEFAULT NULL COMMENT '订单实际修改时间',
  `erp_adjust_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货位调整单',
  `erp_sale_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '零售流水',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',
  `prescription_flag` tinyint DEFAULT '0' COMMENT '处方药（1：需要审方 0：不需要审方）',
  `self_verify_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '骑手取货码',
  `erp_state` tinyint DEFAULT '20' COMMENT '下账状态: 20 待锁库存 30 待下帐  100 已下账  110 已取消',
  `bill_time` datetime DEFAULT NULL COMMENT '下账时间',
  `call_erp_flag` tinyint NOT NULL DEFAULT '1' COMMENT '是否调用ERP接口标识（0不调用 1调用）',
  `member_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',
  `transfer_delivery` tinyint(1) DEFAULT '0' COMMENT '转仓发货: 0-未转仓, 1-已转仓',
  `client_conf_id` bigint DEFAULT NULL COMMENT '网店配置ID',
  `bill_operator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下账操作人',
  `is_prescription` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是审方单（结合审方配置）',
  `prescription_status` tinyint NOT NULL DEFAULT '0' COMMENT '处方状态 0-待审  1-通过  2-不通过  3-取消',
  `is_push_check` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推送审方平台',
  `appointment` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-正常订单，1-预约订单',
  `appointment_business_flag` tinyint(1) DEFAULT NULL COMMENT '预约单业务处理状态 0-待处理 1-已处理',
  `appointment_business_type` tinyint(1) DEFAULT NULL COMMENT '预约单业务处理类型 0-修改门店 1-确认到货',
  `new_customer_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '新客标识，0：非新客 1：新客',
  `integral_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '积分订单标识，0：非积分订单，1：积分订单',
  `request_deliver_goods_result` tinyint(1) DEFAULT NULL COMMENT '申请代发服务商处理结果,1-拒单，2-发货',
  `deliver_goods_refuse_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代发拒单理由',
  `invoice_content` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票内容',
  `invoice_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票抬头',
  `invoice_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '发票类型',
  `need_invoice` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '是否开票，1.开发票；2.不开发票',
  `taxer_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '纳税人识别码',
  `source_online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
  `source_online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店名称',
  `source_organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店名称',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统备注',
  `service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'O2O' COMMENT '服务模式',
  `pay_time` datetime DEFAULT NULL COMMENT '创建时间',
  `vat_taxpayer_number` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '增值税纳税人识别号',
  `invoice_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `order_type` tinyint DEFAULT '0' COMMENT '订单类型:0普通订单,1预约订单,2处方订单,3积分订单',
  `order_is_new` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否新订单: 1-新订单,2老订单',
  `data_version` bigint DEFAULT '0' COMMENT '数据版本,update订单信息默认+1',
  `complex_modify_flag` tinyint DEFAULT '0' COMMENT '复杂换货标识，0未参与复杂换货，1已参与复杂换货',
  `medical_insurance` tinyint DEFAULT '0' COMMENT '0-非医保订单,1-医保订单',
  `cancel_bill_times` int DEFAULT '0' COMMENT '已下账后取消下账次数',
  `wsc_ext_json` varchar(1100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微商城扩展字段',
  `top_hold` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'TOP拦截标识，0不拦截，1拦截 null=平台无此标识',
  `order_pick_type` tinyint DEFAULT NULL COMMENT '订单实际拣货类型 1-人工拣货 2-机器自动拣货',
  `is_video_flag` tinyint DEFAULT '0' COMMENT '是否视频号订单 0否  1是',
  `source_channel_type` tinyint(1) DEFAULT NULL COMMENT '来源渠道类型 1-京东渠道',
  `migration_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台迁移订单号-旧系统迁移数据的订单号',
  `extend_info` json DEFAULT NULL COMMENT '扩展信息',
  `freight_order_no` bigint DEFAULT NULL COMMENT '运费单系统订单号',
  `deleted` bigint DEFAULT '0' COMMENT '逻辑删除字段 默认0-未删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_order_no` (`order_no`) USING BTREE,
  UNIQUE KEY `migration_order_no_index` (`migration_order_no`) USING BTREE,
  UNIQUE KEY `third_order_plat_code` (`third_order_no`,`third_platform_code`,`deleted`),
  KEY `idx_create_time` (`create_time`),
  KEY `i_mer_online_code` (`mer_code`,`online_store_code`) USING BTREE,
  KEY `idx_code` (`mer_code`,`organization_code`,`id` DESC),
  KEY `idx_bill_time` (`bill_time`,`organization_code`),
  KEY `idx_order_info_mer_org` (`mer_code`,`organization_code`,`lock_flag`,`order_state`) USING BTREE,
  KEY `idx_order_info_pre` (`mer_code`,`organization_code`,`is_prescription`) USING BTREE,
  KEY `idx_order_info_rem` (`mer_code`,`organization_code`,`remind_flag`) USING BTREE,
  KEY `order_info_third_platform_code_IDX` (`third_platform_code`,`organization_code`,`online_store_code`) USING BTREE,
  KEY `idx_code_erp_state` (`mer_code`,`erp_state`,`organization_code`) USING BTREE,
  KEY `order_info_mer_code_IDX` (`mer_code`,`erp_sale_no`) USING BTREE,
  KEY `idx_mer_store_client` (`mer_code`,`online_store_code`,`client_code`),
  KEY `idx_third_order_no` (`third_order_no`),
  KEY `idx_created` (`created`,`mer_code`),
  KEY `idx_order_info_mer_source_org` (`order_state`,`mer_code`,`organization_code`,`lock_flag`),
  KEY `idx_source_organization_code` (`source_organization_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18937694 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基本订单信息表';

order_detail 

CREATE TABLE `order_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` bigint NOT NULL COMMENT '订单号',
  `platform_sku_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品三方平台编码',
  `erp_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
  `bar_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品条形编码',
  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
  `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',
  `commodity_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品规格',
  `commodity_count` int DEFAULT NULL COMMENT '商品数量',
  `original_price` decimal(16,2) DEFAULT '0.00' COMMENT '商品原单价',
  `price` decimal(16,2) DEFAULT '0.00' COMMENT '商品售价',
  `total_amount` decimal(16,2) DEFAULT '0.00' COMMENT '小计金额售价*数量',
  `discount_amount` decimal(16,2) DEFAULT '0.00' COMMENT '促销优惠金额',
  `actual_amount` decimal(16,2) DEFAULT '0.00' COMMENT '成交总额小计-优惠',
  `discount_share` decimal(16,2) DEFAULT '0.00' COMMENT '优惠分摊',
  `actual_net_amount` decimal(16,2) DEFAULT '0.00' COMMENT '下账金额',
  `different_share` decimal(16,2) DEFAULT '0.00' COMMENT '差异分摊',
  `status` tinyint DEFAULT '0' COMMENT '明细状态，0, "正常",1, "库存不足异常",2, "商品不存在",10, "已换货",11, "已退款"',
  `manufacture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产商',
  `swap_id` bigint DEFAULT NULL COMMENT '更换的id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `adjust_amount` decimal(16,2) DEFAULT '0.00' COMMENT '调整金额',
  `third_detail_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'null' COMMENT '第三方详情ID',
  `bill_price` decimal(16,4) NOT NULL COMMENT '下账价格',
  `is_gift` tinyint NOT NULL DEFAULT '0' COMMENT '是否是赠品（0不是赠品，1是赠品）',
  `goods_type` tinyint(1) DEFAULT '1' COMMENT '商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品',
  `refund_count` int DEFAULT '0' COMMENT '退货数量',
  `origin_type` tinyint(1) DEFAULT NULL COMMENT '预约单供应商来源 1-云货架，2-DC仓',
  `st_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预约单组织机构编码（云货架商家编码或DC仓编码）',
  `expect_delivery_time` datetime DEFAULT NULL COMMENT '预计送达时间',
  `direct_delivery_type` tinyint(1) DEFAULT NULL COMMENT '供应商代发标识，0-非供应商代发，1-供应商代发',
  `original_erp_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组合商品erpcode',
  `original_erp_code_num` int DEFAULT NULL COMMENT '组合商品原始数量',
  `oms_order_no` bigint NOT NULL DEFAULT '1' COMMENT '子订单号',
  `is_joint` tinyint DEFAULT '0' COMMENT '是否是组合商品，0不是组合商品，1是组合商品',
  `erp_gift` tinyint(1) DEFAULT '1' COMMENT '如果该商品是赠品，该值才有作用；赠品是否下账，0不下账，1下账（默认）',
  `old_erp_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '换货关系码（换货商品前后此致都相同）',
  `weight` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品重量',
  `relation_code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组合商品关系编码',
  `is_reduce_stock` tinyint(1) DEFAULT '1' COMMENT '是否扣减了库存，1是，0否',
  `chailing` tinyint DEFAULT '1' COMMENT '拆零商品标示 1不拆零 2拆零',
  `platform_discount_fee` decimal(16,2) DEFAULT '0.00' COMMENT '平台优惠',
  `merchant_discount_fee` decimal(16,2) DEFAULT '0.00' COMMENT '商家优惠',
  `brokerage_amount` decimal(16,2) DEFAULT '0.00' COMMENT '交易佣金',
  `vip_different_amt` decimal(16,2) DEFAULT '0.00' COMMENT '会员优惠金额',
  `first_type_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '一级分类名称',
  `second_type_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '二级分类名称',
  `type_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三级分类名称',
  `drug_type` tinyint(1) DEFAULT NULL COMMENT '药品类型 OTC甲类(0)/处方(1)/OTC乙类(2)/非药品(3)/OTC(4)',
  `chai_ling_original_erp_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拆零商品原始商品编码',
  `chai_ling_original_num` int DEFAULT NULL COMMENT '拆零商品原始数量',
  `settle_price` decimal(16,2) DEFAULT '0.00' COMMENT '平安结算单价',
  `modify_price_diff` decimal(16,2) DEFAULT NULL COMMENT '换货价差',
  `produce_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生产批号-药房网使用',
  `health_value` decimal(16,2) DEFAULT NULL COMMENT '健康贝换算金额',
  `extend` json DEFAULT NULL COMMENT '明细扩展信息',
  `original_oms_order_no` bigint DEFAULT NULL COMMENT '原系统订单号',
  `third_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方子订单号',
  `payment` decimal(16,2) DEFAULT '0.00' COMMENT '商品实付金额(汇总)',
  `alloc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货位编码',
  `alloc_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货位名称',
  `near_effective_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '近效期状态（0:否；1:是）',
  `average_price` decimal(16,2) DEFAULT '0.00' COMMENT '商品加权成本价',
  `chai_ling_num` int DEFAULT NULL COMMENT '拆零商品的拆零系数',
  `detail_discount` json DEFAULT NULL COMMENT '平台分摊优惠明细',
  `storage_type` tinyint DEFAULT NULL COMMENT '储存条件：0-常温，1-冷藏，2-冷冻，3-阴凉',
  `detail_settlement_status` tinyint DEFAULT NULL COMMENT '明细结算状态：0-待结算，1-已结算，9-已失效',
  `is_medicare_item` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_order_erp_details_id` (`order_no`,`erp_code`,`third_detail_id`) USING BTREE,
  KEY `index_detail_oms_no` (`oms_order_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=45827386 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单明细表';

refund_order

CREATE TABLE `refund_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `refund_no` bigint NOT NULL COMMENT '退款单号，雪花算法',
  `third_refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台退款ID',
  `order_no` bigint NOT NULL COMMENT '订单号',
  `third_order_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单号',
  `type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款类型,0部分退款，1全额退款',
  `third_status` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方退款单状态',
  `state` tinyint DEFAULT NULL COMMENT '退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消',
  `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台编码',
  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
  `client_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网店编码',
  `total_food_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退款商品总金额',
  `total_amount` decimal(16,2) DEFAULT '0.00' COMMENT '退款商品退用户金额',
  `consumer_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退买家总金额',
  `shop_refund` decimal(16,2) DEFAULT '0.00' COMMENT '商家退款总金额',
  `fee_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退还佣金',
  `platform_discount_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退平台优惠',
  `shop_discount_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退还商家优惠',
  `platform_refund_delivery_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退平台配送费',
  `merchant_refund_post_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商家配送费',
  `platform_refund_pack_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退平台包装费',
  `merchant_refund_pack_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商家包装费',
  `detail_discount_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商品明细优惠',
  `checker_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核员id',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '退款原因',
  `desc` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `postage_amount` decimal(16,2) DEFAULT '0.00' COMMENT '邮费退款金额',
  `user_postage` decimal(16,0) DEFAULT '0' COMMENT '退用户配送费金额(单位元)',
  `platform_postage` decimal(16,2) DEFAULT '0.00' COMMENT '商家退还给平台补贴的金额(单位元)',
  `erp_state` tinyint DEFAULT '20' COMMENT '退款单erp状态，20-待下账，100-已下账，102-已取消',
  `erp_refund_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单零售流水',
  `bill_time` datetime DEFAULT NULL COMMENT '下账时间',
  `call_erp_flag` tinyint NOT NULL DEFAULT '1' COMMENT '是否调用ERP接口标识（0不调用 1调用）',
  `complete_time` datetime DEFAULT NULL COMMENT '退款完成时间',
  `refund_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退货类型  0、仅退款，1、退货退款',
  `fill_mt_refund` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否填写了美团退款信息',
  `refund_application_time` datetime DEFAULT NULL COMMENT '退款申请时间',
  `express_return` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退回快递公司',
  `express_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退回快递单号',
  `warehouse_return` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退回仓库',
  `remarks` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '系统备注',
  `platform_modify_time` datetime DEFAULT NULL COMMENT '平台修改时间',
  `oms_order_no` bigint NOT NULL DEFAULT '0',
  `bill_type` tinyint DEFAULT NULL COMMENT '下账类型(O2O)：1仅退款 2退货退款',
  `adjust_amount` decimal(16,2) DEFAULT '0.00' COMMENT '手工调整金额',
  `health_num` int DEFAULT '0' COMMENT '健康贝数量',
  `refund_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款版本号',
  `re_calculate_origin_order_flag` tinyint(1) DEFAULT '0' COMMENT '未下账的部分退款单，是否已重新计算原单财务数据等金额。0-未计算 1-已计算',
  `after_sale_type` tinyint DEFAULT NULL COMMENT '售后单类型 0:退款售后 1:退货售后',
  `extra_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '额外信息json',
  `after_sale_goods_time_start` datetime DEFAULT NULL COMMENT '退货售后单上门取件，预计送达时间开始',
  `after_sale_goods_time_end` datetime DEFAULT NULL COMMENT '退货售后单上门取件，预计送达时间结束',
  `health_value` decimal(16,2) DEFAULT NULL COMMENT '退健康贝换算金额',
  `last_apply_flag` tinyint(1) DEFAULT NULL COMMENT '京东到家是否整单退款 1-整单退款 ',
  `ebai_commission_flag` tinyint(1) DEFAULT NULL COMMENT '饿百获取佣金标识  1-已获取，0-未获取',
  `migration_refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台迁移退款单号-旧系统迁移数据的退款单号',
  `order_is_new` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否新订单: 1-新订单,2老订单',
  `data_version` bigint DEFAULT '0' COMMENT '数据版本,update订单信息默认+1',
  `extend_info` json DEFAULT NULL COMMENT '扩展信息',
  `organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线上门店编码',
  `service_mode` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务模式',
  `source_organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `refund_no` (`refund_no`) USING BTREE,
  UNIQUE KEY `migration_refund_no_index` (`migration_refund_no`),
  KEY `idx_order` (`order_no`),
  KEY `idx_oms_order` (`oms_order_no`),
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_third_order_no` (`third_order_no`,`third_platform_code`) USING BTREE,
  KEY `idx_refundid_plat` (`third_refund_no`,`third_platform_code`) USING BTREE,
  KEY `idx_mer_create_time` (`mer_code`,`create_time`),
  KEY `idx_mer_code_state` (`mer_code`,`state`,`erp_state`),
  KEY `idx_merCode_state_erpState` (`mer_code`,`organization_code`,`state`,`erp_state`) USING BTREE,
  KEY `idx_store` (`mer_code`,`online_store_code`),
  KEY `idx_merCode_source_state_erpState` (`mer_code`,`source_organization_code`,`state`,`erp_state`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1277579 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款单';

refund_detail

CREATE TABLE `refund_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` bigint NOT NULL COMMENT '退款单号',
  `erp_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
  `bar_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品条形编码',
  `third_sku_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单商品三方skuid',
  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
  `commodity_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品规格',
  `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',
  `refund_count` int NOT NULL COMMENT '退款数量',
  `actual_net_amount` decimal(16,4) NOT NULL COMMENT '下账金额',
  `bill_price` decimal(16,4) NOT NULL COMMENT '下账价格',
  `buyer_amount` decimal(16,2) DEFAULT '0.00' COMMENT '退款商品退用户金额',
  `merchant_amount` decimal(16,2) DEFAULT '0.00' COMMENT '商家退还给平台补贴的金额',
  `unit_refund_price` decimal(16,2) DEFAULT '0.00' COMMENT '退款单价',
  `origin_detail_price` decimal(16,4) DEFAULT NULL COMMENT '订单原价',
  `refund_discount_amount` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商品明细优惠',
  `status` tinyint DEFAULT '0' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `third_detail_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'null' COMMENT '第三方详情ID',
  `coupon_amount` decimal(16,2) DEFAULT '0.00',
  `activity_discount_amont` decimal(16,2) DEFAULT '0.00',
  `after_sale_no` bigint DEFAULT NULL COMMENT '售后单号',
  `third_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台订单号',
  `third_refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台退款ID',
  `share_amount` decimal(16,2) DEFAULT '0.00' COMMENT '分摊金额',
  `discount_amount` decimal(16,2) DEFAULT '0.00' COMMENT '折扣金额',
  `adjustment_amount` decimal(16,2) DEFAULT '0.00' COMMENT '调整金额',
  `order_detail_id` bigint DEFAULT NULL COMMENT '订单明细ID',
  `fee_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退还佣金',
  `platform_discount_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退平台优惠',
  `shop_discount_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退还商家优惠',
  `vip_different_amt` decimal(16,2) DEFAULT '0.00' COMMENT '会员优惠金额',
  `accept_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退货入库说明',
  `health_value` decimal(16,2) DEFAULT NULL COMMENT '退健康贝换算金额',
  `detail_discount` json DEFAULT NULL COMMENT '退平台分摊优惠明细',
  `platform_refund_count` int DEFAULT NULL COMMENT '平台退款数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_no` (`refund_no`) USING BTREE COMMENT '退款款单索引',
  KEY `third_no` (`third_order_no`,`third_refund_no`) USING BTREE COMMENT '平台单索引',
  KEY `INDEX_AFTER_SALE_NO` (`after_sale_no`) USING BTREE COMMENT '售后单号'
) ENGINE=InnoDB AUTO_INCREMENT=336074289 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款明细表';