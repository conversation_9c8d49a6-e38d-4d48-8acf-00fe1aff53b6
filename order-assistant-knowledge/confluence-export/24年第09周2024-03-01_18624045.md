# 24年第09周2024-03-01

- [一、上周TODO回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%80%E3%80%81%E4%B8%8A%E5%91%A8TODO%E5%9B%9E%E9%A1%BE)
- [二、本周工作情况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%BA%8C%E3%80%81%E6%9C%AC%E5%91%A8%E5%B7%A5%E4%BD%9C%E6%83%85%E5%86%B5)
- [2、重点项目周进展与风险概况](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-2%E3%80%81%E9%87%8D%E7%82%B9%E9%A1%B9%E7%9B%AE%E5%91%A8%E8%BF%9B%E5%B1%95%E4%B8%8E%E9%A3%8E%E9%99%A9%E6%A6%82%E5%86%B5)
- [三、本周成长回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%89%E3%80%81%E6%9C%AC%E5%91%A8%E6%88%90%E9%95%BF%E5%9B%9E%E9%A1%BE)
- [四、本周CR回顾](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%9B%9B%E3%80%81%E6%9C%AC%E5%91%A8CR%E5%9B%9E%E9%A1%BE)
- [五、本周异常告警&线上问题](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%BA%94%E3%80%81%E6%9C%AC%E5%91%A8%E5%BC%82%E5%B8%B8%E5%91%8A%E8%AD%A6&%E7%BA%BF%E4%B8%8A%E9%97%AE%E9%A2%98)
- [六、本周缺陷](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%85%AD%E3%80%81%E6%9C%AC%E5%91%A8%E7%BC%BA%E9%99%B7)
- [七、团队建设](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E4%B8%83%E3%80%81%E5%9B%A2%E9%98%9F%E5%BB%BA%E8%AE%BE)
- [八、本周TODO](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6378673#id-%E7%AC%AC53%E5%91%A820231228-%E5%85%AB%E3%80%81%E6%9C%AC%E5%91%A8TODO)


### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5**1. **微商城分销订单测试流程支持** 2. **组织机构配置POS类型** 3. **门店同步优化上线以及线上bug修复** | **㊀计划工作****㊁实际完成****㊂遗留问题**1.店铺在单个平台下对应多个网店存在问题2.拼多多配合上线3.**组织机构配置POS类型 测试上线****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. **组织机构配置POS类型 测试上线** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 | 王世达 | **本周总工时：5****1. 海典H2测试联调****2. 科传下账增加医保信息****3. 医保支付增加省/市医保标识****4. 线上bug修复及test环境问题处理** | **㊀计划工作****1. 海典H2测试联调****2. 科传下账增加医保信息****3. 医保支付增加省/市医保标识****㊁实际完成****1. 海典H2测试联调 等待上线****2. 科传下账增加医保信息 已上线****3. 医保支付增加省/市医保标识 开发完成，测试中****㊂遗留问题****1. 海典切换门店配置sql 待准备 (等待运营确认)****2.医保支付增加省/市医保标识****3.周一codereview****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 3 | 杨国枫 | **本周总工时：5d****1.库存优化提测（70）****2.内购商城BUG定位****3.抖音复测****4.线上问题处理** | **㊀计划工作****1.库存优化提测****2.内购商城BUG定位****3.抖音复测（10%）****4，线上问题处理****㊁实际完成****1.库存优化提测****2.内购商城BUG定位****4.线上问题处理****㊂遗留问题****1.库存优化（测试中）****2.抖音复测****3.下账时机修改及生成运费单****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** |  |  |
| 4 |  | **本周总工时：5 day**1. 内购商城联调 2. 线上问题支持 3. 下账时机修改技术方案编写 | **㊀计划工作**1. 内购商城联调 2. 线上问题支持 3. 下账时机修改技术方案编写 **㊁实际完成****㊂遗留问题**1. 内购商城订单流程下发 2. 下账时机修改及生成运费单 3. 海南运营问题 4. 云仓商品迁移订单刷数据 5. 多个处方单图片只存了一个 6. 门店没有修改批号权限 **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 内购商城配合测试 2. 下账时机修改及运费单的生成 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 5 | 杨俊峰 | **本周总工时：5 day****1.**京东到家退款单问题排查 1day**2.**.NET 项目容器化，目前卡在公司加密软件，被公司加密软件处理后就无法使用脚本打包了。目前集团安全团队在处理。 1day**3.线上**问题支持**。**3day | **㊀计划工作****㊁实际完成****㊂遗留问题**1. **.**NET 容器化目前问题抛给集团安全团队了 2. 确认京东到家退款单问题是否上线 **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 | 李洋 | **本周总工时：5day**1. 订单拣货批次是否存在校验需求 2. 海典H2联调测试 | **㊀计划工作**1. 订单拣货批次是否存在校验需求 2. 海典H2联调测试 **㊁实际完成**1. 订单拣货批次是否存在校验需求 2. 海典H2联调测试 **㊂遗留问题**1. B2C退款单创建定时任务时机需要调整 | **** |  |
| 7 | 杨润康 | **本周总工时：5day**1. 优雅发布、催单接口优化、下单提示上线 2. ShardingJDBC接入 3. DDD项目搭建 4. 线下单结构确认 5. 其他: 网关token处理 | **㊀计划工作****㊁实际完成****㊂遗留问题**1.线上下单提示功能验证2.DDD项目V3沟通会3.线下单 科传 H2 4.配置网关token落文档()**㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[③后端研发部-线上值班模板](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6371159)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |