# 2024-01-04 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  | hydee-business-order | 1 | ``` dev ``` |  |  |  |  |  |  |  |
|  | ``` ds-service-jddj ``` | 2 | dev |  |  |  |  |  |  |  |
|  | ``` hydee-xxl-job ``` | 3 | dev |  |  |  |  |  |  |  |
|  | ``` middle-id ``` | 4 | dev |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

新增字段以及初始值设置详细见附件

250

#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| middle-id | application.yml | ``` spring ``` | ``` redisson:   config: |     clusterServersConfig:       idleConnectionTimeout: 10000       connectTimeout: 10000       timeout: 3000       retryAttempts: 3       retryInterval: 1500       failedSlaveReconnectionInterval: 3000       failedSlaveCheckInterval: 60000       password: yxt_redis123       subscriptionsPerConnection: 5       clientName: null       loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}       subscriptionConnectionMinimumIdleSize: 1       subscriptionConnectionPoolSize: 50       slaveConnectionMinimumIdleSize: 24       slaveConnectionPoolSize: 64       masterConnectionMinimumIdleSize: 24       masterConnectionPoolSize: 64       readMode: "SLAVE"       subscriptionMode: "SLAVE"       nodeAddresses:         - "redis://redis-b7ab4589-05e7-4137-a19c-0441965e5d10.cn-southwest-2.dcs.myhuaweicloud.com:6379"       scanInterval: 1000       pingConnectionInterval: 30000       keepAlive: false       tcpNoDelay: true     threads: 16     nettyThreads: 32     codec: !<org.redisson.codec.Kryo5Codec> {}     transportMode: "NIO" ``` |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  | Basic | Aurora_SysConfig | "BusinessOrderUrl": "[http://hydee-business-order.svc.k8s.pro.hxyxt.com/](http://hydee-business-order.svc.k8s.pro.hxyxt.com/)" |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |