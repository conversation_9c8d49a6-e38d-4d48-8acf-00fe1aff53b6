# 网关token生成

#### 背景

外部系统需要通过网关调用接口。网关有鉴权逻辑，需要有正确的token才能请求

#### 生成步骤

1. 在本地新建一个文件夹(推荐),例如 generate_token文件夹

2. 进入generate_token文件夹上右击,选择**Git Bash Here** (windows需安装git)

3. 执行以下命令:

openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:2048

openssl pkcs8 -topk8 -inform PEM -outform PEM -in private_key.pem -out private_key_pkcs8.pem -nocrypt

ssh-keygen -e -m PKCS8 -f private_key.pem > public_key.pkcs8

执行后会生成一下三个文件:

将private_key.pem、public_key.pkcs8 这2个文件发送给会员,让他们在数据库配置。appKey的具体值需要和会员沟通,达成一致就好

4. 将private_key_pkcs8.pem文件中**-----BEGIN PRIVATE KEY-----**和**-----END PRIVATE KEY-----**之间的内容提供给外部系统即可

### 配置步骤

1. 将private_key_pkcs8.pem文件的内容提供给对方
2. 确认秘钥对应的appkey,自定义,双发达成一致即可
3. 执行SQL语句sqlINSERT INTO `base_info`.`sys_merchant_config` (
	`id`,
	`company_id`,
	`mer_code`,
	`type`,
	`conf_key`,
	`user_name`,
	`conf_pwd`,
	`url`,
	`create_time`,
	`modify_time`,
	`private_key`,//对应 private_key.pem
  `public_key`,// 对应public_key.pkcs8
   `admin_url`,
	`app_key` 
)
VALUES
	(
		'75f6d0f5ce4542c88g44bb88a74d41eg',
		'500001',
		NULL,
		'2',
		NULL,
		NULL,
		NULL,
		'https://merchants.hxyxt.com/',
		'2024-05-16 10:58:57',
		'2024-05-16 10:58:57',
		'',
		'',
		'https://merchants.hxyxt.com/',
	'' 
	);


sql# 参考
CREATE TABLE `sys_merchant_config` (
  `id` varchar(50) NOT NULL COMMENT 'ID',
  `company_id` varchar(100) NOT NULL COMMENT '公司ID',
  `mer_code` varchar(10) DEFAULT NULL COMMENT '商家编码',
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类型1 ERP 类型2 电商云',
  `conf_key` varchar(4000) DEFAULT NULL COMMENT '密钥',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `conf_pwd` varchar(512) DEFAULT NULL COMMENT '密码',
  `url` varchar(256) DEFAULT NULL COMMENT '访问路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '末次修改时间',
  `private_key` varchar(4000) DEFAULT NULL COMMENT '统一接口平台私钥',
  `public_key` varchar(4000) DEFAULT NULL COMMENT '统一接口平台公钥',
  `admin_url` varchar(256) DEFAULT NULL COMMENT '统一接口管理后台',
  `app_key` varchar(100) DEFAULT NULL COMMENT '合作方ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_mer_type` (`mer_code`,`type`,`app_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='商户中台外部系统交互配置表';

GET案例

truepackage com.hydee.gateway.feign;

import com.yxt.common.sign.YxtApi;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年05月28日 10:01
 * @email: <EMAIL>
 */
public class TestGet {


  public static void main(String[] args) {
    testApiGateway();
  }

  public static void testApiGateway() {

    String coldKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDsOy2jeTTKFkj+\n"
        + "edJguWhcFXwkUSoDQ17FBFiTqbuOcSa8xt8kHMtfUzv0h++zodOSTLCVY4r8HqB7\n"
        + "8PF3lzgQGlCtXvANVDuYlkyH5hkk1md18ctLzAvMOjzcWpI2Iq64FOr7bZlOkTRz\n"
        + "GoQQXoeTq0jsIJWHnLA31NMg8iaA5RfFbP50v2AKyIzwKaqsu/6nyvpYINbSpKjK\n"
        + "qQV1LtguXOcxQzVnh7GOqUurYwJ3/vbhf5TGSVaqMUcuq7Z0tpIiJ1e2st5lIZfT\n"
        + "pD6K7aYX6LQeqe8uh9woEwLUbinWSqc7//pgdKykZPca5ffDqq6fkhrYvF0vTPvh\n"
        + "cj+2KaTJAgMBAAECggEBAIP4O3LLz5XmBTlEzSaD7a0jw4p2XUYdaOAfoWw8si7N\n"
        + "P5rObvI5s9O5KBFfr6Vmjk940F1kksxuP7F91Se+Lu+wA8oW83w+xlxj+rUg8oMF\n"
        + "1B6ryaq6fwzErCNd5bigf4MPDIcRn0epDugOtCCA7rSsR7zCJ0bvXfKSzq/lF2WB\n"
        + "g5UzpLhRSSQIqnH+zeXDRAfiPz3hvGmPmv3JBke4f4tripf0205rRulW2s6u2dqk\n"
        + "A8Mv411uSA1czBwBThNsoS1x4WdcaqpuqtBuYU3smta9LGIp7EZgTEhEB/drsloy\n"
        + "96HCZdZPBu7SKsYgh+Sf5pVzF+ilqsXBzS2mv0StCAECgYEA9zjakmJXeUtkP11v\n"
        + "DGUyWSlwAHJRysZaQOpYTq2zOfAvs/a1eLjLX/FL7f7Nw4/Ojs4VETUtTpIftl8m\n"
        + "8pLYJxR+E9BBcFn8me4Y63rz618WET25u1Mry48BWny/Li64t/mKlpU1k3SeNR5b\n"
        + "PFkpXYXDPj2hat4lDe7olqt+DEECgYEA9J5r7GTQ/6F69/EIF9QhK3iMxF06vVH9\n"
        + "PEp1GeVj59RuqVE2XfwUaWl4TIvgK7zhSyIvpj04Evn30ux05Ge1m3QBmWusrpN0\n"
        + "uPzHMaMpsNp/UqPr/XusfCmjbiSWKYKrdR0wj53KdYb52I4uZHPR/B8jbi+qWPeY\n"
        + "ZnEKuw7HlokCgYB371sKPS9Wpt6hZwCisM12OAYgEVXg282ZRJdGxDn4168szXlI\n"
        + "cc4oY3WEpmLIhxGcchoCWRv7zCQaY5sPf3Hhs61ei1/T07QaMUphoyqGe4DHKHzc\n"
        + "hgLvJBdyAKb3Dq+KgUNcvlJZkO/XsTxt0Iui1Wvc3CVdJRww4EByKAxkgQKBgCft\n"
        + "R75RAlAHQnP/0otZI4Td61QNx42pOUVC/fQZHgJ0CQ/34oDaqVY/UdRkrFVXQgqq\n"
        + "bM6chW+Lf2jdEYNKbjkOaUhqa8ge51leYQD4GdD1CP0rJlS8f/3QKg3LSPtDicrW\n"
        + "1dtLLQWb32H6axYYVXjqeDT+8LYOy8BEyTSZB/jJAoGBALY8zA4pmcxIIZv0ha0h\n"
        + "mogvJRGpHpedKIUsxUuNORlnaC0raVDWYXsiH7rmkrjxcwKnXpPysVHC/97SSaP8\n"
        + "B9sWiX+oaDp3rZ2JA4DDAuj2d6ESDH6S1kRdXiRfd0Yr9iFOAjyxMh6i1xBhYxBd\n"
        + "T+2rIu1eMJeU23/QGs5ZNVVZ";
    Map<String, Object> params = new HashMap<>();
    String urldev = "https://dev-api.hxyxt.com/zeus/member/api/member/info/search?searchKey=13678206578";
    YxtApi.YxtApiResponse yxtApiResponse = YxtApi.url(urldev)
        .params(params)
        .method("GET")
        .appKey("DEV_DEMO")
        .privateKey(coldKey)
        .signType("RSA2")
        .connectTimeout(1000000)
        .readTimeout(1000000)
        .post();
    System.out.println(yxtApiResponse);
  }
}



POST案例

javatruepackage com.hydee.gateway.feign;

import com.yxt.common.sign.YxtApi;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年05月28日 10:01
 * @email: <EMAIL>
 */
public class TestPost {


  public static void main(String[] args) {
    testApiGateway();
  }

  public static void testApiGateway() {

    String coldKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDsOy2jeTTKFkj+\n"
        + "edJguWhcFXwkUSoDQ17FBFiTqbuOcSa8xt8kHMtfUzv0h++zodOSTLCVY4r8HqB7\n"
        + "8PF3lzgQGlCtXvANVDuYlkyH5hkk1md18ctLzAvMOjzcWpI2Iq64FOr7bZlOkTRz\n"
        + "GoQQXoeTq0jsIJWHnLA31NMg8iaA5RfFbP50v2AKyIzwKaqsu/6nyvpYINbSpKjK\n"
        + "qQV1LtguXOcxQzVnh7GOqUurYwJ3/vbhf5TGSVaqMUcuq7Z0tpIiJ1e2st5lIZfT\n"
        + "pD6K7aYX6LQeqe8uh9woEwLUbinWSqc7//pgdKykZPca5ffDqq6fkhrYvF0vTPvh\n"
        + "cj+2KaTJAgMBAAECggEBAIP4O3LLz5XmBTlEzSaD7a0jw4p2XUYdaOAfoWw8si7N\n"
        + "P5rObvI5s9O5KBFfr6Vmjk940F1kksxuP7F91Se+Lu+wA8oW83w+xlxj+rUg8oMF\n"
        + "1B6ryaq6fwzErCNd5bigf4MPDIcRn0epDugOtCCA7rSsR7zCJ0bvXfKSzq/lF2WB\n"
        + "g5UzpLhRSSQIqnH+zeXDRAfiPz3hvGmPmv3JBke4f4tripf0205rRulW2s6u2dqk\n"
        + "A8Mv411uSA1czBwBThNsoS1x4WdcaqpuqtBuYU3smta9LGIp7EZgTEhEB/drsloy\n"
        + "96HCZdZPBu7SKsYgh+Sf5pVzF+ilqsXBzS2mv0StCAECgYEA9zjakmJXeUtkP11v\n"
        + "DGUyWSlwAHJRysZaQOpYTq2zOfAvs/a1eLjLX/FL7f7Nw4/Ojs4VETUtTpIftl8m\n"
        + "8pLYJxR+E9BBcFn8me4Y63rz618WET25u1Mry48BWny/Li64t/mKlpU1k3SeNR5b\n"
        + "PFkpXYXDPj2hat4lDe7olqt+DEECgYEA9J5r7GTQ/6F69/EIF9QhK3iMxF06vVH9\n"
        + "PEp1GeVj59RuqVE2XfwUaWl4TIvgK7zhSyIvpj04Evn30ux05Ge1m3QBmWusrpN0\n"
        + "uPzHMaMpsNp/UqPr/XusfCmjbiSWKYKrdR0wj53KdYb52I4uZHPR/B8jbi+qWPeY\n"
        + "ZnEKuw7HlokCgYB371sKPS9Wpt6hZwCisM12OAYgEVXg282ZRJdGxDn4168szXlI\n"
        + "cc4oY3WEpmLIhxGcchoCWRv7zCQaY5sPf3Hhs61ei1/T07QaMUphoyqGe4DHKHzc\n"
        + "hgLvJBdyAKb3Dq+KgUNcvlJZkO/XsTxt0Iui1Wvc3CVdJRww4EByKAxkgQKBgCft\n"
        + "R75RAlAHQnP/0otZI4Td61QNx42pOUVC/fQZHgJ0CQ/34oDaqVY/UdRkrFVXQgqq\n"
        + "bM6chW+Lf2jdEYNKbjkOaUhqa8ge51leYQD4GdD1CP0rJlS8f/3QKg3LSPtDicrW\n"
        + "1dtLLQWb32H6axYYVXjqeDT+8LYOy8BEyTSZB/jJAoGBALY8zA4pmcxIIZv0ha0h\n"
        + "mogvJRGpHpedKIUsxUuNORlnaC0raVDWYXsiH7rmkrjxcwKnXpPysVHC/97SSaP8\n"
        + "B9sWiX+oaDp3rZ2JA4DDAuj2d6ESDH6S1kRdXiRfd0Yr9iFOAjyxMh6i1xBhYxBd\n"
        + "T+2rIu1eMJeU23/QGs5ZNVVZ";

    Map<String, Object> params = new HashMap<>();
    params.put("orderNo", "ON1794587915293150216");
    params.put("orderStatus", "REFUSE");
    params.put("orderType", "CUSTOMER_PURCHASE");
    params.put("sourceSystem", "SRM");
    params.put("desc", "确认");

    String urldev = "https://dev-api.hxyxt.com/zeus/assist-synthesis/sign/open-sdk/storeOrder/w/1.0/updateOrderStatus";

    YxtApi.YxtApiResponse yxtApiResponse = YxtApi.url(urldev)
        .params(params)
        .method("POST")
        .appKey("DEV_DEMO")
        .privateKey(coldKey)
        .signType("RSA2")
        .connectTimeout(1000000)
        .readTimeout(1000000)
        .post();
    System.out.println(yxtApiResponse);
  }
}


配置注意事项:

1. filters配置,配置appkey则走AppKey验权
2. 如果配置appkey，需要另外配置一个route，不能在之前的route上修改，否则会有影响


---