# 【20241202】接口超时治理

### 会议目标

- businesses-gateway网关超时时间是否可以调整为10s。
- 确认现有超时接口是否可以处理,以及处理完成时间
- 确认短期内的接口超时治理方式(代码优化，限流等)
- 之前的超时治理经历分享(实践)


### 背景

2024年12月1日,下游服务有大量的接口超时，导致网关所在的服务器CPU使用率飙升，使网关无法及时处理后续请求。在k8s监测几次后认为网关服务不可用，触发pod重启

truebusinesses-gateway超时falseautotoptrue5024

等待过程需要占用CPU、内存等资源。大量的超时会导致服务器资源被占用，无法及时处理后续请求。

### 超时接口统计(20s,近一个月的统计数据)

[https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIEdeH1bQjXS80lQuCEJR?scode=AOsAFQcYAAci9rpx3wAZ0AgQYfAIE&tab=000001](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIEdeH1bQjXS80lQuCEJR?scode=AOsAFQcYAAci9rpx3wAZ0AgQYfAIE&tab=000001)

| 服务 | 超时接口 | 超时次数 | 是否需要优化 | 负责人 |  |
| --- | --- | --- | --- | --- | --- |
| assist-synthesis | /assist-synthesis/c/cornerMark/r/3.0/getToolbarNum | 2771 |  |  | 整体优化方案    **暂定方案时间:** 2025年1月31前**目前措施:**已添加缓存 |
| /assist-synthesis/c/commodityQuery/r/1.0/getLabelCommodityDistribute | 2046 |  |  |
| /assist-synthesis/c/appVersion/r/1.0/getUpdateResource | 1574 |  |  |
| /assist-synthesis/c/achievement/r/1.0/getCurrentMonthData | 1409 |  |  |
| /assist-synthesis/c/achievement/r/1.0/getTodayData | 707 |  |  |
| /assist-synthesis/c/common/disposition/r/1.0/listPolling | 694 |  |  |
| /assist-synthesis/c/advertisement/r/1.0/ListQueryAdvertisement | 644 |  |  |
| /assist-synthesis/c/achievement/r/1.0/listSaleDataTrendCategoryAndDateType | 227 |  |  |
| /assist-synthesis/c/achievement/r/1.0/querySaleDataTrend | 105 |  |  |
| /assist-synthesis/c/commodityQuery/r/2.0/pageQueryCommodity | 79 |  |  |
| /assist-synthesis/c/commonTool/r/1.0/listCommonToolConfig | 51 |  |  |
| /assist-synthesis/c/workflow/r/1.0/getWorkflowCount | 43 |  |  |
| /assist-synthesis/c/training/r/1.0/listQueryHotCourse | 24 |  |  |
| /assist-synthesis/b/appVersion/r/1.0/getLatestInstallPackage | 7 |  |  |
| /assist-synthesis/c/expiryInspect/r/2.0/listDistribution | 5 |  |  |
| /assist-synthesis/c/expiryInspect/r/1.0/getInspectDistribute | 6 |  |  |
| assist-hcm | /assist-hcm/c/empAttend/w/1.0/innerAttend | 23 |  |  |  |
| assist-home | /assist-home/c/resource/r/1.0/listAccreditResource | 8 |  |  |
| /assist-home/c/app/r/1.0/checkAppAndOrg | 5 |  |  |
| assist-growth | /assist-growth/c/decoration/r/1.0/getMyInfo | 6 |  |  |
| /assist-growth/c/popup/r/1.0/listPopup | 5 |  |  |
| assist-middle-porta | /assist-middle-portal/c/userdevice/w/1.0/bindingUserDevice | 7 |  |  |
| member | /member/api/member/info/getDocNoConsumeRecords | 29 |  |  | 后续消费记录上线可解决  todo：超时配置可以更细粒度 |
| # 会员中台  - id: hydee-middle-member  uri: [lb://hydee-middle-member](lb://hydee-middle-member)  predicates:  - Path=/member/**  filters:  - StripPrefix=1  metadata:  connect-timeout: 5000  response-timeout: 30000 |  |  |  |
| dscloud | /dscloud/1.0/ds/order/page/exception | 8 |  |  |  |


### 优化措施

  12 incomplete 接口超时优化   13 incomplete 调整网关全局超时时间为10s，减少资源在异常场景下长时间被占用。结论: 先治理,再调整超时 todo: 1.建表，选择 2. 统计超时时间10s   14 complete 调整健康检查起始时间，减少服务恢复时间。已上线   17 incomplete busineses-gateway网关拆分,C端（小程序，可拆）,B端（一心助手，待讨论）      20 incomplete 前端 traceId    

### 短期治理方案

1. 每周导出超时接口
2. 新建jira优化任务，跟踪解决。
  1. 一个jira卡片，企业文档维护接口
3. 一个jira卡片，企业文档维护接口


  18 incomplete 自动化  

---

#### 超时响应(短期)

true接口超时快速响应falseautotoptrue4761

#### 系统性治理

true接口超时治理falseautotoptrue13971

### 1. 问题诊断专题（深度分析）

**服务维度超时分析：** 通过对504超时日志的细致梳理，我们需要构建一个多维度的服务超时画像。首先，按服务划分超时频次，识别出高风险服务。例如，可以建立一个热力图，纵轴为服务名称，横轴为时间段，用颜色深浅表示超时严重程度。这种可视化方法能直观地展现服务的性能波动特征。

**进一步，我们需要对每个服务的超时特征进行详细剖析：**

- 超时发生的时间分布
- 超时的请求类型与接口特征
- 超时前后的系统负载情况
- 资源消耗（CPU、内存、网络）的关联性


通过这种深度分析，我们可以精准定位导致服务超时的潜在因素，为后续的优化提供数据支撑。

### 2. 技术攻坚（根因诊断）

**链路追踪与性能分析：** 我们将采用全链路性能追踪技术，构建端到端的请求链路视图。这不仅仅是简单地记录日志，而是要建立一个能够还原请求完整生命周期的追踪系统。

**具体实施路径：**

- 引入分布式追踪组件（如Skywalking、Zipkin）
- 为每个请求生成唯一标识，实现请求全链路追踪
- 记录每个服务节点的处理时间、资源消耗
- 构建请求处理的关键路径分析模型


**通过这种细粒度的链路分析，我们可以：**

- 精确定位性能瓶颈
- 识别服务间依赖关系
- 量化每个服务节点的性能开销
- 发现潜在的性能优化点


### 3. 治理路径规划（系统性方案）

**短期应急机制：** 超时快速处理不仅仅是技术问题，更是业务连续性的保障。我们需要设计一个多层次的应急响应体系：

**告警机制：**

- 实时监控超时阈值
- 建立多级告警通道（企微、短信、邮件、电话）
- 根据超时严重程度触发不同级别的告警


**快速恢复策略：**

- 自动降级：当某服务持续超时，自动切换到降级服务
- 流量削峰：通过网关实现精准的流量控制
- 故障隔离：快速隔离故障节点，preventing雪崩效应


### 4. 性能监控体系

**SLA指标设计：** 制定一个多维度的服务等级协议(SLA)评估体系：

**核心指标：**

- 响应时间P99（99%请求的响应时间）
- 可用性：服务可用率>99.9%
- 错误率：低于0.1%
- 吞吐量：峰值流量下的系统处理能力


**评估机制：**

- 每日/周/月性能报告
- 季度性能评估
- 建立性能改进奖励机制


### 5. 长期架构演进

**微服务架构优化：**

- 服务拆分：基于业务domain进行更细粒度拆分
- 组件解耦：减少服务间强依赖
- 引入服务治理平台
- 构建可观测的系统架构