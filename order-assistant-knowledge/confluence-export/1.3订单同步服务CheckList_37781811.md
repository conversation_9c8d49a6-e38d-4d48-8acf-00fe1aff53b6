# 1.3订单同步服务CheckList

### 订单重构

- 消息字段按照Java规范命名,金额字段类型调整(String→BigDecimal)及初始化
- 按照场景,划分为三个聚合根,新增、更新、京东到家调整单; 针对每个聚合根来具体抽象处理逻辑
- 原子服务新增(saveOptional)、更新(updateOptional)、删除逻辑抽象,FullOrderDtoResDto详情(单号和三方详情)、简单订单对象
- 事件处理抽象(BaseOrderEventConsumerAbstract)、事件消息重试MqMessageReHandleAbstract(顺序消费)


### 开发分支

| 仓库 | 开发分支 | 增加部署节点 |
| --- | --- | --- |
| hydee-business-order | order-refactor-syncOrder |  |
| yxtthirdplatform | order-refactor-syncOrder服务:third-platform-order-elm third-platform-order-jddj third-platform-order-mt third-platform-order-other |  |
| order-sync | order-refactor-syncOrder |  |
| order-framework | order-refactor-syncOrder |  |
| order-atom-service | order-refactor-syncOrder | 6 |


### 代码todo:

  2 complete 删除  

# hydee-business-order
todo 合并master前删除

### SDK版本

|  |  |  |
| --- | --- | --- |
| ### order-atom-sdk | 1.1.0-SNAPSHOT |  |
| ### order-common | 1.1.0-SNAPSHOT |  |
| ### order-types | 1.1.0-SNAPSHOT |  |
| ### order-domain-sdk | 1.1.0-SNAPSHOT |  |


### RocketMQ队列

  18 complete 预发配置   19 complete 生产配置  

| 队列 | 说明 | tag | 生产者 | 消费者 |
| --- | --- | --- | --- | --- |
| TP_ORDER_ONLINE_ORDER_SYNC | 订单同步 | TAG_ORDER | hydee-business-order | order-sync-service |
| TP_ORDER_ORDER-SYNC-CREATE-EVENT | 订单创建事件 | TAG_CREATE | order-sync-service | order-sync-service |
| TP_ORDER_ORDER-SYNC-UPDATE-EVENT | 订单更新事件 | TAG_UPDATE | order-sync-service | order-sync-service |
| TP_ORDER_ORDER-SYNC-CREATE-JDDJ-EVENT | 京东到家调整单时间 | TAG_CREATE_JDDJ | order-sync-service | order-sync-service |


### Apollo配置

hydee-business-order配置

  20 complete 预发配置   21 complete 生产配置  

# 转发配置
forward-message-config:
  config-list:
    - platform-code: "platform1"
      all: true
      store-set:
        - "store1"
        - "store2"
    - platform-code: "platform2"
      all: false
      store-set:
        - "store3"
        - "store4"
        - "store5"

#### order-sync配置

  7 complete 预发配置   8 complete 生产配置  

# 再ddd中这个配置无用
compensator:
  enabled: true

# dev
threadpool:
  logCoreSize: 1
  logMaxSize: 3
  logKeepAliveTime: 1000
  logCapacity: 3000


spring:
  rabbitmq:
    host: *************
    port: 5672
    username: user_rw
    password: ${myEncrypt.des(483359ad69775e5044dd55095b2ecaf1470415618132cd3bd809e27dbd7f883e)}
    virtual-host: /business-order
    dead-consumer-time:
      unlock-inventory:
        ready-one: 10000
        ready-two: 20000
        ready-three: 30000
        ready-four: 40000
      refund-front:
        ready-one: 15000
        ready-two: 25000
        ready-three: 35000
        ready-four: 45000
      # 生产配置了下面部分
      lock-stock:
        ready-one: 600000
    batch-size: 50
    topic:
      order: OmsOrder


zero-limit:
  platform-codes: 27,1001

message-notify:
  data-topic: TOPIC_BUSINESS_CLOUD_DATA_DEV
  order-update-topic: GROUP_ORDER_UPDATE_MESSAGE_DEV
  b2c-order-topic: TOPIC_BUSINESS_CLOUD_B2C_ORDER_MESSAGE_DEV
  srm-order-topic: TOPIC_BUSINESS_CLOUD_O2O_SRM_ORDER_DEV
  commodity-create-callback-topic: TOPIC_BUSINESS_CLOUD_COMMODITY_CREATE_CALLBACK_DEV
  commodity-exception-analyse-topic: TOPIC_COMMODITY-EXCEPTION-ANALYSE_DEV
  prescription-approve-topic: TOPIC_OMS_PRESCRIPTION_NOTICE

prescriptionPlatList: 1001,24

hydee-platform:
  url:
    net: https://dev-mall.hxyxt.com/ds-gateway/api/cloud #http://*************:5050/api/cloud
    unified: https://dev-mall.hxyxt.com/ds-gateway/api/cloud
  b2c-url:
    net: http://dscloudapi.hydee.cn/gateway/api/cloud   #测试
    serverurl: http://**************:30002/EcDb/EcDbService.svc #线上


aliyun:
  oss:
    endpoint: oss-cn-chengdu.aliyuncs.com
    #多个bucket用逗号隔开，有多少个它就是注册多少ossFileService到spring 容器中去，
    #bean名字是bucket 名字的驼峰方式+"OssFileService"，例如oms-2018-dev=oms2018DevOssFileService"。
    bucket-name: sk-dev-centermerchant
    grant-expire-time: 1800
    #限制上传文件大小，单位为M
    grant-max-content-length: 100
    access-key-id: LTAI5t8T3JQBCtiJUUVRi9hn
    access-key-secret: ******************************
    #client有默认配置.
    client:
      max-connections: 1024
      socket-timeout: 50000
      connection-timeout: 50000
      connection-request-timeout: 3000
      idle-connection-time: 600000
      max-error-retry: 3
      support-cname: true
      sld-enabled: false
      protocol: https
      user-agent: aliyun-sdk-java

mq:
  topic:
    # 消费消息
    consumer:
      # 线上订单处理
      onlineOrder: TP_ORDER_ONLINE_ORDER_SYNC
      # 订单订单同步重构事件生产 String.format("TP_%s_%s_%s", "领域名",""系统名","业务名")
      # 不按照场景再分topic,防止事件执行乱序,导致不稳定的异常
      orderSyncCreateEvent: TP_ORDER_ORDER-SYNC-CREATE-EVENT
      orderSyncUpdateEvent: TP_ORDER_ORDER-SYNC-UPDATE-EVENT
      orderSyncCreateJddJEvent: TP_ORDER_ORDER-SYNC-CREATE-JDDJ-EVENT     # 生产消息
    producer:
      # 订单订单同步重构事件生产 String.format("TP_%s_%s_%s", "领域名",""系统名","业务名")
      # 不按照场景再分topic,防止事件执行乱序,导致不稳定的异常
      orderSyncCreateEvent: TP_ORDER_ORDER-SYNC-CREATE-EVENT
      orderSyncUpdateEvent: TP_ORDER_ORDER-SYNC-UPDATE-EVENT
      orderSyncCreateJddJEvent: TP_ORDER_ORDER-SYNC-CREATE-JDDJ-EVENT





weixin:
  robot-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb # 企业微信机器人地址


discount:
  apportion:
    open: true
    merCodes: 999999
  # 商品金额分摊优化门店渠道白名单
  white:
    storeList: 1
    platformList: 24,27  

#### order-atom-service配置

  22 complete 预发配置   23 complete 生产配置  

# 后续还要维护
sensitive:
  scope:
    addUpdates[0]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.updateRecord
    addUpdates[1]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.insert
    addUpdates[2]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.insertBatch
    addUpdates[3]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.updateByOrderNo
    addUpdates[4]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.updateByBatch
 	addUpdates[5]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.update
    selects[0]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectByOrderNo
    selects[1]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectList
    selects[2]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectListByOrderNo
    selects[3]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.queryNeedDealPrescriptionList
    selects[4]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectList
    selects[5]: com.yxt.order.atom.order.mapper.OrderInfoExportMapper.listOverall
    selects[6]: com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper.selectByOrderNoList
    selects[7]: com.yxt.order.atom.order.mapper.OrderPrescriptionMapper.selectListByOrderNoList
    selects[8]: com.yxt.order.atom.order.mapper.OrderInfoExportMapper.listOverallByOrderNos






### MySQL

  4 complete 运维处理中  

# 为处理commodity_stock表中的order_detail_id服务
ALTER TABLE `commodity_stock` ADD COLUMN `third_detail_id` varchar(100) NULL COMMENT '第三方详情ID';

### XXL-JOB配置

  24 complete 无预发环境   28 complete 生产配置  

消息重试"CREATE_JDDJ_ORDER_DOMAIN_EVENT_MESSAGE","CREATE_ORDER_DOMAIN_EVENT_MESSAGE","UPDATE_ORDER_DOMAIN_EVENT_MESSAGE","ONLINE_ORDER_MESSAGE"

### third-platform-order-elm、third-platform-order-mt、third-platform-order-jddj、third-platform-order-other都需要配置

  25 complete 预发配置   26 complete 生产配置  

message:
  notify-message:
    order-message-to-order-sync-topic: TP_ORDER_ONLINE_ORDER_SYNC

# 转发配置
forward-message-config:
  config-list:
    - platform-code: "platform1"
      all: false
      store-set:
        - "store1"
        - "store2"
    - platform-code: "platform2"
      all: false
      store-set:
        - "store3"
        - "store4"
        - "store5"

#### 日志检索辅助:

1. 新中台转发消息日志: "ORDER_MESSAGE forward" 如果没有该日志,则说明没有转发,需要检查配置
2. business-order不应该有以下日志:
"business-order不应该有生单的消息走转发逻辑,应该走新的消息中台走,请检查配置两边的配置,为了不影响,这里照旧转发"
如果有则说明还是从business-order了，需要检查配置.

手动创单、rabbitmq延时重试还是走business-order转发