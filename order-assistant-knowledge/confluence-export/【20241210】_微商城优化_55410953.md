# 【20241210】 微商城优化

# 一、业务背景

## 1.1 业务背景

微商城订单优惠金额没透传到中台 部分金额没分摊到明细上 

微商城订单新订单分摊金额逻辑调整 优惠金额透传，查询调整，下账调整

## 1.2 痛点分析

优惠没透传 中台和前台明细对不上

## 1.3 系统现状

订单下单优惠券金额分摊流程

true用券流程falseautotoptrue18712

**1.活动分摊到了明细上 2.优惠券分摊到了明细上 3.现金券没分摊**

小前台页面金额问题

****

中台订单金额问题 B2C 020都有问题

| 订单 | 对应主表 | 明细 |
| --- | --- | --- |
| 1818111996452975879（活动+现金券） |  |  |
| 1818110176863181319（活动） |  |  |
| 1818212035565700871（100优惠券） |  |  |


# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

**现金券金额分摊到明细**

**金额透传到中台**

# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

**金额透传逻辑流程图**

**true金额透传falseautotoptrue25511**

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

**1、020订单查询界面**

**https://test-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/allV2/1818112001400162055?_t=0.007016218759509973&orderNo=1818112001400162055**

****

2、B2C订单查询界面

[https://test-merchants.hxyxt.com/businesses-gateway/b2c/1.0/order/detail/1818134709953595143?_t=0.8644530112748456](https://test-merchants.hxyxt.com/businesses-gateway/b2c/1.0/order/detail/1818134709953595143?_t=0.8644530112748456)

## 5.3 涉及数据库

| 数据库 | SQL |
| --- | --- |
| middle_order | ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `cash_coupon_amount` decimal(16, 4) NULL COMMENT '现金券金额' AFTER `sp_code`; |


## 5.4 安全设计

## 5.5监控报警

## 5.6 问题

**1.明细上的优惠券是否新增字段 看老逻辑里面的优惠券表**

**2.明细上 新增现金券 总金额 明细 JSON**

**3.明细里面多算的会员折上折是否剔除 待定**

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等