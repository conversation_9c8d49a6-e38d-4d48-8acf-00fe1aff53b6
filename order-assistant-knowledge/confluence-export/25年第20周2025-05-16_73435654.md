# 25年第20周2025-05-16

**团队公共事项链接：每周系统指标看板**

**周会前须完成事项：**①JIRA卡片状态变更、 ② 周报填写、 ③值班问题分析处理、④ CR问题汇总分析、⑤慢查询分析、 ⑥生产问题复盘总结、⑦ 上周TODO事项确认

# 一、上周TODO回顾

|  | 待办事项 | 预计完成时间 | 负责人 | 备注 |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |
| 3 |  |  |  |  |


# 二、本周工作情况

## 1、重点项目/专项周进展与风险概况

备注：可以挂jira看板链接，重要项目打标签

|  | 分类 | 文档 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- | --- | --- |
| 1 | 业务需求 | [交易生产项目仪表盘](https://jira.hxyxt.com/secure/Dashboard.jspa?selectPageId=10901) |  |  |  |
| 2 | 技术专项 | [2025年-技术专项](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=50317558) [企微文档](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgGx2hDvS0S72WqbAzMF?scode=AOsAFQcYAAcxB5DxhjAR8AOAYLADg&tab=of7bd1) |  |  |  |
| 3 | 指标简报 | [交易生产组周简报](https://doc.weixin.qq.com/sheet/e3_AdkAhgbzAEIxPQ8Xt0gR9Onc9lErq?scode=AOsAFQcYAAcREnv4wOAdkAhgbzAEI&version=4.1.32.6015&platform=win&tab=qlf8gy) |  |  |  |


## 2、重点项目/专项进展与风险

| 重点项目/专项 | 本周进展 | 下周计划 |
| --- | --- | --- |
| 微商城-交易链路切换 | 交易中台 开发中 | 1. 购物车  a. 购物车商品获取 （营销、会员接口需要确认） b. 购物车商品加购/减购 |
| 微商城-交易链路切换 | 支付中台: 产品设计中 | 1. 完成产品设计评审 |
| 微商城-小前台能力替换 | 能力迁移:  开发ing1.重写微商城新增订单流程-020 hydee-business-order服务 60% 2.微商城新增订单流程O20/B2C融合到 hydee-business-order服务 0% 3.剔除微商接口中台转发 0%  3.1剔除middle-datasync-message  3.2剔除 yxt-xframe 4.整合微商城新增订单 给2整合一起 0% 5.复写middle-order能力 0% 6.剔除middle-order 0% |  |
| 新老模型,数据双向同步 | 数据同步:  开发ing1. 同步框架-100% 2. 老模型 → 新模型   1. O2O正单-90%(含自测)   2. O2O逆单-0%   3. B2C正单-0%   4. B2C逆单-0%   5. 物流相关 3. O2O正单-90%(含自测) 4. O2O逆单-0% 5. B2C正单-0% 6. B2C逆单-0% 7. 物流相关 | 1. 老模型 → 新模型   1. B2C正单-85%（含自测）   2. O2O逆单-10% 2. B2C正单-85%（含自测） 3. O2O逆单-10% |


## 3、成员工作情况

|  | 成员 | 本周 | 下周 |
| --- | --- | --- | --- |
| 1 |  | **本周工作进展:** - **业务需求 1.微商城代发订单退款问题****2.新库存占用接口上线** - **技术专项****1.重写微商城新增订单流程-020 hydee-business-order服务 60%** - **其他**    **风险遗留项:** | **下周重点工作:**1.重写微商城新增订单流程-020 hydee-business-order服务 60% 2.微商城新增订单流程O20/B2C融合到 hydee-business-order服务 0% |
| 2 |  | **本周工作进展:** **业务需求**1. 海典线下订单对账接口试运行,金额、数量排查 2. 第二批次历史订单数据迁移,目前这部分工作需要重做  **技术专项**1. 优雅下线三期相关问题协助解决  **其他**   **风险遗留项:**1. 第二批次历史订单数据迁移方案调整，有延期风险,原因:   1. 已经迁移的数据需要删除,需要重新迁移到归档库   2. 已有接口需要兼容归档库、线下订单库。需要改造已将上线的接口,例如详情接口等   3. 第一批次遗留问题需要前置处理,目前方案变动后,这部分代码也需要适配 2. 已经迁移的数据需要删除,需要重新迁移到归档库 3. 已有接口需要兼容归档库、线下订单库。需要改造已将上线的接口,例如详情接口等 4. 第一批次遗留问题需要前置处理,目前方案变动后,这部分代码也需要适配 | **下周重点工作:**1. 第二批次历史订单数据迁移兼容归档库及其相关接口提测，配合测试 2. 第二批次历史订单数据迁移 3. 第一批次遗留问题处理完成 |
| 3 |  | **本周工作进展:** - **业务需求**   - **天猫B2C 上线 完成**   - 订单补推 erp 完成   - 采购中台建设 目前进度（35%） - **天猫B2C 上线 完成** - 订单补推 erp 完成 - 采购中台建设 目前进度（35%） - **技术专项****** - **其他**    **风险遗留项:** | **下周重点工作:****采购中台提测** |
| 4 |  | **本周工作进展:** - **业务需求** - **技术专项**   1. 老模型 → 新模型     1. O2O正单-90%(含自测)   2. O2O正单-90%(含自测)   1. O2O正单-90%(含自测) - 老模型 → 新模型   1. O2O正单-90%(含自测) - O2O正单-90%(含自测) - **其他**  **风险遗留项:** O2O配送相关表迁移问题-物流中台需规划 | **下周重点工作:**1. 老模型 → 新模型   1. B2C正单-85%（含自测）   2. O2O逆单-10% 2. B2C正单-85%（含自测） 3. O2O逆单-10% |
| 5 |  | **本周工作进展:** - **业务需求**   - 1. 评价中台测试bug修复   - 2. 店铺中台数据迁移 15%     - 店铺基础配置 开发完成     - 标签基础数据 5%   - 店铺基础配置 开发完成   - 标签基础数据 5%   - 3.电子小票测试   - 店铺基础配置 开发完成   - 标签基础数据 5% - 1. 评价中台测试bug修复 - 2. 店铺中台数据迁移 15%   - 店铺基础配置 开发完成   - 标签基础数据 5% - 店铺基础配置 开发完成 - 标签基础数据 5% - 3.电子小票测试 - **技术专项** - **其他**  **风险遗留项:** | **下周重点工作:**1. 评价中台上线 2. 电子小票2.0上线 3. 店铺中台数据迁移   1. 店铺扩展配置   2. 店铺自配送策略   3. 店铺语音配置   4. 店铺支付配置   5. 店铺标签配置 4. 店铺扩展配置 5. 店铺自配送策略 6. 店铺语音配置 7. 店铺支付配置 8. 店铺标签配置 |
| 6 |  | **本周工作进展:** - **业务需求** - **技术专项****** - **其他**    **风险遗留项:** | **下周重点工作:** |
| 7 |  | **本周工作进展:** - **业务需求**1. B2B 商城再来一单优化版本迭代 （联调完成， 等待一心助手提测） ****2. 采购中台下单支持 （开发完成， SDK 已提供，等待俊峰联调）- **技术专项**1. 微商城交易链路切换（customer代码整理中） - **其他**   **风险遗留项:**交易链路切换 - 前端对于购物车相关是否重构或兼容 需要等待产品 5月22日出prd后再确认。 | **下周重点工作:**1. 购物车链路切换- 购物车信息获取 2. 购物车链路切换-购物车加购/减购 |
| 8 |  | **本周工作进展:** - **业务需求**  1. 天猫B2C上线  2. 抖店审方上线- **技术专项****** - **其他**    **风险遗留项:** | **下周重点工作:**1. 对账测试 2. sonar扫描接入 |
| 9 |  | **本周工作进展: 4d** - **业务需求 微信支付配置数据清洗[开发完成]****导出支付配置交付产品****支付中台二期产品设计(50%)** - **技术专项****** - **其他**    **风险遗留项:** | **下周重点工作:**1. 微信支付配置数据清洗上线并处理数据 2. 支付中台二期产品设计评审 |


# 

# 三、系统运行监控

**备注：每周值班人负责整理 值班机制：系统问题值班SOP**

备注：每周值班同学负责整理跟进：包括错误日志告警、接口告警、devops系统资源告警、值班群等运营或用户反映的问题，重复的告警与问题只需记录一次，可大概描述出现频次。
 每周值班同学负责整理跟进：mysql慢sql、es慢查询、redis大key与慢查询、MongoDb慢查询，团队周会判断问题、指配负责人，确定优化计划建立JIRA，每周周会跟进，直到处理完成为止


|  | 分类 | 交易生产事项 |
| --- | --- | --- |
| 1 | 系统资源 | 需要 |
| 2 | 稳定性建设 | 需要 |
| 3 | 风险预警 | 暂定 |
| 需要 |
| 4 | 风险治理 | [[生产环境]告警问题](https://doc.weixin.qq.com/sheet/e3_AWoAvQaBAJE2hvyIxT0ReKqo8zMXP?scode=AOsAFQcYAAcD9ltN26AWoAvQaBAJE&tab=BB08J2) |
| [订单交易组-慢接口统计](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqyh22Ou8R8KzYBP2Vy?scode=AOsAFQcYAAc9OX2fGU) （[性能监控大盘 - Elastic](https://prod-elk.hxyxt.com/app/dashboards#/view/98bbdd80-b3c6-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now)))） |
| [订单交易组-mysql 慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACs3t2kzAhhSl00m7ioxx?scode=AOsAFQcYAAcOArH6zU)([订单组 Mysql慢查询大盘](https://prod-elk.hxyxt.com/app/dashboards#/view/3abe4030-b79d-11ef-a12e-ef4e05a1d720?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1w,to:now)))) |
| [订单交易组-redis慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsqYflwKkfRfiXPJqCaB?scode=AOsAFQcYAAcht1nUeY) [(订单组redis cluster)](https://prod-elk.hxyxt.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d,to:now))&_a=(columns:!(),filters:!(),index:%270eab3b90-7a50-11ef-a12e-ef4e05a1d720%27,interval:auto,query:(language:kuery,query:%27%27),sort:!(!(%27@timestamp%27,desc)))) |
| [订单交易组-ES慢查询](https://doc.weixin.qq.com/sheet/e3_Ae8AqwYcACsSdKyxzF3Q1WJXNxQMW?scode=AOsAFQcYAAcdXkVbYl) |
| 5 | CaseStudy |  |
| 6 | 网关超时 | [网关超时](https://doc.weixin.qq.com/sheet/e3_AZ0AgQYfAIE1q6vmJh1TSyty6lX4b?scode=AOsAFQcYAAcETWbkomAboAOAYLADg&tab=7r4w4e) |


# 四、质量与效率

## 1、本周发布质量回顾

**备注：关注上线部署失败、回滚情况，目的是回顾上线流程是否有问题、checklist是否梳理到位、测试是否到位**

**见于：**

## 2、本周代码质量回顾

### （1）本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘，宣导团队代码规范。

|  | GitLab链接 | 问题描述 | 优化方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |


### （2）本周Sonar代码扫描质量回顾

## 3、本周bug情况回顾

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等，宣导 团队质量意识；⑤重要阻塞bug跟进。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |


## 4、本周技术方案评审情况回顾

**备注：优秀技术方案通晒、问题技术方案指导**

|  | 技术方案 | 优点 | 缺点 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 五、团队建设

备注：团队管理动作、信息与制度传达、成员问题拉齐等





# 六、本周成长与分享

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |


# 七、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |  |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |