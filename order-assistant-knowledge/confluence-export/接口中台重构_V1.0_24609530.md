# 接口中台重构 V1.0

# 项目背景

在持续推进业务过程中发现原有的 api 中台出现了不可调和问题已经不在再适合继续使用，具体问题如下：

1. 现目前接口中台采用.NET CORE 6.0编写，与公司现有技术栈不符、不便于统一维护升级，不能使用公司统一的基础能力。
2. 现有业务中商品、订单、门店管理代码集中在同一服务中容易相互影响，特别是其他流程影响到订单履约流程。
3. 现有业务代码中包含了 海典从项目立项到OMS saas系统建立的所有代码，HEMS、OMS 以及海典官网的代码结合在一起使得代码冗余且不容易阅读。
4. 公司.NET 代码能力优先目前只有一名.NET 开发对接能力受限且第三方平台基本上都提供了java SDK ，改用java后能够显著提升对接速度。且其他团队目前已经在其他java工程中对接第三方能力，这样子使得代码分散且同样的底层功能需要多次实现。


针对目前困境通过.NET API中台重构，通过技术手段解决目前的问题。

1. 通过重构将技术平台迁移到java于现在的theplatform api 集合将所有的第三方对接全部统一管理。使用第三方提供的SDK 可以降低对接成本。（以对接一个接口为例.NET 中对接大概需要 2小时左右，java 的the3plat 中大概需要1小时。）
2. 只保留 OMS 系统所需的业务，精简字段和接口完全剥离HEMS 系统的代码。（下面接口描述阶段有详细列举，梳理合并后的接口）
3. 通过starter配置，可实现根据业务量级来拆分和合并业务模块灵活部署。
4. 通过提供sdk，可以减轻对接方例如订单中台、商品中台的对接效率。不在需要重新构建实体对照字段。


# 现有业务整理

| 服务名 | 核心业务 | 说明 | 此次是否重构? |
| --- | --- | --- | --- |
| ds-service-syst | 系统设置后台job | .NET 承接的所有平台的数据补偿job运营后台的部分功能 | 是 |
| [ds-service-gateway](http://jenkins.hxyxt.com/job/ds-service-gateway/) | 请求转发基础校验 | 网关 | 是 |
| [ds-service-logger](http://jenkins.hxyxt.com/job/ds-service-logger/) | mongo db 日志写入 | 全局日志服务 | 是 |
| [ds-service-eb](http://jenkins.hxyxt.com/job/ds-service-eb/)[ds-service-eb-callback](http://jenkins.hxyxt.com/job/ds-service-eb-callback/) | 订单正向、逆向  商品 门店 | 饿了么 o2o 业务 | 是 |
| [ds-service-jddj](http://jenkins.hxyxt.com/job/ds-service-jddj/)[ds-service-jddj-callback](http://jenkins.hxyxt.com/job/ds-service-jddj-callback/) | 订单正向、逆向  商品 门店 | 京东到家 o2o 业务 | 是 |
| [ds-service-mt](http://jenkins.hxyxt.com/job/ds-service-mt-callback/)[ds-service-mt-callback](http://jenkins.hxyxt.com/job/ds-service-mt-callback/) | 订单正向、逆向  商品 门店 | 京东到家 o2o 业务 | 是 |


# 重构思路

## 业务分析

true业务关联falseautotoptrue10016

## 服务架构图

### 代码分层结构详解

true.net重构架构图falseautotoptrue87624

### 端口号设定

1. gateway: 20000
2. callback: 21000 - 22000
3. order:23000 - 24000
4. goods:25000 -26000
5. rider:27000-28000
6. other:29000-30000


## 项目结构

true.
|--the3platform
|	|--the3platform-adapter-gateway
|	|	|--The3platformAdapterGatewayApplication
|	|	|--controller
|	|	|	|--OrderController
|	|	|	|--RiderController
|	|	|	|--GoodsController
|	|	|	|--StoreController
|	|	|--[the3platform-common]
|	|--the3platform-callback
|	|	|--The3platformCallbackApplication
|	|	|--controller
|	|	|	|--MeituanCallBackController
|	|	|	|--ElemeCallBackController
|	|	|	|--JDDJCallBackController
|	|	|	|--DoudianCallBackController
|	|	|	|--ShunfengCallBackController
|	|	|	|--DadaCallBackController
|	|	|	|--MTRiderCallBackController
|	|	|--[the3platform-common]
|	|--the3platform-adapter-order-meituan
|	|	|--The3platformAdapterMeituanApplication
|	|	|--[the3platform-application]
|	|	|--[the3platform-common]
|	|--the3platform-adapter-order-eleme
|	|--the3platform-adapter-order-jddj
|	|--the3platform-adapter-order-doudian
|	|--the3platform-adapter-rider
|	|--the3platform-adapter-goods
|	|--the3platform-adapter-store
|	|--the3platform-common
|	|	|--dto
|	|	|--mq
|	|	|--enum
|	|	|--config
|	|--the3platform-application
|	|	|--the3platform-application-order
|	|	|	|--controller
|	|	|	|	|--OrderController
|	|	|	|--mq
|	|	|	|	|--OrderMQListener
|	|	|	|--application
|	|	|	|	|--OrderApplication
|	|	|--the3platform-application-goods
|	|	|	|--controller
|	|	|	|	|--GoodsController
|	|	|	|--mq
|	|	|	|	|--GoodsMQListener
|	|	|	|--application
|	|	|	|	|--GoodsApplication
|	|	|--the3platform-application-rider
|	|	|	|--controller
|	|	|	|	|--RiderController
|	|	|	|--mq
|	|	|	|	|--RiderMQListener
|	|	|	|--application
|	|	|	|	|--RiderApplication
|	|	|--the3platform-application-store
|	|	|	|--controller
|	|	|	|	|--StoreController
|	|	|	|--mq
|	|	|	|	|--StoreMQListener
|	|	|	|--application
|	|	|	|	|--StoreApplication
|	|	|--the3platform-application-system
|	|	|	|--controller
|	|	|	|	|--SystemController
|	|	|	|--mq
|	|	|	|	|--SystemMQListener
|	|	|	|--application
|	|	|	|	|--SystemApplication
|	|	|--[the3platform-service]
|	|	|--[the3platform-common]
|	|--the3platform-service
|	|	|--OrderService
|	|	|--RiderService
|	|	|--[the3platform-manager]
|	|	|--[the3platform-common]
|	|--the3platform-manager
|	|	|-MeituanManager
|	|	|-ElemeManager
|	|	|-JDDJManager
|	|	|--[the3platform-common]
|	|	|--[the3platform-client]
|	|	|--[the3platform-repository]
|	|--the3platform-client
|	|	|--meituan
|	|	|--eleme
|	|	|--jddj
|	|	|--doudian
|	|	|--shunfeng
|	|	|--dada
|	|	|--meituan_rider
|	|	|--feign
|	|	|--[the3platform-repository]
|	|--the3platform-repository
|	|	|--redis
|	|	|--mysql
|	|	|--mongo
|	|--the3platform-sdk
|	|	|--the3platform-sdk-order
|	|	|--the3platform-sdk-store
|	|	|--the3platform-sdk-rider
|	|	|--the3platform-sdk-goods
|	|	|--the3platform-sdk-system
|	|	|--[the3platform-common]

## 三方平台回调流程

true.net重构架构设计falseautotoptrue94729

## 调用三方接口流程图

true调用三方服务流程图falseautotoptrue6315

# .net接口梳理

美团接口文档地址：[美团闪购医药团好货技术服务合作中心 (meituan.com)](https://tscc.meituan.com/home/<USER>/medical/21)

饿了么接口文档地址：[饿了么零售开放平台 (ele.me)](https://open-retail.ele.me/#/apidoc)

京东到家接口文档地址：[京东到家 | 开放平台 (jd.com)](https://opendj.jd.com/staticnew/widgets/resources.html)

美团配送文档地址：[开发文档 - 美团配送技术服务合作中心 (meituan.com)](https://peisong.meituan.com/tscc/docNew)

达达接口文档地址：[新达达开放平台 (imdada.cn)](https://newopen.imdada.cn/)

顺丰接口文档地址：[顺丰同城开放平台 (sf-express.com)](https://commit-openic.sf-express.com/#/apidoc)

## 订单接口

### 原有接口梳理

| 类型 | 接口描述 | 接口地址（.net） | 三方平台 |  | 迁移优先级 |
| --- | --- | --- | --- | --- | --- |
| 美团 | 饿了么 | 京东到家 | 微商城 |
| 正单-消息监听 |  | /callback/{PlatformCode}/create/{MerCode}/{ClientCode} | [已支付订单](https://tscc.meituan.com/home/<USER>/174) |  |  |  |  |
| [已确认订单](https://tscc.meituan.com/home/<USER>/177) |  |
| /callback/{PlatformCode}/update/{MerCode}/{ClientCode} | [订单信息修改消息](https://tscc.meituan.com/home/<USER>/297) |  |
| /callback/{PlatformCode}/finish/{MerCode}/{ClientCode} | [已完成订单](https://tscc.meituan.com/home/<USER>/180) |  |
| /callback/{PlatformCode}/remind/{MerCode}/{ClientCode} | [催单消息](https://tscc.meituan.com/home/<USER>/186) |  |
| /callback/{PlatformCode}/rider/{MerCode}/{ClientCode} | [配送状态](https://tscc.meituan.com/home/<USER>/171) |  |
| /callback/{PlatformCode}/all/{MerCode}/{ClientCode} | - | [订单创建通知](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.create&aopApiCategory=order_msg_group&type=push_menu) |  |  |  |
| [订单状态推送](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.status.push&aopApiCategory=order_msg_group&type=push_menu) |  |  |  |
| [订单催单通知](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.remind.push&aopApiCategory=order_msg_group&type=push_menu) |  |  |  |
| [订单物流状态推送](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.deliveryStatus.push&aopApiCategory=order_msg_group&type=push_menu) |  |  |  |
| [订单能否切自配&能否二次呼叫平台配送推送](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.deliveryOperation.push&aopApiCategory=order_msg_group&type=push_menu) |  |  |  |
| /callback/{PlatformCode}/auth/{MerCode}/{ClientCode} | - | 授权码接收 | 授权码接收 |  |  |
| /callback/{PlatformCode}/order/{MerCode}/{ClientCode}?action=/jddj | - | - | [创建新订单消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [拣货完成消息](https://opendj.jd.com/staticnew/widgets/api/MQDetail.html?groupId=0&messageId=0becae34d53345febfea238e3785d9fe) |  |  |
| [订单调整消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [订单开始配送消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [订单等待出库消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [订单妥投消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [订单转自送消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [拣货完成消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [用户取消消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [用户申请取消消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| [订单运单状态消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3001) |  |  |
| 新增或修改门店订单评价消息 |  |  |
| 商家评价审核完成消息 |  |  |
| /callback/{PlatformCode}/order/{MerCode}/{ClientCode} |  |  |  | 正单消息 |  |
| 正单-发起请求 | 订单接单 | /api/Order/ConfirmOrder | [确认订单](https://tscc.meituan.com/home/<USER>/111) | [确认订单](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.confirm-3?aopApiCategory=order_forward&type=null) | [商家确认接单接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=c1a15129d1374e9da7fa35487f878604) |  |  |
| 催单回复 | /api/Order/OrderRemindReply | [催单回复接口](https://tscc.meituan.com/home/<USER>/168) | [商户回复催单](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.remind.reply-3?aopApiCategory=order_all&type=api_menu) | - |  |  |
| 确认拣货完成 | /api/Order/OrderPickComplete | [商家确认已完成拣货](https://tscc.meituan.com/home/<USER>/217) | [订单拣货完成](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.pickcomplete-3?aopApiCategory=order_forward&type=null) | 1. 自配送：[拣货完成且商家自送接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=0e08e71a45dc48b6a337e06a852ac33a) 2. 达达：order/OrderDDTCDelivery 3. 自提：[拣货完成且顾客自提接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=4c0133c8cb264ee5b3f66f2881363cd6) 4. 平台配送：[拣货完成且众包配送接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=ed93745b86c6487eaaea5f55a84785ac) 5. 转自配：[订单达达配送转商家自送接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=e7b4950164754eecac7ea87278c2b071) |  |  |
| 拣货完成 | /api/Order/PickingCompleted | [自配订单已送达](https://tscc.meituan.com/home/<USER>/120) | [自配送接入骑手状态](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryStateSync-3?aopApiCategory=order.delivery_all&type=api_menu) | [订单妥投接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=ecc80f06d35141979f4841f345001f74) |  |  |
| 获取订单详情 |  | [获取订单详细信息](https://tscc.meituan.com/home/<USER>/135) | [查看订单详情](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.get-3?aopApiCategory=order_forward&type=null) | [订单列表查询接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=ba3027848c3c4fda9674966e2a466482) |  |  |
| 获取订单状态 |  | [查询订单状态](https://tscc.meituan.com/home/<USER>/129) | [查看订单状态](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.status.get-3?aopApiCategory=order_forward&type=null) |  |
| 获取真实手机号 | /api/Order/GetPhoneNumber | [拉取用户真实手机号](https://tscc.meituan.com/home/<USER>/223) | - | - |  |  |
| 处方信息查询 |  | /api/v1/gw/rp/picture/list | [订单关联处方笺查询](https://open-retail.ele.me/#/apidoc/me.ele.retail:drug.trade.prescriptiondetail.query-3?aopApiCategory=drug_all&type=api_menu) | [通过订单号查询用药人信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=e90160db92bb4afc8b5e6fe90935fde2) |  |  |
| 获取用药人信息 | /api/order/GetOrderUsedPrescription | order/hospital/rp/used/list |  |
| 订单补单 | /api/Order/RepairO2oOrder |  |  |  |  |  |
| 订单转自配送 | /api/Order/OrderChangeSelf | [专快混配送/企客配送转为商家自配送](https://tscc.meituan.com/home/<USER>/238) | [配送异常切自配送](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.switchselfdelivery-3?aopApiCategory=order_all&type=api_menu) | [订单达达配送转商家自送接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=e7b4950164754eecac7ea87278c2b071) |  |  |
| O2O订单发货(配送) | /api/Order/OrderDelivery | [自配订单配送中](https://tscc.meituan.com/home/<USER>/117) | [自配送接入骑手状态](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryStateSync-3?aopApiCategory=order.delivery_all&type=api_menu) | [拣货完成且商家自送接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=0e08e71a45dc48b6a337e06a852ac33a) | /1.0/admin/merchant/orders/e_send |  |
| 同步骑手状态到平台 | api/Order/OrderRiderStatusSync | [自配送商家同步发货状态和配送信息](https://tscc.meituan.com/home/<USER>/355) | [自配送接入骑手轨迹](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryLocationSync-3?aopApiCategory=order_all&type=api_menu) | - |  |  |
| 订单妥投 | /api/Order/OrderDeliveryEnd | [自配订单已送达](https://tscc.meituan.com/home/<USER>/120) | [自配送接入骑手状态](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfDeliveryStateSync-3?aopApiCategory=order.delivery_all&type=api_menu) | [订单妥投接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=ecc80f06d35141979f4841f345001f74) | /1.0/admin/merchant/orders/e_store/_receive |  |
| 订单发货 | /api/Order/OmsOrderDelivery | [自配订单配送中](https://tscc.meituan.com/home/<USER>/117) | - | /1.0/admin/merchant/orders/e_send |  |
| 订单发货(快递配送) | /api/Order/LogisticsOrderSendOut | [快递配送商家同步配送信息接口](https://tscc.meituan.com/home/<USER>/357) | [【快递发货】订单绑定运单](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.bind.express-3?aopApiCategory=order.delivery_all&type=api_menu) | [绑定第三方运单号接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=4bbf6ad4d05d4fd89622cea515242aae) |  |  |
| 快递配送商家同步配送信息 | /api/Order/OrderLogisticsbtocsync | [快递配送商家同步配送信息接口](https://tscc.meituan.com/home/<USER>/357) |  |
| 取消呼叫配送 | /api/Refund/DeliveryCancel | [取消美团配送订单](https://tscc.meituan.com/home/<USER>/141) | [配送异常不再配送](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.stopdelivery-3?aopApiCategory=order_all&type=null) | - |  |  |
| 自提核验接口 |  | - | [自提订单核销](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.selfpick.checkout-3?aopApiCategory=order.forward_all&type=api_menu) | [订单自提码核验接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=428fa2cb66784b64a85db36ec2972ff9) |  |  |
| 获取评论 |  | [根据门店id批量查询评价信息](https://tscc.meituan.com/home/<USER>/191) | [获取商户订单评论](https://open-retail.ele.me/#/apidoc/me.ele.retail:ugc.ordercomment.get-3?aopApiCategory=ugc_all&type=api_menu) | - |  |  |
| 评论回复 |  | [根据评价id添加商家回复](https://tscc.meituan.com/home/<USER>/194) | [回复评论](https://open-retail.ele.me/#/apidoc/me.ele.retail:ugc.reply-3?aopApiCategory=ugc_all&type=api_menu) |  |
| 退单-消息监听 |  | /callback/{PlatformCode}/cancel/{MerCode}/{ClientCode} | [推送用户或客服取消订单](https://tscc.meituan.com/home/<USER>/201) | - | - |  |  |
| /callback/{PlatformCode}/refund/{MerCode}/{ClientCode} | [推送全额退款信息](https://tscc.meituan.com/home/<USER>/204) |  |
| /callback/{PlatformCode}/partrefund/{MerCode}/{ClientCode} | [推送部分退款信息](https://tscc.meituan.com/home/<USER>/207) |  |
| /callback/{PlatformCode}/hemspartrefund/{MerCode}/{ClientCode} |  |  |  |
| /callback/{PlatformCode}/hemsrefundresult/{MerCode}/{ClientCode} |  |  |  |
| /callback/{PlatformCode}/hemsrefund/{MerCode}/{ClientCode} |  |  |  |
| /callback/{PlatformCode}/all/{MerCode}/{ClientCode} | - | [订单逆向消息推送](https://open-retail.ele.me/#/msgdoc/detail?topicName=order.reverse.push&aopApiCategory=order_msg_group&type=push_menu) |  |  |
| /callback/{PlatformCode}/order/{MerCode}/{ClientCode}?action=/jddj | - | - | [创建售后单申请消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3002) |  |  |
| [创建售后单消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3002) |  |  |
| [修改售后单申请消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3002) |  |  |
| [售后单状态消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3002) |  |  |
| [售后单最终完成消息（物竞天择）](https://opendj.jd.com/staticnew/widgets/resources.html?id=3002) |  |  |
| /callback/{PlatformCode}/refundorder/{MerCode}/{ClientCode} | - | - |  | 退单消息 |  |
| 退单-发起请求 | 拒绝订单退款审核 | /api/Refund/OrderRefundReject | [驳回订单退款申请](https://tscc.meituan.com/home/<USER>/126) | [商家审核逆向单](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.reverse.process-3?aopApiCategory=order_reverse&type=api_menu) | [商家审核用户取消申请接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=906b430307764a3ca3698c05c72f33d0) | - |  |
| 拒绝订单退款审核 | api/Refund/OrderRefundAuditReject | - | /1.0/admin/merchant/refunds/_reject |  |
| 同意订单退款审核 | /api/Refund/OrderRefundAgree | [订单确认退款请求](https://tscc.meituan.com/home/<USER>/123) | - |  |
| 同意订单退款审核 | api/Refund/OrderRefundAuditAgree | - | /1.0/admin/merchant/refunds/_agree |  |
| 申请售后单审核 | /api/Refund/AfsOpenApprove | [售后单（退款/退货退款）审核接口](https://tscc.meituan.com/home/<USER>/411) | - | - | - |  |
| 订单取消 | /api/Refund/OrderCancelB2C/O2O | [商家取消订单](https://tscc.meituan.com/home/<USER>/114) | [商家发起逆向退款](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.reverse.apply-3?aopApiCategory=order_reverse&type=api_menu) | [订单取消且退款接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=6be3f3a811f14f22a83007ab02f23b03) | /1.0/admin/merchant/orders/e_cancel |  |
| 订单取消 | /api/Refund/HemsOrderCancel |  |
| 发起部分退款 | /api/Refund/OrderApplyPartRefund | [发起部分退款](https://tscc.meituan.com/home/<USER>/165) | [商家自主发起售后接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=170&apiid=b8d1ddacb03846a8a2e78c79723c752f) | - |  |
| 获取退单明细 | /api/Refund/OrderRefundDetail | [获取订单退款记录](https://tscc.meituan.com/home/<USER>/322) | [订单逆向查询](https://open-retail.ele.me/#/apidoc/me.ele.retail:order.reverse.query-3?aopApiCategory=order_reverse&type=api_menu) | [查询售后单详情接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=170&apiid=6805ed690b7b4776b058067312c57d98) | - |  |
| 申请售后单审核接口 | api/Afs/AfsOpenApproveRequest | - | - | [申请售后单审核接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=170&apiid=1690f6efc0144d59823b236e0d8506a1) | - |  |
| 售后单确认收货 | api/Afs/AfsConfirmReceiptRequest | - | - | [售后单确认收货接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=170&apiid=4826086e81934405980ae26f80d956e0) | - |  |
| 售后单确认未收到货 |  | - | - | [商家确认未收到货](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=170&apiid=73f133f17bbd4ece9fc5b119f3439a0e) | - |  |


## 门店接口

### 原有接口梳理

| 类型 | 接口描述 | 接口地址（.net） | 三方平台 | 迁移优先级 |
| --- | --- | --- | --- | --- |
| 美团 | 饿了么 | 京东到家 | 微商城 |
| 发起请求 | 获取线上门店 | api/Shop/O2OShopListGet | 1. [获取应用已绑定门店的三方门店ID](https://tscc.meituan.com/home/<USER>/6) 2. [批量获取门店详细信息](https://tscc.meituan.com/home/<USER>/9) | [商户列表](https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.list-3?aopApiCategory=shop_manage&type=api_menu) | /djstore/getStoreInfoPageBean1. [获取到家门店编码列表接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=138426aa19b54c48ae8464af1ca3b681) 2. [根据到家门店编码查询门店基本信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=4c48e347027146d5a103e851055cb1a7) | - |  |
| 获取门店信息 | api/Shop/GetShopList | /1.0/merchant/store/_search |  |
| 获取门店信息 | api/Shop/GetShopSingle | - | [查看商户](https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.get-3?aopApiCategory=shop_manage&type=api_menu) | - | - |  |
| 获取门店令牌 | api/Shop/ShopAccessTokenGet | [ISV安全认证](https://tscc.meituan.com/home/<USER>/market/10210) | - | - | - |  |
| 更新门店状态 | api/Shop/UpdateStoreStatus | 1. [门店设置为营业状态](https://tscc.meituan.com/home/<USER>/12) 2. [门店设置为休息状态](https://tscc.meituan.com/home/<USER>/15) | 1. [商户开业](https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.open-3?aopApiCategory=shop_buss&type=api_menu) 2. [商户休息中](https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.close-3?aopApiCategory=shop_buss&type=api_menu) | /store/updateStoreInfo4Open[修改门店基础信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=2600369a456446f0921e918f3d15e96a) | - |  |
| 更新门店营业时间 | api/Shop/UpdateStoreBusinessHoursWithPartnerId | [更新门店营业时间](https://tscc.meituan.com/home/<USER>/33) | [修改商户](https://open-retail.ele.me/#/apidoc/me.ele.retail:shop.update-3?aopApiCategory=shop_manage&type=api_menu) | [修改门店基础信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=2600369a456446f0921e918f3d15e96a) | - |  |
| 获取门店对账单 | api/Shop/GetShopBalanceBill | [获取门店日账单](https://tscc.meituan.com/home/<USER>/600) | [获取账单订单明细信息](https://open-retail.ele.me/#/apidoc/me.ele.retail:bill.orderdetail-3?aopApiCategory=order_bill&type=api_menu) | [分页查询对账单接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=182&apiid=0bedea74a477409990b2ff8666a9ff44) | - |  |
| 获取美团门店日账单 | api/Shop/GetMeiTuanShopBillofDay | - | - | - |  |
| 电子围栏 |  |  |  | [查询门店围栏信息](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=fd6c04b2f57844cba39af529e0517715) | - |  |
| 消息推送 |  | /callback/{PlatformCode}/shop/{MerCode}/{ClientCode} | [推送门店状态变更](https://tscc.meituan.com/home/<USER>/349) |  | [新增或修改门店消息](https://opendj.jd.com/staticnew/widgets/resources.html?id=3004) | - |  |


## 配送接口

### 原有接口梳理

| 类型 | 接口描述 | 接口地址（.net）美团：MeiTuanRider、达达：DadaRider、顺丰：ShunFengRider | 三方平台 | 迁移优先级 |
| --- | --- | --- | --- | --- |
| 美团 | 达达 | 顺丰 |
| 发起请求 | 获取配送骑手轨迹 | api/{rider}/RiderTrailInfoGet | [获取骑手位置](https://peisong.meituan.com/tscc/docNew#%E8%8E%B7%E5%8F%96%E9%AA%91%E6%89%8B%E4%BD%8D%E7%BD%AE) | [订单详情](https://newopen.imdada.cn/#/development/file/statusQuery) | [获取配送员实时坐标接口](https://commit-openic.sf-express.com/#/apidoc) |  |
| 骑手门店修改 | api/{rider}/RiderShopUpdate | - | [更新门店](https://newopen.imdada.cn/#/development/file/shopUpdate) | - |  |
| 骑手门店获取 | api/{rider}/RiderShopGet | [查询门店信息](https://peisong.meituan.com/tscc/docNew#%E6%9F%A5%E8%AF%A2%E9%97%A8%E5%BA%97%E4%BF%A1%E6%81%AF) | [门店详情](https://newopen.imdada.cn/#/development/file/shopDetail) | [获取商户信息](https://commit-openic.sf-express.com/#/apidoc) |  |
| 骑手门店添加 | api/{rider}/RiderShopAdd | [创建门店](https://peisong.meituan.com/tscc/docNew#%E5%88%9B%E5%BB%BA%E9%97%A8%E5%BA%97) | [创建门店](https://newopen.imdada.cn/#/development/file/shopAdd) | - |  |
| 预发单比价 | api/{rider}/RiderPreOrderForCompareAdd | [发单前预览](https://peisong.meituan.com/tscc/docNew#%E5%8F%91%E5%8D%95%E5%89%8D%E9%A2%84%E8%A7%88) | [查询运费](https://newopen.imdada.cn/#/development/file/readyAdd) | [预创建订单](https://commit-openic.sf-express.com/#/apidoc) |  |
| 骑手预发订单添加 | api/{rider}/RiderPreOrderAdd | [发单前预览](https://peisong.meituan.com/tscc/docNew#%E5%8F%91%E5%8D%95%E5%89%8D%E9%A2%84%E8%A7%88) | - | - |  |
| 骑手订单获取 | api/{rider}/RiderOrderGet | - | [订单详情](https://newopen.imdada.cn/#/development/file/statusQuery) | - |  |
| 骑手订单取消 | api/{rider}/RiderOrderCancel | [取消订单](https://peisong.meituan.com/tscc/docNew#%E5%8F%96%E6%B6%88%E8%AE%A2%E5%8D%95) | [取消订单](https://newopen.imdada.cn/#/development/file/formalCancel) | [取消订单](https://commit-openic.sf-express.com/#/apidoc) |  |
| 骑手订单添加 | api/{rider}/RiderOrderAdd | [发单](https://peisong.meituan.com/tscc/docNew#%E5%8F%91%E5%8D%95) | [直接下单](https://newopen.imdada.cn/#/development/file/add)[重新下单](https://newopen.imdada.cn/#/development/file/reAdd) | [创建订单](https://commit-openic.sf-express.com/#/apidoc) |  |
| 获取骑士配送信息H5页面 |  | [获取骑手位置H5页面](https://peisong.meituan.com/tscc/docNew#%E8%8E%B7%E5%8F%96%E9%AA%91%E6%89%8B%E4%BD%8D%E7%BD%AEH5%E9%A1%B5%E9%9D%A2) | [获取骑士配送信息H5页面](https://newopen.imdada.cn/#/development/file/queryKnightH5Page) | [获取配送员轨迹H5](https://commit-openic.sf-express.com/#/apidoc) |  |
| 消息推送 |  | /api/callback/{platformCode}/riderorder/{MerCode}/{ClientCode} | [订单状态回调](https://peisong.meituan.com/tscc/docNew#%E8%AE%A2%E5%8D%95%E7%8A%B6%E6%80%81%E5%9B%9E%E8%B0%83) | [订单状态回调](https://newopen.imdada.cn/#/development/file/order) | [配送状态更改回调](https://commit-openic.sf-express.com/#/apidoc)[订单完成](https://commit-openic.sf-express.com/#/apidoc)[顺丰原因订单取消回调](https://commit-openic.sf-express.com/#/apidoc) |  |


## 商品接口

| 类型 | 接口描述 | 接口地址（.net） | 三方平台 | 迁移优先级 |
| --- | --- | --- | --- | --- |
| 美团 | 饿了么 | 京东到家 |
| 发起请求 | 库存批量同步 | api/Item/QuantityBatchSync | [批量更新SKU库存](https://tscc.meituan.com/home/<USER>/10116) | [批量修改商品库存](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.stock.update.batch-3?aopApiCategory=item_stock&type=item_all) | [批量修改现货库存接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=200&apiid=10812f9fc7ee4564b552f19270a7e92e) |  |
| api/Item/BatchQuantityUpdate |  |
| 价格批量同步 | api/Item/PriceBatchSync | [批量更新药品价格【不推荐使用】](https://tscc.meituan.com/home/<USER>/277)[批量更新SKU价格【推荐】](https://tscc.meituan.com/home/<USER>/10121) | [批量修改商品价格](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.price.update.batch-3?aopApiCategory=item_price&type=item_all) | [修改门店价格接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=205&apiid=fcbf346648a54d03b92dec8fa62ea643) |  |
| api/Item/BatchPriceUpdate |  |
| 批量修改商品 | api/Item/ItemsUpdate | [批量更新药品](https://tscc.meituan.com/home/<USER>/87) | - | [修改商品信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=32de39cd49104749b059942070458c51) |  |
| 商品批量上线 | api/Item/ItemsOnline | [药品批量上下架](https://tscc.meituan.com/home/<USER>/311) | [批量商品上架](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.online-3?aopApiCategory=item_online&type=item_all) | [修改门店商品可售状态接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=200&apiid=b783a508e2cf4aca94681e4eed9af5bc) |  |
| 商品批量下线 | api/Item/ItemsOffline | [批量商品下架](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.offline-3?aopApiCategory=item_online&type=item_all) |  |
| 获取商品对照码 | api/Item/ItemsGet | [查询门店药品列表【不推荐使用】](https://tscc.meituan.com/home/<USER>/89)[查询门店商品列表【推荐】](https://tscc.meituan.com/home/<USER>/10118) | [商品列表](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.list-3?aopApiCategory=item_manage&type=item_all) | [新查询商家已上传商品信息列表接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=dabe0d7a01b5434ea1323c844bf2d734) |  |
| 批量删除商品 | api/Item/ItemsDelete | [删除药品](https://tscc.meituan.com/home/<USER>/88) | [批量删除商品](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.delete-3?aopApiCategory=item_manage&type=item_all) | - |  |
| 批量创建商品 | api/Item/ItemsCreate | [批量创建药品](https://tscc.meituan.com/home/<USER>/86) | - | ？？[修改商家店内分类信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=1de278c670b64da492f676ab78d62f73) [新版根据商品UPC码批量新增商品接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=c4520585f28246338895d02b6d90d3a3) |  |
| 批量更新商家商品编码 | api/Item/ItemOutSkuidsUpdate | [批量更新app_medicine_code](https://tscc.meituan.com/home/<USER>/298) | - | [批量更新商家商品编码接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=4155d29bbdf649b69c67473b705ce7e7) |  |
| 创建商品分类 | api/Item/CategoryCreate | [创建药品分类](https://tscc.meituan.com/home/<USER>/80) | [新增店铺内分类](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.shop.category.create-3?aopApiCategory=sku_shop_category&type=item_all) | [新增商家店内分类信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=de26f24a62aa47a49e5ab7579d638cb3) |  |
| 获取商品分类 | api/Item/CategoryGet | [查询门店药品分类列表](https://tscc.meituan.com/home/<USER>/83) | [获取商品类目列表](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.shop.category.get-3?aopApiCategory=sku_shop_category&type=item_all) | [查询商家店内分类信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=964f2d248a7e42b196be2ab35b4e93b4) |  |
| 更新商品分类 | api/Item/CategoryUpdate | [更新药品分类](https://tscc.meituan.com/home/<USER>/81) | [修改店铺内分类](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.shop.category.update-3?aopApiCategory=sku_shop_category&type=item_all) | [修改商家店内分类信息接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=1de278c670b64da492f676ab78d62f73) |  |
| 删除商品分类 | api/Item/CategoryDelete | [删除药品分类](https://tscc.meituan.com/home/<USER>/82) | [删除店铺内分类](https://open-retail.ele.me/#/apidoc/me.ele.retail:sku.shop.category.delete-3?aopApiCategory=sku_shop_category&type=item_all) | [删除商家店内分类接口](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=180&apiid=c17b96e9fe254b2a8574f6d1bc0c1667) |  |
| 消息推送 |  | /data-sync/third/callback/{platformCode}/selectStock/{MerCode}/{ClientCode} | [拉取实时商品库存回调](https://tscc.meituan.com/home/<USER>/490) | ？ | ？ |  |
| /callback/{PlatformCode}/createitem/{MerCode}/{ClientCode} | [新增药品消息](https://tscc.meituan.com/home/<USER>/653) | [商品创建消息](https://open-retail.ele.me/#/msgdoc/detail?topicName=sku.create.push&aopApiCategory=item_msg_group&type=push_menu) | - |  |
| /callback/{PlatformCode}/updateitem/{MerCode}/{ClientCode} | [更新药品消息](https://tscc.meituan.com/home/<USER>/655) | [商品修改消息](https://open-retail.ele.me/#/msgdoc/detail?topicName=sku.update.push&aopApiCategory=item_msg_group&type=push_menu) |  |
| /callback/{PlatformCode}/removeitem/{MerCode}/{ClientCode} | [删除药品消息](https://tscc.meituan.com/home/<USER>/654) | [商品删除消息](https://open-retail.ele.me/#/msgdoc/detail?topicName=sku.delete.push&aopApiCategory=item_msg_group&type=push_menu) |  |


## 系统设置

| 类型 | 模块 | 接口地址（.net） | 调用方 | 接口描述 |
| --- | --- | --- | --- | --- |
| 发起请求 | Oms | api/OmsClient/OmsClientAddO2o | 心云 新零售 | o2o新增 client，例如网店 第三方配送账号 |
|  |  | api/OmsClient/OmsClientAddB2c | 心云 新零售 | b2c新增 client, 例如网店 |
|  |  | api/OmsClient/OmsClientDelete | 心云 新零售 | 删除client |
|  |  | api/OmsClient/OmsClientGet | 心云 新零售 | 获取client |
|  |  | api/OmsClient/OmsClientUpdate | 心云 新零售 | 更新client |
|  |  | api/OmsClient/OmsOrderGet | 心云 新零售 | 获取订单详情，此接口查询mysql ，不再调用第三方平台 |
|  |  | api/Group/GroupCacheRefresh | 开发者 | 刷新redis 缓存。直接操作数据库可以 直接删除redis 缓存 |
|  |  | api/Group/DeleteGroup | 心云 新零售 | 删除group info 信息 |
| 定时job | order | AnomalyOrderErrorJob |  | 订单处理异常 这里指第三方平台返回错误的订单。 |
|  |  | AnomalyOrderJob |  | 接口中台处理异常的订单补推 |
|  |  | MongoDBToMqJob |  | order 消费异常（mongo落库异常），补偿推送 |
|  |  | MqRepairJob |  | 发送MQ 失败，补推 |
|  |  | OmsOrderPushJob |  | 心云接受第三方平台推送，发送MQ 失败。补推 |
|  |  | OmsOrderToHisJob |  | hydee_aurora_order 数据迁移至 hydee_aurora_order_his |
|  |  | RiderMessageRepairJob |  | 配送消息消费异常，补推 |


# 新接口梳理

**true新接口梳理falseautotoptrue10915**

# 迁移排期

## 第一期

- **开发完成时间：**
- **测试完成时间：**
- **上线时间：**


| 功能模块 | 功能项 |  | 优先级 | 工时 | 开发时间 | 负责人 |
| --- | --- | --- | --- | --- | --- | --- |
| 项目框架搭建 | 项目框架搭建 + 部署 |  | P0 |  | - |  |
| gateway | gateway + shardingJDBC + mongo集成 |  |  |  | - |
| 订单 | 抖店（改造） | 正单-回调 |  |  | - |
| 正单 |  |  |
| 逆单-回调 |  |  | - |  |
| 逆单 |  |  |
| order服务接口替换 |  |  | - |
| 商品 | 抖店（改造） |  |  |  | - |  |
| SDK | SDK |  |  |  | - |  |


| 功能模块 | 功能项 |  | 优先级 | 工时 | 开发时间 | 负责人 |
| --- | --- | --- | --- | --- | --- | --- |
| 技术设计 | 技术设计 |  | P0 |  |  |  |
| 项目搭建 | 项目搭建 |  | P0 |  |  |  |
| 订单 | 美团 | 正单-回调 |  |  |  |  |
| 正单 |  |  |  |  |
| 逆单-回调 |  |  |  |  |
| 逆单 |  |  |  |  |
| 饿了么 | 正单-回调 |  |  |  |  |
| 正单 |  |  |  |  |
| 逆单-回调 |  |  |  |  |
| 逆单 |  |  |  |  |
| 京东到家 | 正单-回调 |  |  |  |  |
| 正单 |  |  |  |  |
| 逆单-回调 |  |  |  |  |
| 逆单 |  |  |  |  |
| 抖店（改造） | 正单-回调 |  |  |  |  |
| 正单 |  |  |  |  |
| 逆单-回调 |  |  |  |  |
| 逆单 |  |  |  |  |
| 微商城 | 正单 |  |  |  |  |
| 逆单 |  |  |  |  |
| 门店 | 美团 |  |  |  |  |  |
| 饿了么 |  |  |  |  |  |
| 京东到家 |  |  |  |  |  |
| 微商城 |  |  |  |  |  |
| 配送平台 | 美团 |  |  |  |  |  |
| 达达 |  |  |  |  |  |
| 顺丰 |  |  |  |  |  |
| 商品 | 美团 |  |  |  |  |  |
| 饿了么 |  |  |  |  |  |
| 京东到家 |  |  |  |  |  |
| 抖店（改造） |  |  |  |  |  |
| System |  |  |  |  |  |  |
| SDK | SDK |  |  |  |  |  |