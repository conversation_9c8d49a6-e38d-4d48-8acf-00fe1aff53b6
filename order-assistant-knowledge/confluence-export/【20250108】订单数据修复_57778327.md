# 【20250108】订单数据修复

**Green已上线,数据已修复**

**数据修复内容:**

| 修复JIRA | Scene | 异常数据来源 |  | 触发 |
| --- | --- | --- | --- | --- |
| 一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3974 | ONLINE_ORDER_AS_OFFLINE_ORDER | topic: ${mq.topic.consumer.hdOfflineOrder} tag: REMOVE_ONLINE_ORDER | **数据从order-sync进来，然后通过order-atom-service接口入库**com.yxt.order.atom.sdk.offline_order.RepairApi#haiDianOnlineOrderAsOfflinecom.yxt.order.atom.sdk.offline_order.RepairApi#haiDianOnlineRefundOrderAsOffline | **XXL-JOB:****ProblemDataRepairHandler** |
| ONLINE_ORDER_AS_OFFLINE_REFUND_ORDER |
| 一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-4015 | CHAI_LING_GOODS_AMOUNT_ERROR_ORDER | topic: ${mq.topic.consumer.hdOfflineOrder} tag: CHAI_LING | **数据从order-sync进来，然后通过order-atom-service接口入库**com.yxt.order.atom.sdk.offline_order.RepairApi#haiDianChaiLingOrdercom.yxt.order.atom.sdk.offline_order.RepairApi#haiDianChaiLingRefundOrder |
| CHAI_LING_GOODS_AMOUNT_ERROR_REFUND_ORDER |
| 一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-4119 | KE_CHUAN_TOTAL_AMOUNT_ERROR |  | [http://localhost:8080/doc.html#/default/problem-data-get-handler-controller/fixKeChuanAmountErrorUsingPOST](http://localhost:8080/doc.html#/default/problem-data-get-handler-controller/fixKeChuanAmountErrorUsingPOST) | 接口: /fix/ke-chuan/detail-amount-error |


**项目:**

order-atom-service

order-sync-service

order-framework

- order-types
- order-common


**版本前缀:**

fixOfflineOrderData-

**分支:**

fix-offline-order-data-20250108

**SDK:**

<dependency>
  <groupId>com.yxt.order.types</groupId>
  <artifactId>order-types</artifactId>
  <version>fixOfflineOrderData-SNAPSHOT</version>
</dependency>
<dependency>
  <groupId>com.yxt.order.common</groupId>
  <artifactId>order-common</artifactId>
  <version>fixOfflineOrderData-SNAPSHOT</version>
</dependency>
<dependency>
  <groupId>com.yxt.order.atom.sdk</groupId>
  <artifactId>order-atom-sdk</artifactId>
  <version>fixOfflineOrderData-SNAPSHOT</version>
</dependency> 

**订单数据修复表**

CREATE TABLE `order_data_repair` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `input` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '待修复的数据输入',
  `scene` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '修复场景',
  `pre_check` varchar(10) COLLATE utf8mb4_general_ci NOT NULL COMMENT '预检查 WAIT,PASS,FAILED',
  `pre_check_failed` text COLLATE utf8mb4_general_ci COMMENT '预检失败原因',
  `before_image` text COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修复前快照',
  `after_image` text COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修复后快照',
  `repair_result` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修复结果 true,false',
  `repair_failed_reason` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修复失败的原因',
  `business_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单号',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_scene_preCheck_result` (`scene`,`pre_check`,`repair_result`) USING BTREE COMMENT '联合索引',
  KEY `idx_business_no` (`business_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单数据修复';

删除表:

CREATE TABLE `deleted_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `business_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单号',
  `service_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除数据的服务名',
  `database_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表',
  `deleted_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '删除数据',
  `created_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_business_no` (`business_no`) USING BTREE COMMENT '业务单号',
  KEY `idx_created_time` (`created_time`) USING BTREE COMMENT '创建时间',
  KEY `idx_db_table` (`database_name`,`table_name`) USING BTREE COMMENT '数据库和表名'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

**订单数据修复流程**

true数据修复falseautotoptrue14264

原始数据获取

| 修复项 | Scene | 需提供的数据 | 所属 | TODO | 实现 |
| --- | --- | --- | --- | --- | --- |
| 修复线上订单当做线下订单传过来 | ONLINE_ORDER_AS_OFFLINE_ORDER | 门店编码、三方单号，生单时间 | 海典 | 23 complete 添加删除字段   24 complete 已有索引数据删除 | 通过已有的MQ通道,来接收待修复数据 |
|  |  |  |  |  |  |


**上线步骤:**

  9 complete 同步master分支  28 complete order-framework   29 complete order-sync   30 complete order-atom-service     19 complete 发布SDK  20 complete order-types   32 complete order-common   36 complete order-atom-sdk     10 complete 提交pr  11 complete order-atom-service   12 complete order-sync-service   40 complete order-framework     13 complete 建表  37 complete order-data-repair   38 complete deleted_data     14 incomplete 配置job  15 incomplete 预检job   16 incomplete 修复job