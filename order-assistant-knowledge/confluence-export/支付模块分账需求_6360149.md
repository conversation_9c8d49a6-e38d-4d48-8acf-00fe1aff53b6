# 支付模块分账需求

# 一、背景

## 1.1 业务背景

1. 由于三方代发的订单前期在老一心到家商城受到了微信支付警告（线下对账的财务风险提示），所以需要在新商城中实现云仓商品线上结算分账对账，以避免微信侧报警。
2. 当前云仓订单海典已经支持在线分账（汇付），但是由于本期加入了云仓商品支持心币兑换商品订单，导致该类型的云仓兑换订单无法沿用当前的云仓在线分账功能；所以需要针对云仓的兑换商城订单独立一套支付分账的逻辑；


## 1.2 痛点分析

目前不支持使用心币兑换云仓的商品订单进行订单分账流程

## 1.3 系统现状

 云仓订单海典已经支持在线分账（汇付）

# 二、需求分析

## 2.1 业务流程

## 2.2 需求功能点

| 功能模块 | 功能点 | 功能描述 | 所属系统 | 优先级 |
| --- | --- | --- | --- | --- |
| 【服务商后台】商品 | 商品管理1. 商品添加、编辑设置分账金额 | 设置商品价格以及分账金额1. 页面位置：   1.  2.  3. 修改点   1. 当前分账金额删去“用户分佣金额”   2. 当前“服务商成本金额”改名为“服务商分账金额”，“商户分佣金额”改名为“商城分账金额”   3. “服务商分账金额”、“平台分账金额”可设置为0元，“商城分账金额”不可小于等于0元   4. 服务商分账金额+平台分账金额+商城分账金额=实际售价   5. 结算方式默认“固定成本”   6. 屏蔽结算方式选项栏 4. 当前分账金额删去“用户分佣金额” 5. 当前“服务商成本金额”改名为“服务商分账金额”，“商户分佣金额”改名为“商城分账金额” 6. “服务商分账金额”、“平台分账金额”可设置为0元，“商城分账金额”不可小于等于0元 7. 服务商分账金额+平台分账金额+商城分账金额=实际售价 8. 结算方式默认“固定成本” 9. 屏蔽结算方式选项栏 10. 适用对象：超管商户 11. 涉及API：   1. merchandise/1.0/srm-comm/addOrUpdate 12. merchandise/1.0/srm-comm/addOrUpdate | 服务商平台 |  |
| 价格方案1. 价格方案添加设置分账金额 | 1. 页面位置：   1.  2.  3. 修改点：   1. 将分销归属参数配置写死为“商户”   2. 将分销归属栏目配置屏蔽，用户不可编辑   3. 导入商品模板调整：模板从 aliyun 直接下载，[sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com](http://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com) 管理地址？     1.    4.    5. 将用户分销金额删除   6. 校验逻辑修改为：服务商分账金额+平台分账金额+商城分账金额=实际售卖金额   7. 服务商分账金额，平台分账金额可为0   8. 适用商户默认为所有商户   9. 屏蔽适用商户编辑栏目   1.  4. 将分销归属参数配置写死为“商户” 5. 将分销归属栏目配置屏蔽，用户不可编辑 6. 导入商品模板调整：模板从 aliyun 直接下载，[sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com](http://sk-dev-centermerchant.oss-cn-chengdu.aliyuncs.com) 管理地址？   1.  7.  8. 将用户分销金额删除 9. 校验逻辑修改为：服务商分账金额+平台分账金额+商城分账金额=实际售卖金额 10. 服务商分账金额，平台分账金额可为0 11. 适用商户默认为所有商户 12. 屏蔽适用商户编辑栏目 13. 适用对象：所有商户 14. 涉及API:   1. [API-添加价格方案-使用模板导入商品 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6362367)   2. ydjia-merchant-promote >> /add/info 15. [API-添加价格方案-使用模板导入商品 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6362367) 16. ydjia-merchant-promote >> /add/info |  |  |
| 价格方案商品明细1. 价格方案查询 | 1. 页面位置：   1.  2.  3. 修改点：   1. 屏蔽分销员分佣的对应列   2. 导出表对应删除分销员分佣的对应列 4. 屏蔽分销员分佣的对应列 5. 导出表对应删除分销员分佣的对应列 6. 适用对象：所有商户 7. 涉及API：   1. sp-platform/1.0/price-plan/list/price/commodity/for/page，后端无需修改，前端直接屏蔽该字段即可   2. ydjia-merchant-promote >> PricePlanCommodityInfoExportTaskProcessor,修改 excel 实体即可，导出逻辑可参考该文档：[API-价格方案商品明细导出](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6362421) 8. sp-platform/1.0/price-plan/list/price/commodity/for/page，后端无需修改，前端直接屏蔽该字段即可 9. ydjia-merchant-promote >> PricePlanCommodityInfoExportTaskProcessor,修改 excel 实体即可，导出逻辑可参考该文档：[API-价格方案商品明细导出](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6362421) |  |  |
| 价格记录1. 变更记录 | 1. 页面位置：   1.  2.  3. 修改点：   1. 屏蔽用户分佣金额项 4. 屏蔽用户分佣金额项 5. 适用对象：所有商户 |  |  |
| 【服务商后台】用 户 | 个人信息 | 1. 页面位置：   1.  2.  3. 修改点：   1. 屏蔽收款配置栏目 4. 屏蔽收款配置栏目 5. 适用对象：所有商户 |  |  |
| 个人信息编辑 | 1. 页面位置：   1.  2.  3. 修改点：   1. 新增法人身份证字段，非必填，兼容当前数据迁移的法人身份证图片 4. 新增法人身份证字段，非必填，兼容当前数据迁移的法人身份证图片 5. 适用对象：所有商户 6. 涉及API：   1. sp-platform >> /1.0/spAcc/update 请求体新增字段：List<String> corporateIdentityPics;     1. 示例：       1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }     2. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }     1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   2. 示例：     1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   3. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   4. /spAcc/getMerchantBaseInfo 返回提新增字段：List<String> corporateIdentityPics;     1. 示例       1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }     2. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }     1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   5. 示例     1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   6. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   1. 示例：     1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   2. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   1. 示例     1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   2. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 } 7. sp-platform >> /1.0/spAcc/update 请求体新增字段：List<String> corporateIdentityPics;   1. 示例：     1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   2. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] }   1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] } 8. 示例：   1. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] } 9. RDarktrue{ 	"merCode": "SPHYDEE", 	......     "corporateIdentityPics":["public/20231024/5f1dbec549134259a6367ab525543624.jpg","public/20231024/e60715aeba374539a8d4039672e526c4.png"] } 10. /spAcc/getMerchantBaseInfo 返回提新增字段：List<String> corporateIdentityPics;   1. 示例     1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   2. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 }   1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 } 11. 示例   1. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 } 12. RDarktrue{     "code": "10000",     "msg": "操作成功",     "data": {         "merCode": "SPHYDEE",         "merName": "海典超级管理员",         "corporateIdentityPics": [             "public/20231024/e60715aeba374539a8d4039672e526c4.png",             "public/20231024/5f1dbec549134259a6367ab525543624.jpg"         ]     },     "timestamp": 1698131900534 } |  |  |
| 【服务商后台】云仓 | 账单管理 | 1. 页面位置：   1.  2.  3. 修改点：   1. 屏蔽分销员分佣的对应列   2. 屏蔽分销员类型筛选条件   3. 导出表对应删除分销员分佣的对应列 4. 屏蔽分销员分佣的对应列 5. 屏蔽分销员类型筛选条件 6. 导出表对应删除分销员分佣的对应列 7. 涉及API：   1. business-order-web：/export/suplier/bills/action，只需要修改表：dict_order_export_column中的default_choose = 0即可   2. 发现有个API叫：商户后台云仓分账单导出，需要确认该接口是否需要修改，经确认，不需要修改 8. business-order-web：/export/suplier/bills/action，只需要修改表：dict_order_export_column中的default_choose = 0即可 9. 发现有个API叫：商户后台云仓分账单导出，需要确认该接口是否需要修改，经确认，不需要修改 |  |  |
| 【运营后台】客户中心 | 新增、编辑客户 | 1. 页面位置：   1.  2.  3. 修改点：   1. 屏蔽上图中的字段   2. ERP类型默认值为“H2”   3. 所属大区默认值为“西南大区” 4. 屏蔽上图中的字段 5. ERP类型默认值为“H2” 6. 所属大区默认值为“西南大区” 7. 涉及API修改：   1. 需要在低代码平台进行修改 8. 需要在低代码平台进行修改 |  |  |
| 【运营平台】设置 | 会员首页设置 | 1. 页面位置   1.  2.  3. 修改点   1. 前端屏蔽此“我的佣金”项目配置 4. 前端屏蔽此“我的佣金”项目配置 5. 涉及接口：   1. ydjia-merchant-manager：/1.0/homePage/detail 6. ydjia-merchant-manager：/1.0/homePage/detail |  |  |
| 【运营平台】商品 | 云仓服务 | 1. 页面位置：   1.  2.  3. 修改点：   1. 默认关闭云仓分销活动   2. 屏蔽云仓分销tab栏目选项 4. 默认关闭云仓分销活动 5. 屏蔽云仓分销tab栏目选项 |  |  |
| 【服务商平台】云仓 | 用户生成云仓商品订单后，服务商管理员可在云仓-账单管理中查看当前分账单状态 | 1.可正常查看云仓订单产生的分账订单金额明细、分账状态；2.商家入驻汇付后，配置汇付参数可正常按系统结算周期进行分账单结算；将现有海典汇付服务商参数替换为一心到家的汇付服务商参数配置汇付商家参数：【心云运营管理后台】-【支付管理】-【客户支付数据填报】-通道配置-新增汇付支付配置 | 服务商平台 |  |
| 【运营平台】支付 | 支付配置 | 汇付支付相关配置 | 运营平台 |  |
| 【云仓兑换订单分账】订单下单 | 用户使用微商城下单云仓兑换商品 | 1.用户在订单确认页点击“立即兑换”，需要透传当前兑换订单云仓商品的实际售价总额到分账服务预生成分账单并调用汇付接口进行手续费计算和余额支付接口的调用；2.汇付余额支付成功，则用户可正常支付并完成兑换商品下单；若失败，则提示“当前兑换订单商家需采购备货，请联系客服解决”； | 运营管理后台 |  |
| 【云仓兑换订单分账】汇付预充值帐户资金不足通知 | 当前商户汇付资金不足则需要通过企业微信消息通知到相关群 | 1.系统内置余额资金校验值（暂定1000），低于值则触发企业微信群提示；2.提示内容：汇付账户资金N，低于设定的风控值，请及时关注处理避免兑换商城用户下单阻塞；3.提示周期：1/首次低于预设值触发；2/每日早上8:30校验并触发； | 运营管理后台 |  |
| 【云仓兑换订单分账】订单分账 | 预生成云仓兑换订单分账单 | 1.预生成云仓兑换订单分账单，分账采用汇付余额支付接口逻辑进行分账； 2.余额支付出款方：商城账户； 3.余额支付分账方：服务商账户、平台账户； | 运营管理后台 |  |
| 【云仓兑换订单分账】汇付接口对接 | 云仓兑换订单分账场景下的汇付接口对接 | 1.余额支付：生成预结算单后进行余额支付接口调用； 2.余额支付查询：余额支付完成后查询余额支付状态及分账明细； 3.余额支付退款：用户订单退款或取消，需调用余额支付退款接口； 4.余额支付退款查询：余额退款后需通过退款查询状态更新分账单状态； 5.手续费试算：预结算单生成前需进行手续费试算； |  |  |
| 【云仓兑换订单分账】 | 平台型服务商不进行在线分账（京东慧采/会订货） | 用户选择平台型服务商商品进行下单，不需要在线分账（京东慧采、会订货为预充值扣款无需在线分账结算）1.用户选择京东慧采、会订货平台商品下单时，默认不采用汇付分账；2.京东慧采、会订货平台服务商的账单管理中，结算状态默认为“无需结算”； |  |  |


# 三、目标

## 3.1 本期目标

### 3.1.1 业务目标

1. 跑通现有海典支付对接的汇付分账流程（需替换现有海典汇付服务商参数为一心到家汇付服务商参数）；
2. 在心币兑换云仓商品订单场景上通过预充值余额支付的形式兼容汇付的订单分账流程；


### 3.1.2 技术目标

- 梳理支付架构


## 3.2 中长期目标

- 支持扩展多种分账渠道（如收钱吧）


# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
| 分账 | 订单产生后系统需要对订单的供货方，销售方，推广方等相关收益方按比例或约定金额进行收益拆分的过程称为分账；汇付这类支付服务公司可提供接口完成在线分账，即下单后通过汇付账户进行自动分账打款给相关方； |
| 汇付服务商 | 可理解为汇付的代理商家，本期我们将入驻为汇付服务商； |
| 汇付商户 | 汇付的商家账户，类比微信支付商户号，可通过关联服务商后由服务商进行对公分账，需要商家进件注册后才能使用 |
| 汇付用户 | 汇付的个人账户，可类比微信钱包，需要个人用户进件绑定银行卡注册后才能使用 |


## 4.2 流程图

### 4.2.1 系统流程全景图

1. 心币兑换订单流程梳理：
  1. true订单流程分析falseautotoptrue175124
2. true订单流程分析falseautotoptrue175124
3. 心币兑换订单是否需要分账逻辑：
  1. true心币兑换分账falseautotoptrue4192
4. true心币兑换分账falseautotoptrue4192


## 4.3 用例图

## 4.4 ER图

**1、ER模型设计**

**2、核心问题思考**

| 序号 | 问题描述（Q） | 问题解答 / 解决方案（A） |
| --- | --- | --- |
|  |  |  |


## 4.5 架构图

逻辑架构图

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

| 序号 | 表名称 | 表描述 | DML |
| --- | --- | --- | --- |
| 1 | t_pay_divide_account_main | 分账主表 | sqlRDarktrueCREATE TABLE `t_pay_divide_account_main` (   `divide_account_id` bigint NOT NULL COMMENT '分账id',   `platform_divide_account_detail_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台分账明细单号',   `platform_divide_account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台分账单号',   `business_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务订单号',   `platform_business_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台商户号',   `platform_pay_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '平台支付单号',   `channel_divide_account_detail_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道分账明细单号',   `channel_divide_account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道分账单号',   `pay_channel_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付通道编码',   `pay_channel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付通道名称',   `channel_business_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道商户号',   `channel_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '通道订单号',   `divide_account_state` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账单状态PROCESSING：分账中/FINISHED：分账完成',   `divide_account_receive_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账接收方类型',   `divide_account_receive_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账接收方账号',   `divide_account_amount` double(16,6) DEFAULT NULL COMMENT '分账金额',   `divide_account_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账原因描述',   `divide_account_result` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账结果PENDING：待分账/SUCCESS：分账成功/CLOSED：已关闭',   `divide_account_fail_cause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分账失败原因',   `divide_account_create_time` datetime DEFAULT NULL COMMENT '分账开始时间',   `divide_account_down_time` datetime DEFAULT NULL COMMENT '分账完成时间',   `business_id` bigint DEFAULT NULL COMMENT '业务机构编码',   `business_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最终修改时间',   `group_id` bigint DEFAULT NULL COMMENT '集团编码',   `company_id` bigint DEFAULT NULL COMMENT '企业编码',   `divide_object_type` varchar(30) DEFAULT NULL COMMENT '分钟对象类型(own：分给自己，other：分给其他人)',   `receive_channel_business_no` varchar(50) DEFAULT NULL COMMENT '分账接收方通道商户号',   PRIMARY KEY (`divide_account_id`) USING BTREE,   KEY `idx_platform_order_code` (`platform_pay_order_code`),   KEY `idx_business_order_code` (`business_order_code`),   KEY `idx_down_time` (`divide_account_down_time`) USING BTREE,   KEY `idx_account_receive` (`divide_account_receive_type`,`divide_account_receive_code`) USING BTREE,   KEY `idx_channel_no` (`channel_divide_account_detail_no`) USING BTREE,   KEY `idx_platform_account` (`platform_divide_account_no`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分账主表'; |
| 2 | t_divide_account_order | 分账订单表 | sqlRDarktrueCREATE TABLE `t_divide_account_order` (   `divide_account_id` bigint NOT NULL COMMENT '分账id',   `business_divide_account_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `business_pay_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `platform_pay_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `channel_divide_account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `divide_account_state` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `divide_account_callback_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `divide_account_receive_list` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `divide_account_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `can_thaw_last_fund` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `encrypt_str` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `callback_msg` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最终修改时间',   `is_callback` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `third_trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `channel_source` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   PRIMARY KEY (`divide_account_id`) USING BTREE,   UNIQUE KEY `uk_divide_account_order` (`business_divide_account_order_code`) USING BTREE COMMENT '逻辑主键',   KEY `idx_business_order` (`business_pay_order_code`) USING BTREE,   KEY `idx_is_call` (`is_callback`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分账订单表'; |
| 3 | t_divide_account_receiver | 分账接收者信息表 | sqlRDarktrueCREATE TABLE `t_divide_account_receiver` (   `receiver_id` bigint NOT NULL COMMENT 'id',   `receiver_appid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_sub_mchid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_sub_appid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `relation_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `custom_relation` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `receiver_state` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `platform_business_no` varchar(120) DEFAULT NULL COMMENT '平台商户号',   PRIMARY KEY (`receiver_id`) USING BTREE,   KEY `idx_receiver` (`receiver_account`,`receiver_appid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分账接收者信息表'; |
| 4 | t_divide_account_log | 分账日志表 | sqlRDarktrueCREATE TABLE `t_divide_account_log` (   `divide_account_log_id` bigint NOT NULL COMMENT 'id',   `business_divide_account_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `request_param` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `response_param` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `divide_account_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `request_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',   `response_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '响应时间',   `cost_time` int DEFAULT NULL COMMENT '请求耗时，单位ms',   `response_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,   PRIMARY KEY (`divide_account_log_id`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分账日志表'; |
| 5 | srm_bill | 分账单 | sqlRDarktrueCREATE TABLE `srm_bill` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',   `supplier_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供货商编码',   `supplier_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供货商名称',   `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售商编码',   `mer_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '销售商名字',   `status` int DEFAULT NULL COMMENT '账单状态：枚举值：0 待结算、1 已结算, 2，已退款不结算，3，结算异常',   `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'OMS订单编码',   `third_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '微商城订单编码',   `delivery_time` datetime DEFAULT NULL COMMENT '发货时间',   `account_receivable` decimal(10,2) DEFAULT NULL COMMENT '应结金额',   `actual_money` decimal(10,2) DEFAULT NULL COMMENT '实结金额',   `amount_difference` decimal(10,2) DEFAULT NULL COMMENT '金额差（展示废弃）',   `bill_time` datetime DEFAULT NULL COMMENT '结算时间',   `bill_source` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '账单来源',   `bill_type` tinyint(1) DEFAULT NULL COMMENT '分账类型 1:供应商分账 2服务商分账',   `ex_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '异常备注',   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `update_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',   `create_time` datetime NOT NULL COMMENT '创建时间',   `create_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',   `supplier_pay_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商支付号',   `mer_pay_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户支付号',   `business_pay_order_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付请求单号',   `partner_key` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '秘钥',   `channel_source` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付渠道来源',   `spread_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推广门店code',   `spread_store_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推广门店名称',   `brokerage_amount` decimal(10,2) DEFAULT NULL COMMENT '交易佣金',   `payment_charge` decimal(10,2) DEFAULT NULL COMMENT '手续费',   `transaction_payee` int DEFAULT '1' COMMENT '交易收款方 0-销售商 1-服务商 默认销售商',   `payment` decimal(10,2) DEFAULT NULL COMMENT '商品实付',   `st_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单门店编码',   `pay_type` int DEFAULT NULL COMMENT '支付类型200 汇付 ',   `pay_time` datetime DEFAULT NULL COMMENT '支付时间',   PRIMARY KEY (`id`) USING BTREE,   UNIQUE KEY `idx_orderNo` (`order_no`) USING BTREE,   KEY `idx_suplierCode_status` (`supplier_code`,`status`),   KEY `idx_thirdOrderNO` (`third_order_no`),   KEY `idx_createTime` (`create_time`) ) ENGINE=InnoDB AUTO_INCREMENT=1694552561656303619 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |
| 6 | srm_bill_incomion | 分账金额明细 | sqlRDarktrueCREATE TABLE `srm_bill_incomion` (   `id` bigint NOT NULL AUTO_INCREMENT,   `bill_id` bigint NOT NULL COMMENT '分帐单id',   `account_type` tinyint(1) DEFAULT NULL COMMENT '1.商户 2.供应商 3.平台',   `receive_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分账接收方类型(MERCHANT_ID：商户号 PERSONAL_OPENID：个人openid（由父商户APPID转换得到）PERSONAL_SUB_OPENID:个人sub_openid（由子商户APPID转换得到）)',   `receive_mer_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收账人商户code',   `receive_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收账人名称',   `receive_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分账接收方账号',   `bill_amount` decimal(10,2) NOT NULL COMMENT '应结算金额',   `actual_bill_amount` decimal(10,2) DEFAULT NULL COMMENT '实际分账金额',   `bill_desc` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分账原因描述',   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `plan_bill_amount` decimal(10,2) DEFAULT NULL COMMENT '计划分账金额',   `brokerage_amount` decimal(10,2) DEFAULT NULL COMMENT '交易佣金',   `payment_charge` decimal(10,2) DEFAULT NULL COMMENT '手续费',   `receive_flag` tinyint(1) DEFAULT '0' COMMENT '收款人标识 1为收款人 0为分账人',   `replace_receive_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店替代收款人账号',   PRIMARY KEY (`id`),   KEY `idx_billId` (`bill_id`) ) ENGINE=InnoDB AUTO_INCREMENT=5313 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分账信息表'; |
| 7 | srm_bill_goods_info | 分账商品明细 | sqlRDarktrueCREATE TABLE `srm_bill_goods_info` (   `id` bigint NOT NULL,   `bill_id` bigint DEFAULT NULL COMMENT '账单id',   `sku_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供货商商品编码',   `goods_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商商品编码',   `settlement_method` int DEFAULT NULL COMMENT '结算方式',   `settlement_price` decimal(10,2) DEFAULT NULL COMMENT '结算单价',   `number` int DEFAULT NULL COMMENT '数量',   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `update_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新者',   `create_time` datetime DEFAULT NULL COMMENT '创建时间',   `create_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建者',   `price` decimal(10,2) DEFAULT NULL COMMENT '商品单价',   `platform_settlement_price` decimal(10,2) DEFAULT '0.00' COMMENT '平台分账单价',   `mer_settlement_price` decimal(10,2) DEFAULT NULL COMMENT '商户分账单价',   `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品名称',   `third_detail_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台订单商品id',   `status` tinyint(1) DEFAULT '0' COMMENT '商品状态 0:正常 1:取消',   `payment` decimal(10,2) DEFAULT NULL COMMENT '商品实付',   `return_brokerage_amount` decimal(10,2) DEFAULT NULL COMMENT '退还交易佣金',   `return_payment_charge` decimal(10,2) DEFAULT NULL COMMENT '退还手续费',   `brokerage_amount` decimal(10,2) DEFAULT NULL COMMENT '交易佣金',   `payment_charge` decimal(10,2) DEFAULT NULL COMMENT '手续费',   `return_payment` decimal(10,2) DEFAULT NULL COMMENT '商品实付退款',   `user_settlement_price` decimal(10,2) DEFAULT NULL COMMENT '分销用户分账单价',   `price_id` bigint DEFAULT NULL COMMENT '价格组id',   `distribute_settlement` int DEFAULT NULL COMMENT '分销金额结算方，1-服务商，2-销售商，3-海典',   `price_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '价格方案名称',   `refund_time` datetime DEFAULT NULL COMMENT '退款时间',   `refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '退款单号',   PRIMARY KEY (`id`) USING BTREE,   KEY `idx_bill_id` (`bill_id`),   KEY `idx_create_time` (`create_time`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |
| 8 | srm_bill_log | 日志表 | sqlRDarktrueCREATE TABLE `srm_bill_log` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',   `type` int DEFAULT NULL COMMENT '类型 1:新增分账 2新增分账 3进行分账 4分账异常 5分账成功 6分账失败，重新分账 9 取消分账',   `bill_id` bigint DEFAULT NULL COMMENT '分账id',   `oms_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'oms订单编号',   `third_platfrom_no` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台订单号',   `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   PRIMARY KEY (`id`),   KEY `idx_billId` (`bill_id`),   KEY `idx_thirdOrderNo` (`third_platfrom_no`) ) ENGINE=InnoDB AUTO_INCREMENT=4963 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分账日志表'; |
| 9 | srm_task_bill | 分账任务表 | sqlRDarktrueCREATE TABLE `srm_task_bill` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',   `oms_order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'oms订单号',   `bill_time` datetime DEFAULT NULL COMMENT '下账时间',   `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '执行状态  0:待执行 1:执行成功； 2:取消分账 3:分账异常 4:分账中',   `ex_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败原因',   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   `bill_count` int NOT NULL DEFAULT '1' COMMENT '分账次数',   `source_bill_time` datetime DEFAULT NULL COMMENT '原下账时间',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_orderNo` (`oms_order_no`),   KEY `idx_billTime` (`bill_time`) ) ENGINE=InnoDB AUTO_INCREMENT=484 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分帐定时任务表'; |


## 5.3 接口设计

### 5.3.1 相关接口

1. **ydjia-merchant-customer >> /order/addOrder**
2. **/order-info**


## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**研发工时：8pd，自测工时：2pd，联调工时：2pd；**

**研发时间：2023年10月23日-2023年11月01日；**

**测试时间：2023年11月02日-2023年11月03日（联调+自测）；**

| 功能模块 | 功能项 | 需求点 | 服务 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| API： |
| 【服务商平台】 | 【商品】商品添加、编辑设置分账金额 | 1. 当前分账金额删去“用户分佣金额” 2. 当前“服务商成本金额”改名为“服务商分账金额”，“商户分佣金额”改名为“商城分账金额” 3. “服务商分账金额”、“平台分账金额”可设置为0元，“商城分账金额”不可小于等于0元 4. 服务商分账金额+平台分账金额+商城分账金额=实际售价 5. 结算方式默认“固定成本” 6. 屏蔽结算方式选项栏 | 商品服务 |  | 1 |  |  |  |
| 【商品】价格方案 | 1. 将分销归属参数配置写死为“商户” 2. 将分销归属栏目配置屏蔽，用户不可编辑 3. 导入商品模板调整 4. 将用户分销金额删除 5. 校验逻辑修改为：服务商分账金额+平台分账金额+商城分账金额=实际售卖金额 6. 服务商分账金额，平台分账金额可为0 7. 适用商户默认为所有商户 8. 屏蔽适用商户编辑栏目 |  | 2 |  |  |
| 【商品】价格方案查询 | 1. 屏蔽分销员分佣的对应列 2. 导出表对应删除分销员分佣的对应列 |  | 0.5 |  |  |
| 【商品】价格记录 | 1. 屏蔽用户分佣金额项 |  | 0.5 |  |  |
| 【用户】个人信息 | 1. 屏蔽收款配置栏目 2. 新增法人身份证字段，非必填，兼容当前数据迁移的法人身份证图片 |  | 1 |  |  |
| 【云仓】云仓服务 | 1. 默认关闭云仓分销活动 2. 屏蔽云仓分销tab栏目选项 |  | 1 |  |  |
| 【云仓】云仓订单 | 将现有海典汇付服务商参数替换为一心到家的汇付服务商参数跑通现有汇付分账流程 |  | 2 |  |  |
|  |  |  |  |  |  |  |  |  |
| 【云仓兑换订单分账】 | 用户使用微商城下单云仓兑换商品 | 汇付支付成功，生成结账分账单 | 支付服务 |  | 2 |  |  |  |
| 汇付余额查询接口 | 汇付余额查询接口 |  | 1 |  |  |
| 分账的流程 | 更改判断需要分账的逻辑 |  | 1 |  |  |
| 汇付接口对接[余额支付-服务商平台 (huifu.com)](https://paas.huifu.com/partners/api/#/yuer/api_acctpayzf) | 1.余额支付：生成预结算单后进行余额支付接口调用； 2.余额支付查询：余额支付完成后查询余额支付状态及分账明细； 3.余额支付退款：用户订单退款或取消，需调用余额支付退款接口； 4.[余额支付退款查询](https://paas.huifu.com/partners/api/#/yuer/api_acctpaytkcx?id=%e4%bd%99%e9%a2%9d%e6%94%af%e4%bb%98%e9%80%80%e6%ac%be%e6%9f%a5%e8%af%a2)：余额退款后需通过退款查询状态更新分账单状态； 5.手续费试算：预结算单生成前需进行手续费试算； |  | 1 |  | 1.余额支付：生成预结算单后进行余额支付接口调用； 2.余额支付查询：余额支付完成后查询余额支付状态及分账明细；3.手续费试算：预结算单生成前需进行手续费试算；@汪骁跟进 |
| O2O订单工作 | 支持O2O数据迁移测试，bug修改 |  |  | 2-3 |  |  |
| **UI：** |
| 【运营管理后台】 | - 新增服务商   - 相关字段屏蔽 - 相关字段屏蔽 |  |  |  | 0.3 |  |  |  |
| 【服务商平台】 | - 商品编辑：   1. 分账金额校验   2. 页面字段屏蔽 - 分账金额校验 - 页面字段屏蔽 |  |  |  | 0.2 |  |  |
| - 添加价格方案   1. 相关字段屏蔽   2. 导入商品模板调整 - 相关字段屏蔽 - 导入商品模板调整 |  |  |  | 0.5 |  |  |
| - 价格方案商品明细   - 相关字段屏蔽 - 相关字段屏蔽 |  |  |  | 0.3 |  |  |
| - 账单管理   - 相关字段屏蔽 - 相关字段屏蔽 |  |  |  | 0.2 |  |  |
| - 个人信息   1. 新增法人身份证字段   2. .屏蔽收款配置栏目 - 新增法人身份证字段 - .屏蔽收款配置栏目 |  |  |  | 0.5 |  |  |
| 【运营平台】 | - 云仓服务   1. 默认关闭云仓分销活动   2. 屏蔽云仓分销tab栏目选项 - 默认关闭云仓分销活动 - 屏蔽云仓分销tab栏目选项 |  |  |  | 0.5 |  |  |
| - 支付配置   - 当前门店选择增加门店按组织结构搜索逻辑 - 当前门店选择增加门店按组织结构搜索逻辑 |  |  |  | 1 |  |  |
| - 会员设置   - 屏蔽“我的佣金”项目配置 - 屏蔽“我的佣金”项目配置 |  |  |  | 0.3 |  |  |
|  |  |  |  |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等