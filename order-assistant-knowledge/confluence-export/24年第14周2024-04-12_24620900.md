# 24年第14周2024-04-12

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 里程碑 | 当前进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构-DDD项目 |  | 4月1日完成子域划分4月11日完成O2O服务列表划分 | 下周确定对接模型 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  | 4月1日开始方案设计4月11日方案内部评审 | 技术方案设计 | .net代码黑盒,无法多人同时开发. 按平台迁移对接代码 |
| 3 | 优雅发布升级-mq |  | 4月12日完成待办列表,推动全部门升级 | 已完成,待上线 | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  | 4月12日完成待办列表,推动全部门升级 |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | 每周- [上线计划](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq) |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | **本周总工时：5**1. .net重构   1. 技术方案设计   2. .net接口梳理   3. 项目demo搭建 2. 技术方案设计 3. .net接口梳理 4. 项目demo搭建 5. B2C订单相关问题支持 | **㊀计划工作****㊁实际完成**1. .net重构-下周四左右技术方案评审 **㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 |  | **本周总工时：4pd**1. v1.6.2.订单运维工具 2. 下账时机bug修改 3. 线上运维 | **㊀计划工作**1. v1.6.2.订单运维工具 2. 下账时机bug修改 3. 线上运维 **㊁实际完成**1. v1.6.2.订单运维工具 2. 下账时机bug修改 3. 线上运维 **㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 |  | **本周总工时：6d** 1. 线下单需求 4d   1. 开发完成 3d   2. 联调完成(同信息中心) 0.5d   3. 部署打包 2. 开发完成 3d 3. 联调完成(同信息中心) 0.5d 4. 部署打包 5. 优雅发布相关   1. 优雅发布接入计划文档   2. rocketmq优雅发布已上线 6. 优雅发布接入计划文档 7. rocketmq优雅发布已上线 8. 其他   1. 订单中台逆向流程事件风暴会议   2. 订单路由CR会议   3. .net重构方案会议   4. es超时配置已上线   5. 配合应用架构组生成token   6. businesses-gateway问题跟进，解决(0.5d)   7. 其他(面试) 9. 订单中台逆向流程事件风暴会议 10. 订单路由CR会议 11. .net重构方案会议 12. es超时配置已上线 13. 配合应用架构组生成token 14. businesses-gateway问题跟进，解决(0.5d) 15. 其他(面试)     **** | **㊀计划工作****㊁实际完成****㊂遗留问题**  **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 4 |  | **本周总工时：****** 1. xxl-job 文档整理  2. 订单路由提测 3. 支付宝订单对接 4. 线上问题处理 4.1 j007、j010 门店医保订单问题 4.2 B2C门店仓下账问题 | **㊀计划工作** 1. xxl-job 文档整理  2. 订单路由提测 3. 支付宝订单对接**㊁实际完成******1. xxl-job 文档整理 文档整理完成 2. 订单路由提测 已提测 3. 支付宝订单对接 开发20%**㊂遗留问题****1. 订单路由 对接商品****2. 订单路由 对接海典****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 5 |  | **本周总工时：5pd**1.订单路由场景/策略联调2.通过Excel导入指定商品3.智能选择配送4.订单路由bug修复5.内购商城商品限购6.CodeReview，代码优化 | **㊀计划工作****㊁实际完成**1.订单路由场景/策略联调、Excel导入指定商品2.智能选择配送**㊂遗留问题**1.内购商城商品限购，需要商品提供接口获取限购规则2.CodeReview，代码优化**㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **** |  |
| 6 |  | **本周总工时：**1. 医保支付编码调整与上线 2. 生产环境B2C待审方订单刷数到待审核 3. 订单路由研发与提测 4. 支付宝平台订单接入 5. 门店日常运维 | **㊀计划工作**1. 订单路由研发与提测 2. 支付宝平台订单接入 **㊁实际完成**1. 医保支付编码调整与上线 2. 生产环境B2C待审方订单刷数到待审核 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 7 |  | **本周总工时：**1.订单运维设计方案编写2.订单运维代码编写3.下账时机BUG修改 上线4.线上BUG（云仓下单名字过长） | **㊀计划工作**1.订单运维设计方案编写2.订单运维代码编写3.下账时机BUG修改 上线4.线上BUG（云仓下单名字过长）**㊁实际完成****1.订单运维设计方案编写****2.线上BUG（云仓下单名字过长）****㊂遗留问题****1.订单运维代码编写****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1.v1.6.2.订单运维工具**㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 8 |  | **本周总工时：6day**1. 诺和0元单流程 帮助测试以及bug 修改。 2day 2. .NET MQ 消费限制、美团消费优化调整。1.5 day 3. 自配送骑手位置上传 2day 4. 产线问题 美团医保金额错乱 0.5 day | **㊀计划工作****㊁实际完成****㊂遗留问题****㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关****㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx  **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  | [Jackson的多态反序列化 - 后端研发部 - 一心数科数字化产研中心-wiki (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24613490) |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 | 无 | 1.权限校验2.记录日志3.性能优化 | 待办 |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

[交易生产值班文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADg1qOaeEheQ0yz65Hm5M?scode=AOsAFQcYAAcByWy1f0AboAOAYLADg&tab=BB08J2)

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

 1.绩效填写 下周二之前 [绩效文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgCzO9VcGeRHSRMAWb8r?scode=AOsAFQcYAAcmdwBqSuAboAOAYLADg&tab=wxu9jk)

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 | Q1绩效填写 [绩效文档](https://doc.weixin.qq.com/sheet/e3_AboAOAYLADgCzO9VcGeRHSRMAWb8r?scode=AOsAFQcYAAcmdwBqSuAboAOAYLADg&tab=wxu9jk) |  |  |
| 2 | Q1绩效填写 |  |  |
| 3 | Q1绩效填写 | Q1入职不足月,暂不填写 |  |
| 4 | Q1绩效填写 |  |  |
| 5 | Q1绩效填写 |  |  |
| 6 | Q1绩效填写 |  |  |
| 7 | Q1绩效填写 |  |  |
| 8 | Q1绩效填写 |  |  |
| 9 | Q1绩效填写 |  |  |