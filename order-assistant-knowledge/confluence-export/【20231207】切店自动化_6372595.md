# 【20231207】切店自动化

# 一、背景

## 1.1 业务背景

目前切店主要靠人工手动操作，但是美团平台的切换过程较为繁琐，并且店铺迁移过来之后，还需要对店铺进行一系列的初始化过程，全靠人工手动操作，既费时，又不可避免的会出现纰漏，因此，我们需要开发运维工具，可以将切店的整个流程自动化，提高效率。

## 1.2 痛点分析

人工操作费时费力，而且容易出现纰漏

## 1.3 系统现状

切店过程依靠人工手动操作

# 二、需求分析

## 2.1 业务流程

**切店自动化 (axshare.com)**

# 三、目标

**3.1 本期目标**

- 美团店铺：旧系统店铺的解绑与新店铺的授权
- 店铺自动化设置：能将同步过来的门店进行初始化配置，并对切换过程中没同步的订单进行补单操作


# 四、整体设计

**4.1 统一语言定义**

| **名称** | **说明** |
| --- | --- |
|  |  |


**4.2 流程图**

**4.2.1****系统流程全景图**

**1、店铺授权和解绑**

true店铺授权流程图falseautotoptrue7613

**2、店铺配置自动化**

**true店铺自动化配置流程falseautotoptrue9315**

# 五、详细设计

## 5.1 详细模块设计

## 5.2 存储数据库设计

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | ds_online_store_auth_record | 店铺授权|解绑记录表 | RDarkCREATE TABLE `ds_online_store_auth_record` (   `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',   `platform_code` varchar(16) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编码，27美团,24饿百',   `service_mode` varchar(6) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务模式:O2O;B2C',   `platform_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网店编码',   `online_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店编码',   `online_store_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店名称',   `auth_status` tinyint DEFAULT '1' COMMENT '授权处理状态 1待处理 2处理中 3处理成功 4处理失败',   `relieve_status` tinyint DEFAULT '1' COMMENT '解绑处理状态 1待处理 2处理中 3处理成功 4处理失败',   `change_status` tinyint DEFAULT '1' COMMENT '更换门店处理状态 1待处理 2处理中 3处理成功 4处理失败',   `error_msg` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '处理失败描述',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',   `creater` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',   `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',   `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',   `auth_time` datetime DEFAULT NULL COMMENT '新应用授权时间',   PRIMARY KEY (`id`),   KEY `idx_online_store_code` (`online_store_code`) USING BTREE,   KEY `idx_create_time` (`create_time`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=8297 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺授权|解绑记录表	'; |
| 2 | ds_online_store_auto_config_step | 店铺自动配置流程表 | RDarkCREATE TABLE `ds_online_store_auto_config_step` (   `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',   `online_store_id` bigint NOT NULL COMMENT 'ds_online_store主键',   `online_store_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺编码',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',   `creater` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',   `organization_step` json DEFAULT NULL COMMENT '机构绑定',   `store_step` json DEFAULT NULL COMMENT '店铺设置',   `order_step` json DEFAULT NULL COMMENT '订单处理设置',   `sound_step` json DEFAULT NULL COMMENT '声音设置',   `self_delivery_step` json DEFAULT NULL COMMENT '自配送设置',   `bill_step` json DEFAULT NULL COMMENT '下账设置',   `order_pull_step` json DEFAULT NULL COMMENT '拉单',   `merchandise_notify_step` json DEFAULT NULL COMMENT '通知商品中台',   `status` tinyint DEFAULT '1' COMMENT '流程状态 1 待处理 2 处理中 3 处理成功 4 处理失败',   `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_online_store_id` (`online_store_id`) USING BTREE,   KEY `idx_create_time` (`create_time`) USING BTREE,   KEY `idx_online_store_code` (`online_store_code`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺自动配置流程表	'; |
| 3 | ds_online_store_pull_order_record | 店铺拉单记录表 | CREATE TABLE `ds_online_store_pull_order_record` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `online_store_id` bigint NOT NULL COMMENT 'ds_online_store主键',   `online_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺编码',   `third_order_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方单号',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',   `creater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',   `status` tinyint DEFAULT '1' COMMENT '状态：1 无需补单 2 补单成功 3 补单失败',   `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',   `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_online_store_id` (`online_store_id`) USING BTREE,   KEY `idx_create_time` (`create_time`) USING BTREE,   KEY `idx_online_store_code` (`online_store_code`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺自动配置流程表	'; |


## 5.3 接口设计

### 5.3.1 前端交互接口

#### 1 API-店铺授权记录分页列表

1. url**:**/1.0/ds/store/auto/auth/record
2. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| startDate | String | 是 | 操作开始时间，示例值：2022-12-11 |
| endDate | String | 是 | 操作结束时间，示例值：2022-12-11 |
| storeCode | String | 是 | 门店编码 |
| creater | String | 是 | 操作人 |
| authStatus | Integer | 是 | 授权状态 1待处理 2处理中 3处理成功 4处理失败 |
| relieveStatus | Integer | 是 | 解绑状态 1待处理 2处理中 3处理成功 4处理失败 |
| changeStatus | Integer | 是 | 更换门店状态 1待处理 2处理中 3处理成功 4处理失败 |
  2. 示例{
  "authStatus": 0,
  "changeStatus": 0,
  "currentPage": 0,
  "endDate": "string",
  "pageSize": 0,
  "relieveStatus": 0,
  "startDate": "string",
  "storeCode": "string",
  "creater": "string"
}
3. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| startDate | String | 是 | 操作开始时间，示例值：2022-12-11 |
| endDate | String | 是 | 操作结束时间，示例值：2022-12-11 |
| storeCode | String | 是 | 门店编码 |
| creater | String | 是 | 操作人 |
| authStatus | Integer | 是 | 授权状态 1待处理 2处理中 3处理成功 4处理失败 |
| relieveStatus | Integer | 是 | 解绑状态 1待处理 2处理中 3处理成功 4处理失败 |
| changeStatus | Integer | 是 | 更换门店状态 1待处理 2处理中 3处理成功 4处理失败 |
4. 示例{
  "authStatus": 0,
  "changeStatus": 0,
  "currentPage": 0,
  "endDate": "string",
  "pageSize": 0,
  "relieveStatus": 0,
  "startDate": "string",
  "storeCode": "string",
  "creater": "string"
}
5. 响应体：
  1. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| platformCode | String | 平台code | ds_online_store_auth_record >> platform_code |
| platformName | String | 平台名称 |  |
| serviceMode | String | 服务模式：o2o b2c | ds_online_store_auth_record >> service_mode |
| onlineStoreCode | String | 店铺编码 | ds_online_store_auth_record >> online_store_code |
| onlineStoreName | String | 店铺名称 | ds_online_store_auth_record >> online_store_name |
| platformStoreCode | String | 平台店铺编码 | ds_online_store_auth_record >> platform_store_code |
| creater | String | 创建人 | ds_online_store_auth_record >> creater |
| createTime | String | 创建时间，示例值：2023-12-11 12:00:00 | ds_online_store_auth_record >> create_time |
| ``` authTime ``` | String | 新应用授权时间，示例值：2023-12-11 12:00:00 | ds_online_store_auth_record >> auth_time |
| authStatus | Integer | ``` 授权状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> auth_status |
| relieveStatus | Integer | ``` 解绑状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> relieve_status |
| changeStatus | Integer | ``` 更换门店状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> change_status |
| errorMsg | String | ``` 失败信息 ``` | ds_online_store_auth_record >> error_msg |
  2. 示例值{
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "authStatus": 0,
        "changeStatus": 0,
        "createTime": "2023-12-11T02:31:06.359Z",
        "creater": "string",
        "errorMsg": "string",
        "id": 0,
        "onlineStoreCode": "string",
        "onlineStoreName": "string",
        "platformCode": "string",
        "platformName": "string",
        "platformStoreCode": "string",
        "relieveStatus": 0,
        "serviceMode": "string"
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}
6. | 字段名 | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- |
| platformCode | String | 平台code | ds_online_store_auth_record >> platform_code |
| platformName | String | 平台名称 |  |
| serviceMode | String | 服务模式：o2o b2c | ds_online_store_auth_record >> service_mode |
| onlineStoreCode | String | 店铺编码 | ds_online_store_auth_record >> online_store_code |
| onlineStoreName | String | 店铺名称 | ds_online_store_auth_record >> online_store_name |
| platformStoreCode | String | 平台店铺编码 | ds_online_store_auth_record >> platform_store_code |
| creater | String | 创建人 | ds_online_store_auth_record >> creater |
| createTime | String | 创建时间，示例值：2023-12-11 12:00:00 | ds_online_store_auth_record >> create_time |
| ``` authTime ``` | String | 新应用授权时间，示例值：2023-12-11 12:00:00 | ds_online_store_auth_record >> auth_time |
| authStatus | Integer | ``` 授权状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> auth_status |
| relieveStatus | Integer | ``` 解绑状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> relieve_status |
| changeStatus | Integer | ``` 更换门店状态 1待处理 2处理中 3处理成功 4处理失败 ``` | ds_online_store_auth_record >> change_status |
| errorMsg | String | ``` 失败信息 ``` | ds_online_store_auth_record >> error_msg |
7. 示例值{
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "authStatus": 0,
        "changeStatus": 0,
        "createTime": "2023-12-11T02:31:06.359Z",
        "creater": "string",
        "errorMsg": "string",
        "id": 0,
        "onlineStoreCode": "string",
        "onlineStoreName": "string",
        "platformCode": "string",
        "platformName": "string",
        "platformStoreCode": "string",
        "relieveStatus": 0,
        "serviceMode": "string"
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}


#### 2 API-店铺授权Excel上传接口

1. url:/1.0/ds/store/auto/auth/by_excel
2. 请求体：
  1. curl -X POST "http://localhost:12600/1.0/ds/store/auto/auth/by_excel?platformCode=XXXX&serviceMode=XXX" -H "accept: */*" -H "Content-Type: multipart/form-data" -F "file=@XXXX;type=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
3. curl -X POST "http://localhost:12600/1.0/ds/store/auto/auth/by_excel?platformCode=XXXX&serviceMode=XXX" -H "accept: */*" -H "Content-Type: multipart/form-data" -F "file=@XXXX;type=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"


#### 3 API-店铺自动配置增加Excel上传

1. url:/1.0/ds/store/sync/by_excel
2. 请求体：
  1. curl -X POST "{host}/1.0/ds/store/sync/by_excel?onlineClientId[=](http://localhost:12600/1.0/ds/store/auto/auth/by_excel?platformCode=)XXXX" -H "accept: */*" -H "Content-Type: multipart/form-data" -F "file=@XXXX;type=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
3. curl -X POST "{host}/1.0/ds/store/sync/by_excel?onlineClientId[=](http://localhost:12600/1.0/ds/store/auto/auth/by_excel?platformCode=)XXXX" -H "accept: */*" -H "Content-Type: multipart/form-data" -F "file=@XXXX;type=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"


#### 4 API-店铺管理列表-增加自动配置流程筛选、回显

1. api url:1.0/ds/baseinfo/queryDsOnlineStore
2. 请求体新增字段：
  1. storeAutoConfigStatus，类型：int，描述：店铺自动配置状态  1 待处理 2 处理中 3 处理成功 4 处理失败
3. storeAutoConfigStatus，类型：int，描述：店铺自动配置状态  1 待处理 2 处理中 3 处理成功 4 处理失败
4. 响应体增加字段：
  1. | 字段名称 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| storeAutoConfigInfo |  | Object | 门店自动配置信息 |  |
|  | id | Integer | 记录表主键id | ds_online_store_auto_config_step >> id |
|  |  |  |  |  |
|  | onlineStoreCode | String | 门店code | ds_online_store_auto_config_step >> online_store_code |
|  | onlineStoreName | String | 门店名称 | ds_online_store_auto_config_step >> online_store_name |
|  | platformCode | String | 平台编码 | ds_online_store_auto_config_step >> platform_code |
|  | processTime | String | 处理时间，示例值：2023-12-11 12:00:00 | ds_online_store_auto_config_step >> create_time |
|  | processStatus | Integer | 处理状态 1 待处理 2 处理中 3 处理成功 4 处理失败 | ds_online_store_auto_config_step >> status |
|  | stepList | List<StoreAutoConfigStep> | 流程明细 | ds_online_store_auto_config_step |
| StoreAutoConfigStep |
| stepName |  | String | 步骤名 |  |
| status |  | Boolean | 处理状态 1 待处理 2 处理中 3 处理成功 4 处理失败 |  |
| processTime |  | String | 处理时间，示例值：2023-12-11 12:00:00 |  |
| handlerName |  | String | 处理器名 |  |
| repair |  | Boolean | 是否支持重新运行,为true时支持重新运行，null | false都不支持 |  |
| remark |  | String | 备注 |  |
  2. 响应体示例：
    1. {
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "accessType": 0,
        "address": "string",
        "city": "string",
        "contactPhone": "string",
		......
		......
		......
        "storeAutoConfigInfo": {
          "id": 0,
          "onlineStoreCode": "string",
          "onlineStoreName": "string",
          "platformCode": "string",
          "processStatus": 0,
          "processTime": "string",
          "stepList": [
            {
              "createTime": "string",
              "finishTime": "string",
              "remark": "string",
              "status": 0,
			  "handlerName": "string",
			  "repair": null,
              "stepName": "string"
            }
          ]
        }
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}
  3. {
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "accessType": 0,
        "address": "string",
        "city": "string",
        "contactPhone": "string",
		......
		......
		......
        "storeAutoConfigInfo": {
          "id": 0,
          "onlineStoreCode": "string",
          "onlineStoreName": "string",
          "platformCode": "string",
          "processStatus": 0,
          "processTime": "string",
          "stepList": [
            {
              "createTime": "string",
              "finishTime": "string",
              "remark": "string",
              "status": 0,
			  "handlerName": "string",
			  "repair": null,
              "stepName": "string"
            }
          ]
        }
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}
  1. {
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "accessType": 0,
        "address": "string",
        "city": "string",
        "contactPhone": "string",
		......
		......
		......
        "storeAutoConfigInfo": {
          "id": 0,
          "onlineStoreCode": "string",
          "onlineStoreName": "string",
          "platformCode": "string",
          "processStatus": 0,
          "processTime": "string",
          "stepList": [
            {
              "createTime": "string",
              "finishTime": "string",
              "remark": "string",
              "status": 0,
			  "handlerName": "string",
			  "repair": null,
              "stepName": "string"
            }
          ]
        }
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}
5. | 字段名称 |  | 字段类型 | 字段描述 | 数据源 |
| --- | --- | --- | --- | --- |
| storeAutoConfigInfo |  | Object | 门店自动配置信息 |  |
|  | id | Integer | 记录表主键id | ds_online_store_auto_config_step >> id |
|  |  |  |  |  |
|  | onlineStoreCode | String | 门店code | ds_online_store_auto_config_step >> online_store_code |
|  | onlineStoreName | String | 门店名称 | ds_online_store_auto_config_step >> online_store_name |
|  | platformCode | String | 平台编码 | ds_online_store_auto_config_step >> platform_code |
|  | processTime | String | 处理时间，示例值：2023-12-11 12:00:00 | ds_online_store_auto_config_step >> create_time |
|  | processStatus | Integer | 处理状态 1 待处理 2 处理中 3 处理成功 4 处理失败 | ds_online_store_auto_config_step >> status |
|  | stepList | List<StoreAutoConfigStep> | 流程明细 | ds_online_store_auto_config_step |
| StoreAutoConfigStep |
| stepName |  | String | 步骤名 |  |
| status |  | Boolean | 处理状态 1 待处理 2 处理中 3 处理成功 4 处理失败 |  |
| processTime |  | String | 处理时间，示例值：2023-12-11 12:00:00 |  |
| handlerName |  | String | 处理器名 |  |
| repair |  | Boolean | 是否支持重新运行,为true时支持重新运行，null | false都不支持 |  |
| remark |  | String | 备注 |  |
6. 响应体示例：
  1. {
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "accessType": 0,
        "address": "string",
        "city": "string",
        "contactPhone": "string",
		......
		......
		......
        "storeAutoConfigInfo": {
          "id": 0,
          "onlineStoreCode": "string",
          "onlineStoreName": "string",
          "platformCode": "string",
          "processStatus": 0,
          "processTime": "string",
          "stepList": [
            {
              "createTime": "string",
              "finishTime": "string",
              "remark": "string",
              "status": 0,
			  "handlerName": "string",
			  "repair": null,
              "stepName": "string"
            }
          ]
        }
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}
7. {
  "code": "string",
  "data": {
    "currentPage": 0,
    "data": [
      {
        "accessType": 0,
        "address": "string",
        "city": "string",
        "contactPhone": "string",
		......
		......
		......
        "storeAutoConfigInfo": {
          "id": 0,
          "onlineStoreCode": "string",
          "onlineStoreName": "string",
          "platformCode": "string",
          "processStatus": 0,
          "processTime": "string",
          "stepList": [
            {
              "createTime": "string",
              "finishTime": "string",
              "remark": "string",
              "status": 0,
			  "handlerName": "string",
			  "repair": null,
              "stepName": "string"
            }
          ]
        }
      }
    ],
    "pageSize": 0,
    "totalCount": 0,
    "totalPage": 0
  },
  "msg": "string",
  "timestamp": 0
}


#### 5 API-重新运行某项配置

1. api-url：1.0/ds/store/config/repair
2. 请求体：
  1. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` handlerName ``` | String | 否 | 处理器名称 |
| ``` configStepId ``` | String | 否 | 自动化配置步骤 |
  2. 请求体示例：
    1. {
  "handlerName": "string",
  "configStepId": "string"
}
  3. {
  "handlerName": "string",
  "configStepId": "string"
}
  1. {
  "handlerName": "string",
  "configStepId": "string"
}
3. | 字段 | 类型 | 允许为null | 字段描述 |
| --- | --- | --- | --- |
| ``` handlerName ``` | String | 否 | 处理器名称 |
| ``` configStepId ``` | String | 否 | 自动化配置步骤 |
4. 请求体示例：
  1. {
  "handlerName": "string",
  "configStepId": "string"
}
5. {
  "handlerName": "string",
  "configStepId": "string"
}


### 5.3.2 后台服务接口

**美团开发者账号:*********** 无密码,需要凯哥手机验证码**

**美团SDK:**

**饿了么开发者账号:*********** 密码:hxyxt2018**

**饿了么SDK:**

**京东到家开发者账号:yixintangjdcs 密码:yxt123456**

**京东到家SDK:**

**1 美团店铺授权、解绑**

**接口文档地址: https://open-shangou.meituan.com/home/<USER>/481**

**请求地址:https://waimaiopen.meituan.com/api/v1/ecommerce/poi/bind/app/by/account**

**系统级参数:**

| API接口 | 接口描述 | 接口描述 |
| --- | --- | --- |
| timestamp | long | 调用接口时的时间戳，即当前时间戳（当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix - timestamp），注意传输时间戳与当前北京时间前后相差不能超过10分钟 |
| app_id | string | 美团分配给APP方的id |
| sig | string | 输入参数计算后的签名结果 |


**应用级参数:**

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| type | int | 是 | 1 | 操作类型。仅支持传枚举值： 1-授权绑定；2-解除关联；3-修改三方门店ID。 |
| wm_poi_id | long | 是 | 123456 | 美团门店ID，该门店的商家账号必须已绑定当前应用的前提下，才能通过此接口完成门店相关操作。 若上传的门店是下线状态，不支持绑定应用，也不支持修改三方门店ID。 1.type=1（绑定）时，上传门店（非下线）如已绑定，报错。 2.type=2（解绑）时，上传门店（非下线）如未绑定，报错。 3.type=3（修改三方ID）时，上传门店（非下线）如未绑定，报错。 4.type=2（解绑）时，上传的wm_poi_id与app_poi_code之间如没有关联关系，则报错。 |
| app_poi_code | String | 是 | abc | 三方门店ID，与当前应用内已绑定门店的三方ID不能重复。 |


**SDK:**

**250**

**2 美团配送方式查询**

**接口文档地址:https://open-shangou.meituan.com/home/<USER>/9**

**请求地址: https://waimaiopen.meituan.com/api/v1/poi/mget**

**系统级参数:**

| API接口 | 接口描述 | 接口描述 |
| --- | --- | --- |
| timestamp | long | 调用接口时的时间戳，即当前时间戳（当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix - timestamp），注意传输时间戳与当前北京时间前后相差不能超过10分钟 |
| app_id | string | 美团分配给APP方的id |
| sig | string | 输入参数计算后的签名结果 |


应用级参数:

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| app_poi_codes | string | 是 | 111 | APP方门店id，传商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。 支持传多个门店id批量查询，一次调用可上传200个门店id，多个之间以英文逗号分隔；支持部分门店查询成功。 仅支持返回门店id正确的门店信息。 |


**3 美团订单拉取**

**接口文档地址:https://open-shangou.meituan.com/home/<USER>/342**

**请求地址:https://waimaiopen.meituan.com/api/v1/ecommerce/order/getOrderIdByDaySeq**

**系统级参数**

| API接口 | 接口描述 | 接口描述 |
| --- | --- | --- |
| timestamp | long | 调用接口时的时间戳，即当前时间戳（当前距离Epoch（1970年1月1日) 以秒计算的时间，即unix - timestamp），注意传输时间戳与当前北京时间前后相差不能超过10分钟 |
| app_id | string | 美团分配给APP方的id |
| sig | string | 输入参数计算后的签名结果 |


**应用级参数**

| 参数名 | 类型 | 是否必须 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| app_poi_code | string | 是 | 668921 | APP方门店id，即商家中台系统里门店的编码。如商家在操作绑定门店至开放平台应用中时，未绑定三方门店id信息，则默认APP方门店id与美团门店id相同。 |
| date_time | int | 是 | 20190212 | 需查询的订单产生日期，整型数据。仅支持查询近30天内的订单。 |
| day_seq_start | int | 是 | 1 | 订单流水号的开始序号，门店内每日的订单流水号都是从1开始。 |
| day_seq_end | int | 是 | 100 | 订单流水号的结束序号，注意开始流水号与结束流水号的跨度需小于100，即差值最大为99。 |


**4 京东到家配送方式查询**

**接口文档地址:https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=aa5a3f35a1c84d1e821b5690ba2828cd**

**请求地址:https://openapi.jddj.com/djapi/freight/queryStoreFreightConfigNew**

**系统级参数:**

| **字段** | **类型** | **是否必须** | **描述** |
| token | String | 是 | 采用OAuth授权方式为必填参数 |
| app_key | String | 是 | 应用的app_key |
| sign | String | 是 | 签名 |
| timestamp | String | 是 | 时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2011-06-16 13:23:30 |
| format | String | 是 | 暂时只支持json |
| v | String | 是 | API协议版本，可选值:1.0 |


**应用级参数:**

| **字段** | **类型** | **是否必须** | **示例值** | **描述** |
| stationNo | String | 是 |  | 门店编号 |


**5 京东到家订单拉取**

**接口文档地址:https://opendj.jd.com/staticnew/widgets/resources.html?groupid=169&apiid=ba3027848c3c4fda9674966e2a466482**

**请求地址:https://openapi.jddj.com/djapi/order/es/query**

**系统级参数:如上**

**应用级参数:**

| **字段** | **类型** | **是否必须** | **示例值** | **描述** |
| pageNo | Long | 否 | 2 | 当前页数,默认：1 |
| pageSize | Integer | 否 | 30 | 每页条数,默认：20，最大值100 |
| orderId | Long | 否 | 100001036354906 | 订单号（如果传了订单号，其他条件不生效） |
| buyerFullName | String | 否 | 张三 | 客户名 |
| buyerFullName_like | String | 否 | 张 | 客户名（模糊查询） |
| buyerMobile | String | 否 | 15600000000 | 手机号 |
| orderPayType | Integer | 否 | 4 | 订单支付类型（1：货到付款，4：在线支付） |
| buyerPin | String | 否 | JD_2aeh208df8789 | 买家账号 |
| orderStartTime_begin | Date | 否 | 2016-05-05 00:00:00 | 订单开始时间(开始) |
| orderStartTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单开始时间(结束) |
| orderPurchaseTime_begin | Date | 否 | 2016-05-05 00:00:00 | 购买成交时间-支付(开始) |
| orderPurchaseTime_end | Date | 否 | 2016-05-08 23:00:00 | 购买成交时间-支付(结束) |
| deliveryConfirmTime_begin | Date | 否 | 2016-05-08 00:00:00 | 妥投时间(开始) |
| deliveryConfirmTime_end | Date | 否 | 2016-05-08 23:00:00 | 妥投时间(结束) |
| orderCloseTime_begin | Date | 否 | 2016-05-08 00:00:00 | 订单关闭时间(开始) |
| orderCloseTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单关闭时间(结束) |
| orderCancelTime_begin | Date | 否 | 2016-05-08 00:00:00 | 订单取消时间(开始) |
| orderCancelTime_end | Date | 否 | 2016-05-08 23:00:00 | 订单取消时间(结束) |
| orderStatus | Integer | 否 | 32000 | 订单状态（20010:锁定，20020:订单取消，20030:订单取消申请，20040:超时未支付系统取消，31000:等待付款，31020:已付款，41000:待处理，32000:等待出库，33040:配送中，33060:已妥投，90000:订单完成） |
| +orderStatus_list | Set | 否 |  | 订单状态复选条件 |
| +buyerCity_list | Set | 否 |  | 城市复选条件 |
| deliveryBillNo | String | 否 | 1000001 | 承运单号，通常情况下和订单号一致 |
| businessType_list | Integer[] | 否 | 1,2 | 业务类型（1:京东到家商超,2:京东到家美食,3:京东到家精品有约,4:京东到家开放仓,5:哥伦布店内订单,6:货柜项目订单,7:智能货柜项目订单,8:轻松购订单,9:自助收银订单,10:超级会员码），当多个业务类型时，是以逗号分隔的数值串。 |
| orderType | Integer | 否 | 10000 | 订单类型 10000:从门店出的订单 |
| orderTakeSelfCode | String | 否 | eRowg | 订单自提码，当该字段有值时，要求到家配送门店编码必填。 |
| deliveryStationNo | String | 否 | 1000001 | 到家门店编码 |
| deliveryStationNoIsv | String | 否 | 2000001 | 商家门店编码 |
| srcOrderId | String | 否 | *********** | 订单来源系统(比如京东订单号) |
| returnedFields | String[] | 否 | 字段在下面返回结果(data.result.resultList中)查看。可以按标准数组方式传值，也可以按字符串传值。例如："orderId,discount,product"或者["orderId","discount","product"] | 设置返回结果(data.result.resultList中)的字段，字段间用英文逗号隔开，不传返回全部字段。强烈建议升级为可定制化查询模式，提升接口整体查询性能（减少无用字段的消耗） |


**6 饿百订单拉取**

**接口文档地址:https://open-retail.ele.me/#/apidoc/me.ele.retail:order.list-3?aopApiCategory=order_all&type=api_menu**

**请求地址:**

**系统级参数:**

| 名称 | 类型 | 是否必须 | 描述 |
| --- | --- | --- | --- |
| cmd | String | 是 | 接口cmd |
| version | String | 是 | 版本,默认3 |
| timestamp | Long | 是 | 时间戳 |
| ticket | String | 是 | 请求流水号 |
| source | String | 是 | 填写APPID |
| sign | String | 是 | 签名,md5 |
| encrypt | String | 否 | 是否加密,如AES,默认为空 |
| access_token | String | 否 | 访问令牌,访问用户隐私数据时的唯一权限标识。 如果API不需要授权则可以不带入此参数 |


**应用级参数:**

| 名称 | 类型 | 是否必须 | 描述 | 示例值 |
| --- | --- | --- | --- | --- |
| body | message:me.ele.nop.doa.api.param.request.order.OrderListReqDto | 是 | 请求消息体 | 见消息体 |


消息体:

javaEclipsetrue

## 5.4 the3platform 接口设计

**5.4.1 公共请求体**

| 属性 | 字段类型 | 必传 | 描述 |
| --- | --- | --- | --- |
| eccode | String | 是 | 平台code 美团：27 ，京东到家：11 ，饿了么：24 |
| clientid | String | 是 | ``` ds_online_store >> online_client_code ``` |
| body | Object | 是 | 业务请求体 |


### 5.4.2 inner/oms/o2o/store/bind

 描述：美团第三方门店基础维护，解绑、绑定、修改第三方门店id

request：

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| type | integer | 是 | 1 | 1-授权绑定；2-解除关联；3-修改三方门店ID。 |
| thirdStoreId | integer | 是 | 273562 | 美团门店id |
| storeCode | string | 是 | A001 | 自定义门店code |


示例：

{
 "eccode": 27,
 "clientid": "94c13fb44d074acabc1c9300d075c6f0",
 "body": {
 "type": "1",
 "thirdStoreId": "11203980",
 "storeCode": "C366"



 }
}

response:

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| code | integer | 是 | 10000 | 请求返回码10000-成功，其他失败 |
| msg | string | 否 | 成功 | 返回描述信息，code 不为10000时返回错误信息 |


示例：

{
 "code": "10000",
 "msg": "操作成功",
 "data": null,
 "timestamp": 1702871939355
}

### 5.4.3 inner/oms/o2o/store/getDetail

描述：获取门店详细信息，包含门店位置经纬度等。

request：

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| type | integer | 是 | 1 | 1-授权绑定；2-解除关联；3-修改三方门店ID。 |
| platformCode | integer | 是 | 27 | 平台code 美团：27 ，京东到家：11 ，饿了么：24 |
| storeCode | string | 是 | A001 | 自定义门店code |
| oldAppId | long | type=2 必传 | 875 | 需要解绑得原始应用app id ，不传默认 875 |
| newAppId | long | type=1 必传 | 123614 | 新绑定得app id，不传默认 123614 |


response:

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| code | integer | 是 | 10000 | 请求返回码10000-成功，其他失败 |
| msg | string | 否 | 成功 | 返回描述信息，code 不为10000时返回错误信息 |


### 5.4.4 获取配送方式接口

1. url:/inner/oms/o2o/store/getDeliveryType
2. 请求体
  1. | 属性 | 字段类型 | 必传 | 描述 |
| --- | --- | --- | --- |
| body | String | 是 | 门店codeds_online_store >> online_store_code |
3. | 属性 | 字段类型 | 必传 | 描述 |
| --- | --- | --- | --- |
| body | String | 是 | 门店codeds_online_store >> online_store_code |
4. 响应体
  1. | 属性 | 字段类型 | 描述 |
| --- | --- | --- |
| selfDelivery | Boolean | 是否为自配送 |
| deliveryType | String | 配送方式，多个用 , 分割 |
5. | 属性 | 字段类型 | 描述 |
| --- | --- | --- |
| selfDelivery | Boolean | 是否为自配送 |
| deliveryType | String | 配送方式，多个用 , 分割 |


### 5.4.5 /inner/oms/o2o/order/getOrderIdList

描述：根据时间获取个平台的订单id列表

request：

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| startDate | string | 是 | 2023-10-10 12:01:01 | 查询开始时间 |
| endDate | string | 是 | 2023-10-11 12:01:01 | 查询结束时间 |
| storeCode | string | 是 | A001 | 自定义门店code |


示例：

{
 "eccode": 11,
 "clientid": "2e9a09974733485c8d3e9fdb492f9af6",
 "body": {
 "startDate": "2023-12-12 01:01:00",
 "endDate": "2023-12-13 21:01:00",
 "storeCode": "C138"
 }
}

response:

| 属性 | 字段类型 | 必传 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| code | integer | 是 | 10000 | 请求返回码10000-成功，其他失败 |
| msg | string | 否 | 成功 | 返回描述信息，code 不为10000时返回错误信息 |
| data | List<string> | 否 | ["123124124","12312456"] | 返回时间范围内的订单id列表 |


示例：

{
 "code": "10000",
 "msg": "操作成功",
 "data": [
 "2329947779000121"
 ],
 "timestamp": 1702878807325
}

## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2023年12月11日**

**研发时间：2023年12月11日-2023年12月15日（含研发自测）；联调时间：2023年12月13日-2023年11月15日；测试时间：2023年11月18日-2023年11月20日；上线时间：2023年12月20日。**

| 功能模块 | 功能项 | 所属系统 | 优先级 | 工时PD | 预计完成时间 | 负责人 | 进展 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 美团店铺授权 | 新建记录表 | business-order |  |  |  |  | done |
| excel上传 |  |  | done |
| xxx-job处理解绑、授权 |  |  |  |
| 授权记录列表（分页、条件查询） |  |  |  |
| 三方接口对接 | 美团店铺授权、解绑、三方门店id更换 | the3platform |  |  |  |  | 90%-待自测 |
| 美团配送方式查询接口：[https://open-shangou.meituan.com/home/<USER>/9 https://open-shangou.meituan.com/home/<USER>/9](https://open-shangou.meituan.com/home/<USER>/9https://open-shangou.meituan.com/home/<USER>/9) |  |  |  |  | 90%-待自测 |
| 京东到家配送方式查询：[https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=aa5a3f35a1c84d1e821b5690ba2828cd](https://opendj.jd.com/staticnew/widgets/resources.html?groupid=194&apiid=aa5a3f35a1c84d1e821b5690ba2828cd) |  |  |  | 90%-待自测 |
| 美团拉单接口 |  |  |  | 90%-待自测 |
| 饿了么拉单接口 |  |  |  | 90%-待自测 |
| 京东到家拉单接口 |  |  |  | 90%-待自测 |
| 店铺自动配置 | 新建流程表 | business-order |  |  |  |  | done |
| 网店获取、去重（通过excel、分平台拉取） |  |  |  |  |  |
| 机构绑定 |  |  |  |  |  |
| 店铺配置 |  |  | 90%-待自测 |
| 订单处理设置 |  |  | 90%-待自测 |
| 自配送设置 |  |  | 90%-待自测 |
| 提示音设置 |  |  | 90%-待自测 |
| 下账设置 |  |  | 80%-待自测 |
| 通知商品中台 |  |  | 找 长江 提供接口 |
| 补单 |  |  |  |  |  |
| 流程回显、筛选 |  |  |  |  |  |


# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等