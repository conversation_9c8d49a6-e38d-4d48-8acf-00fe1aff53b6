# ####-2 上线清单模板 (独立需求)

****

**模板适用场景：模板适用于独立“业务需求”或“技术专项”上线时使用，通过此模板描述清楚单个“业务需求”或“技术专项”的上线信息。**

# 一、迭代概要

| **迭代内容** |  |
| --- | --- |
| **上线项目** |  |
|  |
| **上线时间** |  |
| **产研负责人****** | 产品（PM） |  |
| 研发（RD） |  |
| 测试（QA） |  |
| 代码审查（Code Reviewer） |  |
| **产品PRD** |  |
| **技术方案** |  |
| **是否测试介入** |  |
| *******测试报告** |  |
| *******回退方案** |  |
| **其他(注意事项）** |  |


# 二、迭代评估

## 2.1 风险问题

|  | **问题（Q）** | **解答 / 思考（A）** | **主R** |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |
| 3 |  |  |  |


## 2.2 慢查询

|  | 分类 | “迭代发布”将产生新问题 | 风险评估说明 | **主R** |
| --- | --- | --- | --- | --- |
| 1 | 慢接口 | 139 incomplete 是 | 140 incomplete 否 |  |  |
| 2 | 慢SQL | 141 incomplete 是 | 142 incomplete 否 |  |  |
| 3 | 慢ES | 143 incomplete 是 | 144 incomplete 否 |  |  |
| 4 | Redis大Key | 145 incomplete 是 | 146 incomplete 否 |  |  |


## 2.3 核心指标

|  | 核心指标项 | “迭代发布”将引发指标大幅波动 | 风险评估说明 | **主R** |
| --- | --- | --- | --- | --- |
| 1 | CPU占用 | 487 incomplete 是 | 488 incomplete 否 |  |  |
| 2 | 内存占用 | 489 incomplete 是 | 490 incomplete 否 |  |  |
| 3 | 网络占用 | 491 incomplete 是 | 492 incomplete 否 |  |  |
| 4 | 磁盘占用 | 493 incomplete 是 | 494 incomplete 否 |  |  |
| 5 | 系统并发量 | 495 incomplete 是 | 496 incomplete 否 |  |  |
| 6 | 日志告警数量 | 499 incomplete 是 | 500 incomplete 否 |  |  |


# 三、迭代依赖

## 3.1 迭代·依赖上游服务

|  | Git项目名称 | 最新Jar版本 | 服务情况(风险/性能/可用性) | 上游·主R |
| --- | --- | --- | --- | --- |
| 1 |  |  |  |  |
| 2 |  |  |  |  |


## 3.2 迭代·影响下游服务

|  | 我方·Git项目名称 | 我方·最新Jar版本 | 我方·提供的新(变化)接口 | 我方·主R | 下游服务·主R |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  |
| 2 |  |  |  |  |  |


# 四、上线配置

## 4.1 MySQL配置

|  | **数据库脚本** | 主R | **配置完成** |
| --- | --- | --- | --- |
| 1 |  |  | 84 incomplete DEV / TEST   85 incomplete PROD |
| 2 |  |  | 147 incomplete DEV / TEST   148 incomplete PROD |


## 4.2 Apollo配置

|  | **项目** | **线上配置** | **变化内容** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- |
| 1 | **** | **** | **** | @xx | 86 incomplete DEV / TEST   87 incomplete PROD |
| 2 | **** | **** | **** | **** | 149 incomplete DEV / TEST   150 incomplete PROD |


## 4.3 RocketMQ配置

|  | **操作类型** | **Mafka****配置** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- |
| 1 |  |  |  | 88 incomplete DEV / TEST   89 incomplete PROD |
| 2 |  |  |  | 151 incomplete DEV / TEST   152 incomplete PROD |


## 4.4 XXL-JOB

|  | **操作类型** | **名称(KEY)** | **方法参数** | **Cron** | **描述** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 1 |  |  |  |  |  | @xx | 90 incomplete DEV / TEST   91 incomplete PROD |
| 2 |  |  |  |  |  |  |  |


## 4.5 网关配置

**备注：网关包含api-gateway、business-gateway、assist-gateway等。**

|  | **网关名称** | **线上配置** | 变化内容 | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- |
| 1 | business-gateway |  |  |  | 92 incomplete DEV / TEST   93 incomplete PROD |
| 2 | api-gateway |  |  |  | 153 incomplete DEV / TEST   154 incomplete PROD |


## 4.6 接口鉴权

|  | **类型** | **系统唯一标识** | **接口名称** | **接口描述** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | 对方授权 | 我方系统唯一标识： | 对方系统唯一标识： | - |  | 94 incomplete DEV / TEST   95 incomplete PROD |
| 2 | 我方授权 | 对方系统唯一标识： | 我方进行服务授权，允许对方调用系统唯一标识接口 | - |  |  |


## 4.7 菜单配置

|  | **操作类型** | **模块** | **配置内容** | **描述** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | 新增 |  |  |  |  | 96 incomplete DEV / TEST   97 incomplete PROD |
| 2 |  |  |  |  |  |  |


## 4.8 权限配置

|  | **业务（模块）名称** | **操作类型** | **角色** | **功能权限** | **数据权限** | **主R** | **配置完成** |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | **** | **** | **** | **** | **** |  | 98 incomplete DEV / TEST   99 incomplete PROD |


## 4.9 OBS文件

|  | **操作类型** | OBS文件路径（包含文件名称） | **主R** | **配置完成** |
| --- | --- | --- | --- | --- |
| 1 | 新增 |  |  | 100 incomplete DEV / TEST   101 incomplete PROD |
| 2 |  |  |  |  |


## 4.10 Redis配置

Redis在线文档：

|  | **操作类型** | Redis Key | Redis 在线文档 | **主R** |
| --- | --- | --- | --- | --- |
| 1 | 新增 |  |  |  |
| 2 |  |  |  |  |


# 五、执行迭代发布流程

## 5.1 上线前

### 5.1.1 确认GIT项目发布顺序

|  | GIT项目名称（按照发布顺序填写） | 主R |
| --- | --- | --- |
| 1 |  |  |
| 2 |  |  |
| 3 |  |  |


### 5.1.2 检查发布准备工作

|  | 步骤 | 执行说明 | 执行结果 |
| --- | --- | --- | --- |
| 1 | Red强制检查项 上线代码CR完成 |  | 117 incomplete 完成 |
| 2 | Red强制检查项 DEV自测验证通过 |  | 118 incomplete 完成 |
| 3 | Red强制检查项 QA测试验证通过 |  | 119 incomplete 完成 |
| 4 | Red强制检查项 代码合并到Master分支 |  | 120 incomplete 完成 |


## 5.2 上线中

备注：关注系统监控指标、服务发布状态。

|  | 步骤 | 执行说明 | 执行结果 |
| --- | --- | --- | --- |
| 1 | 根据快速运维文档-监控系统指标 |  | 121 incomplete 完成 |
| 2 |  |  |  |


## 5.3 上线后

|  | 步骤 | 执行说明 | 执行结果 |
| --- | --- | --- | --- |
| 1 |  |  | 122 incomplete 完成 |
| 2 |  |  |  |
| 3 |  |  |  |