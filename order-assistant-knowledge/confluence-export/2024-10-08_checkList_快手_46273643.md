# 2024-10-08 checkList  快手

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 快手打印 | hydee-oms-logistic |  |  |  |  |  |  |  |  | b |
| 快手打印 | business-order-web |  |  |  |  |  |  |  |  |  |
| 快手打印 | hydee-oms-tool (只需要打包sdk) |  |  |  |  |  |  |  |  |  |
| 快手打印 | third-platform-logistics |  |  |  |  |  |  |  |  |  |
| 快手 | third-platform-order-other |  |  |  |  |  |  |  |  |  |
| third-platform-callback-other |  |  |  |  |  |  |  |  |  |
| business-order-b2c-third |  |  |  |  |  |  |  |  |  |
| third-platform-other |  |  |  |  |  |  |  |  |  |
| 删除mq测试代码 | third-platfrom-order-mt |  |  |  |  |  |  |  |  |  |
| third-platfrom-callback-mt |  |  |  |  |  |  |  |  |  |
| redploy | third-platform-gateway |  |  |  |  |  |  |  |  |  |
| hydee-api-gateway |  |  |  |  |  |  |  |  |  |
| B2C 按单发货 | cloud-ui |  |  |  |  |  |  |  |  |  |
|  |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | 备注 | sql变更 |
| --- | --- | --- |
| ``` dscloud ``` | ``` 初始化快手B2C 平台配置 ``` | ``` INSERT INTO dscloud.ds_platform (platform_code, platform_name, service_mode, create_time, modify_time, service_url,                                  logo_path) VALUES ('3008', '快手', 'B2C', '2024-10-10 10:47:36', '2024-10-10 10:47:38', null, 'https://sk-pro-dscloud.oss-cn-chengdu.aliyuncs.com/platformLogo/%E5%BF%AB%E6%89%8B.png?Expires=4882131531&OSSAccessKeyId=LTAI5t8T3JQBCtiJUUVRi9hn&Signature=jYeLBHXvWiHOKkUDB8qyMg08dCk%3D'); `````` INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ('LOGISTIC_CODE', '3008', '快手', '2024-10-17 10:22:59'); ``````  ``````  ``` |
| dscloud | 初始化快手B2C 对照快递商 配置 | insert into express_mapping (platform_code, value, name, code, oms_express, remark) select '3008', value, name, code, oms_express, remark from express_mapping where platform_code='3003' ;``` INSERT INTO dscloud.express_mapping     (platform_code, value, name, code, oms_express, remark) VALUES     ('3008', '4', '顺丰速运', 'SF', 69, NULL),     ('3008', '3', '申通快递', 'STO', 70, NULL),     ('3008', '6', '圆通速递', 'YTO', 71, NULL),     ('3008', '9', '中通速递', 'ZTO', 72, NULL),     ('3008', '11', '韵达快递', 'YUNDA', 73, NULL),     ('3008', '136', '京东快递', 'JD', 74, NULL),     ('3008', '8', '邮政标准包裹', 'POSTB', 75, NULL),     ('3008', '2', 'EMS邮政标准', 'EMS', 76, NULL),     ('3008', '109', '百世快递', 'HT', 79, NULL),     ('3008', '15', '优速快递', 'YS', 80, NULL),     ('3008', '5', '天天快递', 'TT', 81, NULL),     ('3008', '23', '国通快递', 'GTO', 83, NULL),     ('3008', '10', '宅急送快递', 'ZJS', 84, NULL),     ('3008', '25', '跨越速运', 'KYE', 87, NULL),     ('3008', '8', '邮政小包快递', 'YZXB', 1004, '快手打印'),     ('3008', '109', '极兔速递', 'JTSD', 131, '快手平台快递映射'),     ('3008', '127', '顺丰丰网', 'FENGWANG', 1008, '快手丰网'),     ('3008', '74', '京广速递', 'SZKKE', 92, '快手京广快递'),     ('3008', '119', 'EMS邮政经济速递', 'EMSJJ', 1011, '快手EMS邮政经济速递'),     ('3008', '8', '邮政信件', 'YZXJ', 1012, '快手邮政信件'),     ('3008', '4', '顺丰电商标快', 'SFDSBK', 1015, '快手对码'),     ('3008', '4', '顺丰特快', 'SFTK', 1016, '快手对码'),     ('3008', '4', '顺丰标快', 'SFBK', 1017, '快手对码'),     ('3008', '4', '顺丰卡航', 'SFKH', 1018, '快手对码'); ``` |
| dscloud |  | ALTER TABLE `dscloud`.`ds_online_client`  ADD COLUMN `private_key` varchar(255) NULL COMMENT '解密私钥' AFTER `access_type`, ADD COLUMN `sign_secret` varchar(255) NULL COMMENT '签名密钥' AFTER `private_key`; |
| hydee_aurora_basic |  | ALTER TABLE `hydee_aurora_basic`.`t_base_client`  ADD COLUMN `sign_secret` varchar(255) NULL COMMENT '签名密钥' AFTER `appsecret`; |
| dscloud |  | ALTER TABLE `dscloud`.`order_delivery_address`  MODIFY COLUMN `receiver_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人' AFTER `order_no`, MODIFY COLUMN `receiver_telephone` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人电话' AFTER `receiver_name`, MODIFY COLUMN `receiver_mobile_desen` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货手机号密文' AFTER `oms_order_no`; ``` alter table platform_refund_order     modify photos text null comment '退款图片Json数组', ```ALGORITHM=INPLACE, LOCK=NONE; |
| dscloud | 初始化 店铺快递设置中网点字段 | ``` insert into oms_custom_attributes ( `key`, column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value) select  replace(`key`,'3002','3008'), column_code, column_name, placeholder, type, seq, require_flag, status, type_attrs, tooltip, default_value from oms_custom_attributes where `key` LIKE 'STORE_EXPRESS_MERCHANT_3002%' ``` |
| dscloud | 退款审核 | INSERT INTO dscloud.code_value ( type, code, value_desc) VALUES ('REFUND_PROCESSING_PLATFORM', '3008', '快手'); 删除缓存：hydee:oms:b2c:webDict |
| dscloud | 初始化 店铺快递设置中网点字段 | ``` INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ('PRINT_SERVICE', '3008', '//s.kwaixiaodian.com/zone/supply/dadan/electronic-sheet', '2024-10-11 15:29:55'); ``` |
| dscloud | 初始化快手打印下载地址信息 | ``` INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ('KS_LINK', 'OPEN_SERVICE', '//www.kwaixiaodian.com', DEFAULT); INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ('KS_LINK', 'PRINT_COMPONENT', '//cloudprint.kwaixiaodian.com/rest/pc/scm/ebill/print/installer/download',         DEFAULT); `````` INSERT INTO dscloud.code_value (type, code, value_desc, create_time) VALUES ('ZL_LINK', 'PRINT_COMPONENT', '//sk-pro-centermerchant.oss-cn-chengdu.aliyuncs.com/xinyunprint/XinYunPrintServer_V3.8.1_net4.exe',         DEFAULT); ``` |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| hydee-api-gateway |  | - id: yxt-third-platform-callback-other  uri: [lb://third-platform-callback-other](lb://third-platform-callback-other)  predicates:  - Path=/third-platform/callback/9002/**,/third-platform/callback/9003/**,/third-platform/callback/43/**,/third-platform/callback/3008/**  filters:  - StripPrefix=1 |  |
| third-platform-gateway | **application.yml** | - id: third-platform-logistics  uri: [lb://third-platform-logistics](lb://third-platform-logistics)  predicates:  - Path=/third-platform/logistics/3008/** |  |
| third-platform-callback-other | **** | - businessType: order  platformCodeList: ["9002","9003","43","3008"]  topic: TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-OTHER_CALLBACK  - businessType: system  platformCodeList: ["3008"]  topic: TP_SYSTEM_THIRD-PLATFORM-OTHER-CALLBACK-SYSTEM_CALLBACK |  |
| third-platform-order-other | **** | message:  topic: TP_ORDER_THIRD-PLATFORM-ORDER-CALLBACK-OTHER_CALLBACK # tag区分，多个使用 || 分割，如：9002||11  tag: TAG_9002||TAG_9003||TAG_43||TAG_3008 |  |
| third-platform-other | **** | system:  topic: TP_SYSTEM_THIRD-PLATFORM-OTHER-CALLBACK-SYSTEM_CALLBACK  # tag区分，多个使用 || 分割，如：TAG_9002||TAG_11  tag: TAG_11||TAG_3008 |  |
| third-platform-logistics | **application.yml** | 新增 |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |