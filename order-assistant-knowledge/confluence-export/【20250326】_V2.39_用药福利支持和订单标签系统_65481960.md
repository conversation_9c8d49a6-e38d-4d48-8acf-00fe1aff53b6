# 【20250326】 V2.39 用药福利支持和订单标签系统

# 一、业务背景

## 1.1 业务背景

慢病营销中台，开放了一个慢病福利包，用户购买慢病福利包，享受对应的慢病服务。整个链路呢，店员分享-顾客购买-订单下账-慢病履约；

# 二、需求分析

## 2.1 业务流程

# 三、目标

完成这个慢病分享的记录，即订单储存需要增加分销员；订单模型增加字段；

订单标签，需要订单对应的标签，但是此次，打标由外部调用接口进行打；

# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

# 五、详细设计

## 5.1 详细功能

**5.1.1 订单中台的B2C订单表新增分销人id和分销人类型字段 数据来源于小前台订单数据**

小前台数据存储：

middle_order.order_info.emp_code 推广员

middle_order.staff_promotion_record.emp_code 推广员

**5.1.2 订单标签系统功能实现 标签数据CRUD，通过标签查询订单数据**

**5.1.3 对外提供订单打标签的功能接口**

**5.1.4 对外提供变更订单状态的功能接口**

## 5.2 接口设计

| 接口名称 | 接口路径 |
| --- | --- |
| 新增标签 |  |
| 查询标签 |  |
| 删除标签 |  |
| 编辑标签 |  |
| 通过标签查询订单 |  |
| 订单打标签 |  |
| 变更订单状态 |  |


## 5.3 涉及数据库

| 库名.表名 | sql脚本 |
| --- | --- |
| ``` dscloud_offline.basic_tag_info ``` | ``` CREATE TABLE `order_tag_info` (   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',   `tag_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签编码',   `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',   `tag_desc` varchar(520) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标签描述',   `tag_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签类型：SYSTEM-系统标签 MANUAL-人工标签',   `status` tinyint(1) DEFAULT '0' COMMENT '状态：1启用 0停用',   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',   `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',   PRIMARY KEY (`id`),   UNIQUE KEY `idx_unique_name` (`tag_name`),   UNIQUE KEY `idx_unique_code` (`tag_code`),   KEY `idx_unique_type` (`tag_type`) ) ENGINE=InnoDB AUTO_INCREMENT=136 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单标签信息' ``` |
| ``` middle_order.order_info_extra ``` | ``` ALTER TABLE   order_info_extra ADD   order_tag varchar(520) default 0.000000 null comment '订单标签 ,分隔'; ``` |


## 5.4 涉及服务

| 服务名称 | 描述 |
| --- | --- |
| ``` order-framework ``` |  |
| ``` order-atom-service ``` | 原子服务（标签管理DB操作） |
| order-service |  |
| order-sync-service |  |
| middle-datasync-message |  |
| hydee-middle-order |  |
| ydjia-merchant-customer |  |
| third-platform-callback-other |  |
| third-platform-order-other |  |
| order-service |  |
| hydee-business-order |  |
| hydee-business-order-b2c-third |  |
| hydee-business-order-web |  |


## 

## 5.4 安全设计

## 5.5监控报警

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等