# 2024-01-18 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| [1.1.2 商城首页相关内容逻辑调整](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6376186) | ydjia-merchant-manager | 1 | feature-cloud_freight-V1.1 |  |  |  |  |  |  |  |
| ydjia-merchant-customer | 2 | feature-cloud_freight-V1.0 |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 序号 | 表名称 | 描述 | DML |
| --- | --- | --- | --- |
| 1 | ip：************库：ydj_merchant_manager表：ydj_cloud_delivery_set | 新增表：第三方商家快递配置表 | create table ydj_cloud_delivery_set (     id                     varchar(50)                              not null         primary key,     mer_code               varchar(20)                              not null comment '商家编码',     sp_code                varchar(20)                              not null comment '服务商编码',     ykg                    decimal(16, 2)                           null comment '首重',     freight                decimal(16, 2) default 0.00              null comment '运费',     continue_weight        decimal(16, 2)                           null comment '续重',     renewal_cost           decimal(16, 2) default 0.00              null comment '续费',     postage_free_threshold decimal(16, 2) default 0.00              null comment '包邮门槛',     sort_number            int            default 0                 null,     isvalid                int            default 1                 null comment '是否有效',     create_time            datetime       default CURRENT_TIMESTAMP null comment '创建时间',     create_name            varchar(50)                              null,     modify_name            varchar(50)                              null,     modify_time            datetime       default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '末次修改时间' )     comment '第三方商家快递配置表';  create index idx_merCode_spCode     on ydj_cloud_delivery_set (mer_code, sp_code); |
| 2 | ip：************ 库：ydj_merchant_manager 表：ydj_cloud_delivery_charge_type | 新增表：第三方商家快递计费方式表 | create table ydj_cloud_delivery_charge_type (     id          varchar(50)                        not null         primary key,     mer_code    varchar(20)                        not null comment '商家编码',     sp_code     varchar(20)                        not null comment '服务商编码',     charge_type int      default 1                 not null comment '计费方式，1 默认无邮费 2 按区域/重量计费',     isvalid     int      default 1                 null comment '是否有效',     create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',     create_name varchar(50)                        null,     modify_name varchar(50)                        null,     modify_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '末次修改时间',     constraint idx_merCode_spCode         unique (mer_code, sp_code) )     comment '第三方商家快递计费方式表';  create index idx_merCode_spCode      on ydj_cloud_delivery_charge_type (mer_code, sp_code); |


#### 2.2 apollo配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
|  |  |  |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |