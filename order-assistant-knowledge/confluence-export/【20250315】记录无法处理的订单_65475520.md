# 【20250315】记录无法处理的订单

**表:**

CREATE TABLE `unprocessable_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `scene` VARCHAR(255) COMMENT '处理场景 REPEATED_OFFLINE_ORDER/REPEATED_OFFLINE_REFUND_ORDER',
  `business_id` VARCHAR(255) COMMENT '业务主键',
  `business_no` VARCHAR(255) COMMENT '业务单号',
  `third_platform_code` VARCHAR(255) COMMENT '三方平台编码',
  `allow_operate` VARCHAR(255) COMMENT '是否允许操作 false,true',
  `status` VARCHAR(255) COMMENT '处理状态 UN_HANDLE,HANDLED',
  `note` TEXT COMMENT '笔记',
  `migration` VARCHAR(255) COMMENT '是否是迁移订单 true,false',
  PRIMARY KEY (`id`),
  INDEX `idx_scene` (`scene`),
  INDEX `idx_allow_operate` (`allow_operate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='无法处理的订单';

**XXL-JOB任务:**

unprocessableOrderDataHandler

allow_operate 为true任务才会处理

**Scene:**

UnprocessableOfflineOrderCollectScene

UnprocessableOfflineRefundOrderCollectScene

TODO:

  2 complete 生产SQL执行  

添加字段:

ALTER TABLE `unprocessable_order` 
ADD COLUMN `third_business_no` varchar(255) CHARACTER SET utf8mb4  NULL DEFAULT NULL COMMENT '三方业务单号' AFTER `migration`,
ADD COLUMN `store_code` varchar(255) CHARACTER SET utf8mb4  NULL DEFAULT NULL COMMENT '门店编码' AFTER `third_business_no`,
ADD COLUMN `sharding_no` varchar(255) CHARACTER SET utf8mb4 NULL DEFAULT NULL COMMENT '分表位置' AFTER `store_code`,
ADD COLUMN `count` int(255) NULL DEFAULT NULL COMMENT '数量' AFTER `sharding_no`,
ADD COLUMN `amount_json` varchar(255) NULL COMMENT '金额json' AFTER `count`;

---

  4 incomplete prod执行  

CREATE TABLE `order_data_relation_hana` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `type` varchar(255) DEFAULT NULL COMMENT 'Type: OFFLINE_ORDER\\OFFLINE_REFUND_ORDER',
  `business_no` varchar(255) DEFAULT NULL COMMENT 'Business code',
  `target_schema` varchar(255) DEFAULT NULL COMMENT 'Target schema',
  `hana_id` varchar(255) DEFAULT NULL COMMENT 'Hana archive database ID',
  PRIMARY KEY (`id`),
  INDEX `idx_type_business_no` (`type`, `business_no`) COMMENT 'Index on type and business_no'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Mapping between order data and Hana data';