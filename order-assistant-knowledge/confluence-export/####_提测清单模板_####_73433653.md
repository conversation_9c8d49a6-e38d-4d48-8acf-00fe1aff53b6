# #### 提测清单模板 ####

收件人：[<EMAIL>](mailto:<EMAIL>), [x<PERSON><PERSON><PERSON><PERSON><PERSON>@yxt.wecom.work](mailto:x<PERSON><PERSON><PERSON><PERSON><PERSON>@yxt.wecom.work), [liuchu<PERSON><PERSON>@yxt.wecom.work](mailto:liuchu<PERSON><PERSON>@yxt.wecom.work), [<EMAIL>](mailto:<EMAIL>)

抄送人：[<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>) , [zhang<PERSON><PERSON>@yxt.wecom.work](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>) , [wangrong<PERSON>@yxt.wecom.work](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>) , [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>) , [<EMAIL>](mailto:<EMAIL>) , [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>) , [<EMAIL>](mailto:<EMAIL>), [<EMAIL>](mailto:<EMAIL>)

## 一、提测信息

| **提测内容** | 1、综合服务：销售任务导入2、一心助手首页：商品查询、近效期商品（B端导出），高库存商品，滞销商品、提成商品、二维码扫描、本月数据、会员开卡、app首页页面版本切换（会员日，节假日等）3、App消息推送平台 |
| --- | --- |
| **提测Git项目****************************************** | 后端：assist-synthesis | [assist-group / back-end / assist-synthesis · GitLab (hxyxt.com)](https://yxtgit.hxyxt.com/assist-group/back-end/assist-synthesis) | 提测分支：develop |
| 后端：assist-home | [assist-group / back-end / assist-home · GitLab (hxyxt.com)](https://yxtgit.hxyxt.com/assist-group/back-end/assist-home) | 提测分支：develop |
| 后端：yxt-app-push | [assist-group / back-end / yxt-app-push · GitLab (hxyxt.com)](https://yxtgit.hxyxt.com/assist-group/back-end/yxt-app-push) | 提测分支：develop |
| 后端：businesses-gateway | [https://yxtgit.hxyxt.com/java-business/businesses-gateway/](https://yxtgit.hxyxt.com/java-business/businesses-gateway/-/tree/dev) | 提测分支：feature/common-rdjoint |
| 后端：ydjia-merchant-platform | [https://yxtgit.hxyxt.com/java-business/ydjia-merchant-platform](https://yxtgit.hxyxt.com/java-business/ydjia-merchant-platform) | 提测分支：feature/common-rdjoint |
| 后端：hydee-middle-baseinfo | [https://yxtgit.hxyxt.com/java-business/hydee-middle-baseinfo](https://yxtgit.hxyxt.com/java-business/hydee-middle-baseinfo) | 提测分支：feature/common-rdjoint |
| 后端：oauth | [https://yxtgit.hxyxt.com/java-business/oauth](https://yxtgit.hxyxt.com/java-business/oauth) | 提测分支：feature/common-rdjoint |
| 前端：C端 | [https://yxtgit.hxyxt.com/assist/app](https://yxtgit.hxyxt.com/assist/app) | 提测分支：master |
| 前端：B端 | [https://yxtgit.hxyxt.com/assist/web](https://yxtgit.hxyxt.com/assist/web) | 提测分支：master |
| **提测(上线)清单** |  |
| **提测时间** | 12月05日 |
| **上线时间** | 12月20日 |
| **产研负责人****** | 产品（PM） | 吴秋薇、胡蕊 |
| 前端研发（RD） | 徐赛虎、詹利梅、张茜、付思远、吴敏、付贵勇 |
| 后端研发（RD） | **一心助手首页-应用类工具（销售任务导入）：**张兵、吴铭**一心助手首页-应用类工具（近效期商品）：**张苗、李靖**App消息推送平台：陈令，李阳阳，李飞** |
| 测试（QA） | **功能测试：**肖友权、刘春才、王越春、鲁宇航 **性能测试：**杨明杰、肖友权 |
| 代码审查（Code Reviewer） | 丁鹏、许赛虎 |
| **产品PRD** | [数据导入功能-销售任务导入 - 产品部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6361233)[近效期商品v1.0.3 - 产品部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6359594)[商品查询搜索 - 产品部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=1772905)[消息盒子v1.0.1 - 产品部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6364470) |
| **技术方案** | [#-3 数据导入-销售任务导入 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6364648)[#-4 近效期商品 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6360459)[#-1 商品信息查询 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=1778227)[【20231113】一心助手消息Push平台技术方案 - 后端研发部 - Confluence (hxyxt.com)](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=6366040) |
| **是否测试介入** | 是 |
| *******回退方案** | 新项目发布不支持回退 |
| **其他(注意事项）** | **1.任务消息和效期提醒消息暂未约定跳转链接目前占不支持消息跳转，后续对齐后发布。****2.现金存缴公司编码和D-ERP编码存在映射关系逻辑调整。****3.效期检查同步数据到POS也存在部门编码转化映射逻辑调整。****4.效期商品-陈列组商品列表接口，目前接口耗时较高，且前端分页大小2000，体验和超时风险很大** |


## 二、里程碑

| 里程碑 | 说明 |
| --- | --- |
| 11月1日 | PRD评审 |
| 11月17日 | 技术方案评审 |
| 11月23日 | 开发+自测完成 |
| 11月28日 | 联调完成，提测 |
| 12月20日 | 正式发版 |


## 三、单元测试

| 单测描述项目 | 结果确认 | 备注（说明或者截图） |
| --- | --- | --- |
| **Sonar代码确认无阻断性问题** | 6 complete 确认 | 本次迭代涉及项目无[Sonar](https://sonarqube.hxyxt.com/projects)阻断性代码问题。 |
| **[新增/变更代码]** **确认有单测覆盖** | 3 complete 确认 | 本次迭代新增、变更代码均有对应单测，单测均执行成功。 |
| **[新增/变更代码] 单测行覆盖率>70%** | 4 complete 确认 | 本次迭代新增、变更代码单测行覆盖率为**xx%**，符合团队要求。具体信息如下截图： |