# 第51周-2023-12-15

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | **长期项目** .net流程梳理 |  |  | 还没有正式开始，在开发环节中有部分功能梳理。5% |
| 2 | 1. 拣货复核明细缺失定位 2. 聚石塔、云顶、多多云 需要跟进真实B2C海典转发流程 3. 切流范围确认 +徐凯 |  |  |  |
| 3 | 1. 保山医保支付时防重复校验代码出现两条支付流水，导致查询支付状态失败 2. 保山医保授权码到期自动审核拒绝 3. 商户号1641710730 微信支付资金冻结 等待批量刷解冻接口 |  |  | 已全部处理完成 |
| 4 | 1. 处方图片上传OSS上线 2. 京东到家开发者账号还没有 |  |  | 已全部处理完成 |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 |  | 1.汇付插件上线 2.抖店对接O2O订单 30%  3.释放库存BUG，已经换货适配 4.退款表金额问题数据修复 5.京东到家退款未进入心云定位6.生产运维 | **㊀计划工作**1.汇付插件上线 2.抖店对接O2O订单 30%  3.释放库存BUG，已经换货适配 4.退款表金额问题数据修复 5.京东到家退款未进入心云定位**㊁实际完成**1.汇付插件上线 2.抖店对接O2O订单 30%  3.释放库存BUG，已经换货适配 4.退款表金额问题数据修复 5.京东到家退款未进入心云定位**㊂遗留问题**抖店对接O2O订单 30% 释放库存BUG，已经换货适配**㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 2 |  | 全部投入到切店自动化的项目中 | **㊀计划工作**自动化切店项目开发完毕，并提测**㊁实际完成**除授权解绑接口之外已全部提测**㊂遗留问题**美团授权解绑问题**㊃风险问题****㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关**1. 配合测试完成切店自动化的测试工作 2. 按时上线切店自动化 3. 投入到其他项目中 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 3 |  | 1. 退款单定时拒绝审核，医保退单2小时后未审核自动取消 1.0 day 2. 生产状态医保bug（扭转状态适配） 0.5 day 3. 订单支付成功，但状态为取消状态 0.5 day 4. B2C订单自动审核，B2C异常单处理 0.5 day 5. 保山医保支付流程代码追踪1.0day | **㊀计划工作**1. 退款单定时拒绝审核，医保退单2小时后未审核自动取消 2. 生产状态医保bug（扭转状态适配） 3. 订单支付成功，但是订单为取消状态 4. B2C订单自动审核无日志情况 **㊁实际完成**1. 退款单定时拒绝审核，医保退单2小时后未审核自动取消 2. 生产状态医保bug（扭转状态适配） 3. 订单支付成功，但是订单为取消状态 4. B2C订单自动审核无日志情况 **㊂遗留问题****㊃风险问题**医保支付流程特殊化处理导致状态有问题，后期增加定时状态查询有风险，可能会对现有医保支付流程进行大量改动，测试环境无法测试，上生产风险较大**㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）****** |  |
| 4 |  | 1.切店自动化配置流程 2.5day2.生产运维 2.5day**** | **㊀计划工作**1.自动化切店项目开发完毕，并提测**㊁实际完成**1.自动化切店项目开发完毕，并提测 100%2.超管创建云仓售后单无权限100%3.生产运维 100%**㊂遗留问题** **㊃风险问题** **㊄关于团队/项目建设的建议（想法）** **** | **㊀需求研发相关**1. 配合测试完成切店自动化流程 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** **** |  |
| 5 |  | 1. 协助处理山西、天津心云系统安装中得打印问题。贵州打印问题遗留门店升级。1.5 day 2. 切店自动化对接平台接口编码和测试。1.5day 3. Prometheus 对接。 0.5day 4. 上周运费拆分遗留问题处理 1day。 5. 产线问题协查。 0.5day | **㊀计划工作** **㊁实际完成** **㊂遗留问题** 1.自动化切换中的美团account接口目前没有调通。**㊃风险问题** **㊄关于团队/项目建设的建议（想法）** | .NET 服务暴露了一些问题。例如服务状态无法监控，运行日志无法查看等问题。建议将.NET 服务部署至K8s. 并且进一步完善运行日志以便于追踪问题。此计划需要运维同事支持，能否在下周开展需要再协商。 |  |
| 6 |  |  | **㊀计划工作** **㊁实际完成** **㊂遗留问题** **㊃风险问题** **㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关** **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 7 |  | 1. 编写海典POC技术文档。1.5天 2. 完成与提测订单优化处理需求。3.5天  **** | **㊀计划工作**1. 参与海典POS对接 2. 完成与提测订单优化处理需求 **㊁实际完成**1. 完成与提测订单优化处理需求 **㊂遗留问题** **㊃风险问题** **㊄关于团队/项目建设的建议（想法）** | **㊀需求研发相关**1. 继续进行海典POS对接 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）** |  |
| 8 | 徐国华 | **** | **** | **** |  |


### 2、重点项目周进展与风险概况

|  | 本周进度 | 下周目标 |  |
| --- | --- | --- | --- |
| 经营分析历史数据迁移 | 评审未过，编写设计方案 | 进入开发 | 20号前上线 |
| 切店自动化 | 提测 | 上线 | 20号前上线 |
| 海典POS对接 | 已评审，设计方案通过 | 进入开发 | 月底前上线 |
| 医保 | 已上线 | 完成 |  |
| 抖店对接 | 已评审，开发进度20% | 进入开发 | 月底前提测 |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |
| 3 |  |  |  |
| 4 |  |  |  |
| 5 |  |  |  |
| 6 |  |  |  |
| 7 |  |  |  |
| 8 |  |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | CR问题描述 | 改造方案 |
| --- | --- | --- |
| 1 |  |  |
| 2 |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 系统环境 | 缺陷等级 | 缺陷描述 | 负责人 | 问题分析 | 处理结果 |
| --- | --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 |
| --- | --- | --- | --- | --- |
| 1 | 12月20号部分切流值班人员 |  |  |  |
| 2 | 1. 聚石塔、云顶、多多云 需要跟进真实B2C海典转发流程 |  |  |  |
| 3 | 美团授权解绑问题 |  |  |  |
| 4 | nacos热更新 |  |  |  |
| 5 | .NET 迁移k8s |  |  |  |
| 6 | .NET gateway扩容 |  |  |  |