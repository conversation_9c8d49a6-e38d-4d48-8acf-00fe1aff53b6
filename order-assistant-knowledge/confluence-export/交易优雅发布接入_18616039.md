# 交易优雅发布接入

 接入文档: 

统一接入分支: release-order-grey-20240205

| 项目 | 261 complete 优雅发布接入 | 非prod配置检查:默认不用配置,需要检查有无手动指定了grey.enable=false | re dev | prod配置检查 | 38 complete merge to dev   3 complete 在test环境部署   27 complete 验证  2 - 20240223再次检查 | 合release | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| hydee-middle-order | 110 complete | 76 complete | 109 complete | 75 complete | 39 complete  2   4 complete     28 complete | 64 complete |  |
| hydee-business-order | 170 complete | 77 complete | 53 complete | 11 complete | 40 complete  2   5 complete     29 complete | 65 complete |  |
| hydee-business-order-lua |  |  |  |  |  |  | nginx项目,无需接入 |
| hydee-business-order-ext | 176 complete | 78 complete | 54 complete | 12 complete | 42 complete  2   6 complete     30 complete | 66 complete |  |
| hydee-business-order-web | 179 complete |  | 55 complete | 13 complete | 43 complete  2   7 complete     31 complete | 67 complete |  |
| hydee-business-order-b2c-third | 182 complete | 80 complete | 56 complete | 14 complete | 44 complete  2   8 complete     32 complete | 68 complete |  |
| hydee-third-inside(hydee-oms-logistic) | 10 complete | 81 complete | 57 complete | 15 complete | 45 complete  2   9 complete     33 complete | 69 complete | 因2.0.0.pa-SNAPSHOT的hydee-spring-boot-starter包与2.0.0版本差异较大,为了减少影响,所以这个项目的优雅发布单独引入 |
| middle-datasync-message | 188 complete | 82 complete | 58 complete | 16 complete | 46 complete  2   0 complete     34 complete | 70 complete |  |
| hydee-middle-sdp | 191 complete | 83 complete | 59 complete | 17 complete | 47 complete  2   1 complete     35 complete | 71 complete |  |
| hydee-print |  |  |  |  |  |  | git名 hydeeprintserver .net项目 |
| the3platform-message | 21 complete | 84 complete | 60 complete | 18 complete | 49 complete  2   2 complete     36 complete | 72 complete |  |
| the3platform-adapter | 23 complete | 85 complete | 61 complete | 19 complete | 50 complete  2   25 complete     37 complete | 73 complete |  |
| hecms_aurora |  |  |  |  |  |  | .net项目 |
| hydee-middle-payment | 206 complete | 86 complete | 62 complete | 20 complete | 52 complete  2   26 complete     51 complete | 74 complete |  |
| h3-pay-core | 不用接,项目中已经包含优雅发布com.hydee.h3.pay.config.ShutdownAspect |
| h3-pay-finance | 不用接,项目中已经包含优雅发布com.hydee.h3.finance.config.ShutdownAspect |
| businesses-gateway | 215 complete | 63 complete | 102 complete | 103 complete | 104 complete  2   105 complete     106 complete | 107 complete  merge to master | `static` `{` ```SmoothService.enhanceShutdownApi();` `}`  24 incomplete 20240221升级了spring cloud gateway版本,需要重新再开发环境看一下 |
| hydee-xxl-job | 218 complete | 90 complete | 108 complete | 87 complete | 89 complete  2   22 complete     1597 complete | 88 complete | 因2.0.0.pa-SNAPSHOT的hydee-spring-boot-starter包与2.0.0版本差异较大,为了减少影响,所以这个项目的优雅发布单独引入 发现有的项目有[2.0.0.pa](http://2.0.0.pa/)-SNAPSHOT版本的grey-spring-boot-lib,这个包与2.0.0-SNAPSHOT的差别比较大,需要单独处理 |
| middle-id | 221 complete | 91 complete  已修改,项目中配置的是false | 1598 complete | 1599 complete false to true | 1600 complete  2   41 complete     1601 complete | 95 complete | op |
| hydee-api-gateway | 258 complete | 92 complete | 1602 complete | 1603 complete | 1604 complete  2   48 complete     79 complete | 96 complete  merge to master |  |


#### 发布批次

第一批次:

the3platform-message
the3platform-adapter
hydee-third-inside(hydee-oms-logistic)
middle-datasync-message
hydee-middle-sdp
hydee-middle-payment

第二批次:

hydee-business-order-ext 归档服务不用发
hydee-business-order-web
hydee-business-order-b2c-third 不用发
hydee-middle-order
hydee-business-order

第三批次:
businesses-gateway
hydee-xxl-job
middle-id
hydee-api-gateway