# 2023-12-20 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  | the3platform-adapter | 1 | ``` feature-store_auto_config ``` |  |  |  |  |  |  |  |
|  | hydee-business-order | 2 | ``` feature-store_auto_config ``` |  |  |  |  |  |  |
|  | ``` hydee-xxl-job ``` | 3 | ``` feature-store_auto_config ``` |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

sqlCREATE TABLE `ds_online_store_auth_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `platform_code` varchar(16) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台编码，27美团,24饿百',
  `service_mode` varchar(6) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务模式:O2O;B2C',
  `platform_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网店编码',
  `online_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店编码',
  `online_store_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店名称',
  `auth_status` tinyint DEFAULT '1' COMMENT '授权处理状态 1待处理 2处理中 3处理成功 4处理失败',
  `relieve_status` tinyint DEFAULT '1' COMMENT '解绑处理状态 1待处理 2处理中 3处理成功 4处理失败',
  `change_status` tinyint DEFAULT '1' COMMENT '更换门店处理状态 1待处理 2处理中 3处理成功 4处理失败',
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '处理失败描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `creater` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `retry_times` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',
  `auth_time` datetime DEFAULT NULL COMMENT '新应用授权时间',
  PRIMARY KEY (`id`),
  KEY `idx_online_store_code` (`online_store_code`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺授权|解绑记录表	';

CREATE TABLE `ds_online_store_auto_config_step` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `online_store_id` bigint NOT NULL COMMENT 'ds_online_store主键',
  `online_store_code` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `creater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `organization_step` json DEFAULT NULL COMMENT '机构绑定',
  `store_step` json DEFAULT NULL COMMENT '店铺设置',
  `order_step` json DEFAULT NULL COMMENT '订单处理设置',
  `sound_step` json DEFAULT NULL COMMENT '声音设置',
  `self_delivery_step` json DEFAULT NULL COMMENT '自配送设置',
  `bill_step` json DEFAULT NULL COMMENT '下账设置',
  `order_pull_step` json DEFAULT NULL COMMENT '拉单',
  `merchandise_notify_step` json DEFAULT NULL COMMENT '通知商品中台',
  `status` tinyint DEFAULT '1' COMMENT '流程状态 1 待处理 2 处理中 3 处理成功 4 处理失败',
  `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_online_store_id` (`online_store_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_online_store_code` (`online_store_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺自动配置流程表	';

CREATE TABLE `ds_online_store_pull_order_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `store_config_step_id` bigint NOT NULL COMMENT 'ds_online_store_auto_config_step主键',
  `online_store_id` bigint NOT NULL COMMENT 'ds_online_store主键',
  `online_store_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺编码',
  `third_order_no` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '三方单号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `creater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `status` tinyint DEFAULT '1' COMMENT '状态：1 无需补单 2 补单成功 3 补单失败',
  `remark` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `lock` bit(1) DEFAULT b'0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_online_store_code` (`online_store_code`) USING BTREE,
  KEY `idx_online_store_id` (`online_store_id`) USING BTREE,
  KEY `idx_store_config_id` (`store_config_step_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='店铺拉单记录表';

#### 2.2 apollo配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |
| business-order | application.yml | message-notify | 新增： store-auto-config-topic: TOPIC_BUSINESS_STORE_AUTO_CONFIG client-config-notify-merchandise-topic: MERCH_DS_BATCH_DOWN_TOPIC |
| threadpool | 新增： storeAutoConfigCoreSize: 8 storeAutoConfigMaxSize: 128 storeAutoConfigKeepAliveTime: 0 storeAutoConfigCapacity: 256 |
| the3platform-adapter | application.yml | 新增节点：yxt | ``` 新增: `````` yxt:   meituan:     xinyun-app-id: 123614     xinyun-app-secret: ef5fdb477349f0301f8e7d924339c7b3     yunuo-app-id: 875     yunuo-app-secret: 52df194dc5322b90186121dbbcb529d9 ``` |
| 修改节点：``` spring.cloud.apollo ``` | ``` 增加： config:   server-addr: http://sk-prod-nacos.nacos.cse.com:8848   namespace: d04f590f-6926-4e84-becd-a62f369686a2 ``` |
| 修改节点：spring.datasource | ``` basicMaster:   driverClassName: com.mysql.cj.jdbc.Driver   url: ***************************************************************************************************************** #测试   username: agent   password: 5DMf9mmxK3 basicSlave:   driverClassName: com.mysql.cj.jdbc.Driver   url: ***************************************************************************************************************** #测试   username: agent   password: 5DMf9mmxK3 ``` |
|  |  |  |  |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 网店授权处理 | 海典调度中心 | omsStoreAutoAuthJobHandler | 0/2 * * * * ? | 新增 | {  "serviceModel": "O2O",  "platformCode": "27",  "authStatus": 1,  "relieveStatus": 1,  "changeStatus": 1,  "limit": 6,  "retryTimes": 3 } | 频率：每2秒运行一次 |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |
| RocketMQ | TOPIC_BUSINESS_STORE_AUTO_CONFIG |  |
| RocketMQ | MERCH_DS_BATCH_DOWN_TOPIC |  |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
| d04f590f-6926-4e84-becd-a62f369686a2 | the3platform-adapter | self_delivery_config | true{     "elmSelfDeliveryStore":[         "A236",         "A237",         "A239",         "A262",         "A298",         "A301",         "A302",         "A303",         "A387",         "A419",         "A447",         "A481",         "A482",         "A484",         "A485",         "A488",         "A496",         "A508",         "A509",         "A510",         "A523",         "A524",         "A525",         "A530",         "A531",         "A532",         "A534",         "A544",         "A550",         "A552",         "A558",         "A565",         "A664",         "A687",         "A712",         "A771",         "A780",         "A834",         "A841",         "A864",         "A878",         "A883",         "AA16",         "AA42",         "AA45",         "AA99",         "AAB6",         "AAB8",         "AAC8",         "AAD8",         "AAE4",         "AAG0",         "AAR0",         "AAZ4",         "AB02",         "AB17",         "AB60",         "AB61",         "AB63",         "AB88",         "AB93",         "AB94",         "ABB2",         "ABE5",         "ABH0",         "ABJ5",         "ABJ7",         "ABK2",         "ABM7",         "ABS5",         "ABS8",         "ABT2",         "ABT5",         "ABW7",         "ABY6",         "ABY9",         "ABZ8",         "ABZ9",         "AC04",         "AC36",         "AC55",         "AC64",         "AC83",         "AC92",         "ACB2",         "ACC7",         "ACD1",         "ACE5",         "ACI1",         "ACK0",         "ACN4",         "ACP4",         "ACQ4",         "ACQ9",         "ACW0",         "ACX1",         "ACZ1",         "AD05",         "AD07",         "AD15",         "ADA8",         "ADC4",         "ADC5",         "ADK0",         "ADN4",         "ADV7",         "AE10",         "AE73",         "AEA2",         "AEJ1",         "AEK8",         "AEM1",         "AF31",         "AF57",         "AF59",         "AF78",         "AFA3",         "AFC1",         "AFC2",         "AFE9",         "AFI0",         "AFK2",         "AFM0",         "AFS3",         "AFZ9",         "AG64",         "AG67",         "AG74",         "AG75",         "AGE1",         "AGT9",         "AGU2",         "AGU3",         "AGZ5",         "AH22",         "AH33",         "AH55",         "AH58",         "AH65",         "AH85",         "AH98",         "AHA8",         "AHC0",         "AHD2",         "AHD7",         "AHF5",         "AHN0",         "AHQ1",         "AHX6",         "AHY3",         "AI04",         "AI13",         "AI39",         "AI49",         "AI72",         "AI73",         "AI82",         "AI89",         "AJ02",         "AJ25",         "AJ27",         "AJ58",         "AJ68",         "AJ84",         "AJK5",         "AJM3",         "AJN0",         "AJR8",         "AK28",         "AK90",         "AK92",         "AKE8",         "AKE9",         "AKK1",         "AL41",         "AL69",         "AM30",         "AM42",         "AM51",         "AM53",         "AM57",         "AM62",         "AM75",         "AM90",         "AM92",         "AN14",         "AN21",         "AN42",         "AN55",         "AN77",         "AP19",         "AP28",         "AQ16",         "AQ18",         "AQ22",         "AQ50",         "AQ69",         "AR53",         "AR54",         "AR69",         "AR75",         "AR77",         "AR92",         "AR95",         "AS23",         "AS38",         "AS64",         "AS65",         "AS70",         "AT30",         "AT31",         "AT32",         "AT54",         "AT70",         "AU07",         "AU14",         "AU30",         "AU68",         "AW26",         "AW40",         "AX83",         "AX99",         "AY05",         "AY31",         "AY50",         "AZ20",         "AZ40",         "AZ41",         "AZ64",         "AZ72",         "AZ99",         "B087",         "B108",         "B112",         "B130",         "B132",         "B135",         "B136",         "B140",         "B158",         "B159",         "B183",         "B195",         "B218",         "B233",         "B242",         "B271",         "B286",         "B298",         "B341",         "B363",         "B364",         "B365",         "B403",         "B418",         "B427",         "B428",         "B430",         "B431",         "B432",         "B433",         "B434",         "B435",         "B439",         "B440",         "B446",         "B447",         "B449",         "B450",         "B451",         "B454",         "B456",         "B460",         "B461",         "B464",         "B465",         "B469",         "B472",         "B473",         "B487",         "B489",         "B491",         "B492",         "B493",         "B495",         "B496",         "B525",         "B526",         "B549",         "B550",         "B570",         "B576",         "B578",         "B613",         "B622",         "B636",         "B638",         "B648",         "B649",         "B651",         "B666",         "B739",         "B812",         "B890",         "B909",         "B939",         "B954",         "B962",         "B988",         "B992",         "BA05",         "BA08",         "BA14",         "BA32",         "BA39",         "BA60",         "BA65",         "BA68",         "BA76",         "BA77",         "BA78",         "BA81",         "BA83",         "BA86",         "BA98",         "BA99",         "BB12",         "BB17",         "C024",         "C058",         "C068",         "C069",         "C072",         "C076",         "C078",         "C079",         "C081",         "C144",         "C153",         "C170",         "C171",         "C184",         "C198",         "C204",         "C222",         "C227",         "C316",         "C329",         "C333",         "C334",         "C336",         "C339",         "C344",         "C348",         "C350",         "C352",         "C360",         "C361",         "C365",         "C366",         "C367",         "C368",         "C369",         "C381",         "C409",         "C416",         "C417",         "C429",         "C433",         "C462",         "C467",         "C470",         "C474",         "C479",         "C480",         "C498",         "G279",         "G579",         "GZ48",         "H051",         "H062",         "H064",         "H163",         "H170",         "H262",         "H264",         "H339",         "H361",         "H363",         "H367",         "H429",         "H431",         "H438",         "H440",         "H485",         "H491",         "H492",         "H497",         "H512",         "H515",         "H517",         "H519",         "H582",         "H594",         "H598",         "H620",         "H621",         "H624",         "H638",         "H833",         "HA18",         "HA87",         "HB03",         "HB06",         "HB18",         "HB21",         "HB23",         "HB24",         "HB34",         "HB44",         "HB55",         "HB72",         "HB80",         "HB87",         "HB95",         "HB96",         "HC07",         "HC29",         "HC35",         "HC38",         "HC39",         "HC42",         "HC49",         "HC56",         "HC57",         "HC65",         "HC66",         "HC74",         "HC76",         "HC82",         "HC86",         "HC88",         "HC91",         "HC96",         "HC98",         "HD01",         "HD09",         "HD10",         "HD13",         "HD16",         "HD17",         "HD20",         "HD22",         "HD25",         "HD29",         "HD49",         "HD65",         "HD66",         "HD75",         "HD90",         "HE09",         "HE95",         "HF10",         "HF14",         "HF24",         "HF25",         "HF35",         "HF59",         "JM0009",         "JM0010",         "JM0012",         "K001",         "K002",         "K003",         "K005",         "K006",         "K008",         "K009",         "K013",         "K015",         "K016",         "K017",         "K018",         "K019",         "M004",         "M047",         "M181",         "M182",         "M190",         "M312",         "M351",         "M390",         "M399",         "M400",         "M405",         "M441",         "M443",         "M444",         "M445"     ],     "mtSelfDeliveryType":["4001","4011","4012","4015","0000"] } |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
| OSS | 上传模板 |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |
|  |  |  |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |