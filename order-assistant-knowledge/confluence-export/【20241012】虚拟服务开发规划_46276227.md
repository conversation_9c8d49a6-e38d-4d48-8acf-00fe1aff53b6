# 【20241012】虚拟服务开发规划

# 后端需求池

| 开发内容 | 描述 | 开始时间 | 进度 |
| --- | --- | --- | --- |
| 添加mq消息管理 |  | 20241012 | 完成 |
| 支持动态body取值 |  | 20241014 | 完成 |
| body匹配器 |  | 20241016 | 完成 |
| 支持动态header取值 |  | 20241014 | 完成 |
| mockKey支持动态取值拼接 |  | 20241014 | 完成 |
| 上层路由host透传 |  | 20241015 | 完成 |
| 支持http://* 匹配方式 |  | 20241016 | 完成 |
| mock数据日志 |  | 20241015 | 完成 |
| 支持表单参数动态获取 |  |  |  |
| ``` URLConnection 支持动态路由 ``` |  |  |  |
|  |  |  |  |
| 链路分析 |  |  |  |
| oriHost匹配器 |  | 20241017 | 完成 |
| requestBody json 字段匹配 |  |  |  |
| 支持 * 全匹配模式 |  |  |  |
| http代理，支持记录请求和响应信息 |  |  |  |
| 代理记录数据复制到mockData |  |  |  |
| tag标签管理和相关查询接口 |  |  |  |
| 统一动态路由扩展请求头规范 |  |  |  |


前端需求池

| 开发内容 | 描述 | 开始时间 | 进度 |
| --- | --- | --- | --- |
| 权限管理 |  |  |  |
| mock数据分组 |  |  |  |
| 规则分组 |  |  |  |
|  |  |  |  |


业务需求池

|  |  |  |  |
| --- | --- | --- | --- |
| 三方接口数据统一记录 |  |  |  |
| 开发环境整理 |  |  |  |
| 生产上的店铺部分同步到测试环境 |  |  |  |
|  |  |  |  |


# 待解决

1. 如何在生产环境上找到指定接口的数据