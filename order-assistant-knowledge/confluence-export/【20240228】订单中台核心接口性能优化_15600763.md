# 【20240228】订单中台核心接口性能优化

  4 complete 根据Skywalking查询链路上耗时操作   5 incomplete 慢SQL   6 incomplete Grafana  

**订单处理页面**

| 页面 | 接口 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 抬头统计- 并行获取不同状态的数据 近7天、近24小时、工作日1.10-1.12类似的数据,统计订单不同状态的数量耗时最多 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/state/count/500001/B148?_t=0.3207751275407502&merCode=500001&organCode=B148](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/state/count/500001/B148?_t=0.3207751275407502&merCode=500001&organCode=B148) | GET | 直连数据库 |
| 列表分页- 并行获取订单非主表数据 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/normal](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/normal) | POST | 默认走ES 配置修改可走库 |
| 订单详细 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554) | GET |  |
| 拣货复核- [/detail/getCachePickConfirmInfo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/getCachePickConfirmInfo) 无需优化,走索引 - [order/detail/all/](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.9086249777781124&orderNo=1787863256465150465) 所有的关联查询都用到了索引   - 订单详情会返回门店配置信息,里面耦合了初始化门店的逻辑cn.hydee.middle.business.order.service.impl.baseinfo.DsOnlineStoreConfigServiceImpl#queryStoreConf, 如果线上门店配置不存在则初始化,产品可以优化相关逻辑   - 加索引 sql需要加索引ALTER TABLE `dscloud`.`ds_online_store_warehouse`  ADD INDEX `idx_ds_online_shop_id`(`online_shop_id`) USING BTREE;  ALTER TABLE `dscloud`.`ds_store_order_config`  ADD INDEX `idx_mer_code_out_b2c_client_id`(`mer_code`, `out_b2c_client_id`) USING BTREE;  ALTER TABLE `dscloud`.`platform_pay_mode_relation`  ADD INDEX `idx_mer_code_platform_code`(`mer_code`, `platform_code`) USING BTREE;  ALTER TABLE `dscloud`.`erp_pay_mode`  ADD INDEX `idx_client_conf_id`(`client_conf_id`) USING BTREE; - 订单详情会返回门店配置信息,里面耦合了初始化门店的逻辑cn.hydee.middle.business.order.service.impl.baseinfo.DsOnlineStoreConfigServiceImpl#queryStoreConf, 如果线上门店配置不存在则初始化,产品可以优化相关逻辑 - 加索引 sql需要加索引ALTER TABLE `dscloud`.`ds_online_store_warehouse`  ADD INDEX `idx_ds_online_shop_id`(`online_shop_id`) USING BTREE;  ALTER TABLE `dscloud`.`ds_store_order_config`  ADD INDEX `idx_mer_code_out_b2c_client_id`(`mer_code`, `out_b2c_client_id`) USING BTREE;  ALTER TABLE `dscloud`.`platform_pay_mode_relation`  ADD INDEX `idx_mer_code_platform_code`(`mer_code`, `platform_code`) USING BTREE;  ALTER TABLE `dscloud`.`erp_pay_mode`  ADD INDEX `idx_client_conf_id`(`client_conf_id`) USING BTREE; - [/batch/stock](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/stock) 里面有feign调用hana接口   - opi.* where条件使用了索引，字段较少,暂时可以无需优化 - opi.* where条件使用了索引，字段较少,暂时可以无需优化 - [/baseinfo/queryEmpByCondition](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/baseinfo/queryEmpByCondition) 无业务逻辑,feign接口 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/getCachePickConfirmInfo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/getCachePickConfirmInfo) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.9086249777781124&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.9086249777781124&orderNo=1787863256465150465) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/stock](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/stock) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/baseinfo/queryEmpByCondition](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/baseinfo/queryEmpByCondition) | POST |  |
| 配送跟踪 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/delivery/log/search/1787843642995885568?_t=0.4425551941850594&orderNo=1787843642995885568](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/delivery/log/search/1787843642995885568?_t=0.4425551941850594&orderNo=1787843642995885568) | GET |  |
| 打印拣货单 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/state/count/500001/A002?_t=0.5731420162732341&merCode=500001&organCode=A002](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/state/count/500001/A002?_t=0.5731420162732341&merCode=500001&organCode=A002) | GET |  |
| 修改门店（作废） | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/baseinfo/queryOrganizationForModify](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/baseinfo/queryOrganizationForModify) | POST |  |
| 订单下账列表有2个重复请求,应该按需请求 |  |  |  |


| 页面 | 接口 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 列表分页- aliRefundBillButtonShow 优先前端按钮的逻辑,在后端维护,后端应提供干净的接口,职责要清晰 - 逻辑较为简单 正常200-300ms左右会返回,如果接口慢,主要耗时在验证用户是否属于机构的方法上,一般响应慢是因为缓存失效了,缓存失效5min缓存key中包含userId,即缓存是用户维度的,每一个心云用户都有自己的缓存内容。影响范围限制在登录用户即登录的用户偶尔(根据缓存时间来)会感觉慢一下。临时方案可以增加缓存时间，长期方案可以确认缓存业务逻辑,使用异步方案监测key失效时间,提前刷,相应的其成本较高 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/search) | POST | 直连数据库 |
| 订单详细 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554) | GET |  |
| 重打小票 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/print/web](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/print/web) | POST |  |
| 锁库存 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/2.0/ds/order/lock/inventory](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/2.0/ds/order/lock/inventory) | GET |  |
| 退款 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787851454280142592?_t=0.41293866544050095&orderNo=1787851454280142592](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787851454280142592?_t=0.41293866544050095&orderNo=1787851454280142592) | GET |  |


| 页面 | 接口 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 列表分页ErpRefundInfo、RefundDetail信息获取并行 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/refund/page](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/refund/page) | POST | 默认走ES 配置修改可走库 |
| 订单详细 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/refund/detail/1787864328093704448](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/refund/detail/1787864328093704448) | GET |  |
| [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787864328093704448/hydee-business-refund-log?_t=0.9211434678942119](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787864328093704448/hydee-business-refund-log?_t=0.9211434678942119) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554) | GET |  |


| 页面 | 接口 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 列表分页 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerListPage](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerListPage) | POST | 默认走ES 配置修改可走库 |
| [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerListPage](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerListPage) | POST | 默认走ES 配置修改可走库 |
| [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerCount](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLedgerCount) | POST | 默认走ES 配置修改可走库 |
| 订单详细 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554) | GET |  |


| 页面 | 接口 | 请求方式 | 说明 |
| --- | --- | --- | --- |
| 列表分页 | [https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/delivery/page/search](https://dev-merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/delivery/page/search) | POST | 默认走ES 配置修改可走库 |
| 订单详细 | [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/all/1787863256465150465?_t=0.5110688691541281&orderNo=1787863256465150465) | POST |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/detail/batch/batchNo) | GET |  |
| [https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554](https://merchants.hxyxt.com/businesses-gateway/dscloud/1.0/ds/order/page/orderLog/1787863256465150465/hydee-business-order-log?_t=0.8484570493242554) | GET |  |


科传回调

```
科传Pos对接接口 1W家门店 30秒请求一次全部接口 同步异步 SQL 缓存 索引
```

```
KcPosCallBackController整个Controller 因为科传回调频率较高需要挨个排查
```

| 接口 |  |
| --- | --- |
| ##### POST:/ds/kcPos/callBack/getToken | 看链路评价耗时4ms，无需优化,使用的索引和缓存 |
| ##### POST:/ds/kcPos/callBack/getOrderList##### | 主要耗时在update和es索引sqlALTER TABLE `dscloud`.`inner_store_dictionary`  ADD INDEX `idx_token`(`token`) USING BTREE;并行,在前面的列表优化中已做 |
| ##### POST:/ds/kcPos/callBack/getOrderDetail##### | SELECT id,merchant_code AS merCode,client_code,store_code,pay_mode_id,pay_mode_name,pay_type,platform_code,conf_type,create_time,modify_time,creator,client_conf_id FROM erp_pay_mode WHERE client_conf_id IN (?) AND ( conf_type IN (?) OR ( conf_type IN (?) ) )SELECT id,merchant_code AS merCode,client_code,store_code,pay_mode_id,pay_mode_name,pay_type,platform_code,conf_type,create_time,modify_time,creator,client_conf_id FROM erp_pay_mode WHERE client_conf_id IN (?) AND conf_type = ?- erp_pay_mode 索引在前面已经添加 - 并行,在前面的列表优化中已做 - order_log表3817368条数据,可以做异步操作 @Async - orderSingleQueryManager.buildRespForOrderNormalQuery 前置和后置,调用了2次 |
| ##### POST:/ds/kcPos/callBack/orderAccounting##### 近7日、近24小时、工作日1.10-1.12:科传回传从监控上很少会出现尖峰 | 大部分都是内存计算,平均耗时是200多ms- 并行,在前面的列表优化中已做 - 日志异步, 代码中有多处地方出现,目前只在OrderBasicManager#saveOrderInfoLog这里调整为异步 - 下发声音广播数据,异步,原逻辑是异常只打印了日志,所以做了异步 - 经营数据同步到Kafka - 偶尔耗时长,是调用其他系统的接口耗时，例如Hystrix/MiddleMerchandiseClient#queryCommodityStock 800+ms - 接口中含有库存变更逻辑,同步请求,从业务的角度理解必须同步 |
| ##### POST:/ds/kcPos/callBack/getReturnOrderList##### | - 有循环查库,修改为批量查库 - 里面已经做了请求降级,通过token在方法中实现 - 并行,前面已处理 |
| ##### POST:/ds/kcPos/callBack/getReturnOrderDetail##### | - 整个方法有事务注解,缩小事务范围 - POS机查询订单详情,日志记录异步 |
| ##### POST:/ds/kcPos/callBack/returnOrderAccounting##### | - erp_pay_mode 前面已经加了索引 - 异步操作: 订单更新消息、订单日志写入ES - 接口 /1.0/employee/{id} - 解锁商品库存、查询商品库存 |
| [POST:/ds/kcPos/callBack/checkOrderAccounting](http://POST/ds/kcPos/callBack/checkOrderAccounting)[POST:/ds/kcPos/callBack/checkReturnOrderAccounting](http://POST/ds/kcPos/callBack/checkReturnOrderAccounting)##### | skywalking无记录,可能是废弃接口 |



慢Sql

|  |  |
| --- | --- |
| 大in 全部 | 已做了时间控制和开关 |
| SELECT  count( * )  FROM  ds_online_store os  LEFT JOIN ds_online_store_config osc ON [os.id](http://os.id) = osc.online_store_id  LEFT JOIN ds_online_store_auto_config_step oso ON [os.id](http://os.id) = oso.online_store_id  WHERE  os.mer_code = N   AND os.organization_code IN ( N )   AND os.STATUS != N; | sqlALTER TABLE `dscloud`.`ds_online_store`  ADD INDEX `idx_merCodeOrganCodeStatus`(`mer_code`, `organization_code`, `status`) USING BTREE; |
| SELECT COUNT(N) FROM refund_order ro INNER JOIN order_info oinfo ON ro.order_no = oinfo.order_no WHERE ro.mer_code = N AND ro.state = N AND ro.erp_state >= N AND ro.erp_state <= N AND oinfo.service_mode = N AND oinfo.organization_code IN (N); | 使用到索引,因为是count,扫描行数较多,触发慢sql |
| select sbc.checker_type, sbc.frequency_type from store_bill_config sbc join ds_online_store dos on [sbc.id](http://sbc.id) = ( select max(id) from store_bill_config t where t.mer_code = dos.mer_code and t.store_code = dos.online_store_code and t.client_code = dos.online_client_code and t.store_code = dos.online_store_code ) and sbc.is_batch_no_stock = N where dos.mer_code = N and dos.organization_code = N; | store_bill_config是全表扫描sqlALTER TABLE `store_bill_config`  ADD INDEX `idx_merStoreClientCode`(`mer_code`, `store_code`, `client_code`) USING BTREE; |
| 20240119 |
| select id, order_no, order_state, third_platform_code, third_order_no, third_order_id, third_order_state, off_state, mer_code, client_code, online_store_code, online_store_name, organization_code, organization_name, delivery_time_type, delivery_time_desc, buyer_remark, buyer_message, seller_remark, lock_flag, lock_msg, remind_flag, buyer_name, receiver_lat, receiver_lng, acceptor_id, acceptor_name, accept_time, picker_id, picker_name, pick_operator_id, pick_operator_name, pick_time, canceller_id, canceller_name, cancel_reason, cancel_time, ex_operator_id, ex_operator_name, ex_operator_time, complete_time, created, modified, erp_adjust_no, erp_sale_no, create_time, prescription_flag, day_num, self_verify_code, erp_state, bill_time, call_erp_flag, member_no, transfer_delivery, client_conf_id, bill_operator, is_prescription, prescription_status, is_push_check, appointment, appointment_business_flag, appointment_business_type, new_customer_flag, request_deliver_goods_result, deliver_goods_refuse_reason, integral_flag, invoice_content, invoice_title, invoice_type, need_invoice, taxer_id, source_online_store_code, source_online_store_name, source_organization_code, source_organization_name, complex_modify_flag, medical_insurance, service_mode, cancel_bill_times, wsc_ext_json, top_hold, order_type, order_pick_type, source_channel_type, extend_info, data_version from order_info where remind_flag = N and create_time > DATE_ADD(now(),INTERVAL -N day); | 9 complete 有安全隐患,建议分页处理   10 complete 只查订单号即可,看代码只用到了orderNo   15 complete 使用到索引  ``` 优化分支 release-getReminderOrder ``` |
|  | cn.hydee.middle.business.order.yxtadapter.domainservice.ynoms.cron.YnOmsOrderBillStatusAssignmentCronTaskServiceImpl#doCronTask``` organizationCodes ```  16 complete 切完店之后就不会调用了,不优化 2024.01.22会完成切店  ```  ``` |
|  | 12 complete cn.hydee.middle.business.order.controller.ScheduleJobController#riderTrailInfoFromHEMS —— 单独起一个线程,异步处理 |
|  |  |


cn.hydee.middle.business.order.service.impl.OrderChangeSelfDeliveryServiceImpl#process

当前预约订单也放入了redis导致一直OrderChangeSelfDeliveryServiceImpl.java:247 转自配送失败需要剔除

cn.hydee.middle.business.order.service.impl.OrderChangeSelfDeliveryServiceImpl#changeSelfDelivery

转自配送超时情况了

```

```