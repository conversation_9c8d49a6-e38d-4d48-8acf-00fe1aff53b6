# 【20240806】V2.3 B2C库存占用优化

# 一、背景

## 1.1 业务背景

1. 目前B2C库存占用不准，经常出现超卖，现需要针对B2C的库存占用做优化；


## 1.2 痛点分析

1. B2C库存占用不准确,导致平台出现超卖情况


## 1.3 系统现状

1. 目前系统B2C只占用门店的库存,无法占用到仓库库存;


# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

1. 新订单进入心云后,订单落入最优仓库,库存占用则需要根据规则占用库存;
2. 异常单转正常单,释放其他仓库的库存占用,并重新占用新仓库的库存;
3. 订单下账或者取消,释放当前订单库存;
4. B2C 店铺仓库设置优先级并清洗历史脏数据;


# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

1. **新订单库存占用流程****trueB2C新订单库存占用流程falseautotoptrue65711**
2. **异常单转正常单流程****true异常单转正常单流程falseautotoptrue6572**
3. **订单已下账/同意退款/已取消库存释放流程****true订单已下账释放库存流程falseautotoptrue6572**


# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

### 5.3.1 前端交互接口

1. ****修改仓库配置: /ds/baseinfo/updateWarehouse (不允许存在同一个优先级)


### 5.3.2 纯后端接口

1. ****新订单占用库存接口: cn.hydee.business.order.b2c.service.baseinfo.order.handle.B2COrderHandleChain#doOrderChain
2. 异常单转正常单接口: cn.hydee.business.order.b2c.service.exhandle.OrderExHandleChain#newDoChain
3. 订单下账释放库存接口: cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.b2c.store.B2cStockHandlerImpl#toStockDeduction
4. 订单同意退款释放库存接口: cn.hydee.business.order.b2c.service.retreat.refund.agree.AbstractAuditRefund#isAllRefundAndUpdate


## 5.3 涉及数据库

**CREATE TABLE `commodity_stock` ( `id` bigint NOT NULL AUTO_INCREMENT, `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL, `order_no` bigint NOT NULL COMMENT '订单号', `erp_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '商品编码', `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '库存实际占用门店/仓库', `order_detail_id` int NOT NULL COMMENT '商品详情ID', `stock_qty` int NOT NULL COMMENT '库存数量', `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '组织机构编码', `type` tinyint(1) NOT NULL COMMENT '1 锁库存 2 解锁库存3，锁定库存中4，解锁库存中5，解锁库存失败6，锁库存失败', `create_time` datetime DEFAULT CURRENT_TIMESTAMP, `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `serial_number` bigint DEFAULT NULL COMMENT '流水号(全局唯一)', PRIMARY KEY (`id`), KEY `idx_order_no` (`order_no`) USING BTREE ) ENGINE=InnoDB AUTO_INCREMENT=56561569 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;**

## 5.4 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.5监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.6 问题

**订单转正常单成功后 判断店铺是否自动审核 自动审核就去审核**

**未发货部分退款在删除订单之前嵌入释放库存流程--具体询问世达**

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年08月12日;**

**研发时间：2024年08月13日-2024年8月19日；**

**测试时间：2024年8月19日(提测)；**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等