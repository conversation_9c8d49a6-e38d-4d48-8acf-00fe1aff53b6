# 【20240918】B2C 对接物流中台

# 背景

## 业务背景

a. 当前心云系统，没有物流中台，只有物流单，物流单与订单高度藕合，现将物流单抽出来，完成物流中台的建设；整合骑手配送和快递配送；

b.心云系统，没有对接对应的物流轨迹查询；

c.取消面单，拦截快递，订单修改地址，都是手动操作，效率过低和易操作错；提升人效，每个子公司可节约0.5个人力，按4500/人，一年节省：30万/年的人力成本；

## 系统现状

1. 创建面单 注： WMS 订单物流发货流程由WMS自行处理，处理完回调心云物流信息。


true获取面单falseautotoptrue11611

# 需求分析

prd: v2.5 物流中台建

## 需求功能

|  | 功能单 | 功能说明 |
| --- | --- | --- |
| 1 | 获取面单流程优化 | 1. 优化当前面单流程 2. 对接物流中台创建物流单接口 |
| 2 | 退款流程增加取消面单 | 1. 优化退款流程 2. 对接物流中台取消接口 |
| 3 | 地址修改流程优化 | 1. 优化地址修改流程 2. 对接物流中台修改地址接口 |
| 4 | 增加物流轨迹查询 | 1. 增加物流轨迹展示 |
| 5 | 店铺快递配置 | 1.迁移历史数据2.对接增量数据接口 |


# 目标

## 本期目标

### 业务目标

1.对接物流中台，赋予B2C订单取消面单、拦截快递、修改地址能力。

### 技术目标

一般是技术指标（SLA）、沉淀基础服务、节约开发成本等

## 中长期目标

愿景、长远规划


# 整体设计

## 核心技术问题

*扩展性、数据迁移、兼容等核心技术挑战和解决方案等。*

|  | 核心问题 | 解决方案 |
| --- | --- | --- |
| 1 |  |  |
| 2 |  |  |


# 详细设计

## 模块详细设计

*分模块详细设计， 一般需要模块具体的时序图、流程图、技术方案调研选型对比。1*

创建物流单流程

true创建订单新流程falseautotoptrue12012

取消面单流程

true取消流程falseautotoptrue10163

修改地址流程

true地址修改falseautotoptrue6211

页面修改

| 原型 | 修改点 |
| --- | --- |
|  | 1. 海典推送修改地址订单，增加需改址标识 |
|  | 1. 已发货订单增加物流轨迹查询接口 2. 优化已下账的图标 3. 增加快递单号显示 |
|  | 1. 退款审核增加取消物流单流程 2. 增加二次弹窗确认页面 |


## 存储数据库设计

*新增、修改的字段DDL；索引设计；数据量预估，分库分表设计；有时需要存储选型分析方案：缓存、es等。*

## 接口设计

*新增、修改的接口定义；流量预估，接口性能设计。*

## 安全设计

*时刻警惕资损问题；数据一致性、接口防刷、幂等设计等。*

## 监控报警

*需要思考上线后如何监控，及时响应止损、回滚、降级等方案。*

## 质量效率

*本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。*

# 里程碑

备注：日期统一使用日期组件进行填写；如无特殊情况，里程碑日期、里程碑起止时间、工时等都需要填写。

|  | **里程碑** | 里程碑日期（填写完成日期） | 里程碑起止时间 | 工时(pd) | 备注 |
| --- | --- | --- | --- | --- | --- |
| 开始日期 | 结束日期 |
| 1 | PRD评审 |  |  |  |  |  |
| 2 | 技术方案-设计 |  |  |  |  |  |
| 3 | 技术方案-评审 |  |  |  |  |  |
| 4 | 研发 |  |  |  |  |  |
| 5 | 自测 |  |  |  |  |  |
| 6 | 联调 |  |  |  |  |  |
| 7 | 提测 |  |  |  |  |  |
| 8 | 测试 |  |  |  |  |  |
| 9 | 上线 |  |  |  |  |  |


# 项目排期

| 1 | 创单面单流程优化 | 1.对接物流中台创单接口2.根据订单来源做策略优化 |  |  |  |  |  |
| 2 | 退款流程优化 | 1. 对接物流中台取消接口 2. 增加日志输出 |  |  |  |  |  |
| 3 | 修改地址 | 1. 对接物流中台改址接口 2. 增加标识 |  |  |  |  |  |
| 4 | 物流轨迹查询 | 1.对接物流中台接口 |  |  |  |  |  |
| 5 | 店铺快递配置迁移 | 1.历史数据迁移2.增量数据迁移 |  |  |  |  |  |
| 6 | 内部联调 | 1.自测+联调 |  |  |  |  |  |
|  | 功能模块 | 功能项 | 优先级 | 工时(PD) | 主R | 计划完成日期 | 研发进展 |


# 上线方案

备注：技术方案设计期间，需要提前建立好需求对应的提测清单（必须）、上线清单（可选）文档。

|  | 清单项 | 清单地址 | 说明 |
| --- | --- | --- | --- |
| 1 | 提测清单 |  | 1. 提测清单建立为技术方案的子文档，命名为：xxx(通常为需求名称)-提测清单。 2. 提测前RD需要及时维护最新信息，并识别不同环境之间的差异。 |
| 2 | 上线清单 |  |  |