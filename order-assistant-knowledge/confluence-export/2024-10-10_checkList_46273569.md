# 2024-10-10 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 美团B2C门店商品变更消息推送 | ds-service-mt |  |  |  | 徐``` TP_ORDER_BUSINESS-ORDER_ORDER-MODEL ```国华 |  |  |  |  |  |
| 提供三方订单号+三方平台查详情接口 | hydee-business-order |  |  |  |  |  |  |  |  |  |
| 支持慢病优化需求 | order-atom-service |  |  |  |  |  |  |  |  |  |
| 美团迁移-企客配bug修复 | third-platform-order-mt |  |  |  |  |  |  |  |  |  |
| 线上单对接下游、成本价 | ``` hydee-business-order `````` hydee-business-order-web `````` hydee-business-order-b2c-third ``` |  |  |  |  |  |  |  |  |  |


### 二、配置变更

#### 2.1 数据库变更

| 数据库 | sql变更 | 备注 |
| --- | --- | --- |
|  | ``` CREATE TABLE `order_model` (   `id` bigint NOT NULL AUTO_INCREMENT,   `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',   `third_platform_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编码',   `order_no` bigint NOT NULL COMMENT '订单号，雪花算法',   `third_order_no` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单号',   `order_status` tinyint DEFAULT NULL COMMENT '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',   `order_type` tinyint DEFAULT '0' COMMENT '订单类型:0普通订单,1预约订单,2处方订单,3积分订单,5运费订单,30机器自动拣货订单',   `online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店编码',   `online_store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下单线上门店名称',   `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',   `organization_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线下门店名称',   `source_online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',   `source_online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店名称',   `source_organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',   `source_organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店名称',   `pay_time` datetime DEFAULT NULL COMMENT '支付时间',   `delivery_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配送方式',   `total_actual_order_amount` decimal(16, 2) DEFAULT '0.00' COMMENT '客户实付',   `actual_freight_amount` decimal(16, 2) DEFAULT '0.00' COMMENT '配送费',   `member_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',   `member_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员唯一标示',   `created` datetime DEFAULT NULL COMMENT '订单实际创建时间',   `create_time` datetime DEFAULT CURRENT_TIMESTAMP comment '创建时间',   `accept_time` datetime DEFAULT NULL COMMENT '接单时间',   `complete_time` datetime DEFAULT NULL COMMENT '完成时间',   `service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'O2O' COMMENT '服务模式',   `pay_info_json` json DEFAULT NULL COMMENT '支付信息json',   PRIMARY KEY (`id`) USING BTREE,   UNIQUE KEY `u_order_no` (`order_no`) USING BTREE,   KEY `idx_create_time` (`create_time`),   KEY `i_mer_online_code` (`mer_code`, `online_store_code`) USING BTREE,   KEY `idx_code` (`mer_code`, `organization_code`, `id` DESC),   KEY `idx_created` (`created`, `mer_code`) USING BTREE,   KEY `idx_source_organization_code` (`source_organization_code`) USING BTREE ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC COMMENT = '订单模型数据' ``` |  |
|  | `CREATE` `TABLE` ``order_detail_model` (` ````id```bigint` `NOT` `NULL` `AUTO_INCREMENT,` ````order_no```bigint` `NOT` `NULL` `COMMENT``'订单号'``,` ````commodity_code```varchar``(60)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'商品erp编码'``,` ````commodity_name```varchar``(255)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'商品名称'``,` ````commodity_count```int` `DEFAULT` `NULL` `COMMENT``'商品数量'``,` ````total_actual_amount```decimal``(16, 2)``DEFAULT` `'0.00'` `COMMENT``'成交总额小计-优惠'``,` ````bill_price```decimal``(16, 4)``NOT` `NULL` `COMMENT``'下账价格'``,` ````goods_type` tinyint(1)``DEFAULT` `'1'` `COMMENT``'商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品'``,` ````is_joint` tinyint``DEFAULT` `'0'` `COMMENT``'是否是组合商品，0不是组合商品，1是组合商品'``,` ````main_pic```varchar``(255)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'商品图片'``,` ````is_gift` tinyint``NOT` `NULL` `DEFAULT` `'0'` `COMMENT``'是否是赠品（0不是赠品，1是赠品）'``,` ````status` tinyint``DEFAULT` `'0'` `COMMENT``'明细状态，0, "正常",1, "库存不足异常",2, "商品不存在",10, "已换货",11, "已退款"'``,` ````is_original` tinyint``DEFAULT` `'0'` `COMMENT``'是否是原始明细 0-是 1-否'``,` ````create_time` datetime``DEFAULT` `CURRENT_TIMESTAMP` `comment``'创建时间'``,` ````average_price```decimal``(16, 6)``DEFAULT` `NULL` `COMMENT``'商品不含税加权成本价'``,` ````tax_price```decimal``(16, 6)``DEFAULT` `NULL` `COMMENT``'商品含税加权成本价'``,` ````tax_rate```varchar``(64)``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'税率'``,` ```PRIMARY` `KEY` `(`id`) USING BTREE,` ```KEY` ``idx_order_no` (`order_no`) USING BTREE,` ```KEY` ``idx_create_time` (`create_time`)` `) ENGINE = InnoDB AUTO_INCREMENT = 1``DEFAULT` `CHARSET = utf8mb4``COLLATE` `= utf8mb4_general_ci ROW_FORMAT =``DYNAMIC` `COMMENT =``'订单明细模型数据'` |  |
|  | `CREATE` `TABLE` ``refund_order_model` (` ````id```bigint` `NOT` `NULL` `AUTO_INCREMENT COMMENT``'记录ID'``,` ````mer_code```varchar``(20)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'商户编码'``,` ````third_platform_code```varchar``(20)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'平台编码'``,` ````refund_no```bigint` `NOT` `NULL` `COMMENT``'退款单号，雪花算法'``,` ````third_refund_no```varchar``(64)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'三方平台退款ID'``,` ````order_no```bigint` `NOT` `NULL` `COMMENT``'订单号'``,` ````third_order_no```varchar``(45)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``NOT` `NULL` `COMMENT``'第三方平台订单号'``,` ````complete_time` datetime``DEFAULT` `NULL` `COMMENT``'退款完成时间'``,` ````refund_status` tinyint``DEFAULT` `NULL` `COMMENT``'退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消'``,` ````member_card```varchar``(64)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'会员编号'``,` ````member_id```varchar``(64)``CHARACTER` `SET` `utf8mb4``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'会员唯一标示'``,` ````organization_code```varchar``(40)``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'线下门店编码'``,` ````online_store_code```varchar``(40)``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'线上门店编码'``,` ````source_organization_code```varchar``(40)``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'来源线下门店编码'``,` ````source_online_store_code```varchar``(40)``COLLATE` `utf8mb4_general_ci``DEFAULT` `NULL` `COMMENT``'来源线上门店编码'``,` ````consumer_refund```decimal``(16,2)``DEFAULT` `'0.00'` `COMMENT``'退买家总金额'``,` ````platform_refund_delivery_fee```decimal``(16,2)``NOT` `NULL` `DEFAULT` `'0.00'` `COMMENT``'退平台配送费'``,` ````merchant_refund_post_fee```decimal``(16,2)``NOT` `NULL` `DEFAULT` `'0.00'` `COMMENT``'退商家配送费'``,` ````create_time` datetime``DEFAULT` `CURRENT_TIMESTAMP` `COMMENT``'创建时间'``,` ```PRIMARY` `KEY` `(`id`) USING BTREE,` ```UNIQUE` `KEY` ``refund_no` (`refund_no`) USING BTREE,` ```KEY` ``idx_order` (`order_no`),` ```KEY` ``idx_mer_create_time` (`mer_code`,`create_time`),` ```KEY` ``idx_store` (`mer_code`,`online_store_code`)` `) ENGINE=InnoDB AUTO_INCREMENT=1``DEFAULT` `CHARSET=utf8mb4``COLLATE``=utf8mb4_general_ci ROW_FORMAT=``DYNAMIC` `COMMENT=``'退款单模型表'``;` |  |
|  | CREATE TABLE `refund_detail_model` (  `id` bigint NOT NULL AUTO_INCREMENT,  `refund_no` bigint NOT NULL COMMENT '退款单号',  `commodity_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',  `commodity_count` int NOT NULL COMMENT '退款数量',  `bill_price` decimal(16,4) NOT NULL COMMENT '下账价格',  `is_gift` tinyint NOT NULL DEFAULT '0' COMMENT '是否是赠品（0不是赠品，1是赠品）',  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',  PRIMARY KEY (`id`) USING BTREE,  KEY `index_no` (`refund_no`) USING BTREE COMMENT '退款款单索引' ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款明细模型表' |  |
|  | `CREATE` `TABLE` ``order_commodity_detail_cost_price` (` ````id```bigint` `NOT` `NULL` `AUTO_INCREMENT,` ````service_mode```varchar``(10)``COLLATE` `utf8mb4_unicode_ci``DEFAULT` `NULL` `COMMENT``'服务模式(O2O、B2C)'``,` ````order_no```bigint` `NOT` `NULL` `COMMENT``'订单号(B2C是oms_order_no,O2O是order_no)'``,` ````erp_code```varchar``(50)``COLLATE` `utf8mb4_unicode_ci``DEFAULT` `NULL` `COMMENT``'商品erp编码'``,` ````make_no```varchar``(100)``COLLATE` `utf8mb4_unicode_ci``DEFAULT` `NULL` `COMMENT``'商品批号'``,` ````batch_no```varchar``(100)``COLLATE` `utf8mb4_unicode_ci``DEFAULT` `NULL` `COMMENT``'商品批次'``,` ````cost_price```decimal``(16, 6)``DEFAULT` `NULL` `COMMENT``'商品成本价'``,` ````average_price```decimal``(16, 6)``DEFAULT` `NULL` `COMMENT``'商品不含税加权成本价'``,` ````tax_price```decimal``(16, 6)``DEFAULT` `NULL` `COMMENT``'商品含税加权成本价'``,` ````tax_rate```varchar``(64)``COLLATE` `utf8mb4_unicode_ci``DEFAULT` `NULL` `COMMENT``'税率'``,` ````create_time` datetime``DEFAULT` `CURRENT_TIMESTAMP` `COMMENT``'创建时间'``,` ````modify_time` datetime``DEFAULT` `CURRENT_TIMESTAMP` `ON` `UPDATE` `CURRENT_TIMESTAMP` `COMMENT``'修改时间'``,` ```PRIMARY` `KEY` `(`id`),` ```KEY` ``index_erp_code` (`erp_code`),` ```KEY` ``index_order_no` (`order_no`)` `) ENGINE = InnoDB AUTO_INCREMENT = 1``DEFAULT` `CHARSET = utf8mb4``COLLATE` `= utf8mb4_unicode_ci ROW_FORMAT =``DYNAMIC` `COMMENT =``'订单商品明细成本价'` |  |


#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 备注 |
| --- | --- | --- | --- |
| hydee-business-order |  | `message-notify:` ```order-model-topic: TP_ORDER_BUSINESS-ORDER_ORDER-MODEL` ```refund-order-model-topic: TP_ORDER_BUSINESS-ORDER_REFUND-ORDER-MODEL` |  |


#### 2.3 网关配置变更

| 变更内容 | 修改内容 | 备注 |
| --- | --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |


#### 2.5 MQ

| MQ类型 | Tag | MQ名称 | 备注 |
| --- | --- | --- | --- |
| RocketMQ | TAG_ORDER_MODEL | ``` TP_ORDER_BUSINESS-ORDER_ORDER-MODEL ``` | 线上单正单topic |
| RocketMQ | TAG_REFUND_ORDER_MODEL | ``` TP_ORDER_BUSINESS-ORDER_REFUND-ORDER-MODEL ``` | 线上单退单topic |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  | ParseConfig | Aurora_ParseConfig | "B2cStoreMap":{  "5737_D100": "edca6231d45449829ccce1114e7d50ee",  "17443932": "aa2d6de522074d4280d09ea94f85e06f",  "16648783": "4d5025a7286f4423b3f3bf8ab5594297",  "21252747": "91b6709fab3e427cb9c7602df8ec3e52",  "21239418": "3f7c518923e548e5ac3fc6e8f31fbae0",  "16342645": "de0cf57630d743a1891e47d7a468487f",  "14421827": "83fe15fb0c0b43469b2276a13e212dc7",  "13781856": "79c08768179844fe848288e8bf60878f",  "19786198": "f7ce86cec17f4462adce92f8a7d3373c",  "13808912": "7e37d69a6ef64d13817111ae4f3f879f",  "17489945": "11f75f47651e43b094e593a4e277a7d5",  "16223941": "5a216d92736a49c0b1e69c73ffec8c39",  "13781664": "c5b3ff4929274602a72b07d665a13a06" } |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |