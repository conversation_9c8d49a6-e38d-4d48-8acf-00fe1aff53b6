# 【20250402】 处方单打印小票和提醒延后

# 一、业务背景

## 1.1 业务背景

1.当前存在O2O订单处方审核不通过，门店依旧售卖处方药的情况，这个现象属于违规操作，被有关部门查处会被重罚，系统需要对此现象进行干预。

2.当前O2O处方订单需要经历互联网医院医师开方、互联网医院药师一审、药店药师二审，这三个步骤。

步骤一和二各平台均一致，只有步骤三“药店药师二审“各平台有差异：

## 1.2 痛点分析

1. 处方单审核异常后，如果药品被送出将会出现合规风险。


## 1.3系统现状

1. 系统目前没有强制控制处方单流程


# 二、需求分析

****

# 四、整体设计

## 4.1 详细设计

- ****此次改造影响 京东到家、饿了么 、快手 三个平台020 处方单
- 增加代码开关[print-sound-delay: true]可以切换此次的处方单合规控制，当开关设置为true应用本次新逻辑， false 则不强制控制处方单小票打印以及语音播报
- 020 订单处理，待审方table页增加提示信息


****

- 取消门店配置中处方订单审方选项：【拣货复合校验】，且需要二审的平台将不可以修改选项。默认为审方后作业。


- 当订单处于待审方状态时不允许打印小票。列表页屏蔽打印按钮


- 订单创建后如果是需要审方订单，就不再推送新订单消息以及来单自动打印小票。
- 药师云审核通过后，出发新订单语音播报以及来单小票自动打印。
- 上线后将京东到家、饿了么、快手020 门店处方订单审核全部改为【审方后作业】


# 五、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 六、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 七、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 八、上线方案

1、兼容、回滚方案等

- 上线后如果门店反馈强烈，影响到门店正常作业则关闭开关，设置 print-sound-delay: false
- 设置门店处方审核未不审核处方



2、上线流程、SOP等