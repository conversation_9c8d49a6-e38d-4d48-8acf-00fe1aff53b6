# 2024-05-28-1 checkList

### 一、上线内容

| 需求名称 | 上线服务 | 上线顺序 | 开发分支 | RD | CR人员 | PM | QA | 测试报告 | 上线开始时间 | 上线结束时间 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| [[ORDER-1365] refund表中冗余service_mode 和 organization_code字段 - 一心数科数字化产研中心-Scrum (hxyxt.com)](https://jira.hxyxt.com/browse/ORDER-1365) | hydee-business-order | 1 | dev |  |  | 徐凯 | 杨飞 |  | 2024-05-28 |  |
| hydee-business-order-web | 2 |
| hydee-business-order-b2c-third | 3 |
| [[ORDER-1454] 处方图片异常丢失处理 - 一心数科数字化产研中心-Scrum (hxyxt.com)](https://jira.hxyxt.com/browse/ORDER-1454) | ds-service-mt | 4 | feature-order-1454 |  |  | 徐凯 | 杨飞 |  | 2024-05-28 |  |
| ds-service-eb | 5 |


### 二、配置变更

#### 2.1 数据库变更

ALTER TABLE `dscloud`.`refund_order` 
ADD COLUMN `organization_code` varchar(40) NULL COMMENT '线下门店编码' AFTER `extend_info`,
ADD COLUMN `online_store_code` varchar(40) NULL COMMENT '线上门店编码' AFTER `organization_code`,
ADD COLUMN `service_mode` varchar(10) NULL COMMENT '服务模式' AFTER `online_store_code`,
ALGORITHM=INPLACE,LOCK=NONE;

ALTER TABLE `dscloud`.`refund_order`
DROP INDEX `idx_merCode_state_erpState`, 
ADD INDEX `idx_merCode_state_erpState`(`mer_code`, `organization_code`, `state`, `erp_state`) USING BTREE,
ADD INDEX `idx_store`(`mer_code`, `online_store_code`),
ALGORITHM=INPLACE, LOCK=NONE;

#### 2.2 appoll配置变更

| 服务 | 配置文件 | key | 变更内容 |
| --- | --- | --- | --- |


#### 2.3 网关配置变更

| 变更内容 | 备注 |
| --- | --- |


#### 2.4 xxl-job配置变更

| 任务描述 | 执行器 | JobHandler | Cron | 变更点 | 任务参数 | 备注 |
| --- | --- | --- | --- | --- | --- | --- |
| 刷退款表历史数据job | 订单中台 | ``` replenishRefundOrganizationJobHandler ``` | 每天晚上1-5店=点执行0 0/5 1-5 * * ? | 新增 | { "batchNum":5, "batchSize":2000, "sleepSecond":3, "endTime":"2024-05-28 23:59:59" } |  |


#### 2.5 MQ

| MQ类型 | MQ名称 | 备注 |
| --- | --- | --- |


#### 2.6 nacos

| namespace | Group | DataId | 配置内容 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |


2.7 其它配置变更

| 内容 | 变更点 | 备注 |
| --- | --- | --- |
|  |  |  |


### 三、上线影响

| 上线内容 | 影响范围 | （如出现问题）回滚方案及影响 |
| --- | --- | --- |


### 四、上线SOP

| checkList | 是否完成 | 备注 |
| --- | --- | --- |
| dev配置变更 | 16 incomplete |  |
| dev自测 | 17 incomplete |  |
| 代码CR | 18 incomplete | CR人员：产研测，后续贴PR地址 |
| test配置变更 | 19 incomplete |  |
| test测试 | 4 incomplete |  |
| 测试报告 | 20 incomplete |  |
| Master打tag | 21 incomplete |  |
| 代码合并Master | 22 incomplete |  |
| 预发配置变更 | 23 incomplete |  |
| 预发验证 | 24 incomplete |  |
| 依赖check | 6 incomplete |  |
| 上线周知产研 | 30 incomplete |  |
| 生产配置变更 | 25 incomplete |  |
| 生产发布 | 26 incomplete |  |
| 生产验证 | 27 incomplete |  |
| 日志、告警观察 | 28 incomplete |  |
|  |  |  |