# 【20250226】 拼多多对接方案

# 一、业务背景

## 1.1 业务背景

1. 拼多多订单目前是通过海典做数据转发，不能满足当下需求，如无法合并发货，金额分摊不够精细等，通过商家自研的方式接入拼多多系统


## 1.2 痛点分析

1. 法合并发货，金额分摊不够精细


# 二、需求分析

## 2.1 业务流程

### 正向单接入流程

****

# 三、目标

**3.1 三月中旬完成功能上线**

# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图

# 五、详细设计

## 5.1 详细功能

| 新订单消息 |  | 监听新的订单消息，自动创建平台订单，再创建系统订单 | [https://open.pinduoduo.com/application/document/message?secondCatId=1&id=6](https://open.pinduoduo.com/application/document/message?secondCatId=1&id=6) |
| 订单完成消息 |  | 监听完成消息，平台订单状态变成已完成 | [https://open.pinduoduo.com/application/document/message?secondCatId=1&id=4](https://open.pinduoduo.com/application/document/message?secondCatId=1&id=4) |
| 风控解除消息 |  | 监听风控消息，风控解除创建新的订单 | [https://open.pinduoduo.com/application/document/message?secondCatId=1&id=30](https://open.pinduoduo.com/application/document/message?secondCatId=1&id=30) |
| 查询订单 |  | 查询订单明细 | [https://open.pinduoduo.com/application/document/api?id=pdd.order.information.get](https://open.pinduoduo.com/application/document/api?id=pdd.order.information.get) |
| 查询订单列表 |  | 查询订单列表 | [https://open.pinduoduo.com/application/document/api?id=pdd.order.list.get](https://open.pinduoduo.com/application/document/api?id=pdd.order.list.get) |
| 获取店铺可用快递 |  | 查询店铺签约快递信息 | [https://open.pinduoduo.com/application/document/api?id=pdd.waybill.search](https://open.pinduoduo.com/application/document/api?id=pdd.waybill.search) |
| 获取电子面单 |  | 获取电子面单接口 | [https://open.pinduoduo.com/application/document/api?id=pdd.waybill.get](https://open.pinduoduo.com/application/document/api?id=pdd.waybill.get) |
| 获取物流轨迹 |  | 获取物流轨迹 | [https://open.pinduoduo.com/application/document/api?id=pdd.logistics.ordertrace.get](https://open.pinduoduo.com/application/document/api?id=pdd.logistics.ordertrace.get) |
| 取消面单 |  | 取消面单接口 | [https://open.pinduoduo.com/application/document/api?id=pdd.waybill.cancel](https://open.pinduoduo.com/application/document/api?id=pdd.waybill.cancel) |
| 修改电子面单 |  | 修改电子面单 | [https://open.pinduoduo.com/application/document/api?id=pdd.waybill.update](https://open.pinduoduo.com/application/document/api?id=pdd.waybill.update) |
| 订单合并获取 |  | 获取这一堆订单的合并发货 | 先接入再联调 |
| 发货接口 |  | 订单发货通知平台已发货 | [https://open.pinduoduo.com/application/document/api?id=pdd.logistics.online.send](https://open.pinduoduo.com/application/document/api?id=pdd.logistics.online.send) |
| 售后消息 |  | 监听售后消息，创建的对应的售后单 | [https://open.pinduoduo.com/application/document/message?secondCatId=4&id=10](https://open.pinduoduo.com/application/document/message?secondCatId=4&id=10) |
| 售后单关闭消息 |  | 监听售后单关闭，关闭售后单 | [https://open.pinduoduo.com/application/document/message?secondCatId=4&id=15](https://open.pinduoduo.com/application/document/message?secondCatId=4&id=15) |
| 买家退货消息 |  | 退货退款流程中，同意退货，买家回寄退货 | [https://open.pinduoduo.com/application/document/message?secondCatId=4&id=12](https://open.pinduoduo.com/application/document/message?secondCatId=4&id=12) |
| 退款同意消息 |  |  |  |
| 售后单详情接口 |  | 获取售后单详情 | [https://open.pinduoduo.com/application/document/api?id=pdd.refund.information.get](https://open.pinduoduo.com/application/document/api?id=pdd.refund.information.get) |
| 同意退款 |  | 仅退款，接收到售后单，可直接同意退款；退货退款后，同意退货，再同意退款时调用； | [https://open.pinduoduo.com/application/document/api?id=pdd.refund.agree](https://open.pinduoduo.com/application/document/api?id=pdd.refund.agree) |
| 同意退货 |  | 同意退货，用户申请退货退款，商家同意退货调用此接口 | [https://open.pinduoduo.com/application/document/api?id=pdd.refund.returngoods.agree](https://open.pinduoduo.com/application/document/api?id=pdd.refund.returngoods.agree) |


## 5.2 接口设计

## 5.3 涉及数据库

## 5.4 安全设计

## 5.5监控报警

## 5.6 问题

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年11月27日;**

**研发时间：2024年11月28日-2024年12-17日**

**测试时间：2024年12月17日-2024年12-24日**

**上线时间：2024年12-27日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等