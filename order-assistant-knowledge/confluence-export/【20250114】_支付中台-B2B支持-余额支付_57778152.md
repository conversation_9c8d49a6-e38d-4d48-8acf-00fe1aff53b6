# 【20250114】 支付中台-B2B支持-余额支付

# 一、业务背景

## 1.1 业务背景

 满足B2B余额支付场景需求。

## 1.2 系统现状

****

# 二、需求分析

## 2.1 业务流程

****

# 三、目标

## 3.1 本期目标

实现产品业务需求，为B2B提供余额支付能力。

分为四部分

1. 支付、退款功能
2. 兼容旧版本支付流水
3. 余额支付流水
4. 支付配置


# 四、整体设计

## 4.1 支付流程图

**true1falseautotoptrue135910**

# 五、详细设计

## 5.1 详细模块设计

### 5.1.1 支付状态扭转逻辑

true未命名绘图falseautotoptrue5221

### 5.1.2 状态设计

true支付状态2falseautotoptrue3511

true退款状态2falseautotoptrue3511

### 5.1.3 支付类型

#### 5.1.3.1 系统认证支付

true系统支付falseautotoptrue6112

#### 5.1.3.2 账号认证支付

流程图如上

## 5.2 表设计

true支付配置表falseautotoptrue6733

## 5.3 配置技术设计

### 5.3.1 公司维度配置

true公司维度配置falseautotoptrue3812

### 5.3.2 门店维度配置

根据APPID查询所有配置，门店关联配置

### 5.3.3 新增加盟店配置自动关联

true新增加盟店自动配置falseautotoptrue2813

## 5.4 停用余额支付设计

全部关闭余额支付：通过服务配置可以控制全部服务余额支付关闭

门店关闭余额支付：通过控制支付方式授权来控制。关闭余额支付后，只会影响查询支付渠道。支付过程不做任何处理。

## 5.4 支付、退款方式

5.4.1 单门店单订单支付（done）

5.4.2 单门店多订单支付

5.4.3 多门店多订单支付

5.4.4 单门店单订单部分退款（done）

5.4.5 单门店单订单全额退款（done）

5.4.6 单门店多订单部分退款（done）

5.4.7 单门店多订单全额退款（done）

5.4.8 多门店多订单部分退款

5.4.9 多门店多订单全额退款

## 5.4 接口设计

见swagger

## 5.5 安全设计

该版本服务之间没有安全设计。

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2025年01月17日;**

**研发时间：2025年01月20日-2024年02-21日**

**测试时间：2025年02月24日-2025年03-10日**

**上线时间：2025年03-12日**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等

十、会议遗留问题

2025-01-14

1. MDM变更只监听门店删除和新增消息，所有组织机构查询走MDM接口。消息处理新增支付商户相关配置。
2. 余额流水走 支付中台接口
3. 心云支付流水和退款流水做成新页面
4. 合单退款，需要跟产品沟通，能不能多条退款; 回复：不能。