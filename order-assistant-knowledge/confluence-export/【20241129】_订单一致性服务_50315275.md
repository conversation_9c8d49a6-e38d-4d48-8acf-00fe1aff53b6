# 【20241129】 订单一致性服务

# 一、业务背景

## 1.1 业务背景

心云系统存在订单如下问题：

1.020 、B2C丢单问题 因为预警告警不足 没有差异化对比导致丢失得订单不能及时发现

2.D-ERP会涉及到发版，发版期间的推送给D-ERP订单会丢失，也会有卡单得情况需要重新补推订单到D-ERP

以上两点问题，需要业务小伙伴通知，系统不能自动告警，自动补单
基于以上两点问题需开发订单校验工具 自动补单，定时生成差异化列表进行分析

## 1.2 痛点分析

以上两点问题，需要业务小伙伴通知，系统不能自动告警，自动补单，导致发现问题不及时，导致订单作业超时，

## 1.3 系统现状

**上游：主要针对与订单来源**

| 订单类型 | 订单来源/流向 | 备注/说明 |
| --- | --- | --- |
| 线上--020 订单 | 三方平台（MT/ELM/JDDJ/DY等）--->新接口中台（yxt-third-platform）--MQ--->心云订单中台（hydee-business-order） | 推送 |
| 线上--B2C 订单 | 海典--->心中订单中台（hydee-business-order-b2c-third） | 定时任务拉取，抖店，快手走得新接口中台，其他走的海典 |
| 线下订单--科传 | 通过接口上传 门店--> order-service --> order-sync |  |
| 线下订单--海典: | MQ同步 信息中心 --> order-sync |  |


 1.线上O2O订单当前消息扭转流程

trueO2O订单推送falseautotoptrue9512

当前O2O订单丢失补单操作：往往在该处补推时订单早已做业超时 或在平台处理

2.线上B2C订单当前消息扭转流程

trueB2Cfalseautotoptrue6611

当前B2C订单丢失补单操作

**下游：主要针对与线上订单到D-ERP进行下账**

| 订单类型 | 下账方式 |
| --- | --- |
| 科传 | 科传定时任务拉取 |
| 海典 | 接口推送 |


重推方式：等待下游反馈卡单界面重推/或者系统升级需要重推

# 

# 二、需求分析

## 2.1 业务流程

****

## 2.2 整体架构图

# 三、目标

**3.1 本期目标**

**-->实现O2O自动补单**

**-->实现O2O订单差异报表**

**-->优化B2C自动补单**

**-->实现B2C订单差异报表**

**-->补单失败告警**

**3.2 远期目标**

**-->形成可视化处理界面 处理**

**-->**新订单运行架构全公域全链路

**-->预警策略**

# 四、整体设计

## 4.1 统一语言定义

## 4.2 流程图–上游（MT、JD、ELM等三方）

1.线上O2O订单自动补单流程（流程图需修改，以平台为准，补单时间定时任务 问题，峰值平均分配）

trueO2O订单自动补单流程falseautotoptrue11016

说明：

**直接：抓取心云中台库订单后直接与平台比忽略芒果**

**2.O2O订单凌晨跑差（每天凌晨一次）**

trueO2O订单凌晨跑差falseautotoptrue5413

**3.B2C订单补单逻辑**

trueB2C 原订单补单falseautotoptrue7901

**4.B2C订单凌晨跑差（每天凌晨一次）**

B2C 大部分集中在拼多多和京东

trueB2C订单跑差异falseautotoptrue5411

## 4.3 流程图–下游（海典/科传）

**trueerpfalseautotoptrue7711**

**说明 ：需要海典\科传提供对应接口**

重构后的远期目标

# 五、详细设计

## 5.1 详细模块设计

## 5.2 接口设计

## 5.3 涉及数据库

## 5.4 安全设计

## 5.5监控报警

**嵌入小时标准差公式报警、**

****

**JAVA 标准差公式**

**小时单量+2倍标准差< 平均值=报警（80+（2*7.07）< 90）**

| ``` public static void main(String[] args) {     int[] numbers = {95, 85, 75, 65, 55,45};     int[] numbers2 = {80, 85, 90, 95, 100};     System.out.println("Standard Deviation: " + calculateStandardDeviation(numbers2));     // 平均值=90 标准差=7.07 } public static double calculateStandardDeviation(int[] numbers) {     int mean = 0;     int sum = 0;     // Step 1: Calculate mean     for (int number : numbers) {         mean += number;     }     mean /= numbers.length;     // Step 2: Calculate sum of squared differences     for (int number : numbers) {         sum += Math.pow(number - mean, 2);     }     // Step 3: Calculate standard deviation     return Math.sqrt(sum / numbers.length); } ``` |
| --- |
|  |


问题

| 标准差数据来源方式 | SQL | 数据量 | 问题 |
| --- | --- | --- | --- |
| 按照门店 渠道 小时 单量统计（前7天） | SELECT  online_store_code,  DATE_FORMAT( created, '%y-%m-%d %H' ) AS HOUR,  COUNT(*) AS count  FROM  `order_info`  WHERE  created > '2024-12-01 23:59:59'   AND created < '2024-12-09 00:00:00'  GROUP BY  HOUR,  online_store_code | 217338 | 数据量大统计时间长，每小时更新对应门店小时标准差 |
| 照门店 天数 单量统计 | SELECT  online_store_code,  DATE_FORMAT( created, '%y-%m-%d' ) AS day,  COUNT(*) AS count  FROM  `order_info`  WHERE  created > '2024-12-01 23:59:59'   AND created < '2024-12-09 00:00:00'  GROUP BY  day,  online_store_code | 42166 | 数据量大统计时间长，每天更新对应门店标准差 |


**缓存设计： redis 中使用 json 存储**

**KEY：门店-渠道 value小时标准差集合**

| 字段名称 | hour | standarddeviation |
| --- | --- | --- |
| 字段说明 | 小时（0-23） | 标准差数量 |


**缓存结构**

| C087:24-->[{"hour": 0, "standarddeviation": 100},{"hour": 1, "standarddeviation": 100},{"hour": 2, "standarddeviation": 100},{"hour": 3, "standarddeviation": 100},{"hour": 4, "standarddeviation": 100}] |
| --- |


## 5.6 问题

**针对限流 问题引入**

定时任务

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：**

**研发时间：**

**测试时间：**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等