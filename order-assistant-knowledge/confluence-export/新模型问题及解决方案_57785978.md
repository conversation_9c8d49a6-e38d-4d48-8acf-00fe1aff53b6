# 新模型问题及解决方案

## 1.新模型通用词典

### 心云正单

| 心云正单 |
| --- |
| 订单主状态: order_main_status |
| 订单支付状态: payment_status |
| 订单发货状态: delivery_status |
| 订单售后状态: after_sale_status |
| 订单退款状态: refund_status |
| 订单退货状态: return_status |
| 处方单二审核状态:prescription_audit_status |


| 订单主状态:order_main_status |  | 私域/公域O2O | 私域/公域B2C | B2B平台 | 线下单 | 服务单 | 预约单 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| **创单：CREATED** |  | 待支付/待接单 | 待支付/待接单 | 待支付 |  | 待支付 | 待支付 | 用户已完成订单信息填写并提交，但尚未进行支付等后续操作。 |
| **已支付：Paid** |  | 待接单 | 待接单 | 待接单 |  |  |  | 用户成功完成订单支付，支付款项已到账或处于确认到账状态。 |
| **已接单: Accepted** |  | 待拣货 | 待拣货 | 待拣货 |  | 待使用 | 待拣货 |  |
| **已拣货: Picked** |  | 待配送 | 待配送 | 待配送 |  |  | 待发货 |  |
| **已发货：Shipped** |  | 配送中 | 配送中 | 配送中 |  | 部分核销 | 已发货 | 商家已将商品发出，订单进入物流运输阶段。 |
| **已送达：Received** |  | 已送达 | 已送达 | 已送达 |  |  | 已送达 | 商品已送到顾客/指定位置 |
| **已完成: DONE** |  | 已完成 | 已完成 | 已完成 | 已完成 | 已完成 | 已完成 | 确认收货. |
| **已取消：Canceled** |  | 已取消 | 已取消 | 已取消 |  | 已取消 | 已取消 | **订单已取消**- **含义**：通常是指用户或商家主动采取行动，终止了订单的执行过程。 |
| **已关闭: Closed** |  | 已关闭 | 已关闭 | 已关闭 |  |  | 已关闭 | **订单已关闭**- **含义**：更多地表示订单已经完成了一个生命周期，到达了结束状态，但不一定是因为主动的取消操作。 在订单流程自然结束或因系统原因等被动结束时使用。 |


| 订单支付状态: payment_status |  |  |  |
| --- | --- | --- | --- |
| 未支付: UNPAY |  |  |  |
| 部分支付: PART_PAY |  |  |  |
| 已支付: PAID |  |  |  |
| 支付失败: PAY_FAIL |  |  |  |


| 订单发货状态: delivery_status | O2O | B2C |  |
| --- | --- | --- | --- |
| 待审核: WAIT_VERIFY | 待确认转单 | 待确认转单 |  |
| 待发货: WAIT | 待配送 | 待发货 |  |
| 部分发货: PART_**Shipped** | 部分发货 | 部分发货 |  |
| 待呼叫 | 带呼叫 |  |  |
| 待接单 | 待接单 |  |  |
| 运输中: **Shipped** | 配送中 | 配送中 |  |
| 已签收:**Received** | 已签收 | 已签收 |  |


| 订单售后状态: after_sale_status |  |  |  |
| --- | --- | --- | --- |
| 初始值:null |  |  |  |
| 待审核: WAIT_VERIFY |  |  |  |
| 审核通过,处理中:**passed** |  |  |  |
| 售后完成---------通知平台: **completed** |  |  |  |
| 售后拒绝---------通知平台:REJECTED |  |  |  |
| 取消售后---------自主取消售后请求:**Canceled** |  |  |  |


### 心云逆单 todo:

| 心云逆单 |
| --- |
| 订单售后状态: after_sale_status |
| 订单退货状态: return_status |
| 订单退款状态: refund_status |


| 订单售后状态: after_sale_status |  |  |  |
| --- | --- | --- | --- |
| 待审核:WAIT_VERIFY |  |  |  |
| 审核通过-------:**passed** |  |  |  |
| 售后完成---------通知平台:**completed** |  |  |  |
| 售后拒绝---------通知平台:REJECTED |  |  |  |
| 取消售后---------自主取消售后请求:**Canceled** |  |  |  |


| 订单退货状态: return_status | 物流 | 外卖 | 自送 |
| --- | --- | --- | --- |
| 初始值:null |  |  |  |
| 待退货:WAIT_RETURN | 带回写物流单号 | 待回写收货码 | ----- |
| 退货运输中:RETURN_SHIPPED |  |  | ------ |
| 退货完成: **completed** |  |  |  |
| 退货拒绝: REJECTED |  |  |  |
| 取消:---------自主取消售后请求 |  |  |  |


TODO: 增加-退货途径



| 订单退款状态: refund_status |  |  |  |
| --- | --- | --- | --- |
| 初始值:null |  |  |  |
| 退款中:REFUNDING |  |  |  |
| 退款完成:COMPLETED |  |  |  |
| 退款失败:REFUND_FAIL |  |  |  |
| 取消:---------自主取消售后请求 |  |  |  |


# 数据库表

| 问题描述 | 发起人 | 跟进人 | 解决方案 | 备注 |
| --- | --- | --- | --- | --- |
| offline_order中是否需要增加一个字段用于标识 【线上线下订单】？ |  |  | transaction_channel : 取值为 “online” 代表线上交易，“offline” 代表线下交易 |  |
| offline_order中是否需要增加一个字段标识 O2O、B2C、B2B？线下订单是不是可以用”**L2L**“来表示？ |  |  | transaction_channel +business_type 识别线下O2O | L2L：Local to Local |
| offline_order中缺少【线上门店】关联字段，存储 ds_online_store 的id？还是像之前存储 三方平台后台 维护的门店编码？ |  |  | 增加  `online_store_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线上店铺编码',  `online_store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线上店铺名称', |  |
| offline_order缺少买家备注 |  |  | 增加  `buyer_message` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '买家留言',  `seller_remark` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '卖家备注', |  |
| 下账单是否需要保存下账配置id？ |  |  | 存下账配置唯一码. 不存主键id 增加  `account_config_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下账配置唯一码', |  |
| delivery_order 中存储了收货人姓名信息，delivery_order_ext 中存储了收货人地址信息，是否可以直接关联offline_order_delivery_address？ |  |  | 不关联, 按域操作. |  |
| 缺少配送信息表（类似于 order_delivery_record），主要是用于关联配送网店和配送门店 |  |  | 属于物流中台领域. |  |
| 订单明细换货，需要单独一张表还是像原来的order_detail一样增加swap_id? |  |  | 讨论???? |  |
| 订单明细缺少是否为医保商品标识 |  |  | 增加  `mic_mark` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保商品标识. TRUE、FALSE', |  |
| 缺少订单详细金额信息，offline_order_pay应该是支付类型明细，目前我们涉及到的金额有以下：  @ApiModelProperty("客户实付")   private BigDecimal buyerActualAmount;    @ApiModelProperty("商家实收")   private BigDecimal merchantActualAmount;    @ApiModelProperty("交易佣金")   private BigDecimal brokerageAmount;    @ApiModelProperty("商品总金额")   private BigDecimal totalAmount;    @ApiModelProperty("应收配送费")   private BigDecimal deliveryAmount;    @ApiModelProperty("应收包装费")   private BigDecimal packAmount;    @ApiModelProperty("商家配送费")   private BigDecimal merchantDeliveryAmount;    @ApiModelProperty("商家订单级总优惠")   private BigDecimal merchantOrderDiscountAmount;    @ApiModelProperty("商家配送费优惠")   private BigDecimal merchantDeliveryDiscountAmount;    @ApiModelProperty("商家商品总优惠")   private BigDecimal merchantCommodityDiscountAmount;    @ApiModelProperty("平台配送费")   private BigDecimal platformDeliveryAmount;    @ApiModelProperty("平台订单级优惠汇总")   private BigDecimal platformOrderDiscountAmount;    @ApiModelProperty("平台配送费优惠")   private BigDecimal platformDeliveryDiscountAmount;    @ApiModelProperty("平台商品优惠金额")   private BigDecimal platformCommodityDiscountAmount;    @ApiModelProperty("医保金额")   private BigDecimal medicareAmount;    @ApiModelProperty("剩余交易佣金(实时)")   private BigDecimal remainBrokerageAmount; |  |  | 增加订单金额表  DROP TABLE IF EXISTS `offline_order_amount`; CREATE TABLE `offline_order_amount` (  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内部订单号,自己生成',  `actual_pay_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '实付金额 |待迁移至订单金额表',  `actual_collect_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '实收金额 |待迁移至订单金额表',  `actual_collect_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '实收金额 ',  `brokerage_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '交易佣金 ',  `total_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '商品总金额 ',   `delivery_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '应收配送费 ',  `pack_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '应收包装费 ',  `merchant_delivery_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '商家配送费 ',  `merchant_orderDiscount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '商家订单级总优惠 ',   `merchant_deliveryDiscount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '商家配送费优惠 ',  `merchant_commodityDiscount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '商家商品总优惠 ',  `platform_delivery_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '平台配送费 ',   `platform_order_discount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '平台订单级优惠汇总 ',  `platform_delivery_discount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '平台配送费优惠 ',  `platform_commodity_discount_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '平台商品优惠金额 ',  `medicare_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '医保金额 ',   `remain_brokerage_amount` decimal(16, 6) NULL DEFAULT NULL COMMENT '剩余交易佣金(实时) ',  `created` datetime NULL DEFAULT NULL COMMENT '平台创建时间',  `updated` datetime NULL DEFAULT NULL COMMENT '平台更新时间',  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',  `updated_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',  `sys_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '系统创建时间',  `sys_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '系统更新时间',  `version` bigint NULL DEFAULT NULL COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`)  ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic COMMENT='心云订单金额信息' ; |  |
| offline_order、delivery_order、account_order的对应关系？ |  |  | offline_order 1→多个 deliveryOrder  deliveryOrder 1→1 account_order |  |
| account_order中是否需要存储 支付方式 信息？可能会有多支付方式 |  |  | 增加account_order_pay 增加 account_refund_pay |  |
| 未来下账配置是否可能会有变化？需不需要在 account_order 存储平台的优惠信息？ |  |  | 暂时忽略?? |  |
| 退货单是否需要增加一个退货明细表？ |  |  | 增加 offline_return_order_detail |  |
| 缺少退款原因图片 |  |  | 增加  `third_reason_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台售后原因-文字',  `third_reason_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台售后原因-图片',   `reason_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后原因-文字',  `reason_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后原因-图片', |  |
| 订单表和售后单表缺少 onlineClientCode（线上网店编码） |  |  | 不保存,如需关系可现从配置表内取 |  |
| 售后单中需要新增字段：【业务类型】、【交易场景】、【线上店铺编码】、【线上店铺名称】  `transaction_channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易场景 online:代表线上交易 ,offline:代表线下交易',  `business_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务类型 O2O、B2C、B2B',  `online_store_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线上店铺编码',  `online_store_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线上店铺名称', |  |  | 增加了  `transaction_channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易场景 online:代表线上交易 ,offline:代表线下交易',  `business_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务类型 O2O、B2C、B2B',  但是线上店铺没加 默认以真实门店字段为主也就是  `organization_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属机构编码',  `organization_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属机构名称', |  |
| after_sale_order_detail_ext表中缺少 after_sale_order_detail_no 字段， |  |  | 已补充 |  |
| 售后明细中是否需要保存【商品图片】？目前很多场景都需要展示图片 |  |  | 暂定实时取商品中台 |  |
| 下账表 是否需要保存 【下账配置id】？ |  |  | 在下账单扩展表内有   `account_config_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下账配置唯一码', |  |
| 下帐表 缺少【下账时间】字段 |  |  | 已补充 |  |
| 售后单/退款单 是否像正单一样，新增一个金额表？涉及金额大概如下：  /**    * 退买家总金额    */   private BigDecimal consumerRefundAmount;    /**    * 商家退款总金额    */   private BigDecimal merchantRefundAmount;    /**    * 退平台优惠    */   private BigDecimal platformDiscountRefundAmount;    /**    * 退商家优惠    */   private BigDecimal merchantDiscountRefundAmount;    /**    * 退平台包装费    */   private BigDecimal platformPackRefundAmount;    /**    * 退商家包装费    */   private BigDecimal merchantPackRefundAmount;    /**    * 退平台配送费    */   private BigDecimal platformDeliveryRefundAmount;    /**    * 退商家配送费    */   private BigDecimal merchantDeliveryRefundAmount;    /**    * 退商家明细优惠    */   private BigDecimal merchantDetailDiscountRefundAmount;    /**    * 退平台明细优惠    */   private BigDecimal platformDetailDiscountRefundAmount;    /**    * 退佣金    */   private BigDecimal brokerageRefundAmount;    /**    * 退款商品总金额    */   private BigDecimal totalItemRefundAmount; |  |  | 已补充 |  |
| 缺少售后单审核日志表，涉及以下字段：/**    * 审核时间    */   private String checkTime;    /**    * 审核人id    */   private String checkerId;    /**    * 审核人名    */   private String checkerName;    /**    * 审核类型    */   private String checkType;    /**    * 审核结果    */   private String checkResult;    /**    * 审核备注    */   private String checkMark; |  |  | 不增加 1.依靠after_sale_order_ext的   `rejected_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因-文字',  `remark_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注', 2.依靠  `after_sale_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后单状态机 待审核:WAIT_VERIFY ; 审核通过-------:passed;,售后完成---------通知平台:completed; 售后拒绝---------通知平台:REJECTED; 取消售后---------自主取消售后请求:Canceled',   3.依靠  `reviewer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核员',  `reviewer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核员名字',  `reviewer_time` datetime NULL DEFAULT NULL COMMENT '审核时间', 依靠以上字段 存储审核最后一次结果  其他过程数据依赖 日志表记录. 可以先设计一个统一的一个操作记录表 抽象服务各个单据的操作. |  |
| offline_refund_order 缺少 after_sale_no 字段 |  |  | 已补充 |  |
| ``` 正单缺少 [配送时间类型] 和 [期望配送时间描述] 字段 `````` /**  * 配送时间类型  */ private Integer deliveryTimeType; /**  * 期望配送时间描述  */ private String deliveryTime; ``` |  |  | 已补充  `is_on_booking` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否参加预约单 TRUE、FALSE ',  `booking_time_start` datetime NULL DEFAULT NULL COMMENT '仅预约单有值,预约送达开始时间',  `booking_time_end` datetime NULL DEFAULT NULL COMMENT '仅预约单有值,预约送达结束时间', | 配送时间类型：预约单、即时单期望配送时间描述：预约送达时间范围的描述，如：2025-02-12 00:00:00~2025-02-12 23:59:00 |
| 售后明细  申请退货数量 实际退货数量 |  |  | 已补充, 仅售后单存在计划申请 和实际退货 金额以实际为准做计算   `is_on_booking` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否参加预约单 TRUE、FALSE ',  `booking_time_start` datetime NULL DEFAULT NULL COMMENT '仅预约单有值,预约送达开始时间',  `booking_time_end` datetime NULL DEFAULT NULL COMMENT '仅预约单有值,预约送达结束时间', |  |


业务逻辑



待讨论项



| 条目 |  |  |  |
| --- | --- | --- | --- |
| 1.审核状态是否融合进orderstatus中??? |  | 拆分  `prescription_two_review_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处方单二审核状态', |  |
| 2. 数据表中的deleted字段的实际业务意义需要重新思考，是否真的有必要? | sqlCREATE TABLE `deleted_data` (   `id` int NOT NULL AUTO_INCREMENT,   `business_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务单号',   `service_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '删除数据的服务名',   `database_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '数据库',   `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表',   `deleted_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '删除数据',   `created_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',   `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',   PRIMARY KEY (`id`),   KEY `idx_business_no` (`business_no`) USING BTREE COMMENT '业务单号',   KEY `idx_created_time` (`created_time`) USING BTREE COMMENT '创建时间',   KEY `idx_db_table` (`database_name`,`table_name`) USING BTREE COMMENT '数据库和表名' ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; | 主表deleted 修改  `is_valid` bigint NOT NULL DEFAULT '0' COMMENT '是否起效 1-起效 -1-未起效 ', |  |
| 讨论退货状态和退款状态是否 参考售后状态有用户自主取消的状态 |  | 不增加.售后单增加字段拒绝原因 : `rejected_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因-文字', |  |
| 订单明细换货，需要单独一张表还是像原来的order_detail一样增加swap_id? |  | 增加:详细见下  售后单类型增加 换货类型. 1.门店操作流程变化 门店接到用户换货请求 需生成售后单. 由售后单发起换货 售后单→影响 订单明细→影响发货单明细 2.信息存储.  2.1售后单主单存储 发货单号 仅换货类型时存储  `delivery_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发货单号,仅换货类型存在', 2.2售后单明细存储换货明细 A→B 增加swap_no  `swap_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换货后的心云售后单明细唯一号', 2.3order_detail  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明细状态 NORMAL-正常 exchange-已换货 exchanged-换货后',  `swap_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仅已换货有值.换货后的内部明细编号', 2.4 delivery_order_detail  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明细状态 NORMAL-正常 exchange-已换货 exchanged-换货后',  `swap_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仅已换货有值.换货后的内部明细编号', |  |