package com.yxt.order.assistant.server.knowledge.service;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.assistant.server.knowledge.cf.ConfluenceMarkdownExporter;
import com.yxt.order.assistant.server.knowledge.cf.config.CfPullConfig;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class KnowledgeServiceImpl implements KnowledgeService{

  @Resource
  private ConfluenceMarkdownExporter confluenceMarkdownExporter;


  @Override
  public void pullCfKnowledge(KnowledgeGroup knowledgeGroup) {
    String extendJson = knowledgeGroup.getExtendJson();
    CfPullConfig cfPullConfig = JsonUtils.toObject(extendJson, CfPullConfig.class);
    Assert.notNull(cfPullConfig, "CF 拉取配置不能为空");
    Assert.notNull(cfPullConfig.getRootPageId(), "CF 根页面 ID 不能为空");

    confluenceMarkdownExporter.exportPageAndChildrenToMarkdown(knowledgeGroup.getId(),cfPullConfig);



  }
}
