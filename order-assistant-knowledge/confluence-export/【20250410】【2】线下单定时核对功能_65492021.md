# 【20250410】【2】线下单定时核对功能

### Green已上线

### 需求文档

### JIRA

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-5102

### 项目

order-atom-service 主要是提供心云这边的数量、金额等统计数据

order-consistent-center

order-framework 添加共用mdmService

order-sync 消费补偿数据

### 分支

feature-reconciliation

- order-atom-service 基于该需求分支开发 【20250327】线下订单查询&线下订单数据同步异常监控,主要是使用其ES
- order-consistent-center 基于master分支拉取


### 版本

reconciliation-SNAPSHOT

### SDK

    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>reconciliation-SNAPSHOT</version>
    </dependency>
<dependency>
    <groupId>com.yxt.order.consistent.sdk</groupId>
    <artifactId>order-consistent-sdk</artifactId>
    <version>reconciliation-SNAPSHOT</version>
</dependency>

### Apollo

测试环境

```
order-consistent-center
```

spring:
  datasource:
    dynamic:
      primary: order_master
      strict: false
      datasource:
        order_offline:
          url: ******************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          druid:
            initial-size: 10
            min-idle: 10
            max-active: 50
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000


rocketmq:
  producer:
    group: order-consistent-center-group
  name-server: 10.4.3.242:9876;10.4.3.243:9876

mq:
  topic:
    # 生产消息
    producer:
      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD

infocenter:
  base-url: http://10.0.36.90  
  app-id: 20250301000001
  private-key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCkwpElJdKP6sqRD4ObQLgc7tO2tSayt+rGhKv4lZiLfvdndZwSJxEc5u92gv1XaRHt3nhWkTWBG+/ZlbCAHiddeUf1lWEsCxX+N5wqxobsRF0KpNUSa5+hFLGCCfUZ+Nfn7AniRQXGPchAYpJ7dkID9amloBSD8O8Jbn0Ss0KrSFWweDC6nKZICAQIx+1yKqFz7bRc9lKzeW6XihijK+7y/4MfUSB9sTU1D+b6mqgiv24yyRrSKzPBLrjv6KdFnVv0jeVPIWYMNa965fpo9YQngEM16ok/0v+9kHsjQcA7ht9FOg5DySOZSJ2Z+npgii1vNe1T/xkj2IsKp2AvOCwFAgMBAAECggEAfnY1MD0yvHJsHyG4U28+oj6SVFgBJwZR9yQYV4qDdvbycP/t1mUUFooPXXi3eeNU9q5e0ZtNZRcLZ4gk3IHIl0+i8xZAaqzrqaAHhzGwmCL1A1l3jlb7RIl8oiKAdfnxxrr+7yUphHshfIHsi0U+8IkcONuBQ7Cn2SZsa/EaCBsWn89l12AY+iW2w2L4KqbMjqZ7aBStRFhasdfyBargiz4pnjFbLK2uNBo6QnGHLMbzzfAgJW0PnOOfuMzCaOo2C7Iz5OD/Ej77Naq31vFECwkdGwCRjaKAsAmkHYyq640qvGlcZigQxWWRlW6PeVQkrbRdySTEaDfo5ewM0CfRwQKBgQDZF03HGYGIvPSE8svPJs/UvSIACYUxHfvhg+qvMeTKeWnJFjCfIPKnZG7GVMB6OWu9AeT0Y8bG+cXwJYW6QgU8yeLNRxQO/GombmAVo45+AP1EMl6dt5ooKs4FHelvJ7p3BSVEZp2fnYcG0YH1kbJJ0ulBFa4mFqxpuMrwqgji0QKBgQDCSi6KrKSy9GSOmN020ilfaCMxHUcOrXenDPuxvbZmjpwUJHs5CkFBHgnVzFUdckpzzlw/r5fyhwPVKQBrQzIEpHdHM9Rh6RoW3DJc2LHlVDONu9EHK5lbOiKj6KsuXPHLuvLDbGwgvhGbH8eYOUPCRv6p56e6GpSpMOYbO4f69QKBgQCb32+pFVc8pu+qxeowwt1XErXm8O8BZVYFhoJraJeuCZ87EwO2PiH8rtAa9598It6Ix2NtVnbTR2QoGcj7A4xfKVX/rploaTSOg5HZ96XIM02mOcRV2g6F6LYkVmXVueDYtIkdo9BpWuIosyVs/T/WYem5Iaf5ES6aemS8iFn5AQKBgA6GxVwQ9G983lfbDg8fP4CmpJrzeXCbf6q5ycfMS2r3lqva3muxXJely0507Jg7B14JO9R3KIE1nw+89Q8QAxldwp1MPsDjUNQMuqc+fG6NER2zwTksBVdQzW474zCgOmPlImmCHcOE8oQKqD21al+IN1o0u9GqUxsjUNEQbmZRAoGBAIY1ekxi3S7jWNJRT6IjIWAyDON6z5HNlX6SohcEhoZcNADDtnGvTxh5GOf5FBH6IWjHrRWhkBOh+0p0Hw8uLwdVrU5/aQ9SdjNpTxJScBwaIKCDY2+w61iklfBIgRvWWv18fRJU5uparsqnID+YGduUmQthvf9cQdGuxu3/BY8a
  sale-order-total-path: /api/yxzs/saleOrderTotal
  sale-order-details-path: /api/yxzs/saleOrderDetails
  sale-order-push-path: /api/yxzs/saleOrderPush

### 信息中心接口及鉴权

对账接口

truehttp://10.0.36.90/api/yxzs/saleOrderTotal


参数 json
{
    "checkId": "СHК20250424001",
    "checkType": "OFFLINE_ORDER",
    "companyCode": 1006,
    "storeCode": "H812",
    "createdStart": "2025-04-01 00:00:00",
    "createdEnd": "2025-04-23 00:00:00"
}

http://10.0.36.90/api/yxzs/saleOrderDetails

参数 json
{
    "checkId": "СHК20250424001",
    "checkType": "OFFLINE_ORDER",
    "companyCode": 1006,
    "storeCode": "H812",
    "createdStart": "2025-04-01 00:00:00",
    "createdEnd": "2025-04-23 00:00:00",
    "pageSize": 1 ,
    "pageNo": 1 
}

http://10.0.36.90/api/yxzs/saleOrderDetails

参数 json
{
    "checkId": "СHК20250424001",
    "checkType": "OFFLINE_ORDER",
    "companyCode": 1006,
    "storeCode": "H812",
    "createdStart": "2025-04-01 00:00:00",
    "createdEnd": "2025-04-23 00:00:00",
    "pageSize": 1 ,
    "pageNo": 1 ,
}

http://10.0.36.90/api/yxzs/saleOrderPush

参数 json
{
    "thirdOrderNo": "СHК20250424001",
    "companyCode": 1006
}

### 验签代码

truepackage com.yxt.order.atom.bootstrap;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

public class Main {
    public static void main(String[] args) throws Exception {

        /**
         * 使用驼峰命名法,然后
         */
        HashMap<String, String> params = new HashMap<>();
        params.put("appId", "20250301000001");
        params.put("charset", "utf-8");
        params.put("format", "json");
        params.put("version", "1.0");
        params.put("signType", "RSA2");
        params.put("timestamp", "2025-04-25 16:58:00");
        params.put("bizContent", "{\"thirdOrderNo\":\"1024120200000001\",\"companyCode\":1006}");
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCkwpElJdKP6sqRD4ObQLgc7tO2tSayt+rGhKv4lZiLfvdndZwSJxEc5u92gv1XaRHt3nhWkTWBG+/ZlbCAHiddeUf1lWEsCxX+N5wqxobsRF0KpNUSa5+hFLGCCfUZ+Nfn7AniRQXGPchAYpJ7dkID9amloBSD8O8Jbn0Ss0KrSFWweDC6nKZICAQIx+1yKqFz7bRc9lKzeW6XihijK+7y/4MfUSB9sTU1D+b6mqgiv24yyRrSKzPBLrjv6KdFnVv0jeVPIWYMNa965fpo9YQngEM16ok/0v+9kHsjQcA7ht9FOg5DySOZSJ2Z+npgii1vNe1T/xkj2IsKp2AvOCwFAgMBAAECggEAfnY1MD0yvHJsHyG4U28+oj6SVFgBJwZR9yQYV4qDdvbycP/t1mUUFooPXXi3eeNU9q5e0ZtNZRcLZ4gk3IHIl0+i8xZAaqzrqaAHhzGwmCL1A1l3jlb7RIl8oiKAdfnxxrr+7yUphHshfIHsi0U+8IkcONuBQ7Cn2SZsa/EaCBsWn89l12AY+iW2w2L4KqbMjqZ7aBStRFhasdfyBargiz4pnjFbLK2uNBo6QnGHLMbzzfAgJW0PnOOfuMzCaOo2C7Iz5OD/Ej77Naq31vFECwkdGwCRjaKAsAmkHYyq640qvGlcZigQxWWRlW6PeVQkrbRdySTEaDfo5ewM0CfRwQKBgQDZF03HGYGIvPSE8svPJs/UvSIACYUxHfvhg+qvMeTKeWnJFjCfIPKnZG7GVMB6OWu9AeT0Y8bG+cXwJYW6QgU8yeLNRxQO/GombmAVo45+AP1EMl6dt5ooKs4FHelvJ7p3BSVEZp2fnYcG0YH1kbJJ0ulBFa4mFqxpuMrwqgji0QKBgQDCSi6KrKSy9GSOmN020ilfaCMxHUcOrXenDPuxvbZmjpwUJHs5CkFBHgnVzFUdckpzzlw/r5fyhwPVKQBrQzIEpHdHM9Rh6RoW3DJc2LHlVDONu9EHK5lbOiKj6KsuXPHLuvLDbGwgvhGbH8eYOUPCRv6p56e6GpSpMOYbO4f69QKBgQCb32+pFVc8pu+qxeowwt1XErXm8O8BZVYFhoJraJeuCZ87EwO2PiH8rtAa9598It6Ix2NtVnbTR2QoGcj7A4xfKVX/rploaTSOg5HZ96XIM02mOcRV2g6F6LYkVmXVueDYtIkdo9BpWuIosyVs/T/WYem5Iaf5ES6aemS8iFn5AQKBgA6GxVwQ9G983lfbDg8fP4CmpJrzeXCbf6q5ycfMS2r3lqva3muxXJely0507Jg7B14JO9R3KIE1nw+89Q8QAxldwp1MPsDjUNQMuqc+fG6NER2zwTksBVdQzW474zCgOmPlImmCHcOE8oQKqD21al+IN1o0u9GqUxsjUNEQbmZRAoGBAIY1ekxi3S7jWNJRT6IjIWAyDON6z5HNlX6SohcEhoZcNADDtnGvTxh5GOf5FBH6IWjHrRWhkBOh+0p0Hw8uLwdVrU5/aQ9SdjNpTxJScBwaIKCDY2+w61iklfBIgRvWWv18fRJU5uparsqnID+YGduUmQthvf9cQdGuxu3/BY8a";
        String sign = sign(params, privateKey);
        System.out.println(sign);
    }


    /**
     * 签名
     * @param params 待签名参数
     * @param privateKey 私钥
     * @return
     * @throws Exception
     */
    public static String sign(Map<String, String> params, String privateKey) throws Exception {
        String data = mapToData(params);
        return  sign(data, privateKey);
    }



    /**
     * 将map参数排序转换字符串
     * @param params
     * @return
     */
    public static String mapToData(Map<String, String> params) {
        // 1. 按照键的 ASCII 顺序对参数进行排序
        Map<Object, Object> sortedParams = new TreeMap<>(params);
        // 2. 拼接排序后的参数
        String paramString = sortedParams.entrySet()
                .stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        return paramString;
    }

    /**
     * 通用签名方法，用于RSA和RSA2签名
     * @param data 待签名的数据
     * @param privateKey 私钥
     * @return 返回Base64编码的签名字符串
     * @throws NoSuchAlgorithmException 当指定的算法不存在时抛出
     * @throws InvalidKeySpecException 当密钥规范无效时抛出
     * @throws InvalidKeyException 当私钥无效时抛出
     * @throws UnsupportedEncodingException 当编码不被支持时抛出
     * @throws SignatureException 当签名过程中发生错误时抛出
     */
    public static String sign(String data, String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, UnsupportedEncodingException, SignatureException {
        // 清理私钥，去掉头部和尾部信息
        privateKey = replaceKey(privateKey);

        // 将Base64编码的私钥解码为字节数组
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKey);

        // 创建PKCS8EncodedKeySpec对象用于生成私钥
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);

        // 使用KeyFactory根据算法生成私钥
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(keySpec);

        // 创建Signature对象并初始化为签名模式
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(key);

        // 更新签名对象的数据
        signature.update(data.getBytes("UTF-8"));

        // 对数据进行签名
        byte[] signedData = signature.sign();

        // 返回Base64编码的签名字符串
        return Base64.getEncoder().encodeToString(signedData);
    }

    /**
     * 移除公钥/私钥中的标头、标尾及换行符
     *
     * @param key 密钥
     * @return 清理后的密钥
     */
    protected static String replaceKey(String key) {
        return key.replaceAll("-----BEGIN (.*)-----", "")
                .replaceAll("-----END (.*)-----", "")
                .replaceAll("\\s+", "");
    }
}

postman案例

truecurl --location 'http://10.0.36.90/api/yxzs/saleOrderPush' \
--header 'Content-Type: application/json' \
--data '{
  "appId": "20250301000001",
  "charset": "utf-8",
  "format": "json",
  "version": "1.0",
  "signType": "RSA2",
  "timestamp": "2025-04-25 16:58:00",
  "bizContent": "{\"thirdOrderNo\":\"1024120200000001\",\"companyCode\":1006}",
  "sign": "J/GzcA1PEQzDtFsdRa67LOhDzkpJg6tzdnK0Tme/kBcWM3sv2Sby7NcBogx48fyMwgeC+bXToR0e1bgJQKqU/VG1lqYJihDLCkTAIOxoR+2gerKposjLPilgWwei1vjTGNddohq69Vnw1uW7i5SevNxc1w0QDBn5eadKFDz5jFe+dnz9LM5eB0yoo2ci8LxkY3EvJ5nDPyeY4mMu7nQfi9zmg8iNe00QyqfYckfot4zlh5fVRJElFh1Iw+nSOaXaWkVITJWAP9FFt/Bl829aUXFznNQTy3/uuY4/rl7XFf+iHDSoAGHnEL2E2EZHW6Nr60+PXjGLAgga1apvLoYbvQ=="
}'

### 定时核对功能技术方案

接口定义: [https://doc.weixin.qq.com/doc/w3_AZ0AgQYfAIEiXh6yqqCRra112e1bm?scode=AOsAFQcYAAc0jxUNt7AZ0AgQYfAIE](https://doc.weixin.qq.com/doc/w3_AZ0AgQYfAIEiXh6yqqCRra112e1bm?scode=AOsAFQcYAAc0jxUNt7AZ0AgQYfAIE)

### 交互方案

**心云校验任务**

trueconsistency_checkfalseautotoptrue9593

**XXL-JOB**:

  3 incomplete 新增执行器   4 incomplete 新增任务  

consistencyCheckHandler 一致性检查Job
consistencyCompensateHandler 一致性检查补偿Job
failedTaskRetryHandler 失败的补偿明细,重试

### DB

**一致性检查任务表**

已知信息:

- 信息中心是按照公司分表的


  7 incomplete 上线的时候从测试环境取  

CREATE TABLE `consistency_check` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对账批号',
  `check_id` bigint DEFAULT NULL COMMENT '检测Id',
  `check_dimension` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '校验维度 COMPANY_CODE-子公司,STORE_CODE-门店',
  `check_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '检查执行状态 WAIT,ING,DONE',
  `check_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'OFFLINE_ORDER-线下正单 OFFLINE_REFUND-线下退单',
  `third_req_json` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方请求参数Json',
  `third_res_json` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方响应结果Json',
  `result_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '校验结果 SUCCESS-核对成功,FAILED-核对失败,COMPENSATE_ING-补偿中,COMPENSATE_OK-补偿成功,COMPENSATE_FAILED-补偿失败',
  `result_json` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '结果json PO: CheckResult(our_data,upsteam_data)',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '对账错误消息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_check_id` (`check_id`) USING BTREE,
  KEY `idx_union_index` (`check_id`,`check_dimension`,`check_status`,`check_type`) USING BTREE,
  KEY `idx_batch_no` (`batch_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `consistency_check_compensate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `check_id` bigint DEFAULT NULL COMMENT 'consistency_check表中的check_id',
  `batch_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批号',
  `third_platform_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台编码',
  `business_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '补偿成功的单号',
  `store_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '门店编码',
  `company_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司编码',
  `msg_json` text COLLATE utf8mb4_general_ci COMMENT '补偿的消息体',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  `message_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'rocketmq消息id',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '补偿状态: WAIT,OK,FAILED',
  `error_message` text COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_message_id` (`message_id`) USING BTREE,
  KEY `idx_union` (`third_platform_code`,`business_no`,`store_code`,`company_code`,`check_id`) USING BTREE,
  KEY `idx_check_id_batch_no` (`check_id`,`batch_no`),
  KEY `idx_batch_no` (`batch_no`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

// 公式: 以信息中心的订单数据为准,来减去心云这边的订单数
public class CheckResult{
	private Long upCount;
	private Long yxtOrderCount;

	private BigDecimal upAmount;
    private BigDecimal yxtOrderAmount;

	private Long diffCount; // 0-相等 正数-信息中心比信息多的订单数 负数-心云比信息中心多的订单数(可能重复)
	private BigDecimal diffAmount; // 0-相等 正数-信息中心比信息多的金额 负数-心云比信息中心多的金额(可能重复)
}

yxtOrderCount 使用 ES 获取

yxtOrderAmount 使用 ES 获取

**心云与信息中心交互**

true订单核对falseautotoptrue5323

上线步骤

  21 complete 同步master分支代码  22 complete order-atom-service   23 complete order-consistent-service   24 complete order-sync     25 complete SDK发布  26 complete order-atom-sdk   27 complete order-consistent-sdk     42 complete merge to master  38 complete order-atom-service   39 complete order-consistent-service   40 complete order-sync     28 incomplete Apollo配置  29 complete order-consistent-service   30 incomplete 和刘老师确认生产配置     31 complete 建表   32 complete xxl-job任务配置