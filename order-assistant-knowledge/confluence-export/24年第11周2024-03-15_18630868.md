# 24年第11周2024-03-15

### 一、上周TODO回顾

|  | 待办事项 | 负责人 | 预计完成时间 | 进展 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 1 | 订单中台重构-DDD项目 |  |  | 下周二技术方案评审 | 订单中台历史债务解决 DDD实践 分库分表改造 线下单接入 |
| 2 | .net接口中台迁移至JAVA |  |  |  | .net代码黑盒,无法多人同时开发. 按平台迁移对接代码 |
| 3 | 优雅发布升级-mq |  |  |  | 1.优雅发布已支持nacos逆向注册,解决API调用问题 2.后续版本能支撑MQ消费,异步任务 |
| 4 | 网关升级 |  |  |  | 1.目前网关依赖版本不统一,对接运维侧监控时,存在出入不一致问题 基于上述原因,zeus/hera 后续需要统一做依赖升级. |
| 5 | middle-id升级 |  |  |  | 1.middle-id 分布式id本身存在 已知的瓶颈, 24年春节前上线临时处理版本,后续需要升级迭代扩展版本. 引入美团的leaf |
| 6 | xxl-job执行器优化专项 |  |  |  | 每个组（或业务领域）一个执行器，各组分开管控权限，并且跑定时任务各组建离线微服务，不影响在线的业务 |
| 7 | [https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq](https://doc.weixin.qq.com/sheet/e3_AX4AxwbvADgn8938Xf4QIiqv9Y7ZT?scode=AOsAFQcYAAcDn5YVIOAboAOAYLADg&tab=rafmzq)  上线计划 |  |  |  |  |


### 二、本周工作情况

**1、成员工作情况**

|  | 成员 | 本周工时信息（根据管理思路可选） | 本周工作内容 | 下周工作计划 | 思考总结 |
| --- | --- | --- | --- | --- | --- |
| 1 | 郭志明 | **本周总工时：4pd**1. 退款单下账列表接口sql优化 2. 门店同步兼容换店场景 3. 微商城1.1.6-订单补推部分设计开发 | **㊀计划工作****㊁实际完成**1. 退款单下账列表接口sql优化（已上线） 2. 门店同步兼容换店场景（已上线） 3. 微商城1.1.6-订单补推部分设计开发（待联调） **㊂遗留问题**1. 微商城1.1.6-子公司支付配置部分的设计开发 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. 微商城1.1.6需求开发 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 2 |  | **本周总工时：4pd**1. 下账时机修改以及生成运费单 2. 内购商城测试上线 | **㊀计划工作**1. 下账时机修改以及生成运费单 2. 内购商城测试上线 **㊁实际完成**1. 内购商城测试上线 **㊂遗留问题**1. 下账时机修改以及生成运费单 **㊃风险问题**1. xxx **㊄关于团队/项目建设的建议（想法）**1. xxx | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx |  |
| 3 |  | **本周总工时：5pd**1. 经营分析刷数 2. 订单中台重构一期   1. 接口梳理 + 工时评估   2. 产品研发对齐会议   3. 重构一期相关流程图（.net拉单流程梳理)、架构图(新来应用架构) 3. 接口梳理 + 工时评估 4. 产品研发对齐会议 5. 重构一期相关流程图（.net拉单流程梳理)、架构图(新来应用架构) 6. 线下单相关会议 7. 面试相关工作 **** | **㊀计划工作** **㊁实际完成** **㊂遗留问题**DDD的价值收益和技术收益 **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. xxx **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 4 |  | **本周总工时：5pd******1. 微商城小程序支付 “下单账号和支付账号不一致” 问题  2. 海南无门店异常单处理 3. 订单路由需求评审解析 4. 线上问题处理 | **㊀计划工作** 1. 订单路由需求**㊁实际完成** 1.订单路由需求 主表模型已设计，技术方案进行中 2.  微商城小程序支付 “下单账号和支付账号不一致” 问题。已找到原因，会员中台问题。下次出现联系会员中台删除登录缓存信息 3. 海南无门店异常单处理 已处理。 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 订单路由需求设计方案 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 5 |  | **本周总工时：5pd****1.医保订单增加类型和显示以及导出******a.接口梳理 b.技术方案实现 c.历史数据刷新脚本**2.熟悉订单路由需求** | **㊀计划工作** 1. 医保订单增加类型和显示以及导出需求实现**㊁实际完成** 1.医保订单查询和展示 2.导出医保订单 3.订单详情增加医保金额显示 4.历史数据刷新脚本**㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **** |  |
| 6 |  | **本周总工时：5pd**1. 调整平台配送转自配送时order_detail表的bill_price精度问题 2. b2c门店仓退款单下账时机调整 3. 京东到家部分退款的金额与实际金额不一致（京东下发的退款单价是总价） 4. 美团有部分退款的情况下最后的全额退款单下账失败 5. 订单路由需求评审与熟悉 | **㊀计划工作****㊁实际完成****㊂遗留问题**1. 美团有部分退款的情况下最后的全额退款单下账失败（待测试） **㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 订单路由需求评审与熟悉 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |
| 7 |  | **本周总工时：4.5**1. 内购商城线上BUG 2. 下账时机测试用例评审 3. 下账时机海典下账时机调整 4. 订单线上运维 5. DDD再次确认 | **㊀计划工作** 1. 下账时机**㊁实际完成**1. 内购商城线上BUG 2. 下账时机海典下账时机调整 **㊂遗留问题****㊃风险问题****㊄关于团队/项目建设的建议（想法）****** | **㊀需求研发相关**1. 下账时机 **㊁技术建设相关（例：系统高可用/质量/安全/规范/技术专项/技术沉淀等）**1. xxxx **** |  |


### 2、重点项目周进展与风险概况

| 重点项目需求 | 本周进展 | 下周目标 | 风险评估 |
| --- | --- | --- | --- |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |
|  |  |  |  |


### 三、本周成长回顾

|  | 成员 | 本周学习分享 | 下周学习计划 |
| --- | --- | --- | --- |
| 1 | xxx |  |  |
| 2 | xxx |  |  |


### 四、本周CR回顾

备注：CR回顾主要关注典型问题，并针对问题进行复盘。

|  | GitLab链接 | 问题描述 | 解决方案 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |


### 五、本周异常告警&线上问题

### 六、本周缺陷

备注：“问题分析”需要讲清楚问题①产生原因、②处理方式、③如何避免、④后续将如何改进等。

|  | 缺陷(禅道)链接 | 缺陷描述 | 问题分析 | 负责人 | 处理结果 |
| --- | --- | --- | --- | --- | --- |
| 1 |  |  | *产生原因、处理方式、如何避免、后续将如何改进* |  |  |


### 七、团队建设

（团队管理动作、信息与制度传达、成员问题拉齐等）

### 八、本周TODO

|  | 待办事项 | 预计完成时间 | 负责人 |
| --- | --- | --- | --- |
| 1 |  |  |  |
| 2 |  |  |  |