# 【20241220】线下单添加预售标识【测试中】

一心数科数字化产研中心-Scrumb3077dd8-7d0b-370f-9ea2-10278de8967dORDER-3691

科传是通过单号

海典是通过支付方式

**服务:**

order-atom-service

order-sync 

order-service

order-framework

- order-types


**分支:**

feature-preSale-20250106

**sdk:**

	<dependency>
	  <groupId>com.yxt.order.types</groupId>
	  <artifactId>order-types</artifactId>
	  <version>preOrder-SNAPSHOT</version>
	</dependency>
    <dependency>
      <groupId>com.yxt.order.atom.sdk</groupId>
      <artifactId>order-atom-sdk</artifactId>
      <version>preOrder-SNAPSHOT</version>
    </dependency>
	<dependency> 
  		<groupId>com.yxt.order.open.sdk</groupId>
		<artifactId>order-open-sdk</artifactId>
		<version>preOrder-SNAPSHOT</version>
	</dependency>
    <dependency>
      <groupId>com.yxt.order.open.message</groupId>
      <artifactId>order-open-message</artifactId>
      <version>preOrder-SNAPSHOT</version>
    </dependency>

**SQL:**

ALTER TABLE `dscloud_offline`.`offline_order_${seq}` 
ADD COLUMN `tags` varchar(255) NULL COMMENT '订单标签,逗号(,)分隔 PRE_ORDER-预售订单',ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE `dscloud_offline`.`offline_refund_order_${seq}` 
ADD COLUMN `tags` varchar(255) NULL COMMENT '订单标签,逗号(,)分隔 PRE_ORDER-预售订单',ALGORITHM=INPLACE, LOCK=NONE;

  26 incomplete 注意2502的表  

**刷数脚本:**

flashOfflineOrderPreTagHandler
flashOfflineRefundOrderPreTagHandler

**Apollo配置:**

flashAllYYMMStr: 2406,2407,2408,2409,2410,2411,2412,2501,2502

**上线步骤:**

  2 incomplete **提前一天添加字段**   14 incomplete **同步master代码**  15 incomplete **order-service**   16 incomplete **order-atom-service**   17 incomplete **order-sync-service**   18 incomplete **order-framework**     19 incomplete **提交PR**  20 incomplete **order-service**   21 incomplete **order-atom-service**   22 incomplete **order-sync-service**   23 incomplete **order-framework**     24 incomplete **配置job**