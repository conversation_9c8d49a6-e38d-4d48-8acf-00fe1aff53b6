# 【20241129】线下单支持大数据: 退款数据提供正单促销和券信息【已完成】

#### 服务及分支:

order-atom-service

feature-refund-promotion-coupon-data

#### 是否分表

- 6月份到12月退款数据总量为: 230131 ,退款数据不多
- 和大数据沟通的结果是可以保留清除逻辑，但是不执行。一年后看是否有必要清理365天前的数据。


鉴于退款数据不多且后面可能会清理历史数据,故不进行分表

#### 建表:

  4 complete prod  

CREATE TABLE `big_data_refund_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_no` varchar(64) NOT NULL COMMENT '退单号',
  `data` text DEFAULT NULL COMMENT 'json data',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `updated_by` varchar(64) NOT NULL COMMENT '更新人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint(20) NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_refund_no` (`refund_no`) USING BTREE,
  KEY `idx_created_time` (`created_time`) USING BTREE,
  KEY `idx_updated_time` (`updated_time`) USING BTREE
) ENGINE=InnoDB  AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='大数据需要的退款数据';

#### Job

手动执行一次

  6 complete prod  

order-atom-service

flashRefundPromotionHandler
flashRefundCouponHandler




Apollo配置

flashRefundToBigDataYYMM: 2406,2407,2408,2409,2410,2411,2412

上线checkList

  2 complete 合并master代码