# 【20240514】商品员工推广业绩提成功能

# 一、背景

## 1.1 业务背景

**一心到家私域运营决定让一心堂员工进行私域B2C商品的推广，由私域运营参照现有自营品的推广计划方式进行配置，并生成提成报表由私域运营人员确认后提交财务核算员工的业绩提成。**

## 1.2 痛点分析

## 1.3 系统现状

# 二、需求分析

## 2.1 业务流程

# 三、目标

**3.1 本期目标**

# 四、整体设计

## 4.1 统一语言定义

| **名称** | **说明** |
| --- | --- |
|  |  |


## 4.2 流程图

**1.微商城订单提交**

****

```
ydjia-merchant-customer服务
```

```
order/addOrder
```

**true提交订单改造falseautotoptrue8762**

**2.支付成功回调新增推广记录**

true新增推广记录falseautotoptrue7912

3.新增退款推广记录

true逆向falseautotoptrue8115

# 五、详细设计

## 5.1 详细模块设计

## 5.3 接口设计

### 5.3.1 前端交互接口

**1. 推广记录查询**

 1.1.分页查询

[http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/exportPromotionRecordUsingPOST](http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/exportPromotionRecordUsingPOST)

 1.2.导出

[http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryPromotionRecordUsingPOST](http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryPromotionRecordUsingPOST)

**2.B2C提成报表**

 2.1.查询员工业绩报表头部

[http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryEarningReportHeadUsingPOST](http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryEarningReportHeadUsingPOST)

 2.2.查询员工业绩报表

[http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryEarningReportUsingPOST](http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/queryEarningReportUsingPOST)

 2,3.导出员工业绩报表

[http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/exportEarningReportUsingPOST](http://ydjia-merchant-manager.svc.k8s.dev.hxyxt.com/doc.html#/default/员工推广管理/exportEarningReportUsingPOST)

**3.小前台 历史推广订单**

****

**3.1.推广记录头部**

 **3.1.1 推广记录累计**

[](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryPromotionRecordUsingPOST)[http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryTotalPerformanceUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryTotalPerformanceUsingPOST)

 **3.1.2 推广记录当月**

**** [http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryPromotionByMonthUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryPromotionByMonthUsingPOST)

**3.2.推广记录分页**

 [http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryPromotionRecordUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryPromotionRecordUsingPOST)

**4.商品业绩收益**

****

[http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryIncomeStatisticsUsingPOST](http://ydjia-merchant-customer.svc.k8s.dev.hxyxt.com/doc.html#/default/%E5%91%98%E5%B7%A5%E6%8E%A8%E5%B9%BF%E7%AE%A1%E7%90%86/queryIncomeStatisticsUsingPOST)

## 5.4 涉及数据库

| 数据库 | SQL |
| --- | --- |
| middle_order | ALTER TABLE `middle_order`.`order_info`  ADD COLUMN `emp_code` bigint NULL COMMENT '员工编号 推广员' AFTER `pay_sale_info`; |
| ALTER TABLE `middle_order`.`order_detail`  ADD COLUMN `promotion_ratio_id` bigint NULL COMMENT '商品业绩比例id' AFTER `final_payment_amount`, ADD COLUMN `promotion_ratio` double(5, 4) ZEROFILL NULL COMMENT '商品业绩比例' AFTER `promotion_ratio_id`; |
| CREATE TABLE `staff_promotion_record` (  `id` bigint NOT NULL,  `order_id` bigint NOT NULL COMMENT 'order_info id',  `status` tinyint NOT NULL COMMENT '状态 1支付 2退款',  `commodity_id` int NOT NULL COMMENT '商品中台 商品id',  `commodity_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编码',  `commodity_type` tinyint NOT NULL COMMENT '商品类型 1自营 2云仓',  `commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',  `commodity_number` int NOT NULL COMMENT '商品数量',  `commodity_price` decimal(16,2) NOT NULL COMMENT '商品实付总金额 单位：元',  `promotion_ratio_id` bigint NOT NULL COMMENT '商品业绩比例id',  `promotion_ratio` double(5,4) unsigned zerofill NOT NULL COMMENT '商品业绩比例',  `m_pic` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品图片 （SKU主图）',  `employee_id` bigint NOT NULL COMMENT '员工id 推广员',  `user_id` bigint NOT NULL COMMENT '下单用id 小程序查询用户信息展示用',  `store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '门店编码',  `send_store_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '货源 （自营订单放门店编码，云仓服务商编码）',  `send_store_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '货源中文',  `performance` decimal(16,4) NOT NULL COMMENT '商品业绩金额（commodity_price*promotion_ratio）',  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人',  `updated_by` datetime DEFAULT NULL COMMENT '更新人',  `created_time` datetime NOT NULL COMMENT '创建时间',  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',  `isvalid` bigint NOT NULL DEFAULT '1' COMMENT '是否删除 1-未删除， !=1已删除''',  `version` bigint NOT NULL DEFAULT '1' COMMENT '数据版本，每次update+1',  PRIMARY KEY (`id`),  KEY `idx_created_time` (`created_time`),  KEY `idx_status` (`status`) USING BTREE,  KEY `idx_order_no` (`order_id`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci; |


## 5.5 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

## 5.6 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 5.7 问题

performance 金额小于1分不新增推广记录

# 六、质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

# 七、里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

# 八、项目排期

**接口文档输出：2024年5月15日;**

**研发时间：2024年5月16日-2024年5月22日；**

**联调时间：2024年5月22日-2024年5月24日(含自测)；**

**测试时间：2024年4月24日(提测)；**

**上线时间：**

# 九、上线方案

1、兼容、回滚方案等
2、上线流程、SOP等