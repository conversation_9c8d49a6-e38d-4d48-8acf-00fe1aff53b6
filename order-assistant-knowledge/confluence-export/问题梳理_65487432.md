# 问题梳理

1. 缺少字段梳理：
  1. |  | O2O | B2C | 字段所在位置 |
| --- | --- | --- | --- |
| 接单时间 | 27 complete | 28 incomplete |  |
| 订单取消时间 | 29 complete | 30 complete |  |
| 订单取消原因 | 55 complete | 56 complete |
| 拣货时间 | 31 complete | 32 complete | 拣货人保存在收银员表（OrderCashierDesk）中，拣货时间存在一起？ |
| 自提码 | 33 complete | 34 incomplete |  |
| 收货人经纬度 | 35 complete | 36 complete | OrderDeliveryAddress，发货单（DeliveryOrder）中是否需要保存一份？ |
| 审核时间 | 37 incomplete | 38 complete |  |
| 审核人 | 39 incomplete | 40 complete |
| 发货时间 | 41 incomplete | 42 complete |  |
| 发货人 | 43 incomplete | 44 complete |
| 供应商信息 | 45 incomplete | 46 complete | 目前只有微商城会有，放在了extend中 |
| 仓库信息 | 47 incomplete | 48 complete |  |
| 快递面单状态 | 59 incomplete | 60 complete |  |
| 异常处理时间 | 49 complete | 50 complete | 对于订单异常，建议和老模型中的B2C订单一样，增加一张异常表进行处理，表结构如下：CREATE TABLE oms_order_ex (     id             bigint AUTO_INCREMENT COMMENT '自增id' PRIMARY KEY,     order_no       bigint                                               NOT NULL COMMENT '订单id',     ex_type        tinyint                    DEFAULT 0                 NULL COMMENT '异常类型',     ex_type_desc   varchar(255) COLLATE utf8mb4_general_ci              NULL COMMENT '异常类型描述',     ex_reason      varchar(1024) CHARSET utf8 DEFAULT ''                NULL COMMENT '异常原因',     operate_status tinyint                    DEFAULT 0                 NULL COMMENT '异常处理,0-未处理，1-已处理',     operator       varchar(100) COLLATE utf8mb4_general_ci              NULL COMMENT '处理人',     create_time    datetime                   DEFAULT CURRENT_TIMESTAMP NULL COMMENT '异常生成时间',     operate_time   datetime                                             NULL COMMENT '异常处理时间',    update_time    datetime                   DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' ) |
| 异常处理人 | 51 complete | 52 complete |
| 催单标识 | 63 complete | 64 incomplete |  |
2. |  | O2O | B2C | 字段所在位置 |
| --- | --- | --- | --- |
| 接单时间 | 27 complete | 28 incomplete |  |
| 订单取消时间 | 29 complete | 30 complete |  |
| 订单取消原因 | 55 complete | 56 complete |
| 拣货时间 | 31 complete | 32 complete | 拣货人保存在收银员表（OrderCashierDesk）中，拣货时间存在一起？ |
| 自提码 | 33 complete | 34 incomplete |  |
| 收货人经纬度 | 35 complete | 36 complete | OrderDeliveryAddress，发货单（DeliveryOrder）中是否需要保存一份？ |
| 审核时间 | 37 incomplete | 38 complete |  |
| 审核人 | 39 incomplete | 40 complete |
| 发货时间 | 41 incomplete | 42 complete |  |
| 发货人 | 43 incomplete | 44 complete |
| 供应商信息 | 45 incomplete | 46 complete | 目前只有微商城会有，放在了extend中 |
| 仓库信息 | 47 incomplete | 48 complete |  |
| 快递面单状态 | 59 incomplete | 60 complete |  |
| 异常处理时间 | 49 complete | 50 complete | 对于订单异常，建议和老模型中的B2C订单一样，增加一张异常表进行处理，表结构如下：CREATE TABLE oms_order_ex (     id             bigint AUTO_INCREMENT COMMENT '自增id' PRIMARY KEY,     order_no       bigint                                               NOT NULL COMMENT '订单id',     ex_type        tinyint                    DEFAULT 0                 NULL COMMENT '异常类型',     ex_type_desc   varchar(255) COLLATE utf8mb4_general_ci              NULL COMMENT '异常类型描述',     ex_reason      varchar(1024) CHARSET utf8 DEFAULT ''                NULL COMMENT '异常原因',     operate_status tinyint                    DEFAULT 0                 NULL COMMENT '异常处理,0-未处理，1-已处理',     operator       varchar(100) COLLATE utf8mb4_general_ci              NULL COMMENT '处理人',     create_time    datetime                   DEFAULT CURRENT_TIMESTAMP NULL COMMENT '异常生成时间',     operate_time   datetime                                             NULL COMMENT '异常处理时间',    update_time    datetime                   DEFAULT CURRENT_TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' ) |
| 异常处理人 | 51 complete | 52 complete |
| 催单标识 | 63 complete | 64 incomplete |  |