# 【20250408】低代码平台,支付配置迁移方案

## 1 . 系统现状

### 1.1 低代码平台目前支付相关配置

### 1.2 低代码平台支付表设计

true流程引擎支付配置falseautotoptrue7313

支付通道: 微信,支付宝
支付类型: 微信APP支付,微信刷脸支付,微信扫码枪
支付方式: 用来配置优先级的

特点: 单一渠道所有配置统一存放,不区分渠道类型

### 1.3 支付中台支付表设计

true支付中台支付配置falseautotoptrue11531

子公司 + 门店自己配置方式

不同支付方式 相同渠道时使用同一套配置 : 查询门店支持哪些支付方式,

# 2. 迁移方案

### 2.1 离线迁移

在上线前通过程序读取生产环境h3-pay-core 配置文件, 通过程序方式生成, 支付中台表结构的INSERT 表语句, 通过sql脚本完成数据迁移.