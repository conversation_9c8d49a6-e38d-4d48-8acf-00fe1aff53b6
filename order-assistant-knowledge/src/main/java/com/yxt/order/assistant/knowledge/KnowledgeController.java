package com.yxt.order.assistant.knowledge;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.repository.KnowledgeGroupRepository;
import com.yxt.order.assistant.server.repository.entity.KnowledgeGroup;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/knowledge")
public class KnowledgeController extends AbstractController {

  @Resource
  private KnowledgeGroupRepository knowledgeGroupRepository;

  @RequestMapping("/list-all-knowledge")
  public ResponseBase<List<KnowledgeGroup>> listAllKnowledge() {
    LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<>();
    List<KnowledgeGroup> knowledges = knowledgeGroupRepository.selectList(query);
    return generateSuccess(knowledges);
  }


}
