# 【20241010】 V2.14 京东物流对接

## 一、 背景

B2C物流成本高，大约在3元/单；且各子公司使用物流不统一，现集团统一集采京东快递，谈的成本约为2元/单 。按照B2C一个月，总部24万单，子公司一个月也接近24万单，那么节约运费成本将会有48万；

### 1.1 需求清单

| 功能 | 功能描述 | 接口地址 |
| --- | --- | --- |
| 物流单下单 | 物流单下单请求接口 | [https://open.jdl.com/#/open-business-document/api-doc/267/841](https://open.jdl.com/#/open-business-document/api-doc/267/841) |


### 1.2 痛点分析

## 二、 需求分析

### 2.1 业务流程

[V2.14基础能力建设：京东物流对接](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=46268857)

## 三、 目标

### 3.1 本期目标

实现业务相关功能，保证系统稳定运行

## 四、整体设计

## 五、 详细设计

### 1、 模块详细设计

1、快递商配置新增京东快递配置信息

2、店铺物流配置定义对应的动态表单配置

3、对接京东快递下单前置校验接口和下单接口

### 2、 存储数据库设计

### 3、 接口设计

### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

**技术文档输出：2024年08月15日;**

**研发时间：2024年08月16日-2024年8月22日；**

**测试时间：2024年8月23日(提测)；**

**上线时间：**

## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等