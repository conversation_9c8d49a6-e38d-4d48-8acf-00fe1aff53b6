# 【20240729】 V2.1 处方订单流程优化

## 一、 背景

目前京东到家和饿了么的处方订单，是进入到药事云审核的，在审核过程中可能会出现审核不通过的时候。但这类订单我们没有做拦截处理，也就是说处方审核不通过但是订单依旧发货出去了，这是一个非常有风险的问题；目前饿了么审核不通过率为2%；

饿了么B2C的河南店铺，有收1.5元的打包费。这个打包费了是平台结算给我们的金额。目前了，B2C的下账配置没有打包费这一选项配置是否下账；一天大约40个订单；

### 1.1 需求清单

| 功能 | 功能描述 |
| --- | --- |
| 处方订单流程优化 | 兼容O2O和B2C，优化处方订单流程 |


### 1.2 痛点分析

## 二、 需求分析

### 2.1 业务流程

[V2.1 处方订单流程优化/包装费下账/O2O库存下账失败批量处理](https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=38834141)

## 三、 目标

### 3.1 本期目标

实现业务相关功能，保证系统稳定运行

## 四、整体设计

### 4.1处方订单流程优化

O2O处方单流程优化：

true处方单调整falseautotoptrue8415

## 五、 详细设计

### 1、 模块详细设计

1、审方结果需要有ES日志记录

2、B2C在拣货/批量拣货/发货/批量发货/扫码发货过滤药师审方不通过类型订单

3、批量修改配置调整处方订单审方选项

4、审方不通过类型订单取消后回调更新订单状态

### 2、 存储数据库设计

### 3、 接口设计

### 4、 安全设计

时刻警惕资损问题；数据一致性、接口防刷、幂等设计等；

### 5、 监控报警

需要思考上线后如何监控，及时响应止损、回滚、降级等方案。

## 六、 质量效率

本次迭代可以开发沉淀的提效或保障质量的公共工具或基础服务，或引入的新工具、新技术等。

（研发的重要技术指标，衡量技术产出，鼓励沉淀基础组件）

## 七、 里程碑

| **里程碑** | **时间** |
| 需求评审 |  |
| 技术方案评审 |  |
| 提测 |  |
| 上线/交付 |  |


一般可以为产品研流程的关键节点，也可根据项目实际情况**梳理重要进展时间节点**

## 八、 项目排期

**技术文档输出：2024年08月15日;**

**研发时间：2024年08月16日-2024年8月22日；**

**测试时间：2024年8月23日(提测)；**

**上线时间：**

## 九、 上线方案

1、兼容、回滚方案等
2、上线流程、SOP等